package com.easylinkin.linkappapi.powerdistribution.service;

import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * 配电柜关联设备 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetRefDeviceService extends IService<DistributionCabinetRefDevice> {

    void add(DistributionCabinetRefDevice distributionCabinetRefDevice);

    /**
     * 根据设备查找 配电柜关联设备以及组态以及组态的规则表达式
     * @param device 查找条件 vo
     * @return 配电柜关联设备以及组态以及组态的规则表达式
     */
    List<DistributionCabinetRefDevice> getCabinetRefDevicesWithConfigurationAndExpressions(Device device);

}
