<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.device.dao.WaterRecordsMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.device.entity.WaterRecords">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="collect_time" jdbcType="TIMESTAMP" property="collectTime" />
    <result column="water_increment" jdbcType="DOUBLE" property="waterIncrement" />
    <result column="water_total" jdbcType="DOUBLE" property="waterTotal" />
    <result column="stop_reading" jdbcType="DOUBLE" property="stopReading" />
    <result column="creator_id_" jdbcType="BIGINT" property="creatorId" />
    <result column="create_time_" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_id_" jdbcType="BIGINT" property="modifyId" />
    <result column="modify_time_" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="remark_" jdbcType="LONGVARCHAR" property="remark" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_water_records
        where tenant_id = #{appWaterRecords.tenantId}
        <if test="appWaterRecords.deviceCode != null and appWaterRecords.deviceCode != ''">
          and device_code = #{appWaterRecords.deviceCode}
        </if>
      <if test="appWaterRecords.startTime != null">
        AND create_time_ <![CDATA[>=]]> #{appWaterRecords.startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="appWaterRecords.endTime != null">
        AND create_time_ <![CDATA[<]]>#{appWaterRecords.endTime,jdbcType=TIMESTAMP}
      </if>
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_water_records where id = #{id}
    </select>

    <select id="findByDeviceCode" resultMap="BaseResultMap">
      select * from app_water_records where tenant_id = #{tenantId} and device_code = #{deviceCode}  order by create_time_ desc
    </select>

    <select id="getSumWaterByTime" resultType="double">
      select SUM(water_increment)
      from app_water_records
      where tenant_id = #{tenantId}
      <if test="deviceCode != null and deviceCode != ''">
        and device_code = #{deviceCode}
      </if>
      <if test="areaId != null and areaId != ''">
        AND area_id = #{areaId}
      </if>
      <if test="startTime != null">
        and collect_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and collect_time <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="deviceCodes != null and deviceCodes.size() > 0">
        and device_code in
        <foreach collection="deviceCodes" index="index" item="code" open="(" close=")" separator=",">
          #{code}
        </foreach>
      </if>
    </select>

    <select id="getAvgDayWater" resultType="double">
      select avg(wr.dayWater) from (
        select SUM(water_increment) as dayWater,DATE_FORMAT(collect_time,'%Y%m%d') dayTime
        from app_water_records
        where tenant_id = #{tenantId}
        <if test="deviceCode != null and deviceCode != ''">
          and device_code = #{deviceCode}
        </if>
        group by DATE_FORMAT(collect_time,'%Y%m%d')
      ) wr
    </select>
  
    <select id="getLatestRecord" resultMap="BaseResultMap">
      select * from app_water_records
      where tenant_id = #{tenantId}
      <if test="deviceCode != null and deviceCode != ''">
        and device_code = #{deviceCode}
      </if>
      <if test="deviceCodes != null and deviceCodes.size() > 0">
        and device_code in
        <foreach collection="deviceCodes" index="index" item="code" open="(" close=")" separator=",">
          #{code}
        </foreach>
      </if>
      order by collect_time desc
      limit 1
    </select>

  <select id="getWaterStatisticsVoByDay" resultType="com.easylinkin.linkappapi.device.entity.vo.WaterStatisticsVo">
    SELECT
    <if test="grainSize != null and grainSize == 0">
      DATE_FORMAT( collect_time, '%Y%m%d' ) days,
      IFNULL(SUM(IFNULL(water_increment,0)),0) AS todayWater
    </if>
    <if test="grainSize != null and grainSize == 1">
      DATE_FORMAT( collect_time, '%Y%u' ) weeks,
      IFNULL(SUM(IFNULL(water_increment,0)),0) AS weekWater
    </if>
    <if test="grainSize != null and grainSize == 2">
      DATE_FORMAT( collect_time, '%Y%m' ) months,
      IFNULL(SUM(IFNULL(water_increment,0)),0) AS monthWater
    </if>
    FROM
      app_water_records
    WHERE
    <include refid="where_queryCondition"></include>
    GROUP BY
    <if test="grainSize != null and grainSize == 0">
      days
    </if>
    <if test="grainSize != null and grainSize == 1">
      weeks
    </if>
    <if test="grainSize != null and grainSize == 2">
      months
    </if>
  </select>

  <select id="findOneArea" resultType="com.easylinkin.linkappapi.device.entity.vo.WaterRecordsVo">
    SELECT
      a.device_code deviceCode,
      a.water_increment waterIncrement,
      a.collect_time collectTime,
      b.name_ areaName
    FROM
      `app_water_records` a
        LEFT JOIN app_environmental_area b ON a.area_id = b.id
    WHERE
      a.tenant_id = #{tenantId}
      AND b.level_ = 1
      AND b.parent_id_ = 0
      AND date_format( a.collect_time, "%Y-%m" ) = date_format(#{time}, "%Y-%m" )
    ORDER BY a.collect_time ASC
  </select>


  <sql id="where_queryCondition">
      tenant_id = #{tenantId}
      <if test="deviceCode != null and deviceCode != ''">
        AND device_code = #{deviceCode}
      </if>
      <if test="areaId != null and areaId != ''">
        AND area_id = #{areaId}
      </if>
      AND DATE_FORMAT(collect_time,'%Y%m%d') &gt;= DATE_FORMAT(#{startTime}, '%Y%m%d' )
      AND DATE_FORMAT(collect_time,'%Y%m%d') &lt;= DATE_FORMAT(#{endTime}, '%Y%m%d' )
  </sql>
</mapper>
