package com.easylinkin.linkappapi.powerdistribution.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmInfo;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetAlarmInfoService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @program: linkapp-group-cloud
 * @description: 设备档案-告警信息
 * @author: chenkaixuan
 * @create: 2021-09-16 16:33
 */
@RestController
@RequestMapping("/distributionCabinetArchives/alarm")
public class DistributionCabinetAlarmInfoController {
    @Autowired
    DistributionCabinetAlarmInfoService distributionCabinetAlarmInfoService;

    @PostMapping("getPage")
    public RestMessage getDistributionCabinetsPage(@RequestBody RequestModel<DistributionCabinetAlarmInfo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DistributionCabinetAlarmInfo> record = distributionCabinetAlarmInfoService.getAlarmPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    @PostMapping("exportData")
    public void exportData(@RequestBody DistributionCabinetAlarmInfo alarm, HttpServletRequest request, HttpServletResponse response) {
        distributionCabinetAlarmInfoService.exportData(alarm, request, response);
    }

    @PostMapping("handlerBatch")
    public RestMessage addBatch(@RequestBody DistributionCabinetAlarmInfo distributionCabinetAlarmInfo) {
        distributionCabinetAlarmInfoService.alarmProcessBatch(distributionCabinetAlarmInfo);
        return RestBuilders.successBuilder().build();
    }

    @PostMapping("getDetail")
    public RestMessage getDetail(@RequestBody DistributionCabinetAlarmInfo alarm) {
        Assert.notNull(alarm, "alarm 不能为空");
        return RestBuilders.successBuilder().data(distributionCabinetAlarmInfoService.getDetail(alarm)).build();
    }

}
