<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.rulecomponent.mapper.RuleComponentAttributeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentAttribute">
        <id column="id" property="id"/>
        <result column="device_attribute_id" property="deviceAttributeId"/>
        <result column="device_attribute_parent_id" property="deviceAttributeParentId"/>
        <result column="calculate_sign" property="calculateSign"/>
        <result column="value" property="value"/>
        <result column="unit" property="unit"/>
        <result column="specs" property="specs"/>
        <result column="sort_no" property="sortNo"/>
        <result column="logic_code" property="logicCode"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="rule_component_id" property="ruleComponentId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <select id="getRuleComponentAttribute" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentAttribute">
		SELECT
       		a.*,
       		b.name deviceAttributeName,
       		b.name deviceAttributeParentName
        FROM
            linkapp_rule_component_attribute a
            left join linkapp_device_attribute b on a.device_attribute_id = b.id
            left join linkapp_device_attribute c on a.device_attribute_id = c.id
        <where>
        	<if test="ruleComponentId != null and ruleComponentId != ''">
	        	and a.rule_component_id = #{ruleComponentId}
	        </if>
	        <if test="tenantId != null and tenantId != ''">
	        	and a.tenant_id = #{tenantId}
	        </if>
        </where>
        ORDER BY a.sort_no
    </select>


    <select id="getRuleComponentAttributes" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentAttribute">
		SELECT
       		a.*,
       		b.name deviceAttributeName,
       		c.name deviceAttributeParentName
        FROM
            linkapp_rule_component_attribute a
            left join linkapp_device_attribute b on a.device_attribute_id = b.id
            left join linkapp_device_attribute c on a.device_attribute_id = c.id
        <where>
        	<if test="ruleComponentAttribute.ruleComponentId != null and ruleComponentAttribute.ruleComponentId != ''">
	        	and a.rule_component_id = #{ruleComponentAttribute.ruleComponentId}
	        </if>
	        <if test="ruleComponentAttribute.tenantId != null and ruleComponentAttribute.tenantId != ''">
	        	and a.tenant_id = #{ruleComponentAttribute.tenantId}
	        </if>
        </where>
        ORDER BY a.sort_no
    </select>

	<select id ="flushDeviceUnitData" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentAttribute">
    	SELECT e.name deviceAttributeName,e.identifier identifier, d.* FROM linkapp_rule_component_attribute d
	 	 LEFT JOIN linkapp_rule_component a ON a.id = d.rule_component_id
         LEFT JOIN linkapp_device_unit b ON a.device_unit_id = b.`id`
         LEFT JOIN linkapp_device_unit c ON b.code = c.code
         LEFT JOIN linkapp_device_attribute e ON d.device_attribute_id = e.id
         <where>
         	<if test="deviceUnitId != null and deviceUnitId != ''">
         		and c.id = #{deviceUnitId}
         	</if>
         	<if test="tenantId != null and tenantId != ''">
         		AND a.tenant_id = #{tenantId}
         	</if>
         </where>
    </select>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into linkapp_rule_component_attribute
        (id, device_attribute_id, device_attribute_parent_id, calculate_sign, expression,
        `value`, sort_no, logic_code, create_time, creator, modifier, modify_time, tenant_id,
        rule_component_id, specs, unit)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.deviceAttributeId,jdbcType=VARCHAR}, #{item.deviceAttributeParentId,jdbcType=VARCHAR},
            #{item.calculateSign,jdbcType=VARCHAR}, #{item.expression,jdbcType=VARCHAR}, #{item.value,jdbcType=VARCHAR},
            #{item.sortNo,jdbcType=INTEGER}, #{item.logicCode,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.modifyTime,jdbcType=TIMESTAMP},
            #{item.tenantId,jdbcType=VARCHAR}, #{item.ruleComponentId,jdbcType=VARCHAR}, #{item.specs,jdbcType=VARCHAR},
            #{item.unit,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>
