<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridQrCodeMapper">

    <select id="selectMaxCode" resultType="java.lang.String">
        select max(code) from grid_qr_code
    </select>

    <update id="clearGrid">
        update grid_qr_code set grid_id = '',grid_name = '', code_status = 0 where id = #{id}
    </update>

    <select id="selectOneByCode" resultType="com.easylinkin.linkappapi.grid.models.vo.GridQrCodeVO">
        SELECT  * FROM grid_qr_code WHERE code = #{code}
    </select>

</mapper>