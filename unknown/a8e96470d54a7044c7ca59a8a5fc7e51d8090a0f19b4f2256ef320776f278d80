package com.easylinkin.linkappapi.powerdistribution.service;

import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetStatus;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 配电柜状态(冗余部分组态字段) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-08
 */
public interface DistributionCabinetStatusService extends IService<DistributionCabinetStatus> {

    /**
     * 生成配电柜状态
     * @param device 设备vo
     */
    void checkGenerateDistributionCabinetStatus(Device device);
}
