<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.isoftstone</groupId>
    <artifactId>linkapp-group</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>linkapp-api</artifactId>
  <packaging>jar</packaging>
  <name>linkapp-api</name>
  <description>Demo project for Spring Boot</description>

  <dependencies>
        <!-- Commons IO -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <!-- EasyExcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>
    <dependency>
      <groupId>com.konghq</groupId>
      <artifactId>unirest-java</artifactId>
      <version>3.13.6</version>
    </dependency>
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>core</artifactId>
      <version>3.5.3</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.kafka</groupId>
      <artifactId>spring-kafka</artifactId>
    </dependency>
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>1.25</version>
    </dependency>
    <dependency>
      <groupId>cn.jpush.api</groupId>
      <artifactId>jiguang-common</artifactId>
      <version>1.1.11</version>
    </dependency>
    <dependency>
      <groupId>cn.jpush.api</groupId>
      <artifactId>jpush-client</artifactId>
      <version>3.6.6</version>
    </dependency>
    <dependency>
      <groupId>com.isoftstone</groupId>
      <artifactId>bases-sm</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.isoftstone</groupId>
      <artifactId>bases-sm-entity</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.isoftstone</groupId>
      <artifactId>bases-sm-security</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.isoftstone</groupId>
      <artifactId>bases-excel</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
<!--     <dependency> -->
<!--       <groupId>com.isoftstone</groupId> -->
<!--       <artifactId>bases-seaweed</artifactId> -->
<!--       <version>1.0.0-SNAPSHOT</version> -->
<!--     </dependency> -->
    <dependency>
      <groupId>com.isoftstone</groupId>
      <artifactId>bases-aliyun</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.isoftstone</groupId>
      <artifactId>bases-redis</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-quartz</artifactId>
      <version>2.3.0.RELEASE</version>
<!--      <groupId>org.quartz-scheduler</groupId>-->
<!--      <artifactId>quartz</artifactId>-->
<!--      <version>2.3.0</version>-->
    </dependency>
    <!-- websocket 引入 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>
    <!-- springboot集成thymeleaf的起步依赖 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-thymeleaf</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.session</groupId>
      <artifactId>spring-session-data-redis</artifactId>
    </dependency>

    <!--ES操作相关-->
	<dependency>
		<groupId>org.elasticsearch.client</groupId>
		<artifactId>elasticsearch-rest-client</artifactId>
		<version>6.5.2</version>
		<exclusions>
			<exclusion>
				<groupId>org.elasticsearch</groupId>
				<artifactId>elasticsearch</artifactId>
			</exclusion>
		</exclusions>
	</dependency>
	<dependency>
		<groupId>org.elasticsearch.client</groupId>
		<artifactId>elasticsearch-rest-high-level-client</artifactId>
		<version>6.5.2</version>
		<exclusions>
			<exclusion>
				<groupId>org.elasticsearch</groupId>
				<artifactId>elasticsearch</artifactId>
			</exclusion>
		</exclusions>
	</dependency>

	<dependency>
		<groupId>org.elasticsearch</groupId>
		<artifactId>elasticsearch</artifactId>
		<version>6.5.2</version>
	</dependency>


    <!--    before succ-->
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-xml</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.58</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-mail</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
      <version>2.1.2</version>
    </dependency>

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--    2020-4-7  20:38-->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
      <version>1.1.21</version>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
      <version>3.1.2</version>
    </dependency>
    <!-- mybatis plus 代码生成器依赖 -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
      <version>3.1.2</version>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-support</artifactId>
      <version>2.3.3</version>
    </dependency>

    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
      <version>2.3</version>
    </dependency>
    <!-- 代码生成器模板 -->
    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
    </dependency>

    <!--office 相关 poi-->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
<!--      <version>3.14</version>-->
      <version>4.1.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
<!--      <version>3.14</version>-->
      <version>4.1.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-scratchpad</artifactId>
<!--      <version>3.14</version>-->
      <version>4.1.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.xmlbeans</groupId>
      <artifactId>xmlbeans</artifactId>
      <version>3.1.0</version>
    </dependency>

    <!--     poi模板导入，主力包      -->
    <dependency>
      <groupId>com.deepoove</groupId>
      <artifactId>poi-tl</artifactId>
      <version>1.12.2</version>
    </dependency>

    <!-- itextpdf-->
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>itext-asian</artifactId>
      <version>5.2.0</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>itextpdf</artifactId>
      <version>5.5.13.3</version>
    </dependency>

    <dependency>
      <groupId>cn.afterturn</groupId>
      <artifactId>easypoi-base</artifactId>
      <version>4.3.0</version>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
      <groupId>site.morn.boot</groupId>
      <artifactId>morn-core</artifactId>
      <version>1.2.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <!--阿里文件服务-->
     <dependency>
         <groupId>com.aliyun.oss</groupId>
         <artifactId>aliyun-sdk-oss</artifactId>
         <version>2.8.3</version>
     </dependency>
    <!--国际化支持-->
    <dependency><!--webjars版本定位器 用于省略版本号-->
      <groupId>org.webjars</groupId>
      <artifactId>webjars-locator-core</artifactId>
    </dependency>

    <dependency><!--jQuery前端依赖-->
      <groupId>org.webjars</groupId>
      <artifactId>jquery</artifactId>
      <version>3.3.1</version>
    </dependency>

    <dependency><!--jQuery国际化插件-->
      <groupId>org.webjars.bower</groupId>
      <artifactId>jquery-i18n-properties</artifactId>
      <version>1.2.7</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.12</version>
      <scope>compile</scope>
    </dependency>


    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>1.3.2</version>
    </dependency>
    <dependency>
		<groupId>commons-net</groupId>
		<artifactId>commons-net</artifactId>
		<version>3.1</version>
	</dependency>

  <dependency>
    <groupId>org.mvel</groupId>
    <artifactId>mvel2</artifactId>
    <version>2.4.7.Final</version>
  </dependency>

    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
      <version>2.9.2</version>
    </dependency>
    <!-- swagger-ui -->
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger-ui</artifactId>
      <version>2.9.2</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>3.14.2</version>
      <scope>compile</scope>
    </dependency>


    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-boot-starter</artifactId>
      <version>3.13.6</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.retrofit2</groupId>
      <artifactId>retrofit</artifactId>
      <version>2.9.0</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.retrofit2</groupId>
      <artifactId>converter-scalars</artifactId>
      <version>2.9.0</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.retrofit2</groupId>
      <artifactId>converter-gson</artifactId>
      <version>2.9.0</version>
    </dependency>

    <dependency>
      <groupId>io.minio</groupId>
      <artifactId>minio</artifactId>
      <version>3.0.10</version>
    </dependency>
<!--      <dependency>-->
<!--          <groupId>cn.hutool</groupId>-->
<!--          <artifactId>hutool-all</artifactId>-->
<!--          <version>5.7.4</version>-->
<!--      </dependency>-->

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.7.4</version>
    </dependency>

    <dependency>
      <groupId>eu.bitwalker</groupId>
      <artifactId>UserAgentUtils</artifactId>
      <version>1.21</version>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>org.mybatis.generator</groupId>-->
<!--      <artifactId>mybatis-generator-core</artifactId>-->
<!--      <version>1.3.7</version>-->
<!--      <scope>test</scope>-->
<!--    </dependency>-->
    <!-- mybatis mapper支持 -->
    <dependency>
      <groupId>tk.mybatis</groupId>
      <artifactId>mapper</artifactId>
      <version>4.1.5</version>
    </dependency>
    <dependency>
      <groupId>org.mybatis.generator</groupId>
      <artifactId>mybatis-generator-core</artifactId>
      <version>1.3.7</version>
<!--      <scope>test</scope>-->
    </dependency>

    <!-- 自动刷sql脚本工具包 2021-08-14 -->
<!--    <dependency>-->
<!--      <groupId>org.flywaydb</groupId>-->
<!--      <artifactId>flyway-core</artifactId>-->
<!--      <version>7.12.1</version>-->
<!--    </dependency>-->
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>ocr_api20210707</artifactId>
      <version>1.1.16</version>
    </dependency>

    <dependency>
      <groupId>com.bimface</groupId>
      <artifactId>bimface-java-sdk</artifactId>
      <version>3.3.1</version>
      <classifier>all</classifier>
      <exclusions>
        <exclusion>
          <groupId>com.glodon.paas.foundation</groupId>
          <artifactId>restclient</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--  解析mpp文件  -->
    <dependency>
      <groupId>net.sf.mpxj</groupId>
      <artifactId>mpxj</artifactId>
      <version>12.10.0</version>
    </dependency>

    <dependency>
      <groupId>commons-beanutils</groupId>
      <artifactId>commons-beanutils</artifactId>
      <version>1.9.4</version> <!-- 请检查最新版本 -->
    </dependency>

    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>javase</artifactId>
      <version>3.5.1</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.2.6.RELEASE</version>
        <configuration>
                    <!-- 指定该Main Class为全局的唯一入口 -->
                    <mainClass>com.easylinkin.linkappapi.LinkappApiApplication</mainClass>
                    <layout>ZIP</layout>
         </configuration>
		 <executions>
          <execution>
            <goals>
			  <!--可以把依赖的包都打包到生成的Jar包中-->
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- smart Doc 生成api接口文档插件 -->
      <plugin>
        <groupId>com.github.shalousun</groupId>
        <artifactId>smart-doc-maven-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <configFile>./src/main/resources/smart-doc.json</configFile>
          <excludes>
            <exclude>com.google.guava:guava</exclude>
          </excludes>
          <includes>
            <include>com.alibaba:fastjson</include>
          </includes>
          <skip>true</skip>  <!-- 跳过生成接口html -->
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>html</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>

    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <excludes>
          <exclude>application*.properties</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
        <includes>      <!-- 根据打包时激活的profile来包括对应的配置文件 -->
          <include>application.properties</include>
          <include>application-${profileActive}.properties</include>
        </includes>
      </resource>
    </resources>
  </build>

  <profiles>
    <profile>
      <id>dev</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <profileActive>dev</profileActive>
      </properties>
    </profile>
    <profile>
      <id>local</id>
      <properties>
        <profileActive>local</profileActive>
      </properties>
    </profile>
    <profile>
      <id>test</id>
      <properties>
        <profileActive>test</profileActive>
      </properties>
    </profile>
    <profile>
      <id>mirror</id>
      <properties>
        <profileActive>mirror</profileActive>
      </properties>
    </profile>
    <profile>
      <id>prd</id>
      <properties>
        <profileActive>prd</profileActive>
      </properties>
    </profile>
    <profile>
      <id>poc</id>
      <properties>
        <profileActive>poc</profileActive>
      </properties>
    </profile>
  </profiles>

</project>
