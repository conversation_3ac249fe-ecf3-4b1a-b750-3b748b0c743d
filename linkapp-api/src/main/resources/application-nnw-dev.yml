profile: nnw-test
# 本地起前端服务用
#server:
#  servlet:
#    context-path: /api

spring:
  datasource:
    #    publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAId4amgYmHPt2X/LMy2QYqkbG7DgofJoZ1z26cXi2gGyEEQgIFgM4IznQCG8Iv66fBW0cXFB2a2aq/+8LiAs5F0CAwEAAQ==
    publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALNUDhL3p7INgkL1kxTUwI/npf2oGHTwYv4q+UgbA0WFWMbau001oUSjBO9FWMnHqVXSUPXg0JMG3b5mtkKitsUCAwEAAQ==
    #  dev 数据库配置
    druid:
#      url: ****************************************************************************************************************************************************************************
#      username: linkapp_backend
#      password: LolWBiMdV+xILcd4KbRkyJvJhhH3zmucjkJF8YG2vWfyU4x5+1vALgBP10+esL2cPiY1QX6PDZ5rnggHYzbJBw==
        url: ************************************************************************************************************************************************
        username: linkapp
        password: ws6umbXqO2_KYghoD4GInbXNoyOIfPqd
#        password: ws6umbXqO2_KYghoD4GInbXNoyOIfPqd
#        password: ws6umbXqO2_KYghoD4GInbXNoyOIfPqd
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  flyway:
    # 是否启用flyway
    enabled: true
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: classpath:db/migration
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true

    # 整合thymeleaf 测试 websocket start ---
  #  mvc:
  #    view:
  #      prefix: classpath:/templates/
  #      suffix: .html
  #    static-path-pattern: /static/**
  #  #关闭thymeleaf的缓存，不然在开发过程中修改页面不会立刻生效需要重启，生产可配置为true
  #  thymeleaf:
  #    cache: false
  #    prefix: classpath:/templates/
  #    suffix: .html
  #    mode: HTML5
  #    encoding: UTF-8
  #  resources:
  #    chain:
  #      strategy:
  #        content:
  #          enabled: true
  #          paths: /**
  # 整合thymeleaf 测试 websocket end ---

  redis:
    database: 0
    #    host: linkapp-redis-in.db.dev.easylinkin.net
    #    port: 6379
    #    password: ibanyha1wxKIh8uuMa9VSrMHTXBfxDTE
#    host: *************
#    port: 63192
    host: ************
    port: 15052
    password: QOooooOQ_linkthings

  #默认dohealthcheck为localhost
  elasticsearch:
    rest:
      uris: ["${es.host}:${es.port}"]
#  kafka:
#    producer:
#      bootstrap-servers: **************:9092
#      batch-size: 16384
#      retries: 0
#      buffer-memory: 33554432
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#
#    consumer:
#      enable-auto-commit: true
#      group-id: gridMonitorGroup
#      auto-commit-interval: 1000
#      auto-offset-reset: latest
#      bootstrap-servers: **************:9092
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      concurrency: 3

# 天气模块配置
weather:
  # 天气API配置
  api:
    # 天气API的主机地址（和风天气）
    host: np2mtdt4fv.re.qweatherapi.com
    # API版本
    version: v7
    # 天气API的密钥，需要到 https://dev.qweather.com 申请
    key: 58d147ae03c64b9d84ebc8080c0b15f8
  
  # 定时任务配置
  task:
    # 是否启用天气定时任务，默认启用
    enabled: true

es:
  isUseOnlineEs: false
  host: ************
  port: 15053
# host: linkthings-es-in.db.prd.easylinkin.net

linkapp:
  url: http://************:16005
  defaultPassword: easylinkin@2020
  isEnableVerificationCode: false
  #   是否启用登录密码错误次数超限锁住用户机制
  enableLoginCountCheck: false
  configDeviceExecuteConditionCommand: -10
  airControllerDelay: 3000
  #   四棵中学的配置
  siKeSchool:
    water:
      propCode: water_flux
    electricity:
      propCode: positive_active_energy
    tenantId: 4fd467d36ca30de845c61645eac10965
    #   是否监听，ws实时推送
    openReceiving: false
  runMessageGenerate: false

linkthing:
  #emp同步
  clientId: a6742665981a4bc9b37dc6006546d824
  clientSecret: 6CBF9B2146C3512FBCBB078A8894E45732E0918E9B4F0AD31F7A2D05B0EDC354
  #saas账号
  saasId: a6742665981a4bc9b37dc6006546d824
  saasSecret: 6CBF9B2146C3512FBCBB078A8894E45732E0918E9B4F0AD31F7A2D05B0EDC354
  #数据下行
  keyId: 093bf019bf974481a0b4be7f25eeaccb
  appSecret: 22edd3274a7c4fdebcbc99bcd4cd6a73
  tenantId: 20000592
  url:
    auth_url: http://************:15032/
#    api_url: http://************:15012
    api_url: http://************:15012
    find_active_app_list: http://************:15012/api/appBuyList/findActiveAppList


message:
  #手机短信阿里云的参数
  product: Dysmsapi
  domain: dysmsapi.aliyuncs.com
  accessKeyId: LTAI4GJY8uMYC2XRw92YpbN7
  accessKeySecret: ******************************
  regingId: cn-hangzhou
  signName: 武汉慧联无限科技
  #腾讯邮件服务参数
  host: smtp.exmail.qq.com
  username: <EMAIL>
  password: AomruakoTSsDQQBL1
  subject: 设备告警
  port: 465


  #阿里云oss 的参数
oss:
#  #建工ali
#  type: ali
#  accessKeyId: LTAI5tPo4WdYmq1irwtLciAr
#  accessKeySecret: ******************************
#  endPoint: oss-cn-hangzhou.aliyuncs.com
#  endPoint-public: oss-cn-hangzhou.aliyuncs.com
#  bucket: jg-linkthings
#  dir: public/aep/test/
#  uploadHost: http://jg-linkthings.oss-cn-hangzhou.aliyuncs.com
#  readHost: http://oss-cn-hangzhou.aliyuncs.com

#  #  old liujiong add
  type: minio
  accessKeyId: nini_linkapp
  accessKeySecret: IciYkD2GsI9P9meTDsOx8qrJoL63AkpX
  endPoint: http://************:19000
  endPoint-public: http://************:19000
  bucket: linkapp
  dir: linkapp/public/
  readHost: http://************:16005/api/oss/download
  uploadHost: oss/uploadMinio

#  type: ali
#  accessKeyId: LTAI4GJY8uMYC2XRw92YpbN7
#  accessKeySecret: ******************************
#  endPoint: oss-cn-hangzhou-internal.aliyuncs.com
#  endPoint-public: oss-cn-hangzhou.aliyuncs.com
#  bucket: linksaas-not-prd
#  dir: linkapp/public/
#  uploadHost: https://linksaas-not-prd.oss-cn-hangzhou.aliyuncs.com
#  readHost: https://puboss2-not-prd.easylinkin.com

nb:
  nbVideoUrl: https://nb-video-dev.oss-cn-hangzhou.aliyuncs.com

ai:
  face:
    platform: baidu
    #    koala:
    #      username: <EMAIL>
    #      password: 123456
    #      token: 06585230-a123-4a48-a32b-f207e698b55c
    #      management: http://*************/
    #      recognize: http://*************:8866/
    baidu:
      ak: VHElOS0CgiqAorEW8iX4BrtY
      sk: NobqG3i3cbY6SMwVBqzw4xzrMP86mqep
      management: https://aip.baidubce.com/
      group: easylinkin


#高德地图API调用信息
gaode:
  api:
    #公司--key
    #key: ce9857bf787d8aa6fc4b5ca9f7426ce7

    #lqh--key
    key: ce9857bf787d8aa6fc4b5ca9f7426ce7
    sid: 385437
    #逆地理编码api接口地址
    wep_api_url: https://restapi.amap.com/v3/geocode/regeo?
    #坐标系转换api接口地址
    location_convert_url: https://restapi.amap.com/v3/assistant/coordinate/convert?
    #查询指定坐标与围栏关系
    track_location_url: https://tsapi.amap.com/v1/track/geofence/status/location?
    #创建查询电子围栏地址
    fenceUrl: https://tsapi.amap.com/v1/track/geofence


wechat:
  pay:
    third_url: http://linkos-pay-out-dev-service.easylinkin.com
    attach: 用户充值
    #支付完成后跳转到业务服务的链接
    backUrl: https://linkapp-out-dev-service.easylinkin.com/h5.html#/feesResult
    body: 水电表充值服务
    orgId: aep
    subjectId: 1
    sign: 35dd67140d34aba8528c313fdce51201
    #token: aj1UmMaIUQH87jhHJBAAka
    token: ffssswwewe
    #业务服务接受第三方支付结果回调链接
    biz_notify_url_: http://linkapp-out.dev.service.easylinkin.net/api/recharge/thirdAsynNotify
    #预支付跳转第三方链接
    jump_url: http://linkos-pay-out-dev-service.easylinkin.com/weixin?outTradeNo=
    #订单结果轮询链接
    ask_order_url: http://linkos-pay-out-dev-service.easylinkin.com/weixin/transactions

ipservice:
  #  url: http://*************:40759
  url: http://
  receiveImage: recv

#工地卫士
bladeGuard:
  apiUrl: https://www.whcjj.com/api
  username: guard
  password: e10adc3949ba59abbe56e057f20f883e
  tenantId: 000000
#配电箱微信扫描二维码
inspection:
  url: http://************:16005/constructionapph5/#/inspection

#基坑监测数据获取任务
scheduling:
  business:
    enabled: false
    jkMonitorDataJob:
      cron: 0 0/2 * * * ?
  warning:
    enabled: false
    checkWarning:
      #cron: 0 5 0 * * ?
      cron: 0 0/2 * * * ?
    userOutinWarning:
      cron: 0 0/15 * * * ?

#安全培训链接
edu:
  url: http://************:15004/#/login
  apiUrl: http://************:15004/api


safetyweb:
  url: http://************:15004/api/

dashboard:
  bim:
    bimAppKey: eEHnZtmB1HM5VIzZ4f0PmbrsO9qGAjpk
    bimAppSecret: ********************************
    bimOauth2TokenUrl: https://api.bimface.com/oauth2/token
    bimTokenUrl: https://api.bimface.com/view/token

photo:
  cache:
    directory: /temp/

jiguangpush:
  apns_production: false

#访问金蝶外部接口参数
jdparam:
  tokenUrl: http://auth.whucg.cn
  tokenPath: /sso/oauth2.0/accessToken
  clientId: b14504c6-0ea0-4a84-855f-d063e588bbe0
  clientSecret: axcKN6QUl8sncPyBxdTkFiyD
  redirectUri: http://dashboard.wced.com.cn:26005/easyv-3d/bigscreen.html?fromType=OA#/situationOverview16to9
  userInfoPath: /sso/oauth2.0/profile

#阿里OCR服务keyid和secret
aliocr:
  accesskeyid: LTAI5tB4EAxbDwGCjKvdzgbP
  accesskeysecret: ******************************
  endpoint: ocr-api.cn-hangzhou.aliyuncs.com

#闸机重发参数
gateparam:
  firsttime: 10
  secondtime: 10
  thirdtime: 3600
  maxtimes: 10

#对接光谷文化中心智慧建造云平台参数
systemdocking:
  projuid: 110110255
  urlprefix: "http://2.huhu520.com:50098"

system:
  request:
    time: 1767147198000

# 文件临时目录配置（测试环境）# 临时文件存储目录，可通过环境变量 linkapp_temp_dir 覆盖
file:
  temp:
    directory: ${linkapp_temp_dir:/temp/}

# bimFace配置信息
bimFace:
  appKey: QkviPnP1g2HlrTT6mTDaSYbo7s10ymvn
  appSecret: ********************************
  cachePrefix: bimFace
  apiBaseUrl: https://api.bimface.com
  timeout: 10000
  loginTokenUrl: /oauth2/token
  viewTokenUrl: /view/token



