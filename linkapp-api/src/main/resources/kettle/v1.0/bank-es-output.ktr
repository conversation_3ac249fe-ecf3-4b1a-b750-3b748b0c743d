<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>bank-es-output</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <directory>/</directory>
    <parameters>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2020/07/10 18:53:21.147</created_date>
    <modified_user>-</modified_user>
    <modified_date>2020/07/10 18:53:21.147</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Switch / case</from>
      <to>写日志 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>写日志 2 2</from>
      <to>字符串替换 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字符串替换 2 2</from>
      <to>设置变量</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字符串替换 3</from>
      <to>字符串替换 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>设置变量</from>
      <to>根据id获取设备 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>根据id获取设备 2</from>
      <to>写日志 7 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>从结果获取记录</from>
      <to>Switch / case</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>REST client</from>
      <to>抽取前一天聚合</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>REST client 2</from>
      <to>抽取当天聚合</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>抽取前一天聚合</from>
      <to>拼装当天es查询</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>抽取当天聚合</from>
      <to>写日志 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>抽取当天聚合</from>
      <to>统计当天</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>拼装前一天es查询</from>
      <to>REST client</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>拼装当天es查询</from>
      <to>REST client 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>写日志 7 2</from>
      <to>拼装前一天es查询</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>根据租户获取设备</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>根据空间区域获取设备</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>根据租户获取设备</from>
      <to>拼装前一天es查询</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>根据空间区域获取设备</from>
      <to>拼装前一天es查询</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>统计当天</from>
      <to>最终提交ES数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>统计当天</from>
      <to>删除已存在的当天记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>删除已存在的当天记录</from>
      <to>提交ES</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>删除已存在的当天记录</from>
      <to>删除es日志</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>提交ES</from>
      <to>提交es日志</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>REST client</name>
    <type>Rest</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <applicationType>JSON</applicationType>
    <method>POST</method>
    <url/>
    <urlInField>Y</urlInField>
    <dynamicMethod>N</dynamicMethod>
    <methodFieldName/>
    <urlField>esGetUrl</urlField>
    <bodyField>esGetBody</bodyField>
    <httpLogin/>
    <httpPassword>Encrypted </httpPassword>
    <proxyHost/>
    <proxyPort/>
    <preemptive>N</preemptive>
    <trustStoreFile/>
    <trustStorePassword>Encrypted </trustStorePassword>
    <headers>
      </headers>
    <parameters>
      </parameters>
    <matrixParameters>
      </matrixParameters>
    <result>
      <name>result</name>
      <code/>
      <response_time/>
      <response_header/>
    </result>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1098</xloc>
      <yloc>471</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>REST client 2</name>
    <type>Rest</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <applicationType>JSON</applicationType>
    <method>POST</method>
    <url/>
    <urlInField>Y</urlInField>
    <dynamicMethod>N</dynamicMethod>
    <methodFieldName/>
    <urlField>esGetUrlCurrDay</urlField>
    <bodyField>esGetBodyCurrDay</bodyField>
    <httpLogin/>
    <httpPassword>Encrypted </httpPassword>
    <proxyHost/>
    <proxyPort/>
    <preemptive>N</preemptive>
    <trustStoreFile/>
    <trustStorePassword>Encrypted </trustStorePassword>
    <headers>
      </headers>
    <parameters>
      </parameters>
    <matrixParameters>
      </matrixParameters>
    <result>
      <name>resultCurrDay</name>
      <code/>
      <response_time/>
      <response_header/>
    </result>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1514</xloc>
      <yloc>471</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Switch / case</name>
    <type>SwitchCase</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fieldname>dsType</fieldname>
    <use_contains>N</use_contains>
    <case_value_type>Integer</case_value_type>
    <case_value_format/>
    <case_value_decimal/>
    <case_value_group/>
    <default_target_step>字段选择 2</default_target_step>
    <cases>
      <case>
        <value>2</value>
        <target_step>写日志 2 2</target_step>
      </case>
      <case>
        <value>1</value>
        <target_step>根据空间区域获取设备</target_step>
      </case>
      <case>
        <value>0</value>
        <target_step>根据租户获取设备</target_step>
      </case>
    </cases>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>112</xloc>
      <yloc>224</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>从结果获取记录</name>
    <type>RowsFromResult</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>dsId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>cfgId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>unitCodes</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>attrIds</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>attrs</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>attrCoeffs</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>dsAreaId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>dsDeviceIds</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>dsType</name>
        <type>Integer</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>cfgName</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>cfgCode</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>ruleId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>ruleName</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>tenantId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>projectId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>16</xloc>
      <yloc>224</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志 2</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_error</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>dataESHits</name>
      </field>
      <field>
        <name>dataESAggrDeviceBuk</name>
      </field>
      <field>
        <name>dataESHitsCurrDay</name>
      </field>
      <field>
        <name>dataESAggrDeviceBukCurrDay</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1626</xloc>
      <yloc>343</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志 2 2</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_error</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>dsDeviceIds</name>
      </field>
      <field>
        <name>dsType</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>128</xloc>
      <yloc>352</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志 7 2</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_error</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>es前1</logmessage>
    <fields>
      <field>
        <name>devCodes</name>
      </field>
      <field>
        <name>dsId</name>
      </field>
      <field>
        <name>cfgId</name>
      </field>
      <field>
        <name>unitCodes</name>
      </field>
      <field>
        <name>attrIds</name>
      </field>
      <field>
        <name>attrs</name>
      </field>
      <field>
        <name>attrCoeffs</name>
      </field>
      <field>
        <name>dsAreaId</name>
      </field>
      <field>
        <name>dsDeviceIds</name>
      </field>
      <field>
        <name>dsType</name>
      </field>
      <field>
        <name>cfgName</name>
      </field>
      <field>
        <name>cfgCode</name>
      </field>
      <field>
        <name>ruleId</name>
      </field>
      <field>
        <name>ruleName</name>
      </field>
      <field>
        <name>tenantId</name>
      </field>
      <field>
        <name>projectId</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>784</xloc>
      <yloc>336</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>删除es日志</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>resultDelete</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>720</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>删除已存在的当天记录</name>
    <type>Rest</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <applicationType>JSON</applicationType>
    <method>POST</method>
    <url>http://linkthings-es-in.db.dev.easylinkin.net:9200/etl/voltage_avg</url>
    <urlInField>Y</urlInField>
    <dynamicMethod>N</dynamicMethod>
    <methodFieldName/>
    <urlField>esDeleteUrl</urlField>
    <bodyField>esDeleteBody</bodyField>
    <httpLogin/>
    <httpPassword>Encrypted </httpPassword>
    <proxyHost/>
    <proxyPort/>
    <preemptive>N</preemptive>
    <trustStoreFile/>
    <trustStorePassword>Encrypted </trustStorePassword>
    <headers>
      </headers>
    <parameters>
      </parameters>
    <matrixParameters>
      </matrixParameters>
    <result>
      <name>resultDelete</name>
      <code/>
      <response_time/>
      <response_header/>
    </result>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1392</xloc>
      <yloc>608</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>字符串替换 2 2</name>
    <type>ReplaceString</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <in_stream_name>alist1</in_stream_name>
        <out_stream_name>alist2</out_stream_name>
        <use_regex>yes</use_regex>
        <replace_string>^|$</replace_string>
        <replace_by_string>'</replace_by_string>
        <set_empty_string>N</set_empty_string>
        <replace_field_by_string/>
        <whole_word>no</whole_word>
        <case_sensitive>no</case_sensitive>
        <is_unicode>no</is_unicode>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>384</xloc>
      <yloc>352</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>字符串替换 3</name>
    <type>ReplaceString</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <in_stream_name>dsDeviceIds</in_stream_name>
        <out_stream_name>alist1</out_stream_name>
        <use_regex>yes</use_regex>
        <replace_string>,</replace_string>
        <replace_by_string>','</replace_by_string>
        <set_empty_string>N</set_empty_string>
        <replace_field_by_string/>
        <whole_word>no</whole_word>
        <case_sensitive>no</case_sensitive>
        <is_unicode>no</is_unicode>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>240</xloc>
      <yloc>352</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>抽取前一天聚合</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>dataESHits</name>
        <path>$.hits.hits</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>dataESAggrDeviceBuk</name>
        <path>$.aggregations.by_deviceCode.buckets</path>
        <type>None</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>result</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1226</xloc>
      <yloc>471</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>抽取当天聚合</name>
    <type>JsonInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>dataESHitsCurrDay</name>
        <path>$.hits.hits</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>dataESAggrDeviceBukCurrDay</name>
        <path>$.aggregations.by_deviceCode.buckets</path>
        <type>None</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>resultCurrDay</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1626</xloc>
      <yloc>471</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>拼装前一天es查询</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compatible>N</compatible>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here
var nday = getVariable("nday", "");
var xday = nday;
var nows = "now-";
var dd = "d/d";
var ltDay = nows + xday + dd;
var da = Number(xday) + 1;
var gtDay = nows + da + dd;



var es_host = getVariable("es_host", "");
var es_param = getVariable("es_param_search", "");
var bank_tenant = getVariable("bank_tenant", "");
var esIndex = (bank_tenant == "null") ? projectId : bank_tenant;
var esGetUrl = es_host + "/" + esIndex + "/_search?size=0";

writeToLog("e", esGetUrl);

//var es_timeRange = '{"range":{"createTime":{"gte":"now-2d/d","lt":"now-1d/d"}}}';//数据修改处
var es_timeRange = '{"range":{"createTime":{"gte":"' + gtDay + '","lt":"' + ltDay + '","time_zone":"+08:00"}}}';
var es_aggrs = '"aggs":{"by_deviceCode":{"terms":{"field":"deviceCode.keyword","size": **********},"aggs":{}}}';


var es_bodyJson = JSON.parse('{"query":{"bool":{"filter":[{"bool":{"should":[]}},' + es_timeRange + '],"should":[],"minimum_should_match":1}},' + es_aggrs + '}');


writeToLog("e", "拼接设备code");

var deviceCodes = devCodes.split(",");
for (var i=0; i&lt;deviceCodes.length; i++) {
    es_bodyJson.query.bool.filter[0].bool.should.push({"match_phrase": {"deviceCode": deviceCodes[i]}});
}

writeToLog("e", "拼接设备属性");
var attIdArr = attrs.split(",");
for (i=0; i&lt;attIdArr.length; i++) {
    es_bodyJson.query.bool.should.push({"exists": {"field": attIdArr[i]}});
    es_bodyJson.aggs.by_deviceCode.aggs["max_attr_" + attIdArr[i]] = {"max":{"field": attIdArr[i]}};
}

var esGetBody = JSON.stringify(es_bodyJson);

println(esGetUrl);
writeToLog("e", esGetBody);</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>esGetUrl</name>
        <rename>esGetUrl</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esGetBody</name>
        <rename>esGetBody</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>912</xloc>
      <yloc>160</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>拼装当天es查询</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compatible>N</compatible>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here
var nday = getVariable("nday", "");
var xday = nday;
var gtDay2 = "now-" + xday + "d/d";
var ltDay2 = "";
if(1 == xday) {
	ltDay2 = "now/d";
}else {
	var da = Number(xday) - 1;
	ltDay2 = "now-" + da + "d/d";
}

var es_host = getVariable("es_host", "");
var es_param = getVariable("es_param_search", "");
var bank_tenant = getVariable("bank_tenant", "");
var esIndex = (bank_tenant == "null") ? projectId : bank_tenant;
var esGetUrlCurrDay = es_host + "/" + esIndex + "/_search?size=0";

writeToLog("e", esGetUrlCurrDay);

//var es_timeRange = '{"range":{"createTime":{"gte":"now-1d/d","lt":"now/d"}}}';//数据修改处
var es_timeRange = '{"range":{"createTime":{"gte":"' + gtDay2 + '","lt":"' + ltDay2 + '","time_zone":"+08:00"}}}';
var es_aggrs = '"aggs":{"by_deviceCode":{"terms":{"field":"deviceCode.keyword","size": **********},"aggs":{}}}';
var es_bodyJson = JSON.parse('{"query":{"bool":{"filter":[{"bool":{"should":[]}},'
     + es_timeRange + '],"should":[],"minimum_should_match":1}},' + es_aggrs +'}');


writeToLog("e", "拼接设备code");

var deviceCodes = devCodes.split(",");
for (var i=0; i&lt;deviceCodes.length; i++) {
    es_bodyJson.query.bool.filter[0].bool.should.push({"match_phrase": {"deviceCode": deviceCodes[i]}});
}

writeToLog("e", "拼接设备属性");

var attIdArr = attrs.split(",");
for (var i=0; i&lt;attIdArr.length; i++) {
    es_bodyJson.query.bool.should.push({"exists": {"field": attIdArr[i]}});
    es_bodyJson.aggs.by_deviceCode.aggs["max_attr_" + attIdArr[i]] = {"max":{"field": attIdArr[i]}};
}

var esGetBodyCurrDay = JSON.stringify(es_bodyJson);


writeToLog("e", esGetBodyCurrDay);</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>esGetUrlCurrDay</name>
        <rename>esGetUrlCurrDay</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esGetBodyCurrDay</name>
        <rename>esGetBodyCurrDay</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1386</xloc>
      <yloc>471</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>提交ES</name>
    <type>Rest</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <applicationType>JSON</applicationType>
    <method>PUT</method>
    <url>http://linkthings-es-in.db.dev.easylinkin.net:9200/etl/voltage_avg</url>
    <urlInField>Y</urlInField>
    <dynamicMethod>N</dynamicMethod>
    <methodFieldName/>
    <urlField>esPostUrl</urlField>
    <bodyField>esPostBody2</bodyField>
    <httpLogin/>
    <httpPassword>Encrypted </httpPassword>
    <proxyHost/>
    <proxyPort/>
    <preemptive>N</preemptive>
    <trustStoreFile/>
    <trustStorePassword>Encrypted </trustStorePassword>
    <headers>
      </headers>
    <parameters>
      </parameters>
    <matrixParameters>
      </matrixParameters>
    <result>
      <name>resultPost</name>
      <code/>
      <response_time/>
      <response_header/>
    </result>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1104</xloc>
      <yloc>608</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>提交es日志</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>resultPost</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>960</xloc>
      <yloc>608</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>最终提交ES数据</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>esGetUrl</name>
      </field>
      <field>
        <name>esGetBody</name>
      </field>
      <field>
        <name>result</name>
      </field>
      <field>
        <name>dataESHits</name>
      </field>
      <field>
        <name>dataESAggrDeviceBuk</name>
      </field>
      <field>
        <name>esGetUrlCurrDay</name>
      </field>
      <field>
        <name>esGetBodyCurrDay</name>
      </field>
      <field>
        <name>resultCurrDay</name>
      </field>
      <field>
        <name>dataESHitsCurrDay</name>
      </field>
      <field>
        <name>dataESAggrDeviceBukCurrDay</name>
      </field>
      <field>
        <name>esPostUrl</name>
      </field>
      <field>
        <name>esPostBody2</name>
      </field>
      <field>
        <name>esDeleteUrl</name>
      </field>
      <field>
        <name>esDeleteBody</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1520</xloc>
      <yloc>752</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>根据id获取设备 2</name>
    <type>TransExecutor</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <specification_method>filename</specification_method>
    <trans_object_id/>
    <trans_name/>
    <filename>${Internal.Entry.Current.Directory}/sub_trans2.ktr</filename>
    <directory_path/>
    <group_size>1</group_size>
    <group_field/>
    <group_time/>
    <parameters>
      <variablemapping>
        <variable>dsDeviceIdStrList</variable>
        <field>alist2</field>
        <input/>
      </variablemapping>
      <inherit_all_vars>Y</inherit_all_vars>
    </parameters>
    <execution_result_target_step/>
    <execution_time_field>ExecutionTime</execution_time_field>
    <execution_result_field>ExecutionResult</execution_result_field>
    <execution_errors_field>ExecutionNrErrors</execution_errors_field>
    <execution_lines_read_field>ExecutionLinesRead</execution_lines_read_field>
    <execution_lines_written_field>ExecutionLinesWritten</execution_lines_written_field>
    <execution_lines_input_field>ExecutionLinesInput</execution_lines_input_field>
    <execution_lines_output_field>ExecutionLinesOutput</execution_lines_output_field>
    <execution_lines_rejected_field>ExecutionLinesRejected</execution_lines_rejected_field>
    <execution_lines_updated_field>ExecutionLinesUpdated</execution_lines_updated_field>
    <execution_lines_deleted_field>ExecutionLinesDeleted</execution_lines_deleted_field>
    <execution_files_retrieved_field>ExecutionFilesRetrieved</execution_files_retrieved_field>
    <execution_exit_status_field>ExecutionExitStatus</execution_exit_status_field>
    <execution_log_text_field>ExecutionLogText</execution_log_text_field>
    <execution_log_channelid_field>ExecutionLogChannelId</execution_log_channelid_field>
    <result_rows_target_step>写日志 7 2</result_rows_target_step>
    <result_files_target_step/>
    <result_files_file_name_field>FileName</result_files_file_name_field>
    <executors_output_step/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>656</xloc>
      <yloc>352</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>根据租户获取设备</name>
    <type>TransExecutor</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <specification_method>filename</specification_method>
    <trans_object_id/>
    <trans_name/>
    <filename>${Internal.Entry.Current.Directory}/sub_trans1.ktr</filename>
    <directory_path/>
    <group_size>1</group_size>
    <group_field/>
    <group_time/>
    <parameters>
      <inherit_all_vars>Y</inherit_all_vars>
    </parameters>
    <execution_result_target_step/>
    <execution_time_field>ExecutionTime</execution_time_field>
    <execution_result_field>ExecutionResult</execution_result_field>
    <execution_errors_field>ExecutionNrErrors</execution_errors_field>
    <execution_lines_read_field>ExecutionLinesRead</execution_lines_read_field>
    <execution_lines_written_field>ExecutionLinesWritten</execution_lines_written_field>
    <execution_lines_input_field>ExecutionLinesInput</execution_lines_input_field>
    <execution_lines_output_field>ExecutionLinesOutput</execution_lines_output_field>
    <execution_lines_rejected_field>ExecutionLinesRejected</execution_lines_rejected_field>
    <execution_lines_updated_field>ExecutionLinesUpdated</execution_lines_updated_field>
    <execution_lines_deleted_field>ExecutionLinesDeleted</execution_lines_deleted_field>
    <execution_files_retrieved_field>ExecutionFilesRetrieved</execution_files_retrieved_field>
    <execution_exit_status_field>ExecutionExitStatus</execution_exit_status_field>
    <execution_log_text_field>ExecutionLogText</execution_log_text_field>
    <execution_log_channelid_field>ExecutionLogChannelId</execution_log_channelid_field>
    <result_rows_target_step>拼装前一天es查询</result_rows_target_step>
    <result_files_target_step/>
    <result_files_file_name_field>FileName</result_files_file_name_field>
    <executors_output_step/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>112</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>根据空间区域获取设备</name>
    <type>TransExecutor</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <specification_method>filename</specification_method>
    <trans_object_id/>
    <trans_name/>
    <filename>${Internal.Entry.Current.Directory}/sub_trans3.ktr</filename>
    <directory_path/>
    <group_size>1</group_size>
    <group_field/>
    <group_time/>
    <parameters>
      <inherit_all_vars>Y</inherit_all_vars>
    </parameters>
    <execution_result_target_step/>
    <execution_time_field>ExecutionTime</execution_time_field>
    <execution_result_field>ExecutionResult</execution_result_field>
    <execution_errors_field>ExecutionNrErrors</execution_errors_field>
    <execution_lines_read_field>ExecutionLinesRead</execution_lines_read_field>
    <execution_lines_written_field>ExecutionLinesWritten</execution_lines_written_field>
    <execution_lines_input_field>ExecutionLinesInput</execution_lines_input_field>
    <execution_lines_output_field>ExecutionLinesOutput</execution_lines_output_field>
    <execution_lines_rejected_field>ExecutionLinesRejected</execution_lines_rejected_field>
    <execution_lines_updated_field>ExecutionLinesUpdated</execution_lines_updated_field>
    <execution_lines_deleted_field>ExecutionLinesDeleted</execution_lines_deleted_field>
    <execution_files_retrieved_field>ExecutionFilesRetrieved</execution_files_retrieved_field>
    <execution_exit_status_field>ExecutionExitStatus</execution_exit_status_field>
    <execution_log_text_field>ExecutionLogText</execution_log_text_field>
    <execution_log_channelid_field>ExecutionLogChannelId</execution_log_channelid_field>
    <result_rows_target_step>拼装前一天es查询</result_rows_target_step>
    <result_files_target_step/>
    <result_files_file_name_field>FileName</result_files_file_name_field>
    <executors_output_step/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>224</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>统计当天</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compatible>N</compatible>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//var xday = 1;
var nday = getVariable("nday", "");
var xday = nday;//测试变更数据
//Script here
var timestamp = new Date().getTime();
var etlKind = "electric";

var es_host = getVariable("es_host", "");
var es_param = getVariable("es_param_search", "");
var es_index_prefix = getVariable("es_index_prefix", "");
var esIndex = es_index_prefix + projectId;
var esPostUrl = es_host + "/" + esIndex + "/energy/" + timestamp;


//writeToLog('e', deviceRec);
//var spaceRec = spaceDatas;

// 前一天的聚合数据
eval('var dataPreDay = ' + dataESAggrDeviceBuk);

// 当天的
eval('var dataCurrDay = ' + dataESAggrDeviceBukCurrDay);

// 数组去重
function uniqueArr(arr){
  var hash=[];
  for (var i = 0; i &lt; arr.length; i++) {
    if(hash.indexOf(arr[i])==-1){
      hash.push(arr[i]);
    }
  }
  return hash;
}


var deviceCnt = 0;
var energyDay = 0;
var energyDayChangeRate = 0.0;
var enertyDDDD = "";

if (dataPreDay) {
  deviceCnt = dataPreDay.length;
}
for(var i = 0; i &lt; deviceCnt; i++) {
  var devKey = dataPreDay[i].key;
  for(var j = 0;j &lt; dataCurrDay.length; j++) {
    var devCurKey =  dataCurrDay[j].key;

    if(devKey === devCurKey) {
      // 不同型号表达同种属性的名字可能不同（一个计算配置下有多个型号，且型号属性不同），需遍历Attr累计
      var attIdArr = uniqueArr(attrs.split(","));
      for (var attIndex=0; attIndex&lt;attIdArr.length; attIndex++) {
        var attAggName = "max_attr_" + attIdArr[attIndex];
        if (dataCurrDay[j][attAggName] &amp;&amp; dataPreDay[i][attAggName]) {
          energyDay += ((dataCurrDay[j][attAggName].value) - dataPreDay[i][attAggName].value);
        }
      }
    }

  }

}

//返回格式化后的日期
function dateFtt(fmt, date) { //author: meizz
  var o = {
    "M+": date.getMonth() + 1, //月份
    "d+": date.getDate(), //日
    "h+": date.getHours(), //小时
    "m+": date.getMinutes(), //分
    "s+": date.getSeconds(), //秒
    "q+": Math.floor((date.getMonth() + 3) / 3), //季度
    "S": date.getMilliseconds() //毫秒
  };
  if(/(y+)/.test(fmt))
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  for(var k in o)
    if(new RegExp("(" + k + ")").test(fmt))
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
  return fmt;
}


//var yesterdayTime=(new Date).getTime()-24*60*60*1000;//数据修改处
var yesterdayTime=(new Date).getTime()-24*60*60*1000*xday;
var yesterday=new Date(yesterdayTime);
var calcDate = dateFtt("yyyy-MM-dd", yesterday);
//var createTime = dateFtt("yyyy-MM-dd hh:mm:ss", new Date());
var createTime = new Date();

var jobId = timestamp;

var calcRuleId = ruleId;
var calcRuleName = ruleName;
var calcCfgId = cfgId;
var calcCfgName = cfgName;

var dataSourceId = dsId;
var dataSource = dsAreaId;

var energyType = energyType;
var deviceCnt = deviceCnt;
var energyDay = energyDay;


var esPostBody = JSON.parse("{}");
esPostBody["jobId"] = jobId;
esPostBody["calcRuleId"] = calcRuleId;
esPostBody["calcRuleName"] = calcRuleId;
esPostBody["calcCfgId"] = calcCfgId;
esPostBody["calcCfgName"] = calcCfgName;
esPostBody["dataSourceId"] = dataSourceId;
esPostBody["dataSource"] = dataSourceId;
esPostBody["energyType"] = energyType;
esPostBody["deviceCnt"] = deviceCnt;
//if("4a3e1b78d7992d3e575f5c978244b210" == dataSourceId) {
//	energyDay = energyDay*400;
//}else {
//	energyDay = energyDay*100;
//}
esPostBody["etlValue"] = energyDay;

//esPostBody["energyDayChangeRate"] = 0;
//esPostBody["energyWeek"] = 0;
//esPostBody["energyMonth"] = 0;
//esPostBody["energy7Day"] = 0;
//esPostBody["energy7DayChangeRate"] = 0;
//esPostBody["energy30Day"] = 0;
//esPostBody["energy30DayChangeRate"] = 0;
esPostBody["etlKind"] = etlKind;


esPostBody["calcDate"] = calcDate;
esPostBody["createTime"] = createTime;


var esPostBody2 = JSON.stringify(esPostBody);


// delete exist calcDate es record
var esDeleteUrl = es_host + "/" + esIndex + "/_delete_by_query";
var esDeleteBody = '{"query":{"bool":{"must":[{"term":{"calcDate":"' + calcDate
  + '"}},{"term":{"dataSourceId":"' + dataSourceId +'"}},{"term":{"etlKind":"' + etlKind
  + '"}},{"term":{"calcCfgId":"' + calcCfgId +'"}},{"term":{"calcRuleId":"' + calcRuleId + '"}}]}}}';

</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>esPostUrl</name>
        <rename>esPostUrl</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esPostBody2</name>
        <rename>esPostBody2</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esDeleteUrl</name>
        <rename>esDeleteUrl</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esDeleteBody</name>
        <rename>esDeleteBody</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1626</xloc>
      <yloc>615</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>设置变量</name>
    <type>SetVariable</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <field_name>alist2</field_name>
        <variable_name>dsDeviceIdStrList</variable_name>
        <variable_type>ROOT_JOB</variable_type>
        <default_value/>
      </field>
    </fields>
    <use_formatting>Y</use_formatting>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>352</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
