<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>bank-tenant-data3</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2020/07/04 17:44:56.332</created_date>
    <modified_user>-</modified_user>
    <modified_date>2020/07/04 17:44:56.332</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>从结果获取记录</from>
      <to>写日志</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>从结果获取记录</from>
      <to>拼装前一天es查询</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>拼装前一天es查询</from>
      <to>REST client</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>REST client</from>
      <to>抽取前一天聚合</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>抽取前一天聚合</from>
      <to>拼装当天es查询</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>REST client 2</from>
      <to>抽取当天聚合</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>拼装当天es查询</from>
      <to>REST client 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>抽取当天聚合</from>
      <to>写日志 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>抽取当天聚合</from>
      <to>统计当天</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>拼装7天/30天的统计查询</from>
      <to>REST client 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>统计当天</from>
      <to>拼装7天/30天的统计查询</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>REST client 3</from>
      <to>抽取7天/30天的聚合</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>抽取7天/30天的聚合</from>
      <to>统计7天/30天的数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>统计7天/30天的数据</from>
      <to>提交ES</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>统计当天</from>
      <to>写日志 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>拼装7天/30天的统计查询</from>
      <to>写日志 4</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>统计7天/30天的数据</from>
      <to>写日志 5</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>抽取7天/30天的聚合</from>
      <to>写日志 6</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>REST client</name>
    <type>Rest</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <applicationType>JSON</applicationType>
    <method>POST</method>
    <url/>
    <urlInField>Y</urlInField>
    <dynamicMethod>N</dynamicMethod>
    <methodFieldName/>
    <urlField>esGetUrl</urlField>
    <bodyField>esGetBody</bodyField>
    <httpLogin/>
    <httpPassword>Encrypted </httpPassword>
    <proxyHost/>
    <proxyPort/>
    <preemptive>N</preemptive>
    <trustStoreFile/>
    <trustStorePassword>Encrypted </trustStorePassword>
    <headers>
      </headers>
    <parameters>
      </parameters>
    <matrixParameters>
      </matrixParameters>
    <result>
      <name>result</name>
      <code/>
      <response_time/>
      <response_header/>
    </result>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>416</xloc>
      <yloc>240</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>REST client 2</name>
    <type>Rest</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <applicationType>JSON</applicationType>
    <method>POST</method>
    <url/>
    <urlInField>Y</urlInField>
    <dynamicMethod>N</dynamicMethod>
    <methodFieldName/>
    <urlField>esGetUrlCurrDay</urlField>
    <bodyField>esGetBodyCurrDay</bodyField>
    <httpLogin/>
    <httpPassword>Encrypted </httpPassword>
    <proxyHost/>
    <proxyPort/>
    <preemptive>N</preemptive>
    <trustStoreFile/>
    <trustStorePassword>Encrypted </trustStorePassword>
    <headers>
      </headers>
    <parameters>
      </parameters>
    <matrixParameters>
      </matrixParameters>
    <result>
      <name>resultCurrDay</name>
      <code/>
      <response_time/>
      <response_header/>
    </result>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>832</xloc>
      <yloc>240</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>REST client 3</name>
    <type>Rest</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <applicationType>JSON</applicationType>
    <method>POST</method>
    <url/>
    <urlInField>Y</urlInField>
    <dynamicMethod>N</dynamicMethod>
    <methodFieldName/>
    <urlField>esGetUrlDays</urlField>
    <bodyField>esGetBodyDays</bodyField>
    <httpLogin/>
    <httpPassword>Encrypted </httpPassword>
    <proxyHost/>
    <proxyPort/>
    <preemptive>N</preemptive>
    <trustStoreFile/>
    <trustStorePassword>Encrypted </trustStorePassword>
    <headers>
      </headers>
    <parameters>
      </parameters>
    <matrixParameters>
      </matrixParameters>
    <result>
      <name>resultDays</name>
      <code/>
      <response_time/>
      <response_header/>
    </result>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>640</xloc>
      <yloc>384</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>从结果获取记录</name>
    <type>RowsFromResult</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>dsId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>cfgId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>unitCodes</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>attrIds</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>attrs</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>attrCoeffs</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>dsAreaId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>dsDeviceIds</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>dsType</name>
        <type>Integer</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>cfgName</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>cfgCode</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>ruleId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>ruleName</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>tenantId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>projectId</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>devCodes</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>128</xloc>
      <yloc>176</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_error</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>dsId</name>
      </field>
      <field>
        <name>cfgId</name>
      </field>
      <field>
        <name>unitCodes</name>
      </field>
      <field>
        <name>attrIds</name>
      </field>
      <field>
        <name>attrs</name>
      </field>
      <field>
        <name>attrCoeffs</name>
      </field>
      <field>
        <name>cfgName</name>
      </field>
      <field>
        <name>cfgCode</name>
      </field>
      <field>
        <name>ruleId</name>
      </field>
      <field>
        <name>ruleName</name>
      </field>
      <field>
        <name>tenantId</name>
      </field>
      <field>
        <name>projectId</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>256</xloc>
      <yloc>80</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志 2</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_error</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>dataESHits</name>
      </field>
      <field>
        <name>dataESAggrDeviceBuk</name>
      </field>
      <field>
        <name>dataESHitsCurrDay</name>
      </field>
      <field>
        <name>dataESAggrDeviceBukCurrDay</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>944</xloc>
      <yloc>112</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志 3</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_error</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>energyDay</name>
      </field>
      <field>
        <name>enertyDDDD</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>944</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志 4</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>esGetBodyDays</name>
      </field>
      <field>
        <name>esGetUrlDays</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志 5</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>esPostUrl</name>
      </field>
      <field>
        <name>esPostBody2</name>
      </field>
      <field>
        <name>esPostBody3</name>
      </field>
      <field>
        <name>esPostBody4</name>
      </field>
      <field>
        <name>projectId</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>336</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志 6</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>getSevenThirty</name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>496</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>抽取7天/30天的聚合</name>
    <type>JsonInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>getSevenThirty</name>
        <path>$.hits.hits</path>
        <type>None</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>resultDays</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>496</xloc>
      <yloc>384</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>抽取前一天聚合</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>dataESHits</name>
        <path>$.hits.hits</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>dataESAggrDeviceBuk</name>
        <path>$.aggregations.by_deviceCode.buckets</path>
        <type>None</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>result</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>544</xloc>
      <yloc>240</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>抽取当天聚合</name>
    <type>JsonInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>dataESHitsCurrDay</name>
        <path>$.hits.hits</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>dataESAggrDeviceBukCurrDay</name>
        <path>$.aggregations.by_deviceCode.buckets</path>
        <type>None</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>resultCurrDay</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>944</xloc>
      <yloc>240</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>拼装7天/30天的统计查询</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compatible>N</compatible>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here


var es_host = getVariable("es_host", "");
var es_param = getVariable("es_param_search", "");
var es_index_prefix = getVariable("es_index_prefix", "");
var esIndex = es_index_prefix + projectId;
var esGetUrlDays = es_host + "/" + esIndex + "/energy/_search";

// todo: 拼接7天/30天的聚合查询语句

var esGetBodyDays = "{}";




var es_timeRange = '{"range":{"calcDate":{"gte":"now-60d/d","lt":"now"}}}';
var es_bodyJson = JSON.parse('{"sort":{"calcDate":{"order":"desc"}},"query":'
     + es_timeRange + '}');


		

var esGetBodyDays = JSON.stringify(es_bodyJson);</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>esGetUrlDays</name>
        <rename>esGetUrlDays</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esGetBodyDays</name>
        <rename>esGetBodyDays</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>384</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>拼装前一天es查询</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compatible>N</compatible>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here


var es_host = getVariable("es_host", "");
var es_param = getVariable("es_param_search", "");
var bank_tenant = getVariable("bank_tenant", "");
var esIndex = (bank_tenant == "null") ? projectId : bank_tenant;
var esGetUrl = es_host + "/" + esIndex + "/_search?size=0";

writeToLog("e", esGetUrl);

var es_timeRange = '{"range":{"createTime":{"gte":"now-2d/d","lt":"now-1d/d"}}}';
var es_aggrs = '"aggs":{"by_deviceCode":{"terms":{"field":"deviceCode.keyword"},"aggs":{}}}';
var es_bodyJson = JSON.parse('{"query":{"bool":{"filter":[{"bool":{"should":[]}},'
     + es_timeRange + '],"should":[],"minimum_should_match":1}},' + es_aggrs +'}');


writeToLog("e", "拼接设备code");

var deviceCodes = devCodes.split(",");
for (var i=0; i&lt;deviceCodes.length; i++) {
    es_bodyJson.query.bool.filter[0].bool.should.push({"match": {"deviceCode": deviceCodes[i]}});
}

writeToLog("e", "拼接设备属性");
var attIdArr = attrs.split(",");
for (i=0; i&lt;attIdArr.length; i++) {
    es_bodyJson.query.bool.should.push({"exists": {"field": attIdArr[i]}});
    es_bodyJson.aggs.by_deviceCode.aggs["max_"+attIdArr[i]] = {"max":{"field": attIdArr[i]}};
}

var esGetBody = JSON.stringify(es_bodyJson);

println(esGetUrl);
writeToLog("e", esGetBody);</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>esGetUrl</name>
        <rename>esGetUrl</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esGetBody</name>
        <rename>esGetBody</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>288</xloc>
      <yloc>240</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>拼装当天es查询</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compatible>N</compatible>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here


var es_host = getVariable("es_host", "");
var es_param = getVariable("es_param_search", "");
var bank_tenant = getVariable("bank_tenant", "");
var esIndex = (bank_tenant == "null") ? projectId : bank_tenant;
var esGetUrlCurrDay = es_host + "/" + esIndex + "/_search?size=0";

writeToLog("e", esGetUrlCurrDay);

var es_timeRange = '{"range":{"createTime":{"gte":"now-1d/d","lt":"now/d"}}}';
var es_aggrs = '"aggs":{"by_deviceCode":{"terms":{"field":"deviceCode.keyword"},"aggs":{}}}';
var es_bodyJson = JSON.parse('{"query":{"bool":{"filter":[{"bool":{"should":[]}},'
     + es_timeRange + '],"should":[],"minimum_should_match":1}},' + es_aggrs +'}');


writeToLog("e", "拼接设备code");

var deviceCodes = devCodes.split(",");
for (var i=0; i&lt;deviceCodes.length; i++) {
    es_bodyJson.query.bool.filter[0].bool.should.push({"match": {"deviceCode": deviceCodes[i]}});
}

writeToLog("e", "拼接设备属性");

var attIdArr = attrs.split(",");
for (var i=0; i&lt;attIdArr.length; i++) {
    es_bodyJson.query.bool.should.push({"exists": {"field": attIdArr[i]}});
    es_bodyJson.aggs.by_deviceCode.aggs["max_"+attIdArr[i]] = {"max":{"field": attIdArr[i]}};
}

var esGetBodyCurrDay = JSON.stringify(es_bodyJson);


writeToLog("e", esGetBodyCurrDay);</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>esGetUrlCurrDay</name>
        <rename>esGetUrlCurrDay</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esGetBodyCurrDay</name>
        <rename>esGetBodyCurrDay</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>704</xloc>
      <yloc>240</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>提交ES</name>
    <type>Rest</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <applicationType>JSON</applicationType>
    <method>PUT</method>
    <url>http://linkthings-es-in.db.dev.easylinkin.net:9200/etl/voltage_avg</url>
    <urlInField>Y</urlInField>
    <dynamicMethod>N</dynamicMethod>
    <methodFieldName/>
    <urlField>esPostUrl</urlField>
    <bodyField>esPostBody2</bodyField>
    <httpLogin/>
    <httpPassword>Encrypted </httpPassword>
    <proxyHost/>
    <proxyPort/>
    <preemptive>N</preemptive>
    <trustStoreFile/>
    <trustStorePassword>Encrypted </trustStorePassword>
    <headers>
      </headers>
    <parameters>
      </parameters>
    <matrixParameters>
      </matrixParameters>
    <result>
      <name>resultPost</name>
      <code/>
      <response_time/>
      <response_header/>
    </result>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>192</xloc>
      <yloc>384</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>统计7天/30天的数据</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compatible>N</compatible>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here
var timestamp = new Date().getTime();

var es_host = getVariable("es_host", "");
var es_param = getVariable("es_param_search", "");
var es_index_prefix = getVariable("es_index_prefix", "");
var esIndex = es_index_prefix + projectId;
var esPostUrl = es_host + "/" + esIndex + "/energy/" + timestamp;

// todo


var energyWeek = 0;

var energyMonth = 0;

var calcDate;
var createTime = new Date();


var jobId = timestamp;

var calcRuleId = ruleId;
var calcRuleName = ruleName;
var calcCfgId = cfgId;
var calcCfgName = cfgName;

var dataSourceId = dsId;
var dataSource = dsAreaId;

var energyType = energyType;
var deviceCnt = deviceCnt;
var energyDay = energyDay;

//返回格式化后的日期
function dateFtt(fmt, date) { //author: meizz   
			var o = {
				"M+": date.getMonth() + 1, //月份   
				"d+": date.getDate(), //日   
				"h+": date.getHours(), //小时   
				"m+": date.getMinutes(), //分   
				"s+": date.getSeconds(), //秒   
				"q+": Math.floor((date.getMonth() + 3) / 3), //季度   
				"S": date.getMilliseconds() //毫秒   
			};
			if(/(y+)/.test(fmt))
				fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
			for(var k in o)
				if(new RegExp("(" + k + ")").test(fmt))
					fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
			return fmt;
		}

function isSameMonth( inDate ){ // inDate 是一个date对象
   	var nowDate = new Date();//以昨天为基准
   	nowDate.setTime(nowDate.getTime()-24*60*60*1000);
    return ( ( nowDate.getFullYear() == inDate.getFullYear() ) &amp;&amp; 
             ( nowDate.getMonth() == inDate.getMonth() ) 
           );
}
function isSameWeek( inDate ){ // inDate 是一个date对象
   var inDateStr = inDate.toLocaleDateString();  // 获取如YYYY/MM/DD的日期
   var nowDate = new Date();//以昨天为基准
   nowDate.setTime(nowDate.getTime()-24*60*60*1000);
   var nowTime = nowDate.getTime();
   var nowDay = nowDate.getDay();
   for(var i=0;i&lt;7;i++){
      if(inDateStr == ( new Date(nowTime + (i-nowDay)*24*3600*1000) ).toLocaleDateString() ) return true;
   }
   return false;
}

var energyDayChangeRate = 0.0;
var energyWeek = 0;
var energyMonth = 0;
var energy7Day = 0;
var energy7DayChangeRate = 0.0;
var energy30Day = 0;
var energy30DayChangeRate = 0.0;

var energy714Day = 0;
var energy3060Day = 0;

var yesterdayTime=(new Date).getTime()-24*60*60*1000;
var yesterday=new Date(yesterdayTime);
var calcDate = dateFtt("yyyy-MM-dd", yesterday);
//var createTime = dateFtt("yyyy-MM-dd hh:mm:ss", new Date());
var createTime = new Date();



var sevenThirtyData = JSON.parse(getSevenThirty);
if(sevenThirtyData.length > 0) {
	energyDayChangeRate = (sevenThirtyData[0]._source.energyDay>0) ? (energyDay/sevenThirtyData[0]._source.energyDay) : 1;
}
for(var i = 0; i &lt; sevenThirtyData.length; i++) {
	var resEnergyDay = sevenThirtyData[i]._source.energyDay;
	var resCalcDate = sevenThirtyData[i]._source.calcDate;
	var resDate= new Date(Date.parse(resCalcDate.replace(/-/g,   "/")));
	if(i &lt; 7) {
		energy7Day += resEnergyDay;
	}
	if(i &lt; 30) {
		energy30Day += resEnergyDay;
	}

	if(i &lt; 14 &amp;&amp; i >= 7) {
		energy714Day += resEnergyDay;
	}
	if(i &lt; 60 &amp;&amp; i >= 30) {
		energy3060Day += resEnergyDay;
	}

	if(isSameWeek(resDate)) {
		energyWeek += resEnergyDay;
	}
	if(isSameMonth(resDate)) {
		energyMonth += resEnergyDay;
	}
}
energy7DayChangeRate = (energy714Day > 0) ? (energy7Day/energy714Day) : 1;
energy30DayChangeRate = (energy3060Day > 0) ? (energy30Day/energy3060Day) : 1; 




var esPostBody = JSON.parse("{}");
esPostBody["jobId"] = jobId;
esPostBody["calcRuleId"] = calcRuleId;
esPostBody["calcRuleName"] = calcRuleId;
esPostBody["calcCfgId"] = calcCfgId;
esPostBody["calcCfgName"] = calcCfgName;
esPostBody["dataSourceId"] = dataSourceId;
esPostBody["dataSource"] = dataSourceId;
esPostBody["energyType"] = energyType;
esPostBody["deviceCnt"] = 1;
esPostBody["energyDay"] = energyDay;

esPostBody["energyDayChangeRate"] = energyDayChangeRate;
esPostBody["energyWeek"] = energyWeek;
esPostBody["energyMonth"] = energyMonth;
esPostBody["energy7Day"] = energy7Day;
esPostBody["energy7DayChangeRate"] = energy7DayChangeRate;
esPostBody["energy30Day"] = energy30Day;
esPostBody["energy30DayChangeRate"] = energy30DayChangeRate;


esPostBody["calcDate"] = calcDate;
esPostBody["createTime"] = createTime;


var esPostBody2 = JSON.stringify(esPostBody);
var esPostBody3 = JSON.stringify(sevenThirtyData);
var esPostBody4 = JSON.stringify(energyDayChangeRate);

</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>esPostUrl</name>
        <rename>esPostUrl</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esPostBody</name>
        <rename>esPostBody</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esPostBody2</name>
        <rename>esPostBody2</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esPostBody3</name>
        <rename>esPostBody3</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>esPostBody4</name>
        <rename>esPostBody4</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>336</xloc>
      <yloc>384</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>统计当天</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compatible>N</compatible>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here



//writeToLog('e', deviceRec);
//var spaceRec = spaceDatas;

// 前一天的聚合数据
eval('var dataPreDay = ' + dataESAggrDeviceBuk);

// 当天的
eval('var dataCurrDay = ' + dataESAggrDeviceBukCurrDay);

// todo: 遍历累计，然后计算差值




var deviceCnt = 0;
var energyDay = 0;
var energyDayChangeRate = 0.0;

var enertyDDDD = "";
for(var i = 0; i &lt; dataPreDay.length; i++) {
	var devKey = dataPreDay[i].key;
	for(var j = 0;j &lt; dataCurrDay.length; j++) {
		var devCurKey =  dataCurrDay[j].key;

		if(devKey === devCurKey) {

			energyDay += (dataCurrDay[j].max_combined_active_energy.value - dataPreDay[i].max_combined_active_energy.value);
			}

	}

}</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>deviceCnt</name>
        <rename>deviceCnt</rename>
        <type>Integer</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>energyDay</name>
        <rename>energyDay</rename>
        <type>Number</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>energyDayChangeRate</name>
        <rename>energyDayChangeRate</rename>
        <type>Number</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>enertyDDDD</name>
        <rename>enertyDDDD</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>944</xloc>
      <yloc>384</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
    <error>
      <source_step>统计当天</source_step>
      <target_step/>
      <is_enabled>Y</is_enabled>
      <nr_valuename/>
      <descriptions_valuename/>
      <fields_valuename/>
      <codes_valuename/>
      <max_errors/>
      <max_pct_errors/>
      <min_pct_rows/>
    </error>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
