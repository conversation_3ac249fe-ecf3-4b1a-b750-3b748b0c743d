ALTER TABLE app_user_project ADD `real_sys_` int(2) DEFAULT '0' COMMENT '是否来源于实名制平台(1是0否)base表的弃用';
-- 生产刷数据
-- 以下几个项目非实名制，其他刷成实名制
-- 武昌白沙隔离点工程  3e8985d1c72696551d647b0f12a6fca4
-- 存爱园改造项目  4d364851494fc9003383300ca759780c
-- 幸福中路健康驿站一标段项目  85cd826b8289becc65fdd6309ba3cc66
-- 研发测试项目 70ce670f3e82e0a5f3df9764a0bc203a
UPDATE app_user_project
SET real_sys_ = 1
WHERE
	tenant_id_ NOT IN ( '3e8985d1c72696551d647b0f12a6fca4',
	'4d364851494fc9003383300ca759780c',
	 '85cd826b8289becc65fdd6309ba3cc66',
	 '70ce670f3e82e0a5f3df9764a0bc203a' );
CREATE INDEX app_user_clock_tenant_id__IDX USING BTREE ON app_user_clock (tenant_id_);
CREATE INDEX app_user_gate_tenant_id__IDX USING BTREE ON app_user_gate (tenant_id_);
CREATE INDEX app_user_gate_time__IDX USING BTREE ON app_user_gate (time_);
CREATE INDEX app_user_gate_company_project_id__IDX USING BTREE ON app_user_gate (company_project_id_);
CREATE INDEX app_group_company_project_id__IDX USING BTREE ON app_group (company_project_id_);
CREATE INDEX app_labor_company_project_tenant_id__IDX USING BTREE ON app_labor_company_project (tenant_id_);