ALTER TABLE app_hidden_danger ADD enterprise_check_type int(1) NULL COMMENT '检查类型，来源企业级检查单类型，1日常检查，2专项检查';
ALTER TABLE app_hidden_danger ADD enterprise_source_type int(1) NULL COMMENT '来源，空项目级，2企业级';
ALTER TABLE app_hidden_danger ADD enterprise_create_user_name varchar(50) NULL COMMENT '创建人名称，冗余企业级创建用户名';
ALTER TABLE app_hidden_danger ADD enterprise_organization_id BIGINT NULL COMMENT '企业级的组织id';
ALTER TABLE app_hidden_danger ADD enterprise_create_user_id BIGINT NULL COMMENT '企业级创建人id';
ALTER TABLE app_hidden_danger ADD enterprise_safety_check_id BIGINT NULL COMMENT '企业级安全检查单id';
ALTER TABLE app_hidden_danger ADD enterprise_create_user_phone varchar(20) NULL COMMENT '企业级创建人电话（冗余）';

