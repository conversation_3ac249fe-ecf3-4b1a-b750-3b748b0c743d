ALTER TABLE app_user_blacklist ADD enterprise_source_type_ int(1) DEFAULT '1'  COMMENT '来源，空或者1项目级，2企业级';
ALTER TABLE app_user_blacklist ADD enterprise_create_user_name_ varchar(50) NULL COMMENT '创建人名称，冗余企业级创建用户名';
ALTER TABLE app_user_blacklist ADD enterprise_create_user_id_ bigint(20) NULL COMMENT '企业级创建人id';
ALTER TABLE app_user_blacklist ADD enterprise_organization_id_ bigint(20) NULL COMMENT '企业级的组织id';

ALTER TABLE app_user_blacklist_record ADD enterprise_source_type_ int(1) DEFAULT '1'  COMMENT '来源，空或者1项目级，2企业级';
ALTER TABLE app_user_blacklist_record ADD enterprise_create_user_name_ varchar(50) NULL COMMENT '创建人名称，冗余企业级创建用户名';
ALTER TABLE app_user_blacklist_record ADD enterprise_create_user_id_ bigint(20) NULL COMMENT '企业级创建人id';
ALTER TABLE app_user_blacklist_record ADD enterprise_organization_id_ bigint(20) NULL COMMENT '企业级的组织id';
ALTER TABLE app_user_blacklist_record ADD src_tenant_id_ varchar(32) NULL COMMENT '添加时来源组织(来源项目级:tenant_id_)';
ALTER TABLE app_user_blacklist_record ADD src_organization_id_ bigint(20) NULL COMMENT '添加时来源组织(来源企业级:组织id)';