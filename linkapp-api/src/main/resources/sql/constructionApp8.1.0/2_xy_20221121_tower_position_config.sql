-- 塔吊位置打点配置
CREATE TABLE `app_tower_crane_position_image` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
  `url_` text COMMENT '图纸url',
  `config_` varchar(512) DEFAULT NULL COMMENT '图纸配置',
  `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `remark_` text COMMENT '备注',
  PRIMARY KEY (`id`)
) COMMENT='塔吊位置打点配置';

-- 塔吊在图纸上的位置
CREATE TABLE `app_tower_crane_position` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `machinery_id` int(11) DEFAULT NULL COMMENT '电子档案id',
  `device_code` varchar(32) DEFAULT NULL COMMENT '设备id',
  `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
  `position_x` float(6,2) NOT NULL COMMENT 'x',
  `position_y` float(6,2) NOT NULL COMMENT 'y',
  `circle_radius_` float(6,2) NOT NULL COMMENT '圆半径',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) COMMENT='塔吊在图纸上的位置';

-- 项目经纬度
alter table linkapp_tenant add column longitude varchar(32) DEFAULT NULL  COMMENT '经度',
add column latitude varchar(32) DEFAULT NULL  COMMENT '纬度';

-- 最近访问菜单日志表
CREATE TABLE `app_visit_privilege_log` (
  `id_` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name_` varchar(32) DEFAULT NULL COMMENT '菜单名称',
   `privilege_code_` varchar(255) DEFAULT NULL COMMENT '菜单code',
  `url_` varchar(255) DEFAULT NULL COMMENT '菜单前端地址',
  `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
  `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`id_`)
 ) COMMENT='访问菜单日志';

-- 塔吊防碰撞记录
CREATE TABLE `app_tower_crane_collision_alarm_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `machinery_id` int(11) DEFAULT NULL COMMENT '电子档案id',
  `device_code` varchar(32) DEFAULT NULL COMMENT '设备id',
  `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
  `height` double(20,2) DEFAULT NULL COMMENT '吊钩高度 单位m',
  `range`  double(20,2) DEFAULT NULL COMMENT '回转幅度 单位m',
  `rotation`  double(20,2) DEFAULT NULL COMMENT '回转 单位°',
  `crane_arm_height` double(20,2) DEFAULT NULL COMMENT '塔臂高 单位m',
  `crane_fore_arm_length` double(20,2) DEFAULT NULL COMMENT '前臂长 单位m',
  `crane_rear_arm_length` double(20,2) DEFAULT NULL COMMENT '后臂长 单位m',
  `driver_name1` varchar(256) DEFAULT NULL COMMENT '司机姓名',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `machinery_id_IDX` (`machinery_id`) USING BTREE,
  KEY `device_code_IDX` (`device_code`) USING BTREE,
  KEY `driver_name1_IDX` (`driver_name1`) USING BTREE
) COMMENT='塔吊防碰撞记录';

-- 塔吊循环记录 司机姓名
ALTER TABLE `app_tower_crane_work_record` ADD COLUMN `driver_name1` VARCHAR ( 256 ) DEFAULT NULL COMMENT '司机姓名';

-- 塔吊防碰撞记录 碰撞关联记录id
ALTER TABLE `app_tower_crane_collision_alarm_record` ADD COLUMN `ref_record_id` bigint(20) DEFAULT NULL COMMENT '碰撞关联记录id';

-- 群塔防碰撞配置 权限
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('8405', '84', '群塔防碰撞配置', 'towerCraneCollisionSetting', NULL, 3, NULL, NULL, 84.05, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);

alter table app_tower_crane_collision_alarm_record change column `range` `range_` double(20,2) DEFAULT NULL COMMENT '回转幅度 单位m';