-- ----------------------------
-- 劳务设置
-- ----------------------------
CREATE TABLE `app_lobar_set`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
	`on_type_`       int(2)        DEFAULT NULL COMMENT '在场判断 1人员定位 2实名制闸道',
    `all_type_`      int(2)        DEFAULT NULL COMMENT '展示范围 1项目全员 2分包人员',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime(0)   DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime(0)   DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='劳务设置';

-- 默认数据
INSERT INTO `app_lobar_set` ( `id`, `tenant_id_`, `on_type_`, `all_type_`, `creator_id_`, `create_time_`, `modify_id_`, `modify_time_`, `remark_` )
VALUES( 1, NULL, 2, 1, NULL, '2022-11-25 17:06:11', NULL, '2022-11-25 17:06:14', '1,2,3,4,6,10,11' );

-- ----------------------------
-- 人员定位相关
-- ----------------------------
ALTER TABLE app_user_record ADD `on_area_` int(1) DEFAULT NULL COMMENT '当前区域：1施工区，2办公区，3生活区,4项目外';
ALTER TABLE app_gate ADD `in_area_` int(1) DEFAULT NULL COMMENT '进入区域：1施工区，2办公区，3生活区,4项目外';
ALTER TABLE app_labor_region MODIFY `type_` INT (2) DEFAULT NULL COMMENT '类别(1施工区，2办公区，3生活区)';
ALTER TABLE app_user_blacklist_record ADD `show_` INT (2) DEFAULT '1' COMMENT '是否在花名册显示(1显示，2不显示，删除了黑名单记录就不显示)';