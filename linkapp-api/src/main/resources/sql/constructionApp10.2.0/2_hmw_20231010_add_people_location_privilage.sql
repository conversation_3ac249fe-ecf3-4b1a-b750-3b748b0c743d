-- ------------------------
-- 增加人员定位菜单权限-WEB
-- ------------------------
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('630', '60', '信标管理', 'beaconManagement', NULL, 2, NULL, NULL, 63.00, 1, 'manage/beaconManagement', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('640', '60', '安全帽管理', 'safeHatManagement', NULL, 2, NULL, NULL, 64.00, 1, 'manage/safeHatManagement', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('650', '60', '定位记录', 'locationRecord', NULL, 2, NULL, NULL, 65.00, 1, 'manage/locationRecord', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
-- ------------------------
-- 增加人员定位按钮权限-WEB
-- ------------------------
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6301', '630', '批量禁用', 'beaconManagement:setBatchDisable', NULL, 3, NULL, NULL, 6301.10, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6302', '630', '批量删除', 'beaconManagement:setBatchDel', NULL, 3, NULL, NULL, 6301.20, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6303', '630', '删除', 'beaconManagement:setDel', NULL, 3, NULL, NULL, 6301.30, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6304', '630', '基础信息编辑', 'beaconManagement:setBaseEdit', NULL, 3, NULL, NULL, 6301.40, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6305', '630', '平面图编辑', 'beaconManagement:setPicEdit', NULL, 3, NULL, NULL, 6301.50, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6401', '640', '批量绑定', 'safeHatManagement:setBatchBind', NULL, 3, NULL, NULL, 6401.10, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6402', '640', '批量解绑', 'safeHatManagement:setBatchDisbind', NULL, 3, NULL, NULL, 6401.20, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6403', '640', '同步安全帽', 'safeHatManagement:setSynchronous', NULL, 3, NULL, NULL, 6401.30, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6404', '640', '绑定人员', 'safeHatManagement:setBind', NULL, 3, NULL, NULL, 6401.40, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
-- ------------------------
-- 增加人员定位菜单权限-APP
-- ------------------------
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6600037', '660003', '人员定位', 'peopleLocation', '人员定位', 1, NULL, NULL, 660003.70, 1, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 1);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6600038', '660003', '设备管理', 'deviceControl', '设备管理', 1, NULL, NULL, 660003.80, 1, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 1);
-- ------------------------
-- 增加人员定位按钮权限-APP
-- ------------------------
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('66000381', '6600038', '添加信标', 'deviceControl:addBeacon', NULL, 3, NULL, NULL, 660003.81, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 1);
