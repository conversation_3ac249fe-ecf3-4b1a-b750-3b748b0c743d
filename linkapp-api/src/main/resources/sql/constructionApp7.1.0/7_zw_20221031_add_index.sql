CREATE INDEX app_quality_actual_measure_info_storage_type_IDX USING BTREE ON app_quality_actual_measure_info (storage_type);
CREATE INDEX app_quality_position_tenant_id__IDX USING BTREE ON app_quality_position (tenant_id_);
CREATE INDEX app_quality_position_type__IDX USING BTREE ON app_quality_position (type_);
CREATE INDEX jk_monitor_record_measurement_project_name_IDX USING BTREE ON jk_monitor_record (measurement_project_name);
CREATE INDEX jk_monitor_record_point_name_IDX USING BTREE ON jk_monitor_record (point_name);
CREATE INDEX jk_monitor_record_storage_time_IDX USING BTREE ON jk_monitor_record (storage_time);
