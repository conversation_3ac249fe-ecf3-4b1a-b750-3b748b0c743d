CREATE TABLE `app_jk_monitor_config`
(
    `id`                   int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id`            varchar(100) DEFAULT NULL COMMENT '租户id',
    `project_id`           varchar(100) DEFAULT NULL COMMENT '对接方项目id',
    `source_provider_name` varchar(100) DEFAULT NULL COMMENT '对接方名称',
    `item_monitor_url`     varchar(100) DEFAULT NULL COMMENT '监测项接口',
    `data_monitor_url`     varchar(100) DEFAULT NULL COMMENT '监测数据接口',
    `remark`               varchar(300) DEFAULT NULL COMMENT '基坑概况',
    `craft_video_url`      varchar(200) DEFAULT NULL COMMENT '工艺技法介绍视频url',
    `craft_video_name`     varchar(100) DEFAULT NULL COMMENT '工艺技法介绍视频名称',
    `site_video_device_id` varchar(100) DEFAULT NULL COMMENT '现场视频设备id',
    `jk_img`               varchar(200) DEFAULT NULL COMMENT '基坑平面布置图url',
    `creator`              varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`             varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`          datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`          datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state`         int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY                    `app_jk_monitor_config_tenant_id_IDX` (`tenant_id`) USING BTREE,
    KEY                    `app_jk_monitor_config_project_id_IDX` (`project_id`) USING BTREE
) ENGINE=InnoDB COMMENT='基坑监测配置表';

CREATE TABLE `app_jk_monitor_item_config`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`    varchar(100)  DEFAULT NULL COMMENT '租户id',
    `item_type`    int(1) DEFAULT NULL COMMENT '类型，1测项，2测点',
    `item_code`    varchar(100)  DEFAULT NULL COMMENT '测项点编码',
    `item_name`    varchar(200)  DEFAULT NULL COMMENT '测项点名称',
    `parent_id`    int(11) DEFAULT NULL COMMENT '父级id',
    `parent_ids`   varchar(4000) DEFAULT NULL COMMENT '父级ids',
    `x_axis`       double        DEFAULT NULL COMMENT 'x坐标值',
    `y_axis`       double        DEFAULT NULL COMMENT 'y轴坐标值',
    `creator`      varchar(100)  DEFAULT NULL COMMENT '创建人',
    `modifier`     varchar(100)  DEFAULT NULL COMMENT '修改人',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `app_jk_monitor_item_config_tenant_id_IDX` (`tenant_id`) USING BTREE,
    KEY            `app_jk_monitor_item_config_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB COMMENT='基坑监测测项测点配置';