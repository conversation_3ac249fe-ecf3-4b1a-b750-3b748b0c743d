-- 预警中心-预警设置权限
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('6081', '608', '预警设置', 'warningCenter:warningConfig', NULL, 3, NULL, NULL, 608.1, 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 预警通知人
CREATE TABLE `app_warning_notifier` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `tenant_id_` varchar(50) NOT NULL COMMENT '项目ID(linkapp_tenant)',
	`warning_type` int(1) NOT NULL COMMENT '预警类型 1出勤预警 2班组预警 3年龄预警 4证书到期预警 5人员出场异常预警 6疫情预警',
  `warning_notifiers` text NOT NULL COMMENT '预警通知人，userIds',
  `notice_switch` int(1) NOT NULL COMMENT '推送开关，0停用，1启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警通知人表';
-- 大屏预警设置
ALTER TABLE `linkapp_dashboard`
    ADD COLUMN `warning_setting` text DEFAULT NULL COMMENT '预警设置' AFTER `background_url`;
-- 大屏配置权限

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('8403', '84', '智慧党建', 'partyBuildingSet', NULL, 3, NULL, NULL, 84.03, 2, null, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('8404', '84', '预警设置', 'warningSetting', NULL, 3, NULL, NULL, 84.04, 2, null, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);

-- 用户管理权限

INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('817', '81', '批量导入用户', 'account:importExcel', '批量导入用户', 3, NULL, NULL, 15.17, 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
