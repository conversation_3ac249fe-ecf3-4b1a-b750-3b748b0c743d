-- 检测标准表-混凝土强度标准
CREATE TABLE `app_check_standard`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`     varchar(100)  DEFAULT NULL COMMENT '租户id，冗余',
    `parent_id`     int(11) DEFAULT NULL COMMENT '父级id',
    `parent_ids`    varchar(4000) DEFAULT NULL COMMENT '父级ids，以，隔开',
    `standard_name` varchar(100)  DEFAULT NULL COMMENT '标准名称',
    `standard_val`  varchar(100)  DEFAULT NULL COMMENT '标准值',
    `standard_code` varchar(100)  DEFAULT NULL COMMENT '标准编码',
    `standard_unit` varchar(100)  DEFAULT NULL COMMENT '标准单位',
    `type`          int(2) DEFAULT NULL COMMENT '分类，1混凝土强度等级',
    `use_status`    int(1) DEFAULT NULL COMMENT '使用状态，0停用，1使用',
    `delete_state`  int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY             `app_check_standard_type_IDX` (`type`) USING BTREE,
    KEY             `app_check_standard_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB COMMENT='检测标准表';
-- 混凝土强度标准表初始信息


-- 混凝土强度监测相关表
CREATE TABLE `app_concrete_strength_info`
(
    `id`                        bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`                 varchar(100) DEFAULT NULL COMMENT '租户id',
    `status`                    int(1) DEFAULT NULL COMMENT '状态，1合格，2待复查',
    `position_id`               int(11) DEFAULT NULL COMMENT '部位id',
    `check_device_code`         varchar(100) DEFAULT NULL COMMENT '检测设备编码',
    `member_code`               varchar(100) DEFAULT NULL COMMENT '构建编码',
    `member_type`               varchar(100) DEFAULT NULL COMMENT '构建类型',
    `member_name`               varchar(100) DEFAULT NULL COMMENT '构建类型名称',
    `age`                       varchar(100) DEFAULT NULL COMMENT '期龄',
    `check_area_num`            int(11) DEFAULT NULL COMMENT '测区数量',
    `water_time`                datetime     DEFAULT NULL COMMENT '浇筑日期',
    `check_time`                datetime     DEFAULT NULL COMMENT '检测日期',
    `design_strength_id`        int(11) DEFAULT NULL COMMENT '设计强度等级主键，标准表',
    `design_strength_code`      varchar(20)  DEFAULT NULL COMMENT '设计强度编码',
    `strength_presumption_val`  varchar(100) DEFAULT NULL COMMENT '强度推定值',
    `strength_presumption_unit` varchar(100) DEFAULT NULL COMMENT '强度推定单位',
    `creator`                   varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`                  varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`               datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`               datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state`              int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY                         `app_concrete_strength_info_tenant_id_IDX` (`tenant_id`) USING BTREE,
    KEY                         `app_concrete_strength_info_position_id_IDX` (`position_id`) USING BTREE,
    KEY                         `app_concrete_strength_info_design_strength_id_IDX` (`design_strength_id`) USING BTREE,
    KEY                         `app_concrete_strength_info_status_IDX` (`status`) USING BTREE,
    KEY                         `app_concrete_strength_info_check_time_IDX` (`check_time`) USING BTREE
) ENGINE=InnoDB COMMENT='混凝土强度检测';

CREATE TABLE `app_concrete_strength_detail`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `info_id`       bigint(20) DEFAULT NULL COMMENT '混凝土强度检测主表id',
    `check_face`    varchar(100) DEFAULT NULL COMMENT '测面',
    `check_angle`   varchar(100) DEFAULT NULL COMMENT '测试角度',
    `standard_diff` varchar(100) DEFAULT NULL COMMENT '强度标准差',
    `data_type`     int(1) DEFAULT NULL COMMENT '数据类型，1回弹值，2碳化深度',
    `check_area`    varchar(100) DEFAULT NULL COMMENT '测区',
    `creator`       varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`      varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`   datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`   datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state`  int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY             `app_concrete_strength_detail_info_id_IDX` (`info_id`) USING BTREE
) ENGINE=InnoDB COMMENT='混凝土强度检测详情';

CREATE TABLE `app_concrete_strength_record`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `info_id`      bigint(20) DEFAULT NULL COMMENT '主记录主键，冗余',
    `detail_id`    bigint(20) DEFAULT NULL COMMENT '详情主键',
    `check_no`     varchar(100) DEFAULT NULL COMMENT '测试号',
    `check_val`    varchar(100) DEFAULT NULL COMMENT '测试值',
    `check_unit`   varchar(100) DEFAULT NULL COMMENT '测试单位',
    `creator`      varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`     varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`  datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `app_concrete_strength_record_detail_id_IDX` (`detail_id`) USING BTREE,
    KEY            `app_concrete_strength_record_info_id_IDX` (`info_id`) USING BTREE
) ENGINE=InnoDB COMMENT='混凝土强度检测值记录';