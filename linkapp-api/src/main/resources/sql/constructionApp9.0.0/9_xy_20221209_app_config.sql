
-- 升降机、塔吊告警配置
INSERT INTO app_config
(key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time)
VALUES ('RULE_ALARM_MACHINERY_TYPE_QUIP_TSQZJ_01', NULL,
        '塔吊-高度限位预警,塔吊-重量预警,塔吊-力矩预警,塔吊-幅度限位预警,塔吊-碰撞预警,塔吊-风速预警,塔吊-回转限位预警',
        '塔吊-高度限位预警,塔吊-重量预警,塔吊-力矩预警,塔吊-幅度限位预警,塔吊-碰撞预警,塔吊-风速预警,塔吊-回转限位预警',
        '机械管理:塔吊规则报警','塔吊告警类型', now(), now());
INSERT INTO app_config
(key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time)
VALUES ('RULE_ALARM_MACHINERY_TYPE_QUIP_SJJ_02', NULL,
        '升降机-重量预警,升降机-速度预警,升降机-倾斜预警,升降机-高度预警,升降机-门锁预警',
        '升降机-重量预警,升降机-速度预警,升降机-倾斜预警,升降机-高度预警,升降机-门锁预警',
        '机械管理:升降机规则报警', '升降机告警类型', now(), now());

-- 修改默认配置为tenantId = null
update app_config set tenant_id = null where tenant_id = ''

-- 人员相片字段说明  kyf 2022-12-13
ALTER TABLE app_user_project
    MODIFY COLUMN `photo_` varchar(255) DEFAULT NULL COMMENT '相片url(弃用)';

-- 人员定位字段说明  kyf 2022-12-14
ALTER TABLE app_user_record
    MODIFY COLUMN `on_area_` int(1) DEFAULT NULL COMMENT '当前区域：1施工区，2办公区，3生活区,4项目外(弃用)';
-- 花名册添加人员定位字段说明  kyf 2022-12-14
ALTER TABLE app_user_project
    ADD COLUMN `on_area_` int(1) DEFAULT NULL COMMENT '当前区域：1施工区，2办公区，3生活区,4项目外';

INSERT INTO `app_machinery_parameter` (`type_code_`, `label_`, `key_`, `value_`, `unit_`, `creator_id_`, `create_time_`, `modify_id_`, `modify_time_`, `remark_`)
VALUES
	('QUIP_SJJ_02', '额定载重', 'ratedLoad', NULL, 'kg', NULL, NULL, NULL, NULL, NULL),
	('QUIP_SJJ_02', '额定人数', 'ratedPerson', NULL, '人', NULL, NULL, NULL, NULL, NULL),
	('QUIP_TSQZJ_01', '塔帽高度', 'craneCapHeight', NULL, 'm', NULL, NULL, NULL, NULL, NULL),
	('QUIP_TSQZJ_01', '塔机前臂长', 'craneForeArmLength', NULL, 'm', NULL, NULL, NULL, NULL, NULL),
	('QUIP_TSQZJ_01', '塔机额定重量', 'craneRatedLoad', NULL, 't', NULL, NULL, NULL, NULL, NULL),
	('QUIP_TSQZJ_01', '塔机额定力矩', 'craneRatedTorque', NULL, 't·m', NULL, NULL, NULL, NULL, NULL),
	('QUIP_TSQZJ_01', '塔机后臂长', 'craneRearArmLength', NULL, 'm', NULL, NULL, NULL, NULL, NULL);

CREATE TABLE `app_machinery_num_statistics` (
	`id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键',
	`type_code_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '类型code',
	`machinery_num_` INT ( 11 )  NOT NULL DEFAULT '0' COMMENT '机械数量',
	`record_day` VARCHAR ( 32 ) NOT NULL COMMENT '某天的统计 形式yyyyMMdd',
	`create_time_` datetime DEFAULT NULL COMMENT '创建日期',
PRIMARY KEY ( `id` ) USING BTREE
) COMMENT = '机械监测设备数量统计 按天';