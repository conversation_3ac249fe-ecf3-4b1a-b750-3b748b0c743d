-- 质量问题库类型
ALTER TABLE linkappdb.app_problem_type
    MODIFY COLUMN tenant_id_ varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '租户id，租户为空-企业端模板数据';

-- 质量问题库
ALTER TABLE linkappdb.app_problem
    MODIFY COLUMN tenant_id_ varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '租户id，租户为空-企业端模板数据';

-- select * from linkapp_privilege where name_ = '问题库';
-- 权限

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_,
                               seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_,
                               icon_name, flag_)
VALUES ('11018', '1101', '同步企业问题库', 'safetyTrousers:sync', NULL, 3, NULL, NULL, 11.18, 2, NULL, NULL, '1',
        now(), NULL, NULL, NULL, NULL, 0);


alter table app_problem_type
    add column `last_push_time` datetime DEFAULT NULL COMMENT '最后同步时间';

alter table app_problem
    add column `last_push_time` datetime DEFAULT NULL COMMENT '最后同步时间';