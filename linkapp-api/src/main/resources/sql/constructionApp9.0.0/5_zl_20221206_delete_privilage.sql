-- 1. 设备地图
select * from linkapp_privilege lp where lp.name_ = ''设备地图'';  -- 11
select * from linkapp_privilege lp where lp.parent_id_ = 11;


-- 2. 设备监控
select * from linkapp_privilege lp where lp.name_ = ''设备监控''; -- 12
select * from linkapp_privilege lp where lp.parent_id_ in(12);


-- 3. 电力监控
select * from linkapp_privilege lp where lp.name_ = ''电力监控'';  -- 9
select * from linkapp_privilege lp where lp.parent_id_ in(9); -- 91,92,93
select * from linkapp_privilege lp where lp.parent_id_ in(91,92,93);  -- 921,922,923,924,931,932,933,934
select * from linkapp_privilege lp where lp.parent_id_ in(921,922,923,924,931,932,933,934);

-- 4. 暖通空调
select * from linkapp_privilege lp where lp.name_ = ''暖通空调'';  -- 30
select * from linkapp_privilege lp where lp.parent_id_ in(30); -- 301,302,303,304
select * from linkapp_privilege lp where lp.parent_id_ in(301,302,303,304);

-- 5. 智能监测
select * from linkapp_privilege lp where lp.name_ = ''智能监测'';  -- 29
select * from linkapp_privilege lp where lp.parent_id_ in(29); -- 291,293
select * from linkapp_privilege lp where lp.parent_id_ in(291,293);


-- 6.贷后风控
select * from linkapp_privilege lp where lp.name_ = ''贷后风控'';  -- 37
select * from linkapp_privilege lp where lp.parent_id_ in(37); -- 371,372
select * from linkapp_privilege lp where lp.parent_id_ in(371,372);


-- 7. 资产管理
select * from linkapp_privilege lp where lp.name_ = ''资产管理'';  -- 3
select * from linkapp_privilege lp where lp.parent_id_ in(3); -- 31,32,33
select * from linkapp_privilege lp where lp.parent_id_ in(31,32,33);

-- 8. 人脸识别
select * from linkapp_privilege lp where lp.name_ = ''人脸识别'';  -- 18
select * from linkapp_privilege lp where lp.parent_id_ in(18); -- 181,182,183
select * from linkapp_privilege lp where lp.parent_id_ in(181,182,183);


-- 9. 整改通知单
select * from linkapp_privilege lp where lp.name_ = ''整改通知单'';  -- 1002
select * from linkapp_privilege lp where lp.parent_id_ in(1002);

-- 10. 能耗管理,计算配置,能源管理
select * from linkapp_privilege lp where lp.name_ = ''能耗管理'';  -- 4
select * from linkapp_privilege lp where lp.parent_id_ in(4); -- 41,42,43
select * from linkapp_privilege lp where lp.parent_id_ in(41,42,43); -- 411,412,413,414,421,422,423,424
select * from linkapp_privilege lp where lp.parent_id_ in(411,412,413,414,421,422,423,424);

-- 13. 巡检管理
select * from linkapp_privilege lp where lp.name_ = ''巡检管理'';  -- 20
select * from linkapp_privilege lp where lp.parent_id_ in(20); -- 201,202,203
select * from linkapp_privilege lp where lp.parent_id_ in(201,202,203); -- 2011,2012,2013,2021,2022,2023,2024,2031,2032,2033,2034
select * from linkapp_privilege lp where lp.parent_id_ in(2011,2012,2013,2021,2022,2023,2024,2031,2032,2033,2034);

-- 14. 定位监控
select * from linkapp_privilege lp where lp.name_ = ''定位监控'';  -- 39
select * from linkapp_privilege lp where lp.parent_id_ in(39); -- 391,392,393,394
select * from linkapp_privilege lp where lp.parent_id_ in(391,392,393,394);

-- 15. 水电抄表
select * from linkapp_privilege lp where lp.name_ = ''水电抄表'';  -- 40
select * from linkapp_privilege lp where lp.parent_id_ in(40); -- 402,403,406,407,408
select * from linkapp_privilege lp where lp.parent_id_ in(402,403,406,407,408);  -- 401,404,405
select * from linkapp_privilege lp where lp.parent_id_ in(401,404,405);


-- 16.车辆管理
select * from linkapp_privilege lp where lp.name_ = ''车辆管理'';  -- 50
select * from linkapp_privilege lp where lp.parent_id_ in(50); -- 501,502
select * from linkapp_privilege lp where lp.parent_id_ in(501,502);

-- 17。数据分析
select * from linkapp_privilege lp where lp.name_ = ''数据分析'';  -- 70
select * from linkapp_privilege lp where lp.parent_id_ in(70);

-- 18 大屏配置
select * from linkapp_privilege lp where lp.name_ = ''大屏配置'';
select * from linkapp_privilege lp where lp.parent_id_ in(5);

CREATE TABLE linkapp_tenant_ref_privilege_2022_12_06 select * FROM linkapp_tenant_ref_privilege;
delete from linkapp_tenant_ref_privilege where privilege_id
in (11,12,9,91,92,93,921,922,923,924,931,932,933,934,30,301,302,303,304,29,291,293,37,371,372,3,31,32,33,18,181,182,183,1002,
4,41,42,43,411,412,413,414,421,422,423,424,20,201,202,203,2011,2012,2013,2021,2022,2023,2024,2031,2032,2033,2034,39,391,392,393,394,40,402,403,406,407,408,401,404,405,50,501,502,70,5);

CREATE TABLE linkapp_function_ref_privilege_2022_12_06 select * FROM linkapp_function_ref_privilege;
delete from linkapp_function_ref_privilege where privilege_id
in (11,12,9,91,92,93,921,922,923,924,931,932,933,934,30,301,302,303,304,29,291,293,37,371,372,3,31,32,33,18,181,182,183,1002,
4,41,42,43,411,412,413,414,421,422,423,424,20,201,202,203,2011,2012,2013,2021,2022,2023,2024,2031,2032,2033,2034,39,391,392,393,394,40,402,403,406,407,408,401,404,405,50,501,502,70,5);


CREATE TABLE linkapp_role_ref_privilege_2022_12_06 select * FROM linkapp_role_ref_privilege;
delete from linkapp_role_ref_privilege where privilege_id_
in (11,12,9,91,92,93,921,922,923,924,931,932,933,934,30,301,302,303,304,29,291,293,37,371,372,3,31,32,33,18,181,182,183,1002,
4,41,42,43,411,412,413,414,421,422,423,424,20,201,202,203,2011,2012,2013,2021,2022,2023,2024,2031,2032,2033,2034,39,391,392,393,394,40,402,403,406,407,408,401,404,405,50,501,502,70,5);

CREATE TABLE linkapp_privilege_2022_12_06 select * FROM linkapp_privilege;
delete from linkapp_privilege where id_
in (11,12,9,91,92,93,921,922,923,924,931,932,933,934,30,301,302,303,304,29,291,293,37,371,372,3,31,32,33,18,181,182,183,1002,
4,41,42,43,411,412,413,414,421,422,423,424,20,201,202,203,2011,2012,2013,2021,2022,2023,2024,2031,2032,2033,2034,39,391,392,393,394,40,402,403,406,407,408,401,404,405,50,501,502,70,5);