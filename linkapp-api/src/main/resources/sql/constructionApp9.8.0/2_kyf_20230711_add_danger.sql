-- ----------------------------
-- 新增菜单
-- ----------------------------
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('1010', '1000', '通知设置', 'securityNotificationConfig', NULL, 2, NULL, NULL, 10.20, 1, 'manage/securityNotificationConfig', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('1109', '110', '通知设置', 'qualityNotificationConfig', NULL, 2, NULL, NULL, 11.09, 1, 'manage/qualityNotificationConfig', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
-- ----------------------------
-- 安全通知设置
-- ----------------------------
CREATE TABLE `app_hidden_config`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `rectify_`       tinyint(1)    DEFAULT '1'  COMMENT '整改通知(1开0关)',
    `check_`         tinyint(1)    DEFAULT '1'  COMMENT '整改复查通知(1开0关)',
    `check_end_`     tinyint(1)    DEFAULT '1'  COMMENT '复查结果通知(1开0关)',
    `task_expire_`   tinyint(1)    DEFAULT NULL COMMENT '任务即将超期通知(1开0关)',
    `expire_param_`  int(2)        DEFAULT NULL COMMENT '任务超期时间(小时)',
    `task_over_`     tinyint(1)    DEFAULT NULL COMMENT '任务已超期通知(1开0关)',
    `over_param_`    int(2)        DEFAULT NULL COMMENT '超期通知天数(天)',
    `task_upper_`    tinyint(1)    DEFAULT NULL COMMENT '任务超期上报通知(1开0关)',
    `upper_param_`   varchar(1024)  DEFAULT NULL COMMENT '任务超期上报通知人员id(逗号分隔)',
    `upper_time_`    datetime      DEFAULT NULL COMMENT '任务超期上报生效时间',
    `creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='安全通知设置';

-- ----------------------------
-- 质量通知设置
-- ----------------------------
CREATE TABLE `app_quality_config`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `rectify_`       tinyint(1)    DEFAULT '1'  COMMENT '整改通知(1开0关)',
    `check_`         tinyint(1)    DEFAULT '1'  COMMENT '复核通知(1开0关)',
    `check_end_`     tinyint(1)    DEFAULT '1'  COMMENT '复核结果通知(1开0关)',
    `rectify_expire_`   tinyint(1)    DEFAULT NULL COMMENT '整改即将超期通知(1开0关)',
    `rectify_expire_param_`  int(2)        DEFAULT NULL COMMENT '整改超期时间(小时)',
    `rectify_over_`     tinyint(1)    DEFAULT NULL COMMENT '整改已超期通知(1开0关)',
    `rectify_over_param_`    int(2)        DEFAULT NULL COMMENT '整改超期通知天数(天)',
    `rectify_upper_`    tinyint(1)    DEFAULT NULL COMMENT '整改超期上报通知(1开0关)',
    `rectify_upper_param_`   varchar(1024)  DEFAULT NULL COMMENT '整改超期上报通知人员id(逗号分隔)',
    `rectify_upper_time_`    datetime      DEFAULT NULL COMMENT '整改超期上报生效时间',
    `check_expire_`   tinyint(1)    DEFAULT NULL COMMENT '复核即将超期通知(1开0关)',
    `check_expire_param_`  int(2)        DEFAULT NULL COMMENT '复核超期时间(小时)',
    `check_over_`     tinyint(1)    DEFAULT NULL COMMENT '复核已超期通知(1开0关)',
    `check_over_param_`    int(2)        DEFAULT NULL COMMENT '复核超期通知天数(天)',
    `check_upper_`    tinyint(1)    DEFAULT NULL COMMENT '复核超期上报通知(1开0关)',
    `check_upper_param_`   varchar(1024)   DEFAULT NULL COMMENT '复核超期上报通知人员id(逗号分隔)',
    `check_upper_time_`    datetime      DEFAULT NULL COMMENT '整改超期上报生效时间',
    `creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='质量通知设置';

-- ----------------------------
-- 安全管理查阅记录
-- ----------------------------
CREATE TABLE `app_hidden_action_records`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `link_id_`       varchar(32)   DEFAULT NULL COMMENT '安全隐患关联id',
    `action_type_`   int(2)        DEFAULT NULL COMMENT '操作类型(1发起2查阅3整改4复查)',
    `creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='安全管理查阅记录';
-- ----------------------------
-- 质量管理查阅记录
-- ----------------------------
CREATE TABLE `app_quality_action_records`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `link_id_`       bigint(20)    DEFAULT NULL COMMENT '质量问题关联id',
    `action_type_`   int(2)        DEFAULT NULL COMMENT '操作类型(1发起2查阅3整改4复核)',
    `creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='质量管理查阅记录';

