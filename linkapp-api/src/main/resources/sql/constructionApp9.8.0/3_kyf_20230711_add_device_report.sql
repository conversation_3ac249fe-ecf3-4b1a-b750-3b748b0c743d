-- ----------------------------
-- 设备运行报告
-- ----------------------------
CREATE TABLE `app_report`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `name_`          varchar(32)   DEFAULT NULL COMMENT '报告名称',
    `start_time_`    datetime      DEFAULT NULL COMMENT '报告起始时间',
    `end_time_`      datetime      DEFAULT NULL COMMENT '报告结束时间',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='设备运行报告';

-- ----------------------------
-- 设备运行报告设备统计
-- ----------------------------
CREATE TABLE `app_report_device`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `report_id_`     int(11)       DEFAULT NULL COMMENT '报告id',
    `device_type_id_`  varchar(32)   DEFAULT NULL COMMENT '设备类型id',
    `num_`           int(11)       DEFAULT NULL COMMENT '设备数量',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='设备运行报告设备统计';

-- ----------------------------
-- 设备运行报告告警统计
-- ----------------------------
CREATE TABLE `app_report_alarm`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `report_id_`     int(11)       DEFAULT NULL COMMENT '报告id',
    `type_`          int(11)       DEFAULT NULL COMMENT '告警类型(枚举)',
    `num_`           int(11)       DEFAULT NULL COMMENT '告警次数',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='设备运行报告告警统计';

-- ----------------------------
-- 设备运行报告用水/电量统计
-- ----------------------------
CREATE TABLE `app_report_energy`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `report_id_`     int(11)       DEFAULT NULL COMMENT '报告id',
    `device_code`    varchar(32)   DEFAULT NULL COMMENT '设备code(为null时为汇总数据)',
    `day_`           int(11)       DEFAULT NULL COMMENT '检测天数',
    `area_name_`     varchar(50)   DEFAULT NULL COMMENT '区域名称',
    `num_`           double(20,2)  DEFAULT NULL COMMENT '用水/电量',
    `type_`          int(11)       DEFAULT NULL COMMENT '类型(1用水2用电)',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='设备运行报告用水/电量统计';

-- ----------------------------
-- 设备运行报告喷淋控制统计
-- ----------------------------
CREATE TABLE `app_report_spray`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `report_id_`     int(11)       DEFAULT NULL COMMENT '报告id',
    `device_code`    varchar(32)   DEFAULT NULL COMMENT '设备code(为null时为汇总数据)',
    `day_`           int(11)       DEFAULT NULL COMMENT '喷淋天数',
    `num_`           int(11)       DEFAULT NULL COMMENT '喷淋次数',
    `hours_`         double(20,2)  DEFAULT NULL COMMENT '喷淋时长',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='设备运行报告喷淋控制统计';

-- ----------------------------
-- 设备运行报告ai预警统计
-- ----------------------------
CREATE TABLE `app_report_ai`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `report_id_`     int(11)       DEFAULT NULL COMMENT '报告id',
    `device_code`    varchar(32)   DEFAULT NULL COMMENT '设备code(为null时为汇总数据)',
    `day_`           int(11)       DEFAULT NULL COMMENT '监测天数',
    `num_`           int(11)       DEFAULT NULL COMMENT '告警次数',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='设备运行报告ai预警统计';

-- ----------------------------
-- 设备运行报告ai预警统计详情
-- ----------------------------
CREATE TABLE `app_report_ai_detail`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `report_id_`     int(11)       DEFAULT NULL COMMENT '报告id',
    `link_id_`       int(11)       DEFAULT NULL COMMENT '关联ai预警统计id',
    `event_`         varchar(32)   DEFAULT NULL COMMENT '事件名称',
    `num_`           int(11)       DEFAULT NULL COMMENT '告警次数',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='设备运行报告ai预警统计详情';

-- ----------------------------
-- 设备运行报告电气火灾统计
-- ----------------------------
CREATE TABLE `app_report_electrical`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `report_id_`     int(11)       DEFAULT NULL COMMENT '报告id',
    `device_code`    varchar(32)   DEFAULT NULL COMMENT '设备code(为null时为汇总数据)',
    `day_`           int(11)       DEFAULT NULL COMMENT '监测天数',
    `num_`           int(11)       DEFAULT NULL COMMENT '告警次数',
    `top_event_`     varchar(200)   DEFAULT NULL COMMENT '告警类型top',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='设备运行报告电气火灾统计';

-- ----------------------------
-- 设备运行报告塔吊/升降机统计
-- ----------------------------
CREATE TABLE `app_report_machinery`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `report_id_`     int(11)       DEFAULT NULL COMMENT '报告id',
    `device_code`    varchar(32)   DEFAULT NULL COMMENT '设备code(为null时为汇总数据)',
    `num_`           int(11)       DEFAULT NULL COMMENT '运行次数',
    `weight_`        double(20,2)  DEFAULT NULL COMMENT '吊装重量',
    `day_`           int(11)       DEFAULT NULL COMMENT '监测天数',
    `hours_`         double(20,2)  DEFAULT NULL COMMENT '工作时长',
    `efficiency_`    double(20,2)  DEFAULT NULL COMMENT '本月工效',
    `alarm_num_`     int(11)       DEFAULT NULL COMMENT '告警次数',
    `top_event_`     varchar(200)   DEFAULT NULL COMMENT '告警类型top',
    `type_`          int(11)       DEFAULT NULL COMMENT '类型(1塔吊2升降机)',
    `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='设备运行报告塔吊/升降机统计';
