-- ------------------------
-- 增加用水用电结果表中区域id字段
-- ------------------------
ALTER TABLE `app_water_records` ADD COLUMN `area_id` int(11) COMMENT '区域id，取自app_environmental_area表' AFTER `tenant_id`;
ALTER TABLE `app_electricy_records` ADD COLUMN `area_id` int(11) COMMENT '区域id，取自app_environmental_area表' AFTER `tenant_id`;
-- -------------------
-- 增加报表中心菜单
-- -------------------
INSERT INTO `linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('130', NULL, '报表中心', 'reportCenter', NULL, 1, NULL, NULL, 5.02, 1, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('13001', '130', '用水报表', 'waterConsumptionCeport', NULL, 2, NULL, NULL, 13001, 1, 'manage/waterConsumptionCeport', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('13002', '130', '用电报表', 'electricityConsumptionReport', NULL, 2, NULL, NULL, 13002, 1, 'manage/electricityConsumptionReport', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('13003', '130', '安全检查报表', 'hiddenDangerReport', NULL, 2, NULL, NULL, 13003, 1, 'manage/hiddenDangerReport', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('13004', '130', '机械运行报表', 'mechanicalOperationReport', NULL, 2, NULL, NULL, 13004, 1, 'manage/mechanicalOperationReport', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('13005', '130', '设备运行报告', 'equipmentOperationReport', NULL, 2, NULL, NULL, 13005, 1, 'manage/equipmentOperationReport', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);

-- ------------------------
-- app权限
-- ------------------------
INSERT INTO `linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6600025', '660002', '设备预警', 'appAlarmLog', '设备预警', 1, NULL, NULL, 660002.50, 1, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 1);


-- 新增项目表项目经理跟联系方式
ALTER TABLE `linkapp_tenant`
    ADD COLUMN `project_manager_` varchar(255) COMMENT '项目经理' AFTER `latitude`,
ADD COLUMN `project_manager_phone_` varchar(255) COMMENT '项目经理联系方式' AFTER `project_manager_`;

