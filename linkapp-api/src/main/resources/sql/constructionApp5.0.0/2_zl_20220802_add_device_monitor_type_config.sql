-- ----------------------------
-- 电气火灾监控设备的配置
-- ----------------------------
INSERT INTO app_config (key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time) VALUES('DQHZ_DEVICE_TYPE_NAMES', '405cd2c8633ecbee1c5f61c4a87c9019', '电气火灾监测器', '电气火灾监测器', '安全管理:配电箱管理', '配电箱管理中的配电监测设备类别', '2022-08-02 15:25:05', '2022-08-02 15:25:05');

-- ----------------------------
-- 配电箱电气火灾的设备字段
-- ----------------------------
ALTER TABLE app_electric_box ADD monitor_device_code_ varchar(100) NULL COMMENT '监控电气火灾设备' AFTER qr_code;

-- ----------------------------
-- 新增打点是否移除字段
-- ----------------------------
ALTER TABLE app_device_position ADD is_remove_ TINYINT(4) DEFAULT 0 NOT NULL COMMENT '是否从模块中移除 0未移除 1已移除';

-- ----------------------------
-- 新增打点表模块id字段
-- ----------------------------
ALTER TABLE app_device_position ADD model_id_ TINYINT(4) NOT NULL COMMENT '模块id，0为环境模块,-1为配电箱' AFTER tenant_id;

