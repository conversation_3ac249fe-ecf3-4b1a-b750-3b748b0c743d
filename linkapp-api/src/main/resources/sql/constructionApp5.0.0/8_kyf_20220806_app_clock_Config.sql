-- ----------------------------
-- 考勤规则设置菜单
-- ----------------------------
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(609,60, '考勤规则设置', 'clockConfig', NULL, 2, NULL, NULL, 60.9, 1, 'manage/clockConfig', NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);
-- ----------------------------
-- 考勤规则
-- ----------------------------
CREATE TABLE `app_clock_config`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id(为null时为默认数据)',
    `name_`          varchar(128)  DEFAULT NULL COMMENT '名称',
	`hour_`          double        DEFAULT NULL COMMENT '工日工时比例',
	`count_type_`    int(2)        DEFAULT NULL COMMENT '考勤计算方式(1未减初 2时间累加)',
	`clock_type_`    int(2)        DEFAULT NULL COMMENT '打卡方法(1一天一卡 2一天两卡)',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime(0)   DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime(0)   DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='考勤规则';
-- ----------------------------
-- 默认数据
-- ----------------------------
INSERT INTO app_clock_config(`id`, `tenant_id_`, `name_`, `hour_`, `count_type_`, `clock_type_`, `creator_id_`, `create_time_`, `modify_id_`, `modify_time_`, `remark_`) VALUES (1, NULL, '默认考勤计划', 8, 1, 2, NULL, NULL, NULL, NULL, NULL);

