-- ----------------------------
-- 用水&用电web菜单
-- ----------------------------
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('2039', '2035', '用电历史数据', 'environmentalElectricHistory', NULL, 2, NULL, NULL, 3.51, 1, 'manage/environmentalElectricHistory', NULL, '1', sysdate(), NULL, NULL, NULL, NULL, 0);


INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('2040', '2035', '用水历史数据', 'environmentalWaterHistory', NULL, 2, NULL, NULL, 3.60, 1, 'manage/environmentalWaterHistory', NULL, '1', sysdate(), NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- 用水&用电app菜单
-- ----------------------------
INSERT INTO linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
('6600013', '660001', '用电管理', 'electricyManage', '用电管理', 1, NULL, NULL, 660001.3, 1, null, NULL, '1', now(), NULL, NULL, NULL, NULL, 1);

INSERT INTO linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
('6600014', '660001', '用水管理', 'waterManage', '用水管理', 1, NULL, NULL, 660001.4, 1, null, NULL, '1', now(), NULL, NULL, NULL, NULL, 1);

-- ----------------------------
-- 用电记录表
-- ----------------------------
CREATE TABLE `app_electricy_records` (
	`id` VARCHAR ( 32 ) NOT NULL COMMENT 'ID 主键',
	`device_code` VARCHAR ( 32 ) NOT NULL COMMENT '设备编码',
	`tenant_id` VARCHAR ( 32 ) DEFAULT NULL COMMENT '租户ID',
	`collect_time` datetime DEFAULT NULL COMMENT '采集时间',
	`electricy_increment` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '用电增量',
	`electricy_total` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '累计用电量',
	`stop_reading` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '止码读数',
	`creator_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '创建人id',
	`create_time_` datetime DEFAULT NULL COMMENT '创建日期',
	`modify_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '修改人id',
	`modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
	`remark_` text COMMENT '备注',
PRIMARY KEY ( `id` ) USING BTREE
) COMMENT '用电记录';

-- ------------------------------------
-- 配置中心 喷淋&用水&扬尘&用电设备型号配置
-- ------------------------------------
INSERT INTO `app_config` (`key_`, `tenant_id`, `value_`, `example`, `module_level`, `describe_`, `create_time`, `modify_time`)
VALUES
	('SPRAY_DEVICE_UNIT_CODES', '405cd2c8633ecbee1c5f61c4a87c9019', '[\"DLQ-RS485-16-0123\",\"WLIO-4G-02-0013\"]', '[\"DLQ-RS485-16-0123\",\"WLIO-4G-02-0013\"]', '环境管理:喷淋历史记录', '喷淋历史记录中的设备型号', now(), now());

INSERT INTO `app_config` ( `key_`, `tenant_id`, `value_`, `example`, `module_level`, `describe_`, `create_time`, `modify_time`)
VALUES
	('WATER_DEVICE_UNIT_CODES', '405cd2c8633ecbee1c5f61c4a87c9019', '[\"ZNSB-NBA-11-0136\"]', '[\"ZNSB-NBA-11-0136\"]', '环境管理:用水历史记录', '用水历史记录中的设备型号', now(), now());

INSERT INTO `app_config` (`key_`, `tenant_id`, `value_`, `example`, `module_level`, `describe_`, `create_time`, `modify_time`)
VALUES
	('RAISE_DUST_DEVICE_UNIT_CODES', '405cd2c8633ecbee1c5f61c4a87c9019', '[\"YCJC-YYJR-01-0999\"]', '[\"YCJC-YYJR-01-0999\"]', '环境管理:环境历史记录', '环境历史记录中的设备型号', now(), now());

INSERT INTO `app_config` (`key_`, `tenant_id`, `value_`, `example`, `module_level`, `describe_`, `create_time`, `modify_time`)
VALUES
	('ELECTRICY_DEVICE_UNIT_CODES', '405cd2c8633ecbee1c5f61c4a87c9019', '[\"SXDB-NBAA-01-0012\"]', '[\"SXDB-NBAA-01-0012\"]', '环境管理:用电历史记录', '用电历史记录中的设备型号', now(), now());

