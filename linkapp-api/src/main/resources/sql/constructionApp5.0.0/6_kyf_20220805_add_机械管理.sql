-- ----------------------------
-- 人员类别
-- ----------------------------
CREATE TABLE `app_machinery_user_type`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
	`type_`          int(2)        DEFAULT NULL COMMENT '类别 1工种 2自定义',
    `name_`          varchar(128)  DEFAULT NULL COMMENT '名称',
	`devices_`       varchar(500)  DEFAULT NULL COMMENT '设备类型(多个逗号分隔,保存的是编码)',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime(0)   DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime(0)   DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='人员类别';

-- ----------------------------
-- 人员类别关联人员
-- ----------------------------
CREATE TABLE `app_machinery_user_link`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`      varchar(32)  DEFAULT NULL COMMENT '租户id',
    `type_id_`        int(11)     DEFAULT NULL COMMENT '人员类别id',
    `user_id_`        varchar(32)  DEFAULT NULL COMMENT '用户id',
    `certificate_id_` varchar(1000) DEFAULT NULL COMMENT '证书id(多个逗号分隔)',
	`creator_id_`    bigint(20)   DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime(0)  DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)   DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime(0)  DEFAULT NULL COMMENT '修改时间',
	`remark_`        text         COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB comment '人员类别关联人员';

-- ----------------------------
-- 设备电子档案
-- ----------------------------
CREATE TABLE `app_machinery_record`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`      varchar(32)  DEFAULT NULL COMMENT '租户id',
    `type_code_`      varchar(32)  DEFAULT NULL COMMENT '类型code',
    `code_`           varchar(64)  DEFAULT NULL COMMENT '设备编号',
    `name_`           varchar(32)  DEFAULT NULL COMMENT '名称',
	`is_had_`         int(2)       DEFAULT NULL COMMENT '是否自有设备(1是0否)',
    `supplier_`       varchar(64)  DEFAULT NULL COMMENT '供应商',
	`join_time_`      datetime     DEFAULT NULL COMMENT '进场时间',
	`model_`          varchar(64)  DEFAULT NULL COMMENT '规格型号',
	`manufactor_`     varchar(64)  DEFAULT NULL COMMENT '生产厂家',
    `manufactor_code_` varchar(32) DEFAULT NULL COMMENT '出厂编号',
    `init_code_`      varchar(32)  DEFAULT NULL COMMENT '登记号',
	`manufactor_time_` datetime    DEFAULT NULL COMMENT '出厂日期',
	`is_special_`     int(2)       DEFAULT NULL COMMENT '是否特种设备(1是0否)',
	`parameter_`     text          DEFAULT NULL COMMENT '设备特性参数json',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB comment '设备电子档案';

-- ----------------------------
-- 设备类型表
-- ----------------------------
CREATE TABLE `app_machinery_type`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`      varchar(32)  DEFAULT NULL COMMENT '租户id(预留)',
    `code_`           varchar(32)  DEFAULT NULL COMMENT '编码',
    `name_`           varchar(64)  DEFAULT NULL COMMENT '定义(名称)',
    `value_`          varchar(64)  DEFAULT NULL COMMENT '数值',
	`creator_id_`     bigint(20)   DEFAULT NULL COMMENT '创建人id',
	`create_time_`    datetime     DEFAULT NULL COMMENT '创建日期',
	`modify_id_`      bigint(20)   DEFAULT NULL COMMENT '修改人id',
	`modify_time_`    datetime     DEFAULT NULL COMMENT '修改时间',
	`remark_`         text         COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB comment '设备类型表';
INSERT INTO `app_machinery_type` VALUES (1, NULL, 'QUIP_TSQZJ_01', '塔式起重机', '100001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `app_machinery_type` VALUES (2, NULL, 'QUIP_SJJ_02', '升降机', '100002', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `app_machinery_type` VALUES (3, NULL, 'QUIP_XLPT_03', '卸料平台', '100003', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `app_machinery_type` VALUES (4, NULL, 'QUIP_WJJ_04', '挖掘机', '100004', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `app_machinery_type` VALUES (5, NULL, 'QUIP_TTJ_05', '推土机', '100005', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `app_machinery_type` VALUES (6, NULL, 'QUIP_ZZJ_06', '装载机', '100006', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `app_machinery_type` VALUES (7, NULL, 'QUIP_ZJ_07', '钻机', '100007', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `app_machinery_type` VALUES (8, NULL, 'QUIP_YLJ_08', '压路机', '100008', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `app_machinery_type` VALUES (9, NULL, 'QUIP_ZTC_09', '渣土车', '100009', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `app_machinery_type` VALUES (10, NULL, 'QUIP_SSZ_10', '洒水车', '100010', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- 设备参数表
-- ----------------------------
CREATE TABLE `app_machinery_parameter`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`      varchar(32)  DEFAULT NULL COMMENT '租户id(预留)',
	`type_code_`      varchar(32)  DEFAULT NULL COMMENT '类型code',
    `label_`          varchar(32)  DEFAULT NULL COMMENT '参数显示名',
    `key_`            varchar(32)  DEFAULT NULL COMMENT '参数key',
    `value_`          varchar(32)  DEFAULT NULL COMMENT '参数值',
	`unit_`           varchar(32)  DEFAULT NULL COMMENT '单位',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime   DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime   DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB comment 'app_machinery_parameter';