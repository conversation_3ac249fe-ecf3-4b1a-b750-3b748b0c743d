UPDATE emp_device_type SET create_time='2019-12-03 11:07:32', creator_id=1, creator_name='admin', modify_time='2022-08-09 09:40:28', reviser_id=1, reviser_name='admin', company_id=NULL, description=NULL, `level`=NULL, category=0, name='智能三相电表', remark='', search_code='20000041|20000050', parent_id=20000041, draft_physics_model='{"profile":{"device_category":"三相电表","version":"1.0"},"properties":[{"dataType":{"specs":{},"type":"string"},"name":"设备ID","identifier":"device_id"},{"identifier":"a_phase_voltage","name":"A相电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"b_phase_voltage","name":"B相电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"c_phase_voltage","name":"C相电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"ab_phase_voltage","name":"AB线电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"bc_phase_voltage","name":"BC线电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"ac_phase_voltage","name":"AC线电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"a_phase_current","name":"A相电流","dataType":{"type":"double","specs":{"unit":"A","unit_name":"安培"}}},{"identifier":"b_phase_current","name":"B相电流","dataType":{"type":"double","specs":{"unit":"A","unit_name":"安培"}}},{"identifier":"c_phase_current","name":"C相电流","dataType":{"type":"double","specs":{"unit":"A","unit_name":"安培"}}},{"identifier":"zero_phase_current","name":"零序电流","dataType":{"type":"double","specs":{"unit":"A","unit_name":"安培"}}},{"identifier":"active_power","name":"有功总功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"a_phase_active_power","name":"瞬时A相有功功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"b_phase_active_power","name":"瞬时B相有功功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"c_phase_active_power","name":"瞬时C相有功功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"reactive_power","name":"无功总功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"apparent_power","name":"总视在功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"power_factor","name":"总功率因数","dataType":{"type":"double","specs":{}}},{"identifier":"a_phase_power_factor","name":"A相功率因数","dataType":{"type":"double","specs":{}}},{"identifier":"b_phase_power_factor","name":"B相功率因数","dataType":{"type":"double","specs":{}}},{"identifier":"c_phase_power_factor","name":"C相功率因数","dataType":{"type":"double","specs":{}}},{"identifier":"frequency","name":"电网频率","dataType":{"type":"double","specs":{"unit":"hz","unit_name":"赫兹"}}},{"identifier":"voltage_unbalance_rate","name":"电压不平衡率","dataType":{"type":"double","specs":{}}},{"identifier":"current_unbalance_rate","name":"电流不平衡率","dataType":{"type":"double","specs":{}}},{"identifier":"voltage_ratio","name":"电压变比","dataType":{"type":"double","specs":{}}},{"identifier":"current_ratio","name":"电流变比","dataType":{"type":"double","specs":{}}},{"identifier":"combined_active_energy","name":"组合有功总电能","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"combined_active_energy_point","name":"组合有功总电能尖","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"combined_active_energy_peak","name":"组合有功总电能峰","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"combined_active_energy_level","name":"组合有功总电能平","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"combined_active_energy_valley","name":"组合有功总电能谷","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy","name":"正向有功总电能","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy_point","name":"正向有功总电能尖","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy_peak","name":"正向有功总电能峰","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy_level","name":"正向有功总电能平","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy_valley","name":"正向有功总电能谷","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy","name":"反向有功总电能","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy_point","name":"反向有功总电能尖","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy_peak","name":"反向有功总电能峰","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy_level","name":"反向有功总电能平","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy_valley","name":"反向有功总电能谷","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"switch_state","name":"通断状态","dataType":{"type":"enum","specs":{"0":"断电","1":"通电"}}},{"identifier":"ack_state","name":"应答状态","dataType":{"type":"enum","specs":{"0":"成功","1":"失败"}}},{"identifier":"heartbeat_interval","name":"心跳时间间隔","dataType":{"type":"int","specs":{"unit":"s","unit_name":"分钟"}},"_index":43,"_rowKey":335}],"services":[{"identifier":"switch_control","name":"通断控制","inputs":[]},{"identifier":"heartbeat_cycle_set","name":"心跳时间设置","inputs":[]}]}', physics_model='{"profile":{"device_category":"三相电表","version":"1.0"},"properties":[{"dataType":{"specs":{},"type":"string"},"name":"设备ID","identifier":"device_id"},{"identifier":"a_phase_voltage","name":"A相电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"b_phase_voltage","name":"B相电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"c_phase_voltage","name":"C相电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"ab_phase_voltage","name":"AB线电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"bc_phase_voltage","name":"BC线电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"ac_phase_voltage","name":"AC线电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"a_phase_current","name":"A相电流","dataType":{"type":"double","specs":{"unit":"A","unit_name":"安培"}}},{"identifier":"b_phase_current","name":"B相电流","dataType":{"type":"double","specs":{"unit":"A","unit_name":"安培"}}},{"identifier":"c_phase_current","name":"C相电流","dataType":{"type":"double","specs":{"unit":"A","unit_name":"安培"}}},{"identifier":"zero_phase_current","name":"零序电流","dataType":{"type":"double","specs":{"unit":"A","unit_name":"安培"}}},{"identifier":"active_power","name":"有功总功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"a_phase_active_power","name":"瞬时A相有功功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"b_phase_active_power","name":"瞬时B相有功功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"c_phase_active_power","name":"瞬时C相有功功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"reactive_power","name":"无功总功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"apparent_power","name":"总视在功率","dataType":{"type":"double","specs":{"unit":"kw","unit_name":"千瓦"}}},{"identifier":"power_factor","name":"总功率因数","dataType":{"type":"double","specs":{}}},{"identifier":"a_phase_power_factor","name":"A相功率因数","dataType":{"type":"double","specs":{}}},{"identifier":"b_phase_power_factor","name":"B相功率因数","dataType":{"type":"double","specs":{}}},{"identifier":"c_phase_power_factor","name":"C相功率因数","dataType":{"type":"double","specs":{}}},{"identifier":"frequency","name":"电网频率","dataType":{"type":"double","specs":{"unit":"hz","unit_name":"赫兹"}}},{"identifier":"voltage_unbalance_rate","name":"电压不平衡率","dataType":{"type":"double","specs":{}}},{"identifier":"current_unbalance_rate","name":"电流不平衡率","dataType":{"type":"double","specs":{}}},{"identifier":"voltage_ratio","name":"电压变比","dataType":{"type":"double","specs":{}}},{"identifier":"current_ratio","name":"电流变比","dataType":{"type":"double","specs":{}}},{"identifier":"combined_active_energy","name":"组合有功总电能","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"combined_active_energy_point","name":"组合有功总电能尖","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"combined_active_energy_peak","name":"组合有功总电能峰","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"combined_active_energy_level","name":"组合有功总电能平","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"combined_active_energy_valley","name":"组合有功总电能谷","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy","name":"正向有功总电能","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy_point","name":"正向有功总电能尖","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy_peak","name":"正向有功总电能峰","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy_level","name":"正向有功总电能平","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"positive_active_energy_valley","name":"正向有功总电能谷","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy","name":"反向有功总电能","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy_point","name":"反向有功总电能尖","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy_peak","name":"反向有功总电能峰","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy_level","name":"反向有功总电能平","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"negative_active_energy_valley","name":"反向有功总电能谷","dataType":{"type":"double","specs":{"unit":"kwh","unit_name":"千瓦时"}}},{"identifier":"switch_state","name":"通断状态","dataType":{"type":"enum","specs":{"0":"断电","1":"通电"}}},{"identifier":"ack_state","name":"应答状态","dataType":{"type":"enum","specs":{"0":"成功","1":"失败"}}},{"identifier":"heartbeat_interval","name":"心跳时间间隔","dataType":{"type":"int","specs":{"unit":"s","unit_name":"分钟"}},"_index":43,"_rowKey":335}],"services":[{"identifier":"switch_control","name":"通断控制","inputs":[]},{"identifier":"heartbeat_cycle_set","name":"心跳时间设置","inputs":[]}]}', publish_id=20000528, publish_name='刘波', publish_time='2020-06-16 14:42:58', pic_url_='https://puboss.easylinkin.com/private/console/mix/20200717175333-三相电表.png', scenario_id=4, scenario='能源元场景' WHERE id=20000050;
UPDATE emp_device_type SET create_time='2019-11-12 15:38:35', creator_id=1, creator_name='admin', modify_time='2022-08-09 09:40:08', reviser_id=1, reviser_name='admin', company_id=NULL, description=NULL, `level`=NULL, category=0, name='智能水表', remark='', search_code='20000041|20000034', parent_id=20000041, draft_physics_model='{"profile":{"device_category":"冷水表","version":"1.0"},"properties":[{"dataType":{"specs":{},"type":"int"},"name":"查询水表信息","identifier":"query_meter_information"},{"dataType":{"specs":{},"type":"int"},"name":"流量清零","identifier":"clear_flow"},{"dataType":{"specs":{},"type":"int"},"name":"查询剩余流量","identifier":"query_surplus_flow"},{"dataType":{"specs":{"unit":"H"},"type":"double"},"name":"心跳时间间隔","identifier":"heartbeat_interval"},{"dataType":{"specs":{"unit":"L"},"type":"double"},"name":"剩余流量","identifier":"surplus_flow"},{"dataType":{"specs":{"0":"正常","1":"告警"},"type":"enum"},"name":"剩余流量告警","identifier":"surplus_flow_alarm"},{"dataType":{"specs":{"0":"成功","1":"失败"},"type":"enum"},"name":"应答状态","identifier":"ack_state"},{"dataType":{"specs":{},"type":"string"},"name":"设备ID","identifier":"device_id"},{"dataType":{"specs":{"0":"正常","1":"异常"},"type":"enum"},"name":"强磁干扰","identifier":"magnetic_interference"},{"dataType":{"specs":{"0":"开阀","1":"关阀","2":"异常"},"type":"enum"},"name":"阀门状态","identifier":"valve_state"},{"identifier":"water_flux","name":"当前累计流量","dataType":{"type":"double","specs":{"unit":"t","unit_name":"吨"}}},{"identifier":"battery_voltage","name":"电池电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"battery_state","name":"电池状态","dataType":{"type":"enum","specs":{"0":"正常","1":"欠压"}}}],"services":[{"name":"获取累计流量","identifier":"getWaterFlux","inputs":[]},{"name":"查询水表信息","identifier":"query_meter_information","inputs":[]},{"name":"更改上传时间","identifier":"change_heartbeat_interval","inputs":[]},{"name":"流量充值","identifier":"recharge_flow","inputs":[]},{"name":"流量清零","identifier":"clear_flow","inputs":[]},{"name":"查询剩余流量","identifier":"query_surplus_flow","inputs":[]},{"name":"通断控制","identifier":"switch_control","inputs":[]}]}', physics_model='{"profile":{"device_category":"冷水表","version":"1.0"},"properties":[{"dataType":{"specs":{},"type":"int"},"name":"查询水表信息","identifier":"query_meter_information"},{"dataType":{"specs":{},"type":"int"},"name":"流量清零","identifier":"clear_flow"},{"dataType":{"specs":{},"type":"int"},"name":"查询剩余流量","identifier":"query_surplus_flow"},{"dataType":{"specs":{"unit":"H"},"type":"double"},"name":"心跳时间间隔","identifier":"heartbeat_interval"},{"dataType":{"specs":{"unit":"L"},"type":"double"},"name":"剩余流量","identifier":"surplus_flow"},{"dataType":{"specs":{"0":"正常","1":"告警"},"type":"enum"},"name":"剩余流量告警","identifier":"surplus_flow_alarm"},{"dataType":{"specs":{"0":"成功","1":"失败"},"type":"enum"},"name":"应答状态","identifier":"ack_state"},{"dataType":{"specs":{},"type":"string"},"name":"设备ID","identifier":"device_id"},{"dataType":{"specs":{"0":"正常","1":"异常"},"type":"enum"},"name":"强磁干扰","identifier":"magnetic_interference"},{"dataType":{"specs":{"0":"开阀","1":"关阀","2":"异常"},"type":"enum"},"name":"阀门状态","identifier":"valve_state"},{"identifier":"water_flux","name":"当前累计流量","dataType":{"type":"double","specs":{"unit":"t","unit_name":"吨"}}},{"identifier":"battery_voltage","name":"电池电压","dataType":{"type":"double","specs":{"unit":"V","unit_name":"伏特"}}},{"identifier":"battery_state","name":"电池状态","dataType":{"type":"enum","specs":{"0":"正常","1":"欠压"}}}],"services":[{"name":"获取累计流量","identifier":"getWaterFlux","inputs":[]},{"name":"查询水表信息","identifier":"query_meter_information","inputs":[]},{"name":"更改上传时间","identifier":"change_heartbeat_interval","inputs":[]},{"name":"流量充值","identifier":"recharge_flow","inputs":[]},{"name":"流量清零","identifier":"clear_flow","inputs":[]},{"name":"查询剩余流量","identifier":"query_surplus_flow","inputs":[]},{"name":"通断控制","identifier":"switch_control","inputs":[]}]}', publish_id=20001276, publish_name='赵洪鹏', publish_time='2022-03-18 13:07:29', pic_url_='https://puboss.easylinkin.com/private/console/mix/20200727182838-2.png', scenario_id=4, scenario='能源元场景' WHERE id=20000034;
UPDATE emp_device_type SET create_time='2020-10-26 15:20:58', creator_id=20001276, creator_name='赵洪鹏', modify_time='2022-08-09 09:38:22', reviser_id=1, reviser_name='admin', company_id=NULL, description=NULL, `level`=NULL, category=0, name='扬尘监测系统', remark='多合一气象站传感器，监测温度、湿度、气压、风速、风向、PM2.5/10、噪声等。', search_code=NULL, parent_id=20000040, draft_physics_model='{"profile":{"device_category":"多合一气象监测器","version":"1.0"},"properties":[{"dataType":{"specs":{"unit":"min"},"type":"int"},"name":"雨量累计时间","identifier":"rain_cumulative_time"},{"dataType":{"specs":{"0":"成功","1":"失败"},"type":"bool"},"name":"应答状态","identifier":"ack_state"},{"dataType":{"specs":{"unit":"klux"},"type":"double"},"identifier":"illuminance","name":"光照度"},{"dataType":{"specs":{"unit":"mm"},"type":"double"},"name":"降雨量","identifier":"rainfall"},{"dataType":{"specs":{"unit":"°"},"type":"double"},"name":"风向","identifier":"wind_direction"},{"dataType":{"specs":{"unit":"m/s"},"type":"double"},"identifier":"wind_speed","name":"风速"},{"dataType":{"specs":{"unit":"hPa"},"type":"double"},"name":"大气压","identifier":"air_pressure"},{"dataType":{"specs":{"unit":"dB"},"type":"double"},"name":"噪声","identifier":"noise"},{"dataType":{"specs":{"unit":"ug/m3"},"type":"int"},"name":"PM10浓度","identifier":"pm10"},{"dataType":{"specs":{"unit":"ug/m3"},"type":"int"},"name":"PM2.5浓度","identifier":"pm25"},{"inputs":[],"dataType":{"specs":{"unit":"%"},"type":"double"},"name":"湿度","identifier":"humidity"},{"dataType":{"specs":{"unit":"℃"},"type":"double"},"name":"温度","identifier":"temperature"}],"services":[{"name":"清除雨量","identifier":"clearRainfall","inputs":[]},{"name":"获取气象数据","identifier":"getAirData","inputs":[]}]}', physics_model='{"profile":{"device_category":"多合一气象监测器","version":"1.0"},"properties":[{"dataType":{"specs":{"unit":"min"},"type":"int"},"name":"雨量累计时间","identifier":"rain_cumulative_time"},{"dataType":{"specs":{"0":"成功","1":"失败"},"type":"bool"},"name":"应答状态","identifier":"ack_state"},{"dataType":{"specs":{"unit":"klux"},"type":"double"},"identifier":"illuminance","name":"光照度"},{"dataType":{"specs":{"unit":"mm"},"type":"double"},"name":"降雨量","identifier":"rainfall"},{"dataType":{"specs":{"unit":"°"},"type":"double"},"name":"风向","identifier":"wind_direction"},{"dataType":{"specs":{"unit":"m/s"},"type":"double"},"identifier":"wind_speed","name":"风速"},{"dataType":{"specs":{"unit":"hPa"},"type":"double"},"name":"大气压","identifier":"air_pressure"},{"dataType":{"specs":{"unit":"dB"},"type":"double"},"name":"噪声","identifier":"noise"},{"dataType":{"specs":{"unit":"ug/m3"},"type":"int"},"name":"PM10浓度","identifier":"pm10"},{"dataType":{"specs":{"unit":"ug/m3"},"type":"int"},"name":"PM2.5浓度","identifier":"pm25"},{"inputs":[],"dataType":{"specs":{"unit":"%"},"type":"double"},"name":"湿度","identifier":"humidity"},{"dataType":{"specs":{"unit":"℃"},"type":"double"},"name":"温度","identifier":"temperature"}],"services":[{"name":"清除雨量","identifier":"clearRainfall","inputs":[]},{"name":"获取气象数据","identifier":"getAirData","inputs":[]}]}', publish_id=20001276, publish_name='赵洪鹏', publish_time='2022-03-19 09:23:07', pic_url_='https://puboss.easylinkin.com/private/console/mix/20201026152015-QQ截图20201024121325.jpg', scenario_id=3, scenario='环境元场景' WHERE id=20000254;
UPDATE emp_device_type SET create_time='2022-04-06 11:39:12', creator_id=20000518, creator_name='device_pub_xyf名', modify_time='2022-08-09 09:38:54', reviser_id=1, reviser_name='admin', company_id=NULL, description=NULL, `level`=NULL, category=0, name='喷淋控制器', remark='', search_code=NULL, parent_id=20000040, draft_physics_model=NULL, physics_model=NULL, publish_id=NULL, publish_name=NULL, publish_time=NULL, pic_url_='https://puboss.easylinkin.com/private/console/mix/20220406113911-20210610180410-562c11dfa9ec8a13e355fe69f903918fa1ecc0e8.jpg', scenario_id=3, scenario='环境元场景' WHERE id=20000306;
INSERT INTO emp_device_type (id, create_time, creator_id, creator_name, modify_time, reviser_id, reviser_name, company_id, description, `level`, category, name, remark, search_code, parent_id, draft_physics_model, physics_model, publish_id, publish_name, publish_time, pic_url_, scenario_id, scenario) VALUES(20000307, '2022-08-04 14:45:09', 1, 'admin', '2022-08-04 14:45:30', 1, 'admin', NULL, NULL, NULL, 0, '塔吊', '', NULL, 20000043, NULL, NULL, NULL, NULL, NULL, 'http://exhibition.net4iot.com:16001/api/ossNew/download/private/console/mix/20220804144506-u=1409925316,2518034660&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto.webp', 2, '安防元场景');
INSERT INTO emp_device_type (id, create_time, creator_id, creator_name, modify_time, reviser_id, reviser_name, company_id, description, `level`, category, name, remark, search_code, parent_id, draft_physics_model, physics_model, publish_id, publish_name, publish_time, pic_url_, scenario_id, scenario) VALUES(20000308, '2022-08-04 16:06:28', 1, 'admin', '2022-08-08 16:52:31', 1, 'admin', NULL, NULL, NULL, 0, '升降机安全监管系统', '', NULL, 20000043, NULL, NULL, NULL, NULL, NULL, 'http://exhibition.net4iot.com:16001/api/ossNew/download/private/console/mix/20220808165229-捕获.PNG', 2, '安防元场景');
INSERT INTO emp_device_type (id, create_time, creator_id, creator_name, modify_time, reviser_id, reviser_name, company_id, description, `level`, category, name, remark, search_code, parent_id, draft_physics_model, physics_model, publish_id, publish_name, publish_time, pic_url_, scenario_id, scenario) VALUES(20000309, '2022-08-08 15:25:37', 1, 'admin', '2022-08-09 09:38:05', 1, 'admin', NULL, NULL, NULL, 0, '实名制面板机', '', NULL, 20000044, NULL, NULL, NULL, NULL, NULL, 'http://exhibition.net4iot.com:16001/api/ossNew/download/private/console/mix/20220808152535-339b59f6e4834d528ae7a4e0eac96a78.jpeg', 2, '安防元场景');
INSERT INTO emp_device_type (id, create_time, creator_id, creator_name, modify_time, reviser_id, reviser_name, company_id, description, `level`, category, name, remark, search_code, parent_id, draft_physics_model, physics_model, publish_id, publish_name, publish_time, pic_url_, scenario_id, scenario) VALUES(20000310, '2022-08-08 16:53:32', 1, 'admin', '2022-08-08 16:53:32', 1, 'admin', 20000003, NULL, NULL, 0, '塔机安全监管系统', '', NULL, 20000043, NULL, NULL, NULL, NULL, NULL, 'http://exhibition.net4iot.com:16001/api/ossNew/download/private/console/mix/20220808165330-2.PNG', 2, '安防元场景');
INSERT INTO emp_device_type (id, create_time, creator_id, creator_name, modify_time, reviser_id, reviser_name, company_id, description, `level`, category, name, remark, search_code, parent_id, draft_physics_model, physics_model, publish_id, publish_name, publish_time, pic_url_, scenario_id, scenario) VALUES(20000311, '2022-08-08 16:54:47', 1, 'admin', '2022-08-08 16:54:47', 1, 'admin', 20000003, NULL, NULL, 0, '塔机吊钩可视化系统', '', NULL, 20000043, NULL, NULL, NULL, NULL, NULL, 'http://exhibition.net4iot.com:16001/api/ossNew/download/private/console/mix/20220808165445-3.PNG', 2, '安防元场景');

UPDATE app_config SET value_='喷淋控制器', example='喷淋控制器', module_level='环境管理:喷淋控制器', describe_='环境管理喷淋控制器中的设备类别' where key_ ='SPARY_DEVICE_TYPE_NAMES';

UPDATE app_config SET value_='智能水表', example='智能水表', module_level='环境管理:智能水表', describe_='环境管理智能水表中的设备类别' where key_ ='WATER_DEVICE_TYPE_NAMES';

UPDATE app_config SET value_='智能三相电表', example='智能三相电表', module_level='环境管理:智能三相电表', describe_='环境管理智能三相电表中的设备类别' where key_ ='ELECTRICY_DEVICE_TYPE_NAMES';

UPDATE app_config SET value_='扬尘监测系统', example='扬尘监测系统', module_level='环境管理:扬尘监测系统', describe_='环境管理扬尘监测系统中的设备类别' where key_ ='RAISE_DUST_DEVICE_TYPE_NAMES';