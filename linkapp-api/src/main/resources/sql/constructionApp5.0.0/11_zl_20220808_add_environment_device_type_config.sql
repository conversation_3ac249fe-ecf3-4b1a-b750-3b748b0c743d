INSERT INTO app_config (key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time) 
values
('SPARY_DEVICE_TYPE_NAMES', '25e9d30c461d84cb245a49c3ce12dfb7', '阀门控制器,三相智能断路器', '阀门控制器,三相智能断路器', '环境管理:喷淋控制器', '环境管理喷淋控制器中的设备类别', now(), now());

INSERT INTO app_config (key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time) 
values
('WATER_DEVICE_TYPE_NAMES', '25e9d30c461d84cb245a49c3ce12dfb7', '冷水表,热水表', '冷水表,热水表', '环境管理:智能水表', '环境管理智能水表中的设备类别', now(), now());

INSERT INTO app_config (key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time) 
values
('ELECTRICY_DEVICE_TYPE_NAMES', '25e9d30c461d84cb245a49c3ce12dfb7', '单相电表,三相电表', '单相电表,三相电表', '环境管理:智能电表', '环境管理智能电表中的设备类别', now(), now());

INSERT INTO app_config (key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time) 
values
('RAISE_DUST_DEVICE_TYPE_NAMES', '25e9d30c461d84cb245a49c3ce12dfb7', '多合一空气监测器', '多合一空气监测器', '环境管理:扬尘监控', '环境管理扬尘监控中的设备类别', now(), now());

update app_config  set key_ = 'DQHZ_DEVICE_TYPE_NAMES' where key_ = 'DQHZ_DEVICE_UNIT_CODES';