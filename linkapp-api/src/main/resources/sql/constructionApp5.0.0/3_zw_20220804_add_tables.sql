-- 省市区信息
CREATE TABLE `app_manage_info`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `mange_name`    varchar(100)  DEFAULT NULL COMMENT '管理区域名称',
    `mange_code`    varchar(40)   DEFAULT NULL COMMENT '管理区域编码',
    `type`          int(11) DEFAULT NULL COMMENT '类型，1国家、2省、3市、4区',
    `level`         int(11) DEFAULT NULL COMMENT '等级，树形层级',
    `parent_id`     bigint(20) DEFAULT NULL COMMENT '父级id',
    `parent_ids`    varchar(2000) DEFAULT NULL COMMENT '父级ids',
    `direst_manage` int(11) NOT NULL COMMENT '是否直辖市，0否，1是',
    `sort_no`       int(11) DEFAULT NULL COMMENT '排序号',
    PRIMARY KEY (`id`),
    KEY             `un_manage_info_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB COMMENT='行政管理区域信息表';

-- 项目产值信息
CREATE TABLE `app_output_info`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`         varchar(32)    DEFAULT NULL COMMENT '租户id',
    `tenant_project_id` bigint(20) DEFAULT NULL COMMENT '租户项目id',
    `fee_code`          varchar(50)    DEFAULT NULL COMMENT '费用编码',
    `fee_name`          varchar(100)   DEFAULT NULL COMMENT '费用名称',
    `fee_tax_total`     decimal(20, 4) DEFAULT NULL COMMENT '含税合价',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `creator`           varchar(100)   DEFAULT NULL COMMENT '创建人',
    `modifier`          varchar(100)   DEFAULT NULL COMMENT '修改人',
    `delete_state`      int(1) DEFAULT NULL COMMENT '是否删除，0:已删; 1:存在',
    PRIMARY KEY (`id`),
    KEY                 `linkapp_output_info_tenant_id_IDX` (`tenant_id`) USING BTREE,
    KEY                 `linkapp_output_info_tenant_project_id_IDX` (`tenant_project_id`) USING BTREE
) ENGINE=InnoDB COMMENT='产值信息表';

-- 项目扩展信息
CREATE TABLE `app_tenant_project`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`             varchar(32)    DEFAULT NULL COMMENT '租户id',
    `start_date`            datetime       DEFAULT NULL COMMENT '开工时间',
    `end_date`              datetime       DEFAULT NULL COMMENT '竣工时间',
    `plan_end_date`         datetime       DEFAULT NULL COMMENT '预计竣工时间',
    `progress`              varchar(10)    DEFAULT NULL COMMENT '项目进度，采集进展信息计算更新',
    `build_vol`             varchar(30)    DEFAULT NULL COMMENT '建筑体量',
    `contract_amount`       decimal(20, 4) DEFAULT NULL COMMENT '合同金额',
    `platform_project_name` varchar(300)   DEFAULT NULL COMMENT '项目名称（冗余）',
    `bidding_unit`          varchar(300)   DEFAULT NULL COMMENT '建设单位（冗余）',
    `construction_unit`     varchar(300)   DEFAULT NULL COMMENT '承建单位（冗余）',
    `location`              varchar(300)   DEFAULT NULL COMMENT '项目地址（冗余）',
    `manage_id`             bigint(20) DEFAULT NULL COMMENT '区域id',
    `project_type_name`     varchar(2)     DEFAULT NULL COMMENT '项目类型名称',
    `is_class`              varchar(1)     DEFAULT NULL COMMENT '是否创优',
    `pc_class`              varchar(20)    DEFAULT NULL COMMENT '创优等级',
    `phid_type`             bigint(20) DEFAULT NULL COMMENT '项目类型编码',
    `fill_dt`               datetime       DEFAULT NULL COMMENT '录入日期',
    `project_statue`        int(11) DEFAULT NULL COMMENT '项目状态，1在建，2中标，3施工准备，4停工缓建，5完工待结算，6完工已结算',
    `create_time`           datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_time`           datetime       DEFAULT NULL COMMENT '修改时间',
    `creator`               varchar(100)   DEFAULT NULL COMMENT '创建人',
    `modifier`              varchar(100)   DEFAULT NULL COMMENT '修改人',
    `delete_state`          int(1) DEFAULT NULL COMMENT '是否删除，0:已删; 1:存在',
    `phid`                  bigint(20) DEFAULT NULL COMMENT '新中大现场检查主表id',
    PRIMARY KEY (`id`),
    KEY                     `linkapp_tenant_project_tenant_id_IDX` (`tenant_id`) USING BTREE,
    KEY                     `linkapp_tenant_project_phid_IDX` (`phid`) USING BTREE,
    KEY                     `linkapp_tenant_project_manage_id_IDX` (`manage_id`) USING BTREE
) ENGINE=InnoDB COMMENT='租户项目信息';

-- 施工检查信息
CREATE TABLE `app_build_check_info`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `check_time`        datetime      DEFAULT NULL COMMENT '检查时间',
    `tenant_id`         varchar(32)   DEFAULT NULL COMMENT '租户id',
    `tenant_project_id` bigint(20) DEFAULT NULL COMMENT '租户项目id',
    `check_title`       varchar(200)  DEFAULT NULL COMMENT '检查标题',
    `check_content`     varchar(1000) DEFAULT NULL COMMENT '检查内容',
    `check_level`       int(11) DEFAULT NULL COMMENT '检查级别',
    `check_org_code`    varchar(100)  DEFAULT NULL COMMENT '检查组织编码',
    `check_org_name`    varchar(200)  DEFAULT NULL COMMENT '检查组织名称',
    `check_type`        int(11) DEFAULT NULL COMMENT '检查类别，1质量 2安全',
    `check_model`       int(11) DEFAULT NULL COMMENT '检查类型',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `creator`           varchar(100)  DEFAULT NULL COMMENT '创建人',
    `modifier`          varchar(100)  DEFAULT NULL COMMENT '修改人',
    `delete_state`      int(1) DEFAULT NULL COMMENT '是否删除，0:已删; 1:存在',
    `phid`              bigint(20) DEFAULT NULL COMMENT '新中大现场检查主表id',
    PRIMARY KEY (`id`),
    KEY                 `app_build_check_info_tenant_project_id_IDX` (`tenant_project_id`) USING BTREE,
    KEY                 `app_build_check_info_phid_IDX` (`phid`) USING BTREE,
    KEY                 `app_build_check_info_tenant_id_IDX` (`tenant_id`) USING BTREE
) ENGINE=InnoDB COMMENT='施工检查信息';

-- 机械电子档案与iot设备关联关系
CREATE TABLE `app_machinery_device_ref`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `machinery_id`   int(11) DEFAULT NULL COMMENT '电子档案id',
    `machinery_code` varchar(32) DEFAULT NULL COMMENT '电子档案设备编码（冗余）',
    `device_id`      varchar(32) DEFAULT NULL COMMENT '设备id',
    `device_code`    varchar(32) DEFAULT NULL COMMENT '设备编码（冗余）',
    `delete_state`   int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    `create_time`    datetime    DEFAULT NULL COMMENT '创建时间',
    `creator`        varchar(32) DEFAULT NULL COMMENT '创建人',
    `modifier`       varchar(32) DEFAULT NULL COMMENT '修改人',
    `modify_time`    datetime    DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY              `app_machinery_device_ref_machinery_id_IDX` (`machinery_id`) USING BTREE,
    KEY              `app_machinery_device_ref_device_id_IDX` (`device_id`) USING BTREE,
    KEY              `app_machinery_device_ref_device_code_IDX` (`device_code`) USING BTREE,
    KEY              `app_machinery_device_ref_machinery_code_IDX` (`machinery_code`) USING BTREE
) ENGINE=InnoDB COMMENT='电子档案与设备关联表';