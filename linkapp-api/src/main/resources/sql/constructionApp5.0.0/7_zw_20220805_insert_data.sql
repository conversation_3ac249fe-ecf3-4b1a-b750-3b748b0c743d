-- 行政管理初始数据
INSERT INTO app_manage_info
(id, mange_name, mange_code, `type`, `level`, parent_id, parent_ids, direst_manage, sort_no)
VALUES (1, '中国', '+86', 1, 0, -1, ',-1,', 0, 1);
INSERT INTO app_manage_info
(id, mange_name, mange_code, `type`, `level`, parent_id, parent_ids, direst_manage, sort_no)
VALUES (2, '湖北省', '43', 2, 1, 1, ',-1,1,', 0, 1);
INSERT INTO app_manage_info
(id, mange_name, mange_code, `type`, `level`, parent_id, parent_ids, direst_manage, sort_no)
VALUES (3, '武汉市', '4300', 3, 2, 2, ',-1,1,2,', 0, 1);
INSERT INTO app_manage_info
(id, mange_name, mange_code, `type`, `level`, parent_id, parent_ids, direst_manage, sort_no)
VALUES (4, '天门市', '4317', 3, 2, 2, ',-1,1,2,', 0, 2);
INSERT INTO app_manage_info
(id, mange_name, mange_code, `type`, `level`, parent_id, parent_ids, direst_manage, sort_no)
VALUES (5, '黄冈市', '4322', 3, 2, 2, ',-1,1,2,', 0, 3);
INSERT INTO app_manage_info
(id, mange_name, mange_code, `type`, `level`, parent_id, parent_ids, direst_manage, sort_no)
VALUES (6, '洪山区', '430011', 4, 3, 3, ',-1,1,2,3,', 0, 1);
INSERT INTO app_manage_info
(id, mange_name, mange_code, `type`, `level`, parent_id, parent_ids, direst_manage, sort_no)
VALUES (7, '青山区', '430012', 4, 3, 3, ',-1,1,2,3,', 0, 2);
INSERT INTO app_manage_info
(id, mange_name, mange_code, `type`, `level`, parent_id, parent_ids, direst_manage, sort_no)
VALUES (8, '竟陵城区', '431701', 4, 3, 4, ',-1,1,2,4,', 0, 1);

-- 升降机、塔吊配置
INSERT INTO app_config
(key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time)
VALUES ('MACHINERY_TYPE_QUIP_TSQZJ_01', '405cd2c8633ecbee1c5f61c4a87c9019',
        '[{"unitCode":"TJDG-4G-01-0233","iotType":"main"},{"unitCode":"SXT-QIUJ400W-03-0117","iotType":"video"}]', '[{"unitCode":"TJDG-4G-01-0233","iotType":"main"},{"unitCode":"SXT-QIUJ400W-03-0117","iotType":"video"}]', '机械管理:电子档案',
        '塔吊关联IOT设备', '2022-08-05 13:40:00', '2022-08-05 13:40:00');
INSERT INTO app_config
(key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time)
VALUES ('MACHINERY_TYPE_QUIP_SJJ_02', '405cd2c8633ecbee1c5f61c4a87c9019', '[{"unitCode":"SJJ-4G-01-0233","iotType":"main"},{"unitCode":"SXT-QIUJ400W-03-0117","iotType":"video"}]', '[{"unitCode":"SJJ-4G-01-0233","iotType":"main"},{"unitCode":"SXT-QIUJ400W-03-0117","iotType":"video"}]',
        '机械管理:电子档案', '升降机关联IOT设备', '2022-08-05 13:40:00', '2022-08-05 13:40:00');


