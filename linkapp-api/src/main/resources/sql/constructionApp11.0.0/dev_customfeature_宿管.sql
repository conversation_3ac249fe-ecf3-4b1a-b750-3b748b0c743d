DROP TABLE IF EXISTS child_hospital_building;
CREATE TABLE child_hospital_building(
    `id` VARCHAR(100) NOT NULL  COMMENT '主键' ,
    `building_name` VARCHAR(100) NOT NULL  COMMENT '楼栋名称' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    PRIMARY KEY (id)
)  COMMENT = '儿童医院楼栋表';

DROP TABLE IF EXISTS child_hospital_floor;
CREATE TABLE child_hospital_floor(
    `id` VARCHAR(100) NOT NULL  COMMENT '主键' ,
    `building_id` VARCHAR(100) NOT NULL  COMMENT '楼栋id' ,
    `building_name` VARCHAR(100)   COMMENT '楼栋名称' ,
    `floor_name` VARCHAR(32) NOT NULL  COMMENT '楼层名称' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    PRIMARY KEY (id)
)  COMMENT = '儿童医院楼层表';

DROP TABLE IF EXISTS child_hospital_room;
CREATE TABLE child_hospital_room(
    `id` VARCHAR(100) NOT NULL  COMMENT '主键' ,
    `floor_id` VARCHAR(100)   COMMENT '儿童医院楼层id' ,
    `room_name` VARCHAR(100) NOT NULL  COMMENT '房间名称' ,
    `room_number` INT  DEFAULT 0 COMMENT '房间床位数' ,
    `room_used_number` INT  DEFAULT 0 COMMENT '房间已用床位数' ,
    `room_label` VARCHAR(32)   COMMENT '房间入住人员标签，空：2/5' ,
    `sex` INT(1)  DEFAULT 1 COMMENT '性别 0--无 1--男 2--女' ,
    `room_status` INT(1)  DEFAULT 0 COMMENT '是否入住 0--空 1--入住' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    PRIMARY KEY (id)
)  COMMENT = '儿童医院房间表';


CREATE INDEX floor_id_index ON child_hospital_room(floor_id);

DROP TABLE IF EXISTS child_hospital_room_record;
CREATE TABLE child_hospital_room_record(
    `id` VARCHAR(32) NOT NULL  COMMENT '主键' ,
    `room_id` VARCHAR(32)   COMMENT '房间编号' ,
    `room_name` VARCHAR(32)   COMMENT '房间名称' ,
    `enter_start_time` DATETIME   COMMENT '入住开始时间' ,
    `enter_end_time` DATETIME   COMMENT '入住结束时间' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    PRIMARY KEY (id)
)  COMMENT = '儿童医院房间入住记录表';


CREATE INDEX room_id_index ON child_hospital_room_record(room_id);

DROP TABLE IF EXISTS child_hospital_user_detail;
CREATE TABLE child_hospital_user_detail(
    `id` VARCHAR(100) NOT NULL  COMMENT '主键' ,
    `room_id` VARCHAR(100)   COMMENT '房间入住记录id' ,
    `user_name` VARCHAR(32) NOT NULL  COMMENT '人员名称' ,
    `user_age` INT   COMMENT '年龄' ,
    `identification_number` VARCHAR(32)   COMMENT '身份证号' ,
    `organization` VARCHAR(32)   COMMENT '工作单位' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    `status` INT   COMMENT '状态：1 入住，0 历史' ,
    `out_time` DATETIME   COMMENT '退房时间' ,
    `sex` INT   COMMENT '0 女，1 男' ,
    `smoking` INT   COMMENT '0--不抽烟 1--抽烟' ,
    `snore` INT   COMMENT '0--不打呼 1--打呼' ,
    `image` VARCHAR(255)   COMMENT '图片' ,
    `work_type` VARCHAR(255)   COMMENT '工种' ,
    PRIMARY KEY (id)
)  COMMENT = '儿童医院房间入住人员详情信息登记表';


CREATE INDEX room_id_index ON child_hospital_user_detail(room_id);


/*---------- PC菜单 ----------*/
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('960', null, '宿舍管理', 'dormitoryManage', '宿舍管理', 1, null, null, 960.00, 1, 'manage/dormitoryManage', null, '1', '2024-03-25 17:29:27', null, null, null, null, 0);

/*---------- 小程序菜单 ----------*/
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('660960', '660000', '宿舍管理', 'dormitoryManage', '宿舍管理', 1, null, null, 660960.00, 1, null, null, '1', '2024-03-28 09:33:33', null, null, null, null, 1);