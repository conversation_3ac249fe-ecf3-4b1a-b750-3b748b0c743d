/*
 网格功能sql
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 隐患
-- ----------------------------
ALTER TABLE app_hidden_danger ADD `grid_id` bigint DEFAULT NULL COMMENT '网格id' AFTER `id`;

-- ----------------------------
-- Table structure for grid_appraise
-- ----------------------------
DROP TABLE IF EXISTS grid_appraise;
CREATE TABLE grid_appraise (
                                 id int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 appraise_name varchar(64) DEFAULT NULL COMMENT '评比名称',
                                 red_flag_company_id varchar(32) DEFAULT NULL COMMENT '红旗单位ID',
                                 red_flag_company_name varchar(64) DEFAULT NULL COMMENT '红旗单位名称',
                                 yellow_flag_company_id varchar(32) DEFAULT NULL COMMENT '红旗单位ID',
                                 yellow_flag_company_name varchar(64) DEFAULT NULL COMMENT '红旗单位名称',
                                 end_time datetime DEFAULT NULL COMMENT '填报截止时间',
                                 need_company_count varchar(64) DEFAULT NULL COMMENT '需填报公司数',
                                 company_count varchar(64) DEFAULT NULL COMMENT '填报公司数',
                                 fill_in_user_id varchar(32) DEFAULT NULL COMMENT '指定填报人',
                                 tenant_id varchar(32) DEFAULT NULL COMMENT '租户id',
                                 create_time datetime DEFAULT NULL COMMENT '创建时间',
                                 modify_time datetime DEFAULT NULL COMMENT '修改时间',
                                 creator_id varchar(32) DEFAULT NULL COMMENT '创建人id',
                                 modify_id varchar(32) DEFAULT NULL COMMENT '修改人id',
                                 delete_state int NOT NULL DEFAULT '1' COMMENT '删除标记 1未删除 0已删除',
                                 PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格评比表';

-- ----------------------------
-- Table structure for grid_appraise_company
-- ----------------------------
DROP TABLE IF EXISTS grid_appraise_company;
CREATE TABLE grid_appraise_company (
                                         id int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         appraise_id int NOT NULL COMMENT '评比主键',
                                         company_id varchar(32) DEFAULT NULL COMMENT '单位ID',
                                         company_name varchar(64) DEFAULT NULL COMMENT '单位名称',
                                         schedule_score int DEFAULT NULL COMMENT '进度分',
                                         quality_score int DEFAULT NULL COMMENT '质量分',
                                         safety_score int DEFAULT NULL COMMENT '安全分',
                                         construction_score int DEFAULT NULL COMMENT '文明施工分',
                                         total_score int DEFAULT NULL COMMENT '总分',
                                         PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格评比单位表';

-- ----------------------------
-- Table structure for grid_area_img
-- ----------------------------
DROP TABLE IF EXISTS grid_area_img;
CREATE TABLE grid_area_img (
                                 id int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 img_url varchar(1000) DEFAULT NULL COMMENT '图片地址',
                                 tenant_id varchar(32) DEFAULT NULL COMMENT '租户id',
                                 create_time datetime DEFAULT NULL COMMENT '创建时间',
                                 modify_time datetime DEFAULT NULL COMMENT '修改时间',
                                 creator_id varchar(32) DEFAULT NULL COMMENT '创建人id',
                                 modify_id varchar(32) DEFAULT NULL COMMENT '修改人id',
                                 PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格分区图';

-- ----------------------------
-- Table structure for grid_file
-- ----------------------------
DROP TABLE IF EXISTS grid_file;
CREATE TABLE grid_file (
                             id int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                             bus_id bigint NOT NULL COMMENT '业务id',
                             bus_type tinyint NOT NULL COMMENT '1:日报 2:周报',
                             bus_sub_type tinyint NOT NULL DEFAULT '0' COMMENT '业务子类型',
                             bus_sub_content varchar(255) DEFAULT NULL COMMENT '业务子内容（自定义）',
                             file_url varchar(256) DEFAULT '' COMMENT '文件url',
                             file_name varchar(256) DEFAULT '' COMMENT '文件名称',
                             PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格上传文件';

-- ----------------------------
-- Table structure for grid_info
-- ----------------------------
DROP TABLE IF EXISTS grid_info;
CREATE TABLE grid_info (
                             id int NOT NULL AUTO_INCREMENT COMMENT '主键',
                             parent_id int DEFAULT NULL COMMENT '上级ID(作用于三级网格)',
                             grid_name varchar(36) DEFAULT NULL COMMENT '网格名称',
                             grid_level int NOT NULL DEFAULT '1' COMMENT '网格层级',
                             qr_code varchar(22) DEFAULT NULL COMMENT '二维码',
                             tenant_id varchar(32) DEFAULT NULL COMMENT '租户id',
                             create_time datetime DEFAULT NULL COMMENT '创建时间',
                             modify_time datetime DEFAULT NULL COMMENT '修改时间',
                             creator_id varchar(32) DEFAULT NULL COMMENT '创建人id',
                             modify_id varchar(32) DEFAULT NULL COMMENT '修改人id',
                             delete_state int NOT NULL DEFAULT '1' COMMENT '删除标记 1未删除 0已删除',
                             PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格信息表';

-- ----------------------------
-- Table structure for grid_qr_code
-- ----------------------------
DROP TABLE IF EXISTS grid_qr_code;
CREATE TABLE grid_qr_code (
                                id int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                code varchar(100) NOT NULL COMMENT '二维码编码',
                                code_status int NOT NULL COMMENT '状态 0:空闲 1:在用 2:异常 3:作废',
                                image_url varchar(255) DEFAULT NULL COMMENT '图片地址',
                                grid_id varchar(255) DEFAULT NULL COMMENT '网格id',
                                grid_name varchar(255) DEFAULT NULL COMMENT '网格名称',
                                create_time datetime DEFAULT NULL COMMENT '创建时间',
                                modify_time datetime DEFAULT NULL COMMENT '更新时间',
                                tenant_id varchar(32) DEFAULT NULL COMMENT '租户id',
                                creator varchar(32) DEFAULT NULL COMMENT '创建人id',
                                modifier varchar(32) DEFAULT NULL COMMENT '修改人id',
                                PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格二维码管理';

-- ----------------------------
-- Table structure for grid_qr_code_record
-- ----------------------------
DROP TABLE IF EXISTS grid_qr_code_record;
CREATE TABLE grid_qr_code_record (
                                       id int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       code varchar(100) NOT NULL COMMENT '二维码编码',
                                       start_time datetime NOT NULL COMMENT '开始时间',
                                       end_time datetime DEFAULT NULL COMMENT '结束时间',
                                       grid_id varchar(255) DEFAULT NULL COMMENT '网格id',
                                       grid_name varchar(255) DEFAULT NULL COMMENT '网格名称',
                                       status int NOT NULL COMMENT '状态：0 开启使用 1：使用中 2：历史',
                                       PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='二网格二维码使用记录';

-- ----------------------------
-- Table structure for grid_report_day
-- ----------------------------
DROP TABLE IF EXISTS grid_report_day;
CREATE TABLE grid_report_day (
                                   id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   report_name varchar(32) NOT NULL DEFAULT '' COMMENT '日报名称',
                                   submit_deadline datetime DEFAULT NULL COMMENT '填报截止时间',
                                   requires_submit_num int NOT NULL DEFAULT '0' COMMENT '需填报人员数量',
                                   already_submit_num int NOT NULL DEFAULT '0' COMMENT '已填报人员数量',
                                   tenant_id varchar(32) DEFAULT NULL COMMENT '租户id',
                                   creator_id varchar(32) DEFAULT NULL COMMENT '创建人id',
                                   creator varchar(32) DEFAULT '' COMMENT '创建人',
                                   create_time datetime DEFAULT NULL COMMENT '创建时间',
                                   modify_id varchar(32) DEFAULT NULL COMMENT '修改人id',
                                   modifier varchar(32) DEFAULT '' COMMENT '修改人',
                                   modify_time datetime DEFAULT NULL COMMENT '修改时间',
                                   delete_state int NOT NULL DEFAULT '1' COMMENT '删除标记 1未删除 0已删除',
                                   PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格日报表';

-- ----------------------------
-- Table structure for grid_report_day_detail
-- ----------------------------
DROP TABLE IF EXISTS grid_report_day_detail;
CREATE TABLE grid_report_day_detail (
                                          id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          report_id bigint NOT NULL COMMENT '日报id',
                                          report_user_id bigint NOT NULL COMMENT '日报人员指定表id',
                                          user_id varchar(32) NOT NULL COMMENT '用户id',
                                          grid_hierarchy varchar(32) DEFAULT '' COMMENT '网格层级',
                                          grid_num int DEFAULT '0' COMMENT '网格数量（个）',
                                          build_grid_num int DEFAULT '0' COMMENT '当日施工网格数（个）',
                                          worker_num int DEFAULT '0' COMMENT '当日作业人数（个）',
                                          due_safety_officer_num int DEFAULT NULL COMMENT '安全员应到数（人）',
                                          act_safety_officer_num int DEFAULT NULL COMMENT '安全员实到数（人）',
                                          operation_team_num int DEFAULT NULL COMMENT '当日共有作业班组（个）',
                                          operation_caller_num int DEFAULT NULL COMMENT '开展班前喊话（人）',
                                          operation_call_refer_num int DEFAULT NULL COMMENT '班前喊话涉及人数（人）',
                                          check_general_hazard int DEFAULT '0' COMMENT '当日排查一般隐患（起）',
                                          change_general_hazard int DEFAULT '0' COMMENT '一般隐患整改（起）',
                                          check_great_hazard int DEFAULT '0' COMMENT '当日排查重大隐患（起）',
                                          change_great_hazard int DEFAULT '0' COMMENT '重大隐患整改（起）',
                                          personage_report_hazard int DEFAULT NULL COMMENT '个人举报隐患（项）',
                                          penalty_amount decimal(10,2) DEFAULT NULL COMMENT '违规问题罚款（元）',
                                          change_investment_amount decimal(10,2) DEFAULT '0.00' COMMENT '当日整改投入（万元）',
                                          reward_individuals_amount decimal(10,2) DEFAULT '0.00' COMMENT '当日奖励个人（元）',
                                          emergency_situation text COMMENT '当日突发事件情况',
                                          tenant_id varchar(32) DEFAULT NULL COMMENT '租户id、项目id',
                                          creator_id varchar(32) DEFAULT NULL COMMENT '创建人id',
                                          creator varchar(32) DEFAULT '' COMMENT '创建人',
                                          create_time datetime DEFAULT NULL COMMENT '创建时间',
                                          modify_id varchar(32) DEFAULT NULL COMMENT '修改人id',
                                          modifier varchar(32) DEFAULT '' COMMENT '修改人',
                                          modify_time datetime DEFAULT NULL COMMENT '修改时间',
                                          delete_state tinyint NOT NULL DEFAULT '1' COMMENT '删除标记 1未删除 0已删除',
                                          PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格日报填写详情';

-- ----------------------------
-- Table structure for grid_report_day_user
-- ----------------------------
DROP TABLE IF EXISTS grid_report_day_user;
CREATE TABLE grid_report_day_user (
                                        id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        report_id bigint NOT NULL COMMENT '日报id',
                                        grid_id bigint NOT NULL COMMENT '网格id',
                                        grid_role_name varchar(32) NOT NULL COMMENT '角色名称',
                                        user_id varchar(32) NOT NULL COMMENT '用户id',
                                        submit_time datetime DEFAULT NULL COMMENT '提交时间',
                                        sort int DEFAULT '0' COMMENT '排序',
                                        tenant_id varchar(32) DEFAULT NULL COMMENT '租户id、项目id',
                                        creator_id varchar(32) DEFAULT NULL COMMENT '创建人id',
                                        creator varchar(32) DEFAULT '' COMMENT '创建人',
                                        create_time datetime DEFAULT NULL COMMENT '创建时间',
                                        modify_id varchar(32) DEFAULT NULL COMMENT '修改人id',
                                        modifier varchar(32) DEFAULT '' COMMENT '修改人',
                                        modify_time datetime DEFAULT NULL COMMENT '修改时间',
                                        delete_state tinyint NOT NULL DEFAULT '1' COMMENT '删除标记 1未删除 0已删除',
                                        PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格日报人员指定表';

-- ----------------------------
-- Table structure for grid_report_week
-- ----------------------------
DROP TABLE IF EXISTS grid_report_week;
CREATE TABLE grid_report_week (
                                    id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    report_name varchar(32) NOT NULL DEFAULT '' COMMENT '周报名称',
                                    week_start datetime DEFAULT NULL COMMENT '一周的开始时间',
                                    week_end datetime DEFAULT NULL COMMENT '一周的结束时间',
                                    submit_deadline datetime DEFAULT NULL COMMENT '填报截止时间',
                                    requires_submit_num int NOT NULL DEFAULT '0' COMMENT '需填报人员数量',
                                    already_submit_num int NOT NULL DEFAULT '0' COMMENT '已填报人员数量',
                                    tenant_id varchar(32) DEFAULT NULL COMMENT '租户id',
                                    creator_id varchar(32) DEFAULT NULL COMMENT '创建人id',
                                    creator varchar(32) DEFAULT '' COMMENT '创建人',
                                    create_time datetime DEFAULT NULL COMMENT '创建时间',
                                    modify_id varchar(32) DEFAULT NULL COMMENT '修改人id',
                                    modifier varchar(32) DEFAULT '' COMMENT '修改人',
                                    modify_time datetime DEFAULT NULL COMMENT '修改时间',
                                    delete_state int NOT NULL DEFAULT '1' COMMENT '删除标记 1未删除 0已删除',
                                    PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格周报表';

-- ----------------------------
-- Table structure for grid_report_week_detail
-- ----------------------------
DROP TABLE IF EXISTS grid_report_week_detail;
CREATE TABLE grid_report_week_detail (
                                           id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           report_id bigint NOT NULL COMMENT '周报id',
                                           report_user_id bigint NOT NULL COMMENT '周报人员指定表id',
                                           user_id varchar(32) NOT NULL COMMENT '用户id',
                                           special_content_status text COMMENT '本周专项整治内容及开展情况',
                                           next_week_special_plan text COMMENT '下周拟开展专项整治内容及开展计划',
                                           other_issues_report text COMMENT '其他需要上报事项',
                                           tenant_id varchar(32) DEFAULT NULL COMMENT '租户id、项目id',
                                           creator_id varchar(32) DEFAULT NULL COMMENT '创建人id',
                                           creator varchar(32) DEFAULT '' COMMENT '创建人',
                                           create_time datetime DEFAULT NULL COMMENT '创建时间',
                                           modify_id varchar(32) DEFAULT NULL COMMENT '修改人id',
                                           modifier varchar(32) DEFAULT '' COMMENT '修改人',
                                           modify_time datetime DEFAULT NULL COMMENT '修改时间',
                                           delete_state tinyint NOT NULL DEFAULT '1' COMMENT '删除标记 1未删除 0已删除',
                                           PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格周 报填写详情';

-- ----------------------------
-- Table structure for grid_report_week_user
-- ----------------------------
DROP TABLE IF EXISTS grid_report_week_user;
CREATE TABLE grid_report_week_user (
                                         id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         report_id bigint NOT NULL COMMENT '周报id',
                                         user_id varchar(32) NOT NULL COMMENT '用户id',
                                         submit_time datetime DEFAULT NULL COMMENT '提交时间',
                                         sort int DEFAULT '0' COMMENT '排序',
                                         tenant_id varchar(32) DEFAULT NULL COMMENT '租户id、项目id',
                                         creator_id varchar(32) DEFAULT NULL COMMENT '创建人id',
                                         creator varchar(32) DEFAULT '' COMMENT '创建人',
                                         create_time datetime DEFAULT NULL COMMENT '创建时间',
                                         modify_id varchar(32) DEFAULT NULL COMMENT '修改人id',
                                         modifier varchar(32) DEFAULT '' COMMENT '修改人',
                                         modify_time datetime DEFAULT NULL COMMENT '修改时间',
                                         delete_state tinyint NOT NULL DEFAULT '1' COMMENT '删除标记 1未删除 0已删除',
                                         PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格周报人员指定表';

-- ----------------------------
-- Table structure for grid_user
-- ----------------------------
DROP TABLE IF EXISTS grid_user;
CREATE TABLE grid_user (
                             id int NOT NULL AUTO_INCREMENT COMMENT '主键',
                             grid_id int NOT NULL COMMENT '网格id',
                             grid_role_name varchar(32) NOT NULL COMMENT '角色名称',
                             user_id varchar(32) DEFAULT NULL COMMENT '用户id',
                             tenant_id varchar(32) DEFAULT NULL COMMENT '租户id',
                             PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网格用户表';

INSERT INTO grid_info (id, parent_id, grid_name, grid_level, qr_code, tenant_id, create_time, modify_time, creator_id, modify_id, delete_state) VALUES (1, 0, '网格监督人', 1, NULL, 'f18d90762c54af9bf8d7e7b2bc3fd110', NULL, NULL, NULL, NULL, 1);
INSERT INTO grid_info (id, parent_id, grid_name, grid_level, qr_code, tenant_id, create_time, modify_time, creator_id, modify_id, delete_state) VALUES (2, 0, '网格监督人', 1, NULL, 'f18d90762c54af9bf8d7e7b2bc3fd110', NULL, NULL, NULL, NULL, 1);
INSERT INTO grid_info (id, parent_id, grid_name, grid_level, qr_code, tenant_id, create_time, modify_time, creator_id, modify_id, delete_state) VALUES (3, 0, '网格负责人', 1, NULL, 'f18d90762c54af9bf8d7e7b2bc3fd110', NULL, NULL, NULL, NULL, 1);


INSERT INTO grid_user (id, grid_id, grid_role_name, user_id, tenant_id) VALUES (1, 1, '建设单位负责人', NULL, 'f18d90762c54af9bf8d7e7b2bc3fd110');
INSERT INTO grid_user (id, grid_id, grid_role_name, user_id, tenant_id) VALUES (2, 2, '监理单位总监', NULL, 'f18d90762c54af9bf8d7e7b2bc3fd110');
INSERT INTO grid_user (id, grid_id, grid_role_name, user_id, tenant_id) VALUES (3, 3, '项目经理', NULL, 'f18d90762c54af9bf8d7e7b2bc3fd110');

-- ----------------------------
-- Table structure for bim_task_correlation
-- ----------------------------
DROP TABLE IF EXISTS `bim_task_correlation`;
CREATE TABLE `bim_task_correlation`  (
                                         `task_full_name` varchar(255) NOT NULL COMMENT '任务全称',
                                         `component_id` varchar(50) NULL DEFAULT NULL COMMENT '组件id',
                                         `type` varchar(255) NULL DEFAULT NULL COMMENT '前端字段',
                                         `title` varchar(255) NULL DEFAULT NULL COMMENT '前端字段',
                                         `tenant_id` varchar(50) NULL DEFAULT NULL COMMENT '租客id',
                                         PRIMARY KEY (`task_full_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'bim项目任务（计划）关联表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for bim_project_import_record
-- ----------------------------
DROP TABLE IF EXISTS `bim_project_import_record`;
CREATE TABLE `bim_project_import_record`  (
                                              `id` varchar(32) NOT NULL COMMENT '项目导入记录id',
                                              `project_name` varchar(255) NULL DEFAULT NULL COMMENT '项目名称',
                                              `import_date` datetime NULL DEFAULT NULL COMMENT '导入时间',
                                              `tenant_id` varchar(32) NULL DEFAULT NULL COMMENT '租户id',
                                              `creator_id` varchar(32) NULL DEFAULT NULL COMMENT '创建人id',
                                              `creator` varchar(32) NULL DEFAULT NULL COMMENT '创建人',
                                              `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                              `modify_id` varchar(32) NULL DEFAULT NULL COMMENT '修改人id',
                                              `modifier` varchar(32) NULL DEFAULT NULL COMMENT '修改人',
                                              `modify_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                              `delete_state` int NULL DEFAULT 1 COMMENT '删除标记 1未删除 0已删除',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'bim项目导入记录表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for bim_task
-- ----------------------------
DROP TABLE IF EXISTS `bim_task`;
CREATE TABLE `bim_task`  (
                             `id` varchar(32) NOT NULL COMMENT '主键id',
                             `import_record_id` varchar(32) NULL DEFAULT NULL COMMENT '项目导入记录id',
                             `task_id` int NULL DEFAULT NULL COMMENT '任务Id',
                             `task_pid` int NULL DEFAULT NULL COMMENT '上级任务Id',
                             `level` int NULL DEFAULT NULL COMMENT '结构层级',
                             `task_name` varchar(255) NULL DEFAULT NULL COMMENT '任务名称',
                             `task_full_name` varchar(500) NULL DEFAULT NULL COMMENT '任务全称',
                             `duration` varchar(255) NULL DEFAULT NULL COMMENT '工期',
                             `start_date` datetime NULL DEFAULT NULL COMMENT '开始时间',
                             `end_date` datetime NULL DEFAULT NULL COMMENT '结束时间',
                             `tenant_id` varchar(32) NULL DEFAULT NULL COMMENT '租户id',
                             `creator_id` varchar(32) NULL DEFAULT NULL COMMENT '创建人id',
                             `creator` varchar(32) NULL DEFAULT NULL COMMENT '创建人',
                             `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                             `modify_id` varchar(32) NULL DEFAULT NULL COMMENT '修改人id',
                             `modifier` varchar(32) NULL DEFAULT NULL COMMENT '修改人',
                             `modify_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                             `delete_state` int NULL DEFAULT 1 COMMENT '删除标记 1未删除 0已删除',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'bim项目任务（计划）表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for bim_server_config
-- ----------------------------
DROP TABLE IF EXISTS `bim_server_config`;
CREATE TABLE `bim_server_config`  (
                                      `id` varchar(32) NOT NULL COMMENT '配置id',
                                      `app_key` varchar(255) NULL DEFAULT NULL COMMENT 'app_key',
                                      `app_secret` varchar(255) NULL DEFAULT NULL COMMENT 'app_secret',
                                      `file_id` varchar(255) NULL DEFAULT NULL COMMENT 'file_id',
                                      `tenant_id` varchar(32) NULL DEFAULT NULL COMMENT '租户id',
                                      `creator_id` varchar(32) NULL DEFAULT NULL COMMENT '创建人id',
                                      `creator` varchar(32) NULL DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                      `modify_id` varchar(32) NULL DEFAULT NULL COMMENT '修改人id',
                                      `modifier` varchar(32) NULL DEFAULT NULL COMMENT '修改人',
                                      `modify_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'bim服务配置表' ROW_FORMAT = Dynamic;



INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('967', null, 'BIM管理', 'projectBimManage', 'BIM管理', 1, null, null, 967.00, 1, null, null, '1', '2024-03-28 09:33:33', null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('96701', '967', '进度计划管理', 'projectSchedule', '进度计划管理', 2, null, null, 967.01, 1, 'manage/projectSchedule', null, '1', '2024-03-28 09:33:33', null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('96702', '967', '计划关联', 'projectScheduleRelate', '计划关联', 2, null, null, 967.02, 1, 'manage/projectScheduleRelate', null, '1', '2024-03-28 09:33:33', null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('96703', '967', '大屏展示', 'projectBimScreen', '大屏展示', 2, null, null, 967.03, 1, 'manage/projectBimScreen', null, '1', '2024-03-28 09:33:33', null, null, null, null, 0);

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('980', null, '网格管理', 'gridManage', '网格管理', 1, null, null, 980.00, 1, 'grid', null, '1', null, null, '1016', '2021-06-25 16:46:41', null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('9801', '980', '网格化关系', 'gridInfo', '网格化关系', 2, null, null, 980.10, 1, 'manage/gridInfo', null, '1', null, null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('9802', '980', '片区评论', 'gridAppraise', '片区评论', 2, null, null, 980.20, 1, 'manage/gridAppraise', null, '1', null, null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('9803', '980', '日报周报管理', 'dayNewspaper', '网格化片日报/周报', 2, null, null, 980.30, 1, 'manage/dayNewspaper', null, '1', null, null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('9804', '980', '网格隐患反馈', 'feedback', '网格成员隐患反馈', 2, null, null, 980.40, 1, 'manage/feedback', null, '1', null, null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('9805', '980', '网格二维码', 'gridQrCode', '网格二维码', 2, null, null, 980.50, 1, 'manage/gridQrCode', null, '1', null, null, null, null, null, 0);


INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('660963', '660000', '网格化管理', 'girdManage', '网格化管理', 1, null, null, 660963.00, 1, null, null, '1', '2024-03-28 09:33:38', null, null, null, null, 1);




SET FOREIGN_KEY_CHECKS = 1;
