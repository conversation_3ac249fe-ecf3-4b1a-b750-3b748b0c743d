/*
 * 巡检管理相关sql
 */
DROP TABLE IF EXISTS child_inspection_point;
CREATE TABLE child_inspection_point(
    `id` VARCHAR(100) NOT NULL  COMMENT '主键' ,
    `inspection_point_code` VARCHAR(100)   COMMENT '任务点编号' ,
    `inspection_point_name` VARCHAR(255)   COMMENT '任务点名称' ,
    `description` VARCHAR(255)   COMMENT '描述' ,
    `need_photograph` INT NOT NULL  COMMENT '需拍照，0-不需要，1-需要' ,
    `effect` INT NOT NULL  COMMENT '是否立即生效，0-立即生效，1-次日生效' ,
    `qr_code_id` VARCHAR(64)   COMMENT '二维码id' ,
    `longitude` VARCHAR(32)   COMMENT '经度' ,
    `latitude` VARCHAR(32)   COMMENT '纬度' ,
    `status` INT NOT NULL  COMMENT '状态 0-待巡检，1-正常，2-需整改，3-已整改' ,
    `inspection_point_status` INT NOT NULL  COMMENT '巡检点状态 0：关闭，1：开启' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    `point_image` VARCHAR(255)   COMMENT '图片地址' ,
    `inspection_count` INT   COMMENT '巡检次数' ,
    PRIMARY KEY (id)
)  COMMENT = '巡检管理-巡检点';

DROP TABLE IF EXISTS child_inspection_qr_code;
CREATE TABLE child_inspection_qr_code(
    `id` VARCHAR(100) NOT NULL  COMMENT '主键' ,
    `code` VARCHAR(100) NOT NULL  COMMENT '二维码编码' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    `image_url` VARCHAR(255)   COMMENT '图片地址' ,
    `inspection_point_id` VARCHAR(255)   COMMENT '巡检点id' ,
    `inspection_point_name` VARCHAR(255)   COMMENT '巡检点名称' ,
    `status` INT NOT NULL  COMMENT '状态 0:空闲， 1:在用，2:异常 ,3:作废' ,
    PRIMARY KEY (id)
)  COMMENT = '巡检管理-二维码管理';

DROP TABLE IF EXISTS child_inspection_record;
CREATE TABLE child_inspection_record(
    `id` VARCHAR(100) NOT NULL  COMMENT '主键' ,
    `task_id` VARCHAR(100)   COMMENT '任务ID' ,
    `point_id` VARCHAR(64)   COMMENT '巡检点编号' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    `status` INT   COMMENT '1-正常，2-需整改，3-已整改' ,
    `user_id` VARCHAR(32)   COMMENT '巡检人id' ,
    `assign_user_id` VARCHAR(32)   COMMENT '指派人员id' ,
    `hidden_danger_id` VARCHAR(64)   COMMENT '隐患id' ,
    `type` INT   COMMENT '0:其他填报，1：巡检人填报' ,
    `is_del` INT   COMMENT '0 ：正常，1：删除' ,
    PRIMARY KEY (id)
)  COMMENT = '巡检管理-巡检记录';

DROP TABLE IF EXISTS child_inspection_task;
CREATE TABLE child_inspection_task(
    `id` VARCHAR(100) NOT NULL  COMMENT '主键' ,
    `inspection_name` VARCHAR(100) NOT NULL  COMMENT '任务名称' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    `inspection_time` VARCHAR(200)   COMMENT '巡检时间' ,
    `start_time` DATETIME   COMMENT '开始时间' ,
    `end_time` DATETIME   COMMENT '结束时间' ,
    `duration` VARCHAR(255)   COMMENT '持续时长' ,
    `inspection_point` VARCHAR(255)   COMMENT '巡检点数量' ,
    `person_id` VARCHAR(255)   COMMENT '巡检人员' ,
    `person_name` VARCHAR(255)   COMMENT '巡检人员' ,
    `schedule` INT   COMMENT '进度 （取整四舍五入）' ,
    `status` INT   COMMENT '状态：0 未开始，1 进行中，2 完成' ,
    PRIMARY KEY (id)
)  COMMENT = '巡检管理-巡检任务';

DROP TABLE IF EXISTS child_qr_code_record;
CREATE TABLE child_qr_code_record(
    `id` INT AUTO_INCREMENT COMMENT '主键' ,
    `code` VARCHAR(100) NOT NULL  COMMENT '二维码编码' ,
    `start_time` DATETIME NOT NULL  COMMENT '开始时间' ,
    `end_time` DATETIME   COMMENT '结束时间' ,
    `point_id` VARCHAR(100)   COMMENT '巡检点ID' ,
    `point_name` VARCHAR(100)   COMMENT '巡检点名称' ,
    `status` INT NOT NULL  COMMENT '状态：0 开启使用，1：使用中 ,2：历史' ,
    PRIMARY KEY (id)
)  COMMENT = '二维码使用记录';

DROP TABLE IF EXISTS child_rectification_record;
CREATE TABLE child_rectification_record(
    `id` VARCHAR(100) NOT NULL  COMMENT '主键' ,
    `record_id` VARCHAR(100)   COMMENT '巡检记录ID' ,
    `create_time` DATETIME   COMMENT '创建时间' ,
    `modify_time` DATETIME   COMMENT '更新时间' ,
    `tenant_id` VARCHAR(32)   COMMENT '租户id' ,
    `creator` VARCHAR(32)   COMMENT '创建人id' ,
    `modifier` VARCHAR(32)   COMMENT '修改人id' ,
    `feedback_image_url` VARCHAR(255)   COMMENT '反馈图片' ,
    `feedback_description` VARCHAR(255)   COMMENT '反馈描述' ,
    `handling_user_id` VARCHAR(255)   COMMENT '处置人员id' ,
    `handling_user_name` VARCHAR(255)   COMMENT '处置人员名称' ,
    `handling_time` DATETIME   COMMENT '处置时间' ,
    PRIMARY KEY (id)
)  COMMENT = '巡检管理-整改记录表';


DROP TABLE IF EXISTS child_task_point;
CREATE TABLE child_task_point(
    `id` INT AUTO_INCREMENT COMMENT '' ,
    `point_id` VARCHAR(64) NOT NULL  COMMENT '' ,
    `task_id` VARCHAR(64) NOT NULL  COMMENT '' ,
    `create_time` DATETIME NOT NULL  COMMENT '' ,
    `status` INT NOT NULL  COMMENT '状态 0-待巡检，1-正常，2-异常' ,
    PRIMARY KEY (id)
)  COMMENT = '任务-巡检点关联表';


/*---------- PC菜单 ----------*/
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('115', null, '巡检管理', 'inspectionManage', '巡检管理', 1, null, null, 5.01, 1, null, null, '1', '2024-04-03 09:33:33', null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('11510', '115', '巡检任务管理', 'inspectionWork', null, 2, null, null, 115.10, 1, 'manage/inspectionWork', null, '1', '2022-04-11 13:49:03', null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('11511', '115', '巡检点管理', 'inspectionPoint', null, 2, null, null, 115.11, 1, 'manage/inspectionPoint', null, '1', '2022-04-11 13:49:03', null, null, null, null, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('11512', '115', '二维码管理', 'qrCode', null, 2, null, null, 115.12, 1, 'manage/qrCode', null, '1', '2022-04-11 13:49:03', null, null, null, null, 0);

/*---------- 小程序菜单 ----------*/
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('660962', '660000', '巡检任务', 'inspectionTask', '巡检任务', 1, null, null, 660962.00, 1, null, null, '1', '2024-03-28 09:33:38', null, null, null, null, 1);
