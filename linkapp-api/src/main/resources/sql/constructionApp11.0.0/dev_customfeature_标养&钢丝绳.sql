DROP TABLE IF EXISTS sensor_device;
CREATE TABLE sensor_device(
    `id` INT AUTO_INCREMENT COMMENT '' ,
    `device_id` VARCHAR(32)   COMMENT '设备ID' ,
    `device_code` VARCHAR(32)   COMMENT '设备code' ,
    `device_name` VARCHAR(100)   COMMENT '设备名称' ,
    `device_type_id` varchar(32) COMMENT '设备类型ID',
    `device_type_name` varchar(100) COMMENT '设备类型名称',
    `data` VARCHAR(1000)   COMMENT '' ,
    `max` DECIMAL(10,2)   COMMENT '最大值' ,
    `min` DECIMAL(10,2)   COMMENT '最小值' ,
    `unit` VARCHAR(100)   COMMENT '单位' ,
    `rule` DECIMAL(10,2)   COMMENT '处理规则' ,
    `decimals` INT   COMMENT '小数位' ,
    `tenant_id` varchar(32) COMMENT '租户id',
    `device_status` char(1) DEFAULT '1' COMMENT '0：删除；1：有效',
    PRIMARY KEY (id)
)  COMMENT = '传感设备表' collate = utf8mb4_general_ci;
CREATE UNIQUE INDEX unique_sensor_device_id ON sensor_device(device_id);

DROP TABLE IF EXISTS sensor_device_data;
CREATE TABLE sensor_device_data(
    `id` BIGINT NOT NULL  COMMENT '' ,
    `device_id` VARCHAR(32) NOT NULL DEFAULT '0' COMMENT '' ,
    `data` TEXT   COMMENT '' ,
    `time` DATETIME   COMMENT '' ,
    `result` VARCHAR(50)   COMMENT '' ,
    `level` INT   COMMENT '' ,
    `remark` VARCHAR(100)   COMMENT '' ,
    PRIMARY KEY (id)
)  COMMENT = '设备数据表' collate = utf8mb4_general_ci;


/*---------- PC菜单 ----------*/
<<<<<<<< HEAD:linkapp-api/src/main/resources/sql/constructionApp11.0.0/dev_customfeature_20240417.sql
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) VALUES ('670', '60', '宿舍管理', 'dormitoryManage', '宿舍管理', 2, null, null, 67.00, 1, 'manage/dormitoryManage', null, '1', now(), null, null, null, null, 0);
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('12016', '12013', '钢丝绳监测', 'herbariumManage', '钢丝绳监测', 3, NULL, NULL, 12.22, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
========
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('12016', '12013', '钢丝绳监测', 'herbariumManage', null, 3, null, null, 12.22, 2, null, null, '1', '2024-03-28 09:33:33', null, null, null, null, 0);
>>>>>>>> wjzn/dev:linkapp-api/src/main/resources/sql/constructionApp11.0.0/dev_customfeature_标养&钢丝绳.sql


/*---------- 小程序菜单 ----------*/
<<<<<<<< HEAD:linkapp-api/src/main/resources/sql/constructionApp11.0.0/dev_customfeature_20240417.sql
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) VALUES ('6600039', '660003', '宿舍管理', 'dormitoryManage', '宿舍管理', 1, null, null, 660003.90, 1, null, null, '1', now(), null, null, null, null, 1);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) VALUES ('6600070', '660006', '标养室', 'herbariumManage', '标养室', 1, null, null, 660007.10, 1, null, null, '1', now(), null, null, null, null, 1);
========
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) 
    VALUES ('660961', '660000', '标养室', 'herbariumManage', '标养室', 1, null, null, 660961.00, 1, null, null, '1', '2024-03-28 09:33:36', null, null, null, null, 1);
>>>>>>>> wjzn/dev:linkapp-api/src/main/resources/sql/constructionApp11.0.0/dev_customfeature_标养&钢丝绳.sql


/*---------- 设备相关SQL ----------*/
/*---- 设备类型 ----*/
INSERT INTO linkapp_device_type (id, parent_id, name, ico_path, description, level, search_code, remark, company_id, create_time, creator, modifier, modify_time, delete_state) 
    VALUES ('25c241d9ecb511ee9f580242ac110009', null, '水位传感器', null, null, null, null, null, null, null, null, null, null, DEFAULT);
INSERT INTO linkapp_device_type (id, parent_id, name, ico_path, description, level, search_code, remark, company_id, create_time, creator, modifier, modify_time, delete_state) 
    VALUES ('280e333aecb511ee9f580242ac110009', null, '温度传感器', null, null, null, null, null, null, null, null, null, null, DEFAULT);
INSERT INTO linkapp_device_type (id, parent_id, name, ico_path, description, level, search_code, remark, company_id, create_time, creator, modifier, modify_time, delete_state) 
    VALUES ('2d5219c5ecb511ee9f580242ac110009', null, '湿度传感器', null, null, null, null, null, null, null, null, null, null, DEFAULT);
INSERT INTO linkapp_device_type (id, parent_id, name, ico_path, description, level, search_code, remark, company_id, create_time, creator, modifier, modify_time, delete_state) 
    VALUES ('302902c4ecb511ee9f580242ac110009', null, '微电流传感器', null, null, null, null, null, null, null, null, null, null, DEFAULT);

INSERT INTO linkapp_device_type (id, parent_id, name, ico_path, description, level, search_code, remark, company_id, create_time, creator, modifier, modify_time, delete_state) 
    VALUES ('235c30efecb511ee9f580242ac110009', null, '钢丝绳监测传感器', null, null, null, null, null, null, null, null, null, null, DEFAULT);

/*---- 设备 ----*/
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('16a5f6b4ecb511ee9f580242ac110009', '25c241d9ecb511ee9f580242ac110009', '水位传感器', null, '水位传感器', 'SWJC0001', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('1973e766ecb511ee9f580242ac110009', '280e333aecb511ee9f580242ac110009', '温度传感器', null, '温度传感器', 'WDJC0001', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('1bd09c23ecb511ee9f580242ac110009', '2d5219c5ecb511ee9f580242ac110009', '湿度传感器', null, '湿度传感器', 'SDJC0001', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('1f93892decb511ee9f580242ac110009', '302902c4ecb511ee9f580242ac110009', '微电流传感器', null, '微电流传感器', 'DLJC0001', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);

INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('0f764cb3ecb511ee9f580242ac110009', '235c30efecb511ee9f580242ac110009', '钢丝绳监测传感器', null, '钢丝绳监测00560001', '00560001', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('0f764cb3ecb511ee9f580242ac120009', '235c30efecb511ee9f580242ac110009', '钢丝绳监测传感器', null, '钢丝绳监测00560002', '00560002', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('0f764cb3ecb511ee9f580242ac130009', '235c30efecb511ee9f580242ac110009', '钢丝绳监测传感器', null, '钢丝绳监测00560003', '00560003', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('0f764cb3ecb511ee9f580242ac140009', '235c30efecb511ee9f580242ac110009', '钢丝绳监测传感器', null, '钢丝绳监测00560004', '00560004', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('0f764cb3ecb511ee9f580242ac150009', '235c30efecb511ee9f580242ac110009', '钢丝绳监测传感器', null, '钢丝绳监测00560005', '00560005', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('0f764cb3ecb511ee9f580242ac160009', '235c30efecb511ee9f580242ac110009', '钢丝绳监测传感器', null, '钢丝绳监测00560006', '00560006', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);
INSERT INTO linkapp_device_unit (id, device_type_id, device_type_name, company_id, name, code, remark, icon, identification, device_life, repair_cycle, offline_time, create_time, creator, modifier, modify_time, delete_state, visualization_config, version, physics_model, linkapp_flag) 
    VALUES ('0f764cb3ecb511ee9f580242ac170009', '235c30efecb511ee9f580242ac110009', '钢丝绳监测传感器', null, '钢丝绳监测00560007', '00560007', null, null, null, null, null, null, null, null, null, null, 1, null, '1.0', '{}', null);


/*---- 项目设备相关SQL，需求根据实际情况----*/
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('26a5f6b4ecb511ee9f580242ac110009', 'SWJC0001', '水位传感器', 0, 1, 0, null, null, null, null, null, '16a5f6b4ecb511ee9f580242ac110009', null, null, null, null, null, null, null, null, '3124a647014a5f04c3391f0185c52f92', null, null, null, null, null, '9d2c8f774e900103f8535953f2e85663', 1, 2);
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('3973e766ecb511ee9f580242ac110009', 'WDJC0001', '温度传感器', 0, 1, 0, null, null, null, null, null, '1973e766ecb511ee9f580242ac110009', null, null, null, null, null, null, null, null, '3124a647014a5f04c3391f0185c52f92', null, null, null, null, null, '9d2c8f774e900103f8535953f2e85663', 1, 2);
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('4bd09c23ecb511ee9f580242ac110009', 'SDJC0001', '湿度传感器', 0, 1, 0, null, null, null, null, null, '1bd09c23ecb511ee9f580242ac110009', null, null, null, null, null, null, null, null, '3124a647014a5f04c3391f0185c52f92', null, null, null, null, null, '9d2c8f774e900103f8535953f2e85663', 1, 2);
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('5f93892decb511ee9f580242ac110009', 'DLJC0001', '微电流传感器', 0, 1, 0, null, null, null, null, null, '1f93892decb511ee9f580242ac110009', null, null, null, null, null, null, null, null, '3124a647014a5f04c3391f0185c52f92', null, null, null, null, null, '9d2c8f774e900103f8535953f2e85663', 1, 2);

INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('0f764cb3ecb511ee9f580242ac110009', '00560001', '钢丝绳监测(2号塔吊)', 0, 1, 0, null, null, null, null, null, '0f764cb3ecb511ee9f580242ac110009', null, null, null, null, null, null, null, null, '3124a647014a5f04c3391f0185c52f92', null, null, null, null, null, '9d2c8f774e900103f8535953f2e85663', 1, 2);
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('0f764cb3ecb511ee9f580242ac120009', '00560002', '钢丝绳监测(1号塔吊)', 0, 1, 0, null, null, null, null, null, '0f764cb3ecb511ee9f580242ac120009', null, null, null, null, null, null, null, null, '3124a647014a5f04c3391f0185c52f92', null, null, null, null, null, '9d2c8f774e900103f8535953f2e85663', 1, 2);
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('0f764cb3ecb511ee9f580242ac130009', '00560003', '钢丝绳监测(3号塔吊)', 0, 1, 0, null, null, null, null, null, '0f764cb3ecb511ee9f580242ac130009', null, null, null, null, null, null, null, null, '3124a647014a5f04c3391f0185c52f92', null, null, null, null, null, '9d2c8f774e900103f8535953f2e85663', 1, 2);
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('0f764cb3ecb511ee9f580242ac140009', '00560004', '钢丝绳监测(1号塔吊)', 0, 1, 0, null, null, null, null, null, '0f764cb3ecb511ee9f580242ac140009', null, null, null, null, null, null, null, null, '0241b2ec961a3a6be034318fa27c9035', null, null, null, null, null, '405cd2c8633ecbee1c5f61c4a87c9019', 1, 2);
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('0f764cb3ecb511ee9f580242ac150009', '00560005', '钢丝绳监测(3号塔吊)', 0, 1, 0, null, null, null, null, null, '0f764cb3ecb511ee9f580242ac150009', null, null, null, null, null, null, null, null, '0241b2ec961a3a6be034318fa27c9035', null, null, null, null, null, '405cd2c8633ecbee1c5f61c4a87c9019', 1, 2);
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('0f764cb3ecb511ee9f580242ac160009', '00560006', '钢丝绳监测(2号塔吊)', 0, 1, 0, null, null, null, null, null, '0f764cb3ecb511ee9f580242ac160009', null, null, null, null, null, null, null, null, '0241b2ec961a3a6be034318fa27c9035', null, null, null, null, null, '405cd2c8633ecbee1c5f61c4a87c9019', 1, 2);
INSERT INTO linkapp_device (id, code, name, status, delete_state, online_state, battery, remark, alarm_switch, company_id, project_id, device_unit_id, install_time, repair_time, next_repair_time, last_push_time, create_time, creator, modifier, modify_time, area_id, latitude, longitude, site, indoor_location, area_path, tenant_id, linkthing_delete, work_status) 
    VALUES ('0f764cb3ecb511ee9f580242ac170009', '00560007', '钢丝绳监测(4号塔吊)', 0, 1, 0, null, null, null, null, null, '0f764cb3ecb511ee9f580242ac170009', null, null, null, null, null, null, null, null, '0241b2ec961a3a6be034318fa27c9035', null, null, null, null, null, '405cd2c8633ecbee1c5f61c4a87c9019', 1, 2);


/*---- 设备物联后台配置 ----*/
/*--- 同步设备信息 ---*/
create or replace view v_sensor_device as
select device_type_id device_type,device_type_name device_type_name,id device_id,code device_code,name device_name
from linkapp_device_unit
where device_type_id in (
        '235c30efecb511ee9f580242ac110009'
        ,'25c241d9ecb511ee9f580242ac110009'
        ,'280e333aecb511ee9f580242ac110009'
        ,'2d5219c5ecb511ee9f580242ac110009'
        ,'302902c4ecb511ee9f580242ac110009'
)
;

create or replace view v_ins_sensor_device as
select du.device_id,
       du.device_code,
       du.device_name,
       du.device_type,
       du.device_type_name,
       (case device_type
           when '235c30efecb511ee9f580242ac110009' then '{"interval":"30秒","status":0,"status_remark":"非工作状态"}'
           when '302902c4ecb511ee9f580242ac110009' then '{"status":1,"status_remark":""}'
           else null end) data,
       (case device_type
           when '280e333aecb511ee9f580242ac110009' then 22.01
           else null end) max,
       (case device_type
           when '25c241d9ecb511ee9f580242ac110009' then 0.10
           when '280e333aecb511ee9f580242ac110009' then 17.99
           when '2d5219c5ecb511ee9f580242ac110009' then 95.00
           when '302902c4ecb511ee9f580242ac110009' then 0.00
           else null end) min,
       (case device_type
           when '235c30efecb511ee9f580242ac110009' then '%'
           when '25c241d9ecb511ee9f580242ac110009' then 'cm'
           when '280e333aecb511ee9f580242ac110009' then '°C'
           when '2d5219c5ecb511ee9f580242ac110009' then '%'
           else '' end) unit,
       (case device_type
           when '25c241d9ecb511ee9f580242ac110009' then 100
           else 1 end) rule,
       (case device_type
           when '235c30efecb511ee9f580242ac110009' then 2
           else 1 end) decimals
from v_sensor_device du
where 
    not exists(select 1 from sensor_device sd where sd.device_id = du.device_id)
;

insert into sensor_device (device_id, device_code, device_name, device_type_id, device_type_name, data, max, min, unit, rule, decimals)
select device_id, device_code, device_name, device_type, device_type_name,data, max, min, unit, rule, decimals from v_ins_sensor_device;
