-- ----------------
-- 插入前端环境监控页面
-- ----------------
SELECT * FROM linkapp_privilege WHERE length(id_) = (SELECT max(LENGTH(id_)) FROM linkapp_privilege) ORDER BY id_ DESC;
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_) values
(2035,NULL, '环境监测', 'environmentalMonitoring', NULL, 1, NULL, NULL, 49.10, 1, 'manage/environmentalMonitoring', NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);


-- ----------------
-- 创建设备关联模块表
-- ----------------
-- linkappdb.linkapp_device_model definition

CREATE TABLE `linkapp_device_model` (
  `auto_id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `code_` varchar(32) NOT NULL COMMENT '设备号',
  `model_id_` tinyint(4) NOT NULL COMMENT '模块id',
  `remark_` varchar(100) DEFAULT NULL COMMENT '预留字段备注',
  `tenant_id` varchar(32) NOT NULL,
  PRIMARY KEY (`auto_id_`),
  UNIQUE KEY `linkapp_device_model_un` (`code_`,`model_id_`,`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;

-- 添加标记移除字段
ALTER TABLE `linkapp_device_model`
ADD COLUMN  `is_remove_` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否从设备模块已移除 0未移除 1已移除';