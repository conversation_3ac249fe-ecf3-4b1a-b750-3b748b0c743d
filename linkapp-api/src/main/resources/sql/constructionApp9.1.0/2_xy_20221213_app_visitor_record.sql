-- 来访人员记录表
CREATE TABLE `app_visitor_record` (
`id` VARCHAR ( 32 ) NOT NULL COMMENT 'ID 主键',
`tenant_id_` VARCHAR ( 50 ) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
`name_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '姓名',
`company_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '单位',
`plate_number_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '车牌号',
`id_card_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '证件号码',
`phone_` VARCHAR ( 16 ) DEFAULT NULL COMMENT '电话',
`start_place_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '出发地',
`health_code_status_` INT ( 2 ) DEFAULT NULL COMMENT '健康码状态 0正常 1异常',
`twentyfour_test_status_` INT ( 2 ) DEFAULT NULL COMMENT '武汉市24小时内核酸结果 0正常 1异常',
`temperature_` DOUBLE DEFAULT NULL COMMENT '体温',
`in_time` datetime DEFAULT NULL COMMENT '进场时间',
`out_time` datetime DEFAULT NULL COMMENT '出场时间',
`status_` INT ( 2 ) DEFAULT NULL COMMENT '来访人员状态 0未出场 1已出场',
`creator_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '创建人id',
`create_time_` datetime DEFAULT NULL COMMENT '创建日期',
`modify_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '修改人id',
`modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
`remark_` text COMMENT '备注',
PRIMARY KEY ( `id` )
) COMMENT '来访人员记录';

-- 来访人员权限
-- web
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('611', '60', '来访记录', 'visitorRecord', NULL, 2, NULL, NULL, 61.10, 1, 'manage/visitorRecord', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('6111', '611', '登记出场', 'visitorRecord:out', NULL, 3, NULL, NULL, 6111.10, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);

-- app
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('6600035', '660003', '来访记录', 'visitorRecordApp', '来访记录', 1, NULL, NULL, 660003.50, 1, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 1);
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('66000351', '6600035', '登记出场', 'visitorRecordApp:out', NULL, 3, NULL, NULL, 660003.51, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 1);

