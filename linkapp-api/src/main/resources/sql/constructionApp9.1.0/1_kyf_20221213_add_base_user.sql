ALTER TABLE emp_user_base ADD `work_type_` varchar(32) DEFAULT NULL COMMENT '工种 字典管理';
ALTER TABLE emp_user_base ADD `company_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司id';

-- 设置工种和单位值
UPDATE emp_user_base a
JOIN app_user_project b on a.id = b.user_id_
JOIN app_group c on b.group_id_ = c.id
JOIN app_labor_company_project d on c.company_project_id_ = d.id
set a.work_type_ = b.work_type_, a.company_id_ = d.company_id_;

INSERT INTO `linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('6014', '601', '从人员库添加', 'personnel:addFromBase', NULL, 3, NULL, NULL, 6011.40, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);