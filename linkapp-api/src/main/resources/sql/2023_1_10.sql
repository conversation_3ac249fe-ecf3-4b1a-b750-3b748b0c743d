-- https://www.tapd.cn/47986890/bugtrace/bugs/view/1147986890001014551
alter table  app_danger_type modify column `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键';
alter table  app_danger_type modify column danger_type_id bigint(20) NOT NULL COMMENT '隐患类型id';
alter table  app_danger_type modify column parent_id bigint(20) NOT NULL COMMENT '父id';
alter table  app_danger modify column danger_type_id bigint(20) NOT NULL COMMENT '隐患类型id';



# show create table  app_danger_type;

# CREATE TABLE `app_danger_type` (
#                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
#                                    `parent_id` int(11) NOT NULL COMMENT '父id',
#                                    `danger_type_id` int(11) NOT NULL COMMENT '隐患类型id',
#                                    `full_id` varchar(64) NOT NULL COMMENT '原全称id',
#                                    `full_name` varchar(128) NOT NULL COMMENT '全名',
#                                    `name` varchar(128) NOT NULL COMMENT '名称',
#                                    `code` varchar(16) DEFAULT NULL,
#                                    `level` int(2) NOT NULL COMMENT '层级',
#                                    `project_id` varchar(32) DEFAULT NULL COMMENT '项目id',
#                                    `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
#                                    `last_push_time` datetime DEFAULT NULL COMMENT '最后同步时间',
#                                    PRIMARY KEY (`id`),
#                                    UNIQUE KEY `app_danger_type_un` (`danger_type_id`)
# ) ENGINE=InnoDB AUTO_INCREMENT=2677 DEFAULT CHARSET=utf8 COMMENT='隐患类型';

# show create table  app_danger;
#
# CREATE TABLE `app_danger` (
#                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
#                               `the_id` int(11) DEFAULT NULL COMMENT '原id有重复',
#                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
#                               `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
#                               `remark` varchar(200) DEFAULT NULL COMMENT '备注',
#                               `danger_type_id` int(11) NOT NULL COMMENT '隐患类型id',
#                               `full_id` varchar(64) NOT NULL COMMENT 'full',
#                               `full_name` varchar(128) NOT NULL COMMENT '全名',
#                               `content` varchar(128) NOT NULL COMMENT '内容',
#                               `code` varchar(16) DEFAULT NULL,
#                               `order_` int(2) DEFAULT NULL COMMENT '排序',
#                               `level` int(2) NOT NULL COMMENT '等级',
#                               `change_limit` int(2) NOT NULL,
#                               `delete_status` int(2) NOT NULL COMMENT '删除状态 0-已删除，1-存在',
#                               `record_status` int(2) NOT NULL COMMENT '状态 0-正常',
#                               `points` int(2) DEFAULT NULL,
#                               `fine` int(2) DEFAULT NULL,
#                               `push_period` int(2) DEFAULT NULL,
#                               `related` int(2) DEFAULT NULL COMMENT '是否关联规范 0-未关联',
#                               `identify` varchar(32) DEFAULT NULL,
#                               `project_id` varchar(32) DEFAULT NULL COMMENT '项目id',
#                               `last_push_time` datetime DEFAULT NULL COMMENT '最后同步时间',
#                               PRIMARY KEY (`id`)
# ) ENGINE=InnoDB AUTO_INCREMENT=69005173 DEFAULT CHARSET=utf8 COMMENT='隐患库'