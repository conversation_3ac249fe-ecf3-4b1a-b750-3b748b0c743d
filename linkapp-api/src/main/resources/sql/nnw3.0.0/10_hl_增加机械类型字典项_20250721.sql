INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( 4, '桥式起重机', '14', '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 14, 1, NULL, NULL, NULL, NULL, 0);

INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( 4, '臂架式起重机', '15', '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 15, 1, NULL, NULL, NULL, NULL, 0);

INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( 4, '缆索式起重机', '16', '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 16, 1, NULL, NULL, NULL, NULL, 0);

INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( 4, '桅杆式起重机', '17', '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 17, 1, NULL, NULL, NULL, NULL, 0);

INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( 4, '履带式起重机', '18', '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 18, 1, NULL, NULL, NULL, NULL, 0);
