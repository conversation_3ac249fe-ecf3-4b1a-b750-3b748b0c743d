/*
 Navicat Premium Data Transfer

 Source Server         : 涉铁工程V2
 Source Server Type    : MySQL
 Source Server Version : 50644
 Source Host           : *************:55051
 Source Schema         : linkappdb

 Target Server Type    : MySQL
 Target Server Version : 50644
 File Encoding         : 65001

 Date: 18/07/2025 10:40:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for rail_monitor_daily_report
-- ----------------------------
DROP TABLE IF EXISTS `rail_monitor_daily_report`;
CREATE TABLE `rail_monitor_daily_report`  (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                              `tenant_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目id',
                                              `upload_type` int(11) NULL DEFAULT NULL COMMENT '上传类型：0人工上传，1自动生成',
                                              `monitor_date` datetime(0) NULL DEFAULT NULL COMMENT '监测日期',
                                              `report_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日报名称',
                                              `repoort_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '附件url',
                                              `upload_uid` bigint(20) NULL DEFAULT NULL COMMENT '上传人',
                                              `upload_time` datetime(0) NULL DEFAULT NULL COMMENT '上传时间',
                                              `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                              `is_deleted` int(11) NULL DEFAULT NULL COMMENT '是否删除：0否1是',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '监测日报表' ROW_FORMAT = Compact;
