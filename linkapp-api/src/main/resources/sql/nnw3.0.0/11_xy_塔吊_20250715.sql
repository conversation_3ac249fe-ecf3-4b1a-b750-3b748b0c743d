-- 菜单
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99043', '9904', '塔吊安全监测', 't_tower_safety_monitor', '塔吊安全监测', 2, NULL, NULL, 9904.300, 1, 'manage/towerCraneSafety', NULL, '1', NULL, NULL, NULL, '2025-07-07 09:32:04', NULL, 0);
-- 新增表
-- 塔吊历史数据表
CREATE TABLE `rail_tower_crane_business_record`
(
    `id_`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`               varchar(64)    DEFAULT NULL COMMENT '租户id',
    `mechanical_id_`           varchar(64) NOT NULL COMMENT '机械id',
    `device_id_`               varchar(64) NOT NULL COMMENT '设备id',
    `device_code_`             varchar(64) NOT NULL COMMENT '设备code',
    `device_name_`             varchar(255)   DEFAULT NULL COMMENT '设备名称',
    `weight_`                  decimal(10, 2) DEFAULT NULL COMMENT '吊重 t',
    `alarm_weight_`            int(11) DEFAULT '0' COMMENT '吊重告警 0-正常，1-告警',
    `wind_speed_`              decimal(10, 2) DEFAULT NULL COMMENT '风速 m/s',
    `alarm_wind_speed_`        int(11) DEFAULT '0' COMMENT '风速告警 0-正常，1-告警',
    `range_`                   decimal(10, 2) DEFAULT NULL COMMENT '变幅 m',
    `rotation_`                decimal(10, 2) DEFAULT NULL COMMENT '回转角度 °',
    `slide_`                   decimal(10, 2) DEFAULT NULL COMMENT '溜钩距离 m',
    `alarm_slide_`             int(11) DEFAULT '0' COMMENT '溜钩距离告警 0-正常，1-告警',
    `torque_percentage_`       decimal(10, 2) DEFAULT NULL COMMENT '力矩比 %',
    `alarm_torque_percentage_` int(11) DEFAULT '0' COMMENT '力矩比告警 0-正常，1-告警',
    `horizontal_`              decimal(10, 2) DEFAULT NULL COMMENT '水平角度 °',
    `alarm_horizontal_`        int(11) DEFAULT '0' COMMENT '水平角度告警 0-正常，1-告警',
    `vertical_`                decimal(10, 2) DEFAULT NULL COMMENT '垂直角度 °',
    `alarm_vertical_`          int(11) DEFAULT '0' COMMENT '垂直角度告警 0-正常，1-告警',
    `depth_`                   decimal(10, 2) DEFAULT NULL COMMENT '吊钩下降深度 m',
    `height_`                  decimal(10, 2) DEFAULT NULL COMMENT '吊钩高度 m',
    `cycle_count_`             int(11) DEFAULT NULL COMMENT '工作循环次数',
    `cycle_status_`            int(11) DEFAULT '0' COMMENT '工作循环状态 0-停止，1-触发',
    `all_time_`                decimal(10, 2) DEFAULT NULL COMMENT '终端总工作时间',
    `create_id_`               bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`             datetime       DEFAULT NULL COMMENT '创建日期',
    `modify_id_`               bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`             datetime       DEFAULT NULL COMMENT '修改时间',
    `remark_`                  text COMMENT '备注',
    `alarm_state_`             int(11) DEFAULT '0' COMMENT '总告警状态 0-正常，1-告警',
    PRIMARY KEY (`id_`),
    KEY                        `idx_tenant_id_mechanical_id_device_id_create_time_` (`tenant_id_`,`mechanical_id_`,`device_id_`,`create_time_`)
) COMMENT='塔吊业务数据记录表';

-- 塔吊循环数据表
CREATE TABLE `rail_tower_crane_work_record`
(
    `id_`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `tenant_id_`       varchar(64)    DEFAULT NULL COMMENT '租户id',
    `mechanical_id_`   varchar(64) NOT NULL COMMENT '机械id',
    `device_id_`       varchar(64) NOT NULL COMMENT '设备id',
    `device_code_`     varchar(64) NOT NULL COMMENT '设备code',
    `device_name_`     varchar(255)   DEFAULT NULL COMMENT '设备名称',
    `start_time_`      datetime       DEFAULT NULL COMMENT '开始时间',
    `end_time_`        datetime       DEFAULT NULL COMMENT '结束时间',
    `max_wind_speed_`        decimal(10, 2) DEFAULT NULL COMMENT '最大风速 m/s',
    `max_depth_`       decimal(10, 2) DEFAULT NULL COMMENT '最大吊钩下降深度 m',
    `max_height_`      decimal(10, 2) DEFAULT NULL COMMENT '最大吊钩高度 m',
    `max_range_`     decimal(10, 2) DEFAULT NULL COMMENT '最大变幅 m',
    `max_elevation_`   decimal(10, 2) DEFAULT NULL COMMENT '最大仰角 °',
    `max_travel_`      decimal(10, 2) DEFAULT NULL COMMENT '最大行程 m',
    `max_weight_`        decimal(10, 2) DEFAULT NULL COMMENT '最大吊重 t',
    `max_torque_`      decimal(10, 2) DEFAULT NULL COMMENT '最大力矩 t·m',
    `max_torque_percentage_` decimal(10, 2) DEFAULT NULL COMMENT '最大力矩比 %',
    `max_rotation_`    decimal(10, 2) DEFAULT NULL COMMENT '最大回转 °',
    `runtime_`         decimal(10, 2) DEFAULT NULL COMMENT '本次工作时间 单位秒',
    `upload_time_`     datetime       DEFAULT NULL COMMENT '上报时间',
    `create_id_`       bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`     datetime       DEFAULT NULL COMMENT '创建日期',
    `modify_id_`       bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`     datetime       DEFAULT NULL COMMENT '修改时间',
    `remark_`          text COMMENT '备注',
    PRIMARY KEY (`id_`)
) COMMENT='塔吊循环数据记录表';

ALTER TABLE `rail_tower_crane_business_record`
    ADD COLUMN `upload_time_`     datetime       DEFAULT NULL COMMENT '上报时间';
ALTER TABLE `rail_tower_crane_business_record`
    ADD COLUMN `phi_`                   decimal(10, 2) DEFAULT NULL COMMENT '航向角度 °';