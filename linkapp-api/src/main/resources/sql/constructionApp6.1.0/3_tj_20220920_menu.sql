INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('85', '8', '单体楼层', 'singleFloor', '单体楼层', 2, NULL, NULL, 16.85, 1, 'manage/singleFloor', NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);


-- 按钮
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('851', '85', '新建单体', 'singleFloor:add', null, 3, NULL, NULL, 85.1, 2, null, NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('859', '85', '复制单体', 'singleFloor:copy', null, 3, NULL, NULL, 85.15, 2, null, NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('852', '85', '编辑单体', 'singleFloor:edit', null, 3, NULL, NULL, 85.2, 2, null, NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('853', '85', '删除单体', 'singleFloor:delete', null, 3, NULL, NULL, 85.3, 2, null, NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('854', '85', '批量新建楼层', 'singleFloor:batchAddFloor', null, 3, NULL, NULL, 85.4, 2, null, NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('855', '85', '批量删除楼层', 'singleFloor:batchDeleteFloor', null, 3, NULL, NULL, 85.5, 2, null, NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('856', '85', '新建楼层', 'singleFloor:addFloor', null, 3, NULL, NULL, 85.6, 2, null, NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('857', '85', '编辑楼层', 'singleFloor:editFloor', null, 3, NULL, NULL, 85.7, 2, null, NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('858', '85', '删除楼层', 'singleFloor:deleteFloor', null, 3, NULL, NULL, 85.8, 2, null, NULL, '1',
        NULL, NULL, NULL, NULL, NULL, 0);

-- 添加唯一索引 seq 否则会出现，菜单隐藏现象
CREATE UNIQUE INDEX unq_seq_idx USING BTREE ON linkapp_privilege (seq_);

/**
  环境设备类型
  ENVIRONMENT_DEVICE_TYPES
 */
insert
into app_config (key_,
                 tenant_id,
                 value_,
                 example,
                 module_level,
                 describe_,
                 create_time,
                 modify_time)
values ('ENVIRONMENT_DEVICE_TYPES',
        null,
        '扬尘监测系统,喷淋控制器,智能水表,单相电表,智能三相电表',
        '扬尘监测系统,喷淋控制器,智能水表,单相电表,智能三相电表',
        '环境管理:大屏',
        '环境管理大屏中的展示设备点位类别',
        now(),
        now());

-- 2022-9-21 AI智能预警
INSERT INTO linkappdb.linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                                         search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                                         modifier_, modify_time_, icon_name, flag_)
VALUES ('1009', '1000', 'AI智能预警', 'AiAlarm', NULL, 2, NULL, NULL, 10.19, 1, 'manage/AiAlarm', NULL, '1', now(), NULL,
        NULL, NULL, NULL, 0);


-- 2022-9-28
INSERT INTO linkappdb.linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                                         search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                                         modifier_, modify_time_, icon_name, flag_)
VALUES ('66000201', '660002', 'AI智能预警', 'APPAiAlarm', NULL, 2, NULL, NULL, 660002.9, 1, 'manage/APPAiAlarm', NULL, '1', now(), NULL,
        NULL, NULL, NULL, 1);


-- 单体表
CREATE TABLE `app_building`
(
    `id`                    int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`             varchar(32) NOT NULL COMMENT '租户id',
    `name`                  varchar(30) NOT NULL COMMENT '单体名称',
    `total_building_height` float(11, 2) DEFAULT NULL COMMENT '总建筑高度',
    `total_floor_height`    float(11, 2) DEFAULT NULL COMMENT '总楼层高度',
    `create_time`           datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`           datetime     DEFAULT NULL COMMENT '修改时间',
    `creator`               varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`              varchar(100) DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `app_building_name_IDX` (`name`, `tenant_id`) USING BTREE
) ENGINE = InnoDB
    COMMENT ='单体';

CREATE TABLE `app_floor`
(
    `id`                        int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`                 varchar(32)  NOT NULL COMMENT '租户id',
    `name`                      varchar(30)  NOT NULL COMMENT '楼层名称',
    `building_id`               int(11)      NOT NULL COMMENT '单体id',
    `type`                      smallint(2)  DEFAULT NULL COMMENT '楼层类型，0-基础层，1-首层,2-普通层',
    `building_floor_height`     float(11, 2) NOT NULL COMMENT '建筑层高',
    `structure_floor_height`    float(11, 2) NOT NULL COMMENT '结构层高',
    `building_standard_height`  float(11, 2) DEFAULT NULL COMMENT '建筑底标高',
    `structure_standard_height` float(11, 2) DEFAULT NULL COMMENT '结构底标高',
    `create_time`               datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`               datetime     DEFAULT NULL COMMENT '修改时间',
    `creator`                   varchar(30)  DEFAULT NULL COMMENT '创建人',
    `modifier`                  varchar(30)  DEFAULT NULL COMMENT '修改人',
    `sort_no`                   int(3)       NOT NULL COMMENT '排序号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `app_floor_name_IDX` (`name`, `building_id`) USING BTREE,
    KEY `app_floor_building_id_IDX` (`building_id`) USING BTREE,
    KEY `app_floor_tenant_id_IDX` (`tenant_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='楼层信息';

-- 机械管理关联单体
ALTER TABLE `app_machinery_record`
    ADD COLUMN `building_id` INT(11) DEFAULT NULL COMMENT '单体id' AFTER `parameter_`;
ALTER TABLE `app_machinery_record`
    ADD COLUMN `install_offset` double(20, 2) DEFAULT NULL COMMENT '安装偏差值' AFTER `parameter_`;

-- AI 智能相机型号范围
insert
into app_config (key_,
                 tenant_id,
                 value_,
                 example,
                 module_level,
                 describe_,
                 create_time,
                 modify_time)
values ('AI_CAMERA_DEVICE_UNIT_CODES',
        null,
        'AI-EDGE-01-0238',
        'AI-EDGE-01-0238,AI-EDGE-01-0238-copy',
        '安全管理:AI智能预警',
        'AI智能预警中标定包含哪些设备型号code',
        now(),
        now());


insert
into app_config (key_,
                 tenant_id,
                 value_,
                 example,
                 module_level,
                 describe_,
                 create_time,
                 modify_time)
values ('AI_CAMERA_DEVICE_UNIT_CODES_SUB',
        null,
        'SXT-QIUJ400W-04-0117',
        'SXT-QIUJ400W-04-0117,SXT-QIUJ400W-04-0117-copy',
        '安全管理:AI智能预警',
        'AI智能预警中标定包含哪些设备型号code',
        now(),
        now());
