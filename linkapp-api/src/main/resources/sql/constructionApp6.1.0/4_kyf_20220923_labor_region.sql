-- ----------------------------
-- 区域坐标
-- ----------------------------
CREATE TABLE `app_labor_region`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `name_`          varchar(32)   DEFAULT NULL COMMENT '名称',
    `type_`          int(2)        DEFAULT NULL COMMENT '类别(1施工区,2生活区)',
	`coordinate_type_` int(2)      DEFAULT NULL COMMENT '坐标类型(1高德地图)',
	`coordinate_`    text          DEFAULT NULL COMMENT '区域坐标json',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='区域坐标';

-- ----------------------------
-- ai相机预警统计
-- ----------------------------
CREATE TABLE `app_ai_alarm_count`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
	`coordinate_`    text          DEFAULT NULL COMMENT '统计数据json',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='ai相机预警统计';


-- ----------------------------
-- 安全大屏参数配置
-- ----------------------------
INSERT INTO `app_config`(`key_`, `tenant_id`, `value_`, `example`, `module_level`, `describe_`, `create_time`, `modify_time`) VALUES ( 'SECURITY_DEVICE_TYPES', NULL, 'AI识别主机,塔机安全监管系统,升降机安全监管系统', 'AI识别主机,塔机安全监管系统,升降机安全监管系统', '大屏配置:安全管理', '大屏配置中安全管理除配电箱以外的类型', '2022-09-21 16:52:26', '2022-09-21 16:52:29');

-- ----------------------------
-- 安全大屏安全检测预警参数
-- ----------------------------
INSERT INTO `app_config`(`key_`, `tenant_id`, `value_`, `example`, `module_level`, `describe_`, `create_time`, `modify_time`) VALUES ( 'SECURITY_MONITORING_DEVICE_TYPES', NULL, 'AI识别主机,电气火灾监测器,塔机安全监管系统,升降机安全监管系统', 'AI识别主机,电气火灾监测器,塔机安全监管系统,升降机安全监管系统', '安全管理大屏:预警消息类型', '安全管理大屏需要显示预警消息的设备类型', '2022-09-21 16:52:26', '2022-09-21 16:52:29');

-- ----------------------------
-- 电子档案添加关联权限
-- ----------------------------
INSERT INTO `linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('120114', '12011', '关联', 'electronicRecords:link', NULL, 3, NULL, NULL, 12.50, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- 大屏图纸
-- ----------------------------
CREATE TABLE `app_device_position_image`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
	`url_`           text          DEFAULT NULL COMMENT '图纸url',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='大屏图纸';



