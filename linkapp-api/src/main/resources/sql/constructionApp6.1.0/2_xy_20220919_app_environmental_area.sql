-- 用电计量设备支持使用“电气火灾监测器”进行计量
UPDATE app_config SET value_='智能三相电表,电气火灾监测器', example='智能三相电表,电气火灾监测器', module_level='环境管理:智能电表', describe_='环境管理智能电表中的设备类别' where key_ ='ELECTRICY_DEVICE_TYPE_NAMES';
-- 新增环境管理区域表 1.用水 2.用电
CREATE TABLE `app_environmental_area` (
`id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键',
`tenant_id_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '租户id',
`parent_id_` INT ( 11 ) DEFAULT NULL COMMENT '父id(一级父类别为0)',
`full_id_` VARCHAR ( 64 ) DEFAULT NULL COMMENT '父路径id',
`full_name_` VARCHAR ( 500 ) DEFAULT NULL COMMENT '全名',
`name_` VARCHAR ( 128 ) DEFAULT NULL COMMENT '名称',
`code_` VARCHAR ( 16 ) DEFAULT NULL COMMENT '编码',
`level_` INT ( 2 ) DEFAULT NULL COMMENT '层级',
`order_` INT ( 2 ) DEFAULT NULL COMMENT '排序/编码使用(冗余)',
`type_` INT ( 2 ) DEFAULT '1' COMMENT '类别(1.用水 2.用电)',
`creator_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '创建人id',
`create_time_` datetime DEFAULT NULL COMMENT '创建日期',
`modify_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '修改人id',
`modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
`remark_` text COMMENT '备注/说明',
PRIMARY KEY ( `id` ) USING BTREE
) COMMENT = '环境管理区域表';

-- 新增环境管理区域id
ALTER TABLE `linkapp_device_model` ADD COLUMN environmental_area_id INT ( 11 ) DEFAULT NULL COMMENT '环境管理区域id';

-- 修改字段
ALTER TABLE `linkapp_device_model` CHANGE `auto_id_` `auto_id` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT COMMENT '主键';

update linkapp_privilege set name_ = '环境监测' where id_ = 2037;
update linkapp_privilege set name_ = '自动喷淋' where id_ = 2038;
update linkapp_privilege set name_ = '用电管理' where id_ = 2039;
update linkapp_privilege set name_ = '用水管理' where id_ = 2040;