-- 【大屏/机械管理】升降机工效1005135
-- 权限 修改前端和3d
-- 升降机工作循环记录
CREATE TABLE `app_elevator_work_record` (
	`id` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`machinery_id` INT ( 11 ) DEFAULT NULL COMMENT '电子档案id',
	`device_code` varchar(32) DEFAULT NULL COMMENT '设备code',
	`tenant_id_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '租户id',
	`start_time` datetime DEFAULT NULL COMMENT '开始时间',
	`end_time` datetime DEFAULT NULL COMMENT '结束时间',
	`driver_name` VARCHAR ( 32 ) DEFAULT NULL COMMENT '驾驶员姓名',
	`max_weight` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '最大载重 单位kg',
	`weight_percentage` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '载重百分比 单位%',
	`start_height` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '起点高度 单位m',
	`end_height` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '终点高度 单位m',
	`stroke_height` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '行程高度 单位m',
	`rise_direction` INT ( 2 ) DEFAULT NULL COMMENT '起升方向 0-停止，1-上，2-下，不可能取值为停止',
	`average_speed` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '平均速度 单位m/s',
	`max_tilt_x` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '最大倾斜X 单位°',
	`max_tilt_y` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '最大倾斜Y 单位°',
	`create_time` datetime DEFAULT NULL COMMENT '创建时间',
	PRIMARY KEY ( `id` ),
	KEY `app_elevator_work_record_machinery_id_IDX` ( `machinery_id` ) USING BTREE,
	KEY `app_elevator_work_record_device_code_IDX` ( `device_code` ) USING BTREE
) COMMENT = '升降机工作循环记录';

-- 升降机楼层驻留记录
CREATE TABLE `app_elevator_floor_record` (
	`id` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`machinery_id` INT ( 11 ) DEFAULT NULL COMMENT '电子档案id',
	`device_code` varchar(32) DEFAULT NULL COMMENT '设备code',
	`tenant_id_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '租户id',
	`open_time` datetime DEFAULT NULL COMMENT '开门时间',
	`close_time` datetime DEFAULT NULL COMMENT '关门时间',
	`door_status` INT ( 2 ) DEFAULT NULL COMMENT '开关状态 0-关，1-开',
	`height_` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '高度 单位m',
	`building_id` INT ( 11 ) DEFAULT NULL COMMENT '单体id',
	`building_name` VARCHAR ( 32 ) DEFAULT NULL COMMENT '单体名称',
	`floor_id` INT ( 11 ) DEFAULT NULL COMMENT '楼层id',
	`floor_name` VARCHAR ( 32 ) DEFAULT NULL COMMENT '楼层名称',
	`creator_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '创建人id',
	`create_time_` datetime DEFAULT NULL COMMENT '创建日期',
	`modify_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '修改人id',
	`modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
	`remark_` text COMMENT '备注',
	PRIMARY KEY ( `id` ),
	KEY `app_elevator_floor_record_machinery_id_IDX` ( `machinery_id` ) USING BTREE,
	KEY `app_elevator_floor_record_device_code_IDX` ( `device_code` ) USING BTREE
) COMMENT = '升降机楼层驻留记录';