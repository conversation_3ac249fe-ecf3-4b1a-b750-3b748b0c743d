-- ----------------------------
-- 管理人员下发闸机关联
-- ----------------------------
CREATE TABLE `app_en_user_gate_link`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id(暂时预留)',
	`gate_id_`       varchar(32)   DEFAULT NULL COMMENT '闸机id',
	`user_id_`       bigint(20)    DEFAULT NULL COMMENT '管理人员id',
	`user_name_`     varchar(32)   DEFAULT NULL COMMENT '管理人员名称',
	`card_`          varchar(32)   DEFAULT NULL COMMENT '身份证号',
	`photo_`         varchar(255)  DEFAULT NULL COMMENT '相片url',
	`telephone_`     varchar(32)   DEFAULT NULL COMMENT '联系电话',
	`organization_id_`  bigint(20) DEFAULT NULL COMMENT '企业级的组织id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='管理人员下发闸机关联';

ALTER TABLE app_en_user_gate_link ADD organization_name_ varchar(50) DEFAULT NULL COMMENT '企业级的组织名称';
