-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS `app_version_info`;

-- 创建app_version_info表
CREATE TABLE `app_version_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `device_brand` varchar(100) DEFAULT NULL COMMENT '设备品牌，apple、huawei',
    `os_name` varchar(100) DEFAULT NULL COMMENT '系统名称，ios，android',
    `os_version` varchar(30) DEFAULT NULL COMMENT '系统版本',
    `os_language` varchar(100) DEFAULT NULL COMMENT '系统语言，zh-CN',
    `app_version` varchar(20) DEFAULT NULL COMMENT 'app版本号',
    `app_url` varchar(300) DEFAULT NULL COMMENT 'app下载地址',
    `update_content` varchar(2000) DEFAULT NULL COMMENT '更新内容',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `deleted` int(1) DEFAULT NULL COMMENT '是否删除，1否，0是',
    PRIMARY KEY (`id`),
    KEY `app_version_info_create_time_IDX` (`create_time`) USING BTREE,
    KEY `app_version_info_os_name_IDX` (`os_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8 COMMENT='App版本信息'; 