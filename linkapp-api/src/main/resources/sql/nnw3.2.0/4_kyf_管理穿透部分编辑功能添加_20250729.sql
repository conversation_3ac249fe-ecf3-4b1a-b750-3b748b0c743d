-- =============================================
-- 为班前会、晚交班会、过程盯控、跟班记录、网格安全员日志、安全条件确认表添加编辑按钮权限
-- 开发者: kyf
-- 日期: 2025-07-29
-- 功能: 添加编辑按钮权限配置
-- =============================================

-- =============================================
-- 1. 班前会编辑权限
-- =============================================
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99153001', 99153, '编辑', 't_before_meet_edit', '班前会-编辑', 3, NULL, NULL, 99153.001, 2, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- =============================================
-- 2. 晚交班会编辑权限
-- =============================================
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99154001', 99154, '编辑', 't_late_meet_edit', '晚交班会-编辑', 3, NULL, NULL, 99154.001, 2, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- =============================================
-- 3. 过程盯控编辑权限
-- =============================================
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99155001', 99155, '编辑', 't_process_monitor_edit', '过程盯控-编辑', 3, NULL, NULL, 99155.001, 2, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- =============================================
-- 4. 跟班记录编辑权限
-- =============================================
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99157001', 99157, '编辑', 't_follow_work_edit', '跟班记录-编辑', 3, NULL, NULL, 99157.001, 2, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- =============================================
-- 5. 网格安全员日志编辑权限
-- =============================================
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99158001', 99158, '编辑', 't_grid_safe_edit', '网格安全员日志-编辑', 3, NULL, NULL, 99158.001, 2, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- =============================================
-- 6. 安全条件确认表编辑权限
-- =============================================
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99152001', 99152, '编辑', 't_safe_table_edit', '安全条件确认表-编辑', 3, NULL, NULL, 99152.001, 2, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
