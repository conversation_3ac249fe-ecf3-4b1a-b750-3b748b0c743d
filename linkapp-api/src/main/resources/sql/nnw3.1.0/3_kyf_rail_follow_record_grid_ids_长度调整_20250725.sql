-- =============================================
-- 归档修改: rail_follow_record 表 grid_ids_ 字段长度调整
-- 作者: 系统管理员
-- 日期: 2025-07-25
-- 描述: 将 rail_follow_record 表的 grid_ids_ 字段长度从 varchar(255) 调整为 varchar(5000)
--       以支持更多网格ID的存储需求
-- =============================================

-- 修改 rail_follow_record 表的 grid_ids_ 字段长度
ALTER TABLE `rail_follow_record` 
MODIFY COLUMN `grid_ids_` varchar(5000) DEFAULT NULL COMMENT '网格IDs(多个用逗号分隔)';

-- 验证修改结果的查询语句（可选执行）
-- DESCRIBE rail_follow_record;
-- 或者使用以下语句查看字段信息：
-- SHOW COLUMNS FROM rail_follow_record LIKE 'grid_ids_';
