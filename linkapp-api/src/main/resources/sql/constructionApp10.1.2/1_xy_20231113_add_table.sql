-- 高支模监测配置表
CREATE TABLE `app_gzm_monitor_config`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id_`   varchar(32)  DEFAULT NULL COMMENT '租户id',
    `gzm_overview` varchar(512) DEFAULT NULL COMMENT '高支模概况',
    `gzm_url_`     varchar(200) DEFAULT NULL COMMENT '高支模平面图',
    `creator`      varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`     varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`  datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `tenant_id_IDX` (`tenant_id_`) USING BTREE
) COMMENT='高支模监测配置表';

-- 高支模监测点位表
CREATE TABLE `app_gzm_monitor_item_config`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `device_code`  varchar(32) DEFAULT NULL COMMENT '设备id',
    `tenant_id_`   varchar(32) DEFAULT NULL COMMENT '租户id',
    `x_axis`       double        DEFAULT NULL COMMENT 'x坐标值',
    `y_axis`       double        DEFAULT NULL COMMENT 'y轴坐标值',
    `creator`      varchar(100)  DEFAULT NULL COMMENT '创建人',
    `modifier`     varchar(100)  DEFAULT NULL COMMENT '修改人',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `tenant_id_IDX` (`tenant_id_`) USING BTREE
) COMMENT='高支模监测点位表';

-- 高支模监测配置
-- INSERT INTO `app_config` (`key_`, `tenant_id`, `value_`, `example`, `module_level`, `describe_`, `create_time`, `modify_time`)
-- VALUES
--     ('GZM_DEVICE_TYPES', NULL, '高支模监测', '高支模监测', '系统管理:大屏设置', '高支模监测', now(), now());


-- 高支模监测配置
INSERT INTO `app_config` (`key_`, `tenant_id`, `value_`, `example`, `module_level`, `describe_`,
                          `create_time`, `modify_time`)
VALUES ('GZM_DEVICE_UNIT_CODES', NULL,
        '[{"code":"HH-GZM-QX-0001","showAttrs":["f1","f2","f3"]},{"code":"HH-GZM-YL-0002","showAttrs":["f1"]},{"code":"HH-GZM-CZ-0003","showAttrs":["f1"]},{"code":"HH-GZM-SP-0004","showAttrs":["f1"]}]',
        '[{"code":"HH-GZM-QX-0001","showAttrs":["f1","f2","f3"]},{"code":"HH-GZM-YL-0002","showAttrs":["f1"]},{"code":"HH-GZM-CZ-0003","showAttrs":["f1"]},{"code":"HH-GZM-SP-0004","showAttrs":["f1"]}]
', '系统管理:大屏设置', '高支模监测', now(), now());

-- 权限 系统管理-大屏设置-高支模监测
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
    ('8406', '84', '高支模监测', 'highFormworkMonitoring', NULL, 3, NULL, NULL, 84.06, 2, NULL, NULL, '1', '2023-11-17 10:28:52', NULL, NULL, NULL, NULL, 0);


