-- 新增楼层信标表
CREATE TABLE `app_floor_beacon` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `device_code_` varchar(32) NOT NULL DEFAULT '' COMMENT '设备编号',
                                    `tenant_id_` varchar(32) NOT NULL DEFAULT '' COMMENT '租户id',
                                    `bind_time_` datetime DEFAULT NULL COMMENT '安装时间',
                                    `state_` tinyint(1) NOT NULL DEFAULT '0' COMMENT '启用状态：0-禁用，1-启用',
                                    `battery_electricity_` tinyint(3) NOT NULL DEFAULT '100' COMMENT '电池电量',
                                    `floor_id_` int(11) NOT NULL COMMENT '楼层id',
                                    `beacon_x_position_` float(15,4) NOT NULL DEFAULT '0.0000' COMMENT '平面图x轴坐标',
  `beacon_y_position_` float(15,4) NOT NULL DEFAULT '0.0000' COMMENT '平面图y轴坐标',
  `creator_id_` bigint(20) NOT NULL COMMENT '创建人',
  `create_time_` datetime NOT NULL COMMENT '创建时间',
  `modifier_id_` bigint(20) NOT NULL COMMENT '修改人',
  `modify_time_` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8 COMMENT='楼层信标';

-- 新增定位设备表
CREATE TABLE `linkapp_location_device` (
                                           `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `device_code_` varchar(32) NOT NULL DEFAULT '' COMMENT '设备编号',
                                           `device_type_` int(2) NOT NULL DEFAULT '1' COMMENT '设备类型: 1-安全帽',
                                           `label_code_` varchar(32) DEFAULT '' COMMENT '标签编号',
                                           `tenant_id_` varchar(32) NOT NULL DEFAULT '' COMMENT '租户id',
                                           `user_id_` varchar(32) DEFAULT NULL COMMENT '绑定人员id(花名册user_id)',
                                           `bind_time_` datetime DEFAULT NULL COMMENT '绑定时间',
                                           `battery_` int(3) NOT NULL DEFAULT '0' COMMENT '电池电量',
                                           `online_state_` tinyint(1) NOT NULL DEFAULT '0' COMMENT '在线状态 0-离线 1-在线',
                                           `bind_state_` tinyint(1) NOT NULL DEFAULT '0' COMMENT '绑定状态：0-未绑定 1-已绑定',
                                           `delete_state_` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否删除字段 1:存在; 0:删除',
                                           `latest_reporting_time_` datetime DEFAULT NULL COMMENT '最新上报时间',
                                           `wearing_state_` tinyint(1) NOT NULL DEFAULT '0' COMMENT '佩戴状态：0-脱离 1-穿戴',
                                           `warning_state_` tinyint(1) NOT NULL DEFAULT '0' COMMENT '报警状态：0-未报警 1-已报警',
                                           `location_type_` tinyint(1) NOT NULL DEFAULT '0' COMMENT '位置类型：0-室内 1-室外',
                                           `indoor_location_` varchar(32) DEFAULT '' COMMENT '室内位置',
                                           `height_` float(15,6) DEFAULT '0.000000' COMMENT '高度',
  `longitude_` float(15,6) DEFAULT '0.000000' COMMENT '经度',
  `latitude_` float(15,6) DEFAULT '0.000000' COMMENT '纬度',
  `creator_id_` bigint(20) NOT NULL COMMENT '创建人id',
  `create_time_` datetime NOT NULL COMMENT '创建日期',
  `modify_id_` bigint(20) NOT NULL COMMENT '修改人id',
  `modify_time_` datetime NOT NULL COMMENT '修改时间',
  `remark_` varchar(255) DEFAULT '' COMMENT '备注',
  `x_position_` float(15,4) DEFAULT NULL COMMENT '平面图x轴坐标',
  `y_position_` float(15,4) DEFAULT NULL COMMENT '平面图y轴坐标',
  PRIMARY KEY (`id`),
  KEY `idx_latest_reporting_time_` (`latest_reporting_time_`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8 COMMENT='定位设备表';

-- 新增定位记录表
CREATE TABLE `linkapp_location_record` (
                                           `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `device_code_` varchar(32) NOT NULL DEFAULT '' COMMENT '设备编号',
                                           `device_type_` tinyint(2) NOT NULL DEFAULT '1' COMMENT '设备类型: 1-安全帽',
                                           `tenant_id_` varchar(32) NOT NULL DEFAULT '' COMMENT '租户id',
                                           `user_info_` varchar(32) DEFAULT NULL COMMENT '绑定人员信息',
                                           `location_type_` tinyint(1) NOT NULL DEFAULT '0' COMMENT '位置类型：0-室内 1-室外',
                                           `indoor_location_` varchar(32) NOT NULL DEFAULT '' COMMENT '室内位置',
                                           `longitude_` float(15,6) NOT NULL DEFAULT '0.000000' COMMENT '经度',
  `latitude_` float(15,6) NOT NULL DEFAULT '0.000000' COMMENT '纬度',
  `height_` float(15,6) NOT NULL DEFAULT '0.000000' COMMENT '高度',
  `best_beacon_` varchar(32) NOT NULL DEFAULT '' COMMENT '最优信标编号',
  `delete_state_` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否删除字段 1:存在; 0:删除',
  `creator_` varchar(10) NOT NULL DEFAULT 'SYS' COMMENT '创建人',
  `create_time_` datetime NOT NULL COMMENT '创建日期',
  `modifier_` varchar(10) NOT NULL DEFAULT 'SYS' COMMENT '修改人',
  `modify_time_` datetime NOT NULL COMMENT '修改时间',
  `remark_` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_device_code_` (`device_code_`),
  KEY `idx_create_time_` (`create_time_`),
  KEY `idx_tenant_id_` (`tenant_id_`)
) ENGINE=InnoDB AUTO_INCREMENT=712 DEFAULT CHARSET=utf8 COMMENT='定位记录表';

-- 新增定位记录明细表
CREATE TABLE `linkapp_location_record_detail` (
                                                  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                  `link_id_` bigint(20) NOT NULL COMMENT '关联定位记录表id',
                                                  `device_code_` varchar(32) NOT NULL DEFAULT '' COMMENT '设备编号(信标)',
                                                  `tenant_id_` varchar(32) NOT NULL DEFAULT '' COMMENT '租户id',
                                                  `indoor_location_` varchar(32) NOT NULL DEFAULT '' COMMENT '安装位置',
                                                  `height_` float(15,6) NOT NULL DEFAULT '0.000000' COMMENT '高度',
  `signal_strength_` varchar(32) NOT NULL DEFAULT '' COMMENT '信号强度',
  `delete_state_` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否删除字段 1:存在; 0:删除',
  `creator_` varchar(10) NOT NULL DEFAULT 'SYS' COMMENT '创建人',
  `create_time_` datetime NOT NULL COMMENT '创建日期',
  `modifier_` varchar(10) NOT NULL DEFAULT 'SYS' COMMENT '修改人',
  `modify_time_` datetime NOT NULL COMMENT '修改时间',
  `remark_` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=940 DEFAULT CHARSET=utf8 COMMENT='定位记录明细表';
