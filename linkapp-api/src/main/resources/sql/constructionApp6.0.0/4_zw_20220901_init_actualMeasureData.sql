INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(905, NULL, '主体工程', '主体工程/', '/905/', 0, NULL, 1, 1, '1691', '1691', '2022-09-07 17:46:19', '2022-09-07 17:46:19', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(906, NULL, '混凝土结构工程', '主体工程/混凝土结构工程/', '/905/906/', 905, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:19', '2022-09-07 17:46:19', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(907, NULL, '截面尺寸', '主体工程/混凝土结构工程/截面尺寸/', '/905/906/907/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:19', '2022-09-07 17:46:19', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(908, NULL, '墙体平整度', '主体工程/混凝土结构工程/墙体平整度/', '/905/906/908/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:19', '2022-09-07 17:46:19', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(909, NULL, '墙\\柱垂直度', '主体工程/混凝土结构工程/墙\\柱垂直度/', '/905/906/909/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:19', '2022-09-07 17:46:19', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(910, NULL, '顶板水平度（有爆板..', '主体工程/混凝土结构工程/顶板水平度（有爆板../', '/905/906/910/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(911, NULL, '结构净高', '主体工程/混凝土结构工程/结构净高/', '/905/906/911/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(912, NULL, '楼板厚度', '主体工程/混凝土结构工程/楼板厚度/', '/905/906/912/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(913, NULL, '混凝土观感（1）', '主体工程/混凝土结构工程/混凝土观感（1）/', '/905/906/913/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(914, NULL, '混凝土观感（2）', '主体工程/混凝土结构工程/混凝土观感（2）/', '/905/906/914/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(915, NULL, '混凝土强度', '主体工程/混凝土结构工程/混凝土强度/', '/905/906/915/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 0);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(916, NULL, '门洞口尺寸', '主体工程/混凝土结构工程/门洞口尺寸/', '/905/906/916/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(917, NULL, '窗洞口尺寸', '主体工程/混凝土结构工程/窗洞口尺寸/', '/905/906/917/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(918, NULL, '钢筋保护层厚度', '主体工程/混凝土结构工程/钢筋保护层厚度/', '/905/906/918/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(919, NULL, '相邻踏步高度', '主体工程/混凝土结构工程/相邻踏步高度/', '/905/906/919/', 906, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(920, NULL, '砌筑工程', '主体工程/砌筑工程/', '/905/920/', 905, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(921, NULL, '表面平整度', '主体工程/砌筑工程/表面平整度/', '/905/920/921/', 920, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(922, NULL, '垂直度', '主体工程/砌筑工程/垂直度/', '/905/920/922/', 920, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(923, NULL, '外门窗洞口尺寸', '主体工程/砌筑工程/外门窗洞口尺寸/', '/905/920/923/', 920, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(924, NULL, '门洞口尺寸', '主体工程/砌筑工程/门洞口尺寸/', '/905/920/924/', 920, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(925, NULL, '砌筑节点（1）', '主体工程/砌筑工程/砌筑节点（1）/', '/905/920/925/', 920, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(926, NULL, '砌筑节点（2）', '主体工程/砌筑工程/砌筑节点（2）/', '/905/920/926/', 920, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(927, NULL, '设备安装', '设备安装/', '/927/', 0, NULL, 1, 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(928, NULL, '洁具', '设备安装/洁具/', '/927/928/', 927, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(929, NULL, '座便坑距偏差', '设备安装/洁具/座便坑距偏差/', '/927/928/929/', 928, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(930, NULL, '电气安装', '设备安装/电气安装/', '/927/930/', 927, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(931, NULL, '同一室内的底盒标高差', '设备安装/电气安装/同一室内的底盒标高差/', '/927/930/931/', 930, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(932, NULL, '同一室内开关、插座面板高度偏差', '设备安装/电气安装/同一室内开关、插座面板高度偏差/', '/927/930/932/', 930, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(933, NULL, '开关底盒距门口距离', '设备安装/电气安装/开关底盒距门口距离/', '/927/930/933/', 930, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(934, NULL, '消防系统', '设备安装/消防系统/', '/927/934/', 927, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(935, NULL, '喷淋间距\\数量', '设备安装/消防系统/喷淋间距\\数量/', '/927/934/935/', 934, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(936, NULL, '喷淋间距\\数量', '设备安装/消防系统/喷淋间距\\数量/', '/927/934/936/', 934, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(937, NULL, '装饰工程', '装饰工程/', '/937/', 0, NULL, 1, 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(938, NULL, '抹灰工程', '装饰工程/抹灰工程/', '/937/938/', 937, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(939, NULL, '墙面表面平整度', '装饰工程/抹灰工程/墙面表面平整度/', '/937/938/939/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(940, NULL, '墙面垂直度', '装饰工程/抹灰工程/墙面垂直度/', '/937/938/940/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(941, NULL, '净高（有爆板）', '装饰工程/抹灰工程/净高（有爆板）/', '/937/938/941/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(942, NULL, '顶板水平度（有爆板）', '装饰工程/抹灰工程/顶板水平度（有爆板）/', '/937/938/942/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(943, NULL, '阴阳角方正', '装饰工程/抹灰工程/阴阳角方正/', '/937/938/943/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(944, NULL, '开间\\进深', '装饰工程/抹灰工程/开间\\进深/', '/937/938/944/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(945, NULL, '方正性', '装饰工程/抹灰工程/方正性/', '/937/938/945/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(946, NULL, '地面表面平整度', '装饰工程/抹灰工程/地面表面平整度/', '/937/938/946/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(947, NULL, '地面水平度（有爆板）', '装饰工程/抹灰工程/地面水平度（有爆板）/', '/937/938/947/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(948, NULL, '地面空鼓开裂', '装饰工程/抹灰工程/地面空鼓开裂/', '/937/938/948/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(949, NULL, '户内门洞尺寸偏差', '装饰工程/抹灰工程/户内门洞尺寸偏差/', '/937/938/949/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(950, NULL, '户内门洞口厚度', '装饰工程/抹灰工程/户内门洞口厚度/', '/937/938/950/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(951, NULL, '外墙窗内侧墙体厚度极差', '装饰工程/抹灰工程/外墙窗内侧墙体厚度极差/', '/937/938/951/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(952, NULL, '抹灰裂缝\\空鼓', '装饰工程/抹灰工程/抹灰裂缝\\空鼓/', '/937/938/952/', 938, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(953, NULL, '防渗漏工程', '装饰工程/防渗漏工程/', '/937/953/', 937, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(954, NULL, '防水层厚度（地下室）', '装饰工程/防渗漏工程/防水层厚度（地下室）/', '/937/953/954/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(955, NULL, '搭接长度（地下室）', '装饰工程/防渗漏工程/搭接长度（地下室）/', '/937/953/955/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(956, NULL, '搭接长度（地下室）', '装饰工程/防渗漏工程/搭接长度（地下室）/', '/937/953/956/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(957, NULL, '阴阳角做圆（地下室）', '装饰工程/防渗漏工程/阴阳角做圆（地下室）/', '/937/953/957/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(958, NULL, '保护层（地下室）', '装饰工程/防渗漏工程/保护层（地下室）/', '/937/953/958/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(959, NULL, '卫生间涂膜厚度（卫生间）', '装饰工程/防渗漏工程/卫生间涂膜厚度（卫生间）/', '/937/953/959/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(960, NULL, '找坡坡度（卫生间）', '装饰工程/防渗漏工程/找坡坡度（卫生间）/', '/937/953/960/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(961, NULL, '外窗滴水线、鹰嘴、排水坡度', '装饰工程/防渗漏工程/外窗滴水线、鹰嘴、排水坡度/', '/937/953/961/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(962, NULL, '外窗框封堵', '装饰工程/防渗漏工程/外窗框封堵/', '/937/953/962/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(963, NULL, '防水卷材厚度（屋面）', '装饰工程/防渗漏工程/防水卷材厚度（屋面）/', '/937/953/963/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(964, NULL, '搭接长度（屋面）', '装饰工程/防渗漏工程/搭接长度（屋面）/', '/937/953/964/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(965, NULL, '楼梯间出屋面反坎、发电机烟道、住宅烟道、屋面变形缝等屋面构件反坎（屋面）', '装饰工程/防渗漏工程/楼梯间出屋面反坎、发电机烟道、住宅烟道、屋面变形缝等屋面构件反坎（屋面）/', '/937/953/965/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(966, NULL, '女儿墙（屋面）', '装饰工程/防渗漏工程/女儿墙（屋面）/', '/937/953/966/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(967, NULL, '找坡坡度（屋面）', '装饰工程/防渗漏工程/找坡坡度（屋面）/', '/937/953/967/', 953, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(968, NULL, '成品保护', '装饰工程/成品保护/', '/937/968/', 937, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(969, NULL, '入户门', '装饰工程/成品保护/入户门/', '/937/968/969/', 968, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(970, NULL, '外窗玻璃', '装饰工程/成品保护/外窗玻璃/', '/937/968/970/', 968, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(971, NULL, '阳台门', '装饰工程/成品保护/阳台门/', '/937/968/971/', 968, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(972, NULL, '室内墙面阳角', '装饰工程/成品保护/室内墙面阳角/', '/937/968/972/', 968, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(973, NULL, '木地板', '装饰工程/成品保护/木地板/', '/937/968/973/', 968, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(974, NULL, '浴缸', '装饰工程/成品保护/浴缸/', '/937/968/974/', 968, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(975, NULL, '涂饰工程', '装饰工程/涂饰工程/', '/937/975/', 937, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(976, NULL, '墙面表面平整度', '装饰工程/涂饰工程/墙面表面平整度/', '/937/975/976/', 975, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(977, NULL, '墙面垂直度', '装饰工程/涂饰工程/墙面垂直度/', '/937/975/977/', 975, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(978, NULL, '阴阳角方正', '装饰工程/涂饰工程/阴阳角方正/', '/937/975/978/', 975, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(979, NULL, '顶棚(吊顶)水平度（有爆板）', '装饰工程/涂饰工程/顶棚(吊顶)水平度（有爆板）/', '/937/975/979/', 975, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(980, NULL, '裂缝\\空鼓', '装饰工程/涂饰工程/裂缝\\空鼓/', '/937/975/980/', 975, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(981, NULL, '饰面砖粘贴（墙面）', '装饰工程/饰面砖粘贴（墙面）/', '/937/981/', 937, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(982, NULL, '墙面表面平整度', '装饰工程/饰面砖粘贴（墙面）/墙面表面平整度/', '/937/981/982/', 981, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(983, NULL, '墙面垂直度', '装饰工程/饰面砖粘贴（墙面）/墙面垂直度/', '/937/981/983/', 981, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(984, NULL, '墙面垂直度', '装饰工程/饰面砖粘贴（墙面）/墙面垂直度/', '/937/981/984/', 981, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(985, NULL, '阴阳角方正', '装饰工程/饰面砖粘贴（墙面）/阴阳角方正/', '/937/981/985/', 981, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(986, NULL, '接缝高低差', '装饰工程/饰面砖粘贴（墙面）/接缝高低差/', '/937/981/986/', 981, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(987, NULL, '裂缝\\空鼓', '装饰工程/饰面砖粘贴（墙面）/裂缝\\空鼓/', '/937/981/987/', 981, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(988, NULL, '饰面砖粘贴（地面）', '装饰工程/饰面砖粘贴（地面）/', '/937/988/', 937, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(989, NULL, '表面平整度', '装饰工程/饰面砖粘贴（地面）/表面平整度/', '/937/988/989/', 988, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(990, NULL, '接缝高低差', '装饰工程/饰面砖粘贴（地面）/接缝高低差/', '/937/988/990/', 988, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(991, NULL, '裂缝\\空鼓', '装饰工程/饰面砖粘贴（地面）/裂缝\\空鼓/', '/937/988/991/', 988, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(992, NULL, '室内门', '装饰工程/室内门/', '/937/992/', 937, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(993, NULL, '门框的正、侧面垂直度', '装饰工程/室内门/门框的正、侧面垂直度/', '/937/992/993/', 992, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(994, NULL, '铝合金门窗（或塑钢窗）', '装饰工程/铝合金门窗（或塑钢窗）/', '/937/994/', 937, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(995, NULL, '型材拼缝宽度（不适用塑钢窗）', '装饰工程/铝合金门窗（或塑钢窗）/型材拼缝宽度（不适用塑钢窗）/', '/937/994/995/', 994, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(996, NULL, '相同截面型材拼缝高低差', '装饰工程/铝合金门窗（或塑钢窗）/相同截面型材拼缝高低差/', '/937/994/996/', 994, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(997, NULL, '窗框正面垂直度', '装饰工程/铝合金门窗（或塑钢窗）/窗框正面垂直度/', '/937/994/997/', 994, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(998, NULL, '窗框固定', '装饰工程/铝合金门窗（或塑钢窗）/窗框固定/', '/937/994/998/', 994, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(999, NULL, '边框收口与塞缝', '装饰工程/铝合金门窗（或塑钢窗）/边框收口与塞缝/', '/937/994/999/', 994, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1000, NULL, '地板', '装饰工程/地板/', '/937/1000/', 937, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1001, NULL, '表面平整度', '装饰工程/地板/表面平整度/', '/937/1000/1001/', 1000, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1002, NULL, '接缝宽度（实木复合、实木地板）', '装饰工程/地板/接缝宽度（实木复合、实木地板）/', '/937/1000/1002/', 1000, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1003, NULL, '接缝高低差（实木复合、实木地板）', '装饰工程/地板/接缝高低差（实木复合、实木地板）/', '/937/1000/1003/', 1000, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1004, NULL, '地板水平度（有爆板）', '装饰工程/地板/地板水平度（有爆板）/', '/937/1000/1004/', 1000, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1005, NULL, '钢结构工程', '钢结构工程/', '/1005/', 0, NULL, 1, 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1006, NULL, '焊接工程', '钢结构工程/焊接工程/', '/1005/1006/', 1005, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1007, NULL, '焊缝外观', '钢结构工程/焊接工程/焊缝外观/', '/1005/1006/1007/', 1006, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1008, NULL, '焊缝探伤', '钢结构工程/焊接工程/焊缝探伤/', '/1005/1006/1008/', 1006, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1009, NULL, '防火涂料涂层厚度', '钢结构工程/防火涂料涂层厚度/', '/1005/1009/', 1005, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1010, NULL, '防火涂料涂层厚度', '钢结构工程/防火涂料涂层厚度/防火涂料涂层厚度/', '/1005/1009/1010/', 1009, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1011, NULL, '柱截面尺寸', '钢结构工程/柱截面尺寸/', '/1005/1011/', 1005, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1012, NULL, '柱截面尺寸', '钢结构工程/柱截面尺寸/柱截面尺寸/', '/1005/1011/1012/', 1011, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1013, NULL, '钢管构件直径', '钢结构工程/柱截面尺寸/钢管构件直径/', '/1005/1011/1013/', 1011, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1014, NULL, '箱型截面对角线差', '钢结构工程/柱截面尺寸/箱型截面对角线差/', '/1005/1011/1014/', 1011, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1015, NULL, '柱安装的允许偏差', '钢结构工程/柱安装的允许偏差/', '/1005/1015/', 1005, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1016, NULL, '单层柱垂直度', '钢结构工程/柱安装的允许偏差/单层柱垂直度/', '/1005/1015/1016/', 1015, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1017, NULL, '多层柱垂直度', '钢结构工程/柱安装的允许偏差/多层柱垂直度/', '/1005/1015/1017/', 1015, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1018, NULL, '柱脚底座中心线对定位轴线的偏移', '钢结构工程/柱安装的允许偏差/柱脚底座中心线对定位轴线的偏移/', '/1005/1015/1018/', 1015, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1019, NULL, '柱对接的允许偏差', '钢结构工程/柱对接的允许偏差/', '/1005/1019/', 1005, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1020, NULL, '柱对接的错口', '钢结构工程/柱对接的允许偏差/柱对接的错口/', '/1005/1019/1020/', 1019, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1021, NULL, '玻璃幕墙', '玻璃幕墙/', '/1021/', 0, NULL, 1, 1, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1022, NULL, '施工工艺', '玻璃幕墙/施工工艺/', '/1021/1022/', 1021, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1023, NULL, '横向构件水平度', '玻璃幕墙/施工工艺/横向构件水平度/', '/1021/1022/1023/', 1022, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1024, NULL, '两相邻面板之间接缝高低差', '玻璃幕墙/施工工艺/两相邻面板之间接缝高低差/', '/1021/1022/1024/', 1022, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1025, NULL, '分隔框对角线差', '玻璃幕墙/施工工艺/分隔框对角线差/', '/1021/1022/1025/', 1022, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1026, NULL, '玻璃拼缝', '玻璃幕墙/玻璃拼缝/', '/1021/1026/', 1021, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1027, NULL, '密封胶', '玻璃幕墙/玻璃拼缝/密封胶/', '/1021/1026/1027/', 1026, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1028, NULL, '材料', '玻璃幕墙/材料/', '/1021/1028/', 1021, NULL, 1, 2, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_type
(id, tenant_id, name, full_name, full_id, parent_id, code, sort_no, `level`, creator, modifier, create_time, modify_time, delete_state)
VALUES(1029, NULL, '玻璃', '玻璃幕墙/材料/玻璃/', '/1021/1028/1029/', 1028, NULL, 1, 3, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);


INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(433, 907, NULL, '墙/柱截面尺寸', NULL, '[-5,8]', 'mm', 1, 40, 2, NULL, '同一墙/柱面为一个检测区，每个检测区检测截面尺寸2次，选取其中与设计尺寸偏差最大的一个实测值，作为该实测指标合格率的1个计算点。', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:19', '2022-09-07 17:46:19', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(434, 908, NULL, '墙表面平整度', NULL, '[0,8]', 'mm', 1, 60, NULL, NULL, '任选长边墙两面中的一面作为1个检测区。当所选墙长度小于3米时，同一面墙4个角（顶部及根部）中，取左上及右下2个角。按45度角斜放靠尺，累计测2次表面平整度,这2个实测值分别作为该指标合格率的2个计算点;当所选墙长度大于3米时，还需在墙长度中间水平放靠尺增加测量1次，这3个实测值分别作为该指标合格率的3个计算点。跨洞口部位必测。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:19', '2022-09-07 17:46:19', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(435, 909, NULL, '墙表面平整度', NULL, '[0,8]', 'mm', 1, 60, NULL, NULL, '任选长边墙两面中一面作为1个检测区。当墙长小于3米时，同一面墙距两端头竖向阴阳角约30CM位置，分别按靠尺顶端接触到上部砼顶板位置及靠尺底端接触到下部地面位置测2次垂直度，这2个实测值分别作为该实测指标合格率的2个计算点。当墙长度大于3米时，需在墙长度中间位置靠尺在高度方向居中处加测1次，3个实测值分别作为该实测指标合格率的3个计算点。砼柱：任选砼柱四面中的两面作为一个检测区；分别将靠尺顶端接触到上部砼顶板和下部地面位置各测1次，这2个实测值分别作为该实测指标合格率的2个计算点。砼墙体洞口一侧为垂直度必测部位。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(436, 910, NULL, '顶板水平度', NULL, '[0,15]', 'mm', 1, 50, 5, '[0,20]mm', '使用激光扫平仪，在实测板跨内打出一条水平基准线。分别测量4个角点/板跨几何中心位砼顶板与水平基准线之间的5个垂直距离。最大偏差值﹥15mm时，5个偏差值均按最大偏差值计，作为该实测指标合格率的5个计算点。', 3, 'N', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(437, 911, NULL, '结构净高', NULL, '[-10,10]', 'mm', 1, 50, 5, '[-30,30]mm', '使用激光扫平仪，在实测板跨内打出一条水平基准线。分别测量4个角点/板跨几何中心位砼顶板与水平基准线之间的5个垂直距离。取最低点标高与设计偏差下限比较，取最高点标高与设计偏差上限比较，其中一个超出容许偏差，为不合格，5个实测值与设计值对比产生的偏差值均作为判断该实测指标合格率的5个计算点', 5, 'Y', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(438, 912, NULL, '楼板厚度', NULL, '[-5,8]', 'mm', 1, 10, NULL, NULL, '同一跨板作为1个实测区。每个实测区取1个样本点，取点位置为该板跨中1／3区域。1个实测值作为判断该实测指标合格率的1个计算点。', 1, 'Y', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(439, 913, NULL, '露筋、蜂窝', NULL, '1或0', NULL, 1, 20, NULL, NULL, '抽检15面混凝土墙/柱。每面墙/柱均作为一个实测区..', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(440, 914, NULL, '涨模、开裂', NULL, '1或0', NULL, 1, 20, NULL, NULL, '抽检15面混凝土墙/柱。每面墙/柱均作为一个实测区..', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(441, 915, NULL, '回弹值', NULL, NULL, 'Mpa', 1, 160, 16, NULL, '根据混凝土龄期，对实体构件进行抽检，每个构件回弹10个测区，每个测区16个回弹值。', 7, 'Y', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(442, 915, NULL, '回弹值（测试）', NULL, NULL, 'Mpa', 1, 160, 16, NULL, NULL, 7, 'Y', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(443, 916, NULL, '高', NULL, '[0,10]', 'mm', 1, 16, 2, NULL, '每一个门、窗洞口作为1个实测区，每层不少于4个实测区，累计实测实量16个实测区。各测量2次门、窗洞口宽度及高度净尺寸，取高度或宽度的2个实测值与设计值间的偏差最大值，作为判断高度或宽度实测指标合格率的1个计算点', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(444, 916, NULL, '宽', NULL, '[0,10]', 'mm', 1, 16, 2, NULL, '每一个门、窗洞口作为1个实测区，每层不少于4个实测区，累计实测实量16个实测区。各测量2次门、窗洞口宽度及高度净尺寸，取高度或宽度的2个实测值与设计值间的偏差最大值，作为判断高度或宽度实测指标合格率的1个计算点', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(445, 917, NULL, '高', NULL, '[0,10]', 'mm', 1, 16, 2, NULL, '每一个门、窗洞口作为1个实测区，每层不少于4个实测区，累计实测实量16个实测区。各测量2次门、窗洞口宽度及高度净尺寸，取高度或宽度的2个实测值与设计值间的偏差最大值，作为判断高度或宽度实测指标合格率的1个计算点', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(446, 917, NULL, '宽', NULL, '[0,10]', 'mm', 1, 16, 2, NULL, '每一个门、窗洞口作为1个实测区，每层不少于4个实测区，累计实测实量16个实测区。各测量2次门、窗洞口宽度及高度净尺寸，取高度或宽度的2个实测值与设计值间的偏差最大值，作为判断高度或宽度实测指标合格率的1个计算点', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(447, 918, NULL, '钢筋保护层厚度', NULL, '[-5,8]', 'mm', 1, 12, NULL, NULL, '一套房内墙柱、梁、板底3个测区，每个测区2个点，每点在50cm*50cm范围内满测，共计实测12点，测量结果与设计值相比。1个实测值作为判断该实测指标合格率的1个计算点，主体剔凿打磨位置为必选测区', 1, 'Y', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(448, 919, NULL, '高度', NULL, '[0,6]', 'mm', 1, 10, NULL, NULL, '相邻高度相减', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:20', '2022-09-07 17:46:20', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(449, 921, NULL, '平整度', NULL, '[0,8]', 'mm', 1, 30, NULL, NULL, '每一面墙都可以作为1个实测区，优先选用有门窗、过道洞口的墙面。测量部位选择正手墙面。墙面有门窗、过道洞口的，在各洞口45度斜交测一次，作为新增实测指标合格率的1个计算点', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(450, 922, NULL, '垂直度', NULL, '[0,5]', 'mm', 1, 30, NULL, NULL, '每一面墙都可以作为1个实测区，优先选用有门窗、过道洞口的墙面,测量部位选择正手墙面。应避开墙顶梁、墙底灰砂砖或砼反坎、墙体斜顶砖，消除其测量值的影响，如两米靠尺过高不易定位，可采用1米靠尺', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(451, 923, NULL, '高', NULL, '[-5,10]', 'mm', 1, 40, 2, NULL, '同一外门或外窗洞口均可作为1个实测区。不包括抹灰收口厚度，以砌体边对边，分别测量窗洞口宽度和高度各2次。取高度或宽度的2个实测值与设计值间的偏差最大值，作为判断宽度或高度实测指标合格率的1个计算点', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(452, 923, NULL, '宽', NULL, '[-5,10]', 'mm', 1, 40, 2, NULL, '同一外门或外窗洞口均可作为1个实测区。不包括抹灰收口厚度，以砌体边对边，分别测量窗洞口宽度和高度各2次。取高度或宽度的2个实测值与设计值间的偏差最大值，作为判断宽度或高度实测指标合格率的1个计算点', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(453, 924, NULL, '高', NULL, '[-5,10]', 'mm', 1, 40, 2, NULL, '同一外门或外窗洞口均可作为1个实测区。不包括抹灰收口厚度，以砌体边对边，分别测量窗洞口宽度和高度各2次。取高度或宽度的2个实测值与设计值间的偏差最大值，作为判断宽度或高度实测指标合格率的1个计算点。', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(454, 924, NULL, '宽', NULL, '[-5,10]', 'mm', 1, 40, 2, NULL, '同一外门或外窗洞口均可作为1个实测区。不包括抹灰收口厚度，以砌体边对边，分别测量窗洞口宽度和高度各2次。取高度或宽度的2个实测值与设计值间的偏差最大值，作为判断宽度或高度实测指标合格率的1个计算点。', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(455, 925, NULL, '重要预制或现浇构件', NULL, '1或0', NULL, 1, 30, NULL, NULL, '（1） 门窗框预制块：采用预制混凝土块、实心砖；空心砖墙体则在门窗洞边200mm内的孔洞须用细石混凝土填实；预制块或实心砖的宽度同墙厚；长度不小于200mm；高度应与砌块同高或砌块高度的1/2且不小于100mm；最上部（或最下部）的混凝土块中心距洞口上下边的距离为150～200mm，其余部位的中心距不大于600mm，且均匀分布。\\n（2） 现浇窗台板：宽同墙厚，高度≥120mm，每边入墙内≥200mm（不足200mm通长设置）；\\n（3） 洞口（大于600MM）的过梁：同墙宽，入墙不少于250mm', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(456, 926, NULL, '砌筑工序', NULL, '1或0', NULL, 1, 20, NULL, NULL, '（1） 无断砖、通缝、瞎缝；\\n（2） 墙顶空隙的补砌挤紧或灌缝间隔不少于7天；\\n（3） 不同基体（含各类线槽）镀锌钢丝网规格为10×10×0.7mm，基体搭接不小于100MM；挂网前墙体高低差部分采用水泥砂浆填补；\\n（4） 砌体墙灰缝须双面勾缝。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(457, 929, NULL, '预留洞离墙边距离', NULL, '[0,15]', 'mm', 1, 6, NULL, NULL, '每一个座便预留排水管孔作为一个实测区。本指标在墙面打灰饼或抹灰完成或装饰面完成阶段，且管孔填嵌固定后测量。实测前，通过图纸确定座便器预留排水管孔距，并将其管孔中心距换算为管外壁距距墙体装修完成面距离。如墙体装修面还未完成，现场测量值要减去2cm（墙面瓷砖铺贴预留厚度），以此作为偏差计算的数值进行合格性判断。每1个座便器预留排水管孔距的实测值与设计值之间的偏差值，作为判断该实测指标合格率的1个计算点。', 1, 'Y', 1, '1691', '1691', '2022-09-07 17:46:21', '2022-09-07 17:46:21', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(458, 931, NULL, '标高差', NULL, '[0,10]', 'mm', 1, 36, 6, NULL, '本指标原则上在抹灰阶段或底盒标高调整并固定完成测量。每一个功能房间作为1个实测区。在所选套房的某一功能房间内，使用激光扫平仪在墙面打出一条水平线。以该水平线为基准，用钢卷尺测量该房间内同一标高各电气底盒上口内壁至水平基准线的距离。选取其与水平基准线之间实测值的极差，作为判断该实测指标合格率的1个计算点。', 6, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(459, 932, NULL, '同一室内开关、插座面板高度偏差', NULL, '[0,5]', 'mm', 1, 96, 4, NULL, '（1） 每一个功能房间作为1个实测区，累计实测实量24个实测区。\\n（2） 在所选套房的某一功能房间内，使用激光扫平仪在墙面打出一条水平线。以该水平线为基准，用钢卷尺测量该房间内同一标高位各开关、插座面板至水平基准线的距离。选取其与水平基准线之间实测值的极差，作为判断该实测指标合格率的1个计算点。', 6, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(460, 933, NULL, '开关底盒距门口距离', NULL, '[150,200]', 'mm', 1, 24, NULL, NULL, '（1）每一个功能房间作为1个实测区，累计实测实量24个实测区\\n（2）在所选套房的某一功能房间内，用钢卷尺测量该房间内开关底盒盒边距门口距离，作为判断该实测指标合格率的1个计算点。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(461, 935, NULL, '喷淋间距', NULL, '1或0', NULL, 1, 6, NULL, NULL, '共选取2栋楼，每一层楼电梯前室为1个测区，合计6个测区，参照设计图纸要求对比现场是否符合，符合计1，不符合计0', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(462, 936, NULL, '喷淋数量', NULL, '1或0', NULL, 1, 6, NULL, NULL, '共选取2栋楼，每一层楼电梯前室为1个测区，合计6个测区，参照设计图纸要求对比现场是否符合，符合计1，不符合计0.', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(463, 939, NULL, '墙面表面平整度', NULL, '[0,4]', 'mm', 1, 45, NULL, NULL, '每一个功能房间地面都可以作为1个实测区。任选同一功..', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(464, 940, NULL, '墙面垂直度', NULL, '[0,4]', 'mm', 1, 45, NULL, NULL, '每一面墙作为1个实测区；每一测尺的实测值作为一个合格计算点。同一实测区内，当墙长度小于3米时，同一面墙距两端头竖向阴阳角约30cm位置，分别按以下原则实测2次：一是靠尺顶端接触到上部砼顶板位置时测1次垂直度，二是靠尺底端接触到下部地面位置时测1次垂直度；当墙长度大于3米时，在墙长度中间位置增测一次；具备实测条件的门洞口墙体垂直度为必测项。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(465, 941, NULL, '净高', NULL, '[-15,15]', 'mm', 1, 40, 5, '[-30,30]mm', '每一个功能房间作为1个实测区。实测前，所选套房必须完成地面找平层施工。同时还需了解所选套房的各房间结构楼板的设计厚度和建筑构造做法厚度等；各房间地面的4个角部区域，距地脚边线30cm附近各选取1点（避开吊顶位），在地面几何中心位选取1点，测量出找平层地面与天花顶板间的5个垂直距离，即5个室内净高实测值。用设计层高值减去结构楼板和地面找平层施工设计厚度值，作为该房间理论室内净高值。当实测值与设计值最大偏差值在【-30，+30】mm之间时，5个偏差值的实际值作为判断该实测指标合格率的5个计算点。当最大偏差值﹥30mm或﹤-30mm时，5个偏差值均按最大偏差值计。', 5, 'Y', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(466, 942, NULL, '顶板水平度', NULL, '[0,10]', 'mm', 1, 40, 5, '[0,15]mm', '已完成腻子的同一功能房间内顶板作为1个实测区。同一功能房间内顶板需已完成腻子施工；使用激光扫平仪，在实测板跨内打出一条水平基准线。同一实测区距顶板天花线30CM处位置选取4个角点，以及板跨几何中心位（若板单侧跨度较大可在中心部位增加1个测点），分别测量砼顶板与水平基准线之间的5个垂直距离。以最低点为基准点，计算另外四点与最低点之间的偏差，最大偏差值≤15mm时，5个偏差值（基准点偏差值以0计）的实际值作为判断该实测指标合格率的5个计算点。最大偏差值﹥15mm时，5个偏差值均按最大偏差值计，作为判断该实测指标合格率的5个计算点。', 3, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(467, 943, NULL, '阴阳角', NULL, '[0,4]', 'mm', 1, 30, NULL, NULL, '每面墙的任意一个阴角或阳角均可以作为1个实测区。选取对观感影响较大的阴阳角，同一个部位，从地面向上300mm和1500mm位置分别测量1次。2次实测值作为判断该实测指标合格率的2个计算点。', 1, 'Y', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(468, 944, NULL, '开间', NULL, '[-10,10]', 'mm', 1, 12, 2, NULL, '每一个功能房间的开间和进深分别各作为1个实测区，累计实测实量6个功能房间的12个实测区。同一实测区内按开间（进深）方向测量墙体两端的距离，各得到两个实测值，比较两个实测值与图纸设计尺寸，找出偏差的最大值。', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(469, 944, NULL, '进深', NULL, '[-10,10]', 'mm', 1, 12, 2, NULL, '每一个功能房间的开间和进深分别各作为1个实测区，累计实测实量6个功能房间的12个实测区。同一实测区内按开间（进深）方向测量墙体两端的距离，各得到两个实测值，比较两个实测值与图纸设计尺寸，找出偏差的最大值。', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(470, 945, NULL, '方正性', NULL, '[0,10]', 'mm', 1, 30, 3, NULL, '在同一测区内，实测前需用5米卷尺或激光扫平仪对弹出的两条方正度控制线进行校核，以短边墙为基准进行校核.无误后采用激光扫平仪打出十字线或吊线方式，沿墙长度方向分别测量3个位置（两端和中间）与控制线之间的距离（如果现场找不到控制线，可以一面带窗墙面为基准，用仪器引出两条辅助方正控制线,）。选取3个实测值之间的极差，作为判断该实测指标合格率的1个计算点。', 5, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(471, 946, NULL, '毛坯房交付地面、龙骨地板基层、瓷砖或石材地面', NULL, '[0,4]', 'mm', 1, 30, NULL, NULL, '每户选取的功能房间作为1个实测区，每户选取2个实测区，总计10个实测区。 任选同一功能房间地面的2个对角区域，按与墙面夹角平放靠尺测量2次，加上房间中部区域测量一次，共测量3次。客/餐厅或较大房间地面的中部区域需加测1次。同一功能房间内的3或4个地面平整度实测值', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:22', '2022-09-07 17:46:22', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(472, 946, NULL, '装修房直铺地板交付面基层地面', NULL, '[0,3]', 'mm', 1, 30, NULL, NULL, '每户选取的功能房间作为1个实测区，每户选取2个实测区，总计10个实测区。 任选同一功能房间地面的2个对角区域，按与墙面夹角平放靠尺测量2次，加上房间中部区域测量一次，共测量3次。客/餐厅或较大房间地面的中部区域需加测1次。同一功能房间内的3或4个地面平整度实测值', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(473, 947, NULL, '地面水平度', NULL, '[0,10]', 'mm', 1, 40, 5, '[0,15]mm', '每一个功能房间地面都可以作为1个实测区。使用激光扫平仪，在实测板跨内打出一条水平基准线。同一实测区地面的4个角部区域，距地脚边线30cm以内各选取1点，在地面几何中心位选取1点，分别测量找平层地面与水平基准线之间的5个垂直距离。以最低点为基准点，计算另外四点与最低点之间的偏差。偏差值≤10mm时，该实测点合格；最大偏差值≤15mm时，5个偏差值（基准点偏差值以0计）的实际值作为判断该实测指标合格率的5个计算点。最大偏差值﹥15mm时，5个偏差值均按最大偏差值计，作为判断该实测指标合格率的5个计算点。', 3, 'N', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(474, 948, NULL, '裂缝/空鼓/起砂', NULL, '1或0', NULL, 1, 10, NULL, NULL, '所选户型内每一自然间作为1个实测区。每一自然间内所有地面全检。1个实测区取1个实测值。1个实测值作为1个合格率计算点。所选2套房累计8个实测区，不满足8个时，需增加实测套房数。 ', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(475, 949, NULL, '高度', NULL, '[-10,10]', 'mm', 1, 20, 2, NULL, '个户内门洞都作为1个实测区。实测前需了解所选套房各户内门洞口尺寸。实测前户内门洞口侧面需完成抹灰收口和地面找平层施工，以确保实测值的准确性。', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(476, 949, NULL, '宽度', NULL, '[-10,10]', 'mm', 1, 20, 2, NULL, '个户内门洞都作为1个实测区。实测前需了解所选套房各户内门洞口尺寸。实测前户内门洞口侧面需完成抹灰收口和地面找平层施工，以确保实测值的准确性。', 4, 'Y', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(477, 950, NULL, '厚度', NULL, '[0,5]', 'mm', 1, 24, 3, NULL, '每一个户内门洞，累计8个实测区。实测时，门洞口等测量部位需完成抹灰或装饰收口。墙厚则左、右、顶边各测量一次，3个测量值与设计值之间偏差的最大值，作为墙厚偏差的1个实测值。每一个实测值作为判断该实测指标合格率的1个计算点，一个测区有三个实测值，一个实测点作为一个合格率计算点。', 5, 'Y', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(478, 951, NULL, '瓷砖收口窗', NULL, '[0,4]', 'mm', 1, 30, 3, NULL, '任一樘外门窗都作为一个实测区。累计20个实测区，其中卫生间、厨房等四边瓷砖收口外窗实测区为10个；实测时，外墙窗框等测量部位需完成抹灰或装饰收口。外墙平窗框内侧墙体，在窗框侧面中部各测量2次墙体厚度和沿着竖向窗框尽量在顶端位置测量1次墙体厚度。这3次实测值之间极差值作为判断该实测指标合格率的1个计算点。外墙凸窗框内侧墙体，沿着与内墙面垂直方向，分别测量凸窗台面两端头部位窗框与内墙抹灰完成面之间的距离。2个实测值之间极差值作为判断该实测指标合格率的1个计算点。', 6, 'N', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(479, 951, NULL, '非瓷砖收口窗', NULL, '[0,4]', 'mm', 1, 30, 3, NULL, '任一樘外门窗都作为一个实测区。累计20个实测区，其中卫生间、厨房等四边瓷砖收口外窗实测区为10个；实测时，外墙窗框等测量部位需完成抹灰或装饰收口。外墙平窗框内侧墙体，在窗框侧面中部各测量2次墙体厚度和沿着竖向窗框尽量在顶端位置测量1次墙体厚度。这3次实测值之间极差值作为判断该实测指标合格率的1个计算点。外墙凸窗框内侧墙体，沿着与内墙面垂直方向，分别测量凸窗台面两端头部位窗框与内墙抹灰完成面之间的距离。2个实测值之间极差值作为判断该实测指标合格率的1个计算点。', 6, 'N', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(480, 952, NULL, '裂缝空鼓', NULL, '1或0', NULL, 1, 40, NULL, NULL, '所选户型内每一自然间作为1个实测区。每一自然间内所有墙体全检。1个实测区取1个实测值。1个实测值作为1个合格率计算点。所选2套房累计40个实测区，不满足40个时，需增加实测套房数。 ', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(481, 954, NULL, '防水卷材厚度', NULL, '1或0', NULL, 1, 2, NULL, NULL, '现场随机抽查2卷材料或涂层，每卷材料测量2次与合同要求进行比较，涂层厚度与设计值比较。不同品牌、不同部位均要覆盖。涂层现场取样，检查方式：游标卡尺测量。最小厚度不小于设计厚度80％D，平均厚度不小于设计厚度90％D。其中一项不符合要求或涂膜类无法成膜视为不合格，按实际测量值记录', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(482, 955, NULL, '长边搭接长度', NULL, '1或0', NULL, 1, 10, NULL, NULL, '已施工完成区域随机抽查10处，与规范要求进行比较。长边短边搭接均不低于10cm。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(483, 956, NULL, '短边搭接长度', NULL, '1或0', NULL, 1, 10, NULL, NULL, '已施工完成区域随机抽查10处，与规范要求进行比较。长边短边搭接均不低于10cm。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:23', '2022-09-07 17:46:23', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(484, 957, NULL, '阴阳角做圆', NULL, '1或0', NULL, 1, 10, NULL, NULL, '已施工完成区域随机抽查10处，合格记为”0“，反之则为“1”。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(485, 958, NULL, '防水保护层', NULL, '1或0', NULL, 1, 4, NULL, NULL, '已施工完成区域随机抽查4初，与给定值进行比较，合格记为”0“，反之则为“1”。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(486, 959, NULL, '卫生间涂膜厚度', NULL, '1或0', NULL, 1, 8, NULL, NULL, '共8个测区，现场取样，实测值与设计值进行对比。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(487, 960, NULL, '找坡坡度', NULL, '1或0', NULL, 1, 4, NULL, NULL, '已施工完成区域随机抽查4处，合格记为”0“，反之则为“1”。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(488, 961, NULL, '滴水线、鹰嘴等', NULL, '1或0', NULL, 1, 10, NULL, NULL, '已施工完成区域随机抽查10处，合格记为”0“，反之则为“1”。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(489, 962, NULL, '窗框封堵', NULL, '1或0', NULL, 1, 4, NULL, NULL, '已施工完成区域随机抽查10处，合格记为”0“，反之则为“1”。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(490, 963, NULL, '防水卷材厚度', NULL, '1或0', NULL, 1, 2, NULL, NULL, '现场随机抽查2卷材料，与合同要求进行比较。不同品牌、不同部位均要覆盖。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(491, 964, NULL, '长边搭接长度', NULL, '1或0', NULL, 1, 10, NULL, NULL, '已施工完成区域随机抽查10处，与规范要求进行比较。长边短边搭接不低于10cm。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(492, 965, NULL, '女儿墙及风井等出屋面构筑物根部砼坎质量', NULL, '1或0', NULL, 1, 10, NULL, NULL, '已施工完成区域随机抽查10处，与规范要求进行比较。长边短边搭接不低于10cm。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(493, 966, NULL, '女儿墙', NULL, '1或0', NULL, 1, 10, NULL, NULL, '已施工完成区域随机抽查10处，合格记为”0“，反之则为“1”。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(494, 967, NULL, '天沟、雨水口周边坡度', NULL, '1或0', NULL, 1, 5, NULL, NULL, '已施工完成区域随机抽查5处，合格记为”0“，反之则为“1”。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(495, 969, NULL, '入户门', NULL, '1或0', NULL, 1, 6, NULL, NULL, '共选取2栋楼，每栋楼每一户为1个测区，共6个测区', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:24', '2022-09-07 17:46:24', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(496, 970, NULL, '外窗玻璃', NULL, '1或0', NULL, 1, 6, NULL, NULL, '共选取2栋楼，每栋楼每一户、每一扇玻璃为1个测区，共6个测区', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(497, 971, NULL, '阳台门', NULL, '1或0', NULL, 1, 6, NULL, NULL, '共选取2栋楼，每栋楼每一户、每一樘阳台门为1个测区，共7个测区', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(498, 972, NULL, '室内墙面阳角', NULL, '1或0', NULL, 1, 6, NULL, NULL, '共选取2栋楼，每栋楼每一户、每一处室内阳角为1个测区，共7个测区', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(499, 973, NULL, '木地板', NULL, '1或0', NULL, 1, 6, NULL, NULL, '共选取2栋楼，每栋楼每一户、每一功能间为1个测区，共7个测区', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(500, 974, NULL, '浴缸', NULL, '1或0', NULL, 1, 6, NULL, NULL, '共选取2栋楼，每栋楼每一户为1个测区，共6个测区', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(501, 976, NULL, '平整度', NULL, '[0,3]', 'mm', 1, 45, NULL, NULL, '每一面墙作为1个实测区，累计15个实测区。每一测尺实测值作为一个合格计算点。测量方法同抹灰阶段的墙面平整度。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(502, 977, NULL, '垂直度', NULL, '[0,3]', 'mm', 1, 45, NULL, NULL, '每一面墙作为1个实测区，累计15个实测区。每一测尺实测值作为一个合格计算点。测量方法同抹灰阶段的墙面垂直度。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(503, 978, NULL, '阴阳角', NULL, '[0,3]', 'mm', 1, 30, NULL, NULL, '每面墙的任意一个阴角或阳角均可以作为1个实测区。其他同抹灰阶段的阴阳角方正。', 1, 'Y', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(504, 979, NULL, '顶棚水平度', NULL, '[0,10]', 'mm', 1, 40, 5, '[0,15]mm', '每一个功能房间作为1个实测区，累计实测实量10个实测区。使用激光扫平仪，在实测房间内打出一条水平基准线。同一顶棚（吊顶）内距天花线30cm位置处选取4个角点，以及板跨几何中心位（若板单侧跨度较大可在中心部位增加1个测点），分别测量出与水平基准线之间的5个垂直距离。以最低点为基准点，计算另外四点与最低点之间的偏差，最大偏差值≤15mm时，5个偏差值（基准点偏差值以0计）的实际值作为判断该实测指标合格率的5个计算点。最大偏差值﹥15mm时，5个偏差值均按最大偏差值计，作为判断该实测指标合格率的5个计算点', 3, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(505, 980, NULL, '裂缝/空鼓', NULL, '1或0', NULL, 1, 20, NULL, NULL, '所选户型内每一自然间作为1个实测区。所选套房内所有墙体和天花全检。1个实测值作为1个实测合格率计算点。墙面检查裂缝/空鼓指标，天花则只检查裂缝指标。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(506, 982, NULL, '平整度', NULL, '[0,3]', 'mm', 1, 12, NULL, NULL, '每套房内厨房、卫生间、阳露台的同一面墙都可作为1个实测区，取各墙面左上及右下2个角，按45度角斜放靠尺，分别测量1次，2次测量值作为该实测指标合格率的2个计算点', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(507, 983, NULL, '瓷砖墙垂直度', NULL, '[0,2]', 'mm', 1, 12, NULL, NULL, '每一套房内厨房、卫生间、阳露台都可作为1个实测区。每一个实测区测量2个点，其实测值作为该实测指标合格率的2个计算点', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:25', '2022-09-07 17:46:25', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(508, 984, NULL, '石材墙垂直度', NULL, '[0,3]', 'mm', 1, 12, NULL, NULL, '每一套房内厨房、卫生间、阳露台都可作为1个实测区。每一个实测区测量2个点，其实测值作为该实测指标合格率的2个计算点', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(509, 985, NULL, '阴阳角', NULL, '[0,3]', 'mm', 1, 12, NULL, NULL, '房内厨房、卫生间、阳露台的每一个阴角或阳角都可以作为1个实测区。一个实测区，按30CM、150CM分别测量1次，2次测量值作为该实测指标合格率的2个计算点', 1, 'Y', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(510, 986, NULL, '高低差', NULL, '[0,0.5]', 'mm', 1, 12, NULL, NULL, '该指标宜在装修收尾阶段测量。每一套房内厨房、卫生间、阳台或露台的墙面都可以作为1个实测区。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(511, 987, NULL, '裂缝 空鼓', NULL, '1或0', NULL, 1, 10, NULL, NULL, '所选户型内有饰面砖墙面的厨房、卫生间等每一自然间作为1个实测区。所选套房内所有墙体全检。1个实测区的实测值作为1个实测合格率计算点。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(512, 989, NULL, '平整度', NULL, '[0,2]', 'mm', 1, 12, NULL, NULL, '地漏的汇水区域不测饰面砖地面表面平整度。每一功能房间饰面砖地面都可以作为1个实测区，累计实测实量6个实测区。每一功能房间地面（不包括厨卫间）的4个角部区域，任选两个角与墙面夹角45度平放靠尺共测量2次。客餐厅或较大房间地面的中部区域需加测1次。这2或3次实测值作为判断该实测指标合格率的2或3个计算点。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(513, 990, NULL, '高低差', NULL, '[0,0.5]', 'mm', 1, 12, NULL, NULL, '每一功能房间饰面砖地面都可以作为1个实测区。在每一饰面砖地面目测选取2条疑似高低差最大的饰面砖接缝。用钢尺或其他辅助工具紧靠相邻两饰面砖跨过接缝，用0.5MM钢塞片插入钢尺与饰面砖之间的缝隙。如能插入，则该测量点不合格；反之则该测量点合格。2条接缝高低差的测量值，分别作为判断该实测指标合格率的2个计算点。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(514, 991, NULL, '裂缝 空鼓', NULL, '1或0', NULL, 1, 10, NULL, NULL, '实测时，厨房和卫生间户内墙面测量部位需完成贴砖工程。所选户型内有饰面砖地面的每1个自然间，如厨房、卫生间、走道等，都作为1个实测区。1个实测点作为判断该实测指标合格率的1个计算点。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(515, 993, NULL, '垂直度', NULL, '[0,4]', 'mm', 1, 20, 2, NULL, '每一樘门框都可以作为1个实测区。分别测量一樘门门框的正面和侧面垂直度，共有2个实测结果，取其中实测值较大的，作为该实测指标合格率的1个计算点。', 4, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(516, 995, NULL, '拼缝宽', NULL, '[0,0.3]', 'mm', 1, 10, NULL, NULL, '该指标宜在窗扇安装完、窗框保护膜拆除完的装修收尾阶段测量。户内每一樘门或窗都可以作为1个实测区.在同一铝合金门或窗的窗框、窗扇，目测选取1条疑似缝隙宽度最大的型材拼接缝。用0.2MM钢塞片插入型材拼接缝隙，如能插入，则该测量点不合格；反之则该测量点合格。不合格点均按0.5MM记录，合格点均按0.1MM记录。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(517, 996, NULL, '高低差', NULL, '[0,0.3]', 'mm', 1, 10, NULL, NULL, '该指标宜在窗扇安装完、窗框保护膜拆除完的装修收尾阶段测量。户内每一樘门或窗都可以作为1个实测区.目测选取1条疑似高低差最大的型材拼接缝，用钢尺跨过接缝以0.3MM钢塞片插入钢尺与型材之间的缝隙，如能插入，则该测量点不合格；反之则该测量点合格。1条接缝高低差的测量值，作为该实测指标合格率的1个计算点。不合格点均按0.5MM记录，合格点均按0.2MM记录。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(518, 997, NULL, '垂直度', NULL, '[0,2.5]', 'mm', 1, 20, NULL, NULL, '户内每一樘门或窗都可以作为1个实测区. 用2M靠尺分别测量每一樘铝合金门或窗两边竖框垂直度，取2个实测值中的最大值，作为该实测指标合格率的1个计算点', 4, 'N', 1, '1691', '1691', '2022-09-07 17:46:26', '2022-09-07 17:46:26', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(519, 998, NULL, '窗框固定', NULL, '1或0', NULL, 1, 15, NULL, NULL, '户内每一扇外门窗都可以作为1个实测区，累计实测实量15个实测区。1个实测区作为1个实测合格率计算点。同一套房内的所有外门窗全测。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(520, 999, NULL, '边框收口与塞缝', NULL, '1或0', NULL, 1, 15, NULL, NULL, '同一套房内任何1个外门窗洞口都可以作为1个实测区，同一实测区取1个实测点，实测值作为合格率1个计算点。同一套房内的外门窗需全检。', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(521, 1001, NULL, '平整度', NULL, '[0,2]', 'mm', 1, 24, NULL, NULL, '每一功能房间木地板地面都可以作为1个实测区。任选同一功能房间地面的2个对角区域，按与墙面夹角45度平放靠尺测量2次，加上房间中部区域测量一次，共测量3次。客\\餐厅或较大房间地面的中部区域需加测1次。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(522, 1002, NULL, '缝隙宽度', NULL, '[0,0.5]', 'mm', 1, 12, NULL, NULL, '该指标宜在装修收尾阶段测量。每一功能房间木地板地面都可以作为1个实测区。目测选取2条疑似高低差最大的地板条接缝，分别用钢尺紧靠相邻两地板条跨过接缝，以0.5mm厚度的钢塞片插入钢尺与地板条之间的缝隙；如能插入，则该测量点不合格；反之则该测量点合格。不合格点均按0.6MM记录，合格点均按0.4MM记录。2个实测值均作为该实测指标合格率的2个计算点。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(523, 1003, NULL, '接缝高低', NULL, '[0,0.5]', 'mm', 1, 12, NULL, NULL, '该指标宜在装修收尾阶段测量。每一功能房间木地板地面都可以作为1个实测区。目测选取2条疑似高低差最大的地板条接缝；分别用钢塞片插入钢尺与地板条之间的缝隙；如能插入，则该测量点不合格；反之则该测量点合格。2个实测值均作为计算点。不合格点均按规范标准加0.1MM记录，合格点均按规范标准减0.1MM记录。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(524, 1004, NULL, '地板水平度', NULL, '[0,10]', 'mm', 1, 40, 5, '[0,15]mm', '每一功能房间木地板地面都可以作为1个实测区。使用激光扫平仪，在实测房间内打出一条水平基准线。同一实测区地面的四个角距地脚边线30CM以内各选取1点，在地面几何中心位选取1点，分别测量出地板与水平基准线之间的5个垂直距离。以最低点为基准点，计算另外四点与最低点之间的偏差，最大偏差值≤15mm时，5个偏差值（基准点偏差值以0计）的实际值作为判断该实测指标合格率的5个计算点。最大偏差值﹥15mm时，5个偏差值均按最大偏差值计，作为判断该实测指标合格率的5个计算点。', 3, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(525, 1007, NULL, '焊缝外观是否合格', NULL, '1或0', NULL, 1, 10, NULL, NULL, '合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(526, 1008, NULL, '每条焊缝探伤', NULL, '1或0', NULL, 1, 10, NULL, NULL, '合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(527, 1010, NULL, '防火涂料涂层厚度', NULL, '1或0', NULL, 1, 20, NULL, NULL, '80%以上部位符合设计厚度要求，且最薄处大于设计要求的85%。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(528, 1012, NULL, '柱截面尺寸', NULL, '1或0', NULL, 1, 20, NULL, NULL, '连接处±3mm；非连接处±4mm。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(529, 1013, NULL, '钢管构件直径', NULL, '1或0', NULL, 1, 10, NULL, NULL, '±d/500mm；±5mm', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(530, 1014, NULL, '箱型截面对角线差', NULL, '1或0', NULL, 1, 10, NULL, NULL, '箱型截面对角线差5mm。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(531, 1016, NULL, '单层柱垂直度', NULL, '1或0', NULL, 1, 10, NULL, NULL, 'H≤10m H/1000；H＞10m H/1000且不应大于25mm。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:27', '2022-09-07 17:46:27', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(532, 1017, NULL, '多层柱垂直度', NULL, '1或0', NULL, 1, 10, NULL, NULL, '单节柱 H/1000且不应大于25mm；柱全高 35mm。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(533, 1018, NULL, '柱脚底座中心线对定位轴线的偏移', NULL, '1或0', NULL, 1, 10, NULL, NULL, '柱脚底座中心线对定位轴线的偏移5mm。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(534, 1020, NULL, '柱对接错口', NULL, '[0,3]', 'mm', 1, 10, NULL, NULL, '错口3mm以内合格，否则不合格。', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(535, 1023, NULL, '横向构件水平度', NULL, '1或0', NULL, 1, 10, NULL, NULL, '长度不大于2000≤2mm；长度大于2000≤3mm。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(536, 1024, NULL, '两相邻面板之间接缝高低差', NULL, '[0,1]', 'mm', 1, 10, NULL, NULL, '两相邻面板之间接缝高低差≤1.0mm', 1, 'N', 1, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(537, 1025, NULL, '分隔框对角线差', NULL, '1或0', NULL, 1, 10, NULL, NULL, '长度不大于2000≤3mm；长度大于2000≤3.5mm。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(538, 1027, NULL, '密封胶', NULL, '1或0', NULL, 1, 10, NULL, NULL, '板缝注胶应饱满、密实、连续、均匀、无气泡；表面应横平竖直、深浅一致、宽窄均匀、光滑顺直。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
INSERT INTO app_quality_actual_measure_item
(id, measure_type_id, tenant_id, item_name, item_code, qualified_standard, unit, standard_type, cal_point_num, group_cal_point_num, breaking_point_standard, measure_memo, `algorithm`, design_value, sort_no, creator, modifier, create_time, modify_time, delete_state)
VALUES(539, 1029, NULL, '玻璃', NULL, '1或0', NULL, 1, 20, NULL, NULL, '表面无划痕（每平方米划痕＜100mm，不超过8条）、破损、汽包、夹渣、爆裂现象，并且有ccc标志，玻璃四周无崩边现象。合格填0，不合格填1', 2, 'N', 1, '1691', '1691', '2022-09-07 17:46:28', '2022-09-07 17:46:28', 1);
