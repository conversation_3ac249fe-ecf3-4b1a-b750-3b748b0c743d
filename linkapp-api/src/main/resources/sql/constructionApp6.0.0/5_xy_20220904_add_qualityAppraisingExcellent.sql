-- 质量评优信息表
CREATE TABLE `app_quality_appraising_excellent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id` varchar(100) DEFAULT NULL COMMENT 'tenantId',
  `imgs` varchar(2000) DEFAULT NULL COMMENT '问题照片，多张以，隔开',
  `check_part_id` int(11) DEFAULT NULL COMMENT '施工部位id，来源检查部位',
  `problem_type_id` int(11) DEFAULT NULL COMMENT '问题类型id，来源问题类型库',
  `content_` varchar(1000) DEFAULT NULL COMMENT '优秀做法',
  `sub_org_type` int(11) DEFAULT NULL COMMENT '分包单位类型，1劳务、2专业',
  `sub_org_id` varchar(32) DEFAULT NULL COMMENT '分包单位id',
  `sub_org_name` varchar(200) DEFAULT NULL COMMENT '分包单位名称',
  `sub_group_id` varchar(100) DEFAULT NULL COMMENT '分包班组id',
  `sub_group_name` varchar(200) DEFAULT NULL COMMENT '分包班组名称',
  `noticer_ids` varchar(1000) DEFAULT NULL COMMENT '通知人ids',
  `noticer_names` varchar(1000) DEFAULT NULL COMMENT '通知人名称,隔开',
  `appraising_time` datetime DEFAULT NULL COMMENT '评优时间',
  `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
  `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
  PRIMARY KEY (`id`)
  )  COMMENT='质量评优信息';

-- 质量评优相册菜单
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('806', '80', '质量评优相册', 'qualityAppraisingExcellentPhotoAlbum:select', NULL, 3, NULL, NULL, 80.6, 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 质量评优菜单
INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('6600067', '660006', '质量评优', 'qualityAppraisingExcellent', '质量评优', 1, NULL, NULL, 660006.70, 1, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 1);
