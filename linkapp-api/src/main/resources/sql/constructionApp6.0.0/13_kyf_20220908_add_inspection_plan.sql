-- ----------------------------
-- 自检计划
-- ----------------------------
CREATE TABLE `app_inspection_plan`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `name_`          varchar(32)   DEFAULT NULL COMMENT '名称',
    `type_`          int(2)        DEFAULT NULL COMMENT '类别(1定期检查2月度检查)',
	`basis_`         int(2)        DEFAULT NULL COMMENT '检查依据(1自由检查)',
	`user_ids_`      varchar(500)  DEFAULT NULL COMMENT '检查人员id,逗号分隔',
    `start_time_`    datetime      DEFAULT NULL COMMENT '计划开始时间',
	`end_time_`      datetime      DEFAULT NULL COMMENT '计划结束时间',
	`status_`        int(2)        DEFAULT NULL COMMENT '状态(1未开始2检查中3已完成)',
	`publishing_`    int(2)        DEFAULT NULL COMMENT '发布状态(1未发布2已发布3中止)',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='自检计划';

-- ----------------------------
-- 自检任务
-- ----------------------------
CREATE TABLE `app_inspection_task`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `plan_id_`       int(11)       DEFAULT NULL COMMENT '计划id',
    `start_time_`    datetime      DEFAULT NULL COMMENT '计划开始时间',
	`end_time_`      datetime      DEFAULT NULL COMMENT '计划结束时间',
	`status_`        int(2)        DEFAULT NULL COMMENT '状态(1未开始2已执行)',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='自检任务';

-- 安全检查添加任务id/兼容巡检记录
alter table `app_hidden_danger` add column `task_id_` int(11) DEFAULT NULL COMMENT '任务id';
alter table `app_hidden_danger` add column `type_` int(11) DEFAULT '1' COMMENT '类别(1隐患2巡检记录)';
alter table `app_hidden_danger` modify column `supplement_remarks` varchar(500) DEFAULT NULL COMMENT '补充说明/备注说明';
alter table `app_hidden_danger` modify column `rectify_requirements` varchar(255) DEFAULT NULL COMMENT '整改要求/检查项';
alter table `app_hidden_danger` modify column `status` int(2) DEFAULT NULL COMMENT '状态：0：待整改，1：待复查，2：合格，3：无需整改';
-- ----------------------------
-- 新增菜单
-- ----------------------------
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('1008', '1000', '项目自检', 'inspectionPlan', NULL, 2, NULL, NULL, 10.18, 1, 'manage/inspectionPlan', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(10081,1008, '添加', 'inspectionPlan:add', NULL, 3, NULL, NULL, 1008.1, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(10082,1008, '删除', 'inspectionPlan:delete', NULL, 3, NULL, NULL, 1008.2, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);
-- app菜单
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(6600024,660002, '项目自检', 'peojectInspectionPlan','项目自检', 1, NULL, NULL, 660002.4, 1, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 1);