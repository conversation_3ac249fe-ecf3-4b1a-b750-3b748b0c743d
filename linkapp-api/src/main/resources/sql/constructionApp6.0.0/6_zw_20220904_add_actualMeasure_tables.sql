-- 实测实量信息
CREATE TABLE `app_quality_actual_measure_info`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`       varchar(100)  DEFAULT NULL COMMENT '租户id',
    `position_id`     int(11) DEFAULT NULL COMMENT '部位id',
    `measure_time`    datetime      DEFAULT NULL COMMENT '测量时间',
    `sub_org_id`      varchar(100)  DEFAULT NULL COMMENT '分包单位id',
    `sub_group_id`    varchar(100)  DEFAULT NULL COMMENT '分包单位分组id',
    `sub_org_type`    int(1) DEFAULT NULL COMMENT '分包单位类型，1劳务、2专业',
    `actual_org_type` int(2) DEFAULT NULL COMMENT '实测单位类型，1总包、2总包公司、3监理、4业主',
    `detail_memo`     varchar(2000) DEFAULT NULL COMMENT '详情说明',
    `storage_type`    int(1) DEFAULT NULL COMMENT '存储类型，1暂存，2已存储',
    `sub_org_name`    varchar(300)  DEFAULT NULL COMMENT '分包单位名称',
    `sub_group_name`  varchar(300)  DEFAULT NULL COMMENT '分包单位分组名称',
    `creator`         varchar(100)  DEFAULT NULL COMMENT '创建人',
    `modifier`        varchar(100)  DEFAULT NULL COMMENT '修改人',
    `create_time`     datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_time`     datetime      DEFAULT NULL COMMENT '修改时间',
    `delete_state`    int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY               `app_quality_actual_measure_info_position_id_IDX` (`position_id`) USING BTREE,
    KEY               `app_quality_actual_measure_info_sub_org_id_IDX` (`sub_org_id`) USING BTREE,
    KEY               `app_quality_actual_measure_info_sub_group_id_IDX` (`sub_group_id`) USING BTREE,
    KEY               `app_quality_actual_measure_info_measure_time_IDX` (`measure_time`) USING BTREE,
    KEY               `app_quality_actual_measure_info_tenant_id_IDX` (`tenant_id`) USING BTREE
) ENGINE=InnoDB COMMENT='实测实量信息';

-- 实测实量详情
CREATE TABLE `app_quality_actual_measure_details`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `info_id`       bigint(20) DEFAULT NULL COMMENT '实测实量主记录id',
    `type_id`       bigint(20) DEFAULT NULL COMMENT '实测实量分类id',
    `imgs`          varchar(2000)  DEFAULT NULL COMMENT '实测实量照片，多张以,隔开',
    `pass_rate`     decimal(10, 4) DEFAULT NULL COMMENT '合格率',
    `actual_num`    int(11) DEFAULT NULL COMMENT '实测点数',
    `qualified_num` int(11) DEFAULT NULL COMMENT '合格点数',
    `breaking_num`  int(11) DEFAULT NULL COMMENT '爆点数',
    `creator`       varchar(100)   DEFAULT NULL COMMENT '创建人',
    `modifier`      varchar(100)   DEFAULT NULL COMMENT '修改人',
    `create_time`   datetime       DEFAULT NULL COMMENT '创建时间',
    `modify_time`   datetime       DEFAULT NULL COMMENT '修改时间',
    `delete_state`  int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY             `app_quality_actual_measure_details_info_id_IDX` (`info_id`) USING BTREE,
    KEY             `app_quality_actual_measure_details_type_id_IDX` (`type_id`) USING BTREE
) ENGINE=InnoDB COMMENT='实测实量详情';

-- 实测实量测试记录
CREATE TABLE `app_quality_actual_measure_value`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `info_id`         bigint(20) DEFAULT NULL COMMENT '实测实量id，冗余',
    `details_id`      bigint(20) DEFAULT NULL COMMENT '详情id',
    `item_id`         bigint(20) DEFAULT NULL COMMENT '实测实量项id',
    `benchmark_value` varchar(20)  DEFAULT NULL COMMENT '基准值',
    `group_no`        varchar(100) DEFAULT NULL COMMENT '分组编号、名称',
    `actual_value`    varchar(20)  DEFAULT NULL COMMENT '实测值',
    `actual_state`    int(11) DEFAULT NULL COMMENT '实测状态，1合格，2不合格，3爆点',
    `sort_no`         int(11) DEFAULT NULL COMMENT '排序号',
    `creator`         varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`        varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`     datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`     datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state`    int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY               `app_quality_actual_measure_value_details_id_IDX` (`details_id`) USING BTREE,
    KEY               `app_quality_actual_measure_value_info_id_IDX` (`info_id`) USING BTREE,
    KEY               `app_quality_actual_measure_value_item_id_IDX` (`item_id`) USING BTREE
) ENGINE=InnoDB COMMENT='实测实量测试记录';
