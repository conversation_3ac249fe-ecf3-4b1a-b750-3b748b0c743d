-- ----------------------------
-- 人员黑名单
-- ----------------------------
CREATE TABLE `app_user_blacklist`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `user_id_`       varchar(32)   DEFAULT NULL COMMENT 'userId',
    `type_`          int(2)        DEFAULT NULL COMMENT '人员类别(1工人2班组长)',
    `reason_`        varchar(500)  DEFAULT NULL COMMENT '黑名单原因',
	`url_`           text          COMMENT  '附件',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='人员黑名单';

-- ----------------------------
-- 黑名单记录
-- ----------------------------
CREATE TABLE `app_user_blacklist_record`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `user_id_`       varchar(32)   DEFAULT NULL COMMENT 'userId',
    `type_`          int(2)        DEFAULT NULL COMMENT '类别(1加入黑名单,2移出黑名单)',
    `reason_`        varchar(500)  DEFAULT NULL COMMENT '说明',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='黑名单记录';

-- ----------------------------
-- 新增菜单
-- ----------------------------
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('610', '60', '评价中心', 'blacklist', NULL, 2, NULL, NULL, 61, 1, 'manage/blacklist', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(6101,610, '添加', 'blacklist:add', NULL, 3, NULL, NULL, 610.1, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(6102,610, '移出黑名单', 'blacklist:delete', NULL, 3, NULL, NULL, 610.2, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);

CREATE INDEX app_user_certificate_user_id__IDX USING BTREE ON app_user_certificate (user_id_);
CREATE INDEX app_user_blacklist_tenant_id__IDX USING BTREE ON app_user_blacklist (tenant_id_);
CREATE INDEX app_user_blacklist_user_id__IDX USING BTREE ON app_user_blacklist (user_id_);
