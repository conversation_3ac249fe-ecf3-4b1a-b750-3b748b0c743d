-- web端菜单
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_,
 tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES ('110701', '1107', '删除', 'actualMeasurementAccount_delete', NULL, 3, NULL, NULL, 1107.01, 2, NULL, NULL, '1',
        '2022-09-13 09:00:00', NULL, NULL, NULL, NULL, 0);
-- APP端菜单
INSERT INTO linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('6600068', '660006', '新增实测实量', 'addActualMeasurement', '新增实测实量', 1, NULL, NULL, 660006.80, 1, NULL, NULL, '1', '2022-07-22 17:37:58', NULL, NULL, NULL, NULL, 1);
INSERT INTO linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('6600069', '660006', '实测实量列表', 'actualMeasurementList', '实测实量列表', 1, NULL, NULL, 660006.90, 1, NULL, NULL, '1', '2022-07-22 17:37:58', NULL, NULL, NULL, NULL, 1);
