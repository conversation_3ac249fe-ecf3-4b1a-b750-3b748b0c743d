CREATE TABLE `app_quality_actual_measure_type`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`    varchar(100)  DEFAULT NULL COMMENT '租户id',
    `name`         varchar(200)  DEFAULT NULL COMMENT '分类名称',
    `full_name`    varchar(1000) DEFAULT NULL COMMENT '全分类名，以/隔开结尾',
    `full_id`      varchar(200)  DEFAULT NULL COMMENT '全分类id，以/隔开结尾',
    `parent_id`    bigint(20) DEFAULT NULL COMMENT '父级id',
    `code`         varchar(100)  DEFAULT NULL COMMENT '分类编码',
    `sort_no`      int(11) DEFAULT NULL COMMENT '排序号，1自增',
    `level`        int(11) DEFAULT NULL COMMENT '层级',
    `creator`      varchar(100)  DEFAULT NULL COMMENT '创建人',
    `modifier`     varchar(100)  DEFAULT NULL COMMENT '修改人',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `app_quality_actual_measure_type_tenant_id_IDX` (`tenant_id`) USING BTREE,
    KEY            `app_quality_actual_measure_type_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='实测实量分类';


CREATE TABLE `app_quality_actual_measure_item`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `measure_type_id`         bigint(20) DEFAULT NULL COMMENT '实测实量分类id',
    `tenant_id`               varchar(100) DEFAULT NULL COMMENT '租户id，冗余',
    `item_name`               varchar(200) DEFAULT NULL COMMENT '测量项名称',
    `item_code`               varchar(100) DEFAULT NULL COMMENT '测量项编码',
    `qualified_standard`      varchar(100) DEFAULT NULL COMMENT '合格标准',
    `unit`                    varchar(100) DEFAULT NULL COMMENT '单位',
    `standard_type`           int(1) DEFAULT NULL COMMENT '标准类型，1国标，2行标，3自定义',
    `cal_point_num`           int(11) DEFAULT NULL COMMENT '计算点数量',
    `group_cal_point_num`     int(11) DEFAULT NULL COMMENT '每组计算点',
    `breaking_point_standard` varchar(100) DEFAULT NULL COMMENT '爆点标准',
    `measure_memo`            varchar(500) DEFAULT NULL COMMENT '测量说明',
    `algorithm`               int(11) DEFAULT NULL COMMENT '算法，1、2、3、4',
    `design_value`            varchar(100) DEFAULT NULL COMMENT '设计值，Y、N',
    `sort_no`                 int(11) DEFAULT NULL COMMENT '排序号，1自增',
    `creator`                 varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`                varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`             datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`             datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state`            int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY                       `app_quality_actual_measure_item_measure_type_id_IDX` (`measure_type_id`) USING BTREE
) ENGINE=InnoDB COMMENT='实测实量项';

