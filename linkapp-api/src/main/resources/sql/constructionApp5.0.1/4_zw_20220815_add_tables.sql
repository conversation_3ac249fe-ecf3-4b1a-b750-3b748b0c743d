-- 危大工程字典
CREATE TABLE `app_dangerous_dict`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `memo`            varchar(300) DEFAULT NULL COMMENT '名称/描述',
    `parent_id`       bigint(20) DEFAULT NULL COMMENT '父级id',
    `dangerous_type`  int(1) DEFAULT NULL COMMENT '类型，1分类，2类别描述',
    `level`           int(1) DEFAULT NULL COMMENT '层级',
    `super_dangerous` int(1) DEFAULT NULL COMMENT '是否超危，0否，1是',
    `sort_no`         int(11) DEFAULT NULL COMMENT '排序号',
    `use_state`       int(1) DEFAULT NULL COMMENT '使用状态，1启用，0停用',
    `creator`         varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`        varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`     datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`     datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state`    int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY               `app_dangerous_project_dict_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB COMMENT='危大工程字典';

-- 危大工程信息
CREATE TABLE `app_dangerous_info`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`             varchar(100) DEFAULT NULL COMMENT 'tenant_id',
    `name`                  varchar(300) DEFAULT NULL COMMENT '危大工程名称',
    `describe_str`          varchar(500) DEFAULT NULL COMMENT '危大工程概述',
    `type_id`               bigint(20) DEFAULT NULL COMMENT '危大工程类别id',
    `type_describe_id`      bigint(20) DEFAULT NULL COMMENT '危大工程类别描述id',
    `super_dangerous`       int(1) DEFAULT NULL COMMENT '是否超危，0否，1是',
    `work_area_responsible` varchar(500) DEFAULT NULL COMMENT '工区责任人,多个以，隔开',
    `sub_area_responsible`  varchar(500) DEFAULT NULL COMMENT '分包责任人,多个以，隔开',
    `sub_responsible_org`   varchar(500) DEFAULT NULL COMMENT '责任分包单位,多个以，隔开',
    `plan_start_time`       datetime     DEFAULT NULL COMMENT '计划开始时间',
    `plan_end_time`         datetime     DEFAULT NULL COMMENT '计划结束时间',
    `actual_start_time`     datetime     DEFAULT NULL COMMENT '实际开始时间',
    `actual_end_time`       datetime     DEFAULT NULL COMMENT '实际结束时间',
    `program_state`         int(2) DEFAULT NULL COMMENT '方案状态，1未完成，2已完成',
    `construction_state`    int(2) DEFAULT NULL COMMENT '施工状态，1未开工，2在施，3完工',
    `creator`               varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`              varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`           datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`           datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state`          int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='危大工程信息';

-- 危大工程过程信息
CREATE TABLE `app_dangerous_process`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `dangerous_id`    bigint(20) DEFAULT NULL COMMENT '危大工程id',
    `process_name`    varchar(300) DEFAULT NULL COMMENT '过程名称',
    `step_no`         int(2) DEFAULT NULL COMMENT '步骤号',
    `process_state`   int(1) DEFAULT NULL COMMENT '状态',
    `change_end_time` datetime     DEFAULT NULL COMMENT '改成完成状态的时间',
    `creator`         varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`        varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`     datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`     datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state`    int(2) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY               `app_dangerous_project_material_dangerous_project_id_IDX` (`dangerous_id`) USING BTREE
) ENGINE=InnoDB COMMENT='危大工程过程';

-- 危大工程过程文件

CREATE TABLE `app_dangerous_file`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `dangerous_id` bigint(20) DEFAULT NULL COMMENT '危大id',
    `process_id`   bigint(20) DEFAULT NULL COMMENT '过程id',
    `step_no`      int(11) DEFAULT NULL COMMENT '步骤号，冗余',
    `file_name`    varchar(200) DEFAULT NULL COMMENT '文件名称',
    `file_url`     varchar(300) DEFAULT NULL COMMENT '文件url',
    `file_size`    int(11) DEFAULT NULL COMMENT '文件大小',
    `creator`      varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`     varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`  datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `app_dangerous_file_dangerous_id_IDX` (`dangerous_id`) USING BTREE,
    KEY            `app_dangerous_file_process_id_IDX` (`process_id`) USING BTREE
) ENGINE=InnoDB COMMENT='危大工程过程文件';

-- 危大工程与隐患关系表
CREATE TABLE `app_dangerous_danger_ref`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `dangerous_id` bigint(20) DEFAULT NULL COMMENT '危大工程id',
    `danger_id`    varchar(32)  DEFAULT NULL COMMENT '隐患id',
    `creator`      varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time`  datetime     DEFAULT NULL COMMENT '创建时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `app_dangerous_danger_ref_dangerous_id_IDX` (`dangerous_id`) USING BTREE,
    KEY            `app_dangerous_danger_ref_danger_id_IDX` (`danger_id`) USING BTREE
) ENGINE=InnoDB COMMENT='危大工程过程检查与隐患关系';