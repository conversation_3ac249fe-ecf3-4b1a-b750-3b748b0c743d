-- ------------------------------------
-- 劳务管理-预警中心 疫情监测预警
-- ------------------------------------
-- 增加疫情监测预警1004846 参数
ALTER TABLE `app_warning_config` ADD COLUMN `temperature_` double DEFAULT NULL COMMENT '体温预警值' AFTER `certificate_`,
ADD COLUMN  `health_code_` varchar(32) DEFAULT NULL COMMENT '健康码预警值(1绿码，2黄码，3灰码，4红码) 多选' AFTER `temperature_`;

-- 疫情监测预警表
CREATE TABLE `app_epidemic_warning` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
  `user_id_` varchar(32) DEFAULT NULL COMMENT '人员id',
  `warning_time_` datetime DEFAULT NULL COMMENT '预警时间',
  `warning_rule_` varchar(256) DEFAULT NULL COMMENT '预警规则',
  `temperature_` double DEFAULT NULL COMMENT '体温',
  `health_code_` int(2) DEFAULT NULL COMMENT '健康码(1绿码，2黄码，3灰码，4红码)',
   `type_` int(2) DEFAULT NULL COMMENT '预警类型(1体温预警, 2健康码预警, 3体温和健康码预警)',
  `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `remark_` text COMMENT '备注',
  `status_` int(10) DEFAULT '0' COMMENT '处理状态(1已处理 0未处理)',
  `handle_time_` datetime DEFAULT NULL COMMENT '处理时间',
  `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注',
  `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人',
   PRIMARY KEY (`id`) USING BTREE
) COMMENT='疫情监测预警表';
-- 默认值
UPDATE `app_warning_config` SET `temperature_` = 37.5, `health_code_` = '2,4';