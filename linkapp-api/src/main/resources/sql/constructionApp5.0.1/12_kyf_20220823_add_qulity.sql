-- ----------------------------
-- 部位
-- ----------------------------
CREATE TABLE `app_quality_position`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `parent_id_`     int(11)       DEFAULT NULL COMMENT '父id(一级父类别为0)',
    `full_id_`       varchar(64)   DEFAULT NULL COMMENT '父路径id',
    `full_name_`     varchar(500)  DEFAULT NULL COMMENT '全名',
    `name_`          varchar(128)  DEFAULT NULL COMMENT '名称',
	`code_`          varchar(16)   DEFAULT NULL COMMENT '编码',
    `level_`         int(2)        DEFAULT NULL COMMENT '层级',
	`rectify_id_`    bigint(20)    DEFAULT NULL COMMENT '整改id',
	`notice_id_`     varchar(500)  DEFAULT NULL COMMENT '通知人id(多选,逗号分隔)',
	`subcontractor_` varchar(32)   DEFAULT NULL COMMENT '分包单位',
	`subcontractor_type_` int(2)   DEFAULT NULL COMMENT '分包单位类型(1参建单位 2班组)',
	`order_`         int(2)        DEFAULT NULL COMMENT '排序/编码使用(冗余)',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='部位';

-- ----------------------------
-- 部位图纸
-- ----------------------------
CREATE TABLE `app_quality_drawing`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
    `position_id_`   int(11)       DEFAULT NULL COMMENT '部位id',
    `name_`          varchar(128)  DEFAULT NULL COMMENT '名称',
	`url_`           varchar(255)  DEFAULT NULL COMMENT '图纸url',
	`size_`          varchar(32)   DEFAULT NULL COMMENT '图纸大小',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注/说明',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='部位图纸';

-- ----------------------------
-- 巡检记录
-- ----------------------------
CREATE TABLE `app_quality_inspection`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
	`url_`           text          DEFAULT NULL COMMENT '相片url',
    `position_id_`   int(11)       DEFAULT NULL COMMENT '部位id',
    `problem_id_`    int(11)       DEFAULT NULL COMMENT '问题id',
    `explain_`       varchar(500)  DEFAULT NULL COMMENT '巡检说明',
	`creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
	`modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`        text          COMMENT  '备注',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='巡检记录';

INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(11041,1104, '上传图纸', 'qualityDrawing:add', NULL, 3, NULL, NULL, 1104.1, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(11042,1104, '下载', 'qualityDrawing:down', NULL, 3, NULL, NULL, 1104.2, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(11043,1104, '删除', 'qualityDrawing:delete', NULL, 3, NULL, NULL, 1104.3, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);

ALTER TABLE app_user_project ADD `delete_state_` int(1) DEFAULT '1' COMMENT '是否删除，0删除，1存在';
ALTER TABLE app_labor_company_project ADD `delete_state_` int(1) DEFAULT '1' COMMENT '是否删除，0删除，1存在';
ALTER TABLE app_group ADD `delete_state_` int(1) DEFAULT '1' COMMENT '是否删除，0删除，1存在';


INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(11021,1102, '新增节点', 'inspectionPositionSetting:add', NULL, 3, NULL, NULL, 1102.1, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(11022,1102, '编辑', 'inspectionPositionSetting:edit', NULL, 3, NULL, NULL, 1102.2, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(11023,1102, '删除', 'inspectionPositionSetting:delete', NULL, 3, NULL, NULL, 1102.3, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_ ,parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
values
(11024,1102, 'EXCEL导入', 'inspectionPositionSetting:import', NULL, 3, NULL, NULL, 1102.4, 2, NULL, NULL, '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);