-- 质量问题表
CREATE TABLE `app_quality_question_info`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`            varchar(100)  DEFAULT NULL COMMENT 'tenantId',
    `check_part_id`        int(11) DEFAULT NULL COMMENT '检查部位id，来源检查部位',
    `question_describe_id` int(11) DEFAULT NULL COMMENT '问题描述id，来源问题库',
    `question_level`       int(11) DEFAULT NULL COMMENT '问题等级，先来源问题描述的等级，可修改（1一级、2二级、3三级、4四级）',
    `urgent_level`         int(11) DEFAULT NULL COMMENT '紧急程度，1一般、2严重、3紧要',
    `memo`                 varchar(1000) DEFAULT NULL COMMENT '补充说明，对应排查说明',
    `sub_org_type`         int(11) DEFAULT NULL COMMENT '分包单位类型，1劳务、2专业',
    `sub_org_id`           varchar(32)   DEFAULT NULL COMMENT '分包单位id',
    `sub_org_name`         varchar(200)  DEFAULT NULL COMMENT '分包单位名称',
    `sub_group_id`         varchar(100)  DEFAULT NULL COMMENT '分包班组id',
    `sub_group_name`       varchar(200)  DEFAULT NULL COMMENT '分包班组名称',
    `rectifier_id`         int(11) DEFAULT NULL COMMENT '整改人id',
    `rectifier_name`       varchar(100)  DEFAULT NULL COMMENT '整改人名称',
    `rectifi_site`         int(1) DEFAULT NULL COMMENT '是否现场整改，0否，1是',
    `rectifi_limit_time`   datetime      DEFAULT NULL COMMENT '整改时限',
    `rectifi_target`       varchar(1000) DEFAULT NULL COMMENT '整改需求',
    `review_limit_time`    datetime      DEFAULT NULL COMMENT '复核时限',
    `reviewer_id`          int(11) DEFAULT NULL COMMENT '复核人id',
    `reviewer_name`        varchar(100)  DEFAULT NULL COMMENT '复核人名称',
    `noticer_ids`          varchar(1000) DEFAULT NULL COMMENT '通知人ids',
    `imgs`                 varchar(2000) DEFAULT NULL COMMENT '问题照片，多张以，隔开',
    `noticer_names`        varchar(1000) DEFAULT NULL COMMENT '通知人名称,隔开',
    `question_state`       int(2) DEFAULT NULL COMMENT '问题状态，1待整改、2待复核、3合格',
    `creator`              varchar(100)  DEFAULT NULL COMMENT '创建人',
    `modifier`             varchar(100)  DEFAULT NULL COMMENT '修改人',
    `create_time`          datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_time`          datetime      DEFAULT NULL COMMENT '修改时间',
    `delete_state`         int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY                    `quality_question_info_check_part_id_IDX` (`check_part_id`) USING BTREE
) ENGINE=InnoDB COMMENT='质量问题信息';

-- 质量问题处理记录表
CREATE TABLE `app_quality_question_deal_record`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `question_id`  bigint(20) DEFAULT NULL COMMENT '质量问题id',
    `deal_type`    int(11) DEFAULT NULL COMMENT '处理类型，1整改、2复核',
    `step`         int(11) DEFAULT NULL COMMENT '步骤好，连贯自增',
    `deal_time`    datetime      DEFAULT NULL COMMENT '处理时间',
    `deal_memo`    varchar(2000) DEFAULT NULL COMMENT '处理说明',
    `before_state` int(11) DEFAULT NULL COMMENT '处理前状态',
    `after_stste`  int(11) DEFAULT NULL COMMENT '处理后状态',
    `pass_type`    int(1) DEFAULT NULL COMMENT '处理通过状态，0不通过，1通过',
    `deal_imgs`    varchar(4000) DEFAULT NULL COMMENT '处理图片，多张以逗号隔开',
    `creator`      varchar(100)  DEFAULT NULL COMMENT '创建人',
    `modifier`     varchar(100)  DEFAULT NULL COMMENT '修改人',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `quality_question_deal_record_question_id_IDX` (`question_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='质量问题处理记录（整改、复核）';

-- 质量问题评论表
CREATE TABLE `app_quality_question_comment`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `question_id`  bigint(20) DEFAULT NULL COMMENT '质量问题id',
    `step`         int(11) DEFAULT NULL COMMENT '步骤，连续自增',
    `title`        varchar(200) DEFAULT NULL COMMENT '标题',
    `content`      varchar(500) DEFAULT NULL COMMENT '内容',
    `creator`      varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`     varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`  datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `quality_question_comment_question_id_IDX` (`question_id`) USING BTREE
) ENGINE=InnoDB COMMENT='质量问题评论表';