-- ------------------------------------
-- 【大屏/安全管理/用电安全】用电安全大屏
-- ------------------------------------
CREATE TABLE `app_electric_box_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `electric_box_id_` varchar(32) DEFAULT NULL COMMENT '配电箱ID ',
  `device_code` varchar(32) DEFAULT NULL COMMENT '设备id',
  `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
  `temperature_1` double(20,2) DEFAULT NULL COMMENT '通道1温度 单位℃',
  `temperature_2` double(20,2) DEFAULT NULL COMMENT '通道2温度 单位℃',
  `temperature_3` double(20,2) DEFAULT NULL COMMENT '通道3温度 单位℃',
  `temperature_4` double(20,2) DEFAULT NULL COMMENT '通道4温度 单位℃',
  `active_power` double(20,2) DEFAULT NULL COMMENT '有功总功率 单位W',
	`residual_current` double(20,2) DEFAULT NULL COMMENT '剩余电流 单位mA',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `app_electric_box_record_electric_box_id_IDX` (`electric_box_id_`),
  KEY `app_electric_box_record_device_code_IDX` (`device_code`),
  KEY `app_electric_box_record_tenant_id_IDX` (`tenant_id_`)
) COMMENT='配电箱数据记录';

ALTER TABLE `app_electric_check` ADD COLUMN `status_` INT ( 2 ) DEFAULT NULL COMMENT '巡检状态 0正常 1异常' AFTER `content_`;