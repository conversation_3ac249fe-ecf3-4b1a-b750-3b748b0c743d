-- ------------------------------------
-- 机械管理-电子档案 特殊参数配置
-- ------------------------------------
ALTER TABLE app_machinery_parameter ADD CONSTRAINT app_machinery_parameter_un UNIQUE KEY (tenant_id_,type_code_,key_);

INSERT INTO `app_machinery_parameter` (`tenant_id_`, `type_code_`, `label_`, `key_`, `value_`, `unit_`, `creator_id_`, `create_time_`, `modify_id_`, `modify_time_`, `remark_`)
VALUES
	('405cd2c8633ecbee1c5f61c4a87c9019', 'QUIP_TSQZJ_01', '塔机额定重量', 'craneRatedLoad', NULL, 't', NULL, NULL, NULL, NULL, NULL);
	
INSERT INTO `app_machinery_parameter` (`tenant_id_`, `type_code_`, `label_`, `key_`, `value_`, `unit_`, `creator_id_`, `create_time_`, `modify_id_`, `modify_time_`, `remark_`)
VALUES
	('405cd2c8633ecbee1c5f61c4a87c9019', 'QUIP_TSQZJ_01', '塔机额定力矩', 'craneRatedTorque', NULL, 't·m', NULL, NULL, NULL, NULL, NULL);

-- 塔吊工作循环记录表
CREATE TABLE `app_tower_crane_work_record` (
`id` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT COMMENT 'id',
`machinery_id` INT ( 11 ) DEFAULT NULL COMMENT '电子档案id',
`device_code` VARCHAR ( 32 ) DEFAULT NULL COMMENT '设备id',
`tenant_id_` VARCHAR ( 32 ) DEFAULT NULL COMMENT '租户id',
`start_time` datetime DEFAULT NULL COMMENT '开始时间',
`end_time` datetime DEFAULT NULL COMMENT '结束时间',
`max_weight` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '最大吊重 单位t',
`weight_percentage` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '载重百分比 单位%',
`max_torque` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '最大力矩 单位t·m',
`torque_percentage` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '力矩百分比 单位%',
`max_height` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '最大高度 单位m',
`min_height` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '最小高度 单位m',
`max_range` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '最大幅度 单位m',
`min_range` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '最小幅度 单位m',
`start_rotation` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '起吊点角度 单位°',
`start_range` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '起吊点幅度 单位m',
`start_height` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '起吊点高度 单位m',
`end_rotation` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '卸吊点角度 单位°',
`end_range` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '卸吊点幅度 单位m',
`end_height` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '卸吊点高度 单位m',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
PRIMARY KEY ( `id` ),
KEY `app_tower_crane_work_record_machinery_id_IDX` ( `machinery_id` ) USING BTREE,
KEY `app_tower_crane_work_record_device_code_IDX` ( `device_code` ) USING BTREE
) COMMENT = '塔吊工作循环记录';
