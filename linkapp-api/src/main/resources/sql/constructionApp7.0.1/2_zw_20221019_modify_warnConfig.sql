-- 增加预警配置
ALTER TABLE app_warning_config
    ADD user_out_check_ varchar(10) NULL COMMENT '人员出场检测时间，22:00';
ALTER TABLE app_warning_config
    ADD user_out_switch int(1) NULL COMMENT '人员出场预警开关，0停用，1启用';
ALTER TABLE app_warning_config
    ADD user_stay_duration int(2) NULL COMMENT '停留时长设置，整数';
ALTER TABLE app_warning_config
    ADD user_stay_switch int(1) NULL COMMENT '停留时长开关，0停用，1启用';

-- 修改配置设置
update app_warning_config
set user_out_check_    = '22:00',
    user_out_switch    = 0,
    user_stay_duration = 10,
    user_stay_switch   = 0;

-- 增加出入场异常表
CREATE TABLE `app_user_outin_warning`
(
    `id`             varchar(19) NOT NULL COMMENT '主键',
    `tenant_id_`     varchar(100) DEFAULT NULL COMMENT '租户id',
    `user_id_`       varchar(100) DEFAULT NULL COMMENT '用户id',
    `warning_time_`  datetime     DEFAULT NULL COMMENT '预警时间',
    `warning_rule_`  varchar(500) DEFAULT NULL COMMENT '预警详情',
    `type_`          int(1) DEFAULT NULL COMMENT '预警类型(1出场异常，2进场异常，3停留异常)',
    `create_time_`   datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time_`   datetime     DEFAULT NULL COMMENT '修改时间',
    `remark_`        text COMMENT '备注',
    `status_`        int(1) DEFAULT NULL COMMENT '处理状态(1已处理 0未处理)',
    `group_id`       varchar(100) DEFAULT NULL COMMENT '班组id',
    `handle_time_`   datetime     DEFAULT NULL COMMENT '处理时间',
    `handle_remark_` varchar(500) DEFAULT NULL COMMENT '处理内容',
    `handle_user_`   varchar(100) DEFAULT NULL COMMENT '处理人',
    `operate_type`   int(1) DEFAULT NULL COMMENT '操作类型，1已读，2保留',
    PRIMARY KEY (`id`),
    KEY              `app_early_warn_user_id_IDX` (`user_id_`) USING BTREE,
    KEY              `app_early_warn_tenant_id_IDX` (`tenant_id_`) USING BTREE
) ENGINE=InnoDB COMMENT='人员出入场预警信息';
