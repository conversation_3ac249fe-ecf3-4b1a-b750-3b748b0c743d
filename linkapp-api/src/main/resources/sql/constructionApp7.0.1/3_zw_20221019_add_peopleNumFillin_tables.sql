--  在场人员填报建表
CREATE TABLE `app_people_num_at_fillin`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`              varchar(100) DEFAULT NULL COMMENT '租户id',
    `project_manage_at_num`  int(11) DEFAULT NULL COMMENT '项目部管理人员-在场',
    `project_manage_out_num` int(11) DEFAULT NULL COMMENT '项目部管理人员-出勤',
    `other_manage_at_num`    int(11) DEFAULT NULL COMMENT '其他管理人员-在场',
    `other_manage_out_num`   int(11) DEFAULT NULL COMMENT '其他管理人员-出勤',
    `construction_at_num`    int(11) DEFAULT NULL COMMENT '施工作业人员-在场',
    `construction_out_num`   int(11) DEFAULT NULL COMMENT '施工作业人员-出勤',
    `creator`                varchar(100) DEFAULT NULL COMMENT '创建人',
    `modifier`               varchar(100) DEFAULT NULL COMMENT '修改人',
    `create_time`            datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time`            datetime     DEFAULT NULL COMMENT '修改时间',
    `delete_state`           int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY                      `app_people_num_at_fillin_tenant_id_IDX` (`tenant_id`) USING BTREE,
    KEY                      `app_people_num_at_fillin_create_time_IDX` (`create_time`) USING BTREE
) ENGINE=InnoDB COMMENT='在场人员数量填报';

-- 租户表项目名称增加索引
CREATE INDEX linkapp_tenant_platform_project_name_IDX USING BTREE ON linkapp_tenant (platform_project_name);