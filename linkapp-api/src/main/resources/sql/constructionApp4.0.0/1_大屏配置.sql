-- ----------------------------
-- 大屏配置
-- ----------------------------
ALTER TABLE linkapp_dashboard
    ADD COLUMN `milestone_setting` text DEFAULT NULL COMMENT '里程碑设置' AFTER `sketch`,
    ADD COLUMN `task_setting` text DEFAULT NULL COMMENT '任务设置' AFTER `milestone_setting`,
    ADD COLUMN `background_url` varchar(255) COMMENT '背景图' AFTER `task_setting`;

INSERT INTO `linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`)
VALUES
	('84', '8', '大屏配置', 'bigScreenSetting', '大屏配置', 2, NULL, NULL, 17.10, 1, 'manage/bigScreenSetting', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
