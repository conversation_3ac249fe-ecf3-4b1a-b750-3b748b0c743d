CREATE TABLE `app_device_position`
(

    `device_code` varchar(32) NOT NULL COMMENT '设备编码',
    `position_x`  float(6, 2) not NULL COMMENT '电量',
    `position_y`  float(6, 2) not NULL COMMENT '电量',
    `tenant_id`   varchar(32) DEFAULT NULL COMMENT '租户ID',
    `create_time` datetime    DEFAULT NULL COMMENT '创建时间',
    `creator`     varchar(32) DEFAULT NULL COMMENT '创建人',
    `modifier`    varchar(32) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime    DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`device_code`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 comment '设备在图纸上的位置';

ALTER TABLE linkappdb.linkapp_user MODIFY COLUMN phone varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL;


# 添加用户名唯一索引
alter table linkapp_user
    add unique index (username);

# 添加联合唯一索引  用户管理需求
ALTER TABLE linkapp_user
    ADD UNIQUE INDEX index_unique_ten_ph (tenant_id, phone);

# 绑定工地卫士
ALTER TABLE app_gate
    ADD COLUMN `blade_guard_bind_status_` int(1) DEFAULT 0 COMMENT '是否绑定工地卫士 1是0否' AFTER `device_id_`,
    ADD COLUMN `blade_guard_project_id_` varchar(32) DEFAULT NULL COMMENT '工地卫士 projectID' AFTER `blade_guard_bind_status_`,
    ADD COLUMN `blade_guard_user_id` varchar(32) DEFAULT NULL COMMENT '工地卫士 userId' AFTER `blade_guard_project_id_`,
    ADD COLUMN `blade_guard_jing_du` varchar(32) DEFAULT NULL COMMENT '闸机 经度' AFTER `blade_guard_user_id`,
    ADD COLUMN `blade_guard_wei_du` varchar(32) DEFAULT NULL COMMENT '闸机 维度' AFTER `blade_guard_jing_du`,
    ADD COLUMN `blade_guard_qrcode` varchar(512) DEFAULT NULL COMMENT '工地卫士二维码' AFTER `blade_guard_wei_du`;
