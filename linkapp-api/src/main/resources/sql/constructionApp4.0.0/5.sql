CREATE TABLE `app_message_center`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id`   varchar(32)  NOT NULL COMMENT '租户ID',
    `owner_id`    varchar(32)  NOT NULL COMMENT '所属人id,对应用户表id,-999代表属于所有用户',
    `self`        tinyint(1)   NOT NULL COMMENT '是否是自己本人的，因为是负责人且有查看角色的，会产生两条消息',
    `type`        int(3)       NOT NULL COMMENT '消息类别0-未归类，1-配电箱巡检',
    `status`      int(2)       NOT NULL COMMENT '阅读状态，0-未读，1-已读',
    `title`       varchar(50)  NOT NULL COMMENT '标题',
    `content`     varchar(500) NOT NULL COMMENT '内容',
    `create_time` datetime    DEFAULT NULL COMMENT '创建时间',
    `creator`     varchar(32) DEFAULT NULL COMMENT '创建人',
    `modifier`    varchar(32) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime    DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_owner_id_type_id` (`owner_id`, `type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='消息中心';