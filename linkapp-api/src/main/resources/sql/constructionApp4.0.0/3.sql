-- ----------------------------
-- 喷淋历史记录 喷淋记录&用水记录
-- ----------------------------
CREATE TABLE `app_spray_records` (
	`id` VARCHAR ( 32 ) NOT NULL COMMENT 'ID 主键',
	`device_code` VARCHAR ( 32 ) NOT NULL COMMENT '设备编码',
	`tenant_id` VARCHAR ( 32 ) DEFAULT NULL COMMENT '租户ID',
	`trigger_source` VARCHAR ( 32 ) DEFAULT NULL COMMENT '触发源',
	`trigger_time` datetime DEFAULT NULL COMMENT '触发时间',
	`start_time` datetime DEFAULT NULL COMMENT '喷淋开始时间',
	`end_time` datetime DEFAULT NULL COMMENT '喷淋结束时间',
	`spray_hours_` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '喷淋时长',
	`creator_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '创建人id',
	`create_time_` datetime DEFAULT NULL COMMENT '创建日期',
	`modify_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '修改人id',
	`modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
	`remark_` text COMMENT '备注',
	PRIMARY KEY ( `id` ) USING BTREE
) COMMENT '喷淋记录';

CREATE TABLE `app_water_records` (
	`id` VARCHAR ( 32 ) NOT NULL COMMENT 'ID 主键',
	`device_code` VARCHAR ( 32 ) NOT NULL COMMENT '设备编码',
	`tenant_id` VARCHAR ( 32 ) DEFAULT NULL COMMENT '租户ID',
	`collect_time` datetime DEFAULT NULL COMMENT '采集时间',
	`water_increment` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '用水增量',
	`water_total` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '累计用水',
	`stop_reading` DOUBLE ( 20, 2 ) DEFAULT NULL COMMENT '止码读数',
	`creator_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '创建人id',
	`create_time_` datetime DEFAULT NULL COMMENT '创建日期',
	`modify_id_` BIGINT ( 20 ) DEFAULT NULL COMMENT '修改人id',
	`modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
	`remark_` text COMMENT '备注',
PRIMARY KEY ( `id` ) USING BTREE
) COMMENT '用水记录';