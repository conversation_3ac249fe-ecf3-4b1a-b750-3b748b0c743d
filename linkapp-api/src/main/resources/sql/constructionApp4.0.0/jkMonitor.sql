-- 基坑监测数据主表
CREATE TABLE `jk_monitor_info`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `collect_time` varchar(30)  DEFAULT NULL COMMENT '采集时间',
    `project_id`   varchar(30)  DEFAULT NULL COMMENT '项目id，第三方提供',
    `data_type`    varchar(20)  DEFAULT NULL COMMENT '采集数据类型，1基坑监测数据',
    `tenant_id`    varchar(100) DEFAULT NULL COMMENT '租户id',
    `create_time`  datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY            `jk_monitor_info_collect_time_IDX` (`collect_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='基坑监测记录';
-- 基坑监测数据记录表
CREATE TABLE `jk_monitor_record`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `info_id`                  bigint(20) DEFAULT NULL COMMENT '主记录主键，来源jk_monitor_info',
    `warning_level`            int(11) DEFAULT NULL COMMENT '状态，0,1,2,3对应绿色，黄色，橙色，红色',
    `measurement_project_name` varchar(100) DEFAULT NULL COMMENT '测项名称',
    `create_time`              datetime     DEFAULT NULL COMMENT '创建时间',
    `alarm_value`              varchar(6)   DEFAULT NULL COMMENT '预警值',
    `project_id`               varchar(30)  DEFAULT NULL COMMENT '项目id，第三方提供',
    `total_change_result`      varchar(20)  DEFAULT NULL COMMENT '累计变化量',
    `single_change_result`     varchar(20)  DEFAULT NULL COMMENT '单次变化量',
    `change_rate`              varchar(30)  DEFAULT NULL COMMENT '变化速率',
    `point_name`               varchar(30)  DEFAULT NULL COMMENT '测点名称',
    `is_maximum`               tinyint(4) DEFAULT NULL COMMENT '是否为最大值点',
    `percent`                  varchar(40)  DEFAULT NULL COMMENT '累计值占预警值百分比',
    `source_id`                varchar(30)  DEFAULT NULL COMMENT '原始id，第三方返回数据id',
    `parent_id`                bigint(20) DEFAULT NULL COMMENT '父级id，本表id',
    `points`                   varchar(300) DEFAULT NULL COMMENT '定位地理位置，经纬度，存json串',
    `storage_time`             datetime     DEFAULT NULL COMMENT '存储时间',
    PRIMARY KEY (`id`),
    KEY                        `jk_monitor_record_info_id_IDX` (`info_id`) USING BTREE,
    KEY                        `jk_monitor_record_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='基坑监测数据记录';