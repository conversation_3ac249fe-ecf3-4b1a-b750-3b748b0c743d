-- {
--     "upload_time": "2025-07-11 17:03:08",
--     "max_weight": 0,
--     "work_start_time": "2025-07-11 09:50:06",
--     "device_id": "250512004",
--     "max_wind_speed": 0,
--     "max_inclination": 0,
--     "work_end_time": "2025-07-11 09:50:11",
--     "msg_type": 2,
--     "max_arm_height": 0
-- }

-- 吊车循环数据记录表
CREATE TABLE `rail_crane_work_record`
(
    `id_`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `tenant_id_`       varchar(64)    DEFAULT NULL COMMENT '租户id',
    `mechanical_id_`   varchar(64) NOT NULL COMMENT '机械id',
    `device_id_`       varchar(64) NOT NULL COMMENT '设备id',
    `device_code_`     varchar(64) NOT NULL COMMENT '设备code',
    `device_name_`     varchar(255)   DEFAULT NULL COMMENT '设备名称',
    `work_start_time_` datetime       DEFAULT NULL COMMENT '工作循环起始时间',
    `work_end_time_`   datetime       DEFAULT NULL COMMENT '工作循环结束时间',
    `max_weight_`      decimal(10, 2) DEFAULT NULL COMMENT '最大吊重',
    `max_wind_speed_`  decimal(10, 2) DEFAULT NULL COMMENT '最大风速',
    `max_inclination_` decimal(10, 2) DEFAULT NULL COMMENT '最大倾斜度',
    `max_arm_height_`  decimal(10, 2) DEFAULT NULL COMMENT '最大臂尖高度',
    `upload_time_`     datetime       DEFAULT NULL COMMENT '上报时间',
    `create_id_`       bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`     datetime       DEFAULT NULL COMMENT '创建日期',
    `modify_id_`       bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`     datetime       DEFAULT NULL COMMENT '修改时间',
    `remark_`          text COMMENT '备注',
    PRIMARY KEY (`id_`)
) COMMENT='吊车循环数据记录表';