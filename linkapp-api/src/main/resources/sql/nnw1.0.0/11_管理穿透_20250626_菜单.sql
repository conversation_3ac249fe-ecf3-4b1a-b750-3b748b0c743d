INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9915', null, '管理穿透', 't_manage_penet', '管理穿透', 1, NULL, NULL, 9915, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99151', 9915, '关键作业清单', 't_keywork_list', '关键作业清单', 2, NULL, NULL, 99151, 1, 'organization/criticalOperations', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99152', 9915, '安全确认表', 't_safe_table', '安全确认表', 2, NULL, NULL, 99152, 1, 'organization/safetyConditions', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99153', 9915, '班前会', 't_before_meet', '班前会', 2, NULL, NULL, 99153, 1, 'organization/classMeeting', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99154', 9915, '晚交班会', 't_late_meet', '晚交班会', 2, NULL, NULL, 99154, 1, 'organization/lateShiftMeeting', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);


INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99155', 9915, '过程盯控', 't_process_monitor', '过程盯控', 2, NULL, NULL, 99155, 1, 'organization/processMonitoring', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

