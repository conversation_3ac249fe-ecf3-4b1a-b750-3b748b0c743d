-- 数据字典表
CREATE TABLE `sys_dict` (
    `id_` bigint NOT NULL COMMENT '主键id',
    `dict_name_` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称',
    `dict_code_` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典编码',
    `description_` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
    `create_id_` bigint DEFAULT NULL COMMENT '创建人ID',
    `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
    `update_id_` bigint DEFAULT NULL COMMENT '更新人',
    `update_time_` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id_`) USING BTREE,
    UNIQUE KEY `uk_sd_dict_code_sys_dict` (`dict_code_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据字典分类表';
ALTER TABLE sys_dict MODIFY COLUMN id_ bigint(20) auto_increment NOT NULL COMMENT '主键id';


CREATE TABLE `sys_dict_item` (
     `id_` bigint NOT NULL COMMENT '主键id',
     `dict_id_` bigint DEFAULT NULL COMMENT '字典id',
     `item_text_` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典项文本',
     `item_value_` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典项值',
     `description_` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
     `sort_order_` int DEFAULT NULL COMMENT '排序',
     `status_` int DEFAULT NULL COMMENT '状态（1启用 0不启用）',
     `create_id_` bigint DEFAULT NULL COMMENT '创建人ID',
     `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
     `update_id_` bigint DEFAULT NULL COMMENT '更新人',
     `update_time_` datetime DEFAULT NULL COMMENT '更新时间',
     `is_sync_` tinyint DEFAULT '0' COMMENT '是否同步到redis',
     PRIMARY KEY (`id_`) USING BTREE,
     KEY `idx_sditem_role_dict_id_sys_dict_item` (`dict_id_`) USING BTREE,
     KEY `idx_sditem_role_sort_order_sys_dict_item` (`sort_order_`) USING BTREE,
     KEY `idx_sditem_status_sys_dict_item` (`status_`) USING BTREE,
     KEY `idx_sditem_dict_val_sys_dict_item` (`dict_id_`,`item_value_`) USING BTREE,
     KEY `idx_is_sync_sys_dict_item` (`is_sync_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据字典明细表';
ALTER TABLE sys_dict_item MODIFY COLUMN id_ bigint(20) auto_increment NOT NULL COMMENT '主键id';