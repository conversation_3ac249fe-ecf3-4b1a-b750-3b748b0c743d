CREATE TABLE `rail_linkapp_voice_broad_config` (
  `id` varchar(32) NOT NULL,
  `alarm_type` varchar(100) DEFAULT NULL COMMENT '关联rail_give_alarm_type_config表alarm_type字段 小类',
  `category` varchar(100) DEFAULT NULL COMMENT '关联rail_give_alarm_type_config表category字段 大类',
  `prefix_type` int(100) DEFAULT NULL COMMENT '0:机械名称/设备名称，1:人员姓名',
  `broadcast_content` varchar(200) DEFAULT NULL COMMENT '播报内容 30字',
  `number_times` int(11) DEFAULT NULL COMMENT '播报次数1~5',
  `enable` int(5) DEFAULT NULL COMMENT '是否启用 ，0启用，1禁用',
  `device_codes` varchar(1000) DEFAULT NULL COMMENT '关联播报设备编号,1对多',
  `tenant_id` varchar(32) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

