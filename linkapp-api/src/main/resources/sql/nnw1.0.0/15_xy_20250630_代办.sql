-- 代办中心
CREATE TABLE `rail_todo_center`
(
    `id_`         bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`  varchar(32) NOT NULL COMMENT '租户ID',
    `owner_id_`   varchar(32) NOT NULL COMMENT '所属人id',
    `type_`       int(3) NOT NULL COMMENT '代办类别',
    `status_`     int(2) NOT NULL COMMENT '阅读状态，0-未办，1-已办',
    `title_`      varchar(50) NOT NULL COMMENT '标题',
    `content_`    text        NOT NULL COMMENT '内容 json格式',
    `link_id`     varchar(32) DEFAULT NULL COMMENT '关联记录id',
    `link_seq_`   int(3) NOT NULL COMMENT '关联批次',
    `create_time` datetime    DEFAULT NULL COMMENT '创建时间',
    `creator`     varchar(32) DEFAULT NULL COMMENT '创建人',
    `modifier`    varchar(32) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime    DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id_`),
    KEY           `idx_tenant_id_owner_id_type_id_create_time` (`tenant_id_`, `owner_id_`,`type_`, `create_time`)
) COMMENT='代办中心';
-- 代办类别 字典 1 安全条件确认表、2 晚交班会、3 跟班记录、4 月度考核、5 隐患排查
INSERT INTO linkappdb.sys_dict
(id_, dict_name_, dict_code_, description_, create_id_, create_time_, update_id_, update_time_)
VALUES (18, '代办类别', 'todo_type', '代办类别', NULL, NULL, NULL, NULL);
-- 代办类别 字典 1 安全条件确认表、2 晚交班会、3 跟班记录、4 月度考核、5 隐患排查
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_,
 update_time_, is_sync_)
VALUES (18, '安全条件确认表', 1, '代办类别', 1, 1, NULL, NULL, NULL, NULL, 0),
       (18, '晚交班会', 2, '代办类别', 2, 1, NULL, NULL, NULL, NULL, 0),
       (18, '跟班记录', 3, '代办类别', 3, 1, NULL, NULL, NULL, NULL, 0),
       (18, '月度考核', 4, '代办类别', 4, 1, NULL, NULL, NULL, NULL, 0),
       (18, '隐患排查', 5, '代办类别', 4, 1, NULL, NULL, NULL, NULL, 0);

-- 代办按钮类型
ALTER TABLE `rail_todo_center`
    ADD COLUMN `button_type_` int(2) DEFAULT NULL COMMENT '按钮类型，根据业务自己判断' AFTER `type_`;