-- 网格安全员日志
-- 1.菜单
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99158', 9915, '网格安全员日志', 't_grid_safe', '网格安全员日志', 2, NULL, NULL, 99158, 1, 'organization/gridSafe', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- 2.建表语句
DROP TABLE IF EXISTS rail_grid_security_check_item;
CREATE TABLE `rail_grid_security_check_item`
(
    `id_`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `parent_id_`   int(11) DEFAULT NULL COMMENT '检查项目ID',
    `sort_`        int(11) NOT NULL COMMENT '排序号',
    `name_`        varchar(255) NOT NULL COMMENT '检查项名称',
    `create_id_`   bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_`      text COMMENT '备注',
    PRIMARY KEY (`id_`),
    KEY            `idx_parent_id` (`parent_id_`)
) COMMENT='网格安全员检查项目';
-- 1 施工准备情况
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (1, NULL, 1, '施工准备情况', 1, now(), 1, now(), NULL);
-- 1.班前讲话是否按规定开展，当日作业内容是否清晰、分工是否明确。
-- 2.技术交底、安全交底、安全提示内容是否清晰,是否随机抽查作业人员掌握情况。
-- 3.作业人员是否充分休息，是否班前饮酒，精神状态是否良好，劳动保护用品是否穿戴齐全。
-- 4.班组是否有“四类人员”。
-- 5.材料是否准备齐全，机具状态是否良好，机械或特种设备操作人员是否持证上岗。
-- 6.涉及营业线或邻近营业线施工作业的，施工计划是否批复，异体监督人员是否到岗，防护员数量是否满足要求，防护用品是否齐全。
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (10001, 1, 1, '1.班前讲话是否按规定开展，当日作业内容是否清晰、分工是否明确', 1, now(), 1, now(), NULL),
       (10002, 1, 2, '2.技术交底、安全交底、安全提示内容是否清晰,是否随机抽查作业人员掌握情况', 1, now(), 1, now(), NULL),
       (10003, 1, 3, '3.作业人员是否充分休息，是否班前饮酒，精神状态是否良好，劳动保护用品是否穿戴齐全', 1, now(), 1, now(), NULL),
       (10004, 1, 4, '4.班组是否有“四类人员”', 1, now(), 1, now(), NULL),
       (10005, 1, 5, '5.材料是否准备齐全，机具状态是否良好，机械或特种设备操作人员是否持证上岗',  1, now(), 1, now(), NULL),
       (10006, 1, 6, '6.涉及营业线或邻近营业线施工作业的，施工计划是否批复，异体监督人员是否到岗，防护员数量是否满足要求，防护用品是否齐全', 1, now(), 1, now(), NULL);
-- 2 施工方案、落实
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (2, NULL, 2, '施工方案、落实', 1, now(), 1, now(), NULL);
-- 1.现场作业是否按施工方案、技术交底、作业指导书实施。
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (10007, 2, 1,'1.现场作业是否按施工方案、技术交底、作业指导书实施', 1, now(), 1, now(), null);
-- 3 安全措施落实
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (3, NULL, 3, '安全措施落实', 1, now(), 1, now(), NULL);
-- 1.大型机械是否落实"四定管理"措施和钥匙管理要求。
-- 2.高陡边坡、深基坑、高空作业平台等临边防护是否设置、是否稳固。
-- 3.既有光电缆是否按规定挖探并防护到位;防护措施是否损坏。
-- 4.安全操作规程有关要求是否落实。
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES  (10008, 3, 1, '1.大型机械是否落实"四定管理"措施和钥匙管理要求', 1, now(), 1, now(), NULL),
        (10009, 3, 2, '2.高陡边坡、深基坑、高空作业平台等临边防护是否设置、是否稳固', 1, now(), 1, now(), NULL),
        (10010, 3, 3, '3.既有光电缆是否按规定挖探并防护到位;防护措施是否损坏', 1, now(), 1, now(), NULL),
        (10011, 3, 4, '4.安全操作规程有关要求是否落实', 1, now(), 1, now(), null);
-- 4 作业人员违章
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (4, NULL, 4, '作业人员违章', 1, now(), 1, now(), NULL);
-- 1.防护员是否履行安全防护职能。
-- 2.作业人员是否按技术交底、作业指导书作业、操作规程进行作业。
-- 3.作业人员是否按要求使用劳动保护用品，如不戴安全帽、高空作业不系安全带、动火作业不穿戴劳动保护用品等。
-- 4.作业人员不听工点网格长或其他管理人员正确指令，违章作业。
-- 5.现场作业负责人造章独挥、强令国险作业。
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES  (10012, 4, 1, '1.防护员是否履行安全防护职能', 1, now(), 1, now(), NULL),
        (10013, 4, 2, '2.作业人员是否按技术交底、作业指导书作业、操作规程进行作业', 1, now(), 1, now(), NULL),
        (10014, 4, 3, '3.作业人员是否按要求使用劳动保护用品，如不戴安全帽、高空作业不系安全带、动火作业不穿戴劳动保护用品等', 1, now(), 1, now(), NULL),
        (10015, 4, 4, '4.作业人员不听工点网格长或其他管理人员正确指令，违章作业', 1, now(), 1, now(), NULL);
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES  (10038, 4, 5, '5.作业人员违章，违反作业规范，造成严重后果', 1, now(), 1, now(), NULL);
-- 5 机械设备
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (5, NULL, 5, '机械设备', 1, now(), 1, now(), NULL);
-- 1.机械设备是否已到项目部进行备案。
-- 2.机械操作人员是否参加培训、持证上岗，人机对应。
-- 3.机械设备性能是否完好，安全装置齐全有效。
-- 4.是否有设备传动部位无防护装置。
-- 5.手持小型电动工具绝缘部位是否完好，电缆有无破皮老化。
-- 6.固定式设备接地装置是否完好。
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES  (10016, 5, 1, '1.机械设备是否已到项目部进行备案', 1, now(), 1, now(), NULL),
        (10017, 5, 2, '2.机械操作人员是否参加培训、持证上岗，人机对应', 1, now(), 1, now(), NULL),
        (10018, 5, 3, '3.机械设备性能是否完好，安全装置齐全有效', 1, now(), 1, now(), NULL),
        (10019, 5, 4, '4.是否有设备传动部位无防护装置', 1, now(), 1, now(), NULL),
        (10020, 5, 5, '5.手持小型电动工具绝缘部位是否完好，电缆有无破皮老化', 1, now(), 1, now(), NULL),
        (10021, 5, 6, '6.固定式设备接地装置是否完好', 1, now(), 1, now(), NULL);
-- 6 消防安全
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (6, NULL, 6, '消防安全', 1, now(), 1, now(), NULL);
-- 1.灭火器配备是否满足要求，灭火器状态是否良好。
-- 2.拟进行动火作业范围内是否有可燃物。
-- 3.拟进行动火作业是否配备有灭火器和监护人。
-- 4.作业范围内易发生火灾部位是否已经配备有灭火器，如配电箱、材料库房。
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES  (10022, 6, 1, '1.灭火器配备是否满足要求，灭火器状态是否良好', 1, now(), 1, now(), NULL),
        (10023, 6, 2, '2.拟进行动火作业范围内是否有可燃物', 1, now(), 1, now(), NULL),
        (10024, 6, 3, '3.拟进行动火作业是否配备有灭火器和监护人', 1, now(), 1, now(), NULL),
        (10025, 6, 4, '4.作业范围内易发生火灾部位是否已经配备有灭火器，如配电箱、材料库房', 1, now(), 1, now(), NULL);
-- 7 临时用电
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (7, NULL, 7, '临时用电', 1, now(), 1, now(), NULL);
-- 1.一二级配电箱门锁是否完好。
-- 2.配电箱接地是否完好。
-- 3.设备接地是否完好。
-- 4.是否有电缆皮破损部位。
-- 5.是否有设备带电裸露部位。
-- 6.设备用电是否符合一机一闸。
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES  (10026, 7, 1, '1.一二级配电箱门锁是否完好', 1, now(), 1, now(), NULL),
        (10027, 7, 2, '2.配电箱接地是否完好', 1, now(), 1, now(), NULL),
        (10028, 7, 3, '3.设备接地是否完好', 1, now(), 1, now(), NULL),
        (10029, 7, 4, '4.是否有电缆皮破损部位', 1, now(), 1, now(), NULL),
        (10030, 7, 5, '5.是否有设备带电裸露部位', 1, now(), 1, now(), NULL),
        (10031, 7, 6, '6.设备用电是否符合一机一闸', 1, now(), 1, now(), NULL);
-- 8 作业环境
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (8, NULL, 8, '作业环境', 1, now(), 1, now(), NULL);
-- 1.场地是否有积水。
-- 2.场地是否存在有轻质漂浮物。
-- 3.场地是否存在乱堆乱放，影响人机通行。
-- 4.若进行电焊作业，场地是否干燥。
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES  (10032, 8, 1, '1.场地是否有积水', 1, now(), 1, now(), NULL),
        (10033, 8, 2, '2.场地是否存在有轻质漂浮物', 1, now(), 1, now(), NULL),
        (10034, 8, 3, '3.场地是否存在乱堆乱放，影响人机通行', 1, now(), 1, now(), NULL),
        (10035, 8, 4, '4.若进行电焊作业，场地是否干燥', 1, now(), 1, now(), NULL);
-- 9 应急逃生
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES (9, NULL, 9, '应急逃生', 1, now(), 1, now(), NULL);
-- 1.是否根据当日作业情况携带应急逃生物资或在施工现场配备应急逃生物资；应急逃生物资是否满足相关要求；应急逃生物质设置点位、配备数量是否满足要求。
-- 2.现场应急逃生标牌标识设置是否满足要求；应急逃生通道是否畅通。
INSERT INTO `rail_grid_security_check_item` (`id_`, `parent_id_`, `sort_`, `name_`, `create_id_`, `create_time_`,
                                             `modify_id_`, `modify_time_`, `remark_`)
VALUES  (10036, 9, 1, '1.是否根据当日作业情况携带应急逃生物资或在施工现场配备应急逃生物资；应急逃生物资是否满足相关要求；应急逃生物质设置点位、配备数量是否满足要求。', 1, now(), 1, now(), NULL),
        (10037, 9, 2, '2.现场应急逃生标牌标识设置是否满足要求；应急逃生通道是否畅通。', 1, now(), 1, now(), NULL);

-- 网格安全员日志
DROP TABLE IF EXISTS `rail_grid_security_log`;
CREATE TABLE `rail_grid_security_log`
(
    `id_`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id_`    varchar(64) DEFAULT NULL COMMENT '租户id',
    `grid_id_`      bigint(20) NOT NULL COMMENT '网格ID',
    `user_id_`      bigint(20) NOT NULL COMMENT '用户ID',
    `user_name_`    varchar(255) NOT NULL COMMENT '用户名称',
    `log_time_`     datetime     NOT NULL COMMENT '日志时间',
    `delete_state_` tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_`    bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`  datetime    DEFAULT NULL COMMENT '创建日期',
    `modify_id_`    bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`  datetime    DEFAULT NULL COMMENT '修改时间',
    `remark_`       text COMMENT '备注',
    PRIMARY KEY (`id_`),
    KEY             `idx_grid_id` (`grid_id_`),
    KEY             `idx_user_id` (`user_id_`),
    KEY             `idx_log_time` (`log_time_`)
) COMMENT='网格安全员日志';
-- 网格安全员日志关联表
DROP TABLE IF EXISTS `rail_grid_security_log_relation`;
CREATE TABLE `rail_grid_security_log_relation`
(
    `id_`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `log_id_`        int(11) NOT NULL COMMENT '日志ID',
    `check_item_id_` int(11) NOT NULL COMMENT '检查项ID',
    `check_desc_`    VARCHAR(255) DEFAULT NULL COMMENT '检查情况',
    `problem_desc_`  VARCHAR(255) DEFAULT NULL COMMENT '存在问题',
    `improve_desc_`  VARCHAR(255) DEFAULT NULL COMMENT '督促整改情况',
    `create_id_`     bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`   datetime     DEFAULT NULL COMMENT '创建日期',
    `modify_id_`     bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`   datetime     DEFAULT NULL COMMENT '修改时间',
    `remark_`        VARCHAR(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id_`),
    KEY              `idx_log_id` (`log_id_`)
) COMMENT='网格安全员日志关联表';