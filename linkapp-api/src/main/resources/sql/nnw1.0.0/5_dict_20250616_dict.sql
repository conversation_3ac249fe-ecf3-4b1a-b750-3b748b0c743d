INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('6', '健康状态', 'health_status', '健康状态', NULL, NULL, NULL, NULL);
INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('7', '特种人员证书类型', 'special_book_type', '特种人员证书类型', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('79', '6', '良好', '0', '良好', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('80', '7', '塔式起重机械操作证', '0', '塔式起重机械操作证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('81', '7', '龙门吊司机资格证', '1', '龙门吊司机资格证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('82', '7', '吊车司机资格证', '2', '吊车司机资格证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('83', '7', '压路机司机资格证', '3', '压路机司机资格证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('84', '7', '推土机‌司机资格证', '4', '推土机‌司机资格证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('85', '7', '混凝土泵车司机资格证', '5', '混凝土泵车司机资格证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('86', '7', '搅拌车司机资格证', '7', '搅拌车司机资格证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('87', '7', '挖掘机司机证书', '8', '挖掘机司机证书', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('88', '7', '装载机司机证', '9', '装载机司机证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('89', '7', '工程车司机资格证', '10', '工程车司机资格证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('90', '7', '电工证', '11', '电工证', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('91', '7', '高处作业证', '12', '高处作业证', '1', '1', NULL, NULL, NULL, NULL, '0');



INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('8', '断路器报警故障类型', 'fault_type', '断路器报警故障类型', NULL, NULL, NULL, NULL);



INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('8', '断路器报警故障类型', 'fault_type', '断路器报警故障类型', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '过压', '1', '过压', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '欠压', '2', '欠压', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '过流', '3', '过流', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '过温', '4', '过温', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '漏电', '5', '漏电', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '电弧故障', '6', '电弧故障', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '电弧告警', '7', '电弧告警', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '设备离线', '8', '设备离线', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '漏电自检', '9', '漏电自检', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '漏电测试', '10', '漏电测试', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '瞬时故障', '11', '瞬时故障', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '缺相故障', '12', '缺相故障', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('8', '电弧报警试验', '13', '电弧报警试验', '1', '1', NULL, NULL, NULL, NULL, '0');

CREATE TABLE `rail_circuit_givealarm` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `device_code` varchar(128) DEFAULT NULL COMMENT '设备记录id',
  `give_type` varchar(100) DEFAULT NULL COMMENT '告警类型，字典 0报警 ，1故障，2正常',
  `fault_type` varchar(100) DEFAULT NULL COMMENT '故障类型 可取字典',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '告警\\故障时间',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `content` varchar(1000) DEFAULT NULL COMMENT '告警\\故障内容',
  `code` varchar(255) DEFAULT NULL COMMENT '故障代码',
  `fault_description` varchar(255) DEFAULT NULL COMMENT '报警故障说明',
  `fault_value` double(255,2) DEFAULT NULL COMMENT '故障值',
  `a_phase_current` double(20,2) DEFAULT NULL COMMENT 'a相电流',
  `b_phase_current` double(20,2) DEFAULT NULL COMMENT 'b相电流',
  `c_phase_current` double(20,2) DEFAULT NULL COMMENT 'c相电流',
  `a_phase_voltage` double(20,2) DEFAULT NULL COMMENT 'a相电压',
  `b_phase_voltage` double(20,2) DEFAULT NULL COMMENT 'b相电压',
  `c_phase_voltage` double(20,2) DEFAULT NULL COMMENT 'c相电压',
  `a_phase_power` double(20,2) DEFAULT NULL COMMENT 'a相功率',
  `b_phase_power` double(20,2) DEFAULT NULL COMMENT 'b相功率',
  `c_phase_power` double(20,2) DEFAULT NULL COMMENT 'c相功率',
  `active_power` double(20,2) DEFAULT NULL COMMENT '总功率',
  `line_online_state` varchar(10) DEFAULT NULL COMMENT '在线状态，0离线，1在线',
  `switch_state` varchar(10) DEFAULT NULL COMMENT '开关状态 0断开断闸，1闭合合闸',
  `residual_current` double(20,2) DEFAULT NULL COMMENT '剩余电流',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='断路器设备告警/故障记录';


