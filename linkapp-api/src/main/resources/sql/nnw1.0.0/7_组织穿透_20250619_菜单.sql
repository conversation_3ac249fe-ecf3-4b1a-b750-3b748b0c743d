INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9914', null, '组织穿透', 't_organizational_penet', '组织穿透', 1, NULL, NULL, 9914, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99141', 9914, '部门管理', 't_org_info_resp', '部门管理', 2, NULL, NULL, 99141, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);


INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99142', 9914, '工作职责', 't_work_info_resp', '工作职责', 2, NULL, NULL, 99142, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99143', 9914, '工作流程分解', 't_work_process_dec', '工作流程分解', 2, NULL, NULL, 99143, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99144', 9914, '网格划分', 't_work_mesh_staff', '网格划分', 2, NULL, NULL, 99144, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);


-- 字典
INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('11', '工程类别', 'enginee_type', '工程类别', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('126', '11', '铁路', '1', '铁路', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('127', '11', '市政', '2', '市政', '2', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('128', '11', '房建', '3', '房建', '3', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('129', '11', '公路', '4', '公路', '4', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('130', '11', '机电', '5', '机电', '5', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('131', '11', '桥梁', '6', '桥梁', '6', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('132', '11', '隧道', '7', '隧道', '7', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('133', '11', '水利水电', '8', '水利水电', '8', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('134', '11', '海洋工程', '9', '海洋工程', '9', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('135', '11', '环境保护', '10', '环境保护', '10', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('136', '11', '港口与航道', '11', '港口与航道', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('137', '11', '通信工程', '12', '通信工程', '12', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('138', '11', '石油化工', '13', '石油化工', '13', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('139', '11', '冶金工程', '14', '冶金工程', '14', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('140', '11', '电力工程', '15', '电力工程', '15', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('141', '11', '矿业工程', '16', '矿业工程', '16', '1', NULL, NULL, NULL, NULL, '0');

CREATE TABLE `rail_linkapp_grid_management_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `enginee_type` varchar(50) DEFAULT NULL COMMENT '工程类别 字典enginee_type',
  `number` int(11) DEFAULT NULL COMMENT '网格编号',
  `grid_type` varchar(255) DEFAULT NULL COMMENT '网格类别',
  `grid_name` varchar(255) DEFAULT NULL COMMENT '网格名称',
  `mileage_range` varchar(128) DEFAULT NULL COMMENT '网格里程范围',
  `safety_sup_id` varchar(128) DEFAULT NULL COMMENT '选中安监专务人员id',
  `safety_sup_name` varchar(100) DEFAULT NULL COMMENT '选中安监专务人员姓名',
  `grid_sec_id` varchar(128) DEFAULT NULL COMMENT '选中安网格安全员id',
  `grid_sec_name` varchar(100) DEFAULT NULL COMMENT '选中安网格安全员姓名',
  `grid_foreman_id` varchar(128) DEFAULT NULL COMMENT '选中的施工员/领工员人员id',
  `grid_foreman_name` varchar(100) DEFAULT NULL COMMENT '选中的施工员/领工员人员姓名',
  `grid_siteman_id` varchar(128) DEFAULT NULL COMMENT '选中的现场负责人/安全员id',
  `grid_siteman_name` varchar(100) DEFAULT NULL COMMENT '选中的现场负责人/安全员姓名',
  `staff_number` int(11) DEFAULT NULL COMMENT '高峰工作人员数量',
  `construction_team` varchar(200) DEFAULT NULL COMMENT '施工队伍',
  `is_sole_duty` varchar(20) DEFAULT NULL COMMENT '网格安全员是否专职 是 否',
  `hold_cert` varchar(100) DEFAULT NULL COMMENT '网格安全员持证情况(证书名)',
  `is_formal_employee` varchar(20) DEFAULT NULL COMMENT '网格安全员是否正式职工 是 否',
  `notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `create_time` datetime DEFAULT NULL COMMENT '提交时间创建时间',
  `creator` varchar(255) DEFAULT NULL COMMENT '创建人id',
  `modify_time` datetime DEFAULT NULL COMMENT '更新时间',
  `modifier` varchar(255) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

CREATE TABLE `rail_linkapp_work_responsibility` (
  `id` varchar(32) NOT NULL,
  `work_id` varchar(32) DEFAULT NULL COMMENT '职位id',
  `content` varchar(1000) DEFAULT NULL COMMENT '职责内容',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `sort_index` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='后台穿透式安全管理/部门职责表';

CREATE TABLE `rail_linkapp_work_info` (
  `id` varchar(32) NOT NULL COMMENT '记录id',
  `name` varchar(255) DEFAULT NULL COMMENT '职位名称',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `modifier` varchar(255) DEFAULT NULL,
  `creator` varchar(255) DEFAULT NULL,
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `sort_index` int(11) DEFAULT NULL COMMENT '排序字段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='后台穿透式安全管理/部门表';

CREATE TABLE `rail_linkapp_org_responsibility` (
  `id` varchar(32) NOT NULL,
  `org_id` varchar(32) DEFAULT NULL COMMENT '部门id',
  `content` varchar(1000) DEFAULT NULL COMMENT '职责内容',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `sort_index` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='后台穿透式安全管理/部门职责表';

CREATE TABLE `rail_linkapp_org_info` (
  `id` varchar(32) NOT NULL COMMENT '记录id',
  `name` varchar(255) DEFAULT NULL COMMENT '部门名称',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `modifier` varchar(255) DEFAULT NULL,
  `creator` varchar(255) DEFAULT NULL,
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `sort_index` int(11) DEFAULT NULL COMMENT '排序字段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='后台穿透式安全管理/部门表';

CREATE TABLE `rail_linkapp_workflow_resolve` (
  `id` varchar(32) NOT NULL,
  `name` varchar(100) DEFAULT NULL COMMENT '工作职责名称',
  `workflow_file_url` varchar(1000) DEFAULT NULL COMMENT '流程图地址（pdf/图片）',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `sort_index` int(11) DEFAULT NULL COMMENT '排序字段',
  `creator` varchar(255) DEFAULT NULL COMMENT '创建人',
  `tenant_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='组织穿透/工作流程分解';

