-- 工作台 9900
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9900', NULL, '工作台', 't_workbench', '铁路工作台', 1, NULL, NULL, 9900, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 项目态势 9901
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9901', NULL, '项目态势', 't_dashboard', '项目大屏', 1, NULL, NULL, 9901, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 告警通知 9902
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9902', NULL, '告警通知', 't_alarm', '告警通知', 1, NULL, NULL, 9902, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 人员管理 9903
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9903', NULL, '人员管理', 't_person', '人员管理', 1, NULL, NULL, 9903, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 施工监测
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9904', NULL, '施工监测', 't_monitor', '施工监测', 1, NULL, NULL, 9904, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 营业线监测
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9905', NULL, '营业线监测', 't_bus_monitor', '营业线监测', 1, NULL, NULL, 9905, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 驻地安全
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9906', NULL, '驻地安全', 't_security', '驻地安全', 1, NULL, NULL, 9906, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- AI综合安防
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9907', NULL, 'AI综合安防', 't_ai_security', 'AI综合安防', 1, NULL, NULL, 9907, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 机械管理
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9908', NULL, '机械管理', 't_mechanical', '机械管理', 1, NULL, NULL, 9908, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 全景感知
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9909', NULL, '全景感知', 't_panorama', '全景感知', 1, NULL, NULL, 9909, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 设备管理
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9910', NULL, '设备管理', 't_equipment', '设备管理', 1, NULL, NULL, 9910, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 项目信息配置
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9911', NULL, '项目信息配置', 't_project_config', '项目信息配置', 1, NULL, NULL, 9911, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 视频监控配置
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9912', NULL, '视频监控配置', 't_video_config', '视频监控配置', 1, NULL, NULL, 9912, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- linkapp_tenant
-- 项目图片 project_img_url
-- 项目视频 project_video_url
-- 档案号 project_archive_no
-- 项目名称 platform_project_name
-- 项目简称 project_short_name
-- 涉铁类别 1上跨 2下穿 3临近 project_cross_type
-- 所在省市区 project_area
-- 项目地址 location
-- 项目主管 project_manager_
-- 项目类型 1代管 2代建 project_type
-- 建设状态 1待建设 2建设中 3已验收 project_status
-- 可研批复时间 project_confirm_time
-- 初设批复时间 project_init_confirm_time
-- 施工图批复时间 project_design_confirm_time
-- 预计开工时间 estimate_time
-- 预计竣工时间 completion_time
-- 计划工期 project_plan_duration
-- 建设单位 project_build_unit
-- 设计单位 design_unit
-- 监理单位 construction_control_unit
-- 施工单位 shigong_unit
-- 施工单位负责人 project_shigong_unit_leader
-- 开工时间 actual_start_time
-- 竣工时间 actual_completion_time
-- 涉及线别 project_line
-- 区间及里程 project_interval_distance
-- 工程进展 project_progress
-- 影响运输 project_transport
-- 存在问题 project_problem
-- 施工合同额 project_amount
-- 已付合同额 project_paid_amount
-- 合同工期 project_contract_duration
-- 工程概况 project_desc


-- 档案号
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_archive_no` VARCHAR(255) DEFAULT NULL COMMENT '档案号';
-- 项目图片
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_img_url` VARCHAR(255) DEFAULT NULL COMMENT '项目图片';
-- 项目视频
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_video_url` VARCHAR(255) DEFAULT NULL COMMENT '项目视频';
-- 项目简称
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_short_name` VARCHAR(255) DEFAULT NULL COMMENT '项目简称';
-- 涉铁类别 1上跨 2下穿 3临近
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_cross_type` tinyint(2) DEFAULT NULL COMMENT '涉铁类别 1上跨 2下穿 3临近';
-- 项目类型 1代管 2代建 project_type
ALTER TABLE linkappdb.linkapp_tenant modify column `project_type` tinyint(2) NOT NULL COMMENT '项目类型：1代管 2代建';
-- 建设状态 1待建设 2建设中 3已完工 4停工中 project_status
ALTER TABLE linkappdb.linkapp_tenant modify column `project_status` tinyint(2) NOT NULL COMMENT '建设状态 1待建设 2建设中 3已完工 4停工中';
-- 可研批复时间
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_confirm_time` DATETIME DEFAULT NULL COMMENT '可研批复时间';
-- 初设批复时间
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_init_confirm_time` DATETIME DEFAULT NULL COMMENT '初设批复时间';
-- 施工图批复时间
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_design_confirm_time` DATETIME DEFAULT NULL COMMENT '施工图批复时间';
-- 计划工期
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_plan_duration` VARCHAR(255) DEFAULT NULL COMMENT '计划工期';
-- 建设单位
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_build_unit` VARCHAR(255) DEFAULT NULL COMMENT '建设单位';
-- 施工单位负责人
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_shigong_unit_leader` VARCHAR(255) DEFAULT NULL COMMENT '施工单位负责人';
-- 涉及线别
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_line` VARCHAR(255) DEFAULT NULL COMMENT '涉及线别';
-- 区间及里程
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_interval_distance` VARCHAR(255) DEFAULT NULL COMMENT '区间及里程';
-- 工程进展
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_progress` VARCHAR(255) DEFAULT NULL COMMENT '工程进展';
-- 影响运输
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_transport` VARCHAR(255) DEFAULT NULL COMMENT '影响运输';
-- 存在问题
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_problem` VARCHAR(255) DEFAULT NULL COMMENT '存在问题';
-- 已付合同额
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_paid_amount` DOUBLE DEFAULT NULL COMMENT '已付合同额';
-- 合同工期
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `project_contract_duration` VARCHAR(255) DEFAULT NULL COMMENT '合同工期';


-- 新增 工点信息 表
CREATE TABLE `linkappdb`.`rail_project_point`
(
    `id`                   varchar(64) NOT NULL,
    `tenant_id_`           varchar(64)   DEFAULT NULL,
    `point_name_`          varchar(255)  DEFAULT NULL COMMENT '工点名称',
    `point_leader_id_`     varchar(32)   DEFAULT NULL COMMENT '工点负责人id',
    `point_leader_name_`   varchar(255)  DEFAULT NULL COMMENT '工点负责人姓名',
    `point_level_`         tinyint(2) DEFAULT NULL COMMENT '施工等级：1：Ⅰ级 2：Ⅱ级',
    `point_type_`          tinyint(2) DEFAULT NULL COMMENT '施工类别：1：A类 2：B类',
    `monitor_level_`       tinyint(2) DEFAULT NULL COMMENT '监测等级：1：Ⅰ级 2：Ⅱ级',
    `point_address_`       varchar(255)  DEFAULT NULL COMMENT '工点地址',
    `is_high_risk_`        tinyint(2) DEFAULT NULL COMMENT '是否高风险工点： 0：否 1：是',
    `is_danger_project_`   tinyint(2) DEFAULT NULL COMMENT '是否有危大工程： 0：否 1：是',
    `delete_state`         tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_`           bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`         datetime      DEFAULT NULL COMMENT '创建日期',
    `modify_id_`           bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`         datetime      DEFAULT NULL COMMENT '修改时间',
    `remark_`              text COMMENT '备注',
    `danger_project_name_` varchar(1024) DEFAULT NULL COMMENT '危险工程名称',
    PRIMARY KEY (`id`)
) COMMENT='工点信息表';

-- 设备管理 新增字段 生产厂家 联系方式 供货单位 负责人 联系方式
ALTER TABLE linkappdb.linkapp_device ADD COLUMN `manufacturer` VARCHAR(255) DEFAULT NULL COMMENT '生产厂家';
ALTER TABLE linkappdb.linkapp_device ADD COLUMN `manufacturer_tel_` VARCHAR(255) DEFAULT NULL COMMENT '联系方式';
ALTER TABLE linkappdb.linkapp_device ADD COLUMN `supplier` VARCHAR(255) DEFAULT NULL COMMENT '供货单位';
ALTER TABLE linkappdb.linkapp_device ADD COLUMN `responsible_person` VARCHAR(255) DEFAULT NULL COMMENT '负责人';
ALTER TABLE linkappdb.linkapp_device ADD COLUMN `responsible_person_tel_` VARCHAR(255) DEFAULT NULL COMMENT '联系方式';

-- 设备设施定位表
CREATE TABLE `linkappdb`.`rail_equipment_location`
(
    `id`                 varchar(64) NOT NULL,
    `tenant_id_`         varchar(64)  DEFAULT NULL,
    `equipment_type_`    varchar(64) DEFAULT NULL COMMENT '设备设施类型id -1为机械设备',
    `equipment_id_`      varchar(64)  DEFAULT NULL COMMENT '设备设施id',
    `equipment_name_`    varchar(255) DEFAULT NULL COMMENT '设备设施名称',
    `location_type_`     tinyint(2) DEFAULT NULL COMMENT '定位类型 1、北斗定位 2、人员定位 3、地图打点',
    `equipment_lng_`     varchar(255) DEFAULT NULL COMMENT '设备设施经度',
    `equipment_lat_`     varchar(255) DEFAULT NULL COMMENT '设备设施纬度',
    `create_id_`         bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`       datetime     DEFAULT NULL COMMENT '创建日期',
    `modify_id_`         bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`       datetime     DEFAULT NULL COMMENT '修改时间',
    `remark_`            text COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='铁路设备设施定位表';

-- 机械表
CREATE TABLE `linkappdb`.`rail_mechanical`
(
    `id`                                  varchar(64) NOT NULL,
    `tenant_id_`                          varchar(64)  DEFAULT NULL,
    `work_point_id_`                      varchar(64)  DEFAULT NULL COMMENT '工点id',
    `work_point_name_`                    varchar(255) DEFAULT NULL COMMENT '工点名称',
    `type_`                               varchar(64)  DEFAULT NULL COMMENT '机械类型',
    `name_`                               varchar(100) DEFAULT NULL COMMENT '机械名称',
    `code_`                               varchar(100) DEFAULT NULL COMMENT '机械编号',
    `status_`                             tinyint(2) DEFAULT '0' COMMENT '机械状态：0 在场 1 已退场',
    `owner_ship_type_`                    tinyint(2) DEFAULT NULL COMMENT '产权类型 1：自有 2：租赁（劳务自带）',
    `owner_ship_name_`                    varchar(255) DEFAULT NULL COMMENT '产权单位名称',
    `specification_`                      varchar(255) DEFAULT NULL COMMENT '规格型号',
    `enter_time_`                         datetime     DEFAULT NULL COMMENT '进场时间',
    `out_time_`                           datetime     DEFAULT NULL COMMENT '出场时间',
    `driver_ids_`                         varchar(512) DEFAULT NULL COMMENT '司机ids',
    `manager_id_`                         varchar(64)  DEFAULT NULL COMMENT '施工单位管理员id',
    `manager_name_`                       varchar(64)  DEFAULT NULL COMMENT '施工单位管理员姓名',
    `is_special_equipment_`               tinyint(2) DEFAULT NULL COMMENT '是否特种机械： 0：否 1：是',
    `machinery_acceptance_status_`        varchar(64)  DEFAULT NULL COMMENT '机械验收情况',
    `planned_removal_time_`               datetime     DEFAULT NULL COMMENT '机械计划拆除时间',
    `is_power_`                           tinyint(2) DEFAULT NULL COMMENT '有无助力： 0：无 1：有',
    `manufacturer_`                       varchar(255) DEFAULT NULL COMMENT '生产厂家',
    `manufacturer_tel_`                   varchar(255) DEFAULT NULL COMMENT '联系方式',
    `maintainer_`                         varchar(255) DEFAULT NULL COMMENT '维护人',
    `maintainer_tel_`                     varchar(255) DEFAULT NULL COMMENT '联系方式',
    `maintainer_date_`                    datetime     DEFAULT NULL COMMENT '维护日期',
    `inspection_file_list_`               text COMMENT '验收文件列表',
    `certificate_file_list_`              text COMMENT '合格证书文件列表',
    `appraisal_certificate_file_list_`    text COMMENT '鉴定证书文件列表',
    `registration_certificate_file_list_` text COMMENT '登记使用证书文件列表',
    `equipment_photo_file_list_`          text COMMENT '机械照片文件列表',
    `specifical_info_`                    text COMMENT '机械特殊信息json',
    `delete_state`                        tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_`                          bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`                        datetime     DEFAULT NULL COMMENT '创建日期',
    `modify_id_`                          bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`                        datetime     DEFAULT NULL COMMENT '修改时间',
    `remark_`                             text COMMENT '备注',
    `x_offset_` double DEFAULT NULL COMMENT '定位x偏移量',
    `y_offset_` double DEFAULT NULL COMMENT '定位y偏移量',
    PRIMARY KEY (`id`)
) COMMENT='铁路机械表';

-- 机械类型 字典
-- 机械类型 字典
INSERT INTO linkappdb.sys_dict
(id_, dict_name_, dict_code_, description_, create_id_, create_time_, update_id_, update_time_)
VALUES(4, '机械类型', 'machine_type', '机械类型', NULL, NULL, NULL, NULL);
-- 机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4,'装载机', 1,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 1, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '压路机',2,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 2, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4,'旋挖钻', 3,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 3, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '挖掘机', 4, '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 4, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4,  '汽车起重机',5, '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 5, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '门式起重机',6,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 6, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '塔式起重机',7,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 7, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '随车起重机', 8, '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 8, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '酒水车',9,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 9, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '混凝土泵车',10,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 10, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '发电机',11,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 11, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '冲击钻',12,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 12, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.sys_dict_item
(dict_id_, item_text_, item_value_, description_, sort_order_, status_, create_id_, create_time_, update_id_, update_time_, is_sync_)
VALUES(4, '叉车',13,  '机械类型 1 装载机 2 压路机 3 旋挖钻 4 挖掘机 5 汽车起重机 6 门式起重机 7 塔式起重机 8 随车起重机 9 酒水车 10 混凝土泵车 11 发电机 12 冲击钻 13 叉车', 13, 1, NULL, NULL, NULL, NULL, 0);

-- 机械管理
    -- 一机一卡
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99081', NULL, '一机一卡', 't_one_machine_one_card', '一机一卡', 2, NULL, NULL, 9908.1, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

ALTER TABLE `linkappdb`.`rail_mechanical`
    -- 机械状态：0 在场 1 已退场
    ADD COLUMN `status_` TINYINT(2) DEFAULT '0' COMMENT '机械状态：0 在场 1 已退场' AFTER `code_`;

-- 机械关联设备表
CREATE TABLE `linkappdb`.`rail_mechanical_ref_device`
(
    `id_`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`          varchar(64)  DEFAULT NULL COMMENT '租户id',
    `rail_mechanical_id_` varchar(64) NOT NULL COMMENT '机械id',
    `device_type_`        tinyint(2) NOT NULL COMMENT '关联设备类型 1 主机 2 定位设备 3 视频监控设备 4 其他',
    `device_id_`          varchar(64) NOT NULL COMMENT '设备id',
    `device_code_`        varchar(64) NOT NULL COMMENT '设备code',
    `device_name_`        varchar(255) DEFAULT NULL COMMENT '设备名称',
    `delete_state`        tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_`          bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`        datetime     DEFAULT NULL COMMENT '创建日期',
    `modify_id_`          bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`        datetime     DEFAULT NULL COMMENT '修改时间',
    `remark_`             text COMMENT '备注',
    PRIMARY KEY (`id_`)
) COMMENT='机械关联设备表';

-- 施工监测
    -- 吊车安全监测
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99041', '9904', '吊车安全监测', 't_crane_safety_inspection', '吊车安全监测', 2, NULL, NULL, 9904.1, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- 吊车垂直面保护区表
CREATE TABLE linkappdb.`rail_crane_vertical_protection_area`
(
    `id_`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`       varchar(64) DEFAULT NULL COMMENT '租户id',
    `region_name_`     varchar(50) NOT NULL COMMENT '区域名称',
    `work_station_id_` varchar(64) NOT NULL COMMENT '所属工点',
    `work_time_`       text        NOT NULL COMMENT '作业时间',
    `location_`        text        NOT NULL COMMENT '区域位置',
    `enabled_flag_`    int(11) NOT NULL DEFAULT '1' COMMENT '是否启用 0-不启用，1-启用',
    `delete_state`     tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_`       bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`     datetime    DEFAULT NULL COMMENT '创建日期',
    `modify_id_`       bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`     datetime    DEFAULT NULL COMMENT '修改时间',
    `remark_`          text COMMENT '备注',
    `alarm_flag_`      tinyint(2) DEFAULT '0' COMMENT '是否有警报 1-有，0-无',
    PRIMARY KEY (`id_`)
) COMMENT='吊车垂直面保护区表';

-- 吊车作业区域表
CREATE TABLE `linkappdb`.`rail_crane_operation_area`
(
    `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_` varchar(64) DEFAULT NULL COMMENT '租户id',
    `region_name_` varchar(50) NOT NULL COMMENT '区域名称',
    `work_time_` text NOT NULL COMMENT '作业时间',
    `location_` text NOT NULL COMMENT '区域位置',
    `enabled_flag_` int(11) NOT NULL DEFAULT '1' COMMENT '是否启用 0-不启用，1-启用',
    `delete_state` tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    `alarm_flag_` tinyint(2) DEFAULT '0' COMMENT '是否有警报 1-有，0-无',
    PRIMARY KEY (`id_`)
) COMMENT='吊车作业区域表';

-- 作业区域表关联机械表
CREATE TABLE `linkappdb`.`rail_crane_operation_area_ref_mechanical`
(
    `id_`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `operation_area_id_`  bigint(20) NOT NULL COMMENT '作业区域id',
    `mechanical_id_` varchar(64) NOT NULL COMMENT '绑定机械',
    `delete_state` tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`)
) COMMENT='作业区域表关联机械表';

-- 设备设施定位数据表
CREATE TABLE `linkappdb`.`rail_equipment_location_data`
(
    `id_`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`     varchar(64) DEFAULT NULL COMMENT '租户id',
    `equipment_id_`  varchar(64) NOT NULL COMMENT '设备设施id',
    `equipment_code_`  varchar(64) NOT NULL COMMENT '设备设施编码',
    `location_time_` datetime NOT NULL COMMENT '定位时间',
    `location_lng_`  varchar(20) NOT NULL COMMENT '经度',
    `location_lat_`  varchar(20) NOT NULL COMMENT '纬度',
    `move_flag_`     int(11) DEFAULT 0 COMMENT '是否移动 0-未移动，1-移动',
    `create_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`),
    index `idx_tenant_id_equipment_id_location_time_move_flag_` (`tenant_id_`, `equipment_id_`, `location_time_`, `move_flag_`)
) COMMENT='设备设施定位数据表';
-- 机械工作记录表
CREATE TABLE `linkappdb`.`rail_mechanical_work_record`
(
    `id_`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`     varchar(64) DEFAULT NULL COMMENT '租户id',
    `mechanical_id_` varchar(64) NOT NULL COMMENT '机械id',
    `start_time_`    datetime    NOT NULL COMMENT '作业开始时间',
    `end_time_`      datetime    DEFAULT NULL COMMENT '作业结束时间',
    `work_time_`     int(11) DEFAULT NULL COMMENT '作业时长',
    `create_id_`     bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`   datetime    DEFAULT NULL COMMENT '创建日期',
    `modify_id_`     bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`   datetime    DEFAULT NULL COMMENT '修改时间',
    `remark_`        text COMMENT '备注',
    PRIMARY KEY (`id_`),
    KEY              `idx_tenant_id_mechanical_id_start_time_end_time_` (`tenant_id_`,`mechanical_id_`,`start_time_`,`end_time_`)
)COMMENT='机械工作记录表';
-- 吊机工作记录表
CREATE TABLE `linkappdb`.`rail_crane_business_record`
(
    `id_`                         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`                  varchar(64)    DEFAULT NULL COMMENT '租户id',
    `mechanical_id_`              varchar(64) NOT NULL COMMENT '机械id',
    `device_id_`                  varchar(64) NOT NULL COMMENT '设备id',
    `device_code_`                varchar(64) NOT NULL COMMENT '设备code',
    `device_name_`                varchar(255)   DEFAULT NULL COMMENT '设备名称',
    `work_start_time_`            datetime       DEFAULT NULL COMMENT '工作循环起始时间',
    `work_end_time_`              datetime       DEFAULT NULL COMMENT '工作循环结束时间',
    `arm_force_`                  decimal(10, 2) DEFAULT NULL COMMENT '力臂',
    `alarm_arm_force_`            int(11) DEFAULT '0' COMMENT '力臂告警 0-正常，1-告警',
    `big_arm_len_`                decimal(10, 2) DEFAULT NULL COMMENT '大臂长度',
    `alarm_big_arm_len_`          int(11) DEFAULT '0' COMMENT '大臂长度告警 0-正常，1-告警',
    `level_x_`                    decimal(10, 2) DEFAULT NULL COMMENT '水平度X',
    `alarm_level_x_`              int(11) DEFAULT '0' COMMENT '水平度X告警 0-正常，1-告警',
    `level_y_`                    decimal(10, 2) DEFAULT NULL COMMENT '水平度Y',
    `alarm_level_y_`              int(11) DEFAULT '0' COMMENT '水平度Y告警 0-正常，1-告警',
    `wind_speed_`                 decimal(10, 2) DEFAULT NULL COMMENT '风速',
    `alarm_wind_speed_`           int(11) DEFAULT '0' COMMENT '风速告警 0-正常，1-告警',
    `rotation_`                   decimal(10, 2) DEFAULT NULL COMMENT '回转角度',
    `alarm_rotation_`             int(11) DEFAULT '0' COMMENT '回转角度告警 0-正常，1-告警',
    `pitch_`                      decimal(10, 2) DEFAULT NULL COMMENT '俯仰角',
    `alarm_pitch_`                int(11) DEFAULT '0' COMMENT '俯仰角告警 0-正常，1-告警',
    `moment_`                     decimal(10, 2) DEFAULT NULL COMMENT '主钩力矩',
    `alarm_moment_`               int(11) DEFAULT '0' COMMENT '主钩力矩告警 0-正常，1-告警',
    `weight_`                     decimal(10, 2) DEFAULT NULL COMMENT '主钩吊重',
    `alarm_weight_`               int(11) DEFAULT '0' COMMENT '主钩吊重告警 0-正常，1-告警',
    `inclination_`                decimal(10, 2) DEFAULT NULL COMMENT '倾斜度',
    `alarm_inclination_`          int(11) DEFAULT '0' COMMENT '倾倒告警 0-正常，1-告警',
    `wind_level_`                 decimal(10, 2) DEFAULT NULL COMMENT '风级',
    `arm_height_`                 decimal(10, 2) DEFAULT NULL COMMENT '臂尖高度',
    `work_hour_`                  decimal(10, 2) DEFAULT NULL COMMENT '作业时长',
    `idling_hour_`                decimal(10, 2) DEFAULT NULL COMMENT '怠速时长',
    `work_total_hour_`            decimal(10, 2) DEFAULT NULL COMMENT '总工作时长',
    `hoisting_times_`             int(11) DEFAULT NULL COMMENT '吊装次数',
    `alarm_safe_limit_`           int(11) DEFAULT '0' COMMENT '安全限界告警 0-正常，1-告警',
    `alarm_weight_limit_`         int(11) DEFAULT '0' COMMENT '作业展臂最不利工况可起重吊重告警 0-正常，1-告警',
    `alarm_big_arm_len_limit_`    int(11) DEFAULT '0' COMMENT '最大臂长告警 0-正常，1-告警',
    `alarm_intrusion_prevention_` int(11) DEFAULT '0' COMMENT '施工防侵限告警 0-正常，1-告警',
    `alarm_dump_prevention_`      int(11) DEFAULT '0' COMMENT '防倾倒侵限告警 0-正常，1-告警',
    `alarm_state_`                int(11) DEFAULT '0' COMMENT '总告警状态 0-正常，1-告警',
    `create_id_`                  bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`                datetime       DEFAULT NULL COMMENT '创建日期',
    `modify_id_`                  bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`                datetime       DEFAULT NULL COMMENT '修改时间',
    `remark_`                     text COMMENT '备注',
    PRIMARY KEY (`id_`),
    KEY                           `idx_tenant_id_mechanical_id_device_id_create_time_` (`tenant_id_`,`mechanical_id_`,`device_id_`,`create_time_`)
) COMMENT='吊机业务数据记录表';


ALTER TABLE `linkappdb`.`rail_mechanical`
-- 新增字段
-- 五定图时间：年月日时分
    ADD COLUMN `five_chart_time_` datetime DEFAULT NULL COMMENT '五定图时间';
ALTER TABLE `linkappdb`.`rail_mechanical`
-- 五定图作业内容
    ADD COLUMN `five_chart_content_` varchar(255) DEFAULT NULL COMMENT '五定图作业内容';
ALTER TABLE `linkappdb`.`rail_mechanical`
-- 五定图图片：五张图片；
    ADD COLUMN `five_chart_img_` text DEFAULT NULL COMMENT '五定图图片';
