CREATE TABLE `rail_linkapp_electric_fire_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备唯一记录code',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `temperature_1` double(20,2) DEFAULT NULL,
  `temperature_2` double(20,2) DEFAULT NULL,
  `temperature_3` double(20,2) DEFAULT NULL,
  `temperature_4` double(20,2) DEFAULT NULL,
  `active_power` double(20,2) DEFAULT NULL,
  `residual_current` double(20,2) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

