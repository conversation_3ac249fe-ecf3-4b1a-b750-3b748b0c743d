ALTER TABLE app_electricy_records add `a_phase_voltage` double(20,2) DEFAULT NULL COMMENT 'a相电压';
ALTER TABLE app_electricy_records add  `b_phase_voltage` double(20,2) DEFAULT NULL COMMENT 'b相电压';
ALTER TABLE app_electricy_records add  `c_phase_voltage` double(20,2) DEFAULT NULL COMMENT 'c相电压';
ALTER TABLE app_electricy_records add  `a_phase_current` double(20,2) DEFAULT NULL COMMENT 'a相电流';
ALTER TABLE app_electricy_records add  `b_phase_current` double(20,2) DEFAULT NULL COMMENT 'b相电流';
ALTER TABLE app_electricy_records add  `c_phase_current` double(20,2) DEFAULT NULL COMMENT 'c相电流';
ALTER TABLE app_electricy_records add  `active_power` double(20,2) DEFAULT NULL COMMENT '有功总功率';

ALTER TABLE rail_linkapp_grid_management_info add  `grid_meet_id` varchar(128) DEFAULT NULL COMMENT '应急联系人id';
ALTER TABLE rail_linkapp_grid_management_info add  `grid_meet_name` varchar(100) DEFAULT NULL COMMENT '紧急联系人姓名';
ALTER TABLE rail_linkapp_grid_management_info add  `announcement_file_url` varchar(500) DEFAULT NULL COMMENT '网格公示牌';


INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '取样人员', '17', '取样人员', '12', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '见证员', '18', '取样人员', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '保安', '19', '保安', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '保洁', '20', '保洁', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '后勤', '21', '后勤', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '装载机司机', '22', '装载机司机', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '挖掘机司机', '23', '挖掘机司机', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '物料提升机司机', '24', '物料提升机司机', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '电工', '25', '电工', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '架子工', '26', '架子工', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '安装起重工', '27', '安装起重工', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '挖掘铲运和桩工机械司机', '28', '挖掘铲运和桩工机械司机', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '起重信号工', '29', '起重信号工', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '建筑起重机械安装拆卸工', '30', '建筑起重机械安装拆卸工', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '爆破工', '31', '爆破工', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '塔式起重机司机', '32', '塔式起重机司机', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '塔式起重机安装拆卸工', '33', '塔式起重机安装拆卸工', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '挖掘机驾驶员', '34', '挖掘机驾驶员', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '轮式起重机司机', '35', '轮式起重机司机', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '泵车司机', '36', '泵车司机', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '叉车司机', '37', '叉车司机', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('5', '电焊工', '38', '电焊工', '11', '1', NULL, NULL, NULL, NULL, '0');


ALTER TABLE linkapp_device  add `collect_freq` bigint(20) DEFAULT NULL;
 ALTER TABLE linkapp_device  add  `elect_status` int(11) DEFAULT NULL COMMENT '断路器 电闸状态0合闸，1分闸';