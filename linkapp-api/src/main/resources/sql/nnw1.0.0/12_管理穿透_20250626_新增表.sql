
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for rail_keywork_list
-- ----------------------------
DROP TABLE IF EXISTS `rail_keywork_list`;
CREATE TABLE `rail_keywork_list`  (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `content_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类/作业内容',
                                      `level` int(11) NULL DEFAULT NULL COMMENT '1分类，2内容',
                                      `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '上级id',
                                      `tenant_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
                                      `is_used` int(11) NULL DEFAULT NULL COMMENT '0未使用，1使用',
                                      `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                      `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 73 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '关键作业清单表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of rail_keywork_list
-- ----------------------------
INSERT INTO `rail_keywork_list` VALUES (1, '路基工程', 1, 0, '9d8ec26eb12a86bf7b2924849a23b233', 0, '2025-06-26 11:34:34', NULL);
INSERT INTO `rail_keywork_list` VALUES (2, '桥梁工程', 1, 0, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (3, '隧道工程', 1, 0, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (4, '站后及“四电”工程', 1, 0, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (5, '营业线及邻近营业线', 1, 0, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (6, '其他', 1, 0, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (7, '深路堑开挖作业', 2, 1, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (8, '高陡边坡作业', 2, 1, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (9, '抗滑桩基础开挖', 2, 1, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (10, '抗板墙基础开挖', 2, 1, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (11, '高墩身拆模', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 1, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (12, '水中墩混凝土浇筑', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, '2025-06-26 11:35:10', NULL);
INSERT INTO `rail_keywork_list` VALUES (13, '水中墩拆模', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, '2025-06-26 11:35:10', NULL);
INSERT INTO `rail_keywork_list` VALUES (14, '挂篮安装', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, '2025-06-26 11:35:10', NULL);
INSERT INTO `rail_keywork_list` VALUES (15, '挂篮拆除', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, '2025-06-26 11:35:10', NULL);
INSERT INTO `rail_keywork_list` VALUES (16, '挂篮行走', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (17, '悬臂混凝土浇筑', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (18, '栈桥搭设作业', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (19, '栈桥拆除作业', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (20, '钢围堰就位', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (21, '架桥机过孔', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (22, '架桥机安装', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (23, '架桥机拆除', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (24, '移动模架现浇施工', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (25, '移动模架过孔施工', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (26, '液压爬模安装', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (27, '液压爬模现浇', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (28, '液压爬模爬升', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (29, '高墩身混凝土浇筑', 2, 2, '9d8ec26eb12a86bf7b2924849a23b233', 1, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (33, '隧道初期支护与开挖作业', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (34, '进洞出洞施工作业', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (35, '隧道衬砌拆换', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (36, '隧道瓦斯段落施工作业', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (37, '隧道TBM(盾构)组装、拆解作业', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (38, '拆解作业', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (39, '隧道TBM(盾构)始发作业', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (40, '隧道TBM(盾构)接收作业', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (41, '隧道盾构带压开仓作业', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (42, '隧道盾构常压开仓作业', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (43, '隧道TBM(盾构)空推平移', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (44, '隧道TBM(盾构)特殊地段施工', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (45, '隧道TBM(盾构)联络通道施工', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (46, '隧道TBM(盾构)管片拆除', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (47, '隧道TBM(盾构)长时间停机', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (48, '隧道TBM(盾构)盾尾刷更换', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (49, '隧道TBM(盾构)水平运输', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (50, '隧道TBM(盾构)独立竖井施工', 2, 3, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (51, '大型钢结构拼装作业', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (52, '工程线运输', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (53, '铺轨作业', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (54, '房建工程幕墙施工作业', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (55, '房建工程屋面施工', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (56, '高度≥15 m的通信铁塔组立作业', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (57, '连锁试验', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (58, '电力停送电作业', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (59, '牵引供电停送电作业', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (60, '短路试验作业', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (61, '接触网安装架设', 2, 4, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (62, '天窗点施工作业', 2, 5, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (63, '动火作业', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 1, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (64, '爆破作业', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (65, '起重吊装作业', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 1, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (66, '大型机械设备安装', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 1, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (67, '拆除作业', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 1, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (68, '深基坑开挖作业', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (69, '支架拼装作业', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 1, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (70, '支架预压作业', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 1, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (71, '支架拆除作业', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 1, NULL, NULL);
INSERT INTO `rail_keywork_list` VALUES (72, '下穿既有设施施工作业', 2, 6, '9d8ec26eb12a86bf7b2924849a23b233', 0, NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Data Transfer

 Source Server         : 涉铁工程
 Source Server Type    : MySQL
 Source Server Version : 50644
 Source Host           : ************:15051
 Source Schema         : linkappdb

 Target Server Type    : MySQL
 Target Server Version : 50644
 File Encoding         : 65001

 Date: 27/06/2025 11:23:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for rail_safe_table_item
-- ----------------------------
DROP TABLE IF EXISTS `rail_safe_table_item`;
CREATE TABLE `rail_safe_table_item`  (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                         `keywork_id` bigint(20) NULL DEFAULT NULL COMMENT '关联作业id',
                                         `check_content_classify` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检查内容父类',
                                         `check_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检查内容',
                                         `check_require` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '管理/控制要求',
                                         `check_require_intro` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '管理/控制要求说明',
                                         `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目id',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 141 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '安全条件确认表字段表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of rail_safe_table_item
-- ----------------------------
INSERT INTO `rail_safe_table_item` VALUES (1, 29, '', '作业许可证', '证件', '是否办理高处作业许可证件', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (2, 29, '', '设备操作', '相关证件', '设备操作人员是否持证上岗，是否存在酒后作业', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (3, 29, '', '设备操作', '维修保养', '设备是否按期进行了维修保养', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (4, 29, '', '施工过程', '模板安装', '模板安装满足方案要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (5, 29, '', '施工过程', '混凝土浇筑', '振捣器等设备合格；施工区域严禁非施工人员进入', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (6, 29, '', '高处作业', '高处作业', '临边设置安全防护、安全通道；人员劳保用品穿戴情况，必要时设母索', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (7, 29, '', '临时用电', '相关证件', '电工证件情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (8, 29, '', '临时用电', '现场设备', '电箱、电缆等是否完好；接线、接地是否规范，是否安装剩余电流动作保护器', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (9, 29, '', '特殊时间', '夜间', '夜间施工前，各工段必须提前采取必要的安全保障措施；当晚作业使用的工具、材料等必须在白天进行全面认真检查；夜间施工应有足够的照明', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (10, 29, '', '特殊时间', '特殊天气', '暴风及暴雨后，应对高处作业安全设施逐一加以检查；遇有五级以上强风、浓雾、雷雨、等恶劣的气候，施工人员不得从事露天高空作业', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (11, 29, '', '其他措施', '安全技术交底', '分级交底情况；交底人员覆盖情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (12, 29, '', '其他措施', '班前喊话', '当日班前会开展；作业分工、风险传达；应急措施', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (13, 11, '', '作业许可证', '证件', '是否办理动火作业许可证；\r\n是否办理高处作业许可证；\r\n是否办理吊装作业许可证', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (14, 11, '', '汽车吊', '相关证件', '确认汽车吊和人员巳报批，定期维保', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (15, 11, '', '汽车吊', '安全装置', '设备运行正常，限载、限位装置等工作状态正常', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (16, 11, '', '吊索具', '起重钢丝绳', '起重采用钢丝绳，外观、合格证、质保资料、使用条件符合要求； \r\n接头插编长度和外观符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (17, 11, '', '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (18, 11, '', '高处作业', '高处作业', '临边设置安全防护、安全通道；\r\n人员劳保用品穿戴情况，必要时设母索', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (19, 11, '', '动火作业', '相关证件', '人员证件齐全，已报批', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (20, 11, '', '动火作业', '防火措施', '周围易燃、易爆物品已清理，隔离；\r\n灭火器配备；\r\n人员防护用品', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (21, 11, '', '警戒措施', '指挥人员', '司索工证件情况；\r\n通信情况；\r\n起吊条件满足“十不吊”', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (22, 11, '', '警戒措施', '警戒措施', '警示牌；\r\n封闭措施、警戒人员', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (23, 11, '', '其他措施', '安全技术交底', '分级交底情况；\r\n交底人员覆盖情况；\r\n明确拆除顺序', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (24, 11, '', '其他措施', '班前喊话', '当日班前会开展；\r\n作业分工、风险传达；\r\n应急措施', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (25, 11, '', '其他', '', '', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (26, 63, '', '施工条件', '动火令', '动火作业许可证是否已办理并审批通过', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (27, 63, '', '施工条件', '作业人员资质', '动火操作人员是否持有有效的特种作业操作证(焊工证等）', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (28, 63, '', '施工条件', '作业前培训', '动火操作人员是否接受了动火作业安全操作规程及应急预案培训', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (29, 63, '', '施工条件', '作业环境检查', '作业现场是否已清理,无易燃易爆物品,或已采取有效隔离措施', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (30, 63, '', '作业准备', '消防器材准备', '灭火器、消防沙、消防水带等消防器材是否已配备并处于可用状态', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (31, 63, '', '作业准备', '个人防护', '动火操作人员是否穿戴了防火服防护眼镜防护手套等个人防护装备', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (32, 63, '', '作业准备', '动火设备检查', '动火设备(如焊枪切割机等)是否完好,无损坏或泄漏', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (33, 63, '', '作业准备', '气体管理', '氧气瓶、乙快瓶等气体瓶是否已固定,且间距符合要求,无泄漏', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (34, 63, '', '作业过程', '动火作业监护', '是否有专门的监护人负责动火作业现场的安全监护', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (35, 63, '', '作业过程', '作业规范', '动火作业是否按照操作规程进行,无违规操作', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (36, 63, '', '作业过程', '防火措施', '作业现场是否设置了防火屏障或遮挡物,防止火花飞溅', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (37, 63, '', '作业后检查', '现场清理', '动火作业结束后,是否及时清理现场,消除安全隐患', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (38, 63, '', '作业后检查', '设备归位', '动火设备是否中已归位,并妥善存放', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (39, 63, '', '作业后检查', '消防器材复位', '消防器材是否已复位,并处于可用状态', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (40, 63, '', '其他措施', '安全技术交底', '是否进行了安全技术交底,明确作业风险及防控措施', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (41, 63, '', '其他措施', '班前喊话', '是否开展了班前会,强调了作业安全要求及注意事项', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (42, 65, '', '钢构件', '吊耳', '外观检查、焊缝;吊耳孔直径、主板板厚', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (43, 65, '', '钢构件', '外观', '各构件外观检查符合要求；\r\n编号及放置方向符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (44, 65, '', '吊索具检查', '钢丝绳', '外观检查、长度、绳卡； \r\n质保资料', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (45, 65, '', '吊索具检查', '卸扣', '外观检查(扣体横栓）;\r\n质保资料', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (46, 65, '', '吊装设备', '相关证件', '特种设备和特种作业人员报批', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (47, 65, '', '吊装设备', '支垫', '支垫措施稳固', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (48, 65, '', '吊装设备', '安全装置', '限载限位装置: \r\n声光警示装置', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (49, 65, '', '同步性措施', '警戒措施', '警示牌; \r\n封闭措施警戒人员', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (50, 65, '', '同步性措施', '缆风绳', '外观、位置、长度通信情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (51, 65, '', '同步性措施', '指挥人员', '证件情况；通信情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (52, 65, '', '同步性措施', '人员站位', '人员分工表；参观人员站位;作业人员和管理人员的站位及数量', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (53, 65, '', '安全防护及文明施工', '高处作业', '临边防护安全通道情况;人员劳保用品穿戴情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (54, 65, '', '安全防护及文明施工', '文明施工', '现场垃圾回收;设备防漏油措施(吸油毡)', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (55, 65, '', '试吊', '初步试吊', '开始起吊时,先将构件吊离地面 200~300 mm 后停止起吊,并检查吊机稳定性制动装置可靠性构件平衡性和绑扎牢固性等,待确认无误后,方可继续起吊', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (56, 65, '', '试吊', '试吊观察', '主钩钢丝绳是否均匀绷紧；卸扣与上吊耳是否顺直；吊耳有无异响油漆剥落；重心是否明显偏差', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (57, 65, '', '其他措施', '安全技术交底', '分级交底情况；交底人员覆盖情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (58, 65, '', '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (59, 66, '', '施工条件', '施工方案', '施工方案是否编审、审批齐全有效', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (60, 66, '', '施工条件', '安全技术交底', '风险因素分析完毕,分解到具体操作层,已完成培训及安全技术交底 ', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (61, 66, '', '施工条件', '安全技术措施', '安全技术措施已经按照专项方案的要求执行；是否检查施工作业区域不良地质情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (62, 66, '', '施工条件', '应急准备', '应急物资设备到位,通信畅通,应急照明、消防器材、医疗用品等符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (63, 66, '', '施工条件', '机械设备', '进场验收记录齐全有效,安全交底、防护到位', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (64, 66, '', '施工过程', '安装顺序', '按照方案,明确当日安装内容及顺序', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (65, 66, '', '试吊', '初步试吊', '开始起吊时,先将构件吊离地面 200~300 mm 后停止起吊,并检查吊机稳定性制动装置可靠性构件平衡性和绑扎牢固性等,待确认无误后,方可继续起吊', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (66, 66, '', '试吊', '试吊观察', '主钩钢丝绳是否均匀绷紧；卸扣与上吊耳是否顺直；吊耳有无异响油漆剥落；重心是否明显偏差', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (67, 66, '', '吊装设备', '吊装机械', '满足额定荷载要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (68, 66, '', '吊装设备', '相关证件', '特种设备和特种作业人员报批', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (69, 66, '', '吊装设备', '支垫', '支垫措施稳固', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (70, 66, '', '吊装设备', '安全装置', '限载限位装置；声光警示装置', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (71, 66, '', '作业人员', '特种人员', '特种人员配足配齐且持证上岗', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (72, 66, '', '同步性措施', '警戒措施', '警示牌; \r\n封闭措施警戒人员', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (73, 66, '', '同步性措施', '缆风绳', '外观、位置、长度通信情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (74, 66, '', '同步性措施', '指挥人员', '证件情况；通信情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (75, 66, '', '同步性措施', '人员站位', '人员分工表；参观人员站位;作业人员和管理人员的站位及数量', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (76, 66, '', '安全防护及文明施工', '高处作业', '临边防护安全通道情况;人员劳保用品穿戴情况', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (77, 66, '', '安全防护及文明施工', '文明施工', '现场垃圾回收;设备防漏油措施(吸油毡)', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (78, 66, '', '周边环境核查', '作业环境', '作业环境是否组织检查验收,施工现场是否满足施工作业要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (79, 66, '', '其他措施', '安全技术交底', '分级交底情况;\r\n明确工艺顺序', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (80, 66, '', '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (81, 67, NULL, '作业人员', '证件', '架子工是否持有效证件上岗', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (82, 67, NULL, '方案和交底', '方案', '是否编制专项方案,超过一定规模的高支模作业是否组织专家论证', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (83, 67, NULL, '方案和交底', '交底', '技术交底和安全技术交底是否到位', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (84, 67, NULL, '起重设备', '相关证件', '确认起重设备和人员(操作手、信号司索工等)已报批,定期维保', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (85, 67, NULL, '起重设备', '安全装置', '设备运行正常,限载、限位装置等工作状态正常', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (86, 67, NULL, '吊索具', '起重钢丝绳', '起重采用钢丝绳,外观、合格证、质保资料、使用条件符合要求；\r\n接头插编长度和外观符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (87, 67, NULL, '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (88, 67, NULL, '过程管控', '高处作业', '临边设置安全防护、安全通道;人员劳保用品穿戴情况,必要时设母索', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (89, 67, NULL, '过程管控', '拆除顺序', '是否严格按照方案进行拆除作业', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (90, 67, NULL, '其他措施', '应急预案', '应急救援预案是否编制齐全,应急救援培训是否到位,应急处置措施是否齐全有效,施工作业人员应知应会是否培训口是否到位', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (91, 67, NULL, '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (92, 69, NULL, '作业人员', '证件', '架子工是否持有效证件上岗', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (93, 69, NULL, '方案和交底', '方案', '是否编制专项方案,超过一定规模的高支模作业是否组织专家论证 ', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (94, 69, NULL, '方案和交底', '交底', '技术交底和安全技术交底是否到位', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (95, 69, NULL, '起重设备', '相关证件', '确认起重设备和人员(操作手、信号司索工等)已报批,定期维保', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (96, 69, NULL, '起重设备', '安全装置', '设备运行正常,限载、限位装置等工作状态正常', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (97, 69, NULL, '吊索具', '起重钢丝绳', '起重采用钢丝绳,外观、合格证、质保资料、使用条件符合要求；\r\n接头插编长度和外观符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (98, 69, NULL, '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (99, 69, NULL, '过程管控', '地基承载力', '地基承载力是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (100, 69, NULL, '过程管控', '扫地杆', '地杆距离是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (101, 69, NULL, '过程管控', '剪刀撑', '剪刀撑设置位置和角度是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (102, 69, NULL, '过程管控', '连墙件', '连墙件设置是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (103, 69, NULL, '过程管控', '悬挑钢梁锚汘鑹堆固支座', '悬挑钢梁锚固支座是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (104, 69, NULL, '过程管控', '步距、跨距', '步距、距是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (105, 69, NULL, '过程管控', '安全防护网', '手架水平防护、外侧安全网设置是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (106, 69, NULL, '过程管控', '防护栏杆', '作业层防护栏杆是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (107, 69, NULL, '过程管控', '脚手板', '脚手板铺设是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (108, 69, NULL, '过程管控', '构配件', '构配件表观质量是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (109, 69, NULL, '过程管控', '高处作业', '高处作业人员是否按要求使用劳动防护用品', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (110, 69, NULL, '其他措施', '应急预案', '应急救援预案是否编制齐全,应急救援培训是否到位,应急处置措施是否齐全有效,施工作业人员应知应会是否培训口是否到位', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (111, 69, NULL, '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (112, 70, NULL, '作业人员', '证件', '架子工是否持有效证件上岗', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (113, 70, NULL, '方案和交底', '方案', '是否编制专项方案,超过一定规模的高支模作业是否组织专家论证 ', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (114, 70, NULL, '方案和交底', '交底', '技术交底和安全技术交底是否到位', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (115, 70, NULL, '方案和交底', '观测', '是否提前设置支架沉降观测点', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (116, 70, NULL, '施工过程', '指挥', '是否设置专人统一指挥人员', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (117, 70, NULL, '施工过程', '地基承载力', '地基承载力是否符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (118, 70, NULL, '施工过程', '吊装过程', '避免吊装碰撞支架', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (119, 70, NULL, '吊索具', '起重钢丝绳', '起重采用钢丝绳,外观、合格证、质保资料、使用条件符合要求；\r\n接头插编长度和外观符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (120, 70, NULL, '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (121, 70, NULL, '过程管控', '荷载', '支架预压荷载不应小于支架承受的混凝土结构恒载与模板重量之和的1.1倍\r\n支架预压区域应划分成若干预压单元,每个预压单元内实际预压荷载强度的最大值不应超过该预压单元内预压荷载强度平均值的110%', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (122, 70, NULL, '过程管控', '荷载', '每个预压单元内的预压荷载可采用均布形式', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (123, 70, NULL, '过程管控', '分级加载', '不应少于3级,3级加载依次宜为单元内预压荷载值的60%、80%、100%', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (124, 70, NULL, '过程管控', '加载完成', '支架预压监测过程中,各监测点最初24h的沉降量平均值小于1mm,或各监测点最初72h的沉降量平均值小于5mm口是否应判定支架预压合格', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (125, 70, NULL, '过程管控', '卸载', '可一次性卸载,预压荷载应对称、均衡、同步', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (126, 70, NULL, '过程管控', '监测点布置', '沿混凝土结构纵向每隔1/4跨径应布置一个监测断面', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (127, 70, NULL, '过程管控', '监测点布置', '每个监测断面上的监测点不宜少于5个,并应对称布置', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (128, 70, NULL, '其他措施', '应急预案', '应急救援预案是否编制齐全,应急救援培训是否到位,应急处置措施是否齐全有效,施工作业人员应知应会是否培训口是否到位', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (129, 70, NULL, '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (130, 71, NULL, '作业人员', '证件', '架子工是否持有效证件上岗', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (131, 71, NULL, '方案和交底', '方案', '是否编制专项方案,超过一定规模的高支模作业是否组织专家论证 ', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (132, 71, NULL, '方案和交底', '交底', '技术交底和安全技术交底是否到位', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (133, 71, NULL, '起重设备', '相关证件', '确认起重设备和人员(操作手、信号司索工等)已报批,定期维保', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (134, 71, NULL, '起重设备', '安全装置', '设备运行正常,限载、限位装置等工作状态正常', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (135, 71, NULL, '吊索具', '起重钢丝绳', '起重采用钢丝绳,外观、合格证、质保资料、使用条件符合要求；\r\n接头插编长度和外观符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (136, 71, NULL, '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (137, 71, NULL, '过程管控', '高处作业', '临边设置安全防护、安全通道;人员劳保用品穿戴情况,必要时设母索', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (138, 71, NULL, '过程管控', '拆除顺序', '是否严格按照方案进行拆除作业', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (139, 71, NULL, '其他措施', '应急预案', '应急救援预案是否编制齐全,应急救援培训是否到位,应急处置措施是否齐全有效,施工作业人员应知应会是否培训口是否到位', '9d8ec26eb12a86bf7b2924849a23b233');
INSERT INTO `rail_safe_table_item` VALUES (140, 71, NULL, '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '9d8ec26eb12a86bf7b2924849a23b233');

SET FOREIGN_KEY_CHECKS = 1;



/*
 Navicat Premium Data Transfer

 Source Server         : 涉铁工程
 Source Server Type    : MySQL
 Source Server Version : 50644
 Source Host           : ************:15051
 Source Schema         : linkappdb

 Target Server Type    : MySQL
 Target Server Version : 50644
 File Encoding         : 65001

 Date: 27/06/2025 11:23:44
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for rail_safe_table
-- ----------------------------
DROP TABLE IF EXISTS `rail_safe_table`;
CREATE TABLE `rail_safe_table`  (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                    `grid_id` bigint(20) NULL DEFAULT NULL COMMENT '网格id',
                                    `keywork_parent_id` bigint(20) NULL DEFAULT NULL COMMENT '关联作业父类id',
                                    `keywork_id` bigint(20) NULL DEFAULT NULL COMMENT '关联作业id',
                                    `tenant_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                    `table_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '安全确认表头',
                                    `table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '安全确认表名',
                                    `table_status` int(11) NULL DEFAULT NULL COMMENT '表格状态：0协作中，1待签字，2已完成',
                                    `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                    `check_opinion` int(11) NULL DEFAULT NULL COMMENT '检查意见：1同意，-1不同意，0检查中',
                                    `pre_sign_time` datetime(0) NULL DEFAULT NULL COMMENT '提交待签字时间',
                                    `check_time` datetime(0) NULL DEFAULT NULL COMMENT '检查时间',
                                    `check_user_id` varchar(255) NULL DEFAULT NULL COMMENT '检查人id',
                                    `check_user_sign` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '检查人签名',
                                    `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                    `latest_modify_time` datetime(0) NULL DEFAULT NULL COMMENT '最近修改时间',
                                    `work_time` datetime(0) NULL DEFAULT NULL COMMENT '施工时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '安全确认表总表' ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;


    /*
 Navicat Premium Data Transfer

 Source Server         : 涉铁工程
 Source Server Type    : MySQL
 Source Server Version : 50644
 Source Host           : ************:15051
 Source Schema         : linkappdb

 Target Server Type    : MySQL
 Target Server Version : 50644
 File Encoding         : 65001

 Date: 27/06/2025 11:23:50
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for rail_safe_table_record
-- ----------------------------
DROP TABLE IF EXISTS `rail_safe_table_record`;
CREATE TABLE `rail_safe_table_record`  (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                           `table_id` bigint(20) NULL DEFAULT NULL COMMENT '所属总表id',
                                           `item_id` bigint(20) NULL DEFAULT NULL COMMENT '所填项目id',
                                           `check_result` int(11) NULL DEFAULT NULL COMMENT '检查结果：-1否 ，1是，0未检查',
                                           `check_user_id` bigint(20) NULL DEFAULT NULL COMMENT '检查人',
                                           `check_time` datetime(0) NULL DEFAULT NULL COMMENT '检查时间',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '安全确认表子表' ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;
