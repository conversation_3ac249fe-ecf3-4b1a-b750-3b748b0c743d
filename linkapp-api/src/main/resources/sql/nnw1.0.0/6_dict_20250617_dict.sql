INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('9', '系统告警监测项', 'testing_item', '系统告警监测项', NULL, NULL, NULL, NULL);
INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('10', '通知推送方式', 'push_type', '通知推送方式', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( '9', '违章行为', '1', '违章行为', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( '9', '司机行为', '2', '司机行为', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( '9', '风速', '3', '风速', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( '9', '电器火灾', '4', '电器火灾', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( '9', '断路器故障', '5', '断路器故障', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` ( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ( '9', '电表', '6', '电表', '11', '1', NULL, NULL, NULL, NULL, '0');

INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('124', '10', '站内', '1', '站内', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('125', '10', '短信', '2', '短信', '2', '1', NULL, NULL, NULL, NULL, '0');


INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99061', 9906, '用电安全', 't_use_electricity', '用电安全', 2, NULL, NULL, 99061, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);




SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for rail_give_alarm_type_config
-- ----------------------------
DROP TABLE IF EXISTS `rail_give_alarm_type_config`;
CREATE TABLE `rail_give_alarm_type_config`
(
    `id`                  varchar(32) NOT NULL COMMENT '记录id',
    `text`                varchar(100) DEFAULT NULL COMMENT '告警类型名称',
    `level`               int(11) DEFAULT NULL COMMENT '告警等级，1:一级，2:二级，3:三级',
    `thresh_value` double(20,2) DEFAULT NULL COMMENT '阈值',
    `enabled`             int(10) DEFAULT '1' COMMENT '0启用，1禁用',
    `push_meth`           varchar(20)  DEFAULT NULL COMMENT '1:站内,2:短信',
    `push_user_ids`       varchar(255) DEFAULT NULL COMMENT '短信接收人ids',
    `push_user_names`     varchar(255) DEFAULT NULL COMMENT '短信接收人姓名',
    `handle_user_ids`     varchar(255) DEFAULT NULL COMMENT '处理人id',
    `handle_user_names`   varchar(255) DEFAULT NULL COMMENT '处理人姓名',
    `push_user_phones`    varchar(255) DEFAULT NULL COMMENT '短信接收人接收手机号',
    `tenant_id`           varchar(32)  DEFAULT NULL COMMENT '租户id',
    `fieids`              varchar(500) DEFAULT NULL,
    `type`                int(11) DEFAULT NULL COMMENT '小类 参考枚举',
    `category`            varchar(100) DEFAULT NULL COMMENT '告警模块分类 1-吊车安全监测 2-防护员电子围栏 3-用电安全 4-汛情防护 5-AI行为告警',
    `deduplication_time_` int(11) DEFAULT NULL COMMENT '告警去重时间 单位min',
    `create_id_`          bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`        datetime     DEFAULT NULL COMMENT '创建日期',
    `modify_id_`          bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`        datetime     DEFAULT NULL COMMENT '修改时间',
    `remark_`             text COMMENT '备注',
    PRIMARY KEY (`id`)
) COMMENT='系统告警配置表';
CREATE TABLE `rail_give_system_alarm`
(
    `id`               bigint(11) NOT NULL AUTO_INCREMENT,
    `testing_item`     varchar(50)   DEFAULT NULL COMMENT '监测项,字典',
    `device_code`      varchar(128)  DEFAULT NULL COMMENT '设备唯一编码',
    `level`            varchar(20)   DEFAULT NULL COMMENT '告警等级，1:一级，2:二级，3:三级',
    `create_time`      datetime      DEFAULT NULL COMMENT '告警时间/创建时间',
    `alarm_type`       varchar(128)  DEFAULT NULL COMMENT '关联告警配置记录id',
    `content`          varchar(500)  DEFAULT NULL COMMENT '告警内容',
    `handle_id`        varchar(128)  DEFAULT NULL COMMENT '告警处理人id',
    `handle_name`      varchar(50)   DEFAULT NULL COMMENT '告警处理人姓名',
    `status`           int(11) DEFAULT NULL COMMENT '告警记录状态 0未处理，1已处理，2误报',
    `push_content`     varchar(500)  DEFAULT NULL COMMENT '告警推送内容',
    `modify_time`      datetime      DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `modifier`         varchar(255)  DEFAULT NULL COMMENT '更新人id',
    `tenant_id`        varchar(32)   DEFAULT NULL COMMENT '租户id',
    `handle_file_urls` varchar(1000) DEFAULT NULL COMMENT '处理结果附件',
    `handle_resp`      varchar(255)  DEFAULT NULL COMMENT '处理说明',
    `handle_rest`      int(11) DEFAULT NULL COMMENT '处理结果0已处理,1误报',
    `category`         varchar(100)  DEFAULT NULL COMMENT '告警大类 告警模块分类 1-吊车安全监测 2-防护员电子围栏 3-用电安全 4-汛情防护',
    `ref_id_`          varchar(64)   DEFAULT NULL COMMENT '关联id',
    PRIMARY KEY (`id`)
) COMMENT='系统告警表';




-- ----------------------------
-- Records of rail_give_alarm_type_config
-- ----------------------------
INSERT INTO linkappdb.rail_give_alarm_type_config
(id, `text`, `level`, thresh_value, enabled, push_meth, push_user_ids, push_user_names, handle_user_ids, handle_user_names, push_user_phones, tenant_id, fieids, `type`, category, deduplication_time_, create_id_, create_time_, modify_id_, modify_time_, remark_)
VALUES('315D3595-8958-72EF-CCBC-4905E817', 'A/B/C相告警', '1', 1.0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '5a920f536428050edddcce5212f36a97', 'a_phase_over_load,b_phase_over_load,c_phase_over_load,a_phase_loss_phase,b_phase_loss_phase,c_phase_loss_phase,a_phase_under_voltage,b_phase_under_voltage,c_phase_under_voltage,a_phase_over_voltage,b_phase_over_voltage,c_phase_over_voltage', 5, '3', NULL, 1, '2025-06-19 15:04:55', 1, '2025-06-19 15:05:20', NULL);
INSERT INTO linkappdb.rail_give_alarm_type_config
(id, `text`, `level`, thresh_value, enabled, push_meth, push_user_ids, push_user_names, handle_user_ids, handle_user_names, push_user_phones, tenant_id, fieids, `type`, category, deduplication_time_, create_id_, create_time_, modify_id_, modify_time_, remark_)
VALUES('336CCF54-40CC-46F7-8FCF-16A91CD3', '电压/电流逆向序告警', '1', 1.0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '5a920f536428050edddcce5212f36a97', 'voltage_reverse,current_reverse', 4, '3', NULL, 1, '2025-06-19 15:04:55', 1, '2025-06-19 15:05:20', NULL);
INSERT INTO linkappdb.rail_give_alarm_type_config
(id, `text`, `level`, thresh_value, enabled, push_meth, push_user_ids, push_user_names, handle_user_ids, handle_user_names, push_user_phones, tenant_id, fieids, `type`, category, deduplication_time_, create_id_, create_time_, modify_id_, modify_time_, remark_)
VALUES('64A8713C-0C47-F40E-3BDE-BCC47116', '供电中断告警', '1', 1.0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '5a920f536428050edddcce5212f36a97', 'power_interrupt', 3, '3', NULL, 1, '2025-06-19 15:04:55', 1, '2025-06-19 15:05:20', NULL);
INSERT INTO linkappdb.rail_give_alarm_type_config
(id, `text`, `level`, thresh_value, enabled, push_meth, push_user_ids, push_user_names, handle_user_ids, handle_user_names, push_user_phones, tenant_id, fieids, `type`, category, deduplication_time_, create_id_, create_time_, modify_id_, modify_time_, remark_)
VALUES('A1AF9CE9-784E-0D31-DCAF-DF0F5EDC', '断路器故障', '1', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '5a920f536428050edddcce5212f36a97', NULL, 6, '3', NULL, 1, '2025-06-19 15:04:55', 1, '2025-06-19 15:05:20', NULL);
INSERT INTO linkappdb.rail_give_alarm_type_config
(id, `text`, `level`, thresh_value, enabled, push_meth, push_user_ids, push_user_names, handle_user_ids, handle_user_names, push_user_phones, tenant_id, fieids, `type`, category, deduplication_time_, create_id_, create_time_, modify_id_, modify_time_, remark_)
VALUES('B7C5EFEF-DE37-12C9-2A9E-C7865FF4', '温度过高告警', '1', 75.0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '5a920f536428050edddcce5212f36a97', 'temperature_1,temperature_2,temperature_3,temperature_4', 2, '3', NULL, 1, '2025-06-19 15:04:55', 1, '2025-06-19 15:05:20', NULL);
INSERT INTO linkappdb.rail_give_alarm_type_config
(id, `text`, `level`, thresh_value, enabled, push_meth, push_user_ids, push_user_names, handle_user_ids, handle_user_names, push_user_phones, tenant_id, fieids, `type`, category, deduplication_time_, create_id_, create_time_, modify_id_, modify_time_, remark_)
VALUES('D78CCF64-3B63-F7F0-E64F-0A4A7B3C', '剩余电流过高告警', '1', 1000.0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '5a920f536428050edddcce5212f36a97', 'residual_current', 1, '3', NULL, 1, '2025-06-19 15:04:55', 1, '2025-06-19 15:05:20', NULL);
