-- 劳务队伍表

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `labor_team`;
CREATE TABLE `labor_team`  (
  `id_` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `labor_team_name_` VARCHAR(50) NOT NULL COMMENT '劳务队伍名称',
  `credit_code_` VARCHAR(18) NOT NULL COMMENT '统一社会信用代码',
  `leader_name_` VARCHAR(10) DEFAULT NULL COMMENT '负责人姓名',
  `leader_phone_` VARCHAR(20) DEFAULT NULL COMMENT '负责人手机号',
  `remark_` VARCHAR(100) DEFAULT NULL COMMENT '备注',
  `contract_files_` TEXT NOT NULL COMMENT '合同附件（JSON数组/逗号分隔路径）',
  `tenant_id_` VARCHAR(64) NOT NULL COMMENT '租户ID',
  `creator_id_` BIGINT(20) DEFAULT NULL COMMENT '创建人id',
  `create_time_` DATETIME DEFAULT NULL COMMENT '创建日期',
  `modify_id_` BIGINT(20) DEFAULT NULL COMMENT '修改人id',
  `modify_time_` DATETIME DEFAULT NULL COMMENT '修改时间',
  `is_deleted_` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id_`),
  UNIQUE KEY `uk_tenant_labor_team_name_` (`tenant_id_`, `labor_team_name_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='劳务队伍表';

SET FOREIGN_KEY_CHECKS = 1; 

ALTER TABLE linkappdb.linkapp_privilege MODIFY COLUMN seq_ decimal(9,3) NULL;
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99032', 9903, '劳务队伍', 't_labor_team', '劳务队伍', 2, 'labor_team', 'labor_team', 99032, 1, 'manage/system/laborTeam', 1, '1', NULL, '1', '1', '2023-05-09 09:09:09', NULL, 0);
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99032001', 99032, '新增', 't_labor_team_add', '劳务队伍-新增', 3, NULL, NULL, 99032.001, 2, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99032002', 99032, '编辑', 't_labor_team_edit', '劳务队伍-编辑', 3, NULL, NULL, 99032.002, 2, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99032003', 99032, '删除', 't_labor_team_delete', '劳务队伍-删除', 3, NULL, NULL, 99032.003, 2, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

