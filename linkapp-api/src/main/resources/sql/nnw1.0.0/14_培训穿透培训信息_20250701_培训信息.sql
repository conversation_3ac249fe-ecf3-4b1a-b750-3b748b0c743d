CREATE TABLE `rail_linkapp_safety_edu_plan` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL COMMENT '培训名称',
  `edu_date` varchar(50) DEFAULT NULL COMMENT '培训日期',
  `training_type` varchar(100) DEFAULT NULL COMMENT '培训类型 字典 training_type',
  `training_site` varchar(255) DEFAULT NULL COMMENT '培训场地',
  `content` varchar(1000) DEFAULT NULL COMMENT '培训内容',
  `curator_id` varchar(255) DEFAULT NULL COMMENT '培训负责人id 花名册id',
  `curator_name` varchar(255) DEFAULT NULL COMMENT '负责人姓名 花名册姓名',
  `notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `imgs` varchar(1000) DEFAULT NULL COMMENT '图片地址',
  `files` varchar(1000) DEFAULT NULL COMMENT '附件',
  `pass_number` int(11) DEFAULT NULL,
  `grid_id` varchar(128) DEFAULT NULL COMMENT '培训网格id',
  `grid_name` varchar(255) DEFAULT NULL,
  `complete_number` int(11) DEFAULT NULL COMMENT '参与人数',
  `pass_through_number` int(11) DEFAULT NULL COMMENT '通过认识',
  `modify_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `creator` varchar(255) DEFAULT NULL,
  `modifier` varchar(255) DEFAULT NULL,
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='培训穿透/培训信息表';

CREATE TABLE `rail_linkapp_safety_edu_detail` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `person_id` varchar(255) DEFAULT NULL COMMENT '花名册人员id',
  `kh_number` int(11) DEFAULT NULL COMMENT '考核分数',
  `is_qual` int(5) DEFAULT NULL COMMENT '是否合格 0合格，1不合格',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '提交时间/更新时间',
  `edu_id` varchar(255) DEFAULT NULL COMMENT '培训记录id',
  `tenant_id` varchar(32) DEFAULT NULL,
  `person_name` varchar(150) DEFAULT NULL COMMENT '花名册人员姓名',
  `person_phone` varchar(255) DEFAULT NULL COMMENT '选中人员手机号',
  `person_status` int(10) DEFAULT NULL COMMENT '0离岗，1在岗',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人员培训考核记录表';




INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('14', '培训信息培训类型', 'training_type', '培训信息培训类型', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('187', '14', '公司级安全教育培训', '1', '公司级安全教育培训', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('188', '14', '项目级安全教育培训', '2', '项目级安全教育培训', '2', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('189', '14', '班组级安全教育培训', '3', '班组级安全教育培训', '3', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('190', '14', '特种作业人员安全教育培训', '4', '特种作业人员安全教育培训', '4', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('191', '14', '班前安全教育', '5', '班前安全教育', '5', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('192', '14', '转岗安全教育培训', '6', '转岗安全教育培训', '6', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('193', '14', '体验式安全教育培训', '7', '体验式安全教育培训', '7', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('194', '14', '经常性安全教育培训', '8', '经常性安全教育培训', '8', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('195', '14', '特殊时段安全教育培训', '9', '特殊时段安全教育培训', '9', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('196', '14', '“四新“安全教育培训', '10', '“四新“安全教育培训', '10', '1', NULL, NULL, NULL, NULL, '0');



INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('9918', NULL, '培训穿透', 't_safety_edu', '培训穿透', '1', NULL, NULL, '9918.000', '1', NULL, NULL, '1', NULL, NULL, NULL, '2025-06-26 17:36:39', NULL, '0');
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('99181', 9918, '安全教育管理', 't_safety_edu_plan', '安全教育管理', '2', NULL, NULL, '99181.000', '1', 'training/safetyEducationManagement', NULL, '1', NULL, NULL, NULL, '2025-06-26 17:36:39', NULL, '0');
INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('99182', 9918, '培训人员统计', 't_safety_edu_stat', '培训人员统计', '2', NULL, NULL, '99182.000', '1', 'training/trainingPersonnelStatistics', NULL, '1', NULL, NULL, NULL, '2025-06-26 17:36:39', NULL, '0');
