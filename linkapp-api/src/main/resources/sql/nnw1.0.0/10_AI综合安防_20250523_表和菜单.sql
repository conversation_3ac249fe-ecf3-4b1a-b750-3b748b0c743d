CREATE TABLE `rail_linkapp_ai_host_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备唯一标识',
  `ai_rule_type` varchar(50) DEFAULT NULL COMMENT '识别规则类型 参考字典ai_rule_type',
  `image` varchar(500) DEFAULT NULL COMMENT '图片地址',
  `ipc_sn` varchar(500) DEFAULT NULL COMMENT '摄像头SN码',
  `ipc_address` varchar(500) DEFAULT NULL COMMENT '摄像头地址',
  `channel_status` varchar(20) DEFAULT NULL,
  `ai_event_type` varchar(50) DEFAULT NULL COMMENT '事件类型 参考字典ai_event_type',
  `has_result` varchar(20) DEFAULT NULL COMMENT ' 有无对比结果 :0 - 无对比结果1 - 有对比结果',
  `behaviour_json` varchar(1000) DEFAULT NULL COMMENT '行为分析数据',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='AI综合安防行为记录表';
-- 字典
INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('12', 'AI识别规则类型', 'ai_rule_type', 'AI识别规则类型', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict` (`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('13', 'AI识别事件类型', 'ai_event_type', 'AI识别事件类型', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('142', '12', '未戴安全帽', '00', '未戴安全帽', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('143', '12', ' 未穿工服', '01', '未穿工服', '2', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('144', '12', '吸烟', '02', '吸烟', '3', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('145', '12', '区域入侵', '03', '区域入侵', '4', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('146', '12', '跌倒', '04', '跌倒', '5', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('147', '12', '未穿反光服', '05', '未穿反光服', '6', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('148', '12', '明火', '06', '明火', '7', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('149', '12', '烟雾', '07', '烟雾', '8', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('150', '12', '打架', '08', '打架', '9', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('151', '12', '未知', '99', '未知', '10', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('160', '12', '举手求救', '09', '举手求救', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('161', '12', '未穿安全背带', '10', '未穿安全背带', '12', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('162', '12', '未系安全绳', '11', '未系安全绳', '13', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('152', '13', '行为分析告警数据', '1', '行为分析告警数据', '1', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('153', '13', '告警恢复数据', '2', '告警恢复数据', '2', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('154', '13', '人脸抓拍数据', '3', '人脸抓拍数据', '3', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('155', '13', '人脸比对数据', '4', '人脸比对数据', '4', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('156', '13', '行为比对数据', '5', '行为比对数据', '5', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('157', '13', '保活数据', '6', '保活数据', '6', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('158', '13', '点位变更数据', '7', '点位变更数据', '7', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('159', '13', '交通参数', '8', '交通参数', '8', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('160', '12', '举手求救', '09', '举手求救', '11', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('161', '12', '未穿安全背带', '10', '未穿安全背带', '12', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('162', '12', '未系安全绳', '11', '未系安全绳', '13', '1', NULL, NULL, NULL, NULL, '0');
