-- 花名册
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99031', 9903, '花名册', 't_person', '花名册', 2, NULL, NULL, 99031, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);



SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for rail_linkapp_roster_personnel
-- ----------------------------
DROP TABLE IF EXISTS `rail_linkapp_roster_personnel`;
CREATE TABLE `rail_linkapp_roster_personnel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `roster_type` varchar(20) DEFAULT NULL COMMENT '0:管理人员,1:劳务人员,2:防护员,3:特种作业人员',
  `real_name` varchar(128) DEFAULT NULL COMMENT '人员姓名',
  `belonging_units` varchar(128) DEFAULT NULL COMMENT '人员所属单位，多个单位id用逗号隔开',
  `ids_no` varchar(128) DEFAULT NULL COMMENT '身份证号码',
  `birthday_date` varchar(25) DEFAULT NULL COMMENT '出生日期,根据身份证号码获取',
  `sex` varchar(20) DEFAULT NULL COMMENT '性别,根据身份证号码获取',
  `education_background` varchar(50) DEFAULT NULL COMMENT '学历',
  `profile_pict` varchar(255) DEFAULT NULL COMMENT '人员头像图片',
  `gate_permis` varchar(255) DEFAULT NULL COMMENT '闸机权限，数据源来自闸机管理，多个闸机id逗号拼接',
  `safety_edu_res` int(1) DEFAULT NULL COMMENT '安全教育结果，0合格,1不合格',
  `roster_post` varchar(255) DEFAULT NULL COMMENT '岗位，来自数据源来自字典',
  `start_time` datetime DEFAULT NULL COMMENT '入场时间',
  `end_time` datetime DEFAULT NULL COMMENT '退场时间，如果如果用户填写了入场时间，退场时间应晚与入场时间',
  `auth_book` varchar(255) DEFAULT NULL COMMENT '授权书图片',
  `auth_book_type` varchar(50) DEFAULT NULL COMMENT '证书类型，字典维护',
  `auth_book_content` varchar(255) DEFAULT NULL COMMENT '证书内容页，图片',
  `auth_book_start_date` varchar(25) DEFAULT NULL COMMENT '证书有效开始日期',
  `auth_book_end_date` varchar(25) DEFAULT NULL COMMENT '证书有效结束日期',
  `create_time` datetime DEFAULT NULL COMMENT '人员信息创建时间',
  `creator` varchar(255) DEFAULT NULL COMMENT '人员信息创建人id',
  `modify_time` datetime DEFAULT NULL COMMENT '人员信息更新时间',
  `link_phone` varchar(100) DEFAULT NULL COMMENT '手机号码',
  `labor_team` varchar(255) DEFAULT NULL COMMENT '劳务队伍id,来源劳务队伍管理',
  `work_category` varchar(50) DEFAULT NULL COMMENT '工类,一级联动选项,来源字典',
  `work_type` varchar(50) DEFAULT NULL COMMENT '工种,二级联动选项,来源字典',
  `protect_type` varchar(100) DEFAULT NULL COMMENT '防护人员类型',
  `protect_lssuing_auth` varchar(255) DEFAULT NULL COMMENT '防护人员证书签发机构',
  `protect_auth_book_date` varchar(50) DEFAULT NULL COMMENT '防护人员证书有效时间区间',
  `protect_auth_book_content` varchar(255) DEFAULT NULL COMMENT '防护人员证书内容,最多5张图片',
  `protect_health_status` varchar(100) DEFAULT NULL COMMENT '防护人员健康状态',
  `protect_health_auth_content` varchar(255) DEFAULT NULL COMMENT '防护人员健康证书图片',
  `special_auth_book_type` varchar(255) DEFAULT NULL COMMENT '特种作业证书类型，字典维护',
  `special_auth_book_date` varchar(512) DEFAULT NULL COMMENT '特种作业证书有效时间区间',
  `special_auth_book_content` varchar(1000) DEFAULT NULL COMMENT '特种作业证书证书内容页',
  `appoint_book_content` varchar(512) DEFAULT NULL COMMENT '任命书',
  `social_book_content` varchar(255) DEFAULT NULL COMMENT '社保证明',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `modifier` varchar(128) DEFAULT NULL COMMENT '更新人',
  `work_status` int(11) DEFAULT NULL COMMENT '0:退场，1在岗位',
  `auth_book_json` varchar(1500) DEFAULT NULL,
  `special_auth_book_json` varchar(1500) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='安全管理-人员管理人员花名册';


SET FOREIGN_KEY_CHECKS = 1;