# 所属片区
ALTER TABLE linkappdb.linkapp_tenant ADD COLUMN `belong_area` VARCHAR(255) DEFAULT NULL COMMENT '所属片区';


# 个性化权限配置
CREATE TABLE linkapp_privilege_custom (
  id_ BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  privilege_id_ BIGINT COMMENT '权限id',
  name_ VARCHAR(255) COMMENT '权限名',
  tenant_id_ VARCHAR(255) COMMENT '租户ID',
  seq_ DOUBLE COMMENT '排序的序号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限个性化实体表';

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('19901', NULL, '工作台', '', '菜单分隔符,工作台', 1, NULL, NULL, 19901.1, 0, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('19902', NULL, '安全管理模块', '', '菜单分隔符,安全管理模块', 1, NULL, NULL, 19901.2, 0, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('19903', NULL, '线上协同模块', '', '菜单分隔符,线上协同模块', 1, NULL, NULL, 19901.3, 0, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('19904', NULL, '项目管理', '', '菜单分隔符,项目管理', 1, NULL, NULL, 19901.4, 0, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('19905', NULL, '数据分析', '', '菜单分隔符,数据分析', 1, NULL, NULL, 19901.5, 0, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- 添加停工时的剩余竣工天数字段
ALTER TABLE linkapp_tenant
    ADD COLUMN surplus_day_upon_suspension BIGINT NULL COMMENT '停工时的剩余竣工天数';

-- 添加停工时的项目进度字段
ALTER TABLE linkapp_tenant
    ADD COLUMN progress_upon_suspension VARCHAR(255) NULL COMMENT '停工时的项目进度，保留一位小数';

-- 添加停工时的状态切换时间字段
ALTER TABLE linkapp_tenant
    ADD COLUMN status_change_time_upon_suspension DATETIME NULL COMMENT '停工时的状态切换时间';
