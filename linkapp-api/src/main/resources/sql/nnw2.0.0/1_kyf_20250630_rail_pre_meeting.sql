-- 班前会管理系统数据库设计
-- 作者: kyf
-- 日期: 2025-06-30
-- 描述: 班前会管理系统相关表结构

-- 1. 班前会主表
CREATE TABLE `rail_pre_meeting` (
    `id_` bigint(20) AUTO_INCREMENT COMMENT 'ID主键',
    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
    `grid_id_` bigint(20) DEFAULT NULL COMMENT '网格点ID(关联rail_linkapp_grid_management_info.id)',
    `meeting_time_` datetime DEFAULT NULL COMMENT '班前会时间',
    `content_` varchar(50) DEFAULT NULL COMMENT '班前会内容/主题',
    `team_name_` varchar(100) DEFAULT NULL COMMENT '班组名称',
    `people_count_` int(10) DEFAULT NULL COMMENT '参会人数',
    `supervisor_photo_url_` varchar(1000) DEFAULT NULL COMMENT '监理现场照片URL',
    `leader_photo_url_` varchar(1000) DEFAULT NULL COMMENT '带班人员现场照片URL',
    `safety_officer_photo_url_` varchar(1000) DEFAULT NULL COMMENT '网格安全员现场照片URL',
    `technical_leader_photo_url_` varchar(1000) DEFAULT NULL COMMENT '技术负责人现场照片URL',
    `guard_photo_url_` varchar(1000) DEFAULT NULL COMMENT '防护员现场照片URL',
    `media_files_url_` text COMMENT '图片/影像文件URL列表(JSON格式)',
    `is_deleted_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='班前会表';
