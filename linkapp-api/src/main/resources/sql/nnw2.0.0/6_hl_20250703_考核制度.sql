

-- ----------------------------
-- Table structure for rail_assess_table_item
-- ----------------------------
DROP TABLE IF EXISTS `rail_assess_table_item`;
CREATE TABLE `rail_assess_table_item`  (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                           `tenant_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目id',
                                           `table_type` int(11) NULL DEFAULT NULL COMMENT '表格类型：1网格员2安检专务3施工队伍',
                                           `table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '考核表名',
                                           `index_no` int(11) NULL DEFAULT NULL COMMENT '序号，用于条目排序',
                                           `assess_target` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '考核指标',
                                           `assess_entry` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '考核条目',
                                           `assess_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '考核内容',
                                           `score_standard` int(11) NULL DEFAULT NULL COMMENT '配分标准',
                                           `score_deduct` int(11) NULL DEFAULT NULL COMMENT '扣分分值',
                                           `score_add` int(11) NULL DEFAULT NULL COMMENT '得分分值',
                                           `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                           `calculate_rules` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计分规则',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '考核表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of rail_assess_table_item
-- ----------------------------
INSERT INTO `rail_assess_table_item` VALUES (1, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 1, '核心指标', '事故（事件）', '发生事故(事件),直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (2, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 2, '核心指标', '不良行为', '涉及安全一般、较大、严重不良行为,直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (3, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 3, '核心指标', '评价扣分行为', '被上级单位单次评价扣5分及以上的,直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (4, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 4, '量化指标', '评价扣分及重大事故隐患', '被上级单位单次评价扣1分的,每次扣20分;被项目建设管理单位下发重大园患整改通知书、单次评价扣2分的,每项扣30分', 30, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：3');
INSERT INTO `rail_assess_table_item` VALUES (5, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 5, '量化指标', '考勤打卡', '出勤打卡率、在岗时间,每次扣2分;脱岗每次扣10分', 10, NULL, NULL, NULL, '判断打卡方式：第一种：通过上传的文件记录中有该人员填写或签字的记录来判断：班前会、晚交班会、过程盯控、网格安全员日志，通过以上功能中有该人员填写或签字的记录，则判断该人员当天有打卡；第二种:网格安全员当天有轨迹记录，则算当天打卡第二种；两种有一种即为打卡；扣分方式：一天未打卡，扣2分，最多扣10分；最多扣10分，得分=10-扣分');
INSERT INTO `rail_assess_table_item` VALUES (6, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 6, '量化指标', '智能安全帽/执法记录仪', '是否正确佩戴,每次预警扣1分;佩戴时长是否满足要求,扣2分:是否他人替戴,每次扣10分', 10, NULL, NULL, NULL, '判断方式：1、若该人员绑定了有智能安全帽或手持终端，则判断该人员是否每天有轨迹记录，一天没有扣2分；最多扣10分2、人员未绑定以上两种设备，则系统无法判断：扣分为0，得分：10');
INSERT INTO `rail_assess_table_item` VALUES (7, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 7, '量化指标', '工装配备', '是否配备并使用对讲机,每次扣1分;安全防护服是否按照制定颜色区分穿献,每次扣1分:工具包等必要的安全装备是否配备携带,每次扣1分', 20, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：20');
INSERT INTO `rail_assess_table_item` VALUES (8, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 8, '量化指标', '班前讲话', '是否参与每日网格内班前讲话、每缺1次扣1分:班前讲话记录的是否完整,每次扣5分', 5, NULL, NULL, NULL, '当月该网格安全员负责的网格是否每日都有班前会记录，缺一次扣1分；最多扣5分；\r\n系统若无法判断该网格安全员所属网格的时候，无法判断：扣分为0，得分：5');
INSERT INTO `rail_assess_table_item` VALUES (9, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 9, '量化指标', '日常巡查及整改', '未开展隐患排查整治,每次扣5分:自查发现问题未监督隐患整改到位,每次扣5分;检查整改记录是否完整,每缺1次扣5分:风险是否得到及时控制和消除,扣5分:上级检查发现安全问题,每件扣2分:针对上级检查问题未及时盯控整改到位的,每次扣 20分', 5, NULL, NULL, NULL, '1、没有该网格安全员提交的安全隐患排查记录，直接扣5分；2、改网格安全员提的隐患整改有超期状态为整改中、待复查的隐患，一个扣5分，最多扣5分；得分=5-扣分');
INSERT INTO `rail_assess_table_item` VALUES (10, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 10, '量化指标', '跟班作业', '是否跟随劳务队伍班组进行作业,每次扣1分,查跟班作业记录填写是否完整、每次扣2分;对施工过程是否全面监控,每次扣2分', 10, NULL, NULL, NULL, '当月网格员安全日志或过程盯控是否每日都有记录，缺失一次扣2分，最多扣10分；得分=10-扣分');
INSERT INTO `rail_assess_table_item` VALUES (11, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 11, '量化指标', '关键施工作业', '是否参与作业前安全条件是否确认,每次扣5分:盯控作业中的安全措施是否到位,每次扣5分;作业记录是否填写完善,每次扣5分', 10, NULL, NULL, NULL, '当月有安全条件表的日期，看对应的日期是否有过程盯控记录，没有则一次扣5分，最多扣10分；得分=10-扣分');
INSERT INTO `rail_assess_table_item` VALUES (12, '9d8ec26eb12a86bf7b2924849a23b233', 1, '网格安全员日常考核表', 12, '加分项', '管理创新与突出贡献', '提出有效安全管理建议被采纳。获上级单位安全表彰，建议采纳+5分；表彰+5分/次。', 10, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (16, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 1, '核心指标', '事故（事件）', '责任网格发生生产安全责任事故（或者险性事故、重大影响），直接评定为不合格。', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (17, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 2, '核心指标', '重大监管失职', '被上级单位通报重大履职缺失或瞒报重大隐患，直接评定为不合格。', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (18, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 3, '量化指标', '网格监督职责', '未牵头制定网格监督计划（明确对象、监督内容、标准、监督方式等）。缺失扣5分；内容不全扣2分/项；未签字或未发布扣2分。', 5, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：5');
INSERT INTO `rail_assess_table_item` VALUES (19, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 4, '量化指标', '安全条件确认监督', '1.未督促开展安全条件确认。缺少1次扣10分。安全条件确认走形式，扣10分。2.未检查安全条件确认落实情况，缺1次监督扣5分；未核查确认表扣2分/次。', 15, NULL, NULL, NULL, '若当月有填写的安全条件确认表，签字人是该安监专务且未签字的，一次扣两份；最多扣15分；得分=15-扣分');
INSERT INTO `rail_assess_table_item` VALUES (20, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 5, '量化指标', '隐患整改闭环', '未跟踪整改闭环。限期1项未闭环扣5分；未确认整改或整改不符合要求1次扣5分。', 10, NULL, NULL, NULL, '当月有隐患超时未整改、未复查的隐患，一个扣5分，最多扣10分；得分=10-扣分');
INSERT INTO `rail_assess_table_item` VALUES (21, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 6, '量化指标', '网格安全员履职监督', '未监督网格安全员在岗、五步走、关键作业盯控、工作清单填写等履职情况。发现1次履职缺失未纠正扣5分。', 10, NULL, NULL, NULL, '该安监专务负责的网格下的网格安全员未填写网格安全日志，一日未填写扣5分；最多扣10分；得分=10-扣分');
INSERT INTO `rail_assess_table_item` VALUES (22, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 7, '量化指标', '部门监督计划执行', '1.未牵头制定网格监督计划（明确对象、内容、标准等），缺失扣5分；内容不全扣2分/项；未签字或未发布扣2分。2.对部门未开展监督检查。缺1次扣2分；无记录扣2分。', 5, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：5');
INSERT INTO `rail_assess_table_item` VALUES (23, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 8, '量化指标', '部门隐患处置', '未落实部门隐患处置措施（口头教育/隐患告知书）。缺失扣5分；未覆盖五部一室扣1分/部门。', 5, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：5');
INSERT INTO `rail_assess_table_item` VALUES (24, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 9, '量化指标', '班前讲话监督', '当日班前讲话未开展或未审核（影像资料、签字记录）。缺1次班前讲话扣5分；缺1次审核扣2分；记录不全扣1分/次。', 10, NULL, NULL, NULL, '当月班前会是否每日都有记录，缺一次扣5分');
INSERT INTO `rail_assess_table_item` VALUES (25, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 10, '量化指标', '关键节点验收监督', '1.未督促技术负责人开展关键节点验收。缺少1次扣10分。2.未检查关键节点安全条件验收落实情况。缺1次监督扣5分；未核查验收表扣2分/次。', 15, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：15');
INSERT INTO `rail_assess_table_item` VALUES (26, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 11, '量化指标', '重大隐患清零', '未监督重大隐患整改及复工验收，未跟踪整改扣10分/项；未报告总监扣5分/项。', 15, NULL, NULL, NULL, '当月有隐患类型为重大隐患的隐患排查的状态为整改中、待复查的隐患，一个扣10分，最多扣10分；得分=10-扣分');
INSERT INTO `rail_assess_table_item` VALUES (27, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 12, '量化指标', '工作台账与报告', '未按填写监督记录、形成台账或未每月底上报安全监督情况。台账缺失扣5分；未按时报告扣5分；内容不实扣2分/项。', 10, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：10');
INSERT INTO `rail_assess_table_item` VALUES (28, '9d8ec26eb12a86bf7b2924849a23b233', 2, '安监专务考核表', 13, '加分项', '管理创新与突出贡献', '提出有效安全管理建议被采纳。获上级单位安全表彰，建议采纳+5分；表彰+5分/次。', 10, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (29, '9d8ec26eb12a86bf7b2924849a23b233', 3, '施工队伍考核表', 1, '核心指标', '事故（事件）', '发生事故(事件),直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (30, '9d8ec26eb12a86bf7b2924849a23b233', 3, '施工队伍考核表', 2, '核心指标', '不良行为', '涉及安全一般、较大、严重不良行为,直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (31, '9d8ec26eb12a86bf7b2924849a23b233', 3, '施工队伍考核表', 3, '核心指标', '重大监管失职', '被上级单位单次评价扣5分及以上的,直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');
INSERT INTO `rail_assess_table_item` VALUES (32, '9d8ec26eb12a86bf7b2924849a23b233', 3, '施工队伍考核表', 4, '量化指标', '评价扣分及重大事故隐患', '被上级单位单次评价扣1分的,每次扣20分;被项目建设管理单位下发重大园患整改通知书、单次评价扣2分的,每项扣30分', 30, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：30');
INSERT INTO `rail_assess_table_item` VALUES (33, '9d8ec26eb12a86bf7b2924849a23b233', 3, '施工队伍考核表', 5, '量化指标', '人员履约', '合同中网格安全员未到位,扣10分;网格安全员不履职,每次扣5分', 20, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：30');
INSERT INTO `rail_assess_table_item` VALUES (34, '9d8ec26eb12a86bf7b2924849a23b233', 3, '施工队伍考核表', 6, '量化指标', '安全教育', '劳务队伍是否定期开展内部安全教育,扣10分;是否积极参与项目组织的安全教育培训，每次扣5分', 15, NULL, NULL, NULL, '1当月该班组是否有类型为“班组级安全教育培训/特种作业培训”，没有扣10分，有一个即不扣分；2当月该班组是否有类型为-项目级安全教育培训，没有则扣5分；得分=15-扣分');
INSERT INTO `rail_assess_table_item` VALUES (35, '9d8ec26eb12a86bf7b2924849a23b233', 3, '施工队伍考核表', 7, '量化指标', '班前讲话', '是否执行每日班前讲话制度,每次扣2分;讲话内容是否包括安全注意事项,每次扣2分;是否明确当日作业存在的危险源,每次扣2分;讲话记录是否留存,每次扣2分', 10, NULL, NULL, NULL, '当月该班组是否每日都有班前会记录，缺一次扣2分，最多扣10分；得分=10-扣分');
INSERT INTO `rail_assess_table_item` VALUES (36, '9d8ec26eb12a86bf7b2924849a23b233', 3, '施工队伍考核表', 8, '量化指标', '隐患整改', '各级检查下发的隐患数量比例,每条扣2分;隐患是否及时进行整改,每次扣5分;是否存在拒不整改、屡改屡犯行为,每次扣 25 分', 25, NULL, NULL, NULL, '当月责任班组是该班组且有隐患整改超期为整改中、待复查的隐患，一个扣5分，最多扣25分；得分=25-扣分。');
INSERT INTO `rail_assess_table_item` VALUES (37, '9d8ec26eb12a86bf7b2924849a23b233', 3, '施工队伍考核表', 9, '加分项', '管理创新与突出贡献', '提出有效安全管理建议被采纳。获上级单位安全表彰，建议采纳+5分；表彰+5分/次。', 10, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0');

SET FOREIGN_KEY_CHECKS = 1;



-- ----------------------------
-- Table structure for rail_assess_rule_file
-- ----------------------------
CREATE TABLE `rail_assess_rule_file` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                         `tenant_id` varchar(255) DEFAULT NULL COMMENT '项目id',
                                         `file_type` int(11) DEFAULT NULL COMMENT '文件类型：0考核制度，1负面清单',
                                         `file_url` varchar(255) DEFAULT NULL COMMENT '文件路径',
                                         `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
                                         `is_used` int(11) DEFAULT NULL COMMENT '是否使用：0否1是',
                                         `create_uid` bigint(20) DEFAULT NULL COMMENT '上传人',
                                         `create_time` datetime DEFAULT NULL COMMENT '上传时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;
