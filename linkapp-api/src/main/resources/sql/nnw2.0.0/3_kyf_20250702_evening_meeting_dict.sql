-- 晚交班会相关字典数据

-- 1. 晚交班会状态字典
INSERT INTO `linkappdb`.`sys_dict`
(`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`)
VALUES ('15', '晚交班会状态', 'evening_meeting_status', '晚交班会状态', NULL, NULL, NULL, NULL);

-- 晚交班会状态字典项
INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`)
VALUES ('15', '协作中', '0', '协作中', '1', '1', NULL, NULL, NULL, NULL, '0');

INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`)
VALUES ('15', '签字中', '1', '签字中', '2', '1', NULL, NULL, NULL, NULL, '0');

INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`)
VALUES ('15', '已完成', '2', '已完成', '3', '1', NULL, NULL, NULL, NULL, '0');

-- 2. 晚交班会用户类型字典
INSERT INTO `linkappdb`.`sys_dict`
(`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`)
VALUES ('16', '晚交班会用户类型', 'evening_meeting_user_type', '晚交班会用户类型', NULL, NULL, NULL, NULL);

-- 晚交班会用户类型字典项
INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`)
VALUES ('16', '创建者', '0', '创建者', '1', '1', NULL, NULL, NULL, NULL, '0');

INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`)
VALUES ('16', '协作者', '1', '协作者', '2', '1', NULL, NULL, NULL, NULL, '0');

INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`)
VALUES ('16', '抄送人', '2', '抄送人', '3', '1', NULL, NULL, NULL, NULL, '0');

-- 3. 晚交班会签字状态字典
INSERT INTO `linkappdb`.`sys_dict`
(`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`)
VALUES ('17', '晚交班会签字状态', 'evening_meeting_sign_status', '晚交班会签字状态', NULL, NULL, NULL, NULL);

-- 晚交班会签字状态字典项
INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`)
VALUES ('17', '未签字', '0', '未签字', '1', '1', NULL, NULL, NULL, NULL, '0');

INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`)
VALUES ('17', '已签字', '1', '已签字', '2', '1', NULL, NULL, NULL, NULL, '0');
