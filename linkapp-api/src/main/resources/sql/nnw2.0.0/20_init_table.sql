-- 类型
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ('路基工程', 1, 0, 'ebefaf4df57d3d583b8f3f0e85099794', 0, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '桥梁工程', 1, 0, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道工程', 1, 0, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '站后及“四电”工程', 1, 0, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '营业线及邻近营业线', 1, 0, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '其他', 1, 0, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '深路堑开挖作业', 2, 1, 'ebefaf4df57d3d583b8f3f0e85099794', 0, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '高陡边坡作业', 2, 1, 'ebefaf4df57d3d583b8f3f0e85099794', 0, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '抗滑桩基础开挖', 2, 1, 'ebefaf4df57d3d583b8f3f0e85099794', 0, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '抗板墙基础开挖', 2, 1, 'ebefaf4df57d3d583b8f3f0e85099794', 0, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '高墩身拆模', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '水中墩混凝土浇筑', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '水中墩拆模', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '挂篮安装', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '挂篮拆除', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '挂篮行走', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '悬臂混凝土浇筑', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '栈桥搭设作业', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '栈桥拆除作业', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '钢围堰就位', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 0, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '架桥机过孔', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '架桥机安装', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '架桥机拆除', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '移动模架现浇施工', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '移动模架过孔施工', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '液压爬模安装', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '液压爬模现浇', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '液压爬模爬升', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '高墩身混凝土浇筑', 2, 2, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道初期支护与开挖作业', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '进洞出洞施工作业', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道衬砌拆换', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ('隧道瓦斯段落施工作业', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)组装、拆解作业', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '拆解作业', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)始发作业', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)接收作业', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道盾构带压开仓作业', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道盾构常压开仓作业', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)空推平移', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)特殊地段施工', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)联络通道施工', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)管片拆除', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)长时间停机', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)盾尾刷更换', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)水平运输', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '隧道TBM(盾构)独立竖井施工', 2, 3, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '大型钢结构拼装作业', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '工程线运输', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '铺轨作业', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '房建工程幕墙施工作业', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '房建工程屋面施工', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '高度≥15 m的通信铁塔组立作业', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '连锁试验', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '电力停送电作业', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '牵引供电停送电作业', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '短路试验作业', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '接触网安装架设', 2, 4, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '天窗点施工作业', 2, 5, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '动火作业', 2, 6, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '爆破作业', 2, 6, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '起重吊装作业', 2, 6, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '大型机械设备安装', 2, 6, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '深基坑开挖作业', 2, 6, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '支架拼装作业', 2, 6, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '支架预压作业', 2, 6, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '支架拆除作业', 2, 6, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);
INSERT INTO `rail_keywork_list` (content_name,`level` ,parent_id ,tenant_id ,is_used ,update_time,remark ) VALUES ( '下穿既有设施施工作业', 2, 6, 'ebefaf4df57d3d583b8f3f0e85099794', 1, '2025-07-12 22:58:28', NULL);

-- 分类
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业许可证', '证件', '是否办理高处作业许可证件', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '设备操作', '相关证件', '设备操作人员是否持证上岗，是否存在酒后作业', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '设备操作', '维修保养', '设备是否按期进行了维修保养', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工过程', '模板安装', '模板安装满足方案要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工过程', '混凝土浇筑', '振捣器等设备合格；施工区域严禁非施工人员进入', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '高处作业', '高处作业', '临边设置安全防护、安全通道；人员劳保用品穿戴情况，必要时设母索', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '临时用电', '相关证件', '电工证件情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '临时用电', '现场设备', '电箱、电缆等是否完好；接线、接地是否规范，是否安装剩余电流动作保护器', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '特殊时间', '夜间', '夜间施工前，各工段必须提前采取必要的安全保障措施；当晚作业使用的工具、材料等必须在白天进行全面认真检查；夜间施工应有足够的照明', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '特殊时间', '特殊天气', '暴风及暴雨后，应对高处作业安全设施逐一加以检查；遇有五级以上强风、浓雾、雷雨、等恶劣的气候，施工人员不得从事露天高空作业', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '安全技术交底', '分级交底情况；交底人员覆盖情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身混凝土浇筑' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '班前喊话', '当日班前会开展；作业分工、风险传达；应急措施', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业许可证', '证件', '是否办理动火作业许可证；\r\n是否办理高处作业许可证；\r\n是否办理吊装作业许可证', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '汽车吊', '相关证件', '确认汽车吊和人员巳报批，定期维保', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '汽车吊', '安全装置', '设备运行正常，限载、限位装置等工作状态正常', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊索具', '起重钢丝绳', '起重采用钢丝绳，外观、合格证、质保资料、使用条件符合要求； \r\n接头插编长度和外观符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '高处作业', '高处作业', '临边设置安全防护、安全通道；\r\n人员劳保用品穿戴情况，必要时设母索', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '动火作业', '相关证件', '人员证件齐全，已报批', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '动火作业', '防火措施', '周围易燃、易爆物品已清理，隔离；\r\n灭火器配备；\r\n人员防护用品', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '警戒措施', '指挥人员', '司索工证件情况；\r\n通信情况；\r\n起吊条件满足“十不吊”', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '警戒措施', '警戒措施', '警示牌；\r\n封闭措施、警戒人员', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '安全技术交底', '分级交底情况；\r\n交底人员覆盖情况；\r\n明确拆除顺序', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '班前喊话', '当日班前会开展；\r\n作业分工、风险传达；\r\n应急措施', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '高墩身拆模' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '其他', '', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工条件', '动火令', '动火作业许可证是否已办理并审批通过', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工条件', '作业人员资质', '动火操作人员是否持有有效的特种作业操作证(焊工证等）', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工条件', '作业前培训', '动火操作人员是否接受了动火作业安全操作规程及应急预案培训', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工条件', '作业环境检查', '作业现场是否已清理,无易燃易爆物品,或已采取有效隔离措施', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业准备', '消防器材准备', '灭火器、消防沙、消防水带等消防器材是否已配备并处于可用状态', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业准备', '个人防护', '动火操作人员是否穿戴了防火服防护眼镜防护手套等个人防护装备', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业准备', '动火设备检查', '动火设备(如焊枪切割机等)是否完好,无损坏或泄漏', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业准备', '气体管理', '氧气瓶、乙快瓶等气体瓶是否已固定,且间距符合要求,无泄漏', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业过程', '动火作业监护', '是否有专门的监护人负责动火作业现场的安全监护', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业过程', '作业规范', '动火作业是否按照操作规程进行,无违规操作', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业过程', '防火措施', '作业现场是否设置了防火屏障或遮挡物,防止火花飞溅', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业后检查', '现场清理', '动火作业结束后,是否及时清理现场,消除安全隐患', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业后检查', '设备归位', '动火设备是否中已归位,并妥善存放', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业后检查', '消防器材复位', '消防器材是否已复位,并处于可用状态', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '安全技术交底', '是否进行了安全技术交底,明确作业风险及防控措施', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '动火作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '班前喊话', '是否开展了班前会,强调了作业安全要求及注意事项', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '钢构件', '吊耳', '外观检查、焊缝;吊耳孔直径、主板板厚', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '钢构件', '外观', '各构件外观检查符合要求；\r\n编号及放置方向符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊索具检查', '钢丝绳', '外观检查、长度、绳卡； \r\n质保资料', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊索具检查', '卸扣', '外观检查(扣体横栓）;\r\n质保资料', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊装设备', '相关证件', '特种设备和特种作业人员报批', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊装设备', '支垫', '支垫措施稳固', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊装设备', '安全装置', '限载限位装置: \r\n声光警示装置', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '同步性措施', '警戒措施', '警示牌; \r\n封闭措施警戒人员', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '同步性措施', '缆风绳', '外观、位置、长度通信情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '同步性措施', '指挥人员', '证件情况；通信情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '同步性措施', '人员站位', '人员分工表；参观人员站位;作业人员和管理人员的站位及数量', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '安全防护及文明施工', '高处作业', '临边防护安全通道情况;人员劳保用品穿戴情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '安全防护及文明施工', '文明施工', '现场垃圾回收;设备防漏油措施(吸油毡)', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '试吊', '初步试吊', '开始起吊时,先将构件吊离地面 200~300 mm 后停止起吊,并检查吊机稳定性制动装置可靠性构件平衡性和绑扎牢固性等,待确认无误后,方可继续起吊', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '试吊', '试吊观察', '主钩钢丝绳是否均匀绷紧；卸扣与上吊耳是否顺直；吊耳有无异响油漆剥落；重心是否明显偏差', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '安全技术交底', '分级交底情况；交底人员覆盖情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '起重吊装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工条件', '施工方案', '施工方案是否编审、审批齐全有效', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工条件', '安全技术交底', '风险因素分析完毕,分解到具体操作层,已完成培训及安全技术交底 ', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工条件', '安全技术措施', '安全技术措施已经按照专项方案的要求执行；是否检查施工作业区域不良地质情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工条件', '应急准备', '应急物资设备到位,通信畅通,应急照明、消防器材、医疗用品等符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工条件', '机械设备', '进场验收记录齐全有效,安全交底、防护到位', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '施工过程', '安装顺序', '按照方案,明确当日安装内容及顺序', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '试吊', '初步试吊', '开始起吊时,先将构件吊离地面 200~300 mm 后停止起吊,并检查吊机稳定性制动装置可靠性构件平衡性和绑扎牢固性等,待确认无误后,方可继续起吊', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '试吊', '试吊观察', '主钩钢丝绳是否均匀绷紧；卸扣与上吊耳是否顺直；吊耳有无异响油漆剥落；重心是否明显偏差', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊装设备', '吊装机械', '满足额定荷载要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊装设备', '相关证件', '特种设备和特种作业人员报批', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊装设备', '支垫', '支垫措施稳固', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '吊装设备', '安全装置', '限载限位装置；声光警示装置', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '作业人员', '特种人员', '特种人员配足配齐且持证上岗', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '同步性措施', '警戒措施', '警示牌; \r\n封闭措施警戒人员', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '同步性措施', '缆风绳', '外观、位置、长度通信情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '同步性措施', '指挥人员', '证件情况；通信情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '同步性措施', '人员站位', '人员分工表；参观人员站位;作业人员和管理人员的站位及数量', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '安全防护及文明施工', '高处作业', '临边防护安全通道情况;人员劳保用品穿戴情况', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '安全防护及文明施工', '文明施工', '现场垃圾回收;设备防漏油措施(吸油毡)', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '周边环境核查', '作业环境', '作业环境是否组织检查验收,施工现场是否满足施工作业要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '安全技术交底', '分级交底情况;\r\n明确工艺顺序', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ((select id from rail_keywork_list where content_name = '大型机械设备安装' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), '', '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '作业人员', '证件', '架子工是否持有效证件上岗', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '方案和交底', '方案', '是否编制专项方案,超过一定规模的高支模作业是否组织专家论证', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '方案和交底', '交底', '技术交底和安全技术交底是否到位', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '起重设备', '相关证件', '确认起重设备和人员(操作手、信号司索工等)已报批,定期维保', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '起重设备', '安全装置', '设备运行正常,限载、限位装置等工作状态正常', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '吊索具', '起重钢丝绳', '起重采用钢丝绳,外观、合格证、质保资料、使用条件符合要求；\r\n接头插编长度和外观符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '过程管控', '高处作业', '临边设置安全防护、安全通道;人员劳保用品穿戴情况,必要时设母索', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '过程管控', '拆除顺序', '是否严格按照方案进行拆除作业', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '其他措施', '应急预案', '应急救援预案是否编制齐全,应急救援培训是否到位,应急处置措施是否齐全有效,施工作业人员应知应会是否培训口是否到位', '52d42b85a9abae8c6f32a940ace6edc0');
-- INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( 67, NULL, '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '作业人员', '证件', '架子工是否持有效证件上岗', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '方案和交底', '方案', '是否编制专项方案,超过一定规模的高支模作业是否组织专家论证 ', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '方案和交底', '交底', '技术交底和安全技术交底是否到位', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '起重设备', '相关证件', '确认起重设备和人员(操作手、信号司索工等)已报批,定期维保', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '起重设备', '安全装置', '设备运行正常,限载、限位装置等工作状态正常', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '吊索具', '起重钢丝绳', '起重采用钢丝绳,外观、合格证、质保资料、使用条件符合要求；\r\n接头插编长度和外观符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '地基承载力', '地基承载力是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '扫地杆', '地杆距离是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '剪刀撑', '剪刀撑设置位置和角度是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '连墙件', '连墙件设置是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '悬挑钢梁锚汘鑹堆固支座', '悬挑钢梁锚固支座是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '步距、跨距', '步距、距是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '安全防护网', '手架水平防护、外侧安全网设置是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '防护栏杆', '作业层防护栏杆是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '脚手板', '脚手板铺设是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '构配件', '构配件表观质量是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '高处作业', '高处作业人员是否按要求使用劳动防护用品', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '其他措施', '应急预案', '应急救援预案是否编制齐全,应急救援培训是否到位,应急处置措施是否齐全有效,施工作业人员应知应会是否培训口是否到位', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拼装作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '作业人员', '证件', '架子工是否持有效证件上岗', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '方案和交底', '方案', '是否编制专项方案,超过一定规模的高支模作业是否组织专家论证 ', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '方案和交底', '交底', '技术交底和安全技术交底是否到位', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '方案和交底', '观测', '是否提前设置支架沉降观测点', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '施工过程', '指挥', '是否设置专人统一指挥人员', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '施工过程', '地基承载力', '地基承载力是否符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '施工过程', '吊装过程', '避免吊装碰撞支架', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '吊索具', '起重钢丝绳', '起重采用钢丝绳,外观、合格证、质保资料、使用条件符合要求；\r\n接头插编长度和外观符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '荷载', '支架预压荷载不应小于支架承受的混凝土结构恒载与模板重量之和的1.1倍\r\n支架预压区域应划分成若干预压单元,每个预压单元内实际预压荷载强度的最大值不应超过该预压单元内预压荷载强度平均值的110%', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '荷载', '每个预压单元内的预压荷载可采用均布形式', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '分级加载', '不应少于3级,3级加载依次宜为单元内预压荷载值的60%、80%、100%', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '加载完成', '支架预压监测过程中,各监测点最初24h的沉降量平均值小于1mm,或各监测点最初72h的沉降量平均值小于5mm口是否应判定支架预压合格', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '卸载', '可一次性卸载,预压荷载应对称、均衡、同步', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '监测点布置', '沿混凝土结构纵向每隔1/4跨径应布置一个监测断面', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '监测点布置', '每个监测断面上的监测点不宜少于5个,并应对称布置', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '其他措施', '应急预案', '应急救援预案是否编制齐全,应急救援培训是否到位,应急处置措施是否齐全有效,施工作业人员应知应会是否培训口是否到位', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架预压作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '作业人员', '证件', '架子工是否持有效证件上岗', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '方案和交底', '方案', '是否编制专项方案,超过一定规模的高支模作业是否组织专家论证 ', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '方案和交底', '交底', '技术交底和安全技术交底是否到位', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '起重设备', '相关证件', '确认起重设备和人员(操作手、信号司索工等)已报批,定期维保', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '起重设备', '安全装置', '设备运行正常,限载、限位装置等工作状态正常', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '吊索具', '起重钢丝绳', '起重采用钢丝绳,外观、合格证、质保资料、使用条件符合要求；\r\n接头插编长度和外观符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '吊索具', '卸扣', '卸扣的外观、合格证、质保资料、使用条件符合要求', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '高处作业', '临边设置安全防护、安全通道;人员劳保用品穿戴情况,必要时设母索', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '过程管控', '拆除顺序', '是否严格按照方案进行拆除作业', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '其他措施', '应急预案', '应急救援预案是否编制齐全,应急救援培训是否到位,应急处置措施是否齐全有效,施工作业人员应知应会是否培训口是否到位', '52d42b85a9abae8c6f32a940ace6edc0');
INSERT INTO `rail_safe_table_item`(keywork_id,check_content_classify,check_content,check_require,check_require_intro,tenant_id) VALUES ( (select id from rail_keywork_list where content_name = '支架拆除作业' and tenant_id = '52d42b85a9abae8c6f32a940ace6edc0'), NULL, '其他措施', '班前喊话', '当日班前会开展;\r\n作业分工、风险传达;\r\n应急措施', '52d42b85a9abae8c6f32a940ace6edc0');


INSERT INTO `rail_assess_table_item`(tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ('ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 1, '核心指标', '事故（事件）', '发生事故(事件),直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item`(tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 2, '核心指标', '不良行为', '涉及安全一般、较大、严重不良行为,直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 3, '核心指标', '评价扣分行为', '被上级单位单次评价扣5分及以上的,直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 4, '量化指标', '评价扣分及重大事故隐患', '被上级单位单次评价扣1分的,每次扣20分;被项目建设管理单位下发重大园患整改通知书、单次评价扣2分的,每项扣30分', 30, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：3', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 5, '量化指标', '考勤打卡', '出勤打卡率、在岗时间,每次扣2分;脱岗每次扣10分', 10, NULL, NULL, NULL, '判断打卡方式：第一种：通过上传的文件记录中有该人员填写或签字的记录来判断：班前会、晚交班会、过程盯控、网格安全员日志，通过以上功能中有该人员填写或签字的记录，则判断该人员当天有打卡；第二种:网格安全员当天有轨迹记录，则算当天打卡第二种；两种有一种即为打卡；扣分方式：一天未打卡，扣2分，最多扣10分；最多扣10分，得分=10-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 6, '量化指标', '智能安全帽/执法记录仪', '是否正确佩戴,每次预警扣1分;佩戴时长是否满足要求,扣2分:是否他人替戴,每次扣10分', 10, NULL, NULL, NULL, '判断方式：1、若该人员绑定了有智能安全帽或手持终端，则判断该人员是否每天有轨迹记录，一天没有扣2分；最多扣10分2、人员未绑定以上两种设备，则系统无法判断：扣分为0，得分：10', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 7, '量化指标', '工装配备', '是否配备并使用对讲机,每次扣1分;安全防护服是否按照制定颜色区分穿献,每次扣1分:工具包等必要的安全装备是否配备携带,每次扣1分', 20, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：20', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 8, '量化指标', '班前讲话', '是否参与每日网格内班前讲话、每缺1次扣1分:班前讲话记录的是否完整,每次扣5分', 5, NULL, NULL, NULL, '当月该网格安全员负责的网格是否每日都有班前会记录，缺一次扣1分；最多扣5分；\r\n系统若无法判断该网格安全员所属网格的时候，无法判断：扣分为0，得分：5', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 9, '量化指标', '日常巡查及整改', '未开展隐患排查整治,每次扣5分:自查发现问题未监督隐患整改到位,每次扣5分;检查整改记录是否完整,每缺1次扣5分:风险是否得到及时控制和消除,扣5分:上级检查发现安全问题,每件扣2分:针对上级检查问题未及时盯控整改到位的,每次扣 20分', 5, NULL, NULL, NULL, '1、没有该网格安全员提交的安全隐患排查记录，直接扣5分；2、改网格安全员提的隐患整改有超期状态为整改中、待复查的隐患，一个扣5分，最多扣5分；得分=5-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 10, '量化指标', '跟班作业', '是否跟随劳务队伍班组进行作业,每次扣1分,查跟班作业记录填写是否完整、每次扣2分;对施工过程是否全面监控,每次扣2分', 10, NULL, NULL, NULL, '当月网格员安全日志或过程盯控是否每日都有记录，缺失一次扣2分，最多扣10分；得分=10-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 11, '量化指标', '关键施工作业', '是否参与作业前安全条件是否确认,每次扣5分:盯控作业中的安全措施是否到位,每次扣5分;作业记录是否填写完善,每次扣5分', 10, NULL, NULL, NULL, '当月有安全条件表的日期，看对应的日期是否有过程盯控记录，没有则一次扣5分，最多扣10分；得分=10-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 1, '网格安全员日常考核表', 12, '加分项', '管理创新与突出贡献', '提出有效安全管理建议被采纳。获上级单位安全表彰，建议采纳+5分；表彰+5分/次。', 10, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 1, '核心指标', '事故（事件）', '责任网格发生生产安全责任事故（或者险性事故、重大影响），直接评定为不合格。', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 2, '核心指标', '重大监管失职', '被上级单位通报重大履职缺失或瞒报重大隐患，直接评定为不合格。', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 3, '量化指标', '网格监督职责', '未牵头制定网格监督计划（明确对象、监督内容、标准、监督方式等）。缺失扣5分；内容不全扣2分/项；未签字或未发布扣2分。', 5, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：5', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 4, '量化指标', '安全条件确认监督', '1.未督促开展安全条件确认。缺少1次扣10分。安全条件确认走形式，扣10分。2.未检查安全条件确认落实情况，缺1次监督扣5分；未核查确认表扣2分/次。', 15, NULL, NULL, NULL, '若当月有填写的安全条件确认表，签字人是该安监专务且未签字的，一次扣两份；最多扣15分；得分=15-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 5, '量化指标', '隐患整改闭环', '未跟踪整改闭环。限期1项未闭环扣5分；未确认整改或整改不符合要求1次扣5分。', 10, NULL, NULL, NULL, '当月有隐患超时未整改、未复查的隐患，一个扣5分，最多扣10分；得分=10-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 6, '量化指标', '网格安全员履职监督', '未监督网格安全员在岗、五步走、关键作业盯控、工作清单填写等履职情况。发现1次履职缺失未纠正扣5分。', 10, NULL, NULL, NULL, '该安监专务负责的网格下的网格安全员未填写网格安全日志，一日未填写扣5分；最多扣10分；得分=10-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 7, '量化指标', '部门监督计划执行', '1.未牵头制定网格监督计划（明确对象、内容、标准等），缺失扣5分；内容不全扣2分/项；未签字或未发布扣2分。2.对部门未开展监督检查。缺1次扣2分；无记录扣2分。', 5, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：5', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 8, '量化指标', '部门隐患处置', '未落实部门隐患处置措施（口头教育/隐患告知书）。缺失扣5分；未覆盖五部一室扣1分/部门。', 5, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：5', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 9, '量化指标', '班前讲话监督', '当日班前讲话未开展或未审核（影像资料、签字记录）。缺1次班前讲话扣5分；缺1次审核扣2分；记录不全扣1分/次。', 10, NULL, NULL, NULL, '当月班前会是否每日都有记录，缺一次扣5分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 10, '量化指标', '关键节点验收监督', '1.未督促技术负责人开展关键节点验收。缺少1次扣10分。2.未检查关键节点安全条件验收落实情况。缺1次监督扣5分；未核查验收表扣2分/次。', 15, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：15', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 11, '量化指标', '重大隐患清零', '未监督重大隐患整改及复工验收，未跟踪整改扣10分/项；未报告总监扣5分/项。', 15, NULL, NULL, NULL, '当月有隐患类型为重大隐患的隐患排查的状态为整改中、待复查的隐患，一个扣10分，最多扣10分；得分=10-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 12, '量化指标', '工作台账与报告', '未按填写监督记录、形成台账或未每月底上报安全监督情况。台账缺失扣5分；未按时报告扣5分；内容不实扣2分/项。', 10, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：10', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 2, '安监专务考核表', 13, '加分项', '管理创新与突出贡献', '提出有效安全管理建议被采纳。获上级单位安全表彰，建议采纳+5分；表彰+5分/次。', 10, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 3, '施工队伍考核表', 1, '核心指标', '事故（事件）', '发生事故(事件),直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 3, '施工队伍考核表', 2, '核心指标', '不良行为', '涉及安全一般、较大、严重不良行为,直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 3, '施工队伍考核表', 3, '核心指标', '重大监管失职', '被上级单位单次评价扣5分及以上的,直接评定为不合格', 0, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 3, '施工队伍考核表', 4, '量化指标', '评价扣分及重大事故隐患', '被上级单位单次评价扣1分的,每次扣20分;被项目建设管理单位下发重大园患整改通知书、单次评价扣2分的,每项扣30分', 30, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：30', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 3, '施工队伍考核表', 5, '量化指标', '人员履约', '合同中网格安全员未到位,扣10分;网格安全员不履职,每次扣5分', 20, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：30', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 3, '施工队伍考核表', 6, '量化指标', '安全教育', '劳务队伍是否定期开展内部安全教育,扣10分;是否积极参与项目组织的安全教育培训，每次扣5分', 15, NULL, NULL, NULL, '1当月该班组是否有类型为“班组级安全教育培训/特种作业培训”，没有扣10分，有一个即不扣分；2当月该班组是否有类型为-项目级安全教育培训，没有则扣5分；得分=15-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 3, '施工队伍考核表', 7, '量化指标', '班前讲话', '是否执行每日班前讲话制度,每次扣2分;讲话内容是否包括安全注意事项,每次扣2分;是否明确当日作业存在的危险源,每次扣2分;讲话记录是否留存,每次扣2分', 10, NULL, NULL, NULL, '当月该班组是否每日都有班前会记录，缺一次扣2分，最多扣10分；得分=10-扣分', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 3, '施工队伍考核表', 8, '量化指标', '隐患整改', '各级检查下发的隐患数量比例,每条扣2分;隐患是否及时进行整改,每次扣5分;是否存在拒不整改、屡改屡犯行为,每次扣 25 分', 25, NULL, NULL, NULL, '当月责任班组是该班组且有隐患整改超期为整改中、待复查的隐患，一个扣5分，最多扣25分；得分=25-扣分。', NULL, NULL);
INSERT INTO `rail_assess_table_item` (tenant_id,table_type,table_name,index_no,assess_target,assess_entry,assess_content,score_standard,score_deduct,score_add,remark,calculate_rules,create_uid,create_time) VALUES ( 'ebefaf4df57d3d583b8f3f0e85099794', 3, '施工队伍考核表', 9, '加分项', '管理创新与突出贡献', '提出有效安全管理建议被采纳。获上级单位安全表彰，建议采纳+5分；表彰+5分/次。', 10, NULL, NULL, NULL, '系统无法判断：扣分为0，得分：0', NULL, NULL);
