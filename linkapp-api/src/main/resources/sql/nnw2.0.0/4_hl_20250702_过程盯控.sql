/*
 Navicat Premium Data Transfer

 Source Server         : 涉铁工程
 Source Server Type    : MySQL
 Source Server Version : 50644
 Source Host           : ************:15051
 Source Schema         : linkappdb

 Target Server Type    : MySQL
 Target Server Version : 50644
 File Encoding         : 65001

 Date: 02/07/2025 13:49:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for rail_process_monitor
-- ----------------------------
DROP TABLE IF EXISTS `rail_process_monitor`;
CREATE TABLE `rail_process_monitor`  (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                         `tenant_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目id',
                                         `grid_id` bigint(20) NULL DEFAULT NULL COMMENT '网格id',
                                         `monitor_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '盯控标题',
                                         `monitor_date` datetime(0) NULL DEFAULT NULL COMMENT '盯控日期',
                                         `monitor_content` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '盯控内容',
                                         `monitor_file1` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '盯控附件1',
                                         `monitor_file2` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '盯控附件2',
                                         `monitor_file3` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '盯控附件3',
                                         `monitor_file4` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '盯控附件4',
                                         `monitor_file5` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '盯控附件5',
                                         `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                         `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '过程盯控表' ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;
