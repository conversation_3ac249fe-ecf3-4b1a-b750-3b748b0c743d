-- =============================================
-- 跟班作业模块数据库表
-- 开发者: AI助手
-- 日期: 2025-01-01
-- 功能: 1.2.7 跟班作业模块完整表结构
-- =============================================
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99157', 9915, '跟班作业', 't_follow_work', '跟班作业', 2, NULL, NULL, 99157, 1, 'organization/followWork', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- 1. 跟班计划表
CREATE TABLE `rail_follow_plan` (
    `id_` bigint(20) AUTO_INCREMENT COMMENT 'ID主键',
    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
    `user_id_` bigint(20) DEFAULT NULL COMMENT '跟班人员ID(linkapp_user)',
    `is_deleted_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='跟班计划表';

-- 2. 跟班作业要求表
CREATE TABLE `rail_follow_requirement` (
    `id_` bigint(20) AUTO_INCREMENT COMMENT 'ID主键',
    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
    `sort_order_` int(10) DEFAULT 0 COMMENT '排序序号',
    `content_` text COMMENT '作业要求内容',
    `is_deleted_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `use_state_` int(10) DEFAULT 1 COMMENT '启用状态(0禁用 1启用)',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='跟班作业要求表';

-- 3. 跟班记录表
CREATE TABLE `rail_follow_record` (
    `id_` bigint(20) AUTO_INCREMENT COMMENT 'ID主键',
    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
    `follow_user_id_` bigint(20) DEFAULT NULL COMMENT '跟班人员ID(linkapp_user)',
    `plan_follow_date_` datetime DEFAULT NULL COMMENT '计划跟班日期',
    `grid_ids_` varchar(255) DEFAULT NULL COMMENT '网格IDs(多个用逗号分隔)',
    `duty_situation_summary_` text COMMENT '当日值班情况纪要',
    `production_result_` int(10) DEFAULT NULL COMMENT '带班生产结果(字典代码:follow_production_result 1正常 2口头提醒 3无施工 4其他)',
    `status_` int(10) DEFAULT 0 COMMENT '状态(字典代码:follow_status 0未填写 1签字中 2已完成)',
    `actual_fill_date_` datetime DEFAULT NULL COMMENT '实际填写日期',
    `fill_user_id_` bigint(20) DEFAULT NULL COMMENT '填写人员ID(linkapp_user)',
    `fill_user_sign_url_` varchar(500) DEFAULT NULL COMMENT '填写人签字URL',
    `handover_user_id_` bigint(20) DEFAULT NULL COMMENT '接班人员ID(linkapp_user)',
    `handover_user_sign_url_` varchar(500) DEFAULT NULL COMMENT '接班人员签字URL',
    `handover_sign_time_` datetime DEFAULT NULL COMMENT '接班人员签字时间',
    `is_active_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否激活(0否 1是)',
    `is_deleted_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='跟班记录表';

-- 4. 跟班检查项目目录表(分层级)
CREATE TABLE `rail_follow_check_catalog` (
    `id_` bigint(20) AUTO_INCREMENT COMMENT 'ID主键',
    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
    `parent_id_` bigint(20) DEFAULT 0 COMMENT '父级ID(0为顶级)',
    `content_` text COMMENT '内容',
    `catalog_level_` int(10) DEFAULT 1 COMMENT '层级(1一级 2二级 3三级...)',
    `sort_order_` int(10) DEFAULT 0 COMMENT '排序序号',
    `catalog_path_` varchar(1000) DEFAULT NULL COMMENT '层级路径(如:1,2,3)',
    `is_deleted_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='跟班检查项目目录表';

-- 5. 跟班记录检查结果表(关联目录)
CREATE TABLE `rail_follow_check_result` (
    `id_` bigint(20) AUTO_INCREMENT COMMENT 'ID主键',
    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
    `follow_record_id_` bigint(20) DEFAULT NULL COMMENT '跟班记录ID(rail_follow_record)',
    `catalog_id_` bigint(20) DEFAULT NULL COMMENT '检查项目目录ID(rail_follow_check_catalog)',
    `catalog_content_` text COMMENT '检查目录内容(快照,防止目录内容变更影响历史数据)',
    `parent_catalog_id_` bigint(20) DEFAULT NULL COMMENT '父目录ID(rail_follow_check_catalog)',
    `parent_catalog_content_` text COMMENT '父目录内容(快照,防止父目录内容变更影响历史数据)',
    `check_conclusion_` int(10) DEFAULT NULL COMMENT '检查结论(字典代码:follow_check_conclusion 1是 2否 3不涉及)',
    `is_deleted_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='跟班记录检查结果表';

-- =============================================
-- 初始化跟班作业要求数据
-- =============================================

INSERT INTO `rail_follow_requirement` (`tenant_id_`, `sort_order_`, `content_`, `creator_id_`, `create_time_`, `use_state_`) VALUES 
(NULL, 1, '每日带班领导带班生产,施工员(领工员)和网格安全员应对重点部位、关键工序、特殊时段作业全程跟班。施工项目部应明确本项目重点部位、关键工序、特殊时段作业内容。', NULL, NOW(), 1),
(NULL, 2, '施工项目部制定跟班作业制度,明确各级管理人员跟班作业时间频次、工作内容等,编制跟班作业计划并严格落实。', NULL, NOW(), 1),
(NULL, 3, '施工现场应配备网格安全员对施工作业进行全程监督。网格安全员不在岗时网格内禁止开展任何施工作业。', NULL, NOW(), 1),
(NULL, 4, '为应对突发情况,施工项目部应配置机动网格安全员,机动网格安全员数量应满足现场需要。', NULL, NOW(), 1),
(NULL, 5, '安监专务监督检査跟班领导带班生产,施工员(领工员)、网格安全员关键施工作业跟班情况。', NULL, NOW(), 1);

-- =============================================
-- 字典数据初始化
-- =============================================

-- 1. 跟班状态字典
INSERT INTO `linkappdb`.`sys_dict`
(`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`)
VALUES ('23', '跟班状态', 'follow_status', '跟班状态', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES
('23', '未填写', '0', '未填写', '1', '1', NULL, NULL, NULL, NULL, '0'),
('23', '签字中', '1', '签字中', '2', '1', NULL, NULL, NULL, NULL, '0'),
('23', '已完成', '2', '已完成', '3', '1', NULL, NULL, NULL, NULL, '0');

-- 2. 带班生产结果字典
INSERT INTO `linkappdb`.`sys_dict`
(`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`)
VALUES ('24', '带班生产结果', 'follow_production_result', '带班生产结果', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES
('24', '正常', '1', '正常', '1', '1', NULL, NULL, NULL, NULL, '0'),
('24', '口头提醒', '2', '口头提醒', '2', '1', NULL, NULL, NULL, NULL, '0'),
('24', '无施工', '3', '无施工', '3', '1', NULL, NULL, NULL, NULL, '0'),
('24', '其他', '4', '其他(在值班纪要填写)', '4', '1', NULL, NULL, NULL, NULL, '0');

-- 3. 检查结论字典
INSERT INTO `linkappdb`.`sys_dict`
(`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`)
VALUES ('25', '跟班检查结论', 'follow_check_conclusion', '跟班检查结论', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item`
(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES
('25', '是', '1', '是', '1', '1', NULL, NULL, NULL, NULL, '0'),
('25', '否', '2', '否', '2', '1', NULL, NULL, NULL, NULL, '0'),
('25', '不涉及', '3', '不涉及', '3', '1', NULL, NULL, NULL, NULL, '0');

-- =============================================
-- 初始化检查项目目录数据
-- =============================================

-- 一级目录
INSERT INTO `rail_follow_check_catalog` (`id_`, `tenant_id_`, `parent_id_`, `content_`, `catalog_level_`, `sort_order_`, `catalog_path_`, `creator_id_`, `create_time_`) VALUES 
(1, NULL, 0, '安全文明施工', 1, 1, '1', NULL, NOW()),
(2, NULL, 0, '质量技术管理', 1, 2, '2', NULL, NOW()),
(3, NULL, 0, '高空及大型机械设备', 1, 3, '3', NULL, NOW());

-- 二级目录-安全文明施工
INSERT INTO `rail_follow_check_catalog` (`id_`, `tenant_id_`, `parent_id_`, `content_`, `catalog_level_`, `sort_order_`, `catalog_path_`, `creator_id_`, `create_time_`) VALUES 
(4, NULL, 1, '危险较大分部分项的安全管理监控措实情况。', 2, 1, '1,4', NULL, NOW()),
(5, NULL, 1, '高空作业是否佩戴安全带，安全带是否正确。', 2, 2, '1,5', NULL, NOW()),
(6, NULL, 1, '施工现场防火安全管理（防火重点部位是否配备充足灭火器，施工现场是否设有消防通道，井道是否有易燃接触，是否办理进行动火审批）。', 2, 3, '1,6', NULL, NOW()),
(7, NULL, 1, '管理人员是否进行班前安全教育，人员防护设施是否完善；封闭是否牢固，有无缺口，现场警示标识是否到位。', 2, 4, '1,7', NULL, NOW()),
(8, NULL, 1, '施工现场是否有"四类"人员。', 2, 5, '1,8', NULL, NOW()),
(9, NULL, 1, '特种作业是否持有效证上岗。', 2, 6, '1,9', NULL, NOW()),
(10, NULL, 1, '施工现场备齐申报设备的管理（申报设备是否安置控制，设备电器是否符合防火防雨要求，是否采用一机一闸，消防器材配置符合要求。）', 2, 7, '1,10', NULL, NOW()),
(11, NULL, 1, '施工现场是否出现质量问题及处理情况。', 2, 8, '1,11', NULL, NOW());

-- 二级目录-质量技术管理
INSERT INTO `rail_follow_check_catalog` (`id_`, `tenant_id_`, `parent_id_`, `content_`, `catalog_level_`, `sort_order_`, `catalog_path_`, `creator_id_`, `create_time_`) VALUES 
(12, NULL, 2, '施工作业是否按设测量。', 2, 1, '2,12', NULL, NOW()),
(13, NULL, 2, '施工作业工艺，工序是否规范作业。', 2, 2, '2,13', NULL, NOW()),
(14, NULL, 2, '施工过程与形象进度是否一致', 2, 3, '2,14', NULL, NOW()),
(15, NULL, 2, '带班生产中是否出现其他的技术质量问题。', 2, 4, '2,15', NULL, NOW());

-- 二级目录-高空及大型机械设备
INSERT INTO `rail_follow_check_catalog` (`id_`, `tenant_id_`, `parent_id_`, `content_`, `catalog_level_`, `sort_order_`, `catalog_path_`, `creator_id_`, `create_time_`) VALUES 
(16, NULL, 3, '机械设备操作人员是否为备案人员，是否持有效证上岗，人机对应。', 2, 1, '3,16', NULL, NOW()),
(17, NULL, 3, '机械设备安全装置是否齐全有效，车轮或履带报告是否完全有效期内。', 2, 2, '3,17', NULL, NOW()),
(18, NULL, 3, '使用过程中是否设置一人一机防护。', 2, 3, '3,18', NULL, NOW()),
(19, NULL, 3, '机械设备使用前是否进行了报备并执行"四定"管理要求。', 2, 4, '3,19', NULL, NOW()),
(20, NULL, 3, '作业过程中是否执行安全制度（非纯近营业效70m外作业除外）。', 2, 5, '3,20', NULL, NOW()); 