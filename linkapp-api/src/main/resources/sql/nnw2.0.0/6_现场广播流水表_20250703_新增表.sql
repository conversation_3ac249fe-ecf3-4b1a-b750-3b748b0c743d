CREATE TABLE `rail_linkapp_voice_broad_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `device_code` varchar(128) DEFAULT NULL COMMENT '设备编号phone_number流水里面是这个参数',
  `glb` varchar(255) DEFAULT NULL COMMENT '列车所在公里标',
  `msg_type` varchar(10) DEFAULT NULL COMMENT '消息类型0:数据 ,1:告警',
  `speed` double DEFAULT NULL COMMENT '列车速度 km/h单位',
  `train_no` varchar(128) DEFAULT NULL COMMENT '列车车次',
  `lat` varchar(128) DEFAULT NULL COMMENT '纬度',
  `lng` varchar(128) DEFAULT NULL COMMENT '精度',
  `depart_id` int(11) DEFAULT NULL COMMENT '所属单位',
  `device_name` varchar(200) DEFAULT NULL COMMENT '设备名称',
  `user_id` varchar(255) DEFAULT NULL COMMENT '设备用户id',
  `is_online` varchar(10) DEFAULT NULL COMMENT '在线状态，0离线，1在线',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `phone_number` varchar(128) DEFAULT NULL COMMENT '手机号，用户账号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='喇叭流水记录表';

INSERT INTO `linkappdb`.`sys_dict_item` (`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES ('210', '9', '现场广播', '7', '现场广播', '11', '1', NULL, NULL, NULL, NULL, '0');

INSERT INTO `linkappdb`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('9920', NULL, '语音配置', 't_voice_broad_config', '语音配置', '1', NULL, NULL, '9920.000', '1', '', NULL, '1', NULL, NULL, NULL, '2025-06-12 21:26:04', NULL, '0');
