-- 天气记录表
CREATE TABLE `app_weather_record` (
  `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id_` varchar(32) NOT NULL COMMENT '租户ID',
  `obs_time_` varchar(32) DEFAULT NULL COMMENT '数据观测时间',
  `temp_` varchar(10) DEFAULT NULL COMMENT '温度，默认单位：摄氏度',
  `feels_like_` varchar(10) DEFAULT NULL COMMENT '体感温度，默认单位：摄氏度',
  `icon_` varchar(10) DEFAULT NULL COMMENT '天气状况和图标的代码',
  `text_` varchar(50) DEFAULT NULL COMMENT '天气状况的文字描述',
  `wind360_` varchar(10) DEFAULT NULL COMMENT '风向360角度',
  `wind_dir_` varchar(20) DEFAULT NULL COMMENT '风向',
  `wind_scale_` varchar(10) DEFAULT NULL COMMENT '风力等级',
  `wind_speed_` varchar(10) DEFAULT NULL COMMENT '风速，公里/小时',
  `humidity_` varchar(10) DEFAULT NULL COMMENT '相对湿度，百分比数值',
  `precip_` varchar(10) DEFAULT NULL COMMENT '当前小时累计降水量，默认单位：毫米',
  `pressure_` varchar(10) DEFAULT NULL COMMENT '大气压强，默认单位：百帕',
  `vis_` varchar(10) DEFAULT NULL COMMENT '能见度，默认单位：公里',
  `cloud_` varchar(10) DEFAULT NULL COMMENT '云量，百分比数值',
  `dew_` varchar(10) DEFAULT NULL COMMENT '露点温度',
  `create_time_` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  PRIMARY KEY (`id_`),
  KEY `idx_tenant_id_` (`tenant_id_`),
  KEY `idx_tenant_create_time_` (`tenant_id_`, `create_time_`),
  KEY `idx_create_time_` (`create_time_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气记录表'; 