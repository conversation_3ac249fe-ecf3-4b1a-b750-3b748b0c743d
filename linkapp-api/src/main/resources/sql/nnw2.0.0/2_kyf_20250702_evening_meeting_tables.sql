-- 晚交班会主表
CREATE TABLE `rail_evening_meeting` (
    `id_` bigint(20) AUTO_INCREMENT COMMENT 'ID主键',
    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
    `grid_ids_` varchar(255) DEFAULT NULL COMMENT '网格IDs(多个用逗号分隔)',
    `meeting_time_` datetime DEFAULT NULL COMMENT '晚交班时间',
    `speaker_` varchar(100) DEFAULT NULL COMMENT '主讲人',
    `attendee_count_` int(10) DEFAULT NULL COMMENT '参加人数',
    `location_` varchar(100) DEFAULT NULL COMMENT '地点',
    `work_completion_` text COMMENT '当日工作完成情况',
    `issues_` text COMMENT '存在问题及协调事项',
    `risk_judgment_` text COMMENT '次日风险研判',
    `next_day_plan_` text COMMENT '次日工作安排',
    `media_files_url_` varchar(1000) DEFAULT NULL COMMENT '图片/影像记录URL(多个用逗号分隔)',
    `status_` int(10) DEFAULT 0 COMMENT '状态(字典代码:evening_meeting_status 0协作中 1签字中 2已完成)',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `is_deleted_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='晚交班会主表';

-- 晚交班会用户表
CREATE TABLE `rail_evening_meeting_user` (
    `id_` bigint(20) AUTO_INCREMENT COMMENT 'ID主键',
    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
    `meeting_id_` bigint(20) DEFAULT NULL COMMENT '晚交班会ID',
    `user_id_` bigint(20) DEFAULT NULL COMMENT '签字人ID',
    `user_type_` int(10) DEFAULT NULL COMMENT '用户类型(字典代码:evening_meeting_user_type 0创建者 1协作者 2抄送人)',
    `signature_image_url_` varchar(1000) DEFAULT NULL COMMENT '签名图片URL',
    `signature_time_` datetime DEFAULT NULL COMMENT '签字时间',
    `status_` int(10) DEFAULT 0 COMMENT '状态(字典代码:evening_meeting_sign_status 0未签字 1已签字)',
    `is_deleted_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='晚交班会用户表';