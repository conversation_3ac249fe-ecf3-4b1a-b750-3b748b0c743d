-- 添加监测项字典
INSERT INTO `linkappdb`.`sys_dict`(`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`,
                                   `update_id_`, `update_time_`)
VALUES ('31', '监测项', 'monitor_category', '监测项', NULL, NULL, NULL, NULL);
-- 告警大类 1-吊车安全监测 2-防护员电子围栏 3-用电安全 4-汛情防护  5-AI行为告警 6-现场广播 7-网格员电子围栏 8-高支模监测 9-塔吊安全监测

INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`,
                                        `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`,
                                        `is_sync_`)
VALUES (31, '吊车安全监测', '1', '吊车安全监测', 1, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`,
                                        `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`,
                                        `is_sync_`)
VALUES (31, '防护员电子围栏', '2', '防护员电子围栏', 2, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`,
                                        `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`,
                                        `is_sync_`)
VALUES (31, '用电安全', '3', '用电安全', 3, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`,
                                        `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`,
                                        `is_sync_`)
VALUES (31, '汛情防护', '4', '汛情防护', 4, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`,
                                        `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`,
                                        `is_sync_`)
VALUES (31, 'AI行为告警', '5', 'AI行为告警', 5, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`,
                                        `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`,
                                        `is_sync_`)
VALUES (31, '现场广播', '6', '现场广播', 6, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`,
                                        `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`,
                                        `is_sync_`)
VALUES (31, '网格员电子围栏', '7', '网格员电子围栏', 7, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`,
                                        `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`,
                                        `is_sync_`)
VALUES (31, '高支模监测', '8', '高支模监测', 8, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`,
                                        `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`,
                                        `is_sync_`)
VALUES (31, '塔吊安全监测', '9', '塔吊安全监测', 9, 1, NULL, NULL, NULL, NULL, 0);


