CREATE TABLE `rail_linkapp_high_formwork_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `device_code` varchar(128) DEFAULT NULL COMMENT '设备编号',
  `up_times` int(11) DEFAULT NULL COMMENT '上传频率int类型，单位秒/s',
  `word_mode` varchar(20) DEFAULT NULL COMMENT '工作模式1 - 休眠模式，2 - 触发模式-已禁用，3 - 实时模式，4 - 初始化模式-已禁用',
  `alarm_status` varchar(20) DEFAULT NULL COMMENT '报警状态0 - 正常,1 - 预警,2 - 报警',
  `erg` float(20,2) DEFAULT NULL COMMENT '电量，单位百分号%',
  `device_sn` varchar(128) DEFAULT NULL COMMENT '设备编号',
  `sedimentation` varchar(128) DEFAULT NULL COMMENT '沉降值，单位毫米mm',
  `pressure` varchar(128) DEFAULT NULL COMMENT '压力值 单位 KN',
  `angle_y` varchar(128) DEFAULT NULL COMMENT '倾角角度 Y轴倾角 单位°',
  `angle_x` varchar(128) DEFAULT NULL COMMENT '倾角角度  X轴倾角 单位°',
  `data_time` varchar(50) DEFAULT NULL COMMENT '采集时间',
  `horizontal_displacement` varchar(128) DEFAULT NULL COMMENT '水平位移 单位毫米mm',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `device_sub_type` varchar(20) DEFAULT NULL COMMENT '设备类型',
  `item_id` varchar(32) DEFAULT NULL COMMENT '高支模分项id',
  `high_code` varchar(32) DEFAULT NULL COMMENT '对应高支模编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='高支模检测记录';



CREATE TABLE `rail_linkapp_high_formwork` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录id',
  `code` varchar(32) DEFAULT NULL COMMENT '高支模绑定标识',
  `name` varchar(100) DEFAULT NULL,
  `point_id` varchar(32) DEFAULT NULL COMMENT '工点id',
  `monitoring_point_file` varchar(500) DEFAULT NULL COMMENT '监测点分布图',
  `danger` int(11) DEFAULT NULL COMMENT '是否是危险工程 0否，1是',
  `test_unit_name` varchar(255) DEFAULT NULL COMMENT '第三方监测单位名称',
  `test_person` varchar(100) DEFAULT NULL COMMENT '第三方监测负责人',
  `test_scheme_file` varchar(500) DEFAULT NULL COMMENT '第三方监测方案文件pdf/图片',
  `test_person_phone` varchar(100) DEFAULT NULL COMMENT '第三方监测负责人电话',
  `overview` varchar(1000) DEFAULT NULL COMMENT '高支模概况',
  `create_time` datetime DEFAULT NULL,
  `modify_time` datetime DEFAULT NULL,
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='高支模信息记录表';

CREATE TABLE `rail_linkapp_high_formwork_item_ref` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `item_id` varchar(32) DEFAULT NULL COMMENT '分项id',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `status` int(10) DEFAULT NULL COMMENT '绑定状态，0绑定，1解绑',
  `work_mode` varchar(100) DEFAULT NULL COMMENT '工种模式，1 - 休眠模式，2 - 触发模式-已禁用，3 - 实时模式，4 - 初始化模式-已禁用',
  `up_times` int(11) DEFAULT NULL COMMENT '上传频率 单位 秒/s',
  `modify_time` datetime DEFAULT NULL COMMENT '绑定时间/解绑时间',
  `tenant_id` int(11) DEFAULT NULL,
  `item_name` varchar(255) DEFAULT NULL COMMENT '分项名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;




ALTER TABLE linkapp_device  add   `high_device_type` varchar(100) DEFAULT NULL COMMENT '高支模监测设备类型';
ALTER TABLE linkapp_device  add   `high_work_mode` varchar(255) DEFAULT NULL COMMENT '高支模设备工作模式';

