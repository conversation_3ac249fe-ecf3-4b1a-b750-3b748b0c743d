-- 1.菜单
-- 施工监测
-- 网格员电子围栏
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('99042', '9904', '网格员电子围栏', 't_grid_electric_fence', '网格员电子围栏', 2, NULL, NULL, 9904.2, 1, 'manage/gridElectronicFence', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- 2.建表
-- 人员关联设备表
CREATE TABLE `linkappdb`.`rail_person_ref_device`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`   varchar(64)  DEFAULT NULL COMMENT '租户id',
    `person_id_`   bigint(20) NOT NULL COMMENT '人员id',
    `device_id_`   varchar(64) NOT NULL COMMENT '设备id',
    `device_code_` varchar(64) NOT NULL COMMENT '设备code',
    `device_name_` varchar(255) DEFAULT NULL COMMENT '设备名称',
    `delete_state` tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_`   bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime     DEFAULT NULL COMMENT '创建日期',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime     DEFAULT NULL COMMENT '修改时间',
    `remark_`      text COMMENT '备注',
    PRIMARY KEY (`id_`)
) COMMENT='人员关联设备表';
-- 人员作业区域表
CREATE TABLE `linkappdb`.`rail_person_operation_area`
(
    `id_`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`    varchar(64) DEFAULT NULL COMMENT '租户id',
    `region_name_`  varchar(50) NOT NULL COMMENT '区域名称',
    `work_time_`    text        NOT NULL COMMENT '作业时间',
    `location_`     text        NOT NULL COMMENT '区域位置',
    `enabled_flag_` int(11) NOT NULL DEFAULT '1' COMMENT '是否启用 0-不启用，1-启用',
    `delete_state`  tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_`    bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`  datetime    DEFAULT NULL COMMENT '创建日期',
    `modify_id_`    bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`  datetime    DEFAULT NULL COMMENT '修改时间',
    `remark_`       text COMMENT '备注',
    `alarm_flag_`   tinyint(2) DEFAULT '0' COMMENT '是否有警报 1-有，0-无',
    PRIMARY KEY (`id_`)
) COMMENT='人员作业区域表';

-- 作业区域表关联人员表
CREATE TABLE `linkappdb`.`rail_person_operation_area_ref_person`
(
    `id_`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `operation_area_id_` bigint(20) NOT NULL COMMENT '作业区域id',
    `person_id_`         bigint(20) NOT NULL COMMENT '人员id',
    `delete_state`       tinyint(2) DEFAULT '0' COMMENT '删除状态 0：未删除 1：已删除',
    `create_id_`         bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`       datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_`         bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`       datetime DEFAULT NULL COMMENT '修改时间',
    `remark_`            text COMMENT '备注',
    PRIMARY KEY (`id_`)
) COMMENT='作业区域表关联人员表';

-- 人员定位数据表
CREATE TABLE `linkappdb`.`rail_person_location_data`
(
    `id_`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`     varchar(64)  DEFAULT NULL COMMENT '租户id',
    `person_id_`     bigint(20) NOT NULL COMMENT '人员id',
    `device_id_`     varchar(64) NOT NULL COMMENT '设备id',
    `device_code_`   varchar(64) NOT NULL COMMENT '设备code',
    `device_name_`   varchar(255) DEFAULT NULL COMMENT '设备名称',
    `location_time_` datetime    NOT NULL COMMENT '定位时间',
    `location_lng_`  varchar(20) NOT NULL COMMENT '经度',
    `location_lat_`  varchar(20) NOT NULL COMMENT '纬度',
    `move_flag_`     int(11) DEFAULT 0 COMMENT '是否移动 0-未移动，1-移动',
    `create_id_`     bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`   datetime     DEFAULT NULL COMMENT '创建日期',
    `modify_id_`     bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`   datetime     DEFAULT NULL COMMENT '修改时间',
    `remark_`        text COMMENT '备注',
    PRIMARY KEY (`id_`),
    index            `idx_tenant_id_equipment_id_location_time_move_flag_` (`tenant_id_`, `person_id_`, `location_time_`, `move_flag_`)
) COMMENT='人员设备定位数据表';
-- 人员工作记录表
CREATE TABLE `linkappdb`.`rail_person_work_record`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_id_`   varchar(64) DEFAULT NULL COMMENT '租户id',
    `person_id_`   bigint(20) NOT NULL COMMENT '人员id',
    `start_time_`  datetime NOT NULL COMMENT '作业开始时间',
    `end_time_`    datetime    DEFAULT NULL COMMENT '作业结束时间',
    `work_time_`   int(11) DEFAULT NULL COMMENT '作业时长',
    `create_id_`   bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime    DEFAULT NULL COMMENT '创建日期',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime    DEFAULT NULL COMMENT '修改时间',
    `remark_`      text COMMENT '备注',
    PRIMARY KEY (`id_`),
    KEY            `idx_tenant_id_person_id_start_time_end_time_` (`tenant_id_`,`person_id_`,`start_time_`,`end_time_`)
) COMMENT='人员工作记录表';
ALTER TABLE `linkappdb`.`rail_give_system_alarm`
-- 新增字段 区域ids，保存告警时的所有区域ids，可以是垂直保护区，也可以是作业区，用逗号分隔
    ADD COLUMN `area_ids_` varchar(255) DEFAULT NULL COMMENT '区域ids，保存告警时的所有区域ids，可以是垂直保护区，也可以是作业区，用逗号分隔';
ALTER TABLE `linkappdb`.`rail_person_location_data`
    ADD COLUMN `stay_flag_` int(11) DEFAULT 0 COMMENT '是否长期停留 0-未停留，1-停留';

-- 3.字典