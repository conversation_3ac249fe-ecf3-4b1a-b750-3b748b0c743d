-- 新增菜单
INSERT INTO `linkappdb`.`linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('9916', NULL, '考核穿透', 't_assess_penet', '考核穿透', 1, NULL, NULL, 9916.000, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('99161', '9916', '考核制度', 't_assess_rule', '考核制度', 2, NULL, NULL, 99161.000, 1, 'assess/system', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('99162', '9916', '负面清单', 't_assess_negative', '负面清单', 2, NULL, NULL, 99162.000, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`linkapp_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('99163', '9916', '月度考核', 't_assess_month', '月度考核', 2, NULL, NULL, 99163.000, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);


-- 数据字典
INSERT INTO `linkappdb`.`sys_dict`(`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES (19, '隐患级别', 'trouble_level', '隐患级别', NULL, NULL, NULL, NULL);
INSERT INTO `linkappdb`.`sys_dict`(`id_`, `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES (21, '隐患状态', 'trouble_status', '隐患状态', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item`(`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (211, 19, '一般', '0', '隐患级别', 0, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (212, 19, '突出', '1', '隐患级别', 1, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (213, 19, '重大', '2', '隐患级别', 2, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (221, 21, '待整改', '0', '隐患状态', 0, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (222, 21, '待复查', '1', '隐患状态', 1, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (223, 21, '待销号', '2', '隐患状态', 2, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`(`id_`, `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (224, 21, '已销号', '3', '隐患状态', 3, 1, NULL, NULL, NULL, NULL, 0);

DROP TABLE IF EXISTS `rail_hidden_trouble`;
CREATE TABLE `rail_hidden_trouble`  (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                        `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目id',
                                        `trouble_no` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '隐患编号',
                                        `trouble_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '隐患类别',
                                        `trouble_level` int(11) NULL DEFAULT NULL COMMENT '隐患级别，来自字典',
                                        `duty_group` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '责任班组',
                                        `trouble_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '隐患内容',
                                        `trouble_status` int(11) NULL DEFAULT NULL COMMENT '隐患状态，来自字典',
                                        `rectify_require` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '整改要求',
                                        `trouble_files` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '现场图片或视频',
                                        `trouble_part` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '整改部位',
                                        `rectify_duration` int(11) NULL DEFAULT NULL COMMENT '整改时长',
                                        `rectify_deadline` date NULL DEFAULT NULL COMMENT '整改截止时间',
                                        `is_overdue` int(11) NULL DEFAULT NULL COMMENT '是否超期整改：0未超期，1已超期',
                                        `trouble_remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                        `create_uid` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
                                        `check_uid` bigint(20) NULL DEFAULT NULL COMMENT '检查人',
                                        `rectify_uid` bigint(20) NULL DEFAULT NULL COMMENT '整改人',
                                        `review_uid` bigint(20) NULL DEFAULT NULL COMMENT '复查人',
                                        `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                        `check_time` datetime(0) NULL DEFAULT NULL COMMENT '检查时间',
                                        `rectify_time` datetime(0) NULL DEFAULT NULL COMMENT '最后整改时间',
                                        `review_time` datetime(0) NULL DEFAULT NULL COMMENT '最后复查时间',
                                        `cancel_time` datetime(0) NULL DEFAULT NULL COMMENT '销号时间',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '隐患排查表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for rail_hidden_trouble_deal
-- ----------------------------
DROP TABLE IF EXISTS `rail_hidden_trouble_deal`;
CREATE TABLE `rail_hidden_trouble_deal`  (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                             `deal_round` int(11) NULL DEFAULT NULL COMMENT '处理轮次',
                                             `trouble_id` bigint(20) NULL DEFAULT NULL COMMENT '关联隐患id',
                                             `deal_type` int(11) NULL DEFAULT NULL COMMENT '处理类型：0整改，1复查',
                                             `deal_reply` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '处理回复',
                                             `deal_files` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '处理附件',
                                             `deal_uid` bigint(20) NULL DEFAULT NULL COMMENT '处理人',
                                             `deal_time` datetime(0) NULL DEFAULT NULL COMMENT '处理时间',
                                             `deal_result` int(11) NULL DEFAULT NULL COMMENT '处理结果：0未完成。1已完成',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '隐患排查处理表' ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;
