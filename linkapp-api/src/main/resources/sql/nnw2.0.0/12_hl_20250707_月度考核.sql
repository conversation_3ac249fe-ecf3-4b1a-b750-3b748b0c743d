-- 新增字典
INSERT INTO `linkappdb`.`sys_dict`( `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ('考核表状态', 'assess_status', '考核表状态', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item`( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (26, '待自查', '0', '待自查', 0, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (26, '签字中', '1', '待自查', 1, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (26, '已完成', '2', '待自查', 2, 1, NULL, NULL, NULL, NULL, 0);

INSERT INTO `linkappdb`.`sys_dict`( `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ( '考核表类型', 'assess_type', '考核表类型', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item`( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (27, '网格安全员考核', '1', '待自查', 1, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (27, '安监专务考核', '2', '待自查', 2, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (27, '施工队伍考核', '3', '待自查', 3, 1, NULL, NULL, NULL, NULL, 0);

INSERT INTO `linkappdb`.`sys_dict`( `dict_name_`, `dict_code_`, `description_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`) VALUES ( '考核结论', 'assess_result', '考核结论', NULL, NULL, NULL, NULL);

INSERT INTO `linkappdb`.`sys_dict_item`( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (28, '优秀', '0', '优秀', 0, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (28, '合格', '1', '待自查', 1, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `linkappdb`.`sys_dict_item`( `dict_id_`, `item_text_`, `item_value_`, `description_`, `sort_order_`, `status_`, `create_id_`, `create_time_`, `update_id_`, `update_time_`, `is_sync_`) VALUES (28, '不合格', '1', '待自查', 1, 1, NULL, NULL, NULL, NULL, 0);

-- 月度考核表
CREATE TABLE `rail_assess_table` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                     `tenant_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                                     `table_name` varchar(255) DEFAULT NULL COMMENT '表格名称',
                                     `assess_month` varchar(128) DEFAULT NULL COMMENT '考核年月',
                                     `assess_type` int(11) DEFAULT NULL COMMENT '考核类型，来自字典',
                                     `assess_user_id` bigint(20) DEFAULT NULL COMMENT '考核对象',
                                     `assess_group` varchar(128) DEFAULT NULL COMMENT '考核班组',
                                     `assess_status` int(11) DEFAULT NULL COMMENT '考核表状态，来自字典',
                                     `total_score` int(11) DEFAULT NULL COMMENT '总分',
                                     `actual_score` int(11) DEFAULT NULL COMMENT '总得分',
                                     `deduct_score` int(11) DEFAULT NULL COMMENT '扣分',
                                     `assess_result` int(11) DEFAULT NULL COMMENT '考核结果',
                                     `assess_group_ids` varchar(255) DEFAULT NULL COMMENT '考核组成员id',
                                     `assess_group_sign` varchar(512) DEFAULT NULL COMMENT '考核组成员签字',
                                     `assess_time` datetime DEFAULT NULL COMMENT '考核时间',
                                     `submit_sign_time` datetime DEFAULT NULL COMMENT '提交签字时间',
                                     `finish_time` datetime DEFAULT NULL COMMENT '考核完成时间',
                                     `create_uid` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='月度考核表';

-- 月度考核表记录
CREATE TABLE `rail_assess_table_record` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                            `table_id` bigint(20) DEFAULT NULL COMMENT '关联考核表id',
                                            `item_id` bigint(20) DEFAULT NULL COMMENT '关联考核项id',
                                            `record_actual_score` int(11) DEFAULT NULL COMMENT '单项得分',
                                            `record_deduct_score` int(11) DEFAULT NULL COMMENT '单项扣分',
                                            `record_remark` varchar(512) DEFAULT NULL COMMENT '备注',
                                            `create_uid` bigint(20) DEFAULT NULL COMMENT '填写人',
                                            `create_time` datetime DEFAULT NULL COMMENT '填写时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8 COMMENT='月底考核表详情';
