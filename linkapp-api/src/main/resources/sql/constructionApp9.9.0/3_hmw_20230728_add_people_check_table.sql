CREATE TABLE `app_people_check` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id_` varchar(32) DEFAULT NULL COMMENT '项目id',
  `check_time_` datetime DEFAULT NULL COMMENT '核查时间',
  `record_people_` varchar(32) DEFAULT NULL COMMENT '记录人',
  `check_in_` varchar(32) DEFAULT NULL COMMENT '实名制登记 0-离场 1-在场',
  `safe_edu_` varchar(32) DEFAULT NULL COMMENT '安全教育 0-未进行 1-已进行',
  `age_` varchar(32) DEFAULT NULL COMMENT '人员年龄 0-未超龄 1-超龄',
  `attendance_` varchar(32) DEFAULT NULL COMMENT '人员出勤 0-未连续未出勤 1-连续未出勤',
  `deal_require_` int(2) DEFAULT NULL COMMENT '处理要求：0-信息补录 1-清退场 2-无需处理',
  `status_` int(2) DEFAULT NULL COMMENT '状态：0-待处理 1-已处理',
  `deal_name_` varchar(32) DEFAULT NULL COMMENT '处理人名称',
  `deal_time_` datetime DEFAULT NULL COMMENT '处理日期',
  `people_name_` varchar(32) DEFAULT NULL COMMENT '核查人员姓名',
  `people_company_` varchar(32) DEFAULT NULL COMMENT '核查人参建单位',
  `people_group_` varchar(32) DEFAULT NULL COMMENT '核查人班组',
  `people_work_type_` varchar(32) DEFAULT NULL COMMENT '核查人工种',
  `card_` varchar(32) DEFAULT NULL COMMENT '身份证号',
  `telephone_` varchar(32) DEFAULT NULL COMMENT '联系电话',
  `recent_work_time_` datetime DEFAULT NULL COMMENT '最近考勤日期',
  `deal_result_` varchar(255) DEFAULT NULL COMMENT '处理结果',
  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
  `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `people_status_` int(2) DEFAULT NULL COMMENT '在岗状态: 0-退场 1-进场',
  `update_control_id_` varchar(255) DEFAULT NULL COMMENT '并发控制id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人员核查表';