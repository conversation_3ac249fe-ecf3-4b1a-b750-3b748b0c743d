CREATE TABLE `app_party_build_info`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`     varchar(100)  DEFAULT NULL COMMENT '租户id',
    `title_name`    varchar(300)  DEFAULT NULL COMMENT '标题',
    `resume`        varchar(200)  DEFAULT NULL COMMENT '简述',
    `index_no`      int(11) DEFAULT NULL COMMENT '排序号',
    `content`       longtext COMMENT '内容',
    `data_type`     int(1) DEFAULT NULL COMMENT '数据类型，1重要党课，2重要讲话，3党建视频，4党建活动，5党员风采，6政治面貌构成情况',
    `page_img`      varchar(500)  DEFAULT NULL COMMENT '封面照片',
    `activity_time` datetime      DEFAULT NULL COMMENT '活动时间',
    `imgs`          varchar(4000) DEFAULT NULL COMMENT '图片集，多张图片以，隔开',
    `create_time`   datetime      DEFAULT NULL COMMENT '创建时间',
    `creator`       varchar(100)  DEFAULT NULL COMMENT '创建人',
    `modifier`      varchar(100)  DEFAULT NULL COMMENT '修改人',
    `modify_time`   datetime      DEFAULT NULL COMMENT '修改时间',
    `delete_state`  int(1) DEFAULT NULL COMMENT '是否删除，0是，1否',
    PRIMARY KEY (`id`),
    KEY             `app_party_build_info_data_type_IDX` (`data_type`) USING BTREE,
    KEY             `app_party_build_info_create_time_IDX` (`create_time`) USING BTREE,
    KEY             `app_party_build_info_tenant_id_IDX` (`tenant_id`) USING BTREE
) ENGINE=InnoDB COMMENT='党建信息';

-- 初始化数据
INSERT INTO linkappdb.app_party_build_info
(id, tenant_id, title_name, resume, index_no, content, data_type, page_img, activity_time, imgs, create_time, creator,
 modifier, modify_time, delete_state)
VALUES (1, NULL, '中共党员', NULL, 1, '0', 6, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO linkappdb.app_party_build_info
(id, tenant_id, title_name, resume, index_no, content, data_type, page_img, activity_time, imgs, create_time, creator,
 modifier, modify_time, delete_state)
VALUES (2, NULL, '中共预备党员', NULL, 2, '0', 6, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO linkappdb.app_party_build_info
(id, tenant_id, title_name, resume, index_no, content, data_type, page_img, activity_time, imgs, create_time, creator,
 modifier, modify_time, delete_state)
VALUES (3, NULL, ' 入党积极分子', NULL, 3, '0', 6, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO linkappdb.app_party_build_info
(id, tenant_id, title_name, resume, index_no, content, data_type, page_img, activity_time, imgs, create_time, creator,
 modifier, modify_time, delete_state)
VALUES (4, NULL, '共青团员', NULL, 4, '0', 6, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO linkappdb.app_party_build_info
(id, tenant_id, title_name, resume, index_no, content, data_type, page_img, activity_time, imgs, create_time, creator,
 modifier, modify_time, delete_state)
VALUES (5, NULL, '民主党派人士', NULL, 5, '0', 6, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO linkappdb.app_party_build_info
(id, tenant_id, title_name, resume, index_no, content, data_type, page_img, activity_time, imgs, create_time, creator,
 modifier, modify_time, delete_state)
VALUES (6, NULL, '无党派人士', NULL, 6, '0', 6, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO linkappdb.app_party_build_info
(id, tenant_id, title_name, resume, index_no, content, data_type, page_img, activity_time, imgs, create_time, creator,
 modifier, modify_time, delete_state)
VALUES (7, NULL, '群众', NULL, 7, '0', 6, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
