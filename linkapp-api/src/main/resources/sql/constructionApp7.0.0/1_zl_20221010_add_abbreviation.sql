-- 新增参建单位单位简称字段
ALTER TABLE app_labor_company ADD abbreviation_ varchar(10) NULL COMMENT '单位简称' after type_;

INSERT INTO app_config (key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time) VALUES('LABOUR_BIG_SCREEN_LABOUR_COMPANY_TYPE', NULL, '["1","2","3","4","6","10","11"]', '["1","2","3","4","6","10","11"]', '劳务管理:劳务大屏', '劳务大屏中需要显示的哪些类型的参建单位', '2022-10-10 19:09:00', '2022-10-10 19:09:00');
INSERT INTO app_config (key_, tenant_id, value_, example, module_level, describe_, create_time, modify_time) VALUES('SHOW_ATTR_TYPE_NAMES', null, '电气火灾监测器', '电气火灾监测器', '大屏需要显示属性的设备类型', '大屏需要显示属性的设备类型', now(), now());