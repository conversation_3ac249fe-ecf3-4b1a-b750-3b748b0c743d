-- ------------------------
-- 闸机表增加计入考勤字段
-- ------------------------
ALTER TABLE app_gate ADD `is_record_` tinyint(1) DEFAULT NULL COMMENT '计入考勤：0-否 1-是';
-- ------------------------
-- 用户项目表增加最近考勤时间字段
-- ------------------------
ALTER TABLE app_user_project ADD `clock_time_` datetime DEFAULT NULL COMMENT '最近考勤时间';
-- ------------------------
-- 考勤记录表增加计入考勤字段
-- ------------------------
ALTER TABLE app_user_clock ADD `is_record_` tinyint(1) DEFAULT NULL COMMENT '计入考勤：0-否 1-是';
-- ------------------------
-- 刷数据
-- ------------------------
-- UPDATE app_user_clock SET is_record_ = 1;
UPDATE app_gate SET is_record_ = 1 WHERE service_area IN (1, 2);
UPDATE app_gate SET is_record_ = 0 WHERE is_record_ IS NULL;
