-- ----------------------------
-- 新增菜单
-- ----------------------------
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('1602', '160', '实际进度', 'scheduleAct', NULL, 2, NULL, NULL, 160.02, 1, 'manage/scheduleAct', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('1603', '160', '进度预警', 'scheduleWarning', NULL, 2, NULL, NULL, 160.03, 1,'manage/scheduleWarning', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
-- INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
-- VALUES('1604', '160', '形象进度', 'scheduleImage', NULL, 2, NULL, NULL, 160.04, 1, 'manage/scheduleImage', NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
-- INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
-- VALUES('160401', '1604', '统计设置', 'scheduleImage:set', NULL, 3, NULL, NULL, 1604.01, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
-- INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
-- VALUES('160402', '1604', '编辑', 'scheduleImage:edit', NULL, 3, NULL, NULL, 1604.02, 2, NULL, NULL, '1', now(), NULL, NULL, NULL, NULL, 0);
-- -- ----------------------------
-- -- 进度管理-形象进度设置表
-- -- ----------------------------
-- CREATE TABLE `app_progress_image_set`
-- (
--     `id_`            int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
--     `tenant_id_`     varchar(32)   DEFAULT NULL COMMENT '租户id',
--     `code_`          varchar(32)   DEFAULT NULL COMMENT '选项编码',
--     `is_show_`       int(2)        DEFAULT NULL COMMENT '是否显示(1显示,0不显示)',
--     `compute_type_`  int(2)        DEFAULT NULL COMMENT '计算方式(1按单体,2按楼层)',
--     `creator_id_`    bigint(20)    DEFAULT NULL COMMENT '创建人id',
--     `create_time_`   datetime      DEFAULT NULL COMMENT '创建日期',
--     `modify_id_`     bigint(20)    DEFAULT NULL COMMENT '修改人id',
--     `modify_time_`   datetime      DEFAULT NULL COMMENT '修改时间',
--     `remark_`        text          COMMENT  '备注/说明',
--     PRIMARY KEY (`id_`) USING BTREE
-- ) COMMENT ='进度管理-形象进度设置表';
--
-- -- ----------------------------
-- -- 进度管理-形象进度表
-- -- ----------------------------
-- CREATE TABLE `app_progress_image`
-- (
--     `id_`                  int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键',
--     `tenant_id_`           varchar(32)   DEFAULT NULL COMMENT '租户id',
--     `code_`                varchar(32)   DEFAULT NULL COMMENT '选项编码',
--     `type_`                int(2)        DEFAULT NULL COMMENT '部位类型(1单体,2楼层)',
--     `building_floor_id_`   int(11)       DEFAULT NULL COMMENT '单体楼层id',
--     `progress_`            double(10,2)  DEFAULT '0'  COMMENT '进度',
--     `creator_id_`          bigint(20)    DEFAULT NULL COMMENT '创建人id',
--     `create_time_`         datetime      DEFAULT NULL COMMENT '创建日期',
--     `modify_id_`           bigint(20)    DEFAULT NULL COMMENT '修改人id',
--     `modify_time_`         datetime      DEFAULT NULL COMMENT '修改时间',
--     `remark_`              text          COMMENT  '备注/说明',
--     PRIMARY KEY (`id_`) USING BTREE
-- ) COMMENT ='材进度管理-形象进度表';