CREATE TABLE `app_progress_info`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
    `tenant_id_`   varchar(100) DEFAULT NULL COMMENT '租户id',
    `name_`        varchar(200) DEFAULT NULL COMMENT '计划任务名称',
    `code_`        varchar(100) DEFAULT NULL COMMENT '编码',
    `start_time_`  datetime     DEFAULT NULL COMMENT '开始时间',
    `end_time_`    datetime     DEFAULT NULL COMMENT '结束时间',
    `total_work_`  int(11)      DEFAULT NULL COMMENT '工期',
    `type_`        int(11)      DEFAULT NULL COMMENT '类型，1计划，2任务',
    `plan_type_`   int(11)      DEFAULT NULL COMMENT '计划类型，1总计划，2期间计划，3月计划，4周计划',
    `before_work_` varchar(200) DEFAULT NULL COMMENT '前置任务',
    `point_`       int(11)      DEFAULT NULL COMMENT '关键节点，1是，2否',
    `parent_id_`   bigint(20)   DEFAULT NULL COMMENT '父级id',
    `parent_ids_`  text COMMENT '父级ids',
    `creator_`     varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time_` datetime     DEFAULT NULL COMMENT '创建时间',
    `modifier_`    varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time_` datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`     int(11)      DEFAULT '0' COMMENT '是否删除，0否，1是',
    PRIMARY KEY (`id_`),
    KEY `app_progress_info_parent_id__IDX` (`parent_id_`) USING BTREE
) ENGINE = InnoDB COMMENT ='进度计划信息';

CREATE TABLE `app_progress_integrate_ref`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
    `resource_id_` varchar(100) DEFAULT NULL COMMENT '模型id',
    `progress_id_` bigint(20)   DEFAULT NULL COMMENT '进度任务id',
    `creator_`     varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time_` datetime     DEFAULT NULL COMMENT '创建时间',
    `modifier_`    varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time_` datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`     int(11)      DEFAULT '0' COMMENT '是否删除，0否，1是',
    PRIMARY KEY (`id_`),
    KEY `app_progress_integrate_ref_resource_id__IDX` (`resource_id_`) USING BTREE,
    KEY `app_progress_integrate_ref_progress_id__IDX` (`progress_id_`) USING BTREE
) ENGINE = InnoDB COMMENT ='进度模型关联关系';