-- -- 菜单
-- INSERT INTO linkapp_privilege
-- (id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
-- VALUES('8902', '89', '模型设备设置', 'bimScreenSetting:bimDeviceSetting', '模型设备设置', 3, NULL, NULL, 890.20, 2, '', NULL, '1', '2023-11-08 10:39:19', NULL, NULL, NULL, NULL, 0);
--
-- -- 模型设备打点建表
-- CREATE TABLE `app_device_bim_point`
-- (
--     `id_`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
--     `device_code_`       varchar(100) DEFAULT NULL COMMENT '设备编码',
--     `tenant_id_`         varchar(100) DEFAULT NULL COMMENT '租户id',
--     `point_x_`           double       DEFAULT NULL COMMENT 'bim上点位x坐标',
--     `point_y_`           double       DEFAULT NULL COMMENT 'bim上点位y坐标',
--     `point_z_`           double       DEFAULT NULL COMMENT 'bim上点位z坐标',
--     `resource_id_`       varchar(200) DEFAULT NULL COMMENT 'bim资源id，可空，设备打点不一定在bim构建上',
--     `group_resource_id_` varchar(200) DEFAULT NULL COMMENT '分组资源id，跟随对应的资源显影',
--     `creator_`           varchar(100) DEFAULT NULL COMMENT '创建人',
--     `create_time_`       datetime     DEFAULT NULL COMMENT '创建时间',
--     `modifier_`          varchar(100) DEFAULT NULL COMMENT '修改人',
--     `modify_time_`       datetime     DEFAULT NULL COMMENT '修改时间',
--     `deleted_`           int(11)      DEFAULT '0' COMMENT '是否删除，0否，1是',
--     PRIMARY KEY (`id_`),
--     KEY `app_device_bim_point_device_code__IDX` (`device_code_`) USING BTREE,
--     KEY `app_device_bim_point_tenant_id__IDX` (`tenant_id_`) USING BTREE,
--     KEY `app_device_bim_point_resource_id__IDX` (`resource_id_`) USING BTREE,
--     KEY `app_device_bim_point_group_resource_id__IDX` (`group_resource_id_`) USING BTREE
-- ) ENGINE = InnoDB COMMENT ='设备bim点位数据';
--
