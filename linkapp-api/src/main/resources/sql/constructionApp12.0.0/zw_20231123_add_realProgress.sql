-- 菜单权限
INSERT INTO linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('160201', '1602', '填报', 'scheduleAct:addDetail', NULL, 3, NULL, NULL, 160201.00, 2, NULL, NULL, '1', '2023-11-08 13:36:01', NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('160202', '1602', '删除', 'scheduleAct:del', NULL, 3, NULL, NULL, 160202.00, 2, NULL, NULL, '1', '2023-11-08 13:36:01', NULL, NULL, NULL, NULL, 0);

-- 建表
CREATE TABLE `app_progress_real_detail`
(
    `id_`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
    `progress_task_id_` bigint(20)    DEFAULT NULL COMMENT '计划进度任务id',
    `real_start_time_`  datetime      DEFAULT NULL COMMENT '实际开始时间',
    `real_end_time_`    datetime      DEFAULT NULL COMMENT '实际结束时间',
    `percentage`        double        DEFAULT NULL COMMENT '进度百分比',
    `build_describe_`   varchar(500)  DEFAULT NULL COMMENT '施工说明',
    `imgs_`             varchar(2000) DEFAULT NULL COMMENT '现场照片',
    `creator_`          varchar(100)  DEFAULT NULL COMMENT '创建人',
    `create_time_`      datetime      DEFAULT NULL COMMENT '创建时间',
    `modifier_`         varchar(100)  DEFAULT NULL COMMENT '修改人',
    `modify_time_`      datetime      DEFAULT NULL COMMENT '修改时间',
    `deleted_`          int(11)       DEFAULT '0' COMMENT '是否删除，0否，1是',
    PRIMARY KEY (`id_`),
    KEY `app_progress_real_detail_progress_task_id__IDX` (`progress_task_id_`) USING BTREE
) ENGINE = InnoDB COMMENT ='实际进度详情';