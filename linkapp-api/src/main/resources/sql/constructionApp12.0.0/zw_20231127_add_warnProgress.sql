-- 菜单权限
INSERT INTO linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('160301', '1603', '判断设置', 'scheduleWarning:judgeSet', NULL, 3, NULL, NULL, 160301.00, 2, NULL, NULL, '1', '2023-11-08 13:36:01', NULL, NULL, NULL, NULL, 0);
INSERT INTO linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('160302', '1603', '滞后说明', 'scheduleWarning:lagReason', NULL, 3, NULL, NULL, 160302.00, 2, NULL, NULL, '1', '2023-11-08 13:36:01', NULL, NULL, NULL, NULL, 0);

-- 建表
CREATE TABLE `app_progress_warn_config`
(
    `id_`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
    `tenant_id_`          varchar(100) DEFAULT NULL COMMENT '租户id',
    `finish_less_`        int(11)      DEFAULT NULL COMMENT '完成时下限',
    `finish_more_`        int(11)      DEFAULT NULL COMMENT '完成时上限',
    `undone_less_`        int(11)      DEFAULT NULL COMMENT '未完成时下限',
    `undone_more_`        int(11)      DEFAULT NULL COMMENT '未完成时上限',
    `parent_finish_less_` int(11)      DEFAULT NULL COMMENT '父级完成时下限',
    `parent_finish_more_` int(11)      DEFAULT NULL COMMENT '父级完成时上限',
    `parent_undone_less_` int(11)      DEFAULT NULL COMMENT '父级未完成时下限',
    `parent_undone_more_` int(11)      DEFAULT NULL COMMENT '父级未完成时上限',
    `creator_`            varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time_`        datetime     DEFAULT NULL COMMENT '创建时间',
    `modifier_`           varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time_`        datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`            int(11)      DEFAULT '0' COMMENT '是否删除，0否，1是',
    PRIMARY KEY (`id_`),
    KEY `app_progress_warn_config_tenant_id__IDX` (`tenant_id_`) USING BTREE
) ENGINE = InnoDB COMMENT ='进度预警设置';

CREATE TABLE `app_progress_warn`
(
    `id_`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
    `progress_id_`     bigint(20)   DEFAULT NULL COMMENT '进度计划、任务id',
    `real_start_time_` datetime     DEFAULT NULL COMMENT '实际开始时间',
    `real_end_time_`   datetime     DEFAULT NULL COMMENT '实际结束时间',
    `real_percentage_` double       DEFAULT NULL COMMENT '真实进度百分比',
    `warn_state_`      int(11)      DEFAULT NULL COMMENT '预警状态，1正常，2滞后',
    `plan_work_day_`   int(11)      DEFAULT NULL COMMENT '计划工期（冗余）',
    `start_state_`     int(11)      DEFAULT NULL COMMENT '开始状态，1未开始，2应开始未开始，3进行中，4已完成',
    `creator_`         varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time_`     datetime     DEFAULT NULL COMMENT '创建时间',
    `modifier_`        varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time_`     datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`         int(11)      DEFAULT '0' COMMENT '是否删除，0否，1是',
    PRIMARY KEY (`id_`),
    KEY `app_progress_warn_progress_id__IDX` (`progress_id_`) USING BTREE
) ENGINE = InnoDB COMMENT ='进度预警';

CREATE TABLE `app_progress_lag_info`
(
    `id_`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
    `progress_id_`  bigint(20)   DEFAULT NULL COMMENT '进度计划、任务id',
    `reason_state_` int(11)      DEFAULT NULL COMMENT '滞后原因类型，1天气原因',
    `memo_`         varchar(500) DEFAULT NULL COMMENT '说明备注',
    `creator_`      varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time_`  datetime     DEFAULT NULL COMMENT '创建时间',
    `modifier_`     varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time_`  datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`      int(11)      DEFAULT '0' COMMENT '是否删除，0否，1是',
    PRIMARY KEY (`id_`),
    KEY `app_progress_lag_info_progress_id__IDX` (`progress_id_`) USING BTREE
) ENGINE = InnoDB COMMENT ='进度滞后信息';