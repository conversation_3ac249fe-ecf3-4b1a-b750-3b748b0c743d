CREATE TABLE `system_docking` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `sys_name` varchar(32) NOT NULL COMMENT '对接的系统名称',
  `appid` varchar(64) DEFAULT NULL COMMENT 'appid',
  `secret` varchar(64) DEFAULT NULL COMMENT 'appsecret',
  `tenant_id` varchar(64) DEFAULT NULL,
  `createor` varchar(64) DEFAULT NULL,
  `modifier` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `modify_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8mb3 COMMENT='系统对接配置';

CREATE TABLE `system_dock_device` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(64) DEFAULT NULL COMMENT '设备id',
  `type` int DEFAULT NULL COMMENT '设备类型 0：扬尘设备，1：塔机设备，2：升降机设备，3：视频设备',
  `link_id` bigint DEFAULT NULL COMMENT '关联的系统对接记录id',
  `is_sync` int DEFAULT NULL COMMENT '是否同步，0：不同步，1：同步',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=391 DEFAULT CHARSET=utf8mb3 COMMENT='系统对接关联设备表';



INSERT INTO `jiangong192`.`linkapp_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `tenant_id_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`) VALUES ('88', '8', '系统对接', 'systemDock', '系统对接', '2', NULL, NULL, '15.50', '1', 'manage/systemDock', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0');
