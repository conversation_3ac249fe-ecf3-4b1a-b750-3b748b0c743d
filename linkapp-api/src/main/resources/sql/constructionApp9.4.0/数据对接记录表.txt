CREATE TABLE `system_dock_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `sysname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '对接的系统名称',
  `url` varchar(255) DEFAULT NULL COMMENT '请求接口路径',
  `data` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '请求的接口数据',
  `code` varchar(255) DEFAULT NULL COMMENT '接口返回',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb3 COMMENT='系统对接记录';

