CREATE TABLE `app_construction_album`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`    varchar(100)  DEFAULT NULL COMMENT '租户id',
    `data_type`    int(1) DEFAULT NULL COMMENT '类型，1相册，2事件、3照片',
    `name`         varchar(50)   DEFAULT NULL COMMENT '名称',
    `code`         varchar(50)   DEFAULT NULL COMMENT '编码',
    `parent_id`    bigint(20) DEFAULT NULL COMMENT '父级id',
    `parent_ids`   varchar(4000) DEFAULT NULL COMMENT '父级ids，以，隔开',
    `source_url`   varchar(400)  DEFAULT NULL COMMENT '资源url',
    `remark`       varchar(300)  DEFAULT NULL COMMENT '描述',
    `creator`      varchar(100)  DEFAULT NULL COMMENT '创建人',
    `modifier`     varchar(100)  DEFAULT NULL COMMENT '修改人',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime      DEFAULT NULL COMMENT '修改时间',
    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
    PRIMARY KEY (`id`),
    KEY            `app_construction_album_tenant_id_IDX` (`tenant_id`) USING BTREE,
    KEY            `app_construction_album_data_type_IDX` (`data_type`) USING BTREE,
    KEY            `app_construction_album_parent_id_IDX` (`parent_id`) USING BTREE,
    KEY            `app_construction_album_name_IDX` (`name`) USING BTREE,
    KEY            `app_construction_album_create_time_IDX` (`create_time`) USING BTREE
) ENGINE=InnoDB COMMENT='施工相册表';

-- app菜单权限
INSERT INTO linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_,
 tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES ('660008', '660000', '施工相册', 'appConstructionAlbumManagement', '施工相册', 1, NULL, NULL, 660008.00, 1, NULL,
        NULL, '1', '2022-11-10 09:00:00', NULL, NULL, NULL, NULL, 1);