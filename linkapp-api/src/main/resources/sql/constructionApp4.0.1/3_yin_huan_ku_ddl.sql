# 隐患库字段
# id	createTime	remark	dangerTypeId	fullId	fullName	code	order	level	changeLimit	deleteStatus
#     recordStatus	points	fine	pushPeriod	lastUpdateTime	related	standardPicVOList	identify
CREATE TABLE `app_danger`
(
    `id`             int(11) primary key AUTO_INCREMENT COMMENT '主键',
    `the_id`         int(11)      NOT NULL COMMENT '原id有重复',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `tenant_id`      varchar(32)  DEFAULT NULL COMMENT '租户id',
    `remark`         varchar(200) DEFAULT NULL COMMENT '备注',
    `danger_type_id` int(11)      not null COMMENT '隐患类型id',
    `full_id`        varchar(64)  not null COMMENT 'full',
    `full_name`      varchar(128) not null COMMENT '全名',
    `content`        varchar(128) not null COMMENT '内容',
    `code`           varchar(16)  not null COMMENT '',
    `order_`         int(2)       not null COMMENT '排序',
    `level`          int(2)       not null COMMENT '等级',
    `change_limit`   int(2)       not null COMMENT '',
    `delete_status`  int(2)       not null COMMENT '删除状态 0-已删除，1-存在',
    `record_status`  int(2)       not null COMMENT '状态 0-正常',
    `points`         int(2)       DEFAULT NULL COMMENT '',
    `fine`           int(2)       DEFAULT NULL COMMENT '',
    `push_period`    int(2)       DEFAULT NULL COMMENT '',
    `related`        int(2)       DEFAULT NULL COMMENT '是否关联规范 0-未关联',
    `identify`       varchar(32)  DEFAULT NULL COMMENT ''
) ENGINE = InnoDB comment '隐患库';

# 隐患类型
CREATE TABLE `app_danger_type`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `parent_id`      int(11)      NOT NULL COMMENT '父id',
    `danger_type_id` int(11)      NOT NULL COMMENT '隐患类型id',
    `full_id`        varchar(64)  NOT NULL COMMENT '原全称id',
    `full_name`      varchar(128) NOT NULL COMMENT '全名',
    `name`           varchar(128) NOT NULL COMMENT '名称',
    `code`           varchar(16)  NOT NULL COMMENT '编码',
    `level`          int(2)       NOT NULL COMMENT '层级',
    PRIMARY KEY (`id`),
    UNIQUE KEY `app_danger_type_un` (`danger_type_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='隐患类型';

# 隐患库菜单
INSERT INTO linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
                               search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
                               modifier_, modify_time_, icon_name, flag_)
VALUES ('1004', '1000', '隐患库', 'dangerLib', NULL, 2, NULL, NULL, 10.11, 1, 'manage/dangerLib', NULL, '1',
        '2022-07-23 09:49:09', NULL, NULL, NULL, NULL, 0);

# 给租户分配菜单权限
insert into linkapp_tenant_ref_privilege
    (id, tenant_id, privilege_id)
values ((select replace(uuid(), "-", "")), '405cd2c8633ecbee1c5f61c4a87c9019', '1004'),
       ((select replace(uuid(), "-", "")), '1', '1004');