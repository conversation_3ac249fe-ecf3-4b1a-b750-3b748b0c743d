-- 预警处理 新增字段
alter table `app_user_warning` add column `handle_time_` datetime DEFAULT NULL COMMENT '处理时间' after `status_`,
add `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注' after `handle_time_`,
add `warning_rule_` varchar(256) DEFAULT NULL COMMENT '预警规则' after `warning_time_`,
add `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人' after `handle_remark_`;

alter table `app_group_warning` add column `handle_time_` datetime DEFAULT NULL COMMENT '处理时间' after `status_`,
add `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注' after `handle_time_`,
add `warning_rule_` varchar(256) DEFAULT NULL COMMENT '预警规则' after `warning_time_`,
add `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人' after `handle_remark_`;

alter table `app_age_warning` add column `handle_time_` datetime DEFAULT NULL COMMENT '处理时间' after `status_`,
add `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注' after `handle_time_`,
add `warning_rule_` varchar(256) DEFAULT NULL COMMENT '预警详情' after `warning_time_`,
add `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人' after `handle_remark_`;

alter table `app_certificate_warning` add column `handle_time_` datetime DEFAULT NULL COMMENT '处理时间' after `status_`,
add `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注' after `handle_time_`,
add `warning_rule_` varchar(256) DEFAULT NULL COMMENT '预警规则' after `warning_time_`,
add `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人' after `handle_remark_`;

-- 初始化数据
update `app_user_warning` set `warning_rule_` = '连续10天未出勤预警' where type_ = 1;
update `app_user_warning` set `warning_rule_` = '连续14天未出勤自动退场' where type_ = 2;
update `app_age_warning` set `warning_rule_` = '人员超龄';
update `app_group_warning` set `warning_rule_` = '出勤率低于50%预警';
update `app_certificate_warning` set `warning_rule_` = '证书已过期' where residue_day_ = 0;
update `app_certificate_warning` set `warning_rule_` = '证书即将过期' where residue_day_ > 0;