# 巡检管理 菜单挪位置，删除原来的菜单
# delete from linkapp_privilege  where name_ IN ('巡检管理') or parent_id_  in (80);
# select * from linkapp_privilege  where id_ in (80,801,802,803);
delete
from linkapp_privilege
where id_ in (80, 801, 802, 803);

# 备份
# INSERT INTO linkappdb.linkapp_privilege (id_, parent_id_, name_, privilege_code_, description_, level_, target,
#                                          search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_,
#                                          modifier_, modify_time_, icon_name, flag_)
# VALUES ('80', NULL, '巡检管理', 'inspection', NULL, 1, NULL, NULL, 3.60, 1, 'manage/inspection', NULL, '1',
#         '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0),
#        ('801', '80', '配电箱管理', 'electricBox', NULL, 2, NULL, NULL, 3.51, 1, 'manage/electricBox', NULL, '1',
#         '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0),
#        ('802', '80', '巡检统计报告', 'electricStatistics', NULL, 2, NULL, NULL, 3.52, 1, 'manage/electricStatistics', NULL,
#         '1', '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0),
#        ('803', '80', '巡检项管理', 'electricOption', NULL, 2, NULL, NULL, 3.53, 1, 'manage/electricOption', NULL, '1',
#         '2022-04-11 13:49:03', NULL, NULL, NULL, NULL, 0);