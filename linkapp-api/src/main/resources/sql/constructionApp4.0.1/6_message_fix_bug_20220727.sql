# 消息中心详情
CREATE TABLE `app_message_center_detail`
(
    `id`                int(11) primary key AUTO_INCREMENT COMMENT '主键',
    `device_code`       varchar(32)  not null COMMENT '设备编号',
    `device_type_name`  varchar(32)  default null COMMENT '设备类型名称',
    `position`          varchar(100) default null COMMENT '所在位置',
    `lead_id`           varchar(32)  not null COMMENT '负责人id',
    `tenant_id`         varchar(32)  not null COMMENT '租户id',
    `lead_name`         varchar(20)  default null COMMENT '负责人名称',
    `telephone`         varchar(20)  default null COMMENT '联系方式',
    `message_center_id` int(11)      not null COMMENT '消息中心id'
) ENGINE = InnoDB comment '消息中心详情';