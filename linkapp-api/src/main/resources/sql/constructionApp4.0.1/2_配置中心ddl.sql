# drop table app_config ;
CREATE TABLE `app_config`
(
    `id`           int(11) primary key AUTO_INCREMENT COMMENT '主键',
    `key_`         varchar(100) not NULL COMMENT '配置关键字',
    `tenant_id`    varchar(32) DEFAULT NULL COMMENT '租户ID',
    `value_`       text         not NULL COMMENT '配置的值',
    `example`      text         not NULL COMMENT '样例',
    `module_level` varchar(200) not NULL COMMENT '模块层级',
    `describe_`     varchar(500) not NULL COMMENT '描述',
    `create_time`  datetime    DEFAULT NULL COMMENT '创建时间',
    `modify_time`  datetime    DEFAULT NULL COMMENT '修改时间',
    unique `uni_key_tenant_id` (`key_`, `tenant_id`) comment '关键字在租户下唯一'
) ENGINE = InnoDB comment '配置中心';


# [
#     {
#         "key": "IAQI_STATISTICS_DEVICE_CODES",
#         // key,固定，不可修改,语义化，体现用途，初始化脚本设定，页面不可修改
#         "value": "[{\"tenantId\":\"405cd2c8633ecbee1c5f61c4a87c9019\",\"attrs\":[\"pm10\",\"pm25\"],\"deviceCodes\":[\"40194860\"]}]",
#         "tenantId": "abc124645acbd124",
#         //值，可以是设备编号列表，型号code,其他。类型可以是基本类型、字符串、json
#         "describe": "空气质量IAQI指数分类统计天数功能，设定需要参与计算的设备",
#         //描述,此配置的用途 ，体现是哪个模块哪个业务功能
#         "example": "[{\"tenantId\":\"405cd2c8633ecbee1c5f61c4a87c9019\",\"attrs\":[\"pm10\",\"pm25\"],\"deviceCodes\":[\"40194860\"]}]",
#         //样例 ，初始化脚本设定，页面不可修改
#         "moduleLevel": "环境管理:空气污染"
#         //模块层级，所属的功能模块的层级
#     }
# ]