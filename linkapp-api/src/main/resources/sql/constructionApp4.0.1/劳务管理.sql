ALTER TABLE app_user_record add `record_type_` int(10) DEFAULT 1 COMMENT '1全勤 2半勤 0缺勤';
ALTER TABLE app_user_record add `group_id_` varchar(32) DEFAULT NULL COMMENT '班组';
ALTER TABLE app_user_record add `company_project_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司与项目中间表id';
ALTER TABLE app_user_warning add `status_` int(10) DEFAULT 0 COMMENT '处理状态(1已处理 0未处理)';
ALTER TABLE app_group_warning add `status_` int(10) DEFAULT 0 COMMENT '处理状态(1已处理 0未处理)';
ALTER TABLE app_certificate_warning add `status_` int(10) DEFAULT 0 COMMENT '处理状态(1已处理 0未处理)';
ALTER TABLE app_age_warning add `status_` int(10) DEFAULT 0 COMMENT '处理状态(1已处理 0未处理)';
ALTER TABLE app_user_statistics add `group_id_` varchar(32) DEFAULT NULL COMMENT '班组';

ALTER TABLE app_user_clock add `group_id_` varchar(32) DEFAULT NULL COMMENT '班组';
ALTER TABLE app_user_clock add `company_project_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司与项目中间表id';
ALTER TABLE app_user_gate add `group_id_` varchar(32) DEFAULT NULL COMMENT '班组';
ALTER TABLE app_user_gate add `company_project_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司与项目中间表id';

-- 添加闸机保存的id
ALTER TABLE emp_user_base add `gate_id_` varchar(32) DEFAULT NULL COMMENT '下发到闸机时保存的人员信息数据id';
-- 刷新之前闸机的数据适配
UPDATE app_user_project a
LEFT JOIN emp_user_base b ON a.user_id_ = b.id
SET b.gate_id_ = b.card_
WHERE
	a.gate_status_ = 1;