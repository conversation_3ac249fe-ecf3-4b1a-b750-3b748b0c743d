CREATE TABLE `app_hidden_danger` (
  `id` varchar(32) NOT NULL COMMENT 'id',
  `hidden_danger_id` varchar(32) DEFAULT NULL COMMENT '隐患库隐患id',
  `is_scene_rectify` int(2) DEFAULT NULL COMMENT '是否已现场整改 0：否，1是',
  `supplement_remarks` varchar(255) DEFAULT NULL COMMENT '补充说明',
  `rectify_requirements` varchar(255) DEFAULT NULL COMMENT '整改要求',
  `link_unit_id` varchar(32) DEFAULT NULL COMMENT '参建单位id',
  `rectify_uid` bigint(11) DEFAULT NULL COMMENT '整改人id',
  `rectify_end_time` datetime DEFAULT NULL COMMENT '整改时限',
  `check_uid` bigint(32) DEFAULT NULL COMMENT '复查人id',
  `scene_photo` varchar(1024) DEFAULT NULL COMMENT '现场图片，多个图片名称以逗号拼接',
  `is_overdue` int(2) DEFAULT NULL COMMENT '是否超期。0：否，1：是 （弃用）',
  `status` int(2) DEFAULT NULL COMMENT '状态：0：待整改，1：待复查，2：合格',
  `close_time` datetime DEFAULT NULL COMMENT '复查通过时间',
  `create_uid` bigint(32) DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`),
  KEY `hidden_danger_id` (`hidden_danger_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='隐患记录表';

#不缺失此字段忽略
ALTER TABLE `app_hidden_danger`
ADD COLUMN `tenant_id`  varchar(32) NULL COMMENT '租户id' AFTER `create_time`;


CREATE TABLE `app_hidden_danger_info` (
  `id` varchar(32) NOT NULL COMMENT '记录id',
  `check_account_id` varchar(32) NOT NULL COMMENT '隐患记录id',
  `rectify_result` int(2) DEFAULT NULL COMMENT '整改结果，0：未整改，1：已整改',
  `rectify_photo` varchar(1024) DEFAULT NULL COMMENT '整改现场图片',
  `rectify_remark` varchar(140) DEFAULT NULL COMMENT '整改说明',
  `rectify_time` datetime DEFAULT NULL COMMENT '整改时间',
  `check_result` int(2) DEFAULT NULL COMMENT '整改结果，0：不合格，1：合格',
  `check_remark` varchar(140) DEFAULT NULL COMMENT '复查说明',
  `check_photo` varchar(1024) DEFAULT NULL COMMENT '复查现场图片',
  `check_time` datetime DEFAULT NULL COMMENT '复查时间',
  `is_end` int(2) DEFAULT NULL COMMENT '是否历史 0：否，1：是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='隐患记录整改复查记录表';



