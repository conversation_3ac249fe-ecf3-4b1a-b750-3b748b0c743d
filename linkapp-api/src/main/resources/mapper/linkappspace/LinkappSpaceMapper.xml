<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.space.mapper.LinkappSpaceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.space.entity.LinkappSpace">
        <id column="id" property="id" />
        <result column="space_name" property="spaceName" />
        <result column="short_name" property="shortName" />
        <result column="space_no" property="spaceNo" />
        <result column="sort_no" property="sortNo" />
        <result column="status" property="status" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="city_code" property="cityCode" />
        <result column="district" property="district" />
        <result column="site" property="site" />
        <result column="type" property="type" />
        <result column="parent_id" property="parentId" />
        <result column="map_id" property="mapId" />
        <result column="map_polygon_path" property="mapPolygonPath" />
        <result column="create_time" property="level" />
        <result column="creator" property="status" />
        <result column="modifier" property="remark" />
        <result column="modify_time" property="scope" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>
    
    
    <select id="selectLinkappSpaceList" resultType="com.easylinkin.linkappapi.space.entity.LinkappSpace"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappSpace">
       select
            a.id as id,
            a.space_name as spaceName,
            a.space_no as spaceNo,
            a.short_name as shortName,
            a.sort_no as sortNo,
            a.status as status,
            a.longitude as longitude,
            a.latitude as latitude,
            a.province as province,
            a.city as city,
            a.city_code as cityCode,
            a.district as district,
            a.site as site,
            a.type as type,
            a.parent_id as parentId,
            a.remark as remark,
            a.tenant_id as tenantId,
            a.map_id as mapId,
            a.map_polygon_path as mapPolygonPath,
            a.create_time as createTime,
            a.creator as creator,
            a.modifier as modifier,
            a.modify_time as modifyTime
       from linkapp_space a
      <where>
        1 = 1
       		<if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="spaceName != null and spaceName != ''">
                and a.space_name = #{spaceName}
            </if>
            <if test="spaceNo != null and spaceNo != ''">
                and a.space_no = #{spaceNo}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
            <if test="status == null or status == ''">
                and a.status = 1
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="parentId != null and parentId != ''">
                and a.parent_id = #{parentId}
            </if>
            <if test="tenantId != null and tenantId != ''">
              and a.tenant_id = #{tenantId}
            </if>
        </where>
        order by a.sort_no asc
    </select>

    <select id="selectLinkappSpaceListGlobal" resultType="com.easylinkin.linkappapi.space.entity.LinkappSpace"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappSpace">
        select
        a.id as id,
        a.space_name as spaceName,
        a.space_no as spaceNo,
        a.short_name as shortName,
        a.sort_no as sortNo,
        a.status as status,
        a.longitude as longitude,
        a.latitude as latitude,
        a.province as province,
        a.city as city,
        a.city_code as cityCode,
        a.district as district,
        a.site as site,
        a.type as type,
        a.parent_id as parentId,
        a.remark as remark,
        a.tenant_id as tenantId,
        a.map_id as mapId,
        a.map_polygon_path as mapPolygonPath,
        a.create_time as createTime,
        a.creator as creator,
        a.modifier as modifier,
        a.modify_time as modifyTime
        from linkapp_space a
        <where>
            1 = 1
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="spaceName != null and spaceName != ''">
                and a.space_name = #{spaceName}
            </if>
            <if test="spaceNo != null and spaceNo != ''">
                and a.space_no = #{spaceNo}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
            <if test="status == null or status == ''">
                and a.status = 1
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="parentId != null and parentId != ''">
                and a.parent_id = #{parentId}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and a.tenant_id = #{tenantId}
            </if>
        </where>
        order by a.sort_no asc
    </select>
    
    <select id="selectLinkappSpace" resultType="com.easylinkin.linkappapi.space.entity.LinkappSpace"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappSpace">
       select
            a.id as id,
            a.space_name as spaceName,
            a.space_no as spaceNo,
            a.short_name as shortName,
            a.sort_no as sortNo,
            a.status as status,
            a.longitude as longitude,
            a.latitude as latitude,
            a.province as province,
            a.city as city,
            a.city_code as cityCode,
            a.district as district,
            a.site as site,
            a.type as type,
            a.parent_id as parentId,
            a.remark as remark,
            a.tenant_id as tenantId,
            a.map_id as mapId,
            a.map_polygon_path as mapPolygonPath,
            a.create_time as createTime,
            a.creator as creator,
            a.modifier as modifier,
            a.modify_time as modifyTime
       from linkapp_space a
       <where>
          <if test="linkappSpace.id != null and linkappSpace.id != ''">
            and a.id = #{linkappSpace.id}
          </if>
          <if test="linkappSpace.tenantId != null and linkappSpace.tenantId != ''">
            and a.tenant_id = #{linkappSpace.tenantId}
          </if>
          <if test="linkappSpace.spaceName != null and linkappSpace.spaceName != ''">
            and a.space_name = #{linkappSpace.spaceName}
          </if>
          <if test="linkappSpace.spaceNo != null and linkappSpace.spaceNo != ''">
            and a.space_no = #{linkappSpace.spaceNo}
          </if>
          <if test="linkappSpace.status != null and linkappSpace.status != ''">
            and a.status = #{linkappSpace.status}
          </if>
          <if test="linkappSpace.status == null or linkappSpace.status == ''">
            and a.status = 1
          </if>
          <if test="linkappSpace.type != null and linkappSpace.type != ''">
            and a.type = #{linkappSpace.type}
          </if>
          <if test="linkappSpace.parentId != null and linkappSpace.parentId != ''">
            and a.parent_id = #{linkappSpace.parentId}
          </if>
        </where>
    </select>
    
    
    <select id="selectLinkappSpaceByUser" resultType="com.easylinkin.linkappapi.space.entity.LinkappSpace"
            parameterType="java.lang.String">
       SELECT a.* FROM linkapp_space a 
         LEFT JOIN linkapp_role_space b ON a.id = b.space_id
         LEFT JOIN linkapp_role c ON c.role_id_ = b.role_id
         LEFT JOIN linkapp_user_ref_role d ON c.role_id_ = d.role_id
         LEFT JOIN linkapp_user e ON d.user_id = e.id
       <where>
       		<if test="id != null and id != ''">
       			and e.`id` = #{id}
            </if>
        </where>
    </select>


    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into linkapp_space
        (id, space_name, short_name, space_no, sort_no, `status`, longitude, latitude, province,
        city, city_code, district, site, parent_id, `type`, remark, create_time, creator,
        modifier, modify_time, tenant_id, map_polygon_path, map_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.spaceName,jdbcType=VARCHAR}, #{item.shortName,jdbcType=VARCHAR},
            #{item.spaceNo,jdbcType=VARCHAR}, #{item.sortNo,jdbcType=DECIMAL}, #{item.status,jdbcType=DECIMAL},
            #{item.longitude,jdbcType=VARCHAR}, #{item.latitude,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR}, #{item.cityCode,jdbcType=VARCHAR}, #{item.district,jdbcType=VARCHAR},
            #{item.site,jdbcType=VARCHAR}, #{item.parentId,jdbcType=VARCHAR}, #{item.type,jdbcType=DECIMAL},
            #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR},
            #{item.modifier,jdbcType=VARCHAR}, #{item.modifyTime,jdbcType=TIMESTAMP}, #{item.tenantId,jdbcType=VARCHAR},
            #{item.mapPolygonPath,jdbcType=VARCHAR}, #{item.mapId,jdbcType=VARCHAR})
        </foreach>
    </insert>

</mapper>
