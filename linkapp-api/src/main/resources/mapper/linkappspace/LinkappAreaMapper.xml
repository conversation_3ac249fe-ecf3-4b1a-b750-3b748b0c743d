<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.space.mapper.LinkappAreaMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.space.entity.LinkappArea">
        <id column="id" property="id"/>
        <result column="space_id" property="spaceId"/>
        <result column="area_name" property="areaName"/>
        <result column="area_no" property="areaNo"/>
        <result column="sort_no" property="sortNo"/>
        <result column="type" property="type"/>
        <result column="level" property="level"/>
        <result column="status" property="status"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="img" property="img"/>
        <result column="remark" property="remark"/>
        <result column="site" property="site"/>
        <result column="area_path" property="areaPath"/>
        <result column="map_polygon_path" property="mapPolygonPath"/>
        <result column="parent_id" property="parentId"/>
        <result column="create_time" property="createTime"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="selectLinkappArea" resultMap="BaseResultMap"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappArea">
        select *
        from linkapp_area a
        <where>
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="spaceId != null and spaceId != ''">
                and a.space_id = #{spaceId}
            </if>
            <if test="areaName != null and areaName != ''">
                and a.area_name = #{areaName}
            </if>
            <if test="areaPath != null and areaPath != ''">
                and a.area_path = #{areaPath}
            </if>
            <if test="areaNo != null and areaNo != ''">
                and a.area_no = #{areaNo}
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
            <if test="status == null or status == ''">
                and a.status = 1
            </if>
            <if test="parentId != null and parentId != ''">
                and a.parent_id = #{parentId}
            </if>
            <if test="level != null and level != ''">
                and a.level > 0
            </if>
        </where>
        order by a.sort_no asc
    </select>

    <select id="selectLinkappAreaList" resultType="com.easylinkin.linkappapi.space.entity.LinkappArea"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappArea">
        select a.id               as id,
        a.space_id         as spaceId,
        a.area_name        as areaName,
        a.area_no          as areaNo,
        a.sort_no          as sortNo,
        a.type             as type,
        a.level            as level,
        a.status           as status,
        a.longitude        as longitude,
        a.latitude         as latitude,
        a.img              as img,
        a.remark           as remark,
        a.parent_id        as parentId,
        a.site             as site,
        a.area_path        as areaPath,
        a.map_polygon_path as mapPolygonPath,
        a.tenant_id        as tenantId,
        a.create_time      as createTime,
        a.creator          as creator,
        a.modifier         as modifier,
        a.modify_time      as modifyTime
        from linkapp_area a
        <where>
            a.level > 0
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="spaceId != null and spaceId != ''">
                and a.space_id = #{spaceId}
            </if>
            <if test="areaName != null and areaName != ''">
                and a.area_name = #{areaName}
            </if>
            <if test="areaPath != null and areaPath != ''">
                and a.area_path = #{areaPath}
            </if>
            <if test="areaNo != null and areaNo != ''">
                and a.area_no = #{areaNo}
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="status == null or status == ''">
                and a.status = 1
            </if>
            <if test="parentId != null and parentId != ''">
                and a.parent_id = #{parentId}
            </if>
            <if test="spaceIds != null and spaceIds != ''">
                and a.space_id in
                <foreach collection="spaceIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.sort_no asc
    </select>


    <select id="selectAreaListByTenantId" resultType="com.easylinkin.linkappapi.space.entity.LinkappArea"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappArea">
        select a.id               as id,
        a.space_id         as spaceId,
        a.area_name        as areaName,
        a.area_no          as areaNo,
        a.sort_no          as sortNo,
        a.type             as type,
        a.level            as level,
        a.status           as status,
        a.longitude        as longitude,
        a.latitude         as latitude,
        a.img              as img,
        a.remark           as remark,
        a.parent_id        as parentId,
        a.site             as site,
        a.area_path        as areaPath,
        a.map_polygon_path as mapPolygonPath,
        a.tenant_id        as tenantId,
        a.create_time      as createTime,
        a.creator          as creator,
        a.modifier         as modifier,
        a.modify_time      as modifyTime
        from linkapp_area a
        <where>
            a.level > 0
            and a.parent_id is not null
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="spaceId != null and spaceId != ''">
                and a.space_id = #{spaceId}
            </if>
            <if test="areaName != null and areaName != ''">
                and a.area_name = #{areaName}
            </if>
            <if test="areaPath != null and areaPath != ''">
                and a.area_path = #{areaPath}
            </if>
            <if test="areaNo != null and areaNo != ''">
                and a.area_no = #{areaNo}
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="status == null or status == ''">
                and a.status = 1
            </if>
            <if test="parentId != null and parentId != ''">
                and a.parent_id = #{parentId}
            </if>
            <if test="spaceIds != null and spaceIds != ''">
                and a.space_id in
                <foreach collection="spaceIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.sort_no asc
    </select>

    <!--         通用查询映射结果 -->
    <!--     <resultMap id="exportResultMap" type="com.easylinkin.linkappapi.space.entity.LinkappArea"> -->
    <!--         <id column="id" property="id" /> -->
    <!--         <result column="space_id" property="spaceId" /> -->
    <!--         <result column="area_name" property="areaName" /> -->
    <!--         <result column="area_no" property="areaNo" /> -->
    <!--         <result column="sort_no" property="sortNo" /> -->
    <!--         <result column="type" property="type" /> -->
    <!--         <result column="level" property="level" /> -->
    <!--         <result column="status" property="status" /> -->
    <!--         <result column="longitude" property="longitude" /> -->
    <!--         <result column="latitude" property="latitude" /> -->
    <!--         <result column="img" property="img" /> -->
    <!--         <result column="remark" property="remark" /> -->
    <!--         <result column="site" property="site" /> -->
    <!--         <result column="area_path" property="areaPath" /> -->
    <!--         <result column="map_polygon_path" property="mapPolygonPath" /> -->
    <!--         <result column="parent_id" property="parentId" /> -->
    <!--         <result column="create_time" property="createTime" /> -->
    <!--         <result column="creator" property="creator" /> -->
    <!--         <result column="modifier" property="modifier" /> -->
    <!--         <result column="modify_time" property="modifyTime" /> -->
    <!--         <result column="tenant_id" property="tenantId" /> -->
    <!--         <association property="space" javaType="com.easylinkin.linkappapi.space.entity.LinkappSpace"> -->
    <!-- 	      <result property="spaceName" column="space_name"/> -->
    <!-- 	      <result property="spaceNo" column="space_no"/> -->
    <!--     	</association> -->
    <!--     </resultMap> -->

    <select id="selectExportLinkappAreaList" resultType="com.easylinkin.linkappapi.space.entity.LinkappArea"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappArea">
        select a.id               as id,
        a.space_id         as spaceId,
        a.area_name        as areaName,
        a.area_no          as areaNo,
        a.sort_no          as sortNo,
        a.type             as type,
        a.level            as level,
        a.status           as status,
        a.longitude        as longitude,
        a.latitude         as latitude,
        a.img              as img,
        a.remark           as remark,
        a.parent_id        as parentId,
        c.area_name        as parentName,
        c.area_no          as parentNo,
        a.site             as site,
        a.area_path        as areaPath,
        a.map_polygon_path as mapPolygonPath,
        a.tenant_id        as tenantId,
        a.create_time      as createTime,
        a.creator          as creator,
        a.modifier         as modifier,
        a.modify_time      as modifyTime,
        b.space_name       as spaceName,
        b.space_no         as spaceNo
        from linkapp_area a
        left join linkapp_area c on a.parent_id = c.id
        left join linkapp_space b on a.space_id = b.id
        <where>
            a.level > 0
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="spaceId != null and spaceId != ''">
                and a.space_id = #{spaceId}
            </if>
            <if test="areaName != null and areaName != ''">
                and a.area_name = #{areaName}
            </if>
            <if test="areaNo != null and areaNo != ''">
                and a.area_no = #{areaNo}
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="status == null or status == ''">
                and a.status = 1
            </if>
            <if test="parentId != null and parentId != ''">
                and a.parent_id = #{parentId}
            </if>
        </where>
        order by a.sort_no asc
    </select>


    <select id="selectLinkappAreaTreeList" resultType="com.easylinkin.linkappapi.space.vo.SpaceTreeVo"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappArea">
        select * from (
        SELECT b.id               AS id,
        b.space_name       AS name,
        b.space_no         AS no,
        NULL               AS spaceId,
        b.sort_no          AS sortNo,
        b.type             AS TYPE,
        1                  AS LEVEL,
        b.status           AS STATUS,
        longitude          AS longitude,
        latitude           AS latitude,
        NULL               AS parentId,
        b.space_name       AS areaPath,
        b.site             AS site,
        b.map_id           as mapId,
        b.map_polygon_path as mapPolygonPath,
        b.tenant_id        as tenantId,
        'space'            AS nodeType,
        b.create_time      as createTime
        FROM linkapp_space b
        <where>
            <if test="spaceIds != null and spaceIds != ''">
                and b.id in
                <foreach collection="spaceIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        UNION
        SELECT a.id               AS id,
        a.area_name        AS name,
        a.area_no          AS no,
        a.space_id         AS spaceId,
        a.sort_no          AS sortNo,
        b.type             AS TYPE,
        a.level            AS LEVEL,
        a.status           AS STATUS,
        a.longitude        AS longitude,
        a.latitude         AS latitude,
        a.parent_id        AS parentId,
        a.area_path        AS areaPath,
        a.site             AS site,
        null               as mapId,
        a.map_polygon_path as mapPolygonPath,
        a.tenant_id        as tenantId,
        'area'             AS nodeType,
        a.create_time      as createTime
        FROM linkapp_area a
        LEFT JOIN linkapp_space b ON a.space_id = b.id
        <where>
            <if test="spaceIds != null and spaceIds != ''">
                and a.space_id in
                <foreach collection="spaceIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        )tt
        <where>
            tt.level > 0
            <if test="spaceName != null and spaceName != ''">
                and tt.name = #{spaceName}
            </if>
            <if test="spaceNo != null and spaceNo != ''">
                and tt.no = #{spaceNo}
            </if>
            <if test="nodeType != null and nodeType != ''">
                and tt.nodeType = #{nodeType}
            </if>
            <if test="id != null and id != ''">
                and tt.id = #{id}
            </if>
            <if test="spaceId != null and spaceId != ''">
                and tt.spaceId = #{spaceId}
            </if>
            <if test="type != null and type != ''">
                and tt.type = #{type}
            </if>
            <if test="status != null and status != ''">
                and tt.status = #{status}
            </if>
            <if test="status == null or status == ''">
                and tt.status = 1
            </if>
            <if test="tenantId != null and tenantId != ''">
                and tt.tenantId = #{tenantId}
            </if>
            <if test="areaPath != null and areaPath != ''">
                and tt.areaPath like concat(#{areaPath},':%')
                <!-- AND (tt.areaPath = #{areaPath} or tt.areaPath like concat(#{areaPath},':%')) -->
            </if>
            <if test="parentId != null and parentId != ''">
                and tt.parentId = #{parentId}
            </if>
        </where>
    </select>

    <select id="selectLinkappAreaLowLevlNodeList" resultType="com.easylinkin.linkappapi.space.entity.LinkappArea"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappArea">
        SELECT a.id          as id,
        a.space_id    as spaceId,
        a.area_name   as areaName,
        a.area_no     as areaNo,
        a.sort_no     as sortNo,
        a.type        as type,
        a.level       as level,
        a.status      as status,
        a.longitude   as longitude,
        a.latitude    as latitude,
        a.img         as img,
        a.remark      as remark,
        a.parent_id   as parentId,
        a.site        as site,
        a.area_path   as areaPath,
        a.tenant_id   as tenantId,
        a.create_time as createTime,
        a.creator     as creator,
        a.modifier    as modifier,
        a.modify_time as modifyTime
        FROM linkapp_area a
        left join linkapp_area b on b.id = #{id}
        <where>
            <if test="id != null and id != ''">
                and (a.area_path = b.area_path or a.area_path like concat(b.area_path,':%'))
            </if>
        </where>
        order by a.sort_no asc
    </select>


    <select id="selectAreaByGlobal" resultMap="BaseResultMap">
        select *
        from linkapp_area a
        <where>
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="spaceId != null and spaceId != ''">
                and a.space_id = #{spaceId}
            </if>
            <if test="areaName != null and areaName != ''">
                and a.area_name = #{areaName}
            </if>
            <if test="areaNo != null and areaNo != ''">
                and a.area_no = #{areaNo}
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
            <if test="status == null or status == ''">
                and a.status = 1
            </if>
            <if test="parentId != null and parentId != ''">
                and a.parent_id = #{parentId}
            </if>
        </where>
        order by a.sort_no asc
    </select>

    <resultMap id="SpaceTreeVoMap" type="com.easylinkin.linkappapi.space.vo.SpaceTreeVo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="no" property="no"/>
        <result column="spaceId" property="spaceId"/>
        <result column="sortNo" property="sortNo"/>
        <result column="TYPE" property="type"/>
        <result column="LEVEL" property="level"/>
        <result column="STATUS" property="status"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="parentId" property="parentId"/>
        <result column="areaPath" property="areaPath"/>
        <result column="site" property="site"/>
        <result column="mapId" property="mapId"/>
        <result column="mapPolygonPath" property="mapPolygonPath"/>
        <result column="tenantId" property="tenantId"/>
        <result column="nodeType" property="nodeType"/>
    </resultMap>

    <select id="getAreaTreeListByFunctionIdentifier" resultMap="SpaceTreeVoMap"
            parameterType="com.easylinkin.linkappapi.function.entity.TenantFunctionRefArea">
        select * from (
        select
        s.id               AS id,
        s.space_name       AS name,
        s.space_no         AS no,
        NULL               AS spaceId,
        s.sort_no          AS sortNo,
        s.type             AS TYPE,
        1                  AS LEVEL,
        s.status           AS STATUS,
        s.longitude          AS longitude,
        s.latitude           AS latitude,
        NULL               AS parentId,
        s.space_name       AS areaPath,
        s.site             AS site,
        s.map_id           as mapId,
        s.map_polygon_path as mapPolygonPath,
        s.tenant_id        as tenantId,
        #{tfra.functionIdentifier}        as functionIdentifier,
        'space'            AS nodeType,
        ifnull((
        select count(*) from
        device_ref_area_scope d1 inner join linkapp_device d on d1.device_code=d.code and d.delete_state = 1
        <where>
            <if test="tfra.functionIdentifier != null and tfra.functionIdentifier != ''">
                and d1.function_identifier =#{tfra.functionIdentifier}
            </if>
            <if test="tfra.tenantId != null and tfra.tenantId != ''">
                and d1.tenant_id=#{tfra.tenantId}
            </if>
            and d1.area_id=s.id
        </where>
        ), 0) deviceCount
        from
        tenant_function_ref_area fra left join linkapp_area a on fra.area_id=a.id
        left join linkapp_space s on a.space_id=s.id
        <where>
            <if test="tfra.functionIdentifier != null and tfra.functionIdentifier != ''">
                and fra.function_identifier = #{tfra.functionIdentifier}
            </if>
        </where>
        UNION
        select
        a.id               AS id,
        a.area_name        AS name,
        a.area_no          AS no,
        a.space_id         AS spaceId,
        a.sort_no          AS sortNo,
        s.type             AS TYPE,
        a.level            AS LEVEL,
        a.status           AS STATUS,
        a.longitude        AS longitude,
        a.latitude         AS latitude,
        a.parent_id        AS parentId,
        a.area_path        AS areaPath,
        a.site             AS site,
        null               as mapId,
        a.map_polygon_path as mapPolygonPath,
        a.tenant_id        as tenantId,
        #{tfra.functionIdentifier}        as functionIdentifier,
        'area'             AS nodeType,
        ifnull((
        select count(*) from
        device_ref_area_scope d2 inner join linkapp_device d on d2.device_code=d.code and d.delete_state = 1
        <where>
            <if test="tfra.functionIdentifier != null and tfra.functionIdentifier != ''">
                and d2.function_identifier =#{tfra.functionIdentifier}
            </if>
            <if test="tfra.tenantId != null and tfra.tenantId != ''">
                and d2.tenant_id=#{tfra.tenantId}
            </if>
            and d2.area_id=a.id
        </where>
        ), 0) deviceCount
        from
        tenant_function_ref_area fra left join linkapp_area a on fra.area_id=a.id
        left join linkapp_space s on a.space_id=s.id
        <where>
            <if test="tfra.functionIdentifier != null and tfra.functionIdentifier != ''">
                and fra.function_identifier = #{tfra.functionIdentifier}
            </if>
        </where>
        )tt
        <where>
            tt.level > 0
            and tt.status = 1
            <if test="tfra.tenantId != null and tfra.tenantId != ''">
                and tt.tenantId = #{tfra.tenantId}
            </if>
            <if test="tfra.areaPath != null and tfra.areaPath != ''">
                and (tt.areaPath = #{tfra.areaPath} or tt.areaPath like concat(#{tfra.areaPath},':%'))
            </if>
        </where>
    </select>

    <!--   获取包含子区域 -->
    <select id="getContainParentIdArea" resultType="java.lang.String">
        SELECT
            a.parent_id
        FROM
            linkapp_area a
            LEFT JOIN linkapp_space b ON a.space_id = b.id
        <where>
            a.STATUS = 1
            <if test="areaPath != null and areaPath != ''">
                AND (a.area_path = #{areaPath} or a.area_path like concat(#{areaPath},':%'))
            </if>
            AND a.parent_id IS NOT NULL
        </where>
        group by a.parent_id
    </select>

    <!--    获取指定区域下的最子区域-->
    <select id="getMostChildArea" resultType="com.easylinkin.linkappapi.classroom.entity.ClassroomDevice">
        SELECT
            a.id AS id,
            a.area_name AS NAME,
            a.area_no AS NO,
            a.space_id AS spaceId,
            a.sort_no AS sortNo,
            b.type AS TYPE,
            a. LEVEL AS LEVEL,
            a. STATUS AS STATUS,
            a.longitude AS longitude,
            a.latitude AS latitude,
            a.parent_id AS parentId,
            a.area_path AS areaPath,
            a.site AS site,
            NULL AS mapId,
            a.map_polygon_path AS mapPolygonPath,
            a.tenant_id AS tenantId,
            'area' AS nodeType
        FROM
            linkapp_area a
            LEFT JOIN linkapp_space b ON a.space_id = b.id
        <where>
            a.STATUS = 1
            <if test="areaPath != null and areaPath != ''">
                AND (a.area_path = #{areaPath} or a.area_path like concat(#{areaPath},':%'))
            </if>
            <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(parentIdList)">
                and a.id not in
                <foreach collection="parentIdList" item="parentId" open="(" separator="," close=")">
                    #{parentId}
                </foreach>
            </if>
        </where>
        order by a.area_path
    </select>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into linkapp_area
        (id, parent_id, space_id, area_name, area_no, sort_no, `type`, `level`, `status`,
        img, latitude, longitude, remark, create_time, creator, modifier, modify_time,
        site, area_path, tenant_id, map_polygon_path)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.parentId,jdbcType=VARCHAR}, #{item.spaceId,jdbcType=VARCHAR},
            #{item.areaName,jdbcType=VARCHAR}, #{item.areaNo,jdbcType=VARCHAR}, #{item.sortNo,jdbcType=DECIMAL},
            #{item.type,jdbcType=DECIMAL}, #{item.level,jdbcType=DECIMAL}, #{item.status,jdbcType=DECIMAL},
            #{item.img,jdbcType=VARCHAR}, #{item.latitude,jdbcType=VARCHAR}, #{item.longitude,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR},
            #{item.modifier,jdbcType=VARCHAR}, #{item.modifyTime,jdbcType=TIMESTAMP}, #{item.site,jdbcType=VARCHAR},
            #{item.areaPath,jdbcType=VARCHAR}, #{item.tenantId,jdbcType=VARCHAR}, #{item.mapPolygonPath,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="selectLinkappAreaListAll" resultType="com.easylinkin.linkappapi.space.entity.LinkappArea"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappArea">
        select a.id               as id,
        a.space_id         as spaceId,
        a.area_name        as areaName,
        a.area_no          as areaNo,
        a.sort_no          as sortNo,
        a.type             as type,
        a.level            as level,
        a.status           as status,
        a.longitude        as longitude,
        a.latitude         as latitude,
        a.img              as img,
        a.remark           as remark,
        a.parent_id        as parentId,
        a.site             as site,
        a.area_path        as areaPath,
        a.map_polygon_path as mapPolygonPath,
        a.tenant_id        as tenantId,
        a.create_time      as createTime,
        a.creator          as creator,
        a.modifier         as modifier,
        a.modify_time      as modifyTime
        from linkapp_area a
        <where>
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="spaceId != null and spaceId != ''">
                and a.space_id = #{spaceId}
            </if>
            <if test="areaName != null and areaName != ''">
                and a.area_name = #{areaName}
            </if>
            <if test="areaPath != null and areaPath != ''">
                and a.area_path = #{areaPath}
            </if>
            <if test="areaNo != null and areaNo != ''">
                and a.area_no = #{areaNo}
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="status == null or status == ''">
                and a.status = 1
            </if>
            <if test="parentId != null and parentId != ''">
                and a.parent_id = #{parentId}
            </if>
            <if test="spaceIds != null and spaceIds.size()>0 ">
                and a.space_id in
                <foreach collection="spaceIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="areaPaths != null and areaPaths.size()>0">
                and a.area_path in
                <foreach item="item"  collection="areaPaths" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size()>0">
                and a.id in
                <foreach item="item"  collection="ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="tenantId != null and tenantId != ''">
                and a.tenant_id = #{tenantId}
            </if>
        </where>
        order by a.sort_no asc
    </select>

    <select id="selectLinkappAreaListAllGlobal" resultType="com.easylinkin.linkappapi.space.entity.LinkappArea"
            parameterType="com.easylinkin.linkappapi.space.entity.LinkappArea">
        select a.id               as id,
        a.space_id         as spaceId,
        a.area_name        as areaName,
        a.area_no          as areaNo,
        a.sort_no          as sortNo,
        a.type             as type,
        a.level            as level,
        a.status           as status,
        a.longitude        as longitude,
        a.latitude         as latitude,
        a.img              as img,
        a.remark           as remark,
        a.parent_id        as parentId,
        a.site             as site,
        a.area_path        as areaPath,
        a.map_polygon_path as mapPolygonPath,
        a.tenant_id        as tenantId,
        a.create_time      as createTime,
        a.creator          as creator,
        a.modifier         as modifier,
        a.modify_time      as modifyTime
        from linkapp_area a
        <where>
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="spaceId != null and spaceId != ''">
                and a.space_id = #{spaceId}
            </if>
            <if test="areaName != null and areaName != ''">
                and a.area_name = #{areaName}
            </if>
            <if test="areaPath != null and areaPath != ''">
                and a.area_path = #{areaPath}
            </if>
            <if test="areaNo != null and areaNo != ''">
                and a.area_no = #{areaNo}
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="status == null or status == ''">
                and a.status = 1
            </if>
            <if test="parentId != null and parentId != ''">
                and a.parent_id = #{parentId}
            </if>
            <if test="spaceIds != null and spaceIds.size()>0 ">
                and a.space_id in
                <foreach collection="spaceIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="areaPaths != null and areaPaths.size()>0">
                and a.area_path in
                <foreach item="item"  collection="areaPaths" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size()>0">
                and a.id in
                <foreach item="item"  collection="ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="tenantId != null and tenantId != ''">
                and a.tenant_id = #{tenantId}
            </if>
        </where>
        order by a.sort_no asc
    </select>
</mapper>
