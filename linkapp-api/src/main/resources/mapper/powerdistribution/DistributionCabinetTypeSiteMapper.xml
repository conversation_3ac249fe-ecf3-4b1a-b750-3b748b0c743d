<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetTypeSiteMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetTypeSite">
    <id column="id" property="id"/>
    <result column="site_name" property="siteName"/>
    <result column="sort_no" property="sortNo"/>
    <result column="required" property="required"/>
    <result column="electric_equipment" property="electricEquipment"/>
    <result column="device_unit_code" property="deviceUnitCode"/>
    <result column="device_unit_version" property="deviceUnitVersion"/>
    <result column="distribution_cabinet_type_id" property="distributionCabinetTypeId"/>
  </resultMap>

  <select id="getDistributionCabinetTypeSites" resultMap="BaseResultMap">
    select dcts.* from distribution_cabinet_type_site dcts
    <where>
      1=1
      <if test="distributionCabinetTypeSite.id!=null and distributionCabinetTypeSite.id!=''">
        and dcts.id = #{distributionCabinetTypeSite.id}
      </if>
      <if test="distributionCabinetTypeSite.siteName!=null and distributionCabinetTypeSite.siteName!=''">
        and dcts.id like concat ('%',#{distributionCabinetTypeSite.siteName},'%')
      </if>
      <if test="distributionCabinetTypeSite.deviceUnitCode!=null and distributionCabinetTypeSite.deviceUnitCode!=''">
        and dcts.device_unit_code = #{distributionCabinetTypeSite.deviceUnitCode}
      </if>
      <if test="distributionCabinetTypeSite.deviceUnitVersion!=null and distributionCabinetTypeSite.deviceUnitVersion!=''">
        and dcts.device_unit_version = #{distributionCabinetTypeSite.deviceUnitVersion}
      </if>
      <if test="distributionCabinetTypeSite.distributionCabinetTypeId!=null and distributionCabinetTypeSite.distributionCabinetTypeId!=''">
        and dcts.distribution_cabinet_type_id = #{distributionCabinetTypeSite.distributionCabinetTypeId}
      </if>
      order by sort_no asc
    </where>

  </select>

  <select id="getDistributionCabinetTypeSite" resultMap="BaseResultMap">
    select dcts.* from distribution_cabinet_type_site dcts where  dcts.id = #{id}
  </select>

  <select id="getDistributionCabinetTypeSiteByTenant" resultMap="BaseResultMap">
    SELECT 
	  b.* 
	FROM
	  distribution_cabinet_type a 
	  LEFT JOIN distribution_cabinet_type_site b 
	    ON a.id = b.distribution_cabinet_type_id  
    <where>
      <if test="tenantId != null and tenantId != ''">
        and a.tenant_id = #{tenantId} 
      </if>
      <if test="deviceUnitCode != null and deviceUnitCode != ''">
        and b.device_unit_code = #{deviceUnitCode} 
      </if>
    </where>
  </select>


</mapper>
