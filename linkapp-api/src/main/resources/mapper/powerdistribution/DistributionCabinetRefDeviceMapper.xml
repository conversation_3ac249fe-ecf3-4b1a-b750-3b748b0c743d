<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetRefDeviceMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice">
    <id column="id" property="id"/>
    <result column="distribution_cabinet_id" property="distributionCabinetId"/>
    <result column="device_code" property="deviceCode"/>
    <result column="distribution_cabinet_type_site_id" property="distributionCabinetTypeSiteId"/>
  </resultMap>

  <resultMap id="CabinetRefDevicesWithConfigurationAndExpressionsMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice">
    <id column="id" property="id"/>
    <result column="distribution_cabinet_id" property="distributionCabinetId"/>
    <result column="device_code" property="deviceCode"/>
    <result column="distribution_cabinet_type_site_id" property="distributionCabinetTypeSiteId"/>
    <collection property="distributionCabinetConfigurationList" column="{distributionCabinetConfiguration.distributionCabinetTypeSiteId =distribution_cabinet_type_site_id}"
      select="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetConfigurationMapper.getDistributionCabinetConfigurationWithExpressions"/>
  </resultMap>


  <select id="getCabinetRefDevicesWithConfigurationAndExpressions" resultMap="CabinetRefDevicesWithConfigurationAndExpressionsMap">
    select dcrd.* from distribution_cabinet_ref_device dcrd
    <where>
      1 = 1
      <if test="device.code != null and device.code != ''">
        and dcrd.device_code = #{device.code}
      </if>
    </where>
  </select>

</mapper>
