<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetRepairFileMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairFile">
    <!--@mbg.generated-->
    <!--@Table distribution_cabinet_repair_file-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cabinet_repair_id" jdbcType="INTEGER" property="cabinetRepairId" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, cabinet_repair_id, file_url, file_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into distribution_cabinet_repair_file
    (cabinet_repair_id, file_url, file_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.cabinetRepairId,jdbcType=INTEGER}, #{item.fileUrl,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2021-09-16-->
  <select id="findAllByCabinetRepairId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from distribution_cabinet_repair_file
        where cabinet_repair_id=#{cabinetRepairId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-09-16-->
  <delete id="deleteByCabinetRepairId">
        delete from distribution_cabinet_repair_file
        where cabinet_repair_id=#{cabinetRepairId,jdbcType=INTEGER}
    </delete>
</mapper>