<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionElectricPriceConfigMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPriceConfig">
    <!--@mbg.generated-->
    <!--@Table distribution_electric_price_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="month" jdbcType="VARCHAR" property="month" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="electric_price_id" jdbcType="INTEGER" property="electricPriceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `month`, `type`, electric_price_id
  </sql>

<!--auto generated by MybatisCodeHelper on 2021-09-02-->
  <select id="findAllByElectricPriceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from distribution_electric_price_config
    where electric_price_id=#{electricPriceId,jdbcType=INTEGER}
  </select>
</mapper>