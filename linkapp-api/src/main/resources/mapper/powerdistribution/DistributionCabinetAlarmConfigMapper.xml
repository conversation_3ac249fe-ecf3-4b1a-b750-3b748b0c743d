<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetAlarmConfigMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmConfig">
    <!--@mbg.generated-->
    <!--@Table distribution_cabinet_alarm_config-->
    <id column="cabinet_id" jdbcType="VARCHAR" property="cabinetId" />
    <id column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="value_max" jdbcType="DECIMAL" property="valueMax" />
    <result column="value_min" jdbcType="DECIMAL" property="valueMin" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    cabinet_id, device_code, `type`, value_max, value_min
  </sql>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into distribution_cabinet_alarm_config
    (cabinet_id, device_code, `type`, value_max, value_min)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.cabinetId,jdbcType=VARCHAR}, #{item.deviceCode,jdbcType=VARCHAR}, #{item.type,jdbcType=BOOLEAN}, 
        #{item.valueMax,jdbcType=DECIMAL}, #{item.valueMin,jdbcType=DECIMAL})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2021-09-14-->
  <select id="findAllByDeviceCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from distribution_cabinet_alarm_config
    where device_code=#{distributionCabinetAlarmConfig.deviceCode,jdbcType=VARCHAR}
    <if test="distributionCabinetAlarmConfig.type != null ">
      and type = #{distributionCabinetAlarmConfig.type}
    </if>
    <if test="distributionCabinetAlarmConfig.queryValue != null ">
      and  (value_max <![CDATA[>=]]> #{distributionCabinetAlarmConfig.queryValue} or value_min <![CDATA[<=]]> #{distributionCabinetAlarmConfig.queryValue})
    </if>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-09-17-->
  <update id="updateByCabinetIdAndDeviceCodeAndType">
        update distribution_cabinet_alarm_config
        <set>
            <if test="updated.valueMax != null">
                value_max = #{updated.valueMax,jdbcType=DECIMAL},
            </if>
            <if test="updated.valueMin != null">
                value_min = #{updated.valueMin,jdbcType=DECIMAL},
            </if>
        </set>
        where cabinet_id=#{updated.cabinetId,jdbcType=VARCHAR} and device_code=#{updated.deviceCode,jdbcType=VARCHAR} and
        `type`=#{updated.type,jdbcType=BOOLEAN}
    </update>
</mapper>