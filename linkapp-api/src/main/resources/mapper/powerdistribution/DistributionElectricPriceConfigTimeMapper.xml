<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionElectricPriceConfigTimeMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPriceConfigTime">
    <!--@mbg.generated-->
    <!--@Table distribution_electric_price_config_time-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="start_time" jdbcType="TIME" property="startTime" />
    <result column="end_time" jdbcType="TIME" property="endTime" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="electric_price_config_id" jdbcType="INTEGER" property="electricPriceConfigId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, start_time, end_time, price, electric_price_config_id
  </sql>

<!--auto generated by MybatisCodeHelper on 2021-09-02-->
  <select id="findAllByElectricPriceConfigId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from distribution_electric_price_config_time
    where electric_price_config_id=#{electricPriceConfigId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2021-09-03-->
  <insert id="insertList">
        INSERT INTO distribution_electric_price_config_time(
        id,
        start_time,
        end_time,
        price,
        type,
        electric_price_config_id
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id,jdbcType=INTEGER},
            #{element.startTime,jdbcType=TIME},
            #{element.endTime,jdbcType=TIME},
            #{element.price,jdbcType=DECIMAL},
            #{element.type,jdbcType=INTEGER},
            #{element.electricPriceConfigId,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
</mapper>