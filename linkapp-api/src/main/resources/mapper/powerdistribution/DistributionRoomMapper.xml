<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionRoomMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionRoom">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="area_id" property="areaId"/>
    <result column="description" property="description"/>
    <result column="delete_state" property="deleteState"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="latitude" property="latitude"/>
    <result column="longitude" property="longitude"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="video_path" property="videoPath"/>
  </resultMap>

  <!--  查询多条 或分页查询多条-->
  <resultMap id="getDistributionRoomsMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionRoom">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="area_id" property="areaId"/>
    <result column="description" property="description"/>
    <result column="delete_state" property="deleteState"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="latitude" property="latitude"/>
    <result column="longitude" property="longitude"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="video_path" property="videoPath"/>
    <result column="company_info_id" property="companyInfoId"/>
    <result column="electric_price_id" property="electricPriceId"/>
    <association property="linkappArea" javaType="com.easylinkin.linkappapi.space.entity.LinkappArea">
      <result property="areaName" column="area_name"/>
      <result property="spaceName" column="space_name"/>
    </association>
  </resultMap>


  <select id="getDistributionRooms" resultMap="getDistributionRoomsMap">
    select dr.*,
           la.area_name,
           ls.space_name
    from distribution_room dr
           left join linkapp_area la on la.id = dr.area_id
           left join linkapp_space ls on la.space_id = ls.id
    <where>
      dr.delete_state = 1
      <if test="spacesIds != null and spacesIds.size() > 0">
        and la.space_id in
        <foreach collection="spacesIds" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test='distributionRoom.areaPath != null and distributionRoom.areaPath != ""'>
        and (la.area_path = #{distributionRoom.areaPath} or la.area_path like concat(#{distributionRoom.areaPath},':%'))
      </if>
      <if test='distributionRoom.areaId != null and distributionRoom.areaId != ""'>
        and dr.area_id = #{distributionRoom.areaId}
      </if>
      <if test='distributionRoom.tenantId != null and distributionRoom.tenantId != ""'>
        and dr.tenant_id = #{distributionRoom.tenantId}
      </if>
      <if test='distributionRoom.name != null and distributionRoom.name != ""'>
        and dr.name like concat('%', #{distributionRoom.name}, '%')
      </if>
    </where>
    order by dr.modify_time desc
  </select>


  <!--  查询单条-->
  <resultMap id="getDistributionRoomMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionRoom">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="area_id" property="areaId"/>
    <result column="area_path" property="areaPath"/>
    <result column="description" property="description"/>
    <result column="delete_state" property="deleteState"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="latitude" property="latitude"/>
    <result column="longitude" property="longitude"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="video_path" property="videoPath"/>
    <result column="company_info_id" property="companyInfoId"/>
    <result column="electric_price_id" property="electricPriceId"/>
    <association property="linkappArea" javaType="com.easylinkin.linkappapi.space.entity.LinkappArea">
      <result property="areaName" column="area_name"/>
      <result property="areaPath" column="area_path"/>
      <result property="spaceName" column="space_name"/>
    </association>
    <collection property="distributionCabinetList" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet"
      select="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetMapper.getDistributionCabinetWithRefDevices"
      column="{distributionCabinet.distributionRoomId=id}"/>
  </resultMap>

  <select id="getDistributionRoom" resultMap="getDistributionRoomMap">
    select dr.*,
           la.area_name,
           la.area_path,
           ls.space_name
    from distribution_room dr
           left join linkapp_area la on la.id = dr.area_id
           left join linkapp_space ls on la.space_id = ls.id
    <where>
      dr.delete_state = 1
        and dr.id = #{id}
    </where>
  </select>

  <select id="getRefdevices" resultType="com.easylinkin.linkappapi.device.entity.Device">
    SELECT d.code,
           d.status,
           d.online_state,
           a.area_name        AS areaName,
           s.id               AS spaceId,
           s.space_name       AS spaceName,
           u.device_type_name AS deviceTypeName,
           u.name             AS deviceUnitName,
           u.code             AS deviceUnitCode,
           u.version          AS deviceUnitVersion,
           drrd.sort_no       as sortNo
    FROM linkapp_device d
           inner join distribution_room_ref_device drrd on drrd.device_code = d.code
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
           LEFT JOIN linkapp_area a ON (a.id = d.area_id)
           LEFT JOIN linkapp_space s ON (s.id = a.space_id)
    <where>
      d.delete_state = 1
        and drrd.distribution_room_id = #{id}
    </where>
    order by drrd.sort_no asc
  </select>
</mapper>
