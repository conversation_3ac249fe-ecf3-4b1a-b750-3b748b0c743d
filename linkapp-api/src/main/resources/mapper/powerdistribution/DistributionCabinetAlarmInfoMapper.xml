<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetAlarmInfoMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmInfo">
    <!--@mbg.generated-->
    <!--@Table distribution_cabinet_alarm_info-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cabinet_id" jdbcType="VARCHAR" property="cabinetId" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="source_json" jdbcType="LONGVARCHAR" property="sourceJson" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="alarm_status" jdbcType="BOOLEAN" property="alarmStatus" />
    <result column="alarm_time" jdbcType="TIMESTAMP" property="alarmTime" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="deal_time" jdbcType="TIMESTAMP" property="dealTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, cabinet_id, device_code, `name`, content, source_json, `status`, alarm_status, 
    alarm_time, user_id, deal_time, description
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into distribution_cabinet_alarm_info
    (cabinet_id, device_code, `name`, content, source_json, `status`, alarm_status, alarm_time, 
      user_id, deal_time, description,level)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.cabinetId,jdbcType=VARCHAR}, #{item.deviceCode,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.content,jdbcType=VARCHAR}, #{item.sourceJson,jdbcType=LONGVARCHAR}, #{item.status,jdbcType=BOOLEAN}, 
        #{item.alarmStatus,jdbcType=BOOLEAN}, #{item.alarmTime,jdbcType=TIMESTAMP}, #{item.userId,jdbcType=VARCHAR}, 
        #{item.dealTime,jdbcType=TIMESTAMP}, #{item.description,jdbcType=VARCHAR},#{item.level,jdbcType=INTEGER})
    </foreach>
  </insert>
  
  <select id="findAllByCabientId" resultType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmInfo">
    SELECT temp.*, la.area_path AS areaPath,u.device_type_name deviceTypeName
    FROM (
        SELECT la.id idStr,la.device_code, la.content, la.alarm_time, re.`name`, la.`status`,1 identificationType
            , ld.area_id,ld.device_unit_id,ld.name deviceName
        FROM (
            SELECT la.id,la.device_code, la.content, la.rule_engine_id, alarm_time, `status`
            FROM linkapp_alarm la
            WHERE la.device_code IN (
                SELECT device_code
                FROM distribution_cabinet_ref_device
                <where>
                  <if test="distributionCabinetAlarmInfo.cabinetId != null and distributionCabinetAlarmInfo.cabinetId != '' ">
                    and distribution_cabinet_id = #{distributionCabinetAlarmInfo.cabinetId}
                  </if>
                </where>
            )
            <if test="distributionCabinetAlarmInfo.tenantId != null and distributionCabinetAlarmInfo.tenantId != ''">
                and la.tenant_id = #{distributionCabinetAlarmInfo.tenantId}
            </if>
            <if test="distributionCabinetAlarmInfo.status != null ">
                and la.status = #{distributionCabinetAlarmInfo.status}
            </if>
              <if test="distributionCabinetAlarmInfo.queryTimeStart != null and distributionCabinetAlarmInfo.queryTimeStart != ''">
                  and la.alarm_time &gt;= #{distributionCabinetAlarmInfo.queryTimeStart}
              </if>
              <if test="distributionCabinetAlarmInfo.queryTimeEnd != null and distributionCabinetAlarmInfo.queryTimeEnd != ''">
                  and la.alarm_time &lt;= #{distributionCabinetAlarmInfo.queryTimeEnd}
              </if>
        ) la
            LEFT JOIN rule_engine re ON la.rule_engine_id = re.id
            LEFT JOIN linkapp_device ld ON la.device_code = ld.`code` AND ld.delete_state = 1
            <where>
                <if test="distributionCabinetAlarmInfo.ruleEngineName != null and distributionCabinetAlarmInfo.ruleEngineName != '' ">
                    and re.name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.ruleEngineName}, '%')
                </if>
                <if test="distributionCabinetAlarmInfo.deviceName != null and distributionCabinetAlarmInfo.deviceName != ''">
                    and ld.name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.deviceName}, '%')
                </if>
                <if test="distributionCabinetAlarmInfo.device != null ">
                    <if test="distributionCabinetAlarmInfo.device.name != null and distributionCabinetAlarmInfo.device.name != ''">
                        and ld.name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.device.name}, '%')
                    </if>
                    <if test="distributionCabinetAlarmInfo.device.areaId != null and distributionCabinetAlarmInfo.device.areaId != ''">
                        and ld.area_id =#{distributionCabinetAlarmInfo.device.areaId}
                    </if>
                </if>
            </where>
        UNION
        SELECT dca.id idStr,dca.device_code, dca.content, dca.alarm_time, dca.`name`, dca.`status`,2 identificationType
            , dr.area_id,ld.device_unit_id,ld.name deviceName
        FROM (
            SELECT id,device_code, cabinet_id, content, `name`, alarm_time
                , `status`
            FROM distribution_cabinet_alarm_info
            <where>
              <if test="distributionCabinetAlarmInfo.cabinetId != null and distributionCabinetAlarmInfo.cabinetId != '' ">
                and cabinet_id = #{distributionCabinetAlarmInfo.cabinetId}
              </if>
              <if test="distributionCabinetAlarmInfo.status != null ">
                and status = #{distributionCabinetAlarmInfo.status}
              </if>
              <if test="distributionCabinetAlarmInfo.ruleEngineName != null and distributionCabinetAlarmInfo.ruleEngineName != '' ">
                and name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.ruleEngineName}, '%')
              </if>
                <if test="distributionCabinetAlarmInfo.queryTimeStart != null and distributionCabinetAlarmInfo.queryTimeStart != ''">
                    and alarm_time &gt;= #{distributionCabinetAlarmInfo.queryTimeStart}
                </if>
                <if test="distributionCabinetAlarmInfo.queryTimeEnd != null and distributionCabinetAlarmInfo.queryTimeEnd != ''">
                    and alarm_time &lt;= #{distributionCabinetAlarmInfo.queryTimeEnd}
                </if>
            </where>
        ) dca
            LEFT JOIN distribution_cabinet dc ON dca.cabinet_id = dc.id
            LEFT JOIN distribution_room dr ON dc.distribution_room_id = dr.id
            LEFT JOIN linkapp_device ld ON dca.device_code = ld.`code` AND ld.delete_state = 1
            <where>
                <if test="distributionCabinetAlarmInfo.deviceName != null and distributionCabinetAlarmInfo.deviceName != ''">
                    and ld.name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.deviceName}, '%')
                </if>
                <if test="distributionCabinetAlarmInfo.device != null ">
                    <if test="distributionCabinetAlarmInfo.device.name != null and distributionCabinetAlarmInfo.device.name != ''">
                        and ld.name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.device.name}, '%')
                    </if>
                    <if test="distributionCabinetAlarmInfo.device.areaId != null and distributionCabinetAlarmInfo.device.areaId != ''">
                        and ld.area_id =#{distributionCabinetAlarmInfo.device.areaId}
                    </if>
                </if>
            </where>
    ) temp
        LEFT JOIN linkapp_device_unit u ON temp.device_unit_id = u.id
        LEFT JOIN linkapp_area la ON temp.area_id = la.id
      <where>
          <if test="distributionCabinetAlarmInfo.areaPath != null and distributionCabinetAlarmInfo.areaPath != ''">
              and (ld.area_path = #{distributionCabinetAlarmInfo.areaPath} or ld.area_path like concat(#{distributionCabinetAlarmInfo.areaPath},':%'))
          </if>
          <if test="distributionCabinetAlarmInfo.deviceTypeId != null and distributionCabinetAlarmInfo.deviceTypeId != ''">
              and u.device_type_id = #{distributionCabinetAlarmInfo.deviceTypeId}
          </if>
          <if test="distributionCabinetAlarmInfo.device != null and distributionCabinetAlarmInfo.device.deviceTypeId != null and distributionCabinetAlarmInfo.device.deviceTypeId != ''">
              and u.device_type_id = #{distributionCabinetAlarmInfo.device.deviceTypeId}
          </if>
          <if test="spacesIds != null and spacesIds.size() > 0">
              and la.space_id in
              <foreach collection="spacesIds" item="item" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
      </where>
    ORDER BY temp.alarm_time DESC
  </select>

    <select id="findAllByCabientIdVos" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmVo">
        SELECT
          temp.deviceName deviceName,
          temp.device_code deviceCode,
          u.device_type_name deviceTypeName,
          la.area_path deviceAreaPath,
          temp.name ruleEngineName,
          if(temp.level = 3, '低', if(temp.level = 2, '中', if(temp.level = 1, '高', '未知'))) level,
          temp.content content,
          temp.source_json sourceJson,
          temp.alarm_time alarmTime,
          if(temp.status = 2, '已处理', if(temp.status = 1, '未处理', '未知')) as   processStatus,
          lu.nickname                                                               processUserName,
          temp.process_result   processResult,
          if(temp.mistake_flag = 1, '是', if(temp.mistake_flag = 0, '否', '未知'))        mistakeFlag,
          temp.process_time                                                          processTime
        FROM (
        SELECT la.device_code, la.content, la.alarm_time, re.`name`, la.`status`,la.level,la.source_json,lap.handler_id,lap.process_result,lap.mistake_flag,lap.process_time
        , ld.area_id,ld.name deviceName,ld.device_unit_id
        FROM (
        SELECT la.device_code, la.content, la.rule_engine_id, alarm_time, `status`,la.level,la.source_json,la.id
        FROM linkapp_alarm la
        WHERE la.device_code IN (
        SELECT device_code
        FROM distribution_cabinet_ref_device
        <where>
            <if test="distributionCabinetAlarmInfo.cabinetId != null and distributionCabinetAlarmInfo.cabinetId != '' ">
                and distribution_cabinet_id = #{distributionCabinetAlarmInfo.cabinetId}
            </if>
        </where>
        )
        <if test="distributionCabinetAlarmInfo.alarmIds != null and distributionCabinetAlarmInfo.alarmIds.size() > 0">
            and la.id in 
            <foreach item="item" collection="distributionCabinetAlarmInfo.alarmIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="distributionCabinetAlarmInfo.tenantId != null and distributionCabinetAlarmInfo.tenantId != ''">
            and la.tenant_id = #{distributionCabinetAlarmInfo.tenantId}
        </if>
        <if test="distributionCabinetAlarmInfo.status != null ">
            and la.status = #{distributionCabinetAlarmInfo.status}
        </if>
        <if test="distributionCabinetAlarmInfo.queryTimeStart != null and distributionCabinetAlarmInfo.queryTimeStart != ''">
            and la.alarm_time &gt;= #{distributionCabinetAlarmInfo.queryTimeStart}
        </if>
        <if test="distributionCabinetAlarmInfo.queryTimeEnd != null and distributionCabinetAlarmInfo.queryTimeEnd != ''">
            and la.alarm_time &lt;= #{distributionCabinetAlarmInfo.queryTimeEnd}
        </if>
        ) la
        LEFT JOIN rule_engine re ON la.rule_engine_id = re.id
        LEFT JOIN linkapp_alarm_process lap on la.id = lap.alarm_id
        LEFT JOIN linkapp_device ld ON la.device_code = ld.`code` AND ld.delete_state = 1
        <where>
            <if test="distributionCabinetAlarmInfo.name != null and distributionCabinetAlarmInfo.name != '' ">
                and re.name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.name}, '%')
            </if>
            <if test="distributionCabinetAlarmInfo.deviceName != null and distributionCabinetAlarmInfo.deviceName != ''">
                and ld.name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.deviceName}, '%')
            </if>
        </where>
        UNION
        SELECT dca.device_code, dca.content, dca.alarm_time, dca.`name`, dca.`status`,dca.level,dca.source_json,dca.user_id handler_id,dca.description process_result,dca.alarm_status mistake_flag,dca.deal_time process_time
        , dr.area_id,ld.name deviceName,ld.device_unit_id
        FROM (
        SELECT device_code, cabinet_id, content, `name`, alarm_time,level,source_json,user_id,description,alarm_status,deal_time
        , `status`
        FROM distribution_cabinet_alarm_info
        <where>
            <if test="distributionCabinetAlarmInfo.ids != null and distributionCabinetAlarmInfo.ids.size() > 0">
                and id in
                <foreach item="item" collection="distributionCabinetAlarmInfo.ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributionCabinetAlarmInfo.cabinetId != null and distributionCabinetAlarmInfo.cabinetId != '' ">
                and cabinet_id = #{distributionCabinetAlarmInfo.cabinetId}
            </if>
            <if test="distributionCabinetAlarmInfo.status != null ">
                and status = #{distributionCabinetAlarmInfo.status}
            </if>
            <if test="distributionCabinetAlarmInfo.name != null and distributionCabinetAlarmInfo.name != '' ">
                and name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.name}, '%')
            </if>
            <if test="distributionCabinetAlarmInfo.queryTimeStart != null and distributionCabinetAlarmInfo.queryTimeStart != ''">
                and alarm_time &gt;= #{distributionCabinetAlarmInfo.queryTimeStart}
            </if>
            <if test="distributionCabinetAlarmInfo.queryTimeEnd != null and distributionCabinetAlarmInfo.queryTimeEnd != ''">
                and alarm_time &lt;= #{distributionCabinetAlarmInfo.queryTimeEnd}
            </if>
        </where>
        ) dca
        LEFT JOIN distribution_cabinet dc ON dca.cabinet_id = dc.id
        LEFT JOIN distribution_room dr ON dc.distribution_room_id = dr.id
        LEFT JOIN linkapp_device ld ON dca.device_code = ld.`code` AND ld.delete_state = 1
        <where>
            <if test="distributionCabinetAlarmInfo.deviceName != null and distributionCabinetAlarmInfo.deviceName != ''">
                and ld.name LIKE CONCAT('%', #{distributionCabinetAlarmInfo.deviceName}, '%')
            </if>
        </where>
        ) temp
        LEFT JOIN linkapp_device_unit u ON temp.device_unit_id = u.id
        LEFT JOIN linkapp_area la ON temp.area_id = la.id
        left join linkapp_user lu on temp.handler_id = lu.id
        <where>
            <if test="distributionCabinetAlarmInfo.areaPath != null and distributionCabinetAlarmInfo.areaPath != ''">
                and (ld.area_path = #{distributionCabinetAlarmInfo.areaPath} or ld.area_path like concat(#{distributionCabinetAlarmInfo.areaPath},':%'))
            </if>
            <if test="distributionCabinetAlarmInfo.deviceTypeId != null and distributionCabinetAlarmInfo.deviceTypeId != ''">
                and u.device_type_id = #{distributionCabinetAlarmInfo.deviceTypeId}
            </if>
            <if test="spacesIds != null and spacesIds.size() > 0">
                and la.space_id in
                <foreach collection="spacesIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY temp.alarm_time DESC
    </select>

    <select id="getDetail" resultMap="com.easylinkin.linkappapi.alarm.mapper.AlarmMapper.BaseResultDetail">
        select
        ca.device_code device_code,
        ca.source_json source_json,
        ca.`status` status,
        ca.level level,
        ca.alarm_time alarm_time,
        ca.content content,
        ca.`name`  ru_name,
        ca.alarm_status mistake_flag,
        ca.description process_result,
        ca.deal_time process_time,
        IFNULL(us.nickname, us.username) p_username,
        d.id                             d_id,
        d.code                           d_code,
        d.name                           d_name,
        d.status                         d_status,
        d.online_state                   d_online_state,
        d.device_unit_id                 d_device_unit_id,
        d.area_id                        d_area_id,
        d.site                           d_site,
        d.area_path                      d_area_path,
        s.id                             s_space_id,
        s.space_name                     s_space_name,
        u.device_type_name               t_name,
        u.device_type_id                 t_id,
        a.area_name                      a_area_name,
        u.name                           u_name,
        u.version                        u_version,
        u.code                           u_code
         from distribution_cabinet_alarm_info ca
        left join linkapp_device d on ca.device_code=d.`code` and d.delete_state = 1
        left join linkapp_user us on us.id = ca.user_id
        LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
        LEFT JOIN linkapp_area a ON a.id = d.area_id
        LEFT JOIN linkapp_space s ON s.id = a.space_id
        where ca.id = #{distributionCabinetAlarmInfo.id}
    </select>
</mapper>