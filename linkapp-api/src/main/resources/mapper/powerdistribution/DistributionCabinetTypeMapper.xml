<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetTypeMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetType">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="description" property="description"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
  </resultMap>


  <select id="getDistributionCabinetTypes" resultMap="BaseResultMap">
    select dct.* from distribution_cabinet_type dct
    <where>
      1 = 1
      <if test='distributionCabinetType.name!=null and distributionCabinetType.name!=""'>
        and dct.name like concat ('%',#{distributionCabinetType.name},'%')
      </if>
      <if test='distributionCabinetType.id!=null and distributionCabinetType.id!=""'>
        and dct.id = #{distributionCabinetType.id}
      </if>
      <if test='distributionCabinetType.tenantId!=null and distributionCabinetType.tenantId!=""'>
        and dct.tenant_id = #{distributionCabinetType.tenantId}
      </if>
    </where>
    order by dct.modify_time desc
  </select>

  <select id="getDistributionCabinetType" resultMap="BaseResultMap">

    select dct.* from distribution_cabinet_type dct where  id = #{id}
  </select>


  <resultMap id="getDistributionCabinetTypeAndCabinetTypeAndCabinetConfiguratinsMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetType">
    <result column="dct_name" property="name"/>
    <result column="dct_description" property="description"/>
    <collection property="distributionCabinetTypeSiteList" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetTypeSite">
      <result column="dcts_site_name" property="siteName"/>
      <result column="dcts_sort_no" property="sortNo"/>
      <result column="dcts_required" property="required"/>
      <result column="dcts_electric_equipment" property="electricEquipment"/>
      <result column="dcts_device_unit_code" property="deviceUnitCode"/>
      <result column="dcts_device_unit_version" property="deviceUnitVersion"/>
      <result column="dects_distribution_cabinet_type_id" property="distributionCabinetTypeId"/>
      <collection property="distributionCabinetConfigurationList" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfiguration">
        <result column="dcc_name" property="name"/>
        <result column="dcc_description" property="description"/>
        <result column="dcc_status_picture" property="statusPicture"/>
        <result column="dcc_distribution_cabinet_type_site_id" property="distributionCabinetTypeSiteId"/>
        <result column="dcc_group_number" property="groupNumber"/>
        <collection property="distributionCabinetConfigurationExpressions" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfigurationExpression">
          <result column="dcce_distribution_cabinet_configuration_id" property="distributionCabinetConfigurationId"/>
          <result column="dcce_device_unit_code" property="deviceUnitCode"/>
          <result column="dcce_device_unit_version" property="deviceUnitVersion"/>
          <result column="dcce_device_attribute_identifier" property="deviceAttributeIdentifier"/>
          <result column="dcce_device_attribute_parent_identifier" property="deviceAttributeParentIdentifier"/>
          <result column="dcce_calculate_sign" property="calculateSign"/>
          <result column="dcce_value" property="value"/>
          <result column="dcce_sort_no" property="sortNo"/>
          <result column="dcce_logic_code" property="logicCode"/>
        </collection>
      </collection>
    </collection>
  </resultMap>

  <select id="getDistributionCabinetTypeAndCabinetTypeAndCabinetConfigurations" resultMap="getDistributionCabinetTypeAndCabinetTypeAndCabinetConfiguratinsMap">
    select dct.name dct_name, dct.description dct_description,
    dcts.site_name dcts_site_name, dcts.sort_no dcts_sort_no, dcts.required dcts_required,dcts.electric_equipment as dcts_electric_equipment, dcts.device_unit_code dcts_device_unit_code, dcts.device_unit_version dcts_device_unit_version, dcts.distribution_cabinet_type_id as dects_distribution_cabinet_type_id,
    dcc.name dcc_name, dcc.description dcc_description, dcc.status_picture dcc_status_picture, dcc.distribution_cabinet_type_site_id dcc_distribution_cabinet_type_site_id, dcc.group_number dcc_group_number,
    dcce.distribution_cabinet_configuration_id as dcce_distribution_cabinet_configuration_id, dcce.device_unit_code dcce_device_unit_code, dcce.device_unit_version dcce_device_unit_version, dcce.device_attribute_identifier dcce_device_attribute_identifier,
    dcce.device_attribute_parent_identifier dcce_device_attribute_parent_identifier, dcce.calculate_sign dcce_calculate_sign, dcce.value dcce_value, dcce.sort_no dcce_sort_no, dcce.logic_code dcce_logic_code
    from distribution_cabinet_type dct
    left join distribution_cabinet_type_site dcts on dct.id = dcts.distribution_cabinet_type_id
    left join distribution_cabinet_configuration dcc on dcts.id = dcc.distribution_cabinet_type_site_id
    left join distribution_cabinet_configuration_expression dcce on dcc.id = dcce.distribution_cabinet_configuration_id
    <where>
      1 = 1
      <if test='distributionCabinetType.name!=null and distributionCabinetType.name!=""'>
        and dct.name like concat ('%',#{distributionCabinetType.name},'%')
      </if>
      <if test='distributionCabinetType.id!=null and distributionCabinetType.id!=""'>
        and dct.id = #{distributionCabinetType.id}
      </if>
      <if test='distributionCabinetType.tenantId!=null and distributionCabinetType.tenantId!=""'>
        and dct.tenant_id = #{distributionCabinetType.tenantId}
      </if>
      <if test='distributionCabinetType.ids!=null and distributionCabinetType.ids.size()>0'>
        and dct.id in
        <foreach collection="distributionCabinetType.ids" open="(" separator="," item="item" close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

</mapper>
