<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetConfigurationMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfiguration">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="description" property="description"/>
    <result column="status_picture" property="statusPicture"/>
    <result column="distribution_cabinet_type_site_id" property="distributionCabinetTypeSiteId"/>
    <result column="group_number" property="groupNumber"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
  </resultMap>

  <!--  查询列表-->
  <select id="getDistributionCabinetConfigurations" resultMap="BaseResultMap">
    select dcc.* from distribution_cabinet_configuration dcc
    <where>
      1 = 1
      <if test="distributionCabinetConfiguration.name!=null and distributionCabinetConfiguration.name!=''">
        and dcc.name like concat ('%',#{distributionCabinetConfiguration.name,jdbcType=VARCHAR},'%')
      </if>
      <if test="distributionCabinetConfiguration.tenantId!=null and distributionCabinetConfiguration.tenantId!=''">
        and dcc.tenant_id = #{distributionCabinetConfiguration.tenantId,jdbcType=VARCHAR}
      </if>
      <if test="distributionCabinetConfiguration.id!=null and distributionCabinetConfiguration.id!=''">
        and dcc.id = #{distributionCabinetConfiguration.id,jdbcType=VARCHAR}
      </if>
      <if test="distributionCabinetConfiguration.groupNumber!=null and distributionCabinetConfiguration.groupNumber!=''">
        and dcc.group_number = #{distributionCabinetConfiguration.groupNumber,jdbcType=VARCHAR}
      </if>
      <if test="distributionCabinetConfiguration.tenantId!=null and distributionCabinetConfiguration.tenantId!=''">
        and dcc.tenant_id = #{distributionCabinetConfiguration.tenantId,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <!--  查询单条-->
  <select id="getDistributionCabinetConfiguration" resultMap="BaseResultMap">
    select dcc.* from distribution_cabinet_configuration dcc where  dcc.id = #{id}
  </select>


  <resultMap id="DistributionCabinetConfigurationWithExpressionsMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfiguration">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="description" property="description"/>
    <result column="status_picture" property="statusPicture"/>
    <result column="distribution_cabinet_type_site_id" property="distributionCabinetTypeSiteId"/>
    <result column="group_number" property="groupNumber"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="distribution_cabinet_type_id" property="distributionCabinetTypeId"/>
    <collection property="distributionCabinetConfigurationExpressions" column="{distributionCabinetConfigurationExpression.distributionCabinetConfigurationId = id }"
      select="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetConfigurationExpressionMapper.getDistributionCabinetConfigurationExpressions"/>
  </resultMap>

  <select id="getDistributionCabinetConfigurationWithExpressions" resultMap="DistributionCabinetConfigurationWithExpressionsMap">
    select
    dcc.*,
    dcts.distribution_cabinet_type_id
    from distribution_cabinet_configuration dcc
    left join distribution_cabinet_type_site dcts on dcts.id = dcc.distribution_cabinet_type_site_id
    <where>
      1 = 1
      <if test="distributionCabinetConfiguration.distributionCabinetTypeSiteId != null and distributionCabinetConfiguration.distributionCabinetTypeSiteId != ''">
        and dcc.distribution_cabinet_type_site_id = #{distributionCabinetConfiguration.distributionCabinetTypeSiteId}
      </if>
      <if test="distributionCabinetConfiguration.distributionCabinetTypeId != null and distributionCabinetConfiguration.distributionCabinetTypeId != ''">
        and dcts.distribution_cabinet_type_id = #{distributionCabinetConfiguration.distributionCabinetTypeId}
      </if>
    </where>
    order by dcc.group_number asc
  </select>

</mapper>
