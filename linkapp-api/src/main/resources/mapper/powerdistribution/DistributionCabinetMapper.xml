<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="distribution_cabinet_type_id" property="distributionCabinetTypeId"/>
    <result column="distribution_room_id" property="distributionRoomId"/>
    <result column="sort_no" property="sortNo"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>

  <select id="getDistributionCabinets" resultMap="BaseResultMap">
    select dc.*
    from distribution_cabinet dc
    <where>
      1 = 1
      <if test='distributionCabinet.name != null and distributionCabinet.name != ""'>
        and dc.name like concat('%', #{distributionCabinet.name}, '%')
      </if>
      <if test='distributionCabinet.id != null and distributionCabinet.id != ""'>
        and dc.id = #{distributionCabinet.id}
      </if>
      <if test='distributionCabinet.distributionRoomId != null and distributionCabinet.distributionRoomId != ""'>
        and dc.distribution_room_id = #{distributionCabinet.distributionRoomId}
      </if>
      <if test='distributionCabinet.tenantId != null and distributionCabinet.tenantId != ""'>
        and dc.tenant_id = #{distributionCabinet.tenantId}
      </if>
      <if test='distributionCabinet.sortNo != null'>
        and dc.sort_no = #{distributionCabinet.sortNo}
      </if>
    </where>
    order by dc.sort_no
  </select>


  <resultMap id="getDistributionCabinetMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="distribution_cabinet_type_id" property="distributionCabinetTypeId"/>
    <result column="distributionCabinetTypeName" property="distributionCabinetTypeName"/>
    <result column="statusPicture" property="statusPicture"/>
    <result column="distribution_room_id" property="distributionRoomId"/>
    <result column="sort_no" property="sortNo"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <collection property="distributionCabinetRefDeviceList" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice">
      <result property="deviceCode" column="dcrd_device_code"/>
      <result property="distributionCabinetTypeSiteId" column="distribution_cabinet_type_site_id"/>
      <result property="distributionCabinetId" column="distribution_cabinet_id"/>
      <result property="distributionCabinetTypeSiteName" column="distributionCabinetTypeSiteName"/>
    </collection>
  </resultMap>

  <select id="getDistributionCabinet" resultMap="getDistributionCabinetMap">
    select dc.*,
           dcrd.device_code                       as dcrd_device_code,
           dcrd.distribution_cabinet_type_site_id as distribution_cabinet_type_site_id,
           dcts.site_name                         as distributionCabinetTypeSiteName
    from distribution_cabinet dc
           left join distribution_cabinet_ref_device dcrd on dcrd.distribution_cabinet_id = dc.id
           left join distribution_cabinet_type_site dcts on dcts.id = dcrd.distribution_cabinet_type_site_id
    <where>
      dc.id = #{id}
    </where>
  </select>

  <!-- 通用查询映射结果 -->
  <resultMap id="getRealTimeDistributionCabinetWithStatusMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="distribution_cabinet_type_id" property="distributionCabinetTypeId"/>
    <result column="distribution_room_id" property="distributionRoomId"/>
    <result column="sort_no" property="sortNo"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <association property="distributionCabinetStatus" javaType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetStatus">
      <result column="group_number" property="groupNumber"/>
      <result column="status_picture" property="statusPicture"/>
      <result column="distribution_cabinet_configuration_id" property="distributionCabinetConfigurationId"/>
      <result column="distribution_cabinet_configuration_name" property="distributionCabinetConfigurationName"/>
    </association>
    <collection property="distributionCabinetRefDeviceList" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice"
      select="getCabinetRefDevicesAndDeviceStatus" column="{distributionCabinet.id=id}"/>
  </resultMap>

  <resultMap id="getCabinetRefDevicesAndDeviceStatusMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice">
    <result property="distributionCabinetTypeSiteId" column="distribution_cabinet_type_site_id"/>
    <result property="electricEquipment" column="dcts_electric_equipment"/>
    <result property="deviceCode" column="dcrd_device_code"/>
    <collection property="deviceAttributeStatusList" ofType="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
      <result column="ldas_deviceCode" property="deviceCode"/>
      <result column="device_unit_id" property="deviceUnitId"/>
      <result column="version" property="version"/>
      <result column="prop_code" property="propCode"/>
      <result column="vo_prop_name" property="propName"/>
      <result column="ico_path" property="icoPath"/>
      <result column="vo_unit" property="unit"/>
      <result column="specs" property="specs"/>
      <result column="sort_no" property="sortNo"/>
      <result column="is_show" property="isShow"/>
      <result column="vo_prop_value" property="propValue"/>
      <result column="vo_parent_prop_code" property="parentPropCode"/>
      <result column="vo_array_Index" property="arrayIndex"/>
    </collection>
  </resultMap>

  <select id="getCabinetRefDevicesAndDeviceStatus" resultMap="getCabinetRefDevicesAndDeviceStatusMap">
    select dcrd.device_code                       as dcrd_device_code,
           dcrd.distribution_cabinet_type_site_id as distribution_cabinet_type_site_id,
           dcrd.device_code                       as dcrd_device_code,
           dcts.electric_equipment                as dcts_electric_equipment,
           ldas.device_code                       as ldas_deviceCode,
           ldas.prop_code,
           ldas.version,
           ldas.prop_value                        AS vo_prop_value,
           ldas.parent_prop_code                  AS vo_parent_prop_code,
           ldas.array_Index                       AS vo_array_Index,
           lda.device_unit_id,
           lda.name                               as vo_prop_name,
           lda.unit                               as vo_unit,
           lda.specs,
           lda.sort_no,
           lda.is_show,
           lda.ico_path
    from distribution_cabinet_ref_device dcrd
           left join distribution_cabinet_type_site dcts on dcts.id = dcrd.distribution_cabinet_type_site_id
           left JOIN linkapp_device ld ON ld.code = dcrd.device_code and ld.delete_state = 1
           left join linkapp_device_unit ldu on ldu.id = ld.device_unit_id
           left join linkapp_device_attribute_status ldas on ldas.device_code = dcrd.device_code and ldas.version = ldu.version
           left JOIN linkapp_device_attribute lda ON lda.device_unit_id = ld.device_unit_id
      AND lda.identifier = ldas.prop_code
    where dcrd.distribution_cabinet_id = #{distributionCabinet.id}
<!--      and lda.is_show = 1 监控大屏处是固定有数据都显示-->
<!--      and ldu.code = 'WJBH-RS485-01-0610' 加了是否电力参数 不需要限制查固定型号-->
    order by dcts.sort_no asc
  </select>

  <!--电力监控页面查看-->
  <select id="getRealTimeDistributionCabinetWithStatus" resultMap="getRealTimeDistributionCabinetWithStatusMap">
    select dc.*,
           dcc.group_number,
           IFNULL(dcc.status_picture, dcc2.status_picture) as status_picture,
           dcc.id                                          as distribution_cabinet_configuration_id,
           dcc.name                                        as distribution_cabinet_configuration_name
    <!--    modify by 2020-8-28 bug-配电房方案-电力监控页面加载时间长 -->
    <!--    ,-->
    <!--    dcrd.device_code as dcrd_device_code,-->
    <!--    dcrd.distribution_cabinet_type_site_id as distribution_cabinet_type_site_id,-->
    <!--    dcrd.device_code as dcrd_device_code,-->
    <!--    dcts.site_name as distributionCabinetTypeSiteName-->

    from distribution_cabinet dc
      left join distribution_cabinet_status dcs on dcs.distribution_cabinet_id = dc.id
      left join distribution_cabinet_configuration dcc on dcc.id = dcs.distribution_cabinet_configuration_id
      left join distribution_cabinet_ref_device dcrd on dcrd.distribution_cabinet_id = dc.id
    <!--    left join distribution_cabinet_type_site dcts on dcts.id = dcrd.distribution_cabinet_type_site_id-->
    <!--    left join linkapp_device ld on ld.code = dcrd.device_code and ld.delete_state = 1-->
    <!--    left join linkapp_device_unit ldu on ldu.id = ld.device_unit_id-->
    <!--    left join linkapp_device_attribute_status ldas on ldas.device_code = dcrd.device_code and ldas.version = ldu.version-->
    left join distribution_cabinet_configuration dcc2 on dcc2.distribution_cabinet_type_site_id = dcrd.distribution_cabinet_type_site_id and dcc2.group_number = 0

    <where>
      <if test='distributionCabinet.distributionRoomId != null and distributionCabinet.distributionRoomId != ""'>
        and dc.distribution_room_id = #{distributionCabinet.distributionRoomId}
      </if>
      <if test='distributionCabinet.name != null and distributionCabinet.name != ""'>
        and dc.name like concat('%', #{distributionCabinet.name}, '%')
      </if>
      <if test='distributionCabinet.tenantId != null and distributionCabinet.tenantId != ""'>
        and dc.tenant_id = #{distributionCabinet.tenantId}
      </if>
      <if test='distributionCabinet.id != null and distributionCabinet.id != ""'>
        and dc.id = #{distributionCabinet.id}
      </if>
    </where>
    order by dc.sort_no
  </select>


  <resultMap id="getDistributionCabinetDetailMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="distribution_cabinet_type_id" property="distributionCabinetTypeId"/>
    <result column="distribution_room_id" property="distributionRoomId"/>
    <result column="sort_no" property="sortNo"/>
    <collection property="distributionCabinetRefDeviceList" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice">
      <result property="distributionCabinetTypeSiteId" column="distribution_cabinet_type_site_id"/>
      <result property="distributionCabinetTypeSiteName" column="distributionCabinetTypeSiteName"/>
      <result property="deviceCode" column="dcrd_device_code"/>
      <result property="deviceUnitCode" column="dcrd_device_unit_code"/>
      <result property="sortNo" column="dcrd_sort_no"/>
      <result property="electricEquipment" column="dcrd_electric_equipment"/>
      <result property="distributionCabinetTypeSiteId" column="distribution_cabinet_type_site_id"/>
      <collection property="deviceAttributeStatusList" ofType="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
        <result column="ldas_deviceCode" property="deviceCode"/>
        <result column="device_unit_id" property="deviceUnitId"/>
        <result column="version" property="version"/>
        <result column="prop_code" property="propCode"/>
        <result column="vo_prop_name" property="propName"/>
        <result column="ico_path" property="icoPath"/>
        <result column="vo_unit" property="unit"/>
        <result column="specs" property="specs"/>
        <result column="lda_sort_no" property="sortNo"/>
        <result column="is_show" property="isShow"/>
        <result column="vo_prop_value" property="propValue"/>
        <result column="vo_parent_prop_code" property="parentPropCode"/>
        <result column="vo_array_Index" property="arrayIndex"/>
      </collection>
    </collection>
  </resultMap>

  <select id="getDistributionCabinetDetail" resultMap="getDistributionCabinetDetailMap">
    select temp.id,
           name,
           distribution_cabinet_type_id,
           distribution_room_id,
           tenant_id,
           sort_no,
           dcrd_device_code,
           distribution_cabinet_type_site_id,
           distributionCabinetTypeSiteName,
           dcrd_sort_no,
           dcrd_electric_equipment,
           dcrd_device_unit_code,
           ldas_deviceCode,
           prop_code,
           version,
           vo_prop_value,
           vo_parent_prop_code,
           vo_array_Index,
           device_unit_id,
           vo_prop_name,
           vo_unit,
           specs,
           is_show,
           lda_sort_no,
           ico_path from(
      select
      dc.id,
      dc.name,
      dc.distribution_cabinet_type_id,
      dc.distribution_room_id,
      dc.tenant_id,
      dc.sort_no,
      dcrd.device_code                       as dcrd_device_code,
      dcrd.distribution_cabinet_type_site_id as distribution_cabinet_type_site_id,
      dcts.site_name                         as distributionCabinetTypeSiteName,
      dcts.sort_no                           as dcrd_sort_no,
      dcts.electric_equipment                as dcrd_electric_equipment,
      ldu.code                               as dcrd_device_unit_code,
      ldas.device_code                          ldas_deviceCode,
      ldas.prop_code,
      ldas.version,
      ldas.prop_value                        AS vo_prop_value,
      ldas.parent_prop_code                  AS vo_parent_prop_code,
      ldas.array_Index                       AS vo_array_Index,
      lda.device_unit_id,
      lda.name                               as vo_prop_name,
      lda.unit                               as vo_unit,
      lda.specs,
    <!--    lda.is_show,-->
    <!--    lda.sort_no as lda_sort_no,-->
    ifnull(ldatc.is_show, lda.is_show) is_show,
    ifnull(ldatc.sort_no, lda.sort_no) lda_sort_no,
    lda.ico_path
      from distribution_cabinet dc
             left join distribution_cabinet_ref_device dcrd on dcrd.distribution_cabinet_id = dc.id
             left join distribution_cabinet_type_site dcts on dcts.id = dcrd.distribution_cabinet_type_site_id
             left JOIN `linkapp_device` ld ON ld.code = dcrd.device_code and ld.delete_state = 1
             left JOIN linkapp_device_unit ldu ON ldu.id = ld.device_unit_id
             left join linkapp_device_attribute_status ldas on ldas.device_code = dcrd.device_code and ldas.version = ldu.version
             left JOIN linkapp_device_attribute lda ON lda.device_unit_id = ld.device_unit_id and lda.identifier = ldas.prop_code
             left join linkapp_device_attribute_tenant_config ldatc on ldatc.device_attribute_id = lda.id and ldatc.tenant_id = dc.tenant_id
        AND lda.identifier = ldas.prop_code
      )temp
    <where>
      temp.id = #{id}
        and temp.is_show = 1
    </where>
  </select>


  <select id="getDistributionCabinetWithRefDevices" resultMap="getDistributionCabinetMap">
    select dc.*,
           dcrd.device_code                       as dcrd_device_code,
           dcrd.distribution_cabinet_type_site_id as distribution_cabinet_type_site_id,
           dcrd.distribution_cabinet_id,
           dcts.site_name                         as distributionCabinetTypeSiteName,
           dct.name                               as distributionCabinetTypeName,
           IFNULL(dcc.status_picture, (
             SELECT dcc2.status_picture
             FROM distribution_cabinet_configuration dcc2
                    INNER JOIN distribution_cabinet_type_site dcts2 ON dcts2.id = dcc2.distribution_cabinet_type_site_id
             WHERE dcts2.distribution_cabinet_type_id = dc.distribution_cabinet_type_id
               AND dcc2.group_number = 0
             LIMIT 0, 1
             )
             )                                    as statusPicture
    from distribution_cabinet dc
           left join distribution_cabinet_ref_device dcrd on dcrd.distribution_cabinet_id = dc.id
           left join distribution_cabinet_type_site dcts on dcts.id = dcrd.distribution_cabinet_type_site_id
           left join distribution_cabinet_type dct on dct.id = dcts.distribution_cabinet_type_id
           left join distribution_cabinet_status dcs on dcs.distribution_cabinet_id = dc.id
           left join distribution_cabinet_configuration dcc on dcc.id = dcs.distribution_cabinet_configuration_id
    <where>
      <if test="distributionCabinet.distributionRoomId != null and distributionCabinet.distributionRoomId != ''">
        dc.distribution_room_id = #{distributionCabinet.distributionRoomId}
      </if>
    </where>
    order by dc.sort_no
  </select>

    <select id="getDistributionCabinetAreaPathInfo" resultType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet">
        SELECT dc.id,la.area_path AS areaPath, dr.`name` AS roomName, dc.`name` AS name, dcts.site_name AS siteName,dr.company_info_id companyId
        FROM (
            SELECT id, `name`, distribution_room_id
            FROM distribution_cabinet
            <where>
                <if test="distributionCabinet.ids != null and distributionCabinet.ids.size() > 0">
                    id IN
                    <foreach item="item" index="index" collection="distributionCabinet.ids" open="(" separator="," close=")">
                          #{item}
                    </foreach>
                </if>
            </where>
        ) dc
        LEFT JOIN (
            SELECT distribution_cabinet_id, distribution_cabinet_type_site_id
            FROM distribution_cabinet_ref_device
            <where>
                <if test="device.code != null and device.code != '' ">
                    device_code = #{device.code}
                </if>
            </where>
        ) dcd
        ON dcd.distribution_cabinet_id = dc.id
        LEFT JOIN distribution_cabinet_type_site dcts ON dcd.distribution_cabinet_type_site_id = dcts.id
        LEFT JOIN distribution_room dr ON dc.distribution_room_id = dr.id
        LEFT JOIN linkapp_area la ON dr.area_id = la.id
    </select>

    <select id="getDistributionCabinetByAttr" resultType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet">
          SELECT dc.id, dc.`name`, dc.distribution_room_id FROM distribution_cabinet dc
          <where>
              <if test="distributionCabinet.tenantId != null and distributionCabinet.tenantId != ''">
                  and dc.tenant_id = #{distributionCabinet.tenantId}
              </if>
              <if test="distributionCabinet.roomIds != null and distributionCabinet.roomIds.size()>0">
                  and dc.distribution_room_id in
                  <foreach item="item" collection="distributionCabinet.roomIds" open="(" separator="," close=")">
                      #{item}
                 </foreach>
              </if>
              <if test="fieldProperties != null and fieldProperties.size() > 0">
                 and EXISTS (
                          SELECT dcd.device_code
                          FROM distribution_cabinet_ref_device dcd
                          WHERE dc.id = dcd.distribution_cabinet_id
                              AND EXISTS (
                              SELECT 1
                              FROM linkapp_device ld
                              LEFT JOIN linkapp_device_attribute da ON ld.device_unit_id = da.device_unit_id
                              WHERE ld.`code` = dcd.device_code
                              AND ld.delete_state = 1
                              <if test="fieldProperties != null and fieldProperties.size() > 0">
                                  and da.identifier in
                                  <foreach item="item" collection="fieldProperties" open="(" separator="," close=")">
                                      #{item}
                                  </foreach>
                              </if>
                      )
                  )
              </if>
          </where>
    </select>
</mapper>
