<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCompanyAlarmContactMidMapper">

  <select id="findAllAlarmContactInCompanyId" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmPersonContact">
        SELECT apc.*
        FROM (
            SELECT alarm_contact_id
            FROM distribution_company_alarm_contact_mid
            <where>
              <if test="companyIds != null and companyIds.size() > 0">
                company_id IN
                <foreach item="item"  collection="companyIds" open="(" separator="," close=")">
                   #{item}
                </foreach>
              </if>
            </where>
        ) dcac
            LEFT JOIN linkapp_alarm_person_contact apc
            ON dcac.alarm_contact_id = apc.id AND apc.delete_state = 1
  </select>
</mapper>
