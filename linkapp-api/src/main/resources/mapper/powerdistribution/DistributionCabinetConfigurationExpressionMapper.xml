<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetConfigurationExpressionMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfigurationExpression">
    <id column="id" property="id"/>
    <result column="distribution_cabinet_configuration_id" property="distributionCabinetConfigurationId"/>
    <result column="device_unit_code" property="deviceUnitCode"/>
    <result column="device_unit_version" property="deviceUnitVersion"/>
    <result column="device_attribute_identifier" property="deviceAttributeIdentifier"/>
    <result column="device_attribute_parent_identifier" property="deviceAttributeParentIdentifier"/>
    <result column="calculate_sign" property="calculateSign"/>
    <result column="value" property="value"/>
    <result column="sort_no" property="sortNo"/>
    <result column="logic_code" property="logicCode"/>
    <association property="deviceAttribute" javaType="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute">
      <id column="attr_id" property="id"/>
      <result column="attr_device_unit_id" property="deviceUnitId"/>
      <result column="attr_identifier" property="identifier"/>
      <result column="attr_unit" property="unit"/>
      <result column="attr_parent_id" property="parentId"/>
      <result column="attr_parent_prop_code" property="parentPropCode"/>
      <result column="attr_parent_unit" property="parentUnit"/>
    </association>
  </resultMap>


  <select id="getDistributionCabinetConfigurationExpressions" resultMap="BaseResultMap">
    select dcce.*,
    attr.id as attr_id,
    attr.device_unit_id as attr_device_unit_id,
    attr.unit as attr_unit,
    attr.parent_id as attr_parent_id,
    attr2.identifier as attr_parent_prop_code,
    attr2.unit as attr_parent_unit,
    attr.identifier as attr_identifier
    from distribution_cabinet_configuration_expression dcce
    left join linkapp_device_unit ldu on ldu.code = dcce.device_unit_code and ldu.version = dcce.device_unit_version
    LEFT JOIN linkapp_device_attribute attr on attr.device_unit_id = ldu.id and attr.identifier = dcce.device_attribute_identifier
    LEFT JOIN linkapp_device_attribute attr2 on attr2.id = attr.parent_id
    <where>
      1=1
      <if test="distributionCabinetConfigurationExpression.distributionCabinetConfigurationId != null and distributionCabinetConfigurationExpression.distributionCabinetConfigurationId != ''">
        and dcce.distribution_cabinet_configuration_id = #{distributionCabinetConfigurationExpression.distributionCabinetConfigurationId }
      </if>
    </where>
    order by dcce.sort_no asc
  </select>
  
  <select id="getDistributionCabinetConfigurationExpressionsByTenant" resultMap="BaseResultMap">
    SELECT 
	  b.* 
	FROM
	  distribution_cabinet_configuration a 
	  LEFT JOIN distribution_cabinet_configuration_expression b ON a.id = b.distribution_cabinet_configuration_id 
    <where>
      <if test="tenantId != null and tenantId != ''">
        and a.tenant_id = #{tenantId} 
      </if>
      <if test="deviceUnitCode != null and deviceUnitCode != ''">
        and b.device_unit_code = #{deviceUnitCode} 
      </if>
    </where>
  </select>

</mapper>
