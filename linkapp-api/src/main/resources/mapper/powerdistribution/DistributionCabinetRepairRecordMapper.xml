<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetRepairRecordMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairRecord">
    <!--@mbg.generated-->
    <!--@Table distribution_cabinet_repair_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="repair_time" jdbcType="TIMESTAMP" property="repairTime" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="cabinet_id" jdbcType="VARCHAR" property="cabinetId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, content, repair_time, `status`, cabinet_id, create_time
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into distribution_cabinet_repair_record
    (`name`, content, repair_time, `status`, cabinet_id, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.content,jdbcType=VARCHAR}, #{item.repairTime,jdbcType=TIMESTAMP}, 
        #{item.status,jdbcType=BOOLEAN}, #{item.cabinetId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2021-09-16-->
  <select id="findAllByCabinetId" resultMap="BaseResultMapDetails">
        select
        <include refid="Base_Column_List"/>
        ,case when status = 0 then '异常' else '正常' end statusName
        ,DATE_FORMAT(repair_time,'%Y-%m-%d %H:%i:%s') repairTimeStr
        from distribution_cabinet_repair_record
        where cabinet_id=#{distributionCabinetRepairRecord.cabinetId,jdbcType=VARCHAR}
        <if test="distributionCabinetRepairRecord.name != null and distributionCabinetRepairRecord.name != ''">
          AND name  like CONCAT('%',#{distributionCabinetRepairRecord.name},'%')
        </if>
        <if test="distributionCabinetRepairRecord.status != null">
          and status = #{distributionCabinetRepairRecord.status}
        </if>
        <if test="distributionCabinetRepairRecord.queryStartTime != null">
          and repair_time <![CDATA[>=]]> #{distributionCabinetRepairRecord.queryStartTime}
        </if>
        <if test="distributionCabinetRepairRecord.queryEndTime != null">
          and repair_time <![CDATA[<=]]> #{distributionCabinetRepairRecord.queryEndTime}
        </if>
        <if test="distributionCabinetRepairRecord.ids != null and distributionCabinetRepairRecord.ids.size() > 0">
            and id in 
            <foreach item="item"  collection="distributionCabinetRepairRecord.ids" open="(" separator="," close=")">
                 #{item}
            </foreach>
        </if>
      order by create_time desc,repair_time desc
  </select>


  <resultMap id="BaseResultMapDetails" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairRecord">
    <!--@mbg.generated-->
    <!--@Table distribution_cabinet_repair_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="repair_time" jdbcType="TIMESTAMP" property="repairTime" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="cabinet_id" jdbcType="VARCHAR" property="cabinetId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <collection property="distributionCabinetRepairFileList" column="id" select="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetRepairFileMapper.findAllByCabinetRepairId" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairFile"/>
  </resultMap>
<!--auto generated by MybatisCodeHelper on 2021-09-16-->
  <select id="findByIdOrderByCreateTimeDesc" resultMap="BaseResultMapDetails">
    select
    <include refid="Base_Column_List"/>
    from distribution_cabinet_repair_record
    <where>
      <if test="id != null">
        and id=#{id,jdbcType=INTEGER}
      </if>
    </where>
    order by create_time desc
  </select>
</mapper>