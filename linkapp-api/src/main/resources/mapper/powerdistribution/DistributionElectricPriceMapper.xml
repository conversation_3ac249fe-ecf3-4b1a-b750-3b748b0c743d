<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionElectricPriceMapper">
    <resultMap id="getBaseResultMapById"
               type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPrice">
        <!--@mbg.generated-->
        <!--@Table distribution_electric_price-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <collection property="distributionElectricPriceConfigList"
                    select="findAllByElectricPriceId"
                    column="{electricPriceId=id}">
        </collection>
    </resultMap>

    <resultMap id="getDistributionElectricPriceConfigListMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPriceConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="month" jdbcType="VARCHAR" property="month"/>
        <result column="type" jdbcType="BOOLEAN" property="type"/>
        <result column="electric_price_id" jdbcType="INTEGER" property="electricPriceId"/>
        <collection property="distributionElectricPriceConfigTimeList"
                    select="findAllByElectricPriceConfigId"
                    column="{electricPriceConfigId=id}"
                    ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPriceConfigTime">
            <id column="id" jdbcType="INTEGER" property="id"/>
            <result column="start_time" jdbcType="TIME" property="startTime"/>
            <result column="end_time" jdbcType="TIME" property="endTime"/>
            <result column="price" jdbcType="DECIMAL" property="price"/>
            <result column="electric_price_config_id" jdbcType="INTEGER" property="electricPriceConfigId"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, description, tenant_id, create_time, modify_time, creator, modifier
    </sql>

    <!--auto generated by MybatisCodeHelper on 2021-09-02-->
    <select id="findAllById" resultMap="getBaseResultMapById">
        select
        <include refid="Base_Column_List"/>
        from distribution_electric_price
        where id=#{id,jdbcType=INTEGER}
    </select>

    <select id="findAllByElectricPriceId" resultMap="getDistributionElectricPriceConfigListMap">
        select
        id, `month`, `type`, electric_price_id
        from distribution_electric_price_config
        where electric_price_id=#{electricPriceId,jdbcType=INTEGER}
    </select>

    <select id="findAllByElectricPriceConfigId" resultType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPriceConfigTime">
        select
        id, start_time, end_time, price, electric_price_config_id,type
        from distribution_electric_price_config_time
        where electric_price_config_id=#{electricPriceConfigId,jdbcType=INTEGER}
    </select>

    <select id="findAllByCabinetId" resultMap="getBaseResultMapById">
        select dep.* from (
          select distribution_room_id from  distribution_cabinet where id=#{cabinetId,jdbcType=VARCHAR}
        ) dc
        left join distribution_room dr on dc.distribution_room_id=dr.id
        left join distribution_electric_price dep on dr.electric_price_id=dep.id
    </select>
</mapper>