<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetArchivesMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetArchives">
    <!--@mbg.generated-->
    <!--@Table distribution_cabinet_archives-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="dom" jdbcType="TIMESTAMP" property="dom" />
    <result column="rated_capacity" jdbcType="VARCHAR" property="ratedCapacity" />
    <result column="rated_voltage" jdbcType="VARCHAR" property="ratedVoltage" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="cabinet_id" jdbcType="VARCHAR" property="cabinetId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, manufacturer, dom, rated_capacity, rated_voltage, address, remarks, cabinet_id
  </sql>

  <resultMap id="getDistributionCabinetArchivesVoMap" type="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetArchivesVo">
    <collection property="distributionCabinetArchives" column="cabinetId" select="findAllByCabinetId"/>
    <collection property="distributionCabinetAlarmConfigList" column="cabinetId" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmConfig" select="findCabinetDeviceAlarmConfigAllCabinetId"/>
    <collection property="distributionCabinetFileList" column="cabinetId" ofType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetFile" select="findAllFileByCabinetId"/>
  </resultMap>
  
  <select id="getDistributionCabinetArchives" resultMap="getDistributionCabinetArchivesVoMap">
    SELECT  dc.id AS cabinetId
    , dc.name AS cabinetName, dcy.`name` AS cabinetTypeName, dr.`name` AS roomName
	, SUBSTRING_INDEX(la.area_path, ':', 1) AS spaceName
	, la.area_path AS areaPath, ci.company_name AS companyName, dep.`name` AS priceName
    FROM (
        SELECT id, `name`, distribution_cabinet_type_id, distribution_room_id
        FROM distribution_cabinet
        WHERE id = #{distributionCabinetArchives.cabinetId}
    ) dc
        LEFT JOIN distribution_cabinet_type dcy ON dc.distribution_cabinet_type_id = dcy.id
        LEFT JOIN distribution_room dr ON dc.distribution_room_id = dr.id
        LEFT JOIN linkapp_area la ON dr.area_id = la.id
        LEFT JOIN distribution_company_info ci ON dr.company_info_id = ci.id
        LEFT JOIN distribution_electric_price dep ON dr.electric_price_id = dep.id
  </select>

<!--auto generated by MybatisCodeHelper on 2021-09-13-->
  <select id="findAllByCabinetId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from distribution_cabinet_archives
    where cabinet_id=#{cabinetId,jdbcType=VARCHAR}
  </select>
  
  <select id="findCabinetDeviceAlarmConfigAllCabinetId" resultType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmConfig">
        select cac.*,ld.`name` from distribution_cabinet_alarm_config cac
        left join linkapp_device ld on cac.device_code=ld.`code` and ld.delete_state = 1
        where cac.cabinet_id = #{cabinetId}
  </select>
    
    <select id="getDeviceAttrIsExistAoib" resultType="java.lang.Integer">
            select  count(1)
            from linkapp_device_attribute_status ldas
            LEFT JOIN linkapp_device d ON ldas.device_code = d.code AND d.delete_state = 1
            LEFT JOIN (
            SELECT id,is_show,device_unit_id, GROUP_CONCAT(identifier) AS identifier
            FROM linkapp_device_attribute
            GROUP BY device_unit_id
            HAVING
            ((LOCATE('a_phase_current', identifier) > 0
            AND LOCATE('b_phase_current', identifier) > 0
            AND LOCATE('c_phase_current', identifier) > 0)
            OR (LOCATE('a_phase_voltage', identifier) > 0
            AND LOCATE('b_phase_voltage', identifier) > 0
            AND LOCATE('c_phase_voltage', identifier) > 0))
            ) lda ON lda.device_unit_id = d.device_unit_id AND ldas.prop_code = lda.identifier
            left join linkapp_device_attribute_tenant_config ldatc on ldatc.device_attribute_id = lda.id and ldatc.tenant_id = d.tenant_id
            LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
            WHERE ldas.version = u.version
            AND ldas.device_code = #{deviceCode}
            and ifnull(ldatc.is_show, lda.is_show)=1


<!--        SELECT count(1)
                FROM (
                        SELECT ld.`code`, ld.device_unit_id
                        FROM linkapp_device ld
                        WHERE ld.delete_state = 1
                              AND ld.`code`=#{deviceCode}
                ) ld
                LEFT JOIN (
                        SELECT device_unit_id, GROUP_CONCAT(identifier) AS identifier
                        FROM linkapp_device_attribute
                        GROUP BY device_unit_id
                        HAVING
                            ((LOCATE('a_phase_current', identifier) > 0
                                AND LOCATE('b_phase_current', identifier) > 0
                                AND LOCATE('c_phase_current', identifier) > 0)
                                OR (LOCATE('a_phase_voltage', identifier) > 0
                                AND LOCATE('b_phase_voltage', identifier) > 0
                                AND LOCATE('c_phase_voltage', identifier) > 0))
                ) da
                ON da.device_unit_id = ld.device_unit_id-->
    </select>

    <select id="getDeviceAttrIsExistLoad" resultType="java.lang.Integer">
        select  count(1)
        from linkapp_device_attribute_status ldas
        LEFT JOIN linkapp_device d ON ldas.device_code = d.code AND d.delete_state = 1
        LEFT JOIN (
        SELECT id,is_show,device_unit_id, GROUP_CONCAT(identifier) AS identifier
        FROM linkapp_device_attribute
        GROUP BY device_unit_id
        HAVING LOCATE('apparent_power', identifier) > 0
        ) lda ON lda.device_unit_id = d.device_unit_id AND ldas.prop_code = lda.identifier
        left join linkapp_device_attribute_tenant_config ldatc on ldatc.device_attribute_id = lda.id and ldatc.tenant_id = d.tenant_id
        LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
        WHERE ldas.version = u.version
        AND ldas.device_code = #{deviceCode}
        and ifnull(ldatc.is_show, lda.is_show)=1


<!--                SELECT count(1)
                FROM (
                        SELECT ld.`code`, ld.device_unit_id
                        FROM linkapp_device ld
                        WHERE ld.delete_state = 1
                              AND ld.`code`=#{deviceCode}
                ) ld
                LEFT JOIN (
                        SELECT device_unit_id, GROUP_CONCAT(identifier) AS identifier
                        FROM linkapp_device_attribute
                        GROUP BY device_unit_id
                        HAVING LOCATE('apparent_power', identifier) > 0
                ) da
                ON da.device_unit_id = ld.device_unit_id-->
    </select>

    <select id="findAllFileByCabinetId" resultType="com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetFile">
        select dcf.file_name,dcf.file_url from distribution_cabinet_file dcf where dcf.cabinet_id=#{cabinetId}
    </select>
</mapper>