<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.childhospital.mapper.DormitoryManageUserMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.childhospital.entity.EmpUserBase">
        <result column="id" property="id"/>
        <result column="emp_uid_" property="empUid"/>
        <result column="name_" property="name"/>
        <result column="gender_" property="gender"/>
        <result column="telephone_" property="telephone"/>
        <result column="birthday_" property="birthday"/>
        <result column="card_" property="card"/>
        <result column="photo_" property="photo"/>
        <result column="employ_status_" property="employStatus"/>
        <result column="work_type_" property="workType"/>
        <result column="company_id_" property="companyId"/>
        <result column="company_name" property="companyName"/>
    </resultMap>

    <sql id="base_column">
        a.id,a.emp_uid_,a.name_,a.gender_,a.telephone_,a.birthday_,a.card_,a.employ_status_,a.work_type_,a.company_id_,a.photo_
    </sql>

    <!--查询人员花名册-->
    <select id="realNameCheck" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.childhospital.vo.IDCardVo">
        select
            <include refid="base_column"></include>, b.name_ as company_name
        from emp_user_base a
        left join app_labor_company b on a.company_id_ = b.id
        inner join app_user_project c on c.user_id_ = a.id
        where a.card_ = #{number} and a.name_ = #{name}
        and c.status_ = 1 and c.delete_state_ = 1
    </select>

    <update id="updateCheckStatusById" parameterType="java.lang.String" >
        update child_hospital_user_detail set status = 0 where id = #{userId}
    </update>

    <select id="selectUseRoomAndUser" resultType="java.util.Map">
        select count(DISTINCT a.room_id) AS roomCount, COUNT(*) as userCount from child_hospital_user_detail a
        <if test="floorId!=null and floorId!=''">
            INNER JOIN child_hospital_room b on a.room_id = b.id
        </if>
        where a.status = 1
        <if test="floorId!=null and floorId!=''">
            AND b.floor_id = #{floorId}
        </if>
    </select>

</mapper>
