<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.childhospital.mapper.DormitoryManageBuildingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.childhospital.entity.DormitoryDetail">
        <result column="id" property="id"/>
        <result column="buildId" property="buildId"/>
        <result column="buildingName" property="buildingName"/>
        <result column="floorId" property="floorId"/>
        <result column="floorName" property="floorName"/>
        <result column="roomId" property="roomId"/>
        <result column="roomName" property="roomName"/>
        <result column="roomNumber" property="roomNumber"/>
        <result column="roomUsedNumber" property="roomUsedNumber"/>
        <result column="roomLabel" property="roomLabel"/>
        <result column="sex" property="sex"/>
        <result column="roomStatus" property="roomStatus"/>
    </resultMap>

    <select id="getDormitoryDetail" resultMap="BaseResultMap">
        select
            a.id as id,a.building_name as buildingName,b.id as floorId,b.floor_name as floorName,c.id as roomId,
            c.room_name as roomName,c.room_number as roomNumber,c.room_used_number as roomUsedNumber,
            c.room_label as roomLabel,c.sex,c.room_status as roomStatus
        from child_hospital_building a
        LEFT JOIN child_hospital_floor b on a.id = b.building_id
        LEFT JOIN child_hospital_room c on b.id = c.floor_id
        WHERE 1=1 and a.tenant_id = #{tenantId}
        <if test='entity.buildingId!=null and entity.buildingId!="" '>
            and a.id = #{entity.buildingId}
        </if>
        <if test='entity.floorId!=null and entity.floorId!="" '>
            and b.id = #{entity.floorId}
        </if>
        <if test='entity.status!=null and entity.status!="" '>
            <if test='entity.status == 1'>
                and c.room_number = c.room_used_number
            </if>
            <if test='entity.status > 0'>
                and c.room_number = c.room_used_number
            </if>
        </if>
        <if test='entity.sex!=null and entity.sex !=""'>
            and c.sex = #{entity.sex}
        </if>
        ORDER BY a.building_name,b.floor_name,c.room_name
    </select>

    <resultMap id="UserMap" type="com.easylinkin.linkappapi.childhospital.entity.DormitoryManageUser">
        <result column="id" property="id"/>
        <result column="room_id" property="roomId"/>
        <result column="user_name" property="userName"/>
        <result column="user_age" property="userAge"/>
        <result column="identification_number" property="identificationNumber"/>
        <result column="organization" property="organization"/>
        <result column="sex" property="sex"/>
        <result column="smoking" property="smoking"/>
        <result column="snore" property="snore"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="out_time" property="outTime"/>
        <result column="image" property="image"/>
        <result column="work_type" property="workType"/>
    </resultMap>

    <sql id="Base_Column_list">
        id,room_id,user_name,user_age,identification_number,organization,sex,smoking,snore,status,create_time,out_time,image,work_type
    </sql>

    <select id="getDormitoryUser" parameterType="list" resultMap="UserMap" >
        select <include refid="Base_Column_list"></include> from child_hospital_user_detail where status = 1
        and room_id in
        <choose>
            <when test="list.size == 0">
                (null)
            </when>
            <when test="list!=null and list.size()>0">
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
        </choose>
    </select>

    <select id="getCheckHistoryUser"  resultMap="UserMap" >
        select <include refid="Base_Column_list"></include> from child_hospital_user_detail
        where room_id = #{roomId}
        <if test='keywords!=null and keywords !=""'>
            and (user_name like concat('%', #{keywords}, '%')
                     or identification_number like concat('%', #{keywords}, '%')
                     or organization like concat('%', #{keywords}, '%'))
        </if>
        order by status desc,create_time desc
    </select>

    <select id="getCountHistory" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(*) from child_hospital_user_detail where room_id = #{roomId}
    </select>

    <select id="getCurrentUser"  resultMap="UserMap" >
        select <include refid="Base_Column_list"></include> from child_hospital_user_detail
        where status = 1 and room_id = #{roomId}
    </select>
</mapper>
