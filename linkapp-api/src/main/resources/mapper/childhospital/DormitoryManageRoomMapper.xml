<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.childhospital.mapper.DormitoryManageRoomMapper">

    <insert id="insertBatch"  parameterType="com.easylinkin.linkappapi.childhospital.entity.DormitoryManageRoom">
        insert into child_hospital_room
        (id,floor_id,room_name,room_number,room_used_number,room_label,sex,room_status,create_time,modify_time,tenant_id,creator,modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.floorId},#{item.roomName},#{item.roomNumber},#{item.roomUsedNumber},#{item.roomLabel},
             #{item.sex},#{item.roomStatus},#{item.createTime},#{item.modifyTime},#{item.tenantId},#{item.creator},#{item.modifier})
        </foreach>
    </insert>

    <update id="updateRoomUsedNumber" >
        update child_hospital_room set room_used_number = room_used_number + #{number} where id = #{roomId}
    </update>

    <select id="selectRoomCount" resultType="java.util.Map">
        select count(*) as roomCount, SUM(room_number) as bedCount from child_hospital_room
        <if test="roomId!=null and roomId!=''">
            where floor_id = #{roomId}
        </if>
    </select>

</mapper>
