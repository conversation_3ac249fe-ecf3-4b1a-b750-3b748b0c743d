<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.childhospital.mapper.DormitoryManageFloorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.childhospital.entity.DormitoryManageFloor">
        <result column="id" property="id"/>
        <result column="building_name" property="buildingName"/>
        <result column="floor_name" property="floorName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
    </resultMap>

    <select id="selectDormitoryManage" resultType="com.easylinkin.linkappapi.childhospital.vo.DormitoryManage">
        SELECT
            chf.id AS id,
            chf.building_name AS buildingName,
            chf.floor_name AS floor_name,
            chf.create_time AS createTime,
            chf.update_time AS updateTime
        FROM
            child_hospital_floor chf
        INNER JOIN child_hospital_room chr ON chf.id = chr.floor_id
        WHERE 1=1
        <if test='dormitoryManage.buildingName!=null and dormitoryManage.buildingName!="" '>
            and chf.building_name like CONCAT('%', #{dormitoryManage.buildingName},'%')
        </if>
        <if test='dormitoryManage.floorName!=null and dormitoryManage.floorName!="" '>
            and chf.floor_name like CONCAT('%', #{dormitoryManage.floorName},'%')
        </if>
        <if test='dormitoryManage.sex!=null'>
            and chf.sex = #{dormitoryManage.sex}
        </if>
    </select>

    <insert id="insertBatch"  parameterType="com.easylinkin.linkappapi.childhospital.entity.DormitoryManageFloor">
        insert into child_hospital_floor
        (id,building_name,floor_name,create_time,modify_time,tenant_id,creator,modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.buildingName},#{item.floorName},#{item.createTime},#{item.modifyTime},#{item.creator},#{item.creator},#{item.modifier})
        </foreach>
        ON DUPLICATE KEY UPDATE
        enterprise_id=values(enterprise_id),
        risk_type_id=values(risk_type_id),
        tenant_id=values(tenant_id)
        ,number=values(number)
        ,modify_time=values(modify_time)
        ,modifier=values(modifier);
    </insert>

    <select id="selectFloorCount" resultType="java.lang.Integer">
        select MAX(count) as count FROM (select building_id,count(*) as count from child_hospital_floor group by building_id) a
    </select>


</mapper>
