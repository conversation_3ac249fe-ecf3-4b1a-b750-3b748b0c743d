<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.jglplant.dao.JglExpertResearchAreaDao">
    <resultMap type="com.easylinkin.linkappapi.jglplant.entity.JglExpertResearchArea" id="JglExpertResearchAreaMap">
        <result property="expertId" column="expert_id" jdbcType="INTEGER"/>
        <result property="type1" column="type1" jdbcType="VARCHAR"/>
        <result property="type2" column="type2" jdbcType="VARCHAR"/>
        <result property="type3" column="type3" jdbcType="VARCHAR"/>
        <result property="type4" column="type4" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="" useGeneratedKeys="true">
        insert into jgl_expert_research_area(expert_id, type1, type2, type3, type4)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.expertId}, #{entity.type1}, #{entity.type2}, #{entity.type3}, #{entity.type4})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="" useGeneratedKeys="true">
        insert into jgl_expert_research_area(expert_id, type1, type2, type3, type4)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.expertId}, #{entity.type1}, #{entity.type2}, #{entity.type3}, #{entity.type4})
        </foreach>
        on duplicate key update
        expert_id = values(expert_id) , type1 = values(type1) , type2 = values(type2) , type3 = values(type3) , type4 =
        values(type4)
    </insert>

</mapper>

