<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.jglplant.dao.JglExpertDao">
    <resultMap type="com.easylinkin.linkappapi.jglplant.entity.JglExpert" id="JglExpertMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="INTEGER"/>
        <result property="company" column="company" jdbcType="VARCHAR"/>
        <result property="job" column="job" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="studyPlant" column="study_plant" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into jgl_expert(name, gender, company, job, avatar, study_plant, remark, tenant_id,
                               create_time, modify_time, creator, modifier)
                values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.gender}, #{entity.company}, #{entity.job}, #{entity.avatar},
             #{entity.study_plant}, #{entity.remark}, #{entity.tenantId}, #{entity.createTime}, #{entity.modifyTime},
             #{entity.creator}, #{entity.modifier})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into jgl_expert(name, gender, company, job, avatar, study_plant, remark, tenant_id,
                               create_time, modify_time, creator, modifier)
                values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.gender}, #{entity.company}, #{entity.job}, #{entity.avatar},
             #{entity.study_plant}, #{entity.remark}, #{entity.tenantId}, #{entity.createTime}, #{entity.modifyTime},
             #{entity.creator}, #{entity.modifier})
        </foreach>
        on duplicate key update
                                 name =
                         values (name), gender =
                         values (gender), company =
                         values (company), job =
                         values (job), avatar =
                         values (avatar), study_plant =
                         values (study_plant), remark =
                         values (remark), tenant_id =
                         values (tenant_id),
                                 create_time =
                         values (create_time), modify_time =
                         values (modify_time), creator =
                         values (creator), modifier =
                         values (modifier)
    </insert>

    <resultMap id="JglExpertWithResearchAreaMap" type="com.easylinkin.linkappapi.jglplant.entity.JglExpert">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="INTEGER"/>
        <result property="company" column="company" jdbcType="VARCHAR"/>
        <result property="job" column="job" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="studyPlant" column="study_plant" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <collection property="jglExpertResearchAreaList"
                    ofType="com.easylinkin.linkappapi.jglplant.entity.JglExpertResearchArea">
            <result property="expertId" column="expert_id" jdbcType="INTEGER"/>
            <result property="type1" column="type1" jdbcType="VARCHAR"/>
            <result property="type2" column="type2" jdbcType="VARCHAR"/>
            <result property="type3" column="type3" jdbcType="VARCHAR"/>
            <result property="type4" column="type4" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>


    <sql id="getListCondition">
        je.tenant_id = #{jglExpert.tenantId}
        <if test="jglExpert.name != null">
            and je.name LIKE CONCAT('%', #{jglExpert.name}, '%')
        </if>
        <if test="jglExpert.studyPlant != null">
            and je.study_plant LIKE CONCAT('%', #{jglExpert.studyPlant}, '%')
        </if>
        <if test="jglExpert.company != null">
            and je.company LIKE CONCAT('%', #{jglExpert.company}, '%')
        </if>
    </sql>
    <sql id="condition2">
        <if test="jglExpert.type1 != null">
            and jera.type1 = #{jglExpert.type1}
        </if>
        <if test="jglExpert.type2 != null">
            and jera.type2 = #{jglExpert.type2}
        </if>
        <if test="jglExpert.type3 != null">
            and jera.type3 = #{jglExpert.type3}
        </if>
        <if test="jglExpert.type4 != null">
            and jera.type4 = #{jglExpert.type4}
        </if>
    </sql>

    <select id="getPageList" resultMap="JglExpertWithResearchAreaMap">
        select je2.*, jera2.type1, jera2.type2, jera2.type3, jera2.type4
                from
                ( select distinct je.id
                from jgl_expert je

        <if test="jglExpert.type1 != null or jglExpert.type2 != null or jglExpert.type3 != null or jglExpert.type4 != null">
            inner join jgl_expert_research_area jera on je.id = jera.expert_id

            <include refid="condition2"/>
        </if>
        where
        <include refid="getListCondition"/>
        order by je.modify_time desc, je.create_time desc
        <if test="size > 0">
            limit #{start}, #{size}
        </if>) temp
            left join jgl_expert je2 on je2.id = temp.id
            left join jgl_expert_research_area jera2 on je2.id = jera2.expert_id
            order by je2.modify_time desc, je2.create_time desc
    </select>


    <select id="getPageCount" resultType="java.lang.Long">
        select count(distinct je.id)
                from jgl_expert je

        <if test="jglExpert.type1 != null or jglExpert.type2 != null or jglExpert.type3 != null or jglExpert.type4 != null">
            inner join jgl_expert_research_area jera on je.id = jera.expert_id

            <include refid="condition2"/>
        </if>
        where
        <include refid="getListCondition"/>
    </select>

    <select id="getOneById" resultMap="JglExpertWithResearchAreaMap">
        select je2.*, jera2.type1, jera2.type2, jera2.type3, jera2.type4
        from jgl_expert je2
                     left join jgl_expert_research_area jera2 on je2.id = jera2.expert_id
        <where>
            je2.id = #{id}
        </where>
    </select>

</mapper>

