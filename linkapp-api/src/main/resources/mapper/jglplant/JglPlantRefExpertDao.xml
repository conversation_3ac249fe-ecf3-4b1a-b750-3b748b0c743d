<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.jglplant.dao.JglPlantRefExpertDao">
    <resultMap type="com.easylinkin.linkappapi.jglplant.entity.JglPlantRefExpert" id="JglPlantRefExpertMap">
        <result property="expertId" column="expert_id" jdbcType="INTEGER"/>
        <result property="plantId" column="plant_id" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="" useGeneratedKeys="true">
        insert into jgl_plant_ref_expert(expert_id, plant_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.expertId}, #{entity.plantId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="" useGeneratedKeys="true">
        insert into jgl_plant_ref_expert(expert_id, plant_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.expertId}, #{entity.plantId})
        </foreach>
        on duplicate key update
        expert_id = values(expert_id) , plant_id = values(plant_id)
    </insert>
</mapper>

