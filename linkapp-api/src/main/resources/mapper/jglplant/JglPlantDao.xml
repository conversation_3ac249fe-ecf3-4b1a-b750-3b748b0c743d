<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.jglplant.dao.JglPlantDao">
    <resultMap type="com.easylinkin.linkappapi.jglplant.entity.JglPlant" id="JglPlantMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type1" column="type1" jdbcType="VARCHAR"/>
        <result property="type2" column="type2" jdbcType="VARCHAR"/>
        <result property="type3" column="type3" jdbcType="VARCHAR"/>
        <result property="type4" column="type4" jdbcType="VARCHAR"/>
        <result property="image" column="image" jdbcType="VARCHAR"/>
        <result property="feature" column="feature" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="attachment" column="attachment" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
    </resultMap>


    <resultMap type="com.easylinkin.linkappapi.jglplant.entity.JglPlant" id="JglPlantWithExpertMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type1" column="type1" jdbcType="VARCHAR"/>
        <result property="type2" column="type2" jdbcType="VARCHAR"/>
        <result property="type3" column="type3" jdbcType="VARCHAR"/>
        <result property="type4" column="type4" jdbcType="VARCHAR"/>
        <result property="image" column="image" jdbcType="VARCHAR"/>
        <result property="feature" column="feature" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="attachment" column="attachment" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <collection property="jglExpertList" ofType="com.easylinkin.linkappapi.jglplant.entity.JglExpert">
            <result property="id" column="je_id" jdbcType="INTEGER"/>
            <result property="name" column="je_name" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="INTEGER"/>
            <result property="company" column="company" jdbcType="VARCHAR"/>
            <result property="job" column="job" jdbcType="VARCHAR"/>
            <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
            <result property="studyPlant" column="study_plant" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="tenantId" column="je_tenant_id" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into jgl_plant(name, type1, type2, type3, type4, image, feature, area, attachment,
        tenant_id, create_time, modify_time, creator, modifier)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.type1}, #{entity.type2}, #{entity.type3}, #{entity.type4}, #{entity.image},
            #{entity.feature}, #{entity.area}, #{entity.attachment}, #{entity.tenantId}, #{entity.createTime},
            #{entity.modifyTime}, #{entity.creator}, #{entity.modifier})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into jgl_plant(name, type1, type2, type3, type4, image, feature, area, attachment,
        tenant_id, create_time, modify_time, creator, modifier)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.type1}, #{entity.type2}, #{entity.type3}, #{entity.type4}, #{entity.image},
            #{entity.feature}, #{entity.area}, #{entity.attachment}, #{entity.tenantId}, #{entity.createTime},
            #{entity.modifyTime}, #{entity.creator}, #{entity.modifier})
        </foreach>
        on duplicate key update
        name = values(name) , type1 = values(type1) , type2 = values(type2) , type3 = values(type3) , type4 =
        values(type4) , image = values(image) , feature = values(feature) , area = values(area) , attachment =
        values(attachment) , tenant_id = values(tenant_id) , create_time = values(create_time) , modify_time =
        values(modify_time) , creator = values(creator) , modifier = values(modifier)
    </insert>


    <sql id="getListCondition">
        jp.tenant_id = #{jglPlant.tenantId}
        <if test="jglPlant.type1!=null">
            and jp.type1 = #{jglPlant.type1}
        </if>
        <if test="jglPlant.type2!=null">
            and jp.type2 = #{jglPlant.type2}
        </if>
        <if test="jglPlant.type3!=null">
            and jp.type3 = #{jglPlant.type3}
        </if>
        <if test="jglPlant.type4!=null">
            and jp.type4 = #{jglPlant.type4}
        </if>
        <if test="jglPlant.name!=null">
            and jp.name LIKE CONCAT('%', #{jglPlant.name}, '%')
        </if>
        <if test="jglPlant.feature!=null">
            and jp.feature LIKE CONCAT('%', #{jglPlant.feature}, '%')
        </if>
    </sql>
    <select id="getPageList" resultMap="JglPlantWithExpertMap">
        select
        jp.id, jp.name, jp.type1, jp.type2, jp.type3, jp.type4, jp.image, jp.feature, jp.area, jp.attachment,
        jp.tenant_id, jp.create_time, jp.modify_time, jp.creator, jp.modifier,
        je.id as je_id, je.name as je_name
        from
        (select * from jgl_plant jp where
        <include refid="getListCondition"/>
        order by jp.modify_time desc,jp.create_time desc
        <if test="size > 0">
            limit #{start},#{size}
        </if>
        )jp
        left join jgl_plant_ref_expert jpre on jp.id = jpre.plant_id
        left join jgl_expert je on jpre.expert_id= je.id
        ORDER BY
        jp.modify_time desc,
        jp.create_time desc
    </select>

    <select id="getPageCount" resultType="java.lang.Long">
        select count(1) from jgl_plant jp where
        <include refid="getListCondition"/>
    </select>

    <select id="getOneById" resultMap="JglPlantWithExpertMap">
        select
        jp.id, jp.name, jp.type1, jp.type2, jp.type3, jp.type4, jp.image, jp.feature, jp.area, jp.attachment,
        jp.tenant_id, jp.create_time, jp.modify_time, jp.creator, jp.modifier,
        je.id as je_id, je.name as je_name, je.gender, je.company, je.job, je.avatar, je.study_plant, je.remark,
        je.tenant_id as je_tenant_id
        from jgl_plant jp
        left join jgl_plant_ref_expert jpre on jp.id = jpre.plant_id
        left join jgl_expert je on jpre.expert_id= je.id
        <where>
            jp.id= #{id}
        </where>
    </select>
</mapper>

