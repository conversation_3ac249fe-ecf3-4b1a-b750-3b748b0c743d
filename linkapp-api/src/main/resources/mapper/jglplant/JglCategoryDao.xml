<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.jglplant.dao.JglCategoryDao">
    <resultMap type="com.easylinkin.linkappapi.jglplant.entity.JglCategory" id="JglCategoryMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="englishName" column="english_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into dev-linkapp-mysql.jgl_category(name, type, english_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.type}, #{entity.englishName})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into dev-linkapp-mysql.jgl_category(name, type, english_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.type}, #{entity.englishName})
        </foreach>
        on duplicate key update
        name = values(name) , type = values(type) , english_name = values(english_name)
    </insert>
</mapper>

