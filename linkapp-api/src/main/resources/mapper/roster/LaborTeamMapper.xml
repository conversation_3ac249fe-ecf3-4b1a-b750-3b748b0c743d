<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.roster.mapper.LaborTeamMapper">

    <resultMap id="LaborTeamResultMap" type="com.easylinkin.linkappapi.roster.entity.LaborTeam">
        <id property="id" column="id_"/>
        <result property="laborTeamName" column="labor_team_name_"/>
        <result property="creditCode" column="credit_code_"/>
        <result property="leaderName" column="leader_name_"/>
        <result property="leaderPhone" column="leader_phone_"/>
        <result property="remark" column="remark_"/>
        <result property="contractFiles" column="contract_files_" typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler" />
        <result property="tenantId" column="tenant_id_"/>
        <result property="creatorId" column="creator_id_"/>
        <result property="createTime" column="create_time_"/>
        <result property="modifyId" column="modify_id_"/>
        <result property="modifyTime" column="modify_time_"/>
    </resultMap>

    <select id="listPage" resultMap="LaborTeamResultMap">
        SELECT
            id_,
            labor_team_name_,
            credit_code_,
            leader_name_,
            leader_phone_,
            remark_,
            contract_files_,
            tenant_id_,
            creator_id_,
            create_time_,
            modify_id_,
            modify_time_
        FROM labor_team
        <where>
            <if test="customQueryParams.laborTeamName != null and customQueryParams.laborTeamName != ''">
                AND labor_team_name_ LIKE CONCAT('%', #{customQueryParams.laborTeamName}, '%')
            </if>
            <if test="customQueryParams.leaderName != null and customQueryParams.leaderName != ''">
                AND leader_name_ LIKE CONCAT('%', #{customQueryParams.leaderName}, '%')
            </if>
            <if test="customQueryParams.creditCode != null and customQueryParams.creditCode != ''">
                AND credit_code_ = #{customQueryParams.creditCode}
            </if>
            <if test="customQueryParams.tenantId != null and customQueryParams.tenantId != ''">
                AND tenant_id_ = #{customQueryParams.tenantId}
            </if>
        </where>
        ORDER BY create_time_ DESC
    </select>

</mapper> 