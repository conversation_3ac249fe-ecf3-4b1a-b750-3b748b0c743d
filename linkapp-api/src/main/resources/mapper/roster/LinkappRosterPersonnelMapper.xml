<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.roster.mapper.LinkappRosterPersonnelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.roster.vo.LinkappRosterPersonnelVO">
        <id column="id" property="id" />
        <result column="roster_type" property="rosterType" />
        <result column="real_name" property="realName" />
        <result column="belonging_units" property="belongingUnits" />
        <result column="ids_no" property="idsNo" />
        <result column="birthday_date" property="birthdayDate" />
        <result column="sex" property="sex" />
        <result column="education_background" property="educationBackground" />
        <result column="profile_pict" property="profilePict" />
        <result column="gate_permis" property="gatePermis" />
        <result column="safety_edu_res" property="safetyEduRes" />
        <result column="roster_post" property="rosterPost" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="auth_book" property="authBook" />
        <result column="auth_book_type" property="authBookType" />
        <result column="auth_book_content" property="authBookContent" />
        <result column="auth_book_start_date" property="authBookStartDate" />
        <result column="auth_book_end_date" property="authBookEndDate" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="modify_time" property="modifyTime" />
        <result column="link_phone" property="linkPhone" />
        <result column="labor_team" property="laborTeam" />
        <result column="work_category" property="workCategory" />
        <result column="work_type" property="workType" />
        <result column="protect_type" property="protectType" />
        <result column="protect_lssuing_auth" property="protectLssuingAuth" />
        <result column="protect_auth_book_date" property="protectAuthBookDate" />
        <result column="protect_auth_book_content" property="protectAuthBookContent" />
        <result column="protect_health_status" property="protectHealthStatus" />
        <result column="protect_health_auth_content" property="protectHealthAuthContent" />
        <result column="special_auth_book_type" property="specialAuthBookType" />
        <result column="special_auth_book_date" property="specialAuthBookDate" />
        <result column="appoint_book_content" property="appointBookContent" />
        <result column="social_book_content" property="socialBookContent" />
        <result column="tenant_id" property="tenantId" />
        <result column="modifier" property="modifier" />
        <result column="work_status" property="workStatus" />
        <!--result column="auth_book_json" property="authBookJsonArrs" typeHandler="com.easylinkin.linkappapi.roster.dto.AuthBookJson" / -->
        <result column="auth_book_json" property="authBookJson" />
        <result column="special_auth_book_json" property="specialAuthBookJson" />
        <result column="labor_team_name_" property="laborTeamName" />
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap"  parameterType="com.easylinkin.linkappapi.roster.dto.LinkappRosterPersonnelDTO">
         select a.*,b.labor_team_name_ from rail_linkapp_roster_personnel a
         left join labor_team b on b.id_ = a.labor_team
        <where>
            <if test="entity.tenantId != null and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.rosterTypeOne != null and entity.rosterTypeOne != ''">
                and a.roster_type = #{entity.rosterTypeOne}
            </if>
            <if test="entity.resterTypes != null and entity.resterTypes.size() > 0">
                and
                <foreach collection="entity.resterTypes" item="item" open="(" separator="or" close=")">
                     FIND_IN_SET(#{item},a.roster_type)
                </foreach>
            </if>
            <if test="entity.rosterType != null and entity.rosterType != ''">
                and FIND_IN_SET(#{entity.rosterType},a.roster_type)
            </if>
            <if test="entity.id != null and entity.id != ''">
                and a.id = #{entity.id}
            </if>
            <if test="entity.realName != null and entity.realName != ''">
                and a.real_name like concat('%',#{entity.realName},'%')
            </if>
            <if test="entity.idsNo != null and entity.idsNo != ''">
                and a.ids_no like concat('%',#{entity.idsNo},'%')
            </if>
            <if test="entity.uniKey != null and entity.uniKey != ''">
                and (a.ids_no like concat('%',#{entity.uniKey},'%') or a.real_name like concat('%',#{entity.uniKey},'%'))
            </if>
            <if test="entity.unitId != null and entity.unitId != ''">
                and FIND_IN_SET(#{entity.unitId},a.belonging_units)
            </if>
            <if test="entity.laborTeam != null and entity.laborTeam != ''">
                and a.labor_team = #{entity.laborTeam}
            </if>
            <if test="entity.workType != null and entity.workType != ''">
                and a.work_type = #{entity.workType}
            </if>
            <if test="entity.rosterPost != null and entity.rosterPost != ''">
                and a.roster_post = #{entity.rosterPost}
            </if>

        </where>
	     order by a.create_time desc
    </select>

    <select id="selectPersonByOperationAreaId" resultMap="BaseResultMap">
        SELECT DISTINCT a.*
        FROM rail_linkapp_roster_personnel a,
             rail_person_operation_area_ref_person b
        WHERE a.id = b.person_id_
          and b.delete_state = 0
          and b.operation_area_id_ = #{operationAreaId}
        ORDER BY a.create_time DESC
    </select>


</mapper>