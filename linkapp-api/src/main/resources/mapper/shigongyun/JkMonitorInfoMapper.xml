<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.shigongyun.mapper.JkMonitorInfoMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.shigongyun.entity.JkMonitorInfo">
    <id column="id" property="id"/>
    <result column="collect_time" property="collectTime"/>
    <result column="project_id" property="projectId"/>
    <result column="data_type" property="dataType"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="create_time" property="createTime"/>
  </resultMap>
  <resultMap id="DtosAndChildResultMap" extends="BaseResultMap"
    type="com.easylinkin.linkappapi.shigongyun.dto.MonitorInfoDto">
    <collection property="recordDtoList" column="id"
      select="com.easylinkin.linkappapi.shigongyun.mapper.JkMonitorRecordMapper.selectDtosAndChildByInfoId"/>
  </resultMap>

  <select id="latestRealTimeMonitorData"
    parameterType="com.easylinkin.linkappapi.shigongyun.vo.MonitorInfoVo"
    resultMap="DtosAndChildResultMap">
    select jmi.*
    from jk_monitor_info jmi where 1 = 1
    <if test="tenantId != null and tenantId != ''">
      and jmi.tenant_id = #{tenantId}
    </if>
    <if test="startTime != null">
      <![CDATA[
      and jmi.collect_time >= #{startTime}
      ]]>
    </if>
    order by jmi.create_time desc limit 1
  </select>
</mapper>
