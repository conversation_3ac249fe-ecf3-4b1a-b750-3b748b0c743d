<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.shigongyun.mapper.JkMonitorRecordMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.shigongyun.entity.JkMonitorRecord">
    <id column="id" property="id"/>
    <result column="info_id" property="infoId"/>
    <result column="warning_level" property="warningLevel"/>
    <result column="measurement_project_name" property="measurementProjectName"/>
    <result column="create_time" property="createTime"/>
    <result column="alarm_value" property="alarmValue"/>
    <result column="project_id" property="projectId"/>
    <result column="total_change_result" property="totalChangeResult"/>
    <result column="single_change_result" property="singleChangeResult"/>
    <result column="change_rate" property="changeRate"/>
    <result column="point_name" property="pointName"/>
    <result column="is_maximum" property="isMaximum"/>
    <result column="percent" property="percent"/>
    <result column="source_id" property="sourceId"/>
    <result column="parent_id" property="parentId"/>
    <result column="points" property="points"/>
    <result column="storage_time" property="storageTime"/>
  </resultMap>

  <resultMap id="SonResultMap" extends="BaseResultMap"
    type="com.easylinkin.linkappapi.shigongyun.dto.MonitorRecordDto">
    <collection property="childrenRecord" column="id"
      select="com.easylinkin.linkappapi.shigongyun.mapper.JkMonitorRecordMapper.selectSonByParentId"/>
  </resultMap>

  <resultMap id="InfoRecordDtoMap"
    type="com.easylinkin.linkappapi.shigongyun.dto.MonitorInfoRecordDto">
  </resultMap>

  <select id="selectDtosAndChildByInfoId" parameterType="string" resultMap="SonResultMap">
    select jmr.*
    from jk_monitor_record jmr
    where jmr.info_id = #{infoId}
  </select>
  <select id="selectSonByParentId" parameterType="string" resultMap="SonResultMap">
    select jmr.*
    from jk_monitor_record jmr
    where jmr.parent_id = #{parentId}
  </select>

  <select id="selectInfRecordDtoBy"
    parameterType="com.easylinkin.linkappapi.shigongyun.vo.MonitorRecordVo"
    resultMap="InfoRecordDtoMap">
    select jmr.*,
           jmi.collect_time
    from jk_monitor_info jmi,
         jk_monitor_record jmr
    where jmi.id = jmr.info_id
    <if test="tenantId != null and tenantId != ''">
      and jmi.tenant_id = #{tenantId}
    </if>
    <if test="projectId != null and projectId != ''">
      and jmi.project_id = #{projectId}
    </if>
    <if test="pointName != null and pointName != ''">
      and jmr.point_name = #{pointName}
    </if>
    <if test="startTime != null">
      <![CDATA[
      and jmi.create_time >= #{startTime}
      ]]>
    </if>
    <if test="endTime != null">
      <![CDATA[
      and jmi.create_time <= #{endTime}
      ]]>
    </if>
  </select>
</mapper>
