<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.inspection.mapper.ElectricCheckMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.inspection.entity.ElectricCheck">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="electric_box_id_" property="electricBoxId" />
        <result column="check_time_" property="checkTime" />
        <result column="check_user_id_" property="checkUserId" />
        <result column="position_" property="position" />
        <result column="photo_" property="photo" />
        <result column="content_" property="content" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>
    <resultMap id="BaseResultMapDTO" type="com.easylinkin.linkappapi.inspection.dto.ElectricCheckDTO" extends="BaseResultMap">
        <result column="checkUserName" property="checkUserName" />
        <result column="phone" property="phone" />
        <result column="code_" property="code"/>
        <result column="type_" property="type"/>
        <result column="electricBoxPosition" property="electricBoxPosition"/>
    </resultMap>


  <select id="queryListByPage" resultMap="BaseResultMapDTO">
    SELECT
      a.*,
      b.nickname checkUserName,
      b.phone,
      box.type_,
      box.code_,
      box.position_ electricBoxPosition
    FROM
      app_electric_check a
      LEFT JOIN linkapp_user b ON a.check_user_id_ = b.id
      LEFT JOIN app_electric_box box on a.electric_box_id_ = box.id
    <where>
        <if test="electricCheck.tenantId != null and electricCheck.tenantId != ''">
            AND a.tenant_id_ = #{electricCheck.tenantId}
        </if>
        <if test="electricCheck.electricBoxId != null and electricCheck.electricBoxId != ''">
            AND a.electric_box_id_ = #{electricCheck.electricBoxId}
        </if>
        <if test="electricCheck.status != null and electricCheck.status != ''">
          AND a.status_ = #{electricCheck.status}
        </if>
        <if test="electricCheck.startTime != null">
          <![CDATA[
            and a.check_time_ >= #{electricCheck.startTime}
            ]]>
        </if>
        <if test="electricCheck.endTime != null">
          <![CDATA[
              and a.check_time_ <= #{electricCheck.endTime}
              ]]>
        </if>
    </where>
      ORDER BY a.check_time_ DESC
  </select>

  <select id="selectPhotoAlbumByPage" resultType="com.easylinkin.linkappapi.inspection.dto.ElectricCheckPhotoAlbumDTO">
    SELECT
	    a.id,
      a.photo_ as photo,
      box.code_ as electricBoxCode,
      box.type_ as type,
      box.position_ as position,
      b.nickname as creator,
      a.check_time_ as createTime,
      a.status_ as status,
      tenant.platform_project_name as projectName
    FROM
      app_electric_check a
      LEFT JOIN linkapp_user b ON a.check_user_id_ = b.id
      LEFT JOIN app_electric_box box on a.electric_box_id_ = box.id
      LEFT JOIN linkapp_tenant tenant on tenant.id = a.tenant_id_
    <where>
      a.photo_ IS NOT NULL
      <if test="electricCheck.status != null">
        AND a.status_ = #{electricCheck.status}
      </if>
      <if test="electricCheck.tenantId != null and electricCheck.tenantId != ''">
        AND a.tenant_id_ = #{electricCheck.tenantId}
      </if>
      <if test="electricCheck.createId != null and electricCheck.createId != ''">
        AND a.check_user_id_ = #{electricCheck.createId}
      </if>
      <if test="electricCheck.electricBoxCode != null and electricCheck.electricBoxCode != ''">
        AND a.electric_box_id_ = #{electricCheck.electricBoxCode}
      </if>
      <if test="electricCheck.startTime != null">
        <![CDATA[
            AND a.check_time_ >= #{electricCheck.startTime}
            ]]>
      </if>
      <if test="electricCheck.endTime != null">
        <![CDATA[
              AND a.check_time_ <= #{electricCheck.endTime}
              ]]>
      </if>
      <if test="electricCheck.ids != null and electricCheck.ids.size()>0">
        AND a.id IN
        <foreach item="item" index="index" collection="electricCheck.ids" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    ORDER BY a.check_time_
    <choose>
      <when test="electricCheck.sortAsc">
        ASC
      </when>
      <otherwise>
        DESC
      </otherwise>
    </choose>
  </select>

  <select id="monthList" resultMap="BaseResultMapDTO">
    SELECT
      a.*,
      b.nickname checkUserName
    FROM
      app_electric_check a
      LEFT JOIN linkapp_user b ON a.check_user_id_ = b.id
    WHERE
      a.electric_box_id_ = #{electricBoxId}
      AND date_format( a.check_time_, "%Y-%m" ) = #{mouthStr}
  </select>


</mapper>
