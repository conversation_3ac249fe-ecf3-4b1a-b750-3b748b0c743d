<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.inspection.mapper.ElectricOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.inspection.entity.ElectricOption">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="content_" property="content" />
        <result column="remark_" property="remark" />
        <result column="status_" property="status" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
    </resultMap>
      <select id="queryListByPage" resultMap="BaseResultMap">

      </select>

</mapper>
