<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.inspection.mapper.ElectricBoxRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.inspection.entity.ElectricBoxRecord">
        <id column="id" property="id" />
        <result column="electric_box_id_" property="electricBoxId" />
        <result column="device_code" property="deviceCode" />
        <result column="tenant_id_" property="tenantId" />
        <result column="temperature_1" property="temperature1" />
        <result column="temperature_2" property="temperature2" />
        <result column="temperature_3" property="temperature3" />
        <result column="temperature_4" property="temperature4" />
        <result column="residual_current" property="residualCurrent" />
        <result column="active_power" property="activePower" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="getList" resultMap="BaseResultMap">
        select * from app_electric_box_record
        where tenant_id_ = #{tenantId}
        <if test="deviceCode != null and deviceCode != ''">
            and device_code = #{deviceCode}
        </if>
        <if test="electricBoxId != null and electricBoxId != ''">
            and electric_box_id_ = #{electricBoxId}
        </if>
        <if test="startTime != null">
            AND create_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND create_time <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
        </if>
        order by create_time
    </select>

   <select id="selectByDateOrTime" resultMap="BaseResultMap">
       SELECT a.id,a.create_time,a.temperature_1,a.temperature_2,a.temperature_3,a.temperature_4,a.residual_current,
       <if test="recordDto.dailyOrMonthly == 0 ">
           DATE_FORMAT(a.create_time, '%Y-%m-%d %H:00') AS hour_interval
       </if>
       <if test="recordDto.dailyOrMonthly == 1 ">
           DATE(a.create_time) AS hour_interval
       </if>
       from  app_electric_box_record a where 1=1
       <if test="recordDto.tenantId != '' and recordDto.tenantId != null">
          and a.tenant_id_ = #{recordDto.tenantId}
       </if>
       <if test="recordDto.deviceCode != null and recordDto.deviceCode != ''">
           and a.device_code = #{recordDto.deviceCode}
       </if>
       <if test="recordDto.electricBoxId != null and recordDto.electricBoxId != ''">
           and a.electric_box_id_ = #{recordDto.electricBoxId}
       </if>
       <if test="recordDto.startTime != null and recordDto.endTime != null">
           and  DATE_FORMAT( a.create_time,'%Y-%m-%d') >= #{recordDto.startTime} and DATE_FORMAT( a.create_time,'%Y-%m-%d')<![CDATA[<=]]> #{recordDto.endTime}
       </if>
       <if test="recordDto.startTimeH != null and recordDto.endTimeH != null">
           and  DATE_FORMAT( a.create_time,'%Y-%m-%d %H') >= #{recordDto.startTimeH} and DATE_FORMAT( a.create_time,'%Y-%m-%d %H')<![CDATA[<=]]> #{recordDto.endTimeH}
       </if>
       GROUP BY hour_interval
   </select>

</mapper>
