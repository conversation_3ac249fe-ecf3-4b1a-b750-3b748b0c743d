<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.inspection.mapper.RailLinkappElectricFireRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.inspection.entity.RailLinkappElectricFireRecord">
        <id column="id" property="id" />
        <result column="device_code" property="deviceCode" />
        <result column="tenant_id" property="tenantId" />
        <result column="temperature_1" property="temperature1" />
        <result column="temperature_2" property="temperature2" />
        <result column="temperature_3" property="temperature3" />
        <result column="temperature_4" property="temperature4" />
        <result column="residual_current" property="residualCurrent" />
        <result column="active_power" property="activePower" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="getList" resultMap="BaseResultMap">
        select * from rail_linkapp_electric_fire_record
        where tenant_id = #{tenantId}
        <if test="deviceCode != null and deviceCode != ''">
            and device_code = #{deviceCode}
        </if>
        <if test="startTime != null">
            AND create_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND create_time <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
        </if>
        order by create_time
    </select>

    <select id="selectByDateOrTime" resultMap="BaseResultMap">
        SELECT a.id,a.create_time,a.temperature_1,a.temperature_2,a.temperature_3,a.temperature_4,sum(a.residual_current) as residual_current,
        <if test="recordDto.dailyOrMonthly == 0 ">
            DATE_FORMAT(a.create_time, '%Y-%m-%d %H:00') AS hour_interval
        </if>
        <if test="recordDto.dailyOrMonthly == 1 ">
            DATE(a.create_time) AS hour_interval
        </if>
        from  rail_linkapp_electric_fire_record a where 1=1
        <if test="recordDto.tenantId != '' and recordDto.tenantId != null">
            and a.tenant_id = #{recordDto.tenantId}
        </if>
        <if test="recordDto.deviceCode != null and recordDto.deviceCode != ''">
            and a.device_code = #{recordDto.deviceCode}
        </if>
        <if test="recordDto.startTime != null and recordDto.endTime != null">
            and  DATE_FORMAT( a.create_time,'%Y-%m-%d') >= #{recordDto.startTime} and DATE_FORMAT( a.create_time,'%Y-%m-%d')<![CDATA[<=]]> #{recordDto.endTime}
        </if>
        <if test="recordDto.startTimeH != null and recordDto.endTimeH != null">
            and  DATE_FORMAT( a.create_time,'%Y-%m-%d %H') >= #{recordDto.startTimeH} and DATE_FORMAT( a.create_time,'%Y-%m-%d %H')<![CDATA[<=]]> #{recordDto.endTimeH}
        </if>
        GROUP BY hour_interval
    </select>

    <select id = "selectByTwentyFourHour" resultMap="BaseResultMap">
        SELECT t.*
        FROM rail_linkapp_electric_fire_record t
        where t.create_time >= NOW() - INTERVAL 24 HOUR and t.tenant_id = #{recordDto.tenantId}
        <if test="recordDto.deviceCode != null and recordDto.deviceCode != ''">
            and t.device_code = #{recordDto.deviceCode}
        </if>
   ORDER BY  t.create_time asc
    </select>

    <select id = "selectByThirtyDays" resultMap="BaseResultMap">
        SELECT t1.*

        FROM rail_linkapp_electric_fire_record t1

        INNER JOIN (

        SELECT DATE(create_time) as day, MAX(create_time) as max_time

        FROM rail_linkapp_electric_fire_record

        WHERE create_time >= CURDATE() - INTERVAL 29 DAY

        GROUP BY DATE(create_time)

        ) t2

        ON t1.create_time = t2.max_time
        <where>
        <if test="recordDto.tenantId != null and recordDto.tenantId != ''">
            and t1.tenant_id = #{recordDto.tenantId}
        </if>
        <if test="recordDto.deviceCode != null and recordDto.deviceCode != ''">
            and t1.device_code = #{recordDto.deviceCode}
        </if>
        </where>
        ORDER BY t1.create_time;
    </select>


    <select id="codeByNewRecords" resultType="com.easylinkin.linkappapi.inspection.vo.RailLinkappElectricFireRecordVO">
        SELECT DISTINCT  dr.*
        FROM rail_linkapp_electric_fire_record dr
        INNER JOIN (
        SELECT device_code, MAX(create_time) AS max_time
        FROM rail_linkapp_electric_fire_record
        WHERE  tenant_id = #{tenantId} and device_code IN
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        GROUP BY device_code
        ) AS latest
        ON dr.device_code = latest.device_code
        AND dr.create_time = latest.max_time
    </select>





</mapper>
