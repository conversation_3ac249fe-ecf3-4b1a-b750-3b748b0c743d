<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.inspection.mapper.ElectricBoxMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.inspection.entity.ElectricBox">
        <id column="id" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="code_" property="code"/>
        <result column="type_" property="type"/>
        <result column="position_" property="position"/>
        <result column="lead_id_" property="leadId"/>
        <result column="telephone_" property="telephone"/>
        <result column="circuitry_" property="circuitry"/>
        <result column="electrician_" property="electrician"/>
        <result column="qr_code" property="qrCode"/>
        <result column="monitor_device_code_" property="monitorDeviceCode"/>
        <result column="is_delete_" property="isDelete"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
        <result column="name_" property="leadName"/>
        <result column="use_state_" property="useState"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapDTO" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.inspection.dto.ElectricBoxDTO">
        <result column="check_time_" property="checkTime"/>
        <result column="last_push_time" property="lastPushTime"/>
    </resultMap>
  <update id="removeMonitorDevice">
    update app_electric_box set monitor_device_code_ = null
    where id = #{electricBox.id}
  </update>

  <select id="queryListByPage" resultMap="BaseResultMap">
        SELECT eb.*,
               u.nickname name_,
               u.phone    telephone_
        FROM app_electric_box eb
                 LEFT JOIN linkapp_user u ON eb.lead_id_ = u.id
        WHERE eb.tenant_id_ = #{electricBox.tenantId}
        <if test="electricBox.isDelete != null">
            AND eb.is_delete_ = #{electricBox.isDelete}
        </if>
        <if test="electricBox.code != null and electricBox.code != ''">
            AND eb.code_ like CONCAT('%', #{electricBox.code}, '%')
        </if>
        <if test="electricBox.leadName != null and electricBox.leadName != ''">
            AND u.nickname like CONCAT('%', #{electricBox.leadName}, '%')
        </if>
        <if test="electricBox.position != null and electricBox.position != ''">
            AND eb.position_ like CONCAT('%', #{electricBox.position}, '%')
        </if>
        <if test="electricBox.type != null">
            AND eb.type_ = #{electricBox.type}
        </if>
        <if test="electricBox.ids != null and electricBox.ids.size > 0">
            and eb.id in
            <foreach collection="electricBox.ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY eb.modify_time_ DESC
    </select>

  <select id="queryRefElectricBoxDevices" resultMap="BaseResultMapDTO">
    SELECT eb.*,ld.last_push_time
    FROM app_electric_box eb
    INNER JOIN linkapp_device ld on eb.monitor_device_code_ = ld.code and ld.delete_state = 1 and ld.linkthing_delete = 1
    WHERE eb.tenant_id_ = #{tenantId}
    AND eb.is_delete_ = 0
    AND eb.monitor_device_code_ IS NOT NULL
  </select>

    <select id="findById" resultMap="BaseResultMap">
        SELECT eb.*,
               u.nickname name_,
               u.phone    telephone_
        FROM app_electric_box eb
                 LEFT JOIN linkapp_user u ON eb.lead_id_ = u.id
        WHERE eb.id = #{id}
    </select>

    <select id="getDone" resultMap="BaseResultMapDTO">
        SELECT eb.*,
               ec.check_time_,
               u.nickname name_,
               u.phone    telephone_
        FROM app_electric_check ec
                 LEFT JOIN app_electric_box eb ON ec.electric_box_id_ = eb.id
                 LEFT JOIN linkapp_user u ON eb.lead_id_ = u.id
        WHERE ec.tenant_id_ = #{electricBoxDTO.tenantId}
        <if test="electricBoxDTO.startTime != null">
            AND ec.check_time_ &gt;= #{electricBoxDTO.startTime}
        </if>
        <if test="electricBoxDTO.endTime != null">
            AND ec.check_time_ &lt;= #{electricBoxDTO.endTime}
        </if>
        ORDER BY ec.check_time_ DESC
    </select>

    <select id="listUnChecked" resultMap="BaseResultMap">
        select distinct aeb.lead_id_,
                        aeb.tenant_id_,
                        aeb.type_,
                        aeb.code_,
                        aeb.position_,
                        IFNULL(u.nickname, u.username) name_,
                        u.phone                        telephone_
        from app_electric_box aeb
                 LEFT JOIN linkapp_user u ON aeb.lead_id_ = u.id
        where aeb.is_delete_ = 0
          and aeb.id not in (select electric_box_id_
                            from app_electric_check
                            where check_time_ >= #{start,jdbcType=TIMESTAMP}
                              and check_time_ <![CDATA[<=]]> #{end,jdbcType=TIMESTAMP})
    </select>

    <select id="listUnCheckedDetails" resultMap="BaseResultMap">
        select aeb.*,
               lu.nickname as name_,
               lu.phone    as telephone_
        from app_electric_box aeb
                 LEFT JOIN linkapp_user lu ON aeb.lead_id_ = lu.id
        where aeb.is_delete_ = 0
          and aeb.tenant_id_ = #{messageCenter.tenantId,jdbcType=VARCHAR}
        <if test="messageCenter.self and messageCenter.ownerId != null and messageCenter.ownerId != ''">
            and aeb.lead_id_ = #{messageCenter.ownerId,jdbcType=VARCHAR}
        </if>

        and aeb.code_ not in (select electric_box_id_
                              from app_electric_check
                              where check_time_ >= #{start,jdbcType=TIMESTAMP}
                                and check_time_ <![CDATA[<=]]> #{date10,jdbcType=TIMESTAMP})
        order by aeb.lead_id_
        <!--        UNION-->
        <!--        select aeb.*,-->
        <!--        lu.nickname as name_,-->
        <!--        lu.phone as telephone_-->
        <!--        from app_electric_box aeb-->
        <!--        LEFT JOIN linkapp_user lu ON aeb.lead_id_ = lu.id-->
        <!--        where is_delete_ = 0-->
        <!--        <if test="electricBox.leadId != null and electricBox.leadId != ''">-->
        <!--            and aeb.lead_id_ = #{electricBox.leadId,jdbcType=VARCHAR}-->
        <!--        </if>-->
        <!--        and code_ in (select electric_box_id_-->
        <!--                      from app_electric_check-->
        <!--                      where check_time_ <![CDATA[>]]> #{date10,jdbcType=TIMESTAMP})-->
    </select>

  <select id="countElectricBoxWarnGroupByType" resultType="com.easylinkin.linkappapi.inspection.dto.ElectricBoxWarnGroupByTypeDTO">
    SELECT
      count( * ) AS count,
      rule.`id` AS ruleId,
      rule.`name` AS ruleName
    FROM
      `linkapp_alarm` AS alarm
      INNER JOIN `rule_engine` AS rule ON alarm.`rule_engine_id` = rule.`id`
      LEFT JOIN `app_electric_box` AS box ON alarm.`device_code` = box.`monitor_device_code_`
     WHERE
      box.tenant_id_ = #{tenantId}
      AND box.`monitor_device_code_` IS NOT NULL
    GROUP BY
      alarm.`rule_engine_id`
  </select>

  <select id="countElectricBoxWarnGroupByDevice" resultType="com.easylinkin.linkappapi.inspection.dto.ElectricBoxWarnGroupByDeviceDTO">
    SELECT
      count( * ) AS count,
      box.`code_` AS code,
      box.`position_` AS position
    FROM
      `linkapp_alarm` AS alarm
      LEFT JOIN `app_electric_box` AS box ON alarm.`device_code` = box.`monitor_device_code_`
    WHERE
      box.tenant_id_ = #{tenantId}
      AND box.`monitor_device_code_` IS NOT NULL
    GROUP BY
      box.`monitor_device_code_`
  </select>

    <select id="findMonitorDeviceCodes" resultType="java.lang.String">
        SELECT
            eb.monitor_device_code_
        FROM
            app_electric_box eb
                JOIN linkapp_device d ON eb.monitor_device_code_ = d.CODE
        WHERE
            eb.tenant_id_ = #{tenantId}
          AND eb.is_delete_ = 0
          AND d.delete_state = 1
        ORDER BY eb.modify_time_ DESC
    </select>

    <select id="todayCheck" resultMap="BaseResultMapDTO">
        SELECT
            eb.*,
            ec.check_time_
        FROM
            app_electric_box eb
                LEFT JOIN app_electric_check ec ON eb.id = ec.electric_box_id_
                AND DATE ( ec.check_time_ ) = CURDATE()
        WHERE
            eb.is_delete_ = 0 and eb.use_state_ = 1
        <if test="electricBox.tenantId != null and electricBox.tenantId != ''">
            AND eb.tenant_id_ = #{electricBox.tenantId}
        </if>
        <if test="electricBox.isInspected != null">
            <choose>
                <when test="electricBox.isInspected == 1">
                    AND ec.id IS NOT NULL
                </when>
                <when test="electricBox.isInspected == 0">
                    AND ec.id IS NULL
                </when>
            </choose>
        </if>
        ORDER BY eb.modify_time_ DESC, ec.check_time_ DESC
    </select>
</mapper>
