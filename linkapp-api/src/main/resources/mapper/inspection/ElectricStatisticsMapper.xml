<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.inspection.mapper.ElectricStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.inspection.entity.ElectricStatistics">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="record_time_" property="recordTime" />
        <result column="sum_" property="sum" />
        <result column="done_" property="done" />
        <result column="sum_id_" property="sumId" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

  <resultMap id="BaseResultMapDTO" type="com.easylinkin.linkappapi.inspection.dto.ElectricStatisticsDTO" extends="BaseResultMap">
  </resultMap>

  <select id="queryListByPage" resultMap="BaseResultMapDTO">
    SELECT
      a.*
    FROM
      app_electric_statistics a
    WHERE
      a.tenant_id_ = #{electricStatisticsDTO.tenantId}
    <if test="electricStatisticsDTO.startTime != null">
      and a.record_time_ &gt;=  #{electricStatisticsDTO.startTime}
    </if>
    <if test="electricStatisticsDTO.endTime != null">
      and a.record_time_ &lt;=  #{electricStatisticsDTO.endTime}
    </if>
      ORDER BY a.modify_time_ DESC
  </select>

</mapper>
