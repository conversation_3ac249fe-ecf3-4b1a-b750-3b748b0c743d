<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.crane.mapper.CraneVerticalProtectionAreaMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.crane.entity.CraneVerticalProtectionArea">
        <id property="id" column="id_"/>
        <result property="tenantId" column="tenant_id_"/>
        <result property="regionName" column="region_name_"/>
        <result property="workStationId" column="work_station_id_"/>
        <result property="workTimeList" column="work_time_"
                typeHandler="com.easylinkin.linkappapi.crane.typehandler.WorkTimeListHandler"/>
        <result property="location" column="location_"/>
        <result property="enabledFlag" column="enabled_flag_"/>
        <result property="deleteState" column="delete_state"/>
        <result property="createId" column="create_id_"/>
        <result property="createTime" column="create_time_"/>
        <result property="modifyId" column="modify_id_"/>
        <result property="modifyTime" column="modify_time_"/>
        <result property="remark" column="remark_"/>
        <result property="alarmFlag" column="alarm_flag_"/>
        <result property="workStationName" column="work_station_name_"/>
    </resultMap>
    <sql id="Base_Column_List">
        a.id_,
        a.tenant_id_,
        a.region_name_,
        a.work_station_id_,
        a.work_time_,
        a.location_,
        a.enabled_flag_,
        a.delete_state,
        a.create_id_,
        a.create_time_,
        a.modify_id_,
        a.modify_time_,
        a.remark_,
        a.alarm_flag_,
        b.point_name_ AS work_station_name_
    </sql>

    <select id="queryPageList" resultMap="BaseResultMap">
        SELECT DISTINCT
        <include refid="Base_Column_List"/>
        FROM rail_crane_vertical_protection_area a
                 LEFT JOIN rail_project_point b ON a.work_station_id_ = b.id
        WHERE a.delete_state = 0
        <if test="customQueryParams.tenantId != null and customQueryParams.tenantId != ''">
            AND a.tenant_id_ = #{customQueryParams.tenantId}
        </if>
        <if test="customQueryParams.enabledFlag != null">
            AND a.enabled_flag_ = #{customQueryParams.enabledFlag}
        </if>
        <if test="customQueryParams.workStationId != null and customQueryParams.workStationId != ''">
            AND a.work_station_id_ = #{customQueryParams.workStationId}
        </if>
        <if test="customQueryParams.regionName != null and customQueryParams.regionName != ''">
            AND a.region_name_ LIKE CONCAT('%', #{customQueryParams.regionName}, '%')
        </if>
        <if test="customQueryParams.keyword != null and customQueryParams.keyword != ''">
            AND (a.region_name_ LIKE CONCAT('%', #{customQueryParams.keyword}, '%') OR
                 b.name_ LIKE CONCAT('%', #{customQueryParams.keyword}, '%'))
        </if>
        ORDER BY a.create_time_ DESC
    </select>
</mapper>