<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.crane.mapper.CraneBusinessRecordMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.crane.entity.CraneBusinessRecord">
        <id property="id" column="id_" />
        <result property="tenantId" column="tenant_id_" />
        <result property="mechanicalId" column="mechanical_id_" />
        <result property="deviceId" column="device_id_" />
        <result property="deviceCode" column="device_code_" />
        <result property="deviceName" column="device_name_" />
        <result property="workStartTime" column="work_start_time_" />
        <result property="workEndTime" column="work_end_time_" />
        <result property="armForce" column="arm_force_" />
        <result property="alarmArmForce" column="alarm_arm_force_" />
        <result property="bigArmLen" column="big_arm_len_" />
        <result property="alarmBigArmLen" column="alarm_big_arm_len_" />
        <result property="levelX" column="level_x_" />
        <result property="alarmLevelX" column="alarm_level_x_" />
        <result property="levelY" column="level_y_" />
        <result property="alarmLevelY" column="alarm_level_y_" />
        <result property="windSpeed" column="wind_speed_" />
        <result property="alarmWindSpeed" column="alarm_wind_speed_" />
        <result property="rotation" column="rotation_" />
        <result property="alarmRotation" column="alarm_rotation_" />
        <result property="pitch" column="pitch_" />
        <result property="alarmPitch" column="alarm_pitch_" />
        <result property="moment" column="moment_" />
        <result property="alarmMoment" column="alarm_moment_" />
        <result property="weight" column="weight_" />
        <result property="alarmWeight" column="alarm_weight_" />
        <result property="inclination" column="inclination_" />
        <result property="alarmInclination" column="alarm_inclination_" />
        <result property="windLevel" column="wind_level_" />
        <result property="armHeight" column="arm_height_" />
        <result property="workHour" column="work_hour_" />
        <result property="idlingHour" column="idling_hour_" />
        <result property="workTotalHour" column="work_total_hour_" />
        <result property="hoistingTimes" column="hoisting_times_" />
        <result property="alarmSafeLimit" column="alarm_safe_limit_" />
        <result property="alarmWeightLimit" column="alarm_weight_limit_" />
        <result property="alarmBigArmLenLimit" column="alarm_big_arm_len_limit_" />
        <result property="alarmIntrusionPrevention" column="alarm_intrusion_prevention_" />
        <result property="alarmDumpPrevention" column="alarm_dump_prevention_" />
        <result property="alarmState" column="alarm_state_" />
        <result property="createId" column="create_id_" />
        <result property="createTime" column="create_time_" />
        <result property="modifyId" column="modify_id_" />
        <result property="modifyTime" column="modify_time_" />
        <result property="remark" column="remark_" />
    </resultMap>

    <sql id="Base_Column_List">
        a.id_,
        a.tenant_id_,
        a.mechanical_id_,
        a.device_id_,
        a.device_code_,
        a.device_name_,
        a.work_start_time_,
        a.work_end_time_,
        a.arm_force_,
        a.alarm_arm_force_,
        a.big_arm_len_,
        a.alarm_big_arm_len_,
        a.level_x_,
        a.alarm_level_x_,
        a.level_y_,
        a.alarm_level_y_,
        a.wind_speed_,
        a.alarm_wind_speed_,
        a.rotation_,
        a.alarm_rotation_,
        a.pitch_,
        a.alarm_pitch_,
        a.moment_,
        a.alarm_moment_,
        a.weight_,
        a.alarm_weight_,
        a.inclination_,
        a.alarm_inclination_,
        a.wind_level_,
        a.arm_height_,
        a.work_hour_,
        a.idling_hour_,
        a.work_total_hour_,
        a.hoisting_times_,
        a.alarm_safe_limit_,
        a.alarm_weight_limit_,
        a.alarm_big_arm_len_limit_,
        a.alarm_intrusion_prevention_,
        a.alarm_dump_prevention_,
        a.alarm_state_,
        a.create_id_,
        a.create_time_,
        a.modify_id_,
        a.modify_time_,
        a.remark_
    </sql>

    <select id="findPage" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM rail_crane_business_record a
        WHERE 1=1
        <if test="customQueryParams.tenantId!= null and customQueryParams.tenantId != ''">
            AND a.tenant_id_ = #{customQueryParams.tenantId}
        </if>
        <if test="customQueryParams.mechanicalId!= null and customQueryParams.mechanicalId != ''">
            AND a.mechanical_id_ = #{customQueryParams.mechanicalId}
        </if>
        <if test="customQueryParams.startTime!= null and customQueryParams.startTime != ''">
            AND a.create_time_ <![CDATA[>=]]> #{customQueryParams.startTime}
        </if>
        <if test="customQueryParams.endTime!= null and customQueryParams.endTime != ''">
            AND a.create_time_ <![CDATA[<=]]> #{customQueryParams.endTime}
        </if>
        <if test="customQueryParams.keyword!= null and customQueryParams.keyword != ''">
            AND (a.device_code_ LIKE CONCAT('%', #{customQueryParams.keyword}, '%') OR a.device_name_ LIKE CONCAT('%', #{customQueryParams.keyword}, '%'))
        </if>
        ORDER BY a.create_time_ DESC
    </select>

    <select id="sumWeightByDay" resultType="java.lang.Double">
        SELECT
            SUM(a.max_weight_) AS weight_sum
        FROM rail_crane_work_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
        <if test="day!= null">
            AND DATE_FORMAT(a.upload_time_, '%Y-%m-%d') = DATE_FORMAT(#{day}, '%Y-%m-%d')
        </if>
    </select>

    <select id="countMinHoistingTimesByDay" resultType="java.lang.Long">
        SELECT
             a.hoisting_times_
        FROM rail_crane_business_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
        <if test="day!= null">
            AND DATE_FORMAT(a.create_time_, '%Y-%m-%d') = DATE_FORMAT(#{day}, '%Y-%m-%d')
        </if>
        ORDER BY a.create_time_ DESC
        LIMIT 1
    </select>

    <select id="countMaxHoistingTimesByDay" resultType="java.lang.Long">
        SELECT
        a.hoisting_times_
        FROM rail_crane_business_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
        <if test="day!= null">
            AND DATE_FORMAT(a.create_time_, '%Y-%m-%d') =  DATE_FORMAT(#{day}, '%Y-%m-%d')
        </if>
        ORDER BY a.create_time_ ASC
        LIMIT 1
    </select>

    <select id="getTotalWeight" resultType="java.lang.Double">
        SELECT
            COALESCE(SUM(a.max_weight_), 0) AS total_weight
        FROM rail_crane_work_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
    </select>

    <select id="getTotalHoistingTimes" resultType="java.lang.Long">
        SELECT
            COALESCE(a.hoisting_times_, 0) AS total_hoisting_times
        FROM rail_crane_business_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
        ORDER BY a.create_time_ DESC
        LIMIT 1
    </select>
</mapper>