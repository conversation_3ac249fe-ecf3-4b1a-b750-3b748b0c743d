<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.crane.mapper.CraneOperationAreaMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.crane.entity.CraneOperationArea">
        <id property="id" column="id_"/>
        <result property="tenantId" column="tenant_id_"/>
        <result property="regionName" column="region_name_"/>
        <result property="workTimeList" column="work_time_"
                typeHandler="com.easylinkin.linkappapi.crane.typehandler.WorkTimeListHandler"/>
        <result property="location" column="location_"/>
        <result property="enabledFlag" column="enabled_flag_"/>
        <result property="deleteState" column="delete_state"/>
        <result property="createId" column="create_id_"/>
        <result property="createTime" column="create_time_"/>
        <result property="modifyId" column="modify_id_"/>
        <result property="modifyTime" column="modify_time_"/>
        <result property="remark" column="remark_"/>
        <result property="alarmFlag" column="alarm_flag_"/>
        <collection property="mechanicalList" column="id_"
                    select="com.easylinkin.linkappapi.mechanical.mapper.MechanicalMapper.selectMechanicalByOperationAreaId">
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        a.id_,
        a.tenant_id_,
        a.region_name_,
        a.work_time_,
        a.location_,
        a.enabled_flag_,
        a.delete_state,
        a.create_id_,
        a.create_time_,
        a.modify_id_,
        a.modify_time_,
        a.remark_,
        a.alarm_flag_
    </sql>

    <select id="queryPageList" resultMap="BaseResultMap">
        SELECT DISTINCT
        <include refid="Base_Column_List"/>
        FROM rail_crane_operation_area a
                 LEFT JOIN rail_crane_operation_area_ref_mechanical b ON a.id_ = b.operation_area_id_
                 LEFT JOIN rail_mechanical c ON b.mechanical_id_ = c.id
        WHERE a.delete_state = 0
        <if test="customQueryParams.tenantId != null">
            AND a.tenant_id_ = #{customQueryParams.tenantId}
        </if>
        <if test="customQueryParams.mechanicalIdList != null and customQueryParams.mechanicalIdList.size() > 0">
            AND b.mechanical_id_ IN
            <foreach collection="customQueryParams.mechanicalIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customQueryParams.keyword != null and customQueryParams.keyword != ''">
            AND (a.region_name_ LIKE CONCAT('%', #{customQueryParams.keyword}, '%') or
                 c.name_ LIKE CONCAT('%', #{customQueryParams.keyword}, '%'))
        </if>
        GROUP BY a.id_
        ORDER BY a.create_time_ DESC
    </select>

    <select id="queryById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rail_crane_operation_area a
        WHERE a.id_ = #{id} AND a.delete_state = 0
    </select>

    <select id="selectByMechanicalId" resultMap="BaseResultMap">
        SELECT DISTINCT
        <include refid="Base_Column_List"/>
        FROM rail_crane_operation_area a
                 LEFT JOIN rail_crane_operation_area_ref_mechanical b ON a.id_ = b.operation_area_id_
        WHERE b.mechanical_id_ = #{mechanicalId} AND a.delete_state = 0 AND a.enabled_flag_ = 1
    </select>

    <select id="isLeftAreaAlarm" resultType="boolean">
        SELECT alarm_flag_
        FROM
    </select>
</mapper>