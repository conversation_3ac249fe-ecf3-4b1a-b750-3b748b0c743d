<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.rulecomponent.mapper.RuleComponentServiceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentService">
        <id column="id" property="id"/>
        <result column="device_service_id" property="deviceServiceId"/>
        <result column="device_parm_id" property="deviceParmId"/>
        <result column="device_parm_parent_id" property="deviceParmParentId"/>
        <result column="value" property="value"/>
        <result column="unit" property="unit"/>
        <result column="specs" property="specs"/>
        <result column="sort_no" property="sortNo"/>
        <result column="logic_code" property="logicCode"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="rule_component_id" property="ruleComponentId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
        <collection property="childParamList" column="{deviceParmId=device_parm_id,ruleComponentId=rule_component_id}" select="getParmsNodeById" ofType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentService">
    	</collection>
    </resultMap>


	<resultMap id="getParmsByIdMap"
		type="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentService">
		<id column="id" property="id"/>
        <result column="device_service_id" property="deviceServiceId"/>
        <result column="device_parm_id" property="deviceParmId"/>
        <result column="device_parm_parent_id" property="deviceParmParentId"/>
        <result column="value" property="value"/>
        <result column="unit" property="unit"/>
        <result column="specs" property="specs"/>
        <result column="sort_no" property="sortNo"/>
        <result column="logic_code" property="logicCode"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="rule_component_id" property="ruleComponentId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
        <collection property="childParamList" column="{deviceParmId=device_parm_id,ruleComponentId=rule_component_id}" select="getParmsNodeById" ofType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentService">
        </collection>
	</resultMap>
    
    
    <select id="getParmsNodeById" resultMap="getParmsByIdMap">
		SELECT
       		a.*,
       		b.name deviceServiceName
        FROM
            linkapp_rule_component_service a
            left join linkapp_device_service b on a.device_service_id = b.id
		where a.device_parm_parent_id = #{deviceParmId} and a.rule_component_id=#{ruleComponentId} order by sort_no asc
	</select>

    <select id="getRuleComponentService" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentService">
		SELECT
       		a.*,
       		b.name deviceServiceName
        FROM
            linkapp_rule_component_service a
            left join linkapp_device_service b on a.device_service_id = b.id
        <where>
        	<if test="ruleComponentId != null and ruleComponentId != ''">
	        	and a.rule_component_id = #{ruleComponentId}
	        </if>
	        <if test="tenantId != null and tenantId != ''">
	        	and a.tenant_id = #{tenantId}
	        </if>
        </where>
        ORDER BY a.sort_no
    </select>
    
    
    
    <select id="getRuleComponentServiceList" resultType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentService" parameterType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentService">
		SELECT
       		DISTINCT a.device_service_id,
       		a.rule_component_id,
       		a.tenant_id,
       		b.name deviceServiceName
        FROM
            linkapp_rule_component_service a
            LEFT JOIN linkapp_device_service b ON a.device_service_id = b.id
        <where>
        	<if test="ruleComponentService.ruleComponentId != null and ruleComponentService.ruleComponentId != ''">
	        	and a.rule_component_id = #{ruleComponentService.ruleComponentId}
	        </if>
	        <if test="ruleComponentService.tenantId != null and ruleComponentService.tenantId != ''">
	        	and a.tenant_id = #{ruleComponentService.tenantId}
	        </if>
        </where>
        ORDER BY a.sort_no
    </select>


    <select id="getRuleComponentServices" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentService">
		SELECT
       		a.*,
       		b.name deviceServiceName
        FROM
            linkapp_rule_component_service a
            left join linkapp_device_service b on a.device_service_id = b.id
        <where>
        	<if test="ruleComponentService.ruleComponentId != null and ruleComponentService.ruleComponentId != ''">
	        	and a.rule_component_id = #{ruleComponentService.ruleComponentId}
	        </if>
	        <if test="ruleComponentService.deviceServiceId != null and ruleComponentService.deviceServiceId != ''">
	        	and a.device_service_id = #{ruleComponentService.deviceServiceId}
	        </if>
	        <if test="ruleComponentService.tenantId != null and ruleComponentService.tenantId != ''">
	        	and a.tenant_id = #{ruleComponentService.tenantId}
	        </if>
        </where>
        ORDER BY a.sort_no
    </select>
    
    <select id ="flushDeviceUnitData" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponentAttribute">
    	SELECT e.name deviceServiceName,f.name deviceParmName,g.name deviceParmParentName, d.* FROM linkapp_rule_component_service d
	 	 LEFT JOIN linkapp_rule_component a ON a.id = d.rule_component_id
         LEFT JOIN linkapp_device_unit b ON a.device_unit_id = b.id
         LEFT JOIN linkapp_device_unit c ON b.code = c.code
         LEFT JOIN linkapp_device_service e ON d.device_service_id = e.id
         LEFT JOIN linkapp_device_parm f ON f.device_service_id = d.device_service_id and d.device_parm_id = f.id
         LEFT JOIN linkapp_device_parm g ON g.device_service_id = d.device_service_id and d.device_parm_parent_id = g.id
         <where>
         	<if test="deviceUnitId != null and deviceUnitId != ''">
         		and c.id = #{deviceUnitId}
         	</if>
         	<if test="tenantId != null and tenantId != ''">
         		AND a.tenant_id = #{tenantId}
         	</if>
         </where>
    </select>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into linkapp_rule_component_service
        (id, device_service_id, device_parm_id, device_parm_parent_id, `value`, sort_no,
        logic_code, create_time, creator, modifier, modify_time, tenant_id, rule_component_id,
        unit, specs, `name`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.deviceServiceId,jdbcType=VARCHAR}, #{item.deviceParmId,jdbcType=VARCHAR},
            #{item.deviceParmParentId,jdbcType=VARCHAR}, #{item.value,jdbcType=VARCHAR}, #{item.sortNo,jdbcType=INTEGER},
            #{item.logicCode,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR},
            #{item.modifier,jdbcType=VARCHAR}, #{item.modifyTime,jdbcType=TIMESTAMP}, #{item.tenantId,jdbcType=VARCHAR},
            #{item.ruleComponentId,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.specs,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>
