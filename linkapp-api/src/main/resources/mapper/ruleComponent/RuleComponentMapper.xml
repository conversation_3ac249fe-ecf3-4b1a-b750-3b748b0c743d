<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.rulecomponent.mapper.RuleComponentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponent">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="description" property="description"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="cron" property="cron"/>
        <result column="device_unit_id" property="deviceUnitId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <select id="getRuleComponent" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponent">
		SELECT
       		*
        FROM
            linkapp_rule_component a
        <where>
        	<if test="id != null and id != ''">
	        	and a.id = #{id}
	        </if>
	        <if test="name != null and name != ''">
	        	and a.name like  CONCAT('%',#{name},'%')
	        </if>
	        <if test="type != null and type != ''">
	        	and a.type = #{type}
	        </if>
	        <if test="tenantId != null and tenantId != ''">
	        	and a.tenant_id = #{tenantId}
	        </if>
        </where>
        ORDER BY a.modify_time DESC,a.create_time desc
    </select>


    <select id="getRuleComponents" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.rulecomponent.entity.RuleComponent">
		SELECT
       		a.*
        FROM
            linkapp_rule_component a   
        <where>
	        <if test="ruleComponent.name != null and ruleComponent.name != ''">
	        	and a.name like  CONCAT('%',#{ruleComponent.name},'%')
	        </if>
	        <if test="ruleComponent.types != null and ruleComponent.types != ''">
				and a.type in
				<foreach collection="ruleComponent.types" item="item" open="("
					separator="," close=")">
					#{item}
				</foreach>
			</if>
	        <if test="ruleComponent.tenantId != null and ruleComponent.tenantId != ''">
	        	and a.tenant_id = #{ruleComponent.tenantId}
	        </if>
        </where>
        ORDER BY a.modify_time DESC,a.create_time desc
    </select>

	<insert id="batchInsert" parameterType="map">
		<!--@mbg.generated-->
		insert into linkapp_rule_component
		(id, `name`, `type`, description, create_time, creator, modifier, modify_time, tenant_id,
		cron, device_unit_id)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR},
			#{item.description,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR},
			#{item.modifier,jdbcType=VARCHAR}, #{item.modifyTime,jdbcType=TIMESTAMP}, #{item.tenantId,jdbcType=VARCHAR},
			#{item.cron,jdbcType=VARCHAR}, #{item.deviceUnitId,jdbcType=VARCHAR})
		</foreach>
	</insert>
</mapper>
