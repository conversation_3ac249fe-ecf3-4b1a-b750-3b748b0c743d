<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.partybuild.mapper.PartyBuildInfoMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.partybuild.entity.PartyBuildInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="title_name" jdbcType="VARCHAR" property="titleName"/>
        <result column="resume" jdbcType="VARCHAR" property="resume"/>
        <result column="index_no" jdbcType="INTEGER" property="indexNo"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="page_img" jdbcType="VARCHAR" property="pageImg"/>
        <result column="activity_time" jdbcType="TIMESTAMP" property="activityTime"/>
        <result column="imgs" jdbcType="VARCHAR" property="imgs"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_party_build_info
        where tenant_id = #{appPartyBuildInfo.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_party_build_info
        where id = #{id}
    </select>

    <select id="selectPageBySorts" resultMap="BaseResultMap">
        select *
        from app_party_build_info
        where tenant_id = #{partyBuildInfo.tenantId}
        and delete_state = 1
        <if test="partyBuildInfo.dataType != null">
            and data_type = #{partyBuildInfo.dataType}
        </if>
        <if test="sorts != null and sorts.size() != 0">
            ORDER BY
            <trim suffixOverrides=",">
                <foreach collection="sorts" item="item" index="index">
                    ${item.field} ${item.sortRule},
                </foreach>
            </trim>
        </if>
    </select>
</mapper>
