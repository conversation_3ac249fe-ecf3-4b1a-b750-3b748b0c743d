<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.isoftstone.linkappapi.linkage.mapper.IntelligentTaskMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.isoftstone.linkappapi.linkage.entity.IntelligentTask">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="delay_time" property="delayTime"/>
    <result column="space_id" property="spaceId"/>
    <result column="job_entity_id" property="jobEntityId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="description" property="description"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
  </resultMap>

  <resultMap id="IntelligentTasksMap" type="com.isoftstone.linkappapi.linkage.entity.IntelligentTask">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="delay_time" property="delayTime"/>
    <result column="space_id" property="spaceId"/>
    <result column="space_name" property="spaceName"/>
    <result column="job_entity_id" property="jobEntityId"/>
    <result column="last_execution_time" property="lastExecutionTime"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="description" property="description"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
    <association property="linkappSpace" javaType="com.isoftstone.linkappapi.space.entity.LinkappSpace">
      <result column="space_name" property="spaceName"/>
    </association>
    <association property="jobEntity" javaType="com.isoftstone.linkappapi.taskscheduler.entity.JobEntity">
      <id column="lje_id" property="id"/>
      <result column="cron_expression" property="cronExpression"/>
      <result column="job_status" property="jobStatus"/>
      <result column="job_name" property="jobName"/>
      <result column="job_group" property="jobGroup"/>
    </association>
  </resultMap>

  <select id="getIntelligentTasks" resultMap="IntelligentTasksMap">
    select
    lit.*,
    lje.id lje_id,
    lje.cron_expression,
    lje.job_status,
    lje.job_name,
    lje.job_group,
    s.space_name as space_name
    from linkapp_intelligent_task lit
    left join linkapp_space s on s.id = lit.space_id
    left join linkapp_job_entity lje on lje.id = lit.job_entity_id
    <where>
      lit.delete_state = 1
      <if test='intelligentTask.name!=null and intelligentTask.name!="" '>
        and lit.name LIKE CONCAT('%',#{intelligentTask.name},'%')
      </if>
      <if test='intelligentTask.tenantId!=null and intelligentTask.tenantId!="" '>
        and lit.tenant_id = #{intelligentTask.tenantId}
      </if>
      <if test='intelligentTask.spaceId!=null and intelligentTask.spaceId!="" '>
        and lit.space_id= #{intelligentTask.spaceId}
      </if>
      <if test='intelligentTask.jobEntity!=null and intelligentTask.jobEntity.jobStatus!=null and intelligentTask.jobEntity.jobStatus!="" '>
        and lje.job_status = #{intelligentTask.jobEntity.jobStatus}
      </if>
    </where>
    order by lit.modify_time desc
  </select>


  <resultMap id="getIntelligentTasksByIdsMap" type="com.isoftstone.linkappapi.linkage.entity.IntelligentTask">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="delay_time" property="delayTime"/>
    <association property="jobEntity" javaType="com.isoftstone.linkappapi.taskscheduler.entity.JobEntity">
      <id column="lje_id" property="id"/>
      <result column="job_name" property="jobName"/>
      <result column="job_group" property="jobGroup"/>
    </association>
  </resultMap>

  <select id="getIntelligentTasksByIds" resultMap="getIntelligentTasksByIdsMap">
    select
    lit.*,
    lje.id lje_id,
    lje.job_name,
    lje.job_group
    from linkapp_intelligent_task lit
    left join linkapp_job_entity lje on lje.id = lit.job_entity_id
    <where>
      lit.delete_state = 1 and
      lit.id in
      <foreach collection="ids" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </where>
  </select>

  <resultMap id="getIntelligentTaskMap" type="com.isoftstone.linkappapi.linkage.entity.IntelligentTask">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="delay_time" property="delayTime"/>
    <result column="space_id" property="spaceId"/>
    <result column="space_name" property="spaceName"/>
    <result column="job_entity_id" property="jobEntityId"/>
    <result column="last_execution_time" property="lastExecutionTime"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="description" property="description"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
    <association property="jobEntity" javaType="com.isoftstone.linkappapi.taskscheduler.entity.JobEntity">
      <id column="lje_id" property="id"/>
      <result column="cron_expression" property="cronExpression"/>
      <result column="job_status" property="jobStatus"/>
      <result column="job_name" property="jobName"/>
      <result column="job_group" property="jobGroup"/>
    </association>
<!--    <collection property="linkageConfigRelateServiceParms" column="{id = id}" ofType="com.isoftstone.linkappapi.linkage.entity.LinkageConfigRelateServiceParm" select="com.isoftstone.linkappapi.linkage.mapper.LinkageConfigMapper.selectLinkageConfigRelateServiceParms">-->
<!--    </collection>-->
    <collection property="linkageConfigRefDownDeviceList" ofType="com.isoftstone.linkappapi.linkage.entity.LinkageConfigRefDownDevice">
      <id property="id" column="down_id"/>
      <result property="groupNumber" column="down_groupNumber"/>
      <result property="deviceCode" column="down_deviceCode"/>
<!--      <result property="linkageConfigId" column="down_linkageConfigId"/>-->
      <result property="ruleExecutionId" column="down_linkageConfigId"/>
    </collection>

  </resultMap>

  <select id="getIntelligentTask" resultMap="getIntelligentTaskMap">
    select
    lit.*,
    lje.cron_expression,
    lje.id as lje_id,
    lje.cron_expression,
    lje.job_status,
    lje.job_name,
    lje.job_group,
    lje.job_status,
    down.id as down_id,
    down.group_number as down_groupNumber,
    down.linkage_config_id as down_linkageConfigId,
    down.device_code as down_deviceCode

    from linkapp_intelligent_task lit
    left join linkapp_space s on s.id = lit.space_id
    left join linkapp_job_entity lje on lje.id = lit.job_entity_id
    left join linkage_config_ref_down_device down on down.linkage_config_id = lit.id
    where lit.delete_state = 1 and lit.id=#{id}
  </select>

  <resultMap id="IntelligentTasksByJobNameJobGroupMap" type="com.isoftstone.linkappapi.linkage.entity.IntelligentTask">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="delay_time" property="delayTime"/>
    <result column="space_id" property="spaceId"/>
    <result column="job_entity_id" property="jobEntityId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="description" property="description"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
    <collection property="linkageConfigRefDownDeviceList" ofType="com.isoftstone.linkappapi.linkage.entity.LinkageConfigRefDownDevice">
      <id property="id" column="down_id"/>
      <result property="groupNumber" column="down_groupNumber"/>
      <result property="deviceCode" column="down_deviceCode"/>
<!--      <result property="linkageConfigId" column="down_linkageConfigId"/>-->
      <result property="ruleExecutionId" column="down_linkageConfigId"/>
    </collection>
  </resultMap>

  <select id="getIntelligentTasksByJobNameJobGroup" resultMap="IntelligentTasksByJobNameJobGroupMap">
    select
    lit.*,
    down.id as down_id,
    down.device_code as down_deviceCode,
    down.linkage_config_id as down_linkageConfigId,
    down.group_number as down_groupNumber

    from linkapp_intelligent_task lit
    left join linkapp_job_entity lje on lit.job_entity_id = lje.id
    left join linkage_config_ref_down_device down on down.linkage_config_id = lit.id

    <where>
      lit.delete_state = 1 and
      lje.delete_state = 1
      and lje.job_name = #{jobName}
      and lje.job_group = #{jobGroup}
    </where>
  </select>

</mapper>
