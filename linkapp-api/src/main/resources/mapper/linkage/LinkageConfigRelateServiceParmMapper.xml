<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.linkage.mapper.LinkageConfigRelateServiceParmMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.linkage.entity.LinkageConfigRelateServiceParm">
    <id column="id" property="id"/>
<!--    <result column="linkage_config_id" property="linkageConfigId"/>-->
    <result column="rule_execution_id" property="ruleExecutionId"/>
    <result column="group_number" property="groupNumber"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="device_service_id" property="deviceServiceId"/>
    <result column="device_parm_id" property="deviceParmId"/>
    <result column="parm_value" property="parmValue"/>
    <result column="parent_id" property="parentId"/>
    <result column="array_index" property="arrayIndex"/>
  </resultMap>

  <resultMap id="selectServiceParmExtendMap" type="com.easylinkin.linkappapi.linkage.entity.LinkageConfigRelateServiceParm">
    <id column="id" property="id"/>
<!--    <result column="linkage_config_id" property="linkageConfigId"/>-->
    <result column="rule_execution_id" property="ruleExecutionId"/>
    <result column="group_number" property="groupNumber"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="device_service_id" property="deviceServiceId"/>
    <result column="device_parm_id" property="deviceParmId"/>
    <result column="parm_value" property="parmValue"/>
    <result column="parent_id" property="parentId"/>
    <result column="array_index" property="arrayIndex"/>
    <result column="ds_identifier" property="serviceIdentifier"/>
    <result column="p_identifier" property="identifier"/>
    <result column="p_unit" property="unit"/>
  </resultMap>

  <select id="selectServiceParmExtend" resultMap="selectServiceParmExtendMap">
    select
    crsp.* ,
    ds.identifier as ds_identifier,
    dp.identifier as p_identifier,
    dp.unit as p_unit
    from linkage_config_relate_service_parm crsp
    left join linkapp_device_service ds on crsp.device_service_id = ds.id
    left join linkapp_device_parm dp on crsp.device_parm_id = dp.id
    where 1=1
<!--    and linkage_config_id = #{linkageConfigId}-->
    and rule_execution_id = #{ruleExecutionId}
<!--    and group_number = #{groupNumber}-->
  </select>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into linkage_config_relate_service_parm
    (id, rule_execution_id, group_number, parent_id, array_index,
    device_unit_id, device_service_id, device_parm_id, parm_value)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.ruleExecutionId,jdbcType=VARCHAR},
      #{item.groupNumber,jdbcType=INTEGER}, #{item.parentId,jdbcType=VARCHAR}, #{item.arrayIndex,jdbcType=INTEGER},
      #{item.deviceUnitId,jdbcType=VARCHAR}, #{item.deviceServiceId,jdbcType=VARCHAR},
      #{item.deviceParmId,jdbcType=VARCHAR}, #{item.parmValue,jdbcType=VARCHAR})
    </foreach>
  </insert>

</mapper>
