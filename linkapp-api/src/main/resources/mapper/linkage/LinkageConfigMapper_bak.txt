<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.isoftstone.linkappapi.linkage.mapper.LinkageConfigMapper">


  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.isoftstone.linkappapi.linkage.entity.LinkageConfig">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="delay_time" property="delayTime"/>
    <result column="status" property="status"/>
    <result column="intelligent_rule_id" property="intelligentRuleId"/>
    <result column="delete_state" property="deleteState"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>


  <!-- 列表查询映射结果 -->
  <resultMap id="LinkageConfigsMap" type="com.isoftstone.linkappapi.linkage.entity.LinkageConfig">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="delay_time" property="delayTime"/>
    <result column="status" property="status"/>
    <result column="create_time" property="createTime"/>
    <association property="intelligentRule" javaType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRule">
      <id column="ru_id" property="id"/>
      <result property="name" column="rule_name"/>
      <result property="deviceUnitId" column="rule_deviceUnitId"/>
      <result property="timeScopeCron" column="time_scope_cron"/>
      <result property="commonRuleItem" column="common_rule_item"/>
      <result property="type" column="rule_type"/>
      <association property="deviceUnit" javaType="com.isoftstone.linkappapi.deviceunit.entity.DeviceUnit">
        <result property="deviceTypeName" column="u_deviceTypeName"/>
        <result property="code" column="u_code"/>
        <result property="version" column="version"/>
      </association>
    </association>
  </resultMap>

  <select id="getLinkageConfigs" resultMap="LinkageConfigsMap">
    select
    c.*,
    u.device_type_name as deviceTypeName,
    u.id as rule_deviceUnitId,
    u.code as u_code,
    u.device_type_name as u_deviceTypeName,
    u.version,
    ru.name as rule_name,
    ru.type as rule_type,
    ru.common_rule_item,
    ru.time_scope_cron
    from linkapp_linkage_config c
    left join linkapp_intelligent_rule ru on ru.id = c.intelligent_rule_id
    left join linkapp_device_unit u on u.id = ru.device_unit_id

    <where>
      c.delete_state = 1
      AND ru.id is not null
      <if test="linkageConfig.name!=null and linkageConfig.name!=''">
        AND c.name LIKE CONCAT('%',#{linkageConfig.name},'%')
      </if>
      <if test="linkageConfig.intelligentRuleId!=null and linkageConfig.intelligentRuleId!=''">
        AND c.intelligent_rule_id = #{linkageConfig.intelligentRuleId}
      </if>
      <if test="linkageConfig.status!=null">
        AND c.status = #{linkageConfig.status}
      </if>
    </where>
    order by c.modify_time desc

  </select>


  <!-- 单条查询映射结果 -->
  <resultMap id="LinkageConfigsInfoMap" type="com.isoftstone.linkappapi.linkage.entity.LinkageConfig">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="delay_time" property="delayTime"/>
    <result column="status" property="status"/>
    <result column="intelligent_rule_id" property="intelligentRuleId"/>
    <result column="create_time" property="createTime"/>

    <association property="intelligentRule" javaType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRule">
      <result property="name" column="rule_name"/>
      <result property="timeScopeCron" column="time_scope_cron"/>
      <result property="commonRuleItem" column="common_rule_item"/>
      <result property="type" column="rule_type"/>
    </association>
    <collection property="linkageConfigRelateDeviceCodes" column="id" ofType="java.lang.String" select="selectDeviceCodesByLinkageConfigId">
    </collection>
    <collection property="linkageConfigRelateServiceParms" column="id" ofType="com.isoftstone.linkappapi.linkage.entity.LinkageConfigRelateServiceParm" select="selectLinkageConfigRelateServiceParms">
    </collection>
    <collection property="linkageConfigRefDownDeviceList" ofType="com.isoftstone.linkappapi.linkage.entity.LinkageConfigRefDownDevice">
      <id property="id" column="down_id"/>
      <result property="groupNumber" column="down_groupNumber"/>
      <result property="deviceCode" column="down_deviceCode"/>
      <result property="linkageConfigId" column="down_linkageConfigId"/>
    </collection>
  </resultMap>

  <!-- 单条查询映射结果 -->
  <resultMap id="selectLinkageConfigRelateServiceParmsMap" type="com.isoftstone.linkappapi.linkage.entity.LinkageConfigRelateServiceParm">
    <id column="id" property="id"/>
    <result column="linkage_config_id" property="linkageConfigId"/>
    <result column="group_number" property="groupNumber"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="device_service_id" property="deviceServiceId"/>
    <result column="device_parm_id" property="deviceParmId"/>
    <result column="parm_value" property="parmValue"/>
    <result column="parent_id" property="parentId"/>
    <result column="array_index" property="arrayIndex"/>
    <result column="specs" property="specs"/>
    <result column="unit" property="unit"/>
    <result column="name" property="name"/>
  </resultMap>


  <select id="selectLinkageConfigRelateServiceParms" resultMap="selectLinkageConfigRelateServiceParmsMap">
    SELECT
    p.*,
    ldp.specs,
    ldp.unit,
    ldp.name as name
    FROM linkage_config_relate_service_parm p
    left join linkapp_device_parm ldp on ldp.id = p.device_parm_id
    WHERE linkage_config_id = #{id}
    ORDER BY group_number ASC,device_service_id ASC ,device_parm_id ASC
  </select>

  <select id="selectDeviceCodesByLinkageConfigId" resultType="java.lang.String">
    select device_code from linkapp_linkage_config_ref_device where linkage_config_id = #{id}
  </select>

  <select id="getLinkageConfig" resultMap="LinkageConfigsInfoMap">
    select
    c.*,
    down.id as down_id,
    down.group_number as down_groupNumber,
    down.linkage_config_id as down_linkageConfigId,
    down.device_code as down_deviceCode,
    ru.name as rule_name,
    ru.type as rule_type,
    ru.common_rule_item,
    ru.time_scope_cron
    from linkapp_linkage_config c
    left join linkapp_intelligent_rule ru on ru.id = c.intelligent_rule_id
    left join linkage_config_ref_down_device down on down.linkage_config_id = c.id

    <where>
      c.delete_state = 1
      and c.id = #{id}
    </where>
  </select>


  <!-- 列表查询映射结果 -->
  <resultMap id="getLinkageConfigsHaveRuleByDeviceMap" type="com.isoftstone.linkappapi.linkage.entity.LinkageConfig">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="delay_time" property="delayTime"/>
    <result column="status" property="status"/>
    <result column="create_time" property="createTime"/>
    <association property="intelligentRule" javaType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRule">
      <id column="ru_id" property="id"/>
      <result property="name" column="rule_name"/>
      <result property="timeScopeCron" column="time_scope_cron"/>
      <result property="commonRuleItem" column="common_rule_item"/>
      <result property="type" column="rule_type"/>
    </association>
    <collection property="linkageConfigRefDownDeviceList" ofType="com.isoftstone.linkappapi.linkage.entity.LinkageConfigRefDownDevice">
      <id property="id" column="down_id"/>
      <result property="groupNumber" column="down_groupNumber"/>
      <result property="deviceCode" column="down_deviceCode"/>
      <result property="linkageConfigId" column="down_linkageConfigId"/>
    </collection>
  </resultMap>

  <select id="getLinkageConfigsHaveRuleByDevice" resultMap="getLinkageConfigsHaveRuleByDeviceMap">
    select
    c.*,
    ru.id as ru_id,
    ru.name as ru_name,
    ru.time_scope_cron,
    ru.common_rule_item,
    ru.type as rule_type,
    down.id as down_id,
    down.group_number as down_groupNumber,
    down.linkage_config_id as down_linkageConfigId,
    down.device_code as down_deviceCode

    from linkapp_linkage_config c
    left join linkapp_intelligent_rule ru on ru.id = c.intelligent_rule_id
    left join linkapp_linkage_config_ref_device crd on crd.linkage_config_id = c.id
    left join linkage_config_ref_down_device down on down.linkage_config_id = c.id
    <where>
      c.delete_state = 1
      and ru.id is not null
      and c.status = 2
      and crd.device_code = #{code}

    </where>

  </select>

</mapper>
