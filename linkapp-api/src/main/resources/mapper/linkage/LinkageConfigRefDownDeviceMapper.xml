<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.linkage.mapper.LinkageConfigRefDownDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.linkage.entity.LinkageConfigRefDownDevice">
        <id column="id" property="id"/>
        <result column="rule_execution_id" property="ruleExecutionId"/>
        <result column="device_code" property="deviceCode"/>
    </resultMap>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into linkage_config_ref_down_device
        (id, rule_execution_id, device_code, sort_no
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.ruleExecutionId,jdbcType=VARCHAR},
            #{item.deviceCode,jdbcType=VARCHAR}, #{item.sortNo,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
    <!-- 根据执行器查询 下行关联设备,需要按照设备设定的顺序执行 -->
    <select id="listByRuleExecution" resultType="com.easylinkin.linkappapi.linkage.entity.LinkageConfigRefDownDevice">
        select lcrdd.* from linkage_config_ref_down_device lcrdd inner join linkapp_device ld on lcrdd.device_code =
        ld.code
        <where>
            lcrdd.rule_execution_id = #{ruleExecution.id} and ld.tenant_id = #{ruleExecution.tenantId}
        </where>
        order by sort_no asc;
    </select>

</mapper>
