<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.EpidemicWarningMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.EpidemicWarning">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="user_id_" property="userId" />
        <result column="warning_time_" property="warningTime" />
        <result column="warning_rule_" property="warningRule" />
        <result column="type_" property="type" />
        <result column="temperature_" property="temperature" />
        <result column="health_code_" property="healthCode" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="status_" property="status" />
        <result column="handle_time_" property="handleTime" />
        <result column="handle_remark_" property="handleRemark" />
        <result column="handle_user_" property="handleUser" />
        <result column="operate_type" property="operateType" />
    </resultMap>
    <resultMap id="EpidemicWarningDTOResultMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.lobar.dto.EpidemicWarningDTO">
        <result column="userName" property="userName"/>
        <result column="groupName" property="groupName"/>
        <result column="companyProjectId" property="companyProjectId"/>
        <result column="workType" property="workType"/>
        <result column="telephone" property="telephone"/>
        <result column="card_" property="idCard"/>
        <association property="handleUserInfo" column="handle_user_" select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
    </resultMap>

    <select id="queryList" resultMap="EpidemicWarningDTOResultMap">
        SELECT ew.*,
               ub.name_              userName,
               g.name_               groupName,
               g.company_project_id_ companyProjectId,
               up.work_type_         workType,
               ub.telephone_         telephone,
               ub.card_
        FROM app_epidemic_warning ew
                 left JOIN app_user_project up
                           on ew.user_id_ = up.user_id_ and ew.tenant_id_ = up.tenant_id_
                 left JOIN emp_user_base ub on up.user_id_ = ub.id
                 left JOIN app_group g on up.group_id_ = g.id
        <where>
            <if test="epidemicWarningDTO.tenantId != null and epidemicWarningDTO.tenantId != ''">
                and ew.tenant_id_ = #{epidemicWarningDTO.tenantId}
            </if>
            <if test="epidemicWarningDTO.id != null and epidemicWarningDTO.id != ''">
                and ew.id = #{epidemicWarningDTO.id}
            </if>
            <if
              test="epidemicWarningDTO.companyProjectId != null and epidemicWarningDTO.companyProjectId != ''">
                and g.company_project_id_ = #{epidemicWarningDTO.companyProjectId}
            </if>
            <if test="epidemicWarningDTO.groupId != null and epidemicWarningDTO.groupId != ''">
                and up.group_id_ = #{epidemicWarningDTO.groupId}
            </if>
            <if test="epidemicWarningDTO.workType != null and epidemicWarningDTO.workType != ''">
                and up.work_type_ = #{epidemicWarningDTO.workType}
            </if>
            <if test="epidemicWarningDTO.startTime != null">
                and ew.warning_time_ &gt;= #{epidemicWarningDTO.startTime}
            </if>
            <if test="epidemicWarningDTO.endTime != null">
                and ew.warning_time_ &lt;= #{epidemicWarningDTO.endTime}
            </if>
            <if test="epidemicWarningDTO.status != null">
                and ew.status_ = #{epidemicWarningDTO.status}
            </if>
            <if test="epidemicWarningDTO.userName != null and epidemicWarningDTO.userName != ''">
                and (ub.card_ like concat('%', #{epidemicWarningDTO.userName}, '%') or
                ub.name_ like concat('%', #{epidemicWarningDTO.userName}, '%'))
            </if>
        </where>
        ORDER BY ew.modify_time_ DESC
    </select>
</mapper>
