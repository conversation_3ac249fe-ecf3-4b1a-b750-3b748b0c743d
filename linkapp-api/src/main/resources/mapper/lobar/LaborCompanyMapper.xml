<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.LaborCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.LaborCompany">
        <id column="id" property="id" />
        <result column="name_" property="name" />
        <result column="type_" property="type" />
        <result column="abbreviation_" property="abbreviation" />
        <result column="social_type_" property="socialType" />
        <result column="code_" property="code" />
        <result column="register_time_" property="registerTime" />
        <result column="nature_business" property="natureBusiness" />
        <result column="legal_" property="legal" />
        <result column="legal_telephone_" property="legalTelephone" />
        <result column="address" property="address" />
        <result column="detailed_address_" property="detailedAddress" />
        <result column="create_tenant_id_" property="createTenantId" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>
    <resultMap id="CompanyGroupDtoMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.lobar.dto.LaborCompanyGroupDto">
        <result column="company_project_id" property="companyProjectId"/>
        <collection property="groupList" column="{companyId=id,tenantId=tenant_id}"
                    select="com.easylinkin.linkappapi.lobar.mapper.GroupMapper.selectGroupByMap"/>
    </resultMap>
    <select id="queryListByPage" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        app_labor_company
        where id not in
        (
        SELECT company_id_ FROM app_labor_company_project WHERE tenant_id_ = #{laborCompany.tenantId}
        and delete_state_ = 1
        )
        <if test="laborCompany.name != null and laborCompany.name != ''">
            and name_ like  CONCAT('%',#{laborCompany.name},'%')
        </if>
        ORDER BY modify_time_ DESC
    </select>

    <select id="selectCompanysByIdsStr" parameterType="String" resultMap="BaseResultMap">
        select alc.*
        from app_labor_company alc
        where INSTR(#{idsStr}, alc.id)
    </select>

    <select id="selectCompanyGroupDtoList" parameterType="com.easylinkin.linkappapi.lobar.vo.LaborCompanyGroupVo"
            resultMap="CompanyGroupDtoMap">
        select distinct alc.*,
                        alcp.tenant_id_ as tenant_id,alcp.id as company_project_id
        from app_labor_company alc,
             app_labor_company_project alcp
        where alc.id = alcp.company_id_
        and alcp.delete_state_ = 1
        <if test="laborCompany != null">
            <if test="laborCompany.tenantId != null and laborCompany.tenantId != ''">
                and alcp.tenant_id_ = #{laborCompany.tenantId}
            </if>
            <if test="laborCompany.buildType != null and laborCompany.buildType != ''">
                and alc.build_type_ = #{laborCompany.buildType}
            </if>
        </if>
    </select>

      <select id="countCompany" resultType="java.util.Map">
        SELECT
          a.company_id_ companyId,
          b.name_ name,
          b.build_type_ buildType,
          count( a.tenant_id_ ) a
        FROM
          app_labor_company_project a
          JOIN app_labor_company b ON a.company_id_ = b.id
          JOIN linkapp_tenant c ON a.tenant_id_ = c.id
        WHERE
          a.status_ = 1
          AND a.delete_state_ = 1
          <if test="tenantId != null and tenantId != ''">
            and a.tenant_id_ = #{tenantId}
          </if>
        GROUP BY a.company_id_
        ORDER BY a DESC
      </select>

    <select id="countCompanyPage"
      resultType="com.easylinkin.linkappapi.lobar.dto.app.EnterpriseDTO">
      SELECT
          b.name_ NAME,
          count( DISTINCT(a.tenant_id_) ) projectNum
        FROM
          app_labor_company_project a
          JOIN app_labor_company b ON a.company_id_ = b.id
          JOIN linkapp_tenant c ON a.tenant_id_ = c.id
        WHERE
          a.status_ = 1
          AND a.delete_state_ = 1
        <if test="companyName != null and companyName != ''">
          and b.name_ like  CONCAT('%',#{companyName},'%')
        </if>
        GROUP BY a.company_id_
        ORDER BY projectNum DESC
    </select>
</mapper>
