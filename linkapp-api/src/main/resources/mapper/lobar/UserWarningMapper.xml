<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserWarningMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserWarning">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="user_id_" property="userId" />
        <result column="warning_time_" property="warningTime" />
        <result column="warning_rule_" property="warningRule" />
        <result column="type_" property="type" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="status_" property="status" />
        <result column="handle_time_" property="handleTime" />
        <result column="handle_remark_" property="handleRemark" />
        <result column="handle_user_" property="handleUser" />
        <result column="operate_type" property="operateType" />
    </resultMap>
    <resultMap id="WarningDTOResultMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.lobar.dto.UserWarningDTO">
        <result column="user_name" property="userName"/>
        <result column="group_name" property="groupName"/>
        <result column="company_project_id_" property="companyProjectId"/>
        <result column="work_type_" property="workType"/>
        <result column="telephone_" property="telephone"/>
        <result column="card_" property="idCard"/>
        <association property="handleUserInfo" column="handle_user_" select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
    </resultMap>

    <select id="queryList" resultMap="WarningDTOResultMap">
        SELECT uw.*,
               ub.name_ user_name,
               ub.card_,
               g.name_  group_name,
               g.company_project_id_,
               up.work_type_,
               ub.telephone_
        FROM app_user_warning uw
                 left JOIN app_user_project up
                           on uw.user_id_ = up.user_id_ and uw.tenant_id_ = up.tenant_id_
                 left JOIN emp_user_base ub on up.user_id_ = ub.id
                 left JOIN app_group g on up.group_id_ = g.id
        WHERE uw.tenant_id_ = #{userWarningDTO.tenantId}
        <if test="userWarningDTO.companyProjectId != null and userWarningDTO.companyProjectId != ''">
            and g.company_project_id_ = #{userWarningDTO.companyProjectId}
        </if>
        <if test="userWarningDTO.groupId != null and userWarningDTO.groupId != ''">
            and up.group_id_ = #{userWarningDTO.groupId}
        </if>
        <if test="userWarningDTO.workType != null and userWarningDTO.workType != ''">
            and up.work_type_ = #{userWarningDTO.workType}
        </if>
        <if test="userWarningDTO.startTime != null">
            <![CDATA[ and uw.warning_time_ >= #{userWarningDTO.startTime} ]]>
        </if>
        <if test="userWarningDTO.endTime != null">
            <![CDATA[ and uw.warning_time_ <= #{userWarningDTO.endTime} ]]>
        </if>
        <if test="userWarningDTO.status != null">
            and uw.status_ = #{userWarningDTO.status}
        </if>
        <if test="userWarningDTO.userName != null and userWarningDTO.userName != ''">
            and (ub.card_ like concat('%',#{userWarningDTO.userName}, '%') or ub.name_ like concat('%',#{userWarningDTO.userName}, '%'))
        </if>
        ORDER BY uw.modify_time_ DESC
    </select>

  <select id="findMaxByUserId" resultMap="BaseResultMap">
    SELECT
      uw.user_id_,
      max( uw.warning_time_ ) warning_time_
    FROM
      app_user_warning uw
    WHERE
      uw.tenant_id_ = #{tenantId}
      AND uw.user_id_ IN
      <foreach collection="uIds" item="userId" index="index" open="(" close=")" separator=",">
        #{userId}
      </foreach>
    GROUP BY
      uw.user_id_
  </select>

</mapper>
