<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.ClockConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.ClockConfig">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="name_" property="name" />
        <result column="hour_" property="hour" />
        <result column="count_type_" property="countType" />
        <result column="clock_type_" property="clockType" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="nickname" property="creatorName" />
    </resultMap>

    <select id="queryListByPage" resultMap="BaseResultMap">
        SELECT
          a.*,
          b.nickname
        FROM
          app_clock_config a
          LEFT JOIN linkapp_user b ON a.creator_id_ = b.id
        WHERE
          a.tenant_id_ = #{clockConfig.tenantId}
        ORDER BY a.modify_time_ DESC
    </select>

</mapper>
