<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.GateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.Gate">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="name_" property="name" />
        <result column="brand_" property="brand" />
        <result column="code_" property="code" />
        <result column="position_" property="position" />
        <result column="status_" property="status" />
        <result column="direction_" property="direction" />
        <result column="health_temperature_" property="healthTemperature" />
        <result column="device_id_" property="deviceId" />
        <result column="blade_guard_bind_status_" property="bladeGuardBindStatus"/>
        <result column="blade_guard_project_id_" property="bladeGuardProjectId"/>
        <result column="blade_guard_user_id" property="bladeGuardUserId"/>
        <result column="blade_guard_jing_du" property="bladeGuardJingDu"/>
        <result column="blade_guard_wei_du" property="bladeGuardWeiDu"/>
        <result column="blade_guard_qrcode" property="bladeGuardQRCode"/>
        <result column="blade_guard_project_name" property="bladeGuardProjectName"/>
        <result column="blade_guard_project_address" property="bladeGuardProjectAddress"/>
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="service_area" property="serviceArea" />
        <result column="access_" property="access" />
        <result column="in_area_" property="inArea" />
    </resultMap>

  <select id="queryListByPage" resultType="com.easylinkin.linkappapi.lobar.entity.Gate">
        SELECT
        *
        FROM
        app_gate
        WHERE tenant_id_ = #{gate.tenantId}
        <if test="gate.name != null and gate.name != ''">
          and name_ like  CONCAT('%',#{gate.name},'%')
        </if>
        ORDER BY modify_time_ DESC
  </select>

    <select id="selectGateRecords" resultType="com.easylinkin.linkappapi.lobar.vo.GateRecordVo">
        select aup.user_name_ userName,
        ub.telephone_ telephone,
        g.name_ groupName,
        al.name_ companyName,
        auc.gate_code gateCode,
        (select AG.name_
        from app_gate ag
        where auc.gate_code = AG.code_ and auc.tenant_id_ = ag.tenant_id_) gateName,
        auc.clock_time clockTime,
        auc.photo_ photo
        from app_user_clock auc
        LEFT JOIN app_user_project aup ON auc.tenant_id_ = aup.tenant_id_ AND auc.user_id_ = aup.user_id_
        LEFT JOIN emp_user_base ub ON aup.user_id_ = ub.id
        LEFT JOIN app_group g ON aup.group_id_ = g.id
        LEFT JOIN app_labor_company_project ap ON g.company_project_id_ = ap.id
        LEFT JOIN app_labor_company al ON ap.company_id_ = al.id
        where auc.tenant_id_ = #{query.tenantId}
        <if test="query.userName != null and query.userName != ''">
            and aup.user_name_ like concat('%', #{query.userName}, '%')
        </if>
        <if test="query.gateCode != null and query.gateCode != ''">
            and auc.gate_code in
            <foreach collection="query.gateCode.split(',')" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="query.startTime != null and query.startTime != ''">
            and auc.clock_time &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and auc.clock_time &lt;= #{query.endTime}
        </if>
        order by auc.clock_time desc
    </select>

</mapper>
