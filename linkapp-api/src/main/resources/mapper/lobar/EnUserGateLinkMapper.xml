<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.EnUserGateLinkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.EnUserGateLink">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="gate_id_" property="gateId" />
        <result column="user_id_" property="userId" />
        <result column="user_name_" property="userName" />
        <result column="card_" property="card" />
        <result column="photo_" property="photo" />
        <result column="telephone_" property="telephone" />
        <result column="organization_id_" property="organizationId" />
        <result column="organization_name_" property="organizationName" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="queryListByPage" resultMap="BaseResultMap">
        SELECT
          a.*
        FROM
          app_en_user_gate_link a
        WHERE
          a.tenant_id_ = #{dto.tenantId}
        <if test="dto.gateId != null and dto.gateId != ''">
            AND a.gate_id_ =#{dto.gateId}
        </if>
        <if test="dto.userName != null and dto.userName != ''">
            and (a.user_name_ like  CONCAT('%',#{dto.userName},'%')
            or a.card_ like  CONCAT('%',#{dto.userName},'%')
            or a.telephone_ like  CONCAT('%',#{dto.userName},'%'))
        </if>
    </select>

</mapper>
