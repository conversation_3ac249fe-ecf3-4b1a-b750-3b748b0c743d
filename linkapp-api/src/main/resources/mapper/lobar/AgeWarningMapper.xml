<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.AgeWarningMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.AgeWarning">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="user_id_" property="userId" />
        <result column="warning_time_" property="warningTime" />
        <result column="warning_rule_" property="warningRule" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="status_" property="status" />
        <result column="handle_time_" property="handleTime" />
        <result column="handle_remark_" property="handleRemark" />
        <result column="handle_user_" property="handleUser" />
        <result column="operate_type" property="operateType" />
    </resultMap>
    <resultMap id="AgeWarningDTOResultMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.lobar.dto.AgeWarningDTO">
        <result column="user_name" property="userName"/>
        <result column="group_name" property="groupName"/>
        <result column="company_project_id_" property="companyProjectId"/>
        <result column="work_type_" property="workType"/>
        <result column="telephone_" property="telephone"/>
        <result column="card_" property="idCard"/>
        <result column="gender_" property="gender"/>
        <association property="empUserInfo" column="user_id_" select="com.easylinkin.linkappapi.lobar.mapper.EmpUserBaseMapper.selectById"/>
        <association property="handleUserInfo" column="handle_user_" select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
    </resultMap>

    <select id="queryList" resultMap="AgeWarningDTOResultMap">
        SELECT aw.*,
               ub.name_ user_name,
               ub.card_,
               ub.gender_,
               g.name_  group_name,
               g.company_project_id_,
               up.work_type_,
               ub.telephone_
        FROM app_age_warning aw
                 left JOIN app_user_project up
                           on aw.user_id_ = up.user_id_ and aw.tenant_id_ = up.tenant_id_
                 left JOIN emp_user_base ub on up.user_id_ = ub.id
                 left JOIN app_group g on up.group_id_ = g.id
        WHERE aw.tenant_id_ = #{ageWarningDTO.tenantId}
        <if test="ageWarningDTO.companyProjectId != null and ageWarningDTO.companyProjectId != ''">
            and g.company_project_id_ = #{ageWarningDTO.companyProjectId}
        </if>
        <if test="ageWarningDTO.groupId != null and ageWarningDTO.groupId != ''">
            and up.group_id_ = #{ageWarningDTO.groupId}
        </if>
        <if test="ageWarningDTO.workType != null and ageWarningDTO.workType != ''">
            and up.work_type_ = #{ageWarningDTO.workType}
        </if>
        <if test="ageWarningDTO.userName != null and ageWarningDTO.userName != ''">
            and (ub.card_ like concat('%', #{ageWarningDTO.userName}, '%') or
            ub.name_ like concat('%', #{ageWarningDTO.userName}, '%'))
        </if>
        <if test="ageWarningDTO.startTime != null">
            and aw.warning_time_ &gt;= #{ageWarningDTO.startTime}
        </if>
        <if test="ageWarningDTO.endTime != null">
            and aw.warning_time_ &lt;= #{ageWarningDTO.endTime}
        </if>
        <if test="ageWarningDTO.status != null">
            and aw.status_ = #{ageWarningDTO.status}
        </if>
        <if test="ageWarningDTO.userName != null and ageWarningDTO.userName != ''">
            and (ub.card_ like concat('%',#{ageWarningDTO.userName},'%') or ub.name_ like concat('%',#{ageWarningDTO.userName},'%'))
        </if>
        ORDER BY aw.modify_time_ DESC
    </select>

</mapper>
