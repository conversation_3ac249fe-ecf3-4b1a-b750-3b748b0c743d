<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserBlacklistRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserBlacklistRecord">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="user_id_" property="userId" />
        <result column="type_" property="type" />
        <result column="reason_" property="reason" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="enterprise_source_type_" property="enterpriseSourceType" />
        <result column="enterprise_create_user_name_" property="enterpriseCreateUserName" />
        <result column="enterprise_create_user_id_" property="enterpriseCreateUserId" />
        <result column="enterprise_organization_id_" property="enterpriseOrganizationId" />
        <result column="src_tenant_id_" property="srcTenantId" />
        <result column="src_organization_id_" property="srcOrganizationId" />
        <result column="show_" property="show" />
    </resultMap>

  <select id="queryListByPage"
    resultType="com.easylinkin.linkappapi.lobar.dto.UserBlacklistRecordDTO">
    SELECT
      d.platform_project_name projectName,
      b.join_time_ joinTime,
      if(b.status_ = 0,b.leave_time_,null) leaveTime,
      b.status_ STATUS,
      a.create_time_ createTime,
      a.type_ type,
      if(a.enterprise_source_type_ = 2,a.enterprise_create_user_name_,e.nickname) creatorName,
      a.reason_ reason,
      c.name_ userName,
      c.card_ card,
      a.enterprise_organization_id_ enterpriseOrganizationId,
      f.platform_project_name srcProjectName,
      a.src_organization_id_ srcOrganizationId,
      a.enterprise_source_type_ enterpriseSourceType
    FROM
      app_user_blacklist_record a
      LEFT JOIN app_user_project b ON a.tenant_id_ = b.tenant_id_
      AND a.user_id_ = b.user_id_
      LEFT JOIN emp_user_base c ON a.user_id_ = c.id
      LEFT JOIN linkapp_tenant d ON a.tenant_id_ = d.id
      LEFT JOIN linkapp_user e ON a.creator_id_ = e.id
      LEFT JOIN linkapp_tenant f ON a.src_tenant_id_ = f.id
    <where>
<!--        <if test="dto.tenantId != null and dto.tenantId != ''">-->
<!--          AND a.tenant_id_ = #{dto.tenantId}-->
<!--        </if>-->
        <if test="dto.userName != null and dto.userName != ''">
          AND c.name_ LIKE CONCAT('%',#{dto.userName},'%')
        </if>
        <if test="dto.userId != null and dto.userId != ''">
          AND a.user_id_ = #{dto.userId}
        </if>
        <if test="dto.projectName != null and dto.projectName != ''">
          AND d.platform_project_name LIKE CONCAT('%',#{dto.projectName},'%')
        </if>
        <if test="dto.enterpriseSourceType != null">
          AND a.enterprise_source_type_ = #{dto.enterpriseSourceType}
        </if>
        <if test="dto.srcOrganizationId != null">
          AND a.src_organization_id_ = #{dto.srcOrganizationId}
        </if>
        <if test="dto.show != null">
          AND a.show_ = #{dto.show}
        </if>
    </where>
    ORDER BY
        a.create_time_ DESC
  </select>

</mapper>
