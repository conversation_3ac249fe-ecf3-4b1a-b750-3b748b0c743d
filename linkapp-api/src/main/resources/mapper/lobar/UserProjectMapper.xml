<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserProject">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="user_id_" property="userId" />
        <result column="photo_" property="photo" />
        <result column="user_name_" property="userName" />
        <result column="gender_" property="gender" />
        <result column="birthday_" property="birthday" />
        <result column="no_" property="no" />
        <result column="group_id_" property="groupId" />
        <result column="type_" property="type" />
        <result column="work_type_" property="workType" />
        <result column="is_group_leader_" property="isGroupLeader" />
        <result column="join_time_" property="joinTime" />
        <result column="leave_time_" property="leaveTime" />
        <result column="status_" property="status" />
        <result column="gate_id_" property="gateId" />
        <result column="hat_sn_" property="hatSn" />
        <result column="delete_state_" property="deleteState" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="real_sys_" property="realSys" />
        <result column="on_area_" property="onArea" />
    </resultMap>
    <resultMap id="BaseResultDTO" type="com.easylinkin.linkappapi.lobar.dto.UserProjectDTO" extends="BaseResultMap">
        <result column="groupName" property="groupName"/>
        <result column="companyName" property="companyName"/>
        <result column="card_" property="card"/>
        <result column="card_a_" property="cardA"/>
        <result column="telephone_" property="telephone"/>
        <result column="companyId" property="companyId"/>
        <result column="company_project_id_" property="companyProjectId"/>
        <result column="nation_" property="nation" />
        <result column="build_type_" property="buildType" />
        <result column="abbreviation_" property="abbreviation" />
        <result column="platform_project_name" property="projectName" />
        <result column="project_amount" property="projectAmount" />
        <result column="real_sys_" property="realSys" />
        <result column="education_" property="education" />
        <result column="address_" property="address" />
        <result column="authority_" property="authority" />
        <result column="card_type_" property="cardType" />
        <association property="certNum" column="user_id_"
                     select="com.easylinkin.linkappapi.lobar.mapper.UserCertificateMapper.getMyCertNum"/>
        <association property="blackNum" column="{tenantId=tenant_id_,userId=user_id_}"
                     select="com.easylinkin.linkappapi.lobar.mapper.UserBlacklistMapper.getBlackNumBy"/>
    </resultMap>

  <resultMap id="BaseResultDTO2" type="com.easylinkin.linkappapi.lobar.dto.UserProjectDTO" extends="BaseResultMap">
    <result column="groupName" property="groupName"/>
    <result column="companyName" property="companyName"/>
    <result column="card_" property="card"/>
    <result column="card_a_" property="cardA"/>
    <result column="telephone" property="telephone"/>
    <result column="companyId" property="companyId"/>
    <result column="company_project_id_" property="companyProjectId"/>
    <result column="nation_" property="nation" />
    <result column="build_type_" property="buildType" />
    <result column="abbreviation_" property="abbreviation" />
    <result column="platform_project_name" property="projectName" />
    <result column="project_amount" property="projectAmount" />
    <result column="record_type_" property="recordType" />
    <result column="build_type_" property="buildType" />
  </resultMap>

    <resultMap id="BaseResultDTO3" type="com.easylinkin.linkappapi.lobar.dto.UserProjectDTO" extends="BaseResultMap">
        <result column="groupName" property="groupName"/>
        <result column="companyName" property="companyName"/>
        <result column="card_" property="card"/>
        <result column="card_a_" property="cardA"/>
        <result column="telephone" property="telephone"/>
        <result column="companyId" property="companyId"/>
        <result column="company_project_id_" property="companyProjectId"/>
        <result column="nation_" property="nation" />
        <result column="build_type_" property="buildType" />
        <result column="abbreviation_" property="abbreviation" />
        <result column="platform_project_name" property="projectName" />
        <result column="project_amount" property="projectAmount" />
        <result column="record_type_" property="recordType" />
        <result column="build_type_" property="buildType" />
        <result column="education_" property="education" />
        <result column="address_" property="address" />
        <result column="authority_" property="authority" />
        <result column="card_type_" property="cardType" />
    </resultMap>

  <select id="queryListByPage" resultMap="BaseResultDTO">
        SELECT
        u.*,g.name_ groupName,al.name_ companyName,
        ub.card_a_,
        ub.telephone_ telephone,
        ub.card_, ub.nation_
        FROM app_user_project u
        LEFT JOIN emp_user_base ub  ON u.user_id_ = ub.id
        left JOIN app_group g on u.group_id_ = g.id
        left JOIN app_labor_company_project ap on g.company_project_id_ = ap.id
        left JOIN app_labor_company al on ap.company_id_ = al.id
    WHERE
        u.delete_state_ = 1
        <if test="userProject.tenantId != null and userProject.tenantId != ''">
          and u.tenant_id_ = #{userProject.tenantId}
        </if>
<!--        <if test="userProject.blackFlag == '1'.toString() ">-->
<!--          and abl.id is not null-->
<!--        </if>-->
<!--        <if test="userProject.blackFlag == '0'.toString() ">-->
<!--          and abl.id is null-->
<!--        </if>-->
        <if test="userProject.userName != null and userProject.userName != ''">
            and (u.user_name_ like  CONCAT('%',#{userProject.userName},'%') or ub.card_ like CONCAT('%',#{userProject.userName},'%') )
        </if>
        <if test="userProject.groupName != null and userProject.groupName != ''">
          and g.name_ like  CONCAT('%',#{userProject.groupName},'%')
        </if>

        <if test="userProject.status != null and userProject.status != 2">
            and u.status_ = #{userProject.status}
        </if>
        <if test="userProject.eduState != null and userProject.eduState == 1">
            and u.edu_state_ = #{userProject.eduState}
        </if>
        <if test="userProject.eduState != null and userProject.eduState == 0">
            and IFNULL(u.edu_state_, '') != 1
        </if>
        <if test="userProject.workType != null and userProject.workType != ''">
            and u.work_type_ = #{userProject.workType}
        </if>
        <if test="userProject.gateStatus != null">
            and u.gate_status_ = #{userProject.gateStatus}
        </if>
        <if test="userProject.groupId != null and userProject.groupId != ''">
            and u.group_id_ = #{userProject.groupId}
        </if>
        <if test="userProject.gateId != null and userProject.gateId != ''">
            and u.user_id_ not in (
            SELECT ul.user_id_ FROM app_user_gate_link ul WHERE ul.gate_id_ = #{userProject.gateId})
        </if>
        <if test="userProject.notUserIds !=null and userProject.notUserIds.size>0">
          and u.user_id_ not in
          <foreach collection="userProject.notUserIds" index="index" item="userId" open="(" separator="," close=")">
            #{userId}
          </foreach>
        </if>
        <if test="userProject.userIds !=null and userProject.userIds.size>0">
          and u.user_id_ in
          <foreach collection="userProject.userIds" index="index" item="userId" open="(" separator="," close=")">
            #{userId}
          </foreach>
        </if>
        <if test="userProject.companyProjectId != null and userProject.companyProjectId != ''">
            and g.company_project_id_ = #{userProject.companyProjectId}
        </if>
        <if test="userProject.keyword != null and userProject.keyword != ''">
            and (u.user_name_ like  CONCAT('%',#{userProject.keyword},'%')
            or u.no_ like  CONCAT('%',#{userProject.keyword},'%')
            or ub.telephone_ like  CONCAT('%',#{userProject.keyword},'%'))
        </if>
        <if test='userProject.peopleBind == "1"'>
            AND NOT EXISTS(SELECT 1 FROM linkapp_location_device lld WHERE u.id = lld.user_id_)
        </if>
        ORDER BY u.modify_time_ DESC
    </select>

    <select id="findUserGate" resultMap="BaseResultDTO">
        SELECT
        ul.id,
        eu.photo_,
        up.user_name_,
        up.gender_,
        up.birthday_,
        up.group_id_,
        up.work_type_,
        up.type_,
        up.tenant_id_,
        up.user_id_,
        g.name_ groupName
        FROM app_user_gate_link ul
        left JOIN app_user_project up
        left join emp_user_base eu on up.user_id_ = eu.id
        on ul.user_id_ = up.user_id_ and ul.tenant_id_ = up.tenant_id_
        left JOIN app_group g on up.group_id_ = g.id
        WHERE ul.tenant_id_ = #{userProject.tenantId}
        <if test="userProject.userName != null and userProject.userName != ''">
            and up.user_name_ like  CONCAT('%',#{userProject.userName},'%')
        </if>
        <if test="userProject.gateId != null and userProject.gateId != ''">
            and ul.gate_id_ = #{userProject.gateId}
        </if>
        <if test="userProject.groupId != null and userProject.groupId != ''">
            and up.group_id_ = #{userProject.groupId}
        </if>
        ORDER BY ul.modify_time_ DESC
    </select>

    <select id="selectByConfig" resultMap="BaseResultMap">
        SELECT
        up.*
        FROM
        app_user_project up
        WHERE
        up.tenant_id_ = #{tenantId}
        AND up.status_ = 1
        AND DATE_SUB( CURDATE( ), INTERVAL #{absent} DAY ) &gt;=  date(up.join_time_)
        AND up.user_id_ NOT IN
        ( SELECT user_id_ FROM app_user_record WHERE DATE_SUB( CURDATE( ), INTERVAL #{absent} DAY )
         &lt;=   date( record_time_ ) AND tenant_id_ = #{tenantId} AND record_type_ != 0)
    </select>

  <select id="countUser" resultMap="BaseResultDTO">
      SELECT
      u.*,al.name_ companyName,ub.card_
      FROM app_user_project u
      left JOIN emp_user_base ub on user_id_ = ub.id
      left JOIN app_group g on u.group_id_ = g.id
      left JOIN app_labor_company_project ap on g.company_project_id_ = ap.id
      left JOIN app_labor_company al on ap.company_id_ = al.id
      WHERE
        u.status_ = 1
      <if test="tenantId != null and tenantId != ''">
        AND u.tenant_id_ = #{tenantId}
      </if>
      <if test="buildTypes!=null and buildTypes.size>0">
        and al.build_type_ in
        <foreach collection="buildTypes" index="index" item="buildType" open="(" separator="," close=")">
          #{buildType}
        </foreach>
      </if>
      ORDER BY u.modify_time_ DESC
  </select>

  <select id="countUser2" resultMap="BaseResultDTO2">
    SELECT
    u.*,al.name_ companyName,ub.card_
    FROM app_user_project u
    left JOIN emp_user_base ub on user_id_ = ub.id
    left JOIN app_group g on u.group_id_ = g.id
    left JOIN app_labor_company_project ap on g.company_project_id_ = ap.id
    left JOIN app_labor_company al on ap.company_id_ = al.id
    WHERE
    u.status_ = 1
    <if test="tenantId != null and tenantId != ''">
      AND u.tenant_id_ = #{tenantId}
    </if>
    <if test="companyId != null and companyId != ''">
      AND ap.company_id_ = #{companyId}
    </if>
    <if test="buildTypes!=null and buildTypes.size>0">
      and al.build_type_ in
      <foreach collection="buildTypes" index="index" item="buildType" open="(" separator="," close=")">
        #{buildType}
      </foreach>
    </if>
    ORDER BY u.modify_time_ DESC
  </select>
    <select id="selectAll" resultType="com.easylinkin.linkappapi.lobar.dto.UserProjectDTO">
        SELECT
        u.tenant_id_ tenantId,
        u.type_ type,
        u.group_id_,
        g.company_project_id_ companyProjectId,
        u.user_id_ userId,
        u.work_type_ workType
        FROM app_user_project u
        left JOIN app_group g on u.group_id_ = g.id
        where u.status_ = 1
        <if test="tenantId != null and tenantId != ''">
            AND u.tenant_id_ = #{tenantId}
        </if>
    </select>

    <select id="queryUserProjectDtoList"
      parameterType="com.easylinkin.linkappapi.lobar.vo.UserProjectVo" resultMap="BaseResultDTO">
        select u.*,
               g.name_        as groupName,
               al.id as companyId,
               al.name_       as companyName,
               eub.telephone_ as telephone,
                eub.education_,
                eub.card_type_,
                eub.address_,
                eub.authority_
        from app_user_project u
                 left join app_group g on
            u.group_id_ = g.id
                 left join app_labor_company_project ap on
            g.company_project_id_ = ap.id
                 left join app_labor_company al on
            ap.company_id_ = al.id
                 left join emp_user_base eub on u.user_id_ = eub.id
        where u.delete_state_ = 1 and u.status_ = 1
        <if test="userProject != null ">
            <if test="userProject.tenantId != null and userProject.tenantId != ''">
                and u.tenant_id_ = #{userProject.tenantId}
            </if>
            <if test="userProject.type != null and userProject.type != ''">
                and u.type_ = #{userProject.type}
            </if>
            <if test="userProject.workType != null and userProject.workType != ''">
                and u.work_type_ like concat(#{userProject.workType}, '%')
            </if>
            <if test="userProject.groupId != null and userProject.groupId != ''">
                and u.group_id_ = #{userProject.groupId}
            </if>
        </if>
        <if test="userCompany != null ">
            <if test="userCompany.id != null and userCompany.id != ''">
                and al.id = #{userCompany.id}
            </if>
        </if>
        <if test="paramKey != null and paramKey != ''">
            and (eub.telephone_ like concat('%', #{paramKey}, '%')
                or u.user_name_ like concat('%', #{paramKey}, '%'))
        </if>
    </select>

    <select id="queryUserProjectDtoList1"
            parameterType="com.easylinkin.linkappapi.lobar.vo.UserProjectVo" resultMap="BaseResultDTO3">
        select u.*,
        eub.card_,
        eub.card_a_,
        g.name_        as groupName,
        al.id as companyId,
        al.name_       as companyName,
        eub.telephone_ as telephone,
        eub.education_,
        eub.card_type_,
        eub.address_,
        eub.authority_
        from app_user_project u
        left join app_group g on u.group_id_ = g.id and g.tenant_id_ = u.tenant_id_
        left join app_labor_company_project ap on g.company_project_id_ = ap.id and ap.delete_state_ = 1
        left join app_labor_company al on ap.company_id_ = al.id
        left join emp_user_base eub on u.user_id_ = eub.id
        where u.delete_state_ = 1
        <if test="userProject != null ">
            <if test="userProject.tenantId != null and userProject.tenantId != ''">
                and u.tenant_id_ = #{userProject.tenantId}
            </if>
        </if>
    </select>

  <select id="findByCard" resultMap="BaseResultDTO">
      SELECT
        a.telephone_,
        a.name_ user_name_,
        c.company_project_id_,
        b.*
      FROM
        emp_user_base a
        LEFT JOIN app_user_project b ON a.id = b.user_id_
        LEFT JOIN app_group c ON b.group_id_ = c.id
      WHERE
        a.card_ = #{card}
        AND b.status_ = 1
  </select>

  <select id="findByUserId" resultMap="BaseResultDTO">
      SELECT
        b.company_project_id_,
        b.name_ groupName,
        d.name_ companyName,
        a.*
      FROM
        app_user_project a
        LEFT JOIN app_group b ON a.group_id_ = b.id
        LEFT JOIN app_labor_company_project c ON b.company_project_id_ = c.id
        LEFT JOIN app_labor_company d ON c.company_id_ = d.id
      WHERE
        a.user_id_ = #{userId}
        AND a.delete_state_ = 1
        AND a.tenant_id_ = #{tenantId}
  </select>

  <select id="selectByCard" resultMap="BaseResultMap">
      SELECT
          a.*,
          b.photo_
        FROM
          app_user_project a
          LEFT JOIN emp_user_base b ON a.user_id_ = b.id
        WHERE
          b.card_ = #{card}
  </select>
  <select id="getRecordByWorkType" resultMap="BaseResultMap">
      SELECT
      a.work_type_,
      b.id
    FROM
      app_user_project a
      LEFT JOIN app_user_record b ON a.user_id_ = b.user_id_
      AND a.tenant_id_ = b.tenant_id_
      AND b.record_type_ != 0
      AND to_days( b.record_time_ ) = to_days( now( ) )
    WHERE
      a.status_ = 1
      AND a.tenant_id_ = #{tenantId}
      AND a.work_type_ LIKE CONCAT(#{workType},'%')
  </select>

    <select id="findById" resultMap="BaseResultDTO">
        SELECT
          a.*,
          b.name_ groupName,
          d.name_ companyName,
          b.company_project_id_
        FROM
          app_user_project a
          LEFT JOIN app_group b ON a.group_id_ = b.id
          LEFT JOIN app_labor_company_project c ON b.company_project_id_ = c.id
          LEFT JOIN app_labor_company d ON c.company_id_ = d.id
        WHERE
          a.id = #{id}
    </select>

  <select id="findByGateId" resultMap="BaseResultDTO">
    SELECT
        a.telephone_,
        a.name_ user_name_,
        c.company_project_id_,
        b.*
      FROM
        emp_user_base a
        LEFT JOIN app_user_project b ON a.id = b.user_id_
        LEFT JOIN app_group c ON b.group_id_ = c.id
      WHERE
        a.gate_id_ = #{gateId}
        AND b.status_ = 1
  </select>

  <select id="findByCardOrGateId" resultMap="BaseResultDTO">
    SELECT
        a.telephone_,
        a.name_ user_name_,
        a.card_,
        c.company_project_id_,
        b.*
      FROM
        emp_user_base a
        LEFT JOIN app_user_project b ON a.id = b.user_id_
        LEFT JOIN app_group c ON b.group_id_ = c.id
      WHERE
        b.status_ = 1
        AND  (a.gate_id_ = #{cardOrGateId} or a.card_ = #{cardOrGateId})
  </select>

    <select id="findByCardOrGateIdToDock" resultMap="BaseResultDTO3">
        SELECT
            a.telephone_,
            a.name_ user_name_,
            a.card_,
            c.company_project_id_,
            b.*
        FROM
            emp_user_base a
                LEFT JOIN app_user_project b ON a.id = b.user_id_
                LEFT JOIN app_group c ON b.group_id_ = c.id
        WHERE
            1=1
          AND  (a.gate_id_ = #{cardOrGateId} or a.card_ = #{cardOrGateId})
    </select>

    <select id="countUserNumBy" resultType="java.lang.Integer">
        select count(*) from app_user_project aup
        <where>
            and delete_state_ = 1
            <if test="status != null">
                and aup.status_ = #{status}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and aup.tenant_id_ = #{tenantId}
            </if>
        </where>
    </select>

  <select id="findNewJoin" resultType="com.easylinkin.linkappapi.lobar.dto.UserProjectDTO">
    SELECT
      b.name_ userName,
      a.join_time_ joinTime,
      b.photo_ photo,
      a.work_type_ workType,
	    c.name_ groupName
    FROM
      app_user_project a
      LEFT JOIN emp_user_base b ON a.user_id_ = b.id
      left JOIN app_group c on c.id = a.group_id_
    WHERE
      a.tenant_id_ = #{tenantId}
      AND a.status_ = 1
    ORDER BY
      a.join_time_ DESC
      LIMIT #{size}
  </select>

    <select id="countUserNumByGroupId" parameterType="com.easylinkin.linkappapi.lobar.entity.UserProject" resultType="int">
        SELECT
            count(*)
        FROM app_user_project u
        LEFT JOIN emp_user_base ub  ON u.user_id_ = ub.id
        left JOIN app_group g on u.group_id_ = g.id
        left JOIN app_labor_company_project ap on g.company_project_id_ = ap.id
        left JOIN app_labor_company al on ap.company_id_ = al.id
        WHERE u.delete_state_ = 1
            <if test="tenantId != null and tenantId != ''">
                and u.tenant_id_ = #{tenantId}
            </if>
        <if test="groupId != null and groupId != ''">
            and u.group_id_ = #{groupId}
        </if>
    </select>
  <select id="findUserRegionByIds" resultType="com.easylinkin.linkappapi.lobar.dto.region.UserRegionDTO">
    SELECT
      a.id userId,
      b.name_ userName,
      c.work_type_ workType,
      d.name_ groupName,
      e.name_ companyName,
      e.build_type_ buildType
    FROM
      linkapp_user a
      JOIN emp_user_base b ON a.phone = b.telephone_
      JOIN app_user_project c ON b.id = c.user_id_ and c.status_ = 1 AND c.delete_state_ = 1
      AND c.tenant_id_ = #{tenantId}
      LEFT JOIN app_group d ON c.group_id_ = d.id
      LEFT JOIN app_labor_company_project f ON d.company_project_id_ = f.id
      LEFT JOIN app_labor_company e ON f.company_id_ = e.id
    WHERE
      a.id IN
    <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>
  <select id="findUserRByIds" resultType="com.easylinkin.linkappapi.lobar.dto.region.UserRegionDTO">
    SELECT
      a.id userId,
      c.user_id_ baseUserId
    FROM
        linkapp_user a
    LEFT JOIN emp_user_base b ON a.phone = b.telephone_
    LEFT JOIN app_user_project c ON b.id = c.user_id_
    WHERE
    a.id IN
    <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    and c.status_ = 1 AND c.delete_state_ = 1
    and c.tenant_id_ = #{tenantId}
  </select>

  <select id="findByTenantId" resultMap="BaseResultDTO">
    SELECT
      a.*,
      b.name_ groupName,
      d.id companyId,
      d.name_ companyName,
      b.company_project_id_,
      d.abbreviation_,
      d.build_type_,
      e.platform_project_name,
      e.project_amount
    FROM
      app_user_project a
      LEFT JOIN app_group b ON a.group_id_ = b.id
      LEFT JOIN app_labor_company_project c ON b.company_project_id_ = c.id
      LEFT JOIN app_labor_company d ON c.company_id_ = d.id
      LEFT JOIN linkapp_tenant e on a.tenant_id_ = e.id
    WHERE
      a.status_ = 1 AND a.delete_state_ = 1
      <if test="tenantId != null and tenantId != ''">
        and a.tenant_id_ = #{tenantId}
      </if>
  </select>

  <select id="findByTenantId2" resultMap="BaseResultDTO2">
    SELECT
    a.*,
    b.name_ groupName,
    d.id companyId,
    d.name_ companyName,
    b.company_project_id_,
    d.abbreviation_,
    d.build_type_,
    e.platform_project_name,
    e.project_amount
    FROM
    app_user_project a
    LEFT JOIN app_group b ON a.group_id_ = b.id
    LEFT JOIN app_labor_company_project c ON b.company_project_id_ = c.id
    LEFT JOIN app_labor_company d ON c.company_id_ = d.id
    LEFT JOIN linkapp_tenant e on a.tenant_id_ = e.id
    WHERE
    a.status_ = 1 AND a.delete_state_ = 1
    <if test="queryParams.tenantId != null and queryParams.tenantId != ''">
      and a.tenant_id_ = #{queryParams.tenantId}
    </if>
    <if test="queryParams.tenantIds !=null and queryParams.tenantIds.size>0">
      and a.tenant_id_ in
      <foreach collection="queryParams.tenantIds" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="findByCompany" resultMap="BaseResultDTO2">
      SELECT
        b.name_ companyName,
        e.id tenant_id_,
        e.platform_project_name,
        e.project_amount,
        d.id,
        d.user_id_
      FROM
        app_labor_company_project a
        LEFT JOIN app_labor_company b ON a.company_id_ = b.id
        LEFT JOIN app_group c ON a.id = c.company_project_id_
        LEFT JOIN app_user_project d ON c.id = d.group_id_
        AND d.status_ = 1
        AND d.delete_state_ = 1
        LEFT JOIN linkapp_tenant e ON a.tenant_id_ = e.id
      WHERE
        a.status_ = 1
        AND a.delete_state_ = 1
        AND e.id IS NOT NULL
  </select>

  <select id="findByGateLinkId" resultMap="BaseResultDTO">
    	SELECT
        d.telephone_,
        d.name_ user_name_,
        d.card_,
        c.company_project_id_,
        b.*
      FROM
        app_user_gate_link a
        LEFT JOIN app_user_project b ON a.user_id_ = b.user_id_ and b.status_ = 1
        LEFT JOIN app_group c ON b.group_id_ = c.id
        LEFT JOIN emp_user_base d ON a.user_id_ = d.id
      WHERE
       a.id = #{id}
  </select>

  <select id="findOnByTenantId2" resultMap="BaseResultDTO2">
    SELECT
      a.*,
      b.name_ groupName,
      d.id companyId,
      d.name_ companyName,
      b.company_project_id_,
      d.abbreviation_,
      d.build_type_,
      e.platform_project_name,
      e.project_amount
      FROM
      app_user_project a
      LEFT JOIN app_group b ON a.group_id_ = b.id
      LEFT JOIN app_labor_company_project c ON b.company_project_id_ = c.id
      LEFT JOIN app_labor_company d ON c.company_id_ = d.id
      LEFT JOIN linkapp_tenant e on a.tenant_id_ = e.id
      WHERE
      a.delete_state_ = 1
      and to_days(a.join_time_) = to_days(now())
      <if test="queryParams.tenantId != null and queryParams.tenantId != ''">
        and a.tenant_id_ = #{queryParams.tenantId}
      </if>
      <if test="queryParams.tenantIds !=null and queryParams.tenantIds.size>0">
        and a.tenant_id_ in
        <foreach collection="queryParams.tenantIds" index="index" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
  </select>

  <select id="queryEnListByPage" resultMap="BaseResultDTO2">
    SELECT DISTINCT
      ub.id user_id_,
      ub.card_a_,
      ub.telephone_ telephone,
      ub.card_,
      ub.nation_,
      ub.photo_,
      ub.gender_,
      ub.birthday_,
      ub.work_type_,
      ub.name_ user_name_
    FROM
    emp_user_base ub
    LEFT JOIN app_user_project u ON u.user_id_ = ub.id
    <if test="userProject.blackFlag != null">
      LEFT JOIN app_user_blacklist abl ON ub.id = abl.user_id_
    </if>
    <where>
      <if test="userProject.blackFlag != null">
        u.delete_state_ = 1
      </if>
      <if test="userProject.tenantId != null and userProject.tenantId != ''">
        and u.tenant_id_ = #{userProject.tenantId}
      </if>
      <if test="userProject.blackFlag == '1'.toString() ">
        and abl.id is not null
      </if>
      <if test="userProject.blackFlag == '0'.toString() ">
        and abl.id is null
      </if>
      <if test="userProject.userName != null and userProject.userName != ''">
        and (u.user_name_ like  CONCAT('%',#{userProject.userName},'%') or ub.card_ like CONCAT('%',#{userProject.userName},'%') )
      </if>
      <if test="userProject.status != null and userProject.status != 2">
        and u.status_ = #{userProject.status}
      </if>
      <if test="userProject.workType != null and userProject.workType != ''">
        and u.work_type_ = #{userProject.workType}
      </if>
      <if test="userProject.gateStatus != null">
        and u.gate_status_ = #{userProject.gateStatus}
      </if>
      <if test="userProject.groupId != null and userProject.groupId != ''">
        and u.group_id_ = #{userProject.groupId}
      </if>
      <if test="userProject.gateId != null and userProject.gateId != ''">
        and u.user_id_ not in (
        SELECT ul.user_id_ FROM app_user_gate_link ul WHERE ul.gate_id_ = #{userProject.gateId})
      </if>
      <if test="userProject.keyword != null and userProject.keyword != ''">
        and (u.user_name_ like  CONCAT('%',#{userProject.keyword},'%')
        or u.no_ like  CONCAT('%',#{userProject.keyword},'%')
        or ub.name_ like  CONCAT('%',#{userProject.keyword},'%')
        or ub.telephone_ like  CONCAT('%',#{userProject.keyword},'%'))
      </if>
    </where>
    ORDER BY u.modify_time_ DESC
  </select>

  <select id="countAllByEnterprise" resultMap="BaseResultMap">
    SELECT
      a.*
    FROM
      app_user_project a
      LEFT JOIN linkapp_tenant b ON a.tenant_id_ = b.id
    WHERE
      a.status_ = 1
      AND a.delete_state_ = 1
      <if test="projectName != null and projectName != ''">
        AND b.platform_project_name LIKE CONCAT('%',#{projectName},'%')
      </if>
      <if test="tenantIds !=null and tenantIds.size>0">
        AND a.tenant_id_ IN
        <foreach collection="tenantIds" index="index" item="tenantId" open="(" separator="," close=")">
          #{tenantId}
        </foreach>
      </if>
      <if test="projectIds !=null and projectIds.size>0">
        and b.project_id in
        <foreach collection="projectIds" index="index" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
  </select>

  <select id="countInToday" resultType="java.lang.Integer">
    SELECT
    count( a.user_id_ )
    FROM
    app_user_project a
    LEFT JOIN app_group b ON a.group_id_ = b.id
    LEFT JOIN app_labor_company_project c ON b.company_project_id_ = c.id
    LEFT JOIN app_labor_company d ON c.company_id_ = d.id
    WHERE
    a.status_ = 1
    AND a.delete_state_ = 1
    <if test="tenantId != null and tenantId != ''">
      AND a.tenant_id_ = #{tenantId}
    </if>
    <if test="userType == 1">
      AND a.work_type_ LIKE '管理人员%' AND d.build_type_ = '13'
    </if>
    <if test="userType == 2">
      AND a.work_type_ LIKE '管理人员%' AND d.build_type_ != '13'
    </if>
    <if test="userType == 3">
      AND (a.work_type_ LIKE '作业人员%' or a.work_type_ LIKE '其他人员%')
    </if>
  </select>
  <select id="queryBlackListByPage" resultMap="BaseResultDTO2">
    SELECT
    u.*,g.name_ groupName,
    ub.card_a_,
    ub.telephone_ telephone,
    ub.card_, ub.nation_,
    ub.real_sys_
    FROM app_user_project u
    LEFT JOIN emp_user_base ub  ON u.user_id_ = ub.id
    left JOIN app_group g on u.group_id_ = g.id
    WHERE
    u.delete_state_ = 1
    <if test="userProject.tenantId != null and userProject.tenantId != ''">
      and u.tenant_id_ = #{userProject.tenantId}
    </if>
    <if test="userProject.userName != null and userProject.userName != ''">
      and (u.user_name_ like  CONCAT('%',#{userProject.userName},'%') or ub.card_ like CONCAT('%',#{userProject.userName},'%') )
    </if>
    <if test="userProject.groupName != null and userProject.groupName != ''">
      and g.name_ like  CONCAT('%',#{userProject.groupName},'%')
    </if>

    <if test="userProject.status != null and userProject.status != 2">
      and u.status_ = #{userProject.status}
    </if>
    <if test="userProject.workType != null and userProject.workType != ''">
      and u.work_type_ = #{userProject.workType}
    </if>
    <if test="userProject.gateStatus != null">
      and u.gate_status_ = #{userProject.gateStatus}
    </if>
    <if test="userProject.groupId != null and userProject.groupId != ''">
      and u.group_id_ = #{userProject.groupId}
    </if>
    <if test="userProject.gateId != null and userProject.gateId != ''">
      and u.user_id_ not in (
      SELECT ul.user_id_ FROM app_user_gate_link ul WHERE ul.gate_id_ = #{userProject.gateId})
    </if>
    <if test="userProject.notUserIds !=null and userProject.notUserIds.size>0">
      and u.user_id_ not in
      <foreach collection="userProject.notUserIds" index="index" item="userId" open="(" separator="," close=")">
        #{userId}
      </foreach>
    </if>
    <if test="userProject.userIds !=null and userProject.userIds.size>0">
      and u.user_id_ in
      <foreach collection="userProject.userIds" index="index" item="userId" open="(" separator="," close=")">
        #{userId}
      </foreach>
    </if>
    <if test="userProject.companyProjectId != null and userProject.companyProjectId != ''">
      and g.company_project_id_ = #{userProject.companyProjectId}
    </if>
    <if test="userProject.keyword != null and userProject.keyword != ''">
      and (u.user_name_ like  CONCAT('%',#{userProject.keyword},'%')
      or u.no_ like  CONCAT('%',#{userProject.keyword},'%')
      or ub.telephone_ like  CONCAT('%',#{userProject.keyword},'%'))
    </if>
    ORDER BY u.modify_time_ DESC
  </select>

  <select id="getOnArea" resultMap="BaseResultDTO2">
    SELECT
    a.*,
    d.company_project_id_
    FROM
    app_user_project a
    LEFT JOIN app_group d on a.group_id_ = d.id
    LEFT JOIN app_labor_company_project b ON d.company_project_id_ = b.id
    LEFT JOIN app_labor_company c ON b.company_id_ = c.id
    WHERE
    a.tenant_id_ = #{tenantId}
    AND a.delete_state_ = 1 AND a.status_ = 1
    AND a.on_area_ IN (1,2,3)
    <if test="buildTypes !=null and buildTypes.size>0">
      AND c.build_type_ IN
      <foreach collection="buildTypes" index="index" item="buildType" open="(" separator="," close=")">
        #{buildType}
      </foreach>
    </if>
  </select>

  <select id="onAreasList" resultType="com.easylinkin.linkappapi.lobar.dto.UserProjectDTO">
    SELECT
      a.id id,
      a.user_id_ userId,
      b.photo_ photo,
      b.gender_ gender,
      b.name_ userName,
      e.name_ companyName,
      c.name_ groupName,
      a.work_type_ workType,
      a.on_area_ onArea
    FROM
      app_user_project a
      LEFT JOIN emp_user_base b ON a.user_id_ = b.id
      LEFT JOIN app_group c ON a.group_id_ = c.id
      LEFT JOIN app_labor_company_project d ON c.company_project_id_ = d.id
      LEFT JOIN app_labor_company e ON d.company_id_ = e.id
    WHERE
      a.tenant_id_ = #{dto.tenantId}
      and a.delete_state_ = 1 and a.status_ = 1
      <if test="dto.groupId != null and dto.groupId != ''">
        and a.group_id_ = #{dto.groupId}
      </if>
      <if test="dto.keyword != null and dto.keyword != ''">
        and (b.name_ like  CONCAT('%',#{dto.keyword},'%')
        or b.card_ like  CONCAT('%',#{dto.keyword},'%')
        or b.telephone_ like  CONCAT('%',#{dto.keyword},'%'))
      </if>
      <if test="dto.onArea != null">
        and a.on_area_ = #{dto.onArea}
      </if>
      <if test="dto.onArea == null">
        and a.on_area_ in (1,2,3)
      </if>
    ORDER BY
      a.modify_time_ DESC
  </select>

    <select id="listUserCountByProject" resultType="com.easylinkin.linkappapi.lobar.dto.UserCountByProjectDTO">
        <!--        SELECT count(u.id)             count,-->
        <!--               t.platform_project_name projectName-->
        <!--        FROM app_user_project u-->
        <!--                 LEFT JOIN emp_user_base ub ON u.user_id_ = ub.id-->
        <!--                 LEFT JOIN app_group g ON u.group_id_ = g.id-->
        <!--                 LEFT JOIN app_labor_company_project lcp ON g.company_project_id_ = lcp.id-->
        <!--                 left join linkapp_tenant t on u.tenant_id_ = t.id-->
        <!--        WHERE u.status_ = 1-->
        <!--          and u.delete_state_ = 1-->
        <!--        <if test="companyId != null and companyId != ''">-->
        <!--            AND lcp.company_id_ = #{companyId}-->
        <!--        </if>-->
        <!--        group by t.id order by count desc-->

        SELECT count(u.id) count, t.platform_project_name projectName
        FROM app_labor_company_project lcp
                 left join linkapp_tenant t on lcp.tenant_id_ = t.id
                 LEFT JOIN app_group g ON g.company_project_id_ = lcp.id
                 LEFT JOIN app_user_project u ON u.group_id_ = g.id and u.delete_state_ = 1 and u.status_ = 1
        <where>
            <if test="companyId != null and companyId != ''">
                AND lcp.company_id_ = #{companyId}
            </if>
        </where>
        group by t.id
        order by count desc
    </select>

    <select id="listUserCountByWork" resultType="com.easylinkin.linkappapi.lobar.dto.UserCountByWorkDTO">
        SELECT count(u.id)     count,
               u.work_type_ as name
        FROM app_user_project u
                 LEFT JOIN emp_user_base ub ON u.user_id_ = ub.id
                 LEFT JOIN app_group g ON u.group_id_ = g.id
                 LEFT JOIN app_labor_company_project lcp ON g.company_project_id_ = lcp.id
        WHERE u.status_ = 1
          and u.delete_state_ = 1
        <if test="companyId != null and companyId != ''">
            AND lcp.company_id_ = #{companyId}
        </if>
        group by u.work_type_
        order by count desc
    </select>

    <update id="updateAreaByTenantId">
        UPDATE app_user_project
        set on_area_ = #{userAreaDTO.area}, modify_time_ = #{userAreaDTO.modifyTime}
        where tenant_id_ = #{userAreaDTO.tenantId} and on_area_ in
        <foreach collection="userAreaDTO.areaList" item="id" open="(" close=")" separator="," index="i">
            #{id}
        </foreach>
    </update>
</mapper>
