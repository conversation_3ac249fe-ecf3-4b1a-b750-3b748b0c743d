<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserCertificateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserCertificate">
        <id column="id" property="id" />
        <result column="user_id_" property="userId" />
        <result column="name_" property="name" />
        <result column="no_" property="no" />
        <result column="type_" property="type" />
        <result column="level_" property="level" />
        <result column="valid_time_" property="validTime" />
        <result column="stop_time_" property="stopTime" />
        <result column="url_" property="url" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

  <select id="queryList" resultMap="BaseResultMap">
    SELECT
    *
    FROM app_user_certificate
    <where>
      <if test="userCertificate.userId != null and userCertificate.userId != ''">
        and user_id_ = #{userCertificate.userId}
      </if>
    </where>
    ORDER BY modify_time_ DESC
  </select>
  <select id="findByTenantId" resultMap="BaseResultMap">
    SELECT
      uc.*
    FROM
      app_user_certificate uc
      LEFT JOIN app_user_project up ON uc.user_id_ = up.user_id_
    WHERE
      up.status_ = 1
      AND up.tenant_id_ = #{tenantId}
  </select>

    <select id="getMyCertNum" parameterType="String" resultType="int">
        select count(*)
        from app_user_certificate auc
        where auc.user_id_ = #{userId}
    </select>
</mapper>
