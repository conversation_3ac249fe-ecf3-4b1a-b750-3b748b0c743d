<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.LaborCompanyProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.LaborCompanyProject">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="company_id_" property="companyId" />
        <result column="company_name_" property="companyName" />
        <result column="join_time_" property="joinTime" />
        <result column="leave_time_" property="leaveTime" />
        <result column="status_" property="status" />
        <result column="delete_state_" property="deleteState" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <resultMap id="BaseResultMapDTO" type="com.easylinkin.linkappapi.lobar.dto.LaborCompanyProjectDTO" extends="com.easylinkin.linkappapi.lobar.mapper.LaborCompanyMapper.BaseResultMap">
        <result column="companyProjectId" property="companyProjectId" />
        <result column="build_type_" property="buildType" />
    </resultMap>

    <select id="queryListByPage" resultMap="BaseResultMapDTO">
        SELECT
        l.*, lc.id companyProjectId, lc.status_, lc.join_time_, lc.leave_time_
        FROM  app_labor_company_project lc
        left JOIN app_labor_company l
        on l.id = lc.company_id_
        WHERE lc.tenant_id_ = #{laborCompany.tenantId}
        and lc.delete_state_ = 1
        <if test="laborCompany.name != null and laborCompany.name != ''">
            and l.name_ like  CONCAT('%',#{laborCompany.name},'%')
        </if>
        <if test="laborCompany.buildType != null and laborCompany.buildType != ''">
            and l.build_type_ = #{laborCompany.buildType}
        </if>
        <if test="laborCompany.status != null">
            and lc.status_ = #{laborCompany.status}
        </if>
        ORDER BY lc.modify_time_ DESC
    </select>

  <select id="findAll" resultMap="BaseResultMapDTO">
      SELECT
      l.*, lc.id companyProjectId
      FROM  app_labor_company_project lc
      left JOIN app_labor_company l
      on l.id = lc.company_id_
      WHERE lc.tenant_id_ = #{tenantId}
      and lc.status_ = 1
      ORDER BY lc.modify_time_ DESC
  </select>

  <select id="findCompanyByName" resultMap="BaseResultMap">
    SELECT
      cp.*
    FROM
      app_labor_company_project cp
      LEFT JOIN app_labor_company c ON cp.company_id_ = c.id
    WHERE
      c.name_ = #{company}
      and cp.delete_state_ = 1
      AND cp.tenant_id_ = #{tenantId}
  </select>

    <select id="querySubAddProjectNum" resultType="java.util.Map">
        select
            alc.id,
            count(*) as num
        from
            app_labor_company_project alcp
                left join app_labor_company alc on
                    alcp.company_id_ = alc.id
                left join linkapp_tenant lt on
                    alcp.tenant_id_ = lt.id
            where lt.id is not null
        group by alc.id
        having alc.id is not null
    </select>

    <select id="queryLaborCompanySelect" resultType="com.easylinkin.linkappapi.lobar.entity.vo.LaborCompanySelectVO">
        select c.* from app_labor_company c
            inner join app_labor_company_project cp
                       on c.id = cp.company_id_
        where cp.status_ = 1 and cp.delete_state_ = 1 and cp.tenant_id_ = #{tenantId}
    </select>
</mapper>
