<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.GroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.Group">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="company_project_id_" property="companyProjectId" />
        <result column="name_" property="name" />
        <result column="main_people_" property="mainPeople" />
        <result column="telephone_" property="telephone" />
        <result column="card_type_" property="cardType" />
        <result column="card_no_" property="cardNo" />
        <result column="join_time_" property="joinTime" />
        <result column="leave_time_" property="leaveTime" />
        <result column="status_" property="status" />
        <result column="lead_user_id_" property="leadUserId" />
        <result column="delete_state_" property="deleteState" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="service_area_" property="serviceArea" />
    </resultMap>
    <resultMap id="BaseResultDTO" type="com.easylinkin.linkappapi.lobar.dto.GroupDTO" extends="BaseResultMap">
      <result column="companyName" property="companyName" />
      <result column="leadName" property="leadName" />
      <result column="leadPhone" property="leadPhone" />
      <result column="userNum" property="userNum" />
    </resultMap>
    <resultMap id="CompanyTreeResultMap" type="com.easylinkin.linkappapi.lobar.dto.CompanyTree">
        <result column="parentId" property="parentId" />
        <result column="parentName" property="parentName" />
        <result column="id" property="id" />
        <result column="name" property="name" />
        <association property="userNum" column="{tenantId=tenant_id,groupId=id}" select="com.easylinkin.linkappapi.lobar.mapper.UserProjectMapper.countUserNumByGroupId" />
    </resultMap>

    <select id="queryListByPage" resultMap="BaseResultDTO">
        SELECT
        l.name_ companyName,
        e.name_ leadName,
        e.telephone_ leadPhone,
        g.*
        FROM app_group g
        left JOIN app_labor_company_project lc on g.company_project_id_ = lc.id
        left JOIN app_labor_company l on lc.company_id_ = l.id
        left JOIN emp_user_base e on g.lead_user_id_ = e.id
        WHERE g.tenant_id_ = #{group.tenantId}
        and g.delete_state_ = 1
        <if test="group.companyProjectId != null and group.companyProjectId != ''">
            and g.company_project_id_ = #{group.companyProjectId}
        </if>
        <if test="group.name != null and group.name != ''">
            and g.name_ like  CONCAT('%',#{group.name},'%')
        </if>
        <if test="group.leadName != null and group.leadName != ''">
          and e.name_ like  CONCAT('%',#{group.leadName},'%')
        </if>
        <if test="group.id != null and group.id != ''">
          and g.id = #{group.id}
        </if>
        <if test="group.status != null">
          and g.status_ = #{group.status}
        </if>
        ORDER BY g.modify_time_ DESC
    </select>

  <select id="getTree" resultMap="CompanyTreeResultMap">
        SELECT
        lc.id parentId,
        l.name_ parentName,
        g.id id,
        g.name_ name,
        lc.tenant_id_ tenant_id
        FROM app_labor_company_project lc
        left JOIN app_labor_company l on lc.company_id_ = l.id
        left JOIN app_group g on lc.id = g.company_project_id_ and g.tenant_id_ = #{tenantId} and g.delete_state_ = 1
        WHERE lc.tenant_id_ = #{tenantId}
        and lc.delete_state_ = 1
        <if test="groupName != null and groupName != ''">
          and g.name_ like  CONCAT('%',#{groupName},'%')
        </if>
  </select>

    <select id="selectGroupByMap" parameterType="map" resultMap="BaseResultMap">
        select ag.*
        from app_group ag,
             app_labor_company_project alcp
        where ag.company_project_id_ = alcp.id
          and ag.delete_state_ = 1
          and alcp.company_id_ = #{companyId}
          and ag.tenant_id_ = #{tenantId}
    </select>
</mapper>
