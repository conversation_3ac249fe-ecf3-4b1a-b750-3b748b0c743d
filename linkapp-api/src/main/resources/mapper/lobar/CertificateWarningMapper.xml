<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.CertificateWarningMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.CertificateWarning">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="user_id_" property="userId" />
        <result column="certificate_id_" property="certificateId" />
        <result column="warning_rule_" property="warningRule" />
        <result column="name_" property="name" />
        <result column="no_" property="no" />
        <result column="type_" property="type" />
        <result column="level_" property="level" />
        <result column="residue_day_" property="residueDay" />
        <result column="warning_time_" property="warningTime" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="status_" property="status" />
        <result column="handle_time_" property="handleTime" />
        <result column="handle_remark_" property="handleRemark" />
        <result column="operate_type" property="operateType" />
    </resultMap>

    <resultMap id="BaseResultMapDTO" extends="BaseResultMap" type="com.easylinkin.linkappapi.lobar.dto.CertificateWarningDTO">
        <result column="userName" property="userName" />
        <result column="card_" property="idCard"/>
        <result column="telephone_" property="telephone"/>
        <association property="handleUserInfo" column="handle_user_" select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
    </resultMap>

    <select id="queryList" resultMap="BaseResultMapDTO">
        SELECT cw.*,
               ub.name_ userName,
               ub.card_,
               ub.telephone_
        FROM app_certificate_warning cw
                 left JOIN emp_user_base ub
                           on cw.user_id_ = ub.id
        WHERE cw.tenant_id_ = #{warningDTO.tenantId}
        <if test="warningDTO.type != null and warningDTO.type != ''">
            and cw.type_ = #{warningDTO.type}
        </if>
        <if test="warningDTO.level != null and warningDTO.level != ''">
            and cw.level_ = #{warningDTO.level}
        </if>
        <if test="warningDTO.startTime != null">
            <![CDATA[
            and cw.warning_time_ >= #{warningDTO.startTime}
            ]]>
        </if>
        <if test="warningDTO.endTime != null">
            <![CDATA[
            and cw.warning_time_ <= #{warningDTO.endTime}
            ]]>
        </if>
        <if test="warningDTO.userName != null and warningDTO.userName != ''">
            and (ub.card_ like concat('%', #{warningDTO.userName}, '%') or
                 ub.name_ like concat('%', #{warningDTO.userName}, '%'))
        </if>
        <if test="warningDTO.status != null">
            and cw.status_ = #{warningDTO.status}
        </if>
        ORDER BY cw.modify_time_ DESC
    </select>

</mapper>
