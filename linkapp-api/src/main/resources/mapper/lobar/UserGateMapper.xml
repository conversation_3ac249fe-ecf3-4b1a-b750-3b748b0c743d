<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserGateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserGate">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="gate_id_" property="gateId" />
        <result column="time_" property="time" />
        <result column="photo_" property="photo" />
        <result column="temperature_" property="temperature" />
        <result column="health_code_" property="healthCode" />
        <result column="travel_code_" property="travelCode" />
        <result column="type_" property="type" />
        <result column="user_id_" property="userId" />
        <result column="user_name_" property="userName" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="group_id_" property="groupId" />
        <result column="company_project_id_" property="companyProjectId" />
    </resultMap>
    <resultMap id="UserGateDtoResultMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.shigongyun.dto.UserGateClockDTO">
        <result column="platform_project_name" property="platformProjectName" />

    </resultMap>
    <select id="queryListByPage" resultType="com.easylinkin.linkappapi.lobar.dto.UserGateDTO">
        SELECT
        u.id,
        eu.name_ userName,
        ug.name_ groupName,
        ug.company_project_id_ companyProjectId,
        u.temperature_ temperature,
        up.work_type_ workType,
        u.health_code_ healthCode,
        u.travel_code_ travelCode,
        u.time_ time
        FROM
        app_user_gate u
        LEFT JOIN app_user_project up ON u.user_id_ = up.user_id_
        AND u.tenant_id_ = up.tenant_id_
        LEFT JOIN emp_user_base eu ON up.user_id_ = eu.id
        LEFT JOIN app_group ug ON up.group_id_ = ug.id
        WHERE
        u.tenant_id_ = #{userGateDTO.tenantId}
        AND date_format( u.time_, "%Y-%m" ) = #{userGateDTO.mouthStr}
        <if test="userGateDTO.userName != null and userGateDTO.userName != ''">
            and eu.name_ like  CONCAT('%',#{userGateDTO.userName},'%')
        </if>
        <if test="userGateDTO.companyProjectId != null and userGateDTO.companyProjectId != ''">
            and ug.company_project_id_ = #{userGateDTO.companyProjectId}
        </if>
        <if test="userGateDTO.groupId != null and userGateDTO.groupId != ''">
            and up.group_id_ =  #{userGateDTO.groupId}
        </if>
        <if test="userGateDTO.workType != null and userGateDTO.workType != ''">
            and up.work_type_ = #{userGateDTO.workType}
        </if>
        ORDER BY u.modify_time_ DESC
    </select>

    <select id="queryMonthByPage" resultType="com.easylinkin.linkappapi.lobar.dto.UserGateDTO">
        SELECT
          up.user_id_ userId,
          eu.name_ userName
        FROM
          app_user_project up
          LEFT JOIN emp_user_base eu ON up.user_id_ = eu.id
          LEFT JOIN app_group ug ON up.group_id_ = ug.id
        WHERE
          up.tenant_id_ = #{userGateDTO.tenantId}
          and (up.status_ = 1
          or
          (up.status_ = 0 and date_format( up.leave_time_ , "%Y-%m" ) &gt;=  #{userGateDTO.mouthStr}))
        <if test="userGateDTO.userName != null and userGateDTO.userName != ''">
            and eu.name_ like  CONCAT('%',#{userGateDTO.userName},'%')
        </if>
        <if test="userGateDTO.companyProjectId != null and userGateDTO.companyProjectId != ''">
            and ug.company_project_id_ = #{userGateDTO.companyProjectId}
        </if>
        <if test="userGateDTO.groupId != null and userGateDTO.groupId != ''">
            and up.group_id_ =  #{userGateDTO.groupId}
        </if>
        <if test="userGateDTO.workType != null and userGateDTO.workType != ''">
            and up.work_type_ = #{userGateDTO.workType}
        </if>
        ORDER BY up.modify_time_ DESC
    </select>

    <select id="findAllByMonth" resultMap="BaseResultMap">
        SELECT
          u.user_id_,
          u.time_,
          u.temperature_,
          u.health_code_,
          u.travel_code_
        FROM
          app_user_gate u
        WHERE
          u.tenant_id_ = #{userGateDTO.tenantId}
          AND date_format( u.time_, "%Y-%m" ) = #{userGateDTO.mouthStr}
          <if test="userGateDTO.ids != null and userGateDTO.ids.size() > 0">
            and user_id_ in
            <foreach collection="userGateDTO.ids" index="index" item="id" open="(" close=")" separator=",">
              #{id}
            </foreach>
          </if>
      ORDER BY u.modify_time_ DESC
    </select>

  <select id="findCompanyToDay"
    resultType="com.easylinkin.linkappapi.lobar.dto.app.AppUserGateDTO">
        SELECT
          b.name_ companyName,
          a.id companyProjectId,
          count( DISTINCT d.id ) sum,
          m.temperature
        FROM
          app_labor_company_project a
          LEFT JOIN app_labor_company b ON a.company_id_ = b.id
          LEFT JOIN app_group c ON a.id = c.company_project_id_
          AND c.status_ = 1
          LEFT JOIN app_user_project d ON c.id = d.group_id_
          AND d.status_ = 1
          LEFT JOIN (select e.company_project_id_,e.tenant_id_, count( DISTINCT e.id ) temperature from app_user_gate e where  e.time_
          BETWEEN #{startTime}
          AND #{endTime}  group by e.company_project_id_,e.tenant_id_) m
          ON a.id = m.company_project_id_
        WHERE
          a.tenant_id_ = #{tenantId}
          AND a.status_ = 1
        GROUP BY
          a.id
        ORDER BY
          a.modify_time_
  </select>
    <select id="findCompanyTime"
      resultType="com.easylinkin.linkappapi.lobar.dto.app.AppUserGateDTO">
        SELECT
          c.name_ companyName,
          a.company_project_id_ companyProjectId,
          sum( a.sum_ ) sum,
          m.temperature
        FROM
          app_user_statistics a
          LEFT JOIN app_labor_company_project b ON a.company_project_id_ = b.id
          LEFT JOIN app_labor_company c ON b.company_id_ = c.id
          LEFT JOIN (select e.company_project_id_,e.tenant_id_, count( DISTINCT e.id ) temperature from app_user_gate e where  e.time_
          BETWEEN #{startTime}
          AND #{endTime}  group by e.company_project_id_,e.tenant_id_) m
          ON a.id = m.company_project_id_
        WHERE
          a.tenant_id_ = #{tenantId}
          AND a.record_time_ BETWEEN #{startTime}
          AND #{endTime}
        GROUP BY
          a.company_project_id_
        ORDER BY
          b.modify_time_
    </select>
    <select id="findGroupToDay"
      resultType="com.easylinkin.linkappapi.lobar.dto.app.AppUserGateDTO">
        SELECT
          a.name_ groupName,
          a.id groupId,
          count( DISTINCT b.id ) sum,
          count( DISTINCT e.id ) temperature
        FROM
          app_group a
          LEFT JOIN app_user_project b ON a.id = b.group_id_
          AND b.status_ = 1
          LEFT JOIN app_user_gate e ON a.id = e.group_id_
          AND e.time_ BETWEEN #{startTime}
          AND #{endTime}
        WHERE
          a.tenant_id_ = #{tenantId}
          AND a.status_ = 1
          and a.company_project_id_ = #{companyProjectId}
        GROUP BY
          a.id
        ORDER BY
          a.modify_time_
    </select>

    <select id="findGroupTime"
      resultType="com.easylinkin.linkappapi.lobar.dto.app.AppUserGateDTO">
        SELECT
          b.name_ groupName,
          a.group_id_ groupId,
          sum( a.sum_ ) sum,
          count( DISTINCT d.id ) temperature
        FROM
          app_user_statistics a
          LEFT JOIN app_group b ON a.group_id_ = b.id
          LEFT JOIN app_user_gate d ON a.group_id_ = d.group_id_
          AND d.time_ BETWEEN #{startTime}
          AND #{endTime}
        WHERE
          a.tenant_id_ = #{tenantId}
          AND a.company_project_id_ = #{companyProjectId}
          AND a.record_time_ BETWEEN #{startTime}
          AND #{endTime}
        GROUP BY
          a.group_id_
        ORDER BY
          b.modify_time_
    </select>

    <select id="findUser" resultType="com.easylinkin.linkappapi.lobar.dto.UserGateDTO">
      SELECT
        b.photo_ photo,
        b.name_ userName,
        e.name_ companyName,
        c.name_ groupName,
        a.time_ time,
        a.temperature_ temperature,
        a.health_code_ healthCode
      FROM
        app_user_gate a
        LEFT JOIN emp_user_base b ON a.user_id_ = b.id
        LEFT JOIN app_group c ON a.group_id_ = c.id
        LEFT JOIN app_labor_company_project d ON a.company_project_id_ = d.id
        LEFT JOIN app_labor_company e ON d.company_id_ = e.id
      WHERE
        a.tenant_id_ = #{tenantId}
        <if test="groupId != null and groupId != ''">
          AND a.group_id_ = #{groupId}
        </if>
        <if test="companyProjectId != null and companyProjectId != ''">
          AND a.company_project_id_ = #{companyProjectId}
        </if>
        AND a.time_ BETWEEN #{startTime}
        AND #{endTime}
    </select>

    <select id="dockGetAll" resultType="com.easylinkin.linkappapi.lobar.dto.UserGateDTO">
        SELECT
        b.photo_ photo,
        b.name_ userName,
        e.name_ companyName,
        c.name_ groupName,
        a.time_ time,
        a.temperature_ temperature,
        a.health_code_ healthCode
        FROM
        app_user_gate a
        LEFT JOIN emp_user_base b ON a.user_id_ = b.id
        LEFT JOIN app_group c ON a.group_id_ = c.id
        LEFT JOIN app_labor_company_project d ON a.company_project_id_ = d.id
        LEFT JOIN app_labor_company e ON d.company_id_ = e.id
        WHERE a.tenant_id_ = #{tenantId}
    </select>

    <select id="countNumGroupByHealthCode" parameterType="com.easylinkin.linkappapi.shigongyun.vo.UserClockGateVo"
            resultType="java.util.Map">
        select aug.health_code_ as healthCode,
               count(*)         as num
        from app_user_gate aug,
             linkapp_tenant lt
        where aug.tenant_id_ = lt.id
          and lt.status = 1
        <if test="userGate != null ">
            <if test="userGate.type != null">
                and aug.type_ = #{userGate.type}
            </if>
        </if>
        <if test="startTime != null">
            <![CDATA[
            and aug.time_ >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and aug.time_ <= #{endTime}
            ]]>
        </if>
        and aug.health_code_ is not null
        group by aug.health_code_
    </select>

    <select id="queryGateClockDtoList" parameterType="com.easylinkin.linkappapi.shigongyun.vo.UserClockGateVo"
            resultMap="UserGateDtoResultMap">
        select aug.*,
               lt.platform_project_name
        from app_user_gate aug,
             linkapp_tenant lt
        where lt.status = 1
        <if test="userGate != null ">
            <if test="userGate.type != null">
                and aug.type_ = #{userGate.type}
            </if>
        </if>
        and aug.tenant_id_ = lt.id
        <if test="tenantId != null and tenantId != ''">
            and aug.tenant_id_ = #{tenantId}
        </if>
        <if test="startTime != null">
            <![CDATA[
            and aug.time_ >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and aug.time_ <= #{endTime}
            ]]>
        </if>
        order by
            aug.time_ desc
    </select>
</mapper>
