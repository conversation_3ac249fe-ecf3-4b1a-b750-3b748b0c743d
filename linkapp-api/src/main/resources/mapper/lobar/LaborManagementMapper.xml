<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.LaborManagementMapper">

    <select id="getPeopleInfo" resultType="com.easylinkin.linkappapi.lobar.entity.vo.PeopleInfoVo">
        SELECT u.user_name_ userName, /* 姓名 */
        u.gender_ gender, /* 性别 */
        u.birthday_ birthday, /* 生日 */
        al.name_ companyName, /* 参建单位 */
        g.name_ groupName, /* 班组 */
        u.work_type_ workType, /* 工种 */
        ub.card_ cardNo, /* 身份证号 */
        ub.telephone_ telephone, /* 联系电话 */
        ur.record_time_ recordTime, /* 最近考勤记录 */
        u.status_ peopleStatus, /* 状态: 在岗/离岗 */
        u.edu_state_ eduState /* 安全教育 0未进行，1已进行 */
        FROM app_user_project u
        LEFT JOIN emp_user_base ub ON u.user_id_ = ub.id
        LEFT JOIN app_group g ON u.group_id_ = g.id
        LEFT JOIN app_labor_company_project ap ON g.company_project_id_ = ap.id
        LEFT JOIN app_labor_company al ON ap.company_id_ = al.id
        LEFT JOIN app_user_record ur ON u.tenant_id_ = ur.tenant_id_
        AND u.user_id_ = ur.user_id_
        AND ur.record_type_ != 0
        WHERE u.delete_state_ = 1
        <if test="tenantId != null and tenantId != ''">
            and u.tenant_id_ = #{tenantId}
        </if>
        <if test="cardNo != null and cardNo != ''">
            and ub.card_ = #{cardNo}
        </if>
        ORDER BY ur.record_time_ DESC
        LIMIT 1
    </select>

    <select id="getPeopleList" resultType="com.easylinkin.linkappapi.lobar.entity.vo.PeopleInfoVo">
        SELECT u.user_name_ userName, /* 姓名 */
        u.gender_ gender, /* 性别 */
        u.birthday_ birthday, /* 生日 */
        al.name_ companyName, /* 参建单位 */
        g.name_ groupName, /* 班组 */
        u.work_type_ workType, /* 工种 */
        ub.card_ cardNo, /* 身份证号 */
        ub.telephone_ telephone, /* 联系电话 */
        u.status_ peopleStatus, /* 状态: 在岗/离岗 */
        u.edu_state_ eduState /* 安全教育 0未进行，1已进行 */
        FROM app_user_project u
        LEFT JOIN emp_user_base ub ON u.user_id_ = ub.id
        LEFT JOIN app_group g ON u.group_id_ = g.id
        LEFT JOIN app_labor_company_project ap ON g.company_project_id_ = ap.id
        LEFT JOIN app_labor_company al ON ap.company_id_ = al.id
        WHERE u.delete_state_ = 1
        <if test="tenantId != null and tenantId != ''">
            and u.tenant_id_ = #{tenantId}
        </if>
        <if test="userInfo != null and userInfo != ''">
            and (ub.card_ = #{userInfo} or u.user_name_ like concat('%', #{userInfo}, '%'))
        </if>
        ORDER BY u.user_name_
    </select>

    <select id="getCheckRecordList" resultType="com.easylinkin.linkappapi.lobar.entity.vo.CheckRecordVo">
        select id,
        people_name_ peopleName,
        people_status_ peopleStatus,
        card_ cardNo,
        safe_edu_ safeEdu,
        age_ age,
        attendance_ attendance,
        check_time_ checkTime,
        record_people_ recordPeople,
        status_ status,
        people_company_ peopleCompany,
        check_in_ checkIn,
        deal_require_ dealRequire,
        deal_name_ dealName,
        deal_time_ dealTime,
        update_control_id_ updateControlId
        from app_people_check
        where 1=1
        <if test="query.tenantId != null and query.tenantId != ''">
            and tenant_id_ = #{query.tenantId}
        </if>

        <if test="query.peopleName != null and query.peopleName != ''">
            and people_name_ LIKE CONCAT('%', #{query.peopleName}, '%')
        </if>

        <if test="query.startDate != null and query.startDate != ''">
            and check_time_ &gt;= #{query.startDate}
        </if>

        <if test="query.endDate != null and query.endDate != ''">
            and check_time_ &lt;= #{query.endDate}
        </if>

        <if test="query.status != null and query.status != ''">
            and status_ = #{query.status}
        </if>

        <if test="query.recordPeople != null and query.recordPeople != ''">
            and record_people_ LIKE CONCAT('%', #{query.recordPeople}, '%')
        </if>

        <if test="query.dealRequire != null and query.dealRequire != ''">
            and deal_require_ = #{query.dealRequire}
        </if>
        order by check_time_ desc
    </select>

    <select id="getCount" resultType="com.easylinkin.linkappapi.lobar.entity.vo.CheckCountVo">
        select count(*) total,
        count(case when status_ = '0' then 1 else null end) unPro,
        count(case when status_ = '1' then 1 else null end) pro
        from app_people_check
        where 1=1
        <if test="query.tenantId != null and query.tenantId != ''">
            and tenant_id_ = #{query.tenantId}
        </if>

        <if test="query.peopleName != null and query.peopleName != ''">
            and people_name_ LIKE CONCAT('%', #{query.peopleName}, '%')
        </if>

        <if test="query.startDate != null and query.startDate != ''">
            and check_time_ &gt;= #{query.startDate}
        </if>

        <if test="query.endDate != null and query.endDate != ''">
            and check_time_ &lt; DATE_ADD(#{query.endDate}, INTERVAL 1 day)
        </if>

        <if test="query.status != null and query.status != ''">
            and status_ = #{query.status}
        </if>

        <if test="query.recordPeople != null and query.recordPeople != ''">
            and record_people_ LIKE CONCAT('%', #{query.recordPeople}, '%')
        </if>

        <if test="query.dealRequire != null and query.dealRequire != ''">
            and deal_require_ = #{query.dealRequire}
        </if>
    </select>

</mapper>
