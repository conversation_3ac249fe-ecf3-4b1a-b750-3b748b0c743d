<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.EmpUserBaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.emp.EmpUserBase">
        <id column="id" property="id" />
        <result column="emp_uid_" property="empUid" />
        <result column="name_" property="name" />
        <result column="gender_" property="gender" />
        <result column="telephone_" property="telephone" />
        <result column="education_" property="education" />
        <result column="nation_" property="nation" />
        <result column="degree_" property="degree" />
        <result column="birthday_" property="birthday" />
        <result column="card_type_" property="cardType" />
        <result column="card_" property="card" />
        <result column="authority_" property="authority" />
        <result column="address_" property="address" />
        <result column="card_start_" property="cardStart" />
        <result column="card_end_" property="cardEnd" />
        <result column="photo_" property="photo" />
        <result column="card_a_" property="cardA" />
        <result column="card_b_" property="cardB" />
        <result column="employ_time_" property="employTime" />
        <result column="fire_time_" property="fireTime" />
        <result column="employ_status_" property="employStatus" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="real_sys_" property="realSys" />
        <result column="groupName" property="groupName" />
        <result column="companyName" property="companyName" />
        <result column="company_id_" property="companyId" />
        <result column="work_type_" property="workType" />
    </resultMap>

    <select id="getBaseUsers" resultMap="BaseResultMap">
        SELECT
        eu.id,
        eu.photo_,
        eu.name_,
        eu.card_,
        eu.birthday_,
        eu.gender_,
        eu.telephone_,
        eu.work_type_,
        al.name_ companyName
        FROM emp_user_base eu
        left JOIN app_labor_company al
        on eu.company_id_ = al.id
        WHERE eu.id not in
        (SELECT au.user_id_ FROM app_user_project au WHERE au.delete_state_ = 1 and au.tenant_id_ = #{userProject.tenantId})
        <if test="userProject.userName != null and userProject.userName != ''">
            and (eu.name_ like  CONCAT('%',#{userProject.userName},'%')
            or eu.card_ like  CONCAT('%',#{userProject.userName},'%')
            or eu.telephone_ like  CONCAT('%',#{userProject.userName},'%'))
        </if>
        <if test="userProject.workType != null and userProject.workType != ''">
            AND eu.work_type_ = #{userProject.workType}
        </if>
        <if test="userProject.companyId != null and userProject.companyId != ''">
            AND eu.company_id_ = #{userProject.companyId}
        </if>
        ORDER BY eu.modify_time_ DESC
    </select>

    <select id="queryByGroupId" resultMap="BaseResultMap">
        SELECT
          b.*
        FROM
          app_user_project a
          LEFT JOIN emp_user_base b ON a.user_id_ = b.id
        WHERE
          a.group_id_ = #{userProject.groupId}
        ORDER BY
          a.modify_time_ DESC
    </select>

    <select id="queryByGate" resultMap="BaseResultMap">
        SELECT
            c.*,
            a.name_ groupName,
            e.name_ companyName
        FROM
            app_group a
        LEFT JOIN app_user_project b ON a.id = b.group_id_
        AND b.status_ = 1 and b.delete_state_ =1 AND b.real_sys_ = 0  and a.tenant_id_ = b.tenant_id_
        LEFT JOIN emp_user_base c ON b.user_id_ = c.id
        LEFT JOIN app_labor_company_project d ON a.company_project_id_ = d.id
        LEFT JOIN app_labor_company e ON e.id = d.company_id_
        WHERE
          a.tenant_id_ = #{tenantId}
        <if test="serviceArea != null and serviceArea != ''">
            and a.service_area_ like  CONCAT('%',#{serviceArea},'%')
        </if>
        <if test="ids !=null and ids.size>0">
            and c.id not in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

  <select id="queryListByPage" resultMap="BaseResultMap">
    SELECT
      a.*,
      b.name_ companyName
    FROM
      emp_user_base a
      LEFT JOIN app_labor_company b ON a.company_id_ = b.id
    <where>
        <if test="user.name != null and user.name != ''">
            and (a.name_ like  CONCAT('%',#{user.name},'%')
            or a.card_ like  CONCAT('%',#{user.name},'%')
            or a.telephone_ like  CONCAT('%',#{user.name},'%'))
        </if>
        <if test="user.workType != null and user.workType != ''">
            AND a.work_type_ = #{user.workType}
        </if>
        <if test="user.companyId != null and user.companyId != ''">
            AND a.company_id_ = #{user.companyId}
        </if>
    </where>
    order by a.modify_time_ DESC
  </select>

    <select id="getExistByQuery" resultType="com.easylinkin.linkappapi.lobar.entity.vo.EmpUserBaseVO">
        SELECT * FROM emp_user_base
        <where>
            <choose>
                <when test="(query.telephone == null or query.telephone == '') and (query.card == null or query.card == '')">
                    1 = 2
                </when>
                <otherwise>
                    <if test="query.telephone != null and query.telephone != ''">
                        AND telephone_ = #{query.telephone}
                    </if>
                    <if test="query.card != null and query.card != ''">
                        AND card_ = #{query.card}
                    </if>
                </otherwise>
            </choose>
        </where>
        LIMIT 1
    </select>

</mapper>
