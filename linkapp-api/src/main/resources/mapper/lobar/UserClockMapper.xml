<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserClockMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserClock">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="clock_time" property="clockTime" />
        <result column="direction_" property="direction" />
        <result column="type_" property="type" />
        <result column="photo_" property="photo" />
        <result column="user_id_" property="userId" />
        <result column="user_name_" property="userName" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="group_id_" property="groupId" />
        <result column="company_project_id_" property="companyProjectId" />
        <result column="gate_code" property="gateCode"/>
        <result column="service_area" property="serviceArea"/>
    </resultMap>
    <resultMap id="UserClockDtoMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">

    </resultMap>

    <select id="queryListByPage" resultType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">
        SELECT
        uc.id,
        ub.name_ userName,
        ub.telephone_ telephone,
        ub.photo_ photo,
        g.name_ groupName,
        uc.clock_time clockTime
        FROM
        app_user_clock uc
        left JOIN app_user_project up ON uc.user_id_ = up.user_id_ and uc.tenant_id_ = up.tenant_id_
        left JOIN emp_user_base ub on up.user_id_ = ub.id
        left JOIN app_group g on up.group_id_ = g.id
        WHERE uc.tenant_id_ = #{userClockDTO.tenantId}
            and ( uc.is_record_ != 0 or uc.is_record_ is null)
        <if test="userClockDTO.userName != null and userClockDTO.userName != ''">
            and ub.name_ like  CONCAT('%',#{userClockDTO.userName},'%')
        </if>
        <if test="userClockDTO.startTime != null">
            and uc.clock_time &gt;=  #{userClockDTO.startTime}
        </if>
        <if test="userClockDTO.endTime != null">
            and uc.clock_time &lt;=  #{userClockDTO.endTime}
        </if>
        ORDER BY uc.modify_time_ DESC
    </select>

  <select id="queryRecordByPage" resultType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">
        SELECT
        r.id,
        r.record_time_ recordTime,
        ub.name_ userName,
        ub.telephone_ telephone,
        ub.photo_ photo,
        g.name_ groupName,
        up.work_type_ workType,
        r.hour_ as hours
        FROM app_user_record r
        left JOIN app_user_project up on r.user_id_ = up.user_id_ and r.tenant_id_ = up.tenant_id_
        left JOIN emp_user_base ub on up.user_id_ = ub.id
        left JOIN app_group g on up.group_id_ = g.id
        WHERE
        r.tenant_id_ = #{userClockDTO.tenantId}
--         暂时加上这个条件
        and r.hour_ &gt; 0
        <if test="userClockDTO.userName != null and userClockDTO.userName != ''">
            and ub.name_ like  CONCAT('%',#{userClockDTO.userName},'%')
        </if>
          <if test="userClockDTO.groupId != null and userClockDTO.groupId != ''">
              and up.group_id_ =  #{userClockDTO.groupId}
          </if>
      <if test="userClockDTO.workType != null and userClockDTO.workType != ''">
          and up.work_type_ = #{userClockDTO.workType}
      </if>
        ORDER BY r.modify_time_ DESC
  </select>

  <select id="queryList" resultType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">
    SELECT
      up.user_id_ userId,
      eu.name_ userName,
      g.name_ groupName,
      g.company_project_id_ companyProjectId,
      up.work_type_ workType,
      count( ur.record_time_ ) onNum,
      up.clock_time_ clockTime
    FROM
      app_user_project up
      LEFT JOIN emp_user_base eu ON up.user_id_ = eu.id
      LEFT JOIN app_group g ON up.group_id_ = g.id
      LEFT JOIN app_user_record ur ON up.tenant_id_ = ur.tenant_id_
      AND up.user_id_ = ur.user_id_
      AND date_format( ur.record_time_, "%Y-%m" ) = #{userClockDTO.mouthStr}
      AND ur.record_type_ != 0
    WHERE
      up.tenant_id_ = #{userClockDTO.tenantId}
      and (up.status_ = 1
      or
      (up.status_ = 0 and up.delete_state_ = 1 and date_format( up.leave_time_ , "%Y-%m" ) &gt;=  #{userClockDTO.mouthStr}))
      <if test="userClockDTO.userName != null and userClockDTO.userName != ''">
        and eu.name_ like  CONCAT('%',#{userClockDTO.userName},'%')
      </if>
      <if test="userClockDTO.companyProjectId != null and userClockDTO.companyProjectId != ''">
        and g.company_project_id_ = #{userClockDTO.companyProjectId}
      </if>
      <if test="userClockDTO.groupId != null and userClockDTO.groupId != ''">
        and up.group_id_ =  #{userClockDTO.groupId}
      </if>
      <if test="userClockDTO.workType != null and userClockDTO.workType != ''">
        and up.work_type_ = #{userClockDTO.workType}
      </if>
    GROUP BY
      up.user_id_
    ORDER BY up.clock_time_ DESC
  </select>

  <select id="findOnUser" resultMap="BaseResultMap">
    SELECT
        uc.*
    FROM
        app_user_clock uc
    LEFT JOIN app_user_project up ON uc.tenant_id_ = up.tenant_id_
        AND uc.user_id_ = up.user_id_
    WHERE
        uc.tenant_id_ = #{group.tenantId}
        AND ( uc.is_record_ != 0 or uc.is_record_ is null)
        AND up.group_id_ = #{group.id}
        AND up.status_ = 1
        AND uc.direction_ = 1
        AND uc.clock_time &lt;=  #{endTime}
        AND uc.clock_time &gt;=  #{startTime}
  </select>

<!--  <select id="queryMonth" resultType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">-->
<!--    SELECT-->
<!--      up.user_id_ userId,-->
<!--      up.user_name_ userName,-->
<!--      count( ur.record_time_ ) onNum-->
<!--    FROM-->
<!--      app_user_project up-->
<!--      LEFT JOIN app_group g ON up.group_id_ = g.id-->
<!--      LEFT JOIN app_user_record ur ON up.tenant_id_ = ur.tenant_id_-->
<!--      AND up.user_id_ = ur.user_id_-->
<!--      AND date_format(ur.record_time_, "%Y-%m" ) = #{userClockDTO.mouthStr}-->
<!--      AND ur.record_type_ != 0-->
<!--    WHERE-->
<!--      up.tenant_id_ = #{userClockDTO.tenantId}-->
<!--      and (up.status_ = 1-->
<!--      or-->
<!--      (up.status_ = 0 and up.delete_state_ = 1 and date_format( up.leave_time_ , "%Y-%m" ) &gt;=  #{userClockDTO.mouthStr}))-->
<!--      <if test="userClockDTO.userName != null and userClockDTO.userName != ''">-->
<!--        and up.user_name_ like  CONCAT('%',#{userClockDTO.userName},'%')-->
<!--      </if>-->
<!--      <if test="userClockDTO.companyProjectId != null and userClockDTO.companyProjectId != ''">-->
<!--        and g.company_project_id_ = #{userClockDTO.companyProjectId}-->
<!--      </if>-->
<!--      <if test="userClockDTO.groupId != null and userClockDTO.groupId != ''">-->
<!--        and up.group_id_ =  #{userClockDTO.groupId}-->
<!--      </if>-->
<!--      <if test="userClockDTO.workType != null and userClockDTO.workType != ''">-->
<!--        and up.work_type_ = #{userClockDTO.workType}-->
<!--      </if>-->
<!--    GROUP BY-->
<!--      up.user_id_-->
<!--  </select>-->

<!--  2022 12 07 版本-->
  <select id="queryMonth" resultType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">
    SELECT
    up.user_id_ userId,
    eu.name_ userName,
    g.name_ groupName,
    ac.name_ companyName
    FROM
    app_user_project up
    LEFT JOIN emp_user_base eu ON up.user_id_ = eu.id
    LEFT JOIN app_group g ON up.group_id_ = g.id
    LEFT JOIN app_labor_company_project ap on g.company_project_id_ = ap.id
    LEFT JOIN app_labor_company ac on ap.company_id_ = ac.id
    WHERE
    up.tenant_id_ = #{userClockDTO.tenantId}
    and (up.status_ = 1
    or
    (up.status_ = 0 and up.delete_state_ = 1 and date_format( up.leave_time_ , "%Y-%m" ) &gt;=  #{userClockDTO.mouthStr}))
    <if test="userClockDTO.userName != null and userClockDTO.userName != ''">
      and eu.name_ like  CONCAT('%',#{userClockDTO.userName},'%')
    </if>
    <if test="userClockDTO.companyProjectId != null and userClockDTO.companyProjectId != ''">
      and g.company_project_id_ = #{userClockDTO.companyProjectId}
    </if>
    <if test="userClockDTO.groupId != null and userClockDTO.groupId != ''">
      and up.group_id_ =  #{userClockDTO.groupId}
    </if>
    <if test="userClockDTO.workType != null and userClockDTO.workType != ''">
      and up.work_type_ = #{userClockDTO.workType}
    </if>
    GROUP BY
    up.user_id_
  </select>

  <select id="findAllMonth" resultMap="BaseResultMap">
    SELECT
      id,
      user_id_,
      direction_,
      clock_time
    FROM
      app_user_clock
    WHERE
      tenant_id_ = #{userClockDTO.tenantId}
      AND (is_record_ != 0 or is_record_ is null)
      AND date_format(clock_time, "%Y-%m" ) = #{userClockDTO.mouthStr}
      <if test="userClockDTO.ids != null and userClockDTO.ids.size() > 0">
        and user_id_ in
        <foreach collection="userClockDTO.ids" index="index" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>
      </if>
      ORDER BY clock_time
  </select>

  <select id="findAllMonthForExport" resultMap="BaseResultMap">
    SELECT
    user_id_,
    clock_time
    FROM
    app_user_clock
    WHERE
    tenant_id_ = #{userClockDTO.tenantId}
    AND (is_record_ != 0 or is_record_ is null)
    AND date_format(clock_time, "%Y-%m" ) = #{userClockDTO.mouthStr}
      <if test="userClockDTO.ids != null and userClockDTO.ids.size() > 0">
          and user_id_ in
          <foreach collection="userClockDTO.ids" index="index" item="id" open="(" close=")" separator=",">
              #{id}
          </foreach>
      </if>
  </select>

  <select id="userByTime" resultType="com.easylinkin.linkappapi.lobar.dto.screen.LobarScreenDTO">
    SELECT
      count(DISTINCT a.user_id_) AS num,
      DATE_FORMAT( a.clock_time, "%Y-%m-%d %H:00:00" ) AS cTime
    FROM
      app_user_clock a
    WHERE
       a.tenant_id_ = #{userClockDTO.tenantId}
       AND (a.is_record_ != 0 or a.is_record_ is null)
       AND a.direction_ = 1
       AND a.clock_time BETWEEN #{userClockDTO.startTime}
      AND #{userClockDTO.endTime}
     <if test="userClockDTO.userIds != null and userClockDTO.userIds.size() > 0">
      and a.user_id_ in
      <foreach collection="userClockDTO.userIds" index="index" item="userId" open="(" close=")" separator=",">
        #{userId}
      </foreach>
    </if>
    GROUP BY
      DATE_FORMAT( a.clock_time, "%Y-%m-%d %H:00:00" )
  </select>

    <select id="findUserByTime" resultMap="BaseResultMap">
        SELECT tem.* from
        (
        SELECT
        a.user_id_,
        a.clock_time
        FROM
        app_user_clock a
        WHERE
        a.tenant_id_ = #{userClockDTO.tenantId}
        AND (a.is_record_ != 0 or a.is_record_ is null)
        AND a.direction_ = 1
        AND a.clock_time &gt;= #{userClockDTO.startTime}
        AND a.clock_time &lt;= #{userClockDTO.endTime} ) tem
        <where>
            <if test="userClockDTO.userIds != null and userClockDTO.userIds.size() > 0">
                and tem.user_id_ in
                <foreach collection="userClockDTO.userIds" index="index" item="userId" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
        </where>
    </select>

  <select id="findLast" resultType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">
    SELECT
      a.direction_ direction,
      b.photo_ photo,
      a.clock_time clockTime,
      b.name_ userName,
      b.gender_ gender
    FROM
      app_user_clock a
      LEFT JOIN emp_user_base b ON a.user_id_ = b.id
    WHERE
      a.tenant_id_ = #{tenantId}
      AND (a.is_record_ != 0 or a.is_record_ is null)
    ORDER BY
      clock_time DESC
      LIMIT #{size}
  </select>

    <select id="countNumGroupByInOut" parameterType="com.easylinkin.linkappapi.shigongyun.vo.UserClockGateVo"
            resultType="java.util.Map">
        select auc.direction_ as inOutType,
               count(*)       as num
        from app_user_clock auc,
             linkapp_tenant lt
        where auc.tenant_id_ = lt.id
        and lt.status = 1
        AND (auc.is_record_ != 0 or auc.is_record_ is null)
        <if test="userClock.type != null">
            and auc.type_ = #{userClock.type}
        </if>
        <if test="startTime != null">
            <![CDATA[
            and auc.clock_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and auc.clock_time <= #{endTime}
            ]]>
        </if>
        and auc.direction_ is not null
        group by auc.direction_
    </select>

    <select id="countNumGroupByUserAndDire" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
            resultType="java.util.Map">
        select auc.user_id_   as userId,
               auc.direction_ as direction,
               auc.clock_time as clockTime,
               count(*)       as num
        from app_user_clock auc
        where auc.type_ = 2
        AND (auc.is_record_ != 0 or auc.is_record_ is null)
        <if test="tenantId != null and tenantId != ''">
            and auc.tenant_id_ = #{tenantId}
        </if>
        <if test="startTime != null">
            <![CDATA[
            and auc.clock_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and auc.clock_time <= #{endTime}
            ]]>
        </if>
        group by auc.user_id_, auc.direction_
    </select>

  <select id="getTenClock" resultType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">
    SELECT
      a.id,
      a.direction_ direction,
      a.photo_ photo,
      b.name_ userName,
      a.clock_time clockTime,
      c.name_ groupName,
      e.name_ companyName,
      f.on_area_ onArea,
      f.id userProjectId
    FROM
      app_user_clock a
      LEFT JOIN emp_user_base b ON a.user_id_ = b.id
      LEFT JOIN app_group c ON a.group_id_ = c.id
      LEFT JOIN app_labor_company_project d ON a.company_project_id_ = d.id
      LEFT JOIN app_labor_company e ON d.company_id_ = e.id
      LEFT JOIN app_user_project f ON a.user_id_ = f.user_id_ and a.tenant_id_ = f.tenant_id_
      and f.delete_state_ = 1
    WHERE
      a.tenant_id_ = #{tenantId}
      AND (a.is_record_ != 0 or a.is_record_ is null)
    ORDER BY
      a.clock_time DESC
      LIMIT #{num}
  </select>

  <select id="getByToday"
    resultType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">
    SELECT
      *
    FROM
      (
    SELECT
      a.company_project_id_ companyProjectId,
      a.group_id_ groupId,
      a.user_id_ userId,
      a.direction_ direction,
      a.clock_time clockTime,
      b.work_type_ workType
    FROM
      app_user_clock a
      LEFT JOIN app_user_project b ON a.tenant_id_ = b.tenant_id_
      AND a.user_id_ = b.user_id_
    WHERE
      TO_DAYS( a.clock_time ) = TO_DAYS( NOW( ) )
      AND (a.is_record_ != 0 or a.is_record_ is null)
      AND a.tenant_id_ = #{tenantId}
    ORDER BY
      a.clock_time DESC
      ) a
    GROUP BY
      a.userId
  </select>
  <select id="findNewAttention" resultType="com.easylinkin.linkappapi.lobar.dto.UserProjectDTO">
      SELECT a.user_name_                          userName,
             a.clock_time                          joinTime,
             a.photo_                              photo,
             (select work_type_
              from app_user_project aup
              where a.user_id_ = aup.user_id_
                AND a.tenant_id_ = aup.tenant_id_) workType
      FROM app_user_clock a
      WHERE a.direction_ = 1
        AND (a.is_record_ != 0 or a.is_record_ is null)
        AND a.tenant_id_ = #{tenantId}
      ORDER BY a.clock_time DESC
      LIMIT #{size}
  </select>

    <select id="queryLastUserClockList" parameterType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO"
            resultMap="UserClockDtoMap">
        select auc2.*
        from app_user_clock auc2,
        (select auc.user_id_        as userId,
                auc.direction_      as direction,
                max(auc.clock_time) as clockTime
         from app_user_clock auc
        where auc.tenant_id_ = #{tenantId}
        AND (auc.is_record_ != 0 or auc.is_record_ is null)
        <if test="serviceArea != null">
            and auc.service_area = #{serviceArea}
        </if>
        <if test="direction != null">
            and auc.direction_ = #{direction}
        </if>
        <if test="startTime != null">
            <![CDATA[
            and auc.clock_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and auc.clock_time <= #{endTime}
            ]]>
        </if>
        and auc.direction_ is not null
        group by auc.user_id_,
            auc.direction_
        ) g
        where auc2.user_id_ = g.userId
          and auc2.direction_ = g.direction
          and clock_time = g.clockTime
          AND (auc2.is_record_ != 0 or auc2.is_record_ is null)
    </select>
    <select id="getOutinWarnClockIn" resultMap="BaseResultMap">
        select
        auc.*
        from
        app_user_clock auc ,
        app_user_outin_warning auow
        where
        auc.id = auow.in_clock_id
        and auow.id = #{id}
        AND (auc.is_record_ != 0 or auc.is_record_ is null)
    </select>

  <select id="getByDeviceCode"
    resultType="com.easylinkin.linkappapi.lobar.dto.UserClockDTO">
    SELECT
      c.name_ userName,
      a.clock_time clockTime,
      a.photo_ photo,
      b.work_type_ workType
    FROM
      app_user_clock a
      LEFT JOIN app_user_project b ON a.user_id_ = b.user_id_
      AND a.tenant_id_ = b.tenant_id_
      LEFT JOIN emp_user_base c ON a.user_id_ = c.id
    WHERE
      to_days(a.clock_time) = to_days(now())
      AND (a.is_record_ != 0 or a.is_record_ is null)
      AND a.gate_code = #{deviceCode}
      AND a.tenant_id_ = #{tenantId}
    ORDER BY
      a.clock_time DESC
      LIMIT #{size}
  </select>

    <select id="dockGetListByTenantId" resultType="com.easylinkin.linkappapi.lobar.entity.UserClock">
        SELECT
            a.*
        FROM
            app_user_clock a
                LEFT JOIN emp_user_base b ON a.user_id_ = b.id
                LEFT JOIN app_group c ON a.group_id_ = c.id
                LEFT JOIN app_labor_company_project d ON a.company_project_id_ = d.id
                LEFT JOIN app_labor_company e ON d.company_id_ = e.id
                LEFT JOIN app_user_project f ON a.user_id_ = f.user_id_ and a.tenant_id_ = f.tenant_id_
                and f.delete_state_ = 1
        WHERE
            a.tenant_id_ = #{tenantId}
          AND (a.is_record_ != 0 or a.is_record_ is null)
          AND a.clock_time >= '2023-10-23 00:00:00'
    </select>

    <select id="queryClockTime" resultType="java.lang.String">
        SELECT
            max(uc.clock_time) clockTime
        FROM
            app_user_clock uc
        WHERE
            uc.tenant_id_ = #{clockDto.tenantId}
            AND uc.user_id_ = #{clockDto.userId}
            AND (uc.is_record_ != 0 or uc.is_record_ is null)
    </select>
</mapper>
