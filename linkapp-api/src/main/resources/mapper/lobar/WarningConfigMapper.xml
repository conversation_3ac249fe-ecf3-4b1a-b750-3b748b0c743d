<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.WarningConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.WarningConfig">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="absent_" property="absent" />
        <result column="automatic_exit_" property="automaticExit" />
        <result column="group_" property="group" />
        <result column="group_check_" property="groupCheck" />
        <result column="man_age_" property="manAge" />
        <result column="woman_age_" property="womanAge" />
        <result column="certificate_" property="certificate" />
        <result column="temperature_" property="temperature" />
        <result column="health_code_" property="healthCode" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="absent_switch" property="absentSwitch" />
        <result column="automatic_exit_switch" property="automaticExitSwitch" />
        <result column="group_switch" property="groupSwitch" />
        <result column="man_age_switch" property="manAgeSwitch" />
        <result column="woman_age_switch" property="womanAgeSwitch" />
        <result column="certificate_switch" property="certificateSwitch" />
        <result column="temperature_switch" property="temperatureSwitch" />
        <result column="health_code_switch" property="healthCodeSwitch" />
        <result column="user_out_check_" property="userOutCheck" />
        <result column="user_out_switch" property="userOutSwitch" />
        <result column="user_stay_duration" property="userStayDuration" />
        <result column="user_stay_switch" property="userStaySwitch" />
    </resultMap>

</mapper>
