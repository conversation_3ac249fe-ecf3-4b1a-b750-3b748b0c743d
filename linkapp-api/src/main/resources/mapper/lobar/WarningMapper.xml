<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.WarningMapper">
    <resultMap id="WarningDTOResultMap" type="com.easylinkin.linkappapi.lobar.entity.Warning">
        <association property="empUserInfo" column="userId"
                     select="com.easylinkin.linkappapi.lobar.mapper.EmpUserBaseMapper.selectById"/>
        <association property="handleUserInfo" column="handle_user_"
                     select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
    </resultMap>

    <select id="queryList" resultMap="WarningDTOResultMap">
        select * from
        (
        SELECT user_.id,
               1        AS warningType,
               user_.status_,
               warning_rule_,
               warning_time_,
               handle_time_,
               handle_remark_,
               handle_user_,
               operate_type,
               user_id_ AS userId
        FROM `app_user_warning` user_
        WHERE user_.tenant_id_ = #{warningDTO.tenantId}
        <if test="warningDTO.userId != null and warningDTO.userId != ''">
            AND user_.user_id_ = #{warningDTO.userId}
        </if>
        <if test="warningDTO.userIds != null and warningDTO.userIds.size() > 0">
            AND user_.user_id_ in
            <foreach item="item" index="index" collection="warningDTO.userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="warningDTO.status != null">
            AND user_.status_ = #{warningDTO.status}
        </if>
        <if test="warningDTO.startTime != null">
            AND user_.warning_time_ &gt;= #{warningDTO.startTime}
        </if>
        <if test="warningDTO.endTime != null">
            AND user_.warning_time_ &lt;= #{warningDTO.endTime}
        </if>

        UNION

        SELECT DISTINCT group_.id,
                        2    AS warningType,
                        group_.status_,
                        warning_rule_,
                        warning_time_,
                        handle_time_,
                        handle_remark_,
                        handle_user_,
                        operate_type,
                        null AS userId
        FROM `app_group_warning` group_
                 LEFT JOIN `app_user_project` project_ ON project_.group_id_ = group_.group_id_
        WHERE group_.tenant_id_ = #{warningDTO.tenantId}
        <if test="warningDTO.status != null">
            AND group_.status_ = #{warningDTO.status}
        </if>
        <if test="warningDTO.startTime != null">
            AND group_.warning_time_ &gt;= #{warningDTO.startTime}
        </if>
        <if test="warningDTO.endTime != null">
            AND group_.warning_time_ &lt;= #{warningDTO.endTime}
        </if>
        <if test="warningDTO.userIds != null and warningDTO.userIds.size() > 0">
            AND project_.user_id_ in
            <foreach item="item" index="index" collection="warningDTO.userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="warningDTO.paramKey != null and warningDTO.paramKey != ''">
            and INSTR(#{warningDTO.paramKey}, group_.group_id_)
        </if>

        UNION

        SELECT age_.id,
               3        AS warningType,
               age_.status_,
               warning_rule_,
               warning_time_,
               handle_time_,
               handle_remark_,
               handle_user_,
               operate_type,
               user_id_ AS userId
        FROM `app_age_warning` age_
        WHERE age_.tenant_id_ = #{warningDTO.tenantId}
        <if test="warningDTO.userId != null and warningDTO.userId != ''">
            AND age_.user_id_ = #{warningDTO.userId}
        </if>
        <if test="warningDTO.userIds != null and warningDTO.userIds.size() > 0">
            AND age_.user_id_ in
            <foreach item="item" index="index" collection="warningDTO.userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="warningDTO.status != null">
            AND age_.status_ = #{warningDTO.status}
        </if>
        <if test="warningDTO.startTime != null">
            AND age_.warning_time_ &gt;= #{warningDTO.startTime}
        </if>
        <if test="warningDTO.endTime != null">
            AND age_.warning_time_ &lt;= #{warningDTO.endTime}
        </if>

        UNION

        SELECT certificate_.id,
               4        AS warningType,
               certificate_.status_,
               warning_rule_,
               warning_time_,
               handle_time_,
               handle_remark_,
               handle_user_,
               operate_type,
               user_id_ AS userId
        FROM `app_certificate_warning` certificate_
        WHERE certificate_.tenant_id_ = #{warningDTO.tenantId}
        <if test="warningDTO.userId != null and warningDTO.userId != ''">
            AND certificate_.user_id_ = #{warningDTO.userId}
        </if>
        <if test="warningDTO.userIds != null and warningDTO.userIds.size() > 0">
            AND certificate_.user_id_ in
            <foreach item="item" index="index" collection="warningDTO.userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="warningDTO.status != null">
            AND certificate_.status_ = #{warningDTO.status}
        </if>
        <if test="warningDTO.startTime != null">
            AND certificate_.warning_time_ &gt;= #{warningDTO.startTime}
        </if>
        <if test="warningDTO.endTime != null">
            AND certificate_.warning_time_ &lt;= #{warningDTO.endTime}
        </if>
        union
        select user_outin_.id,
               5        as warningType,
               user_outin_.status_,
               warning_rule_,
               warning_time_,
               handle_time_,
               handle_remark_,
               handle_user_,
               operate_type,
               user_id_ as userId
        from app_user_outin_warning user_outin_
        where user_outin_.tenant_id_ = #{warningDTO.tenantId}
        <if test="warningDTO.userId != null and warningDTO.userId != ''">
            AND user_outin_.user_id_ = #{warningDTO.userId}
        </if>
        <if test="warningDTO.userIds != null and warningDTO.userIds.size() > 0">
            AND user_outin_.user_id_ in
            <foreach item="item" index="index" collection="warningDTO.userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="warningDTO.status != null">
            AND user_outin_.status_ = #{warningDTO.status}
        </if>
        <if test="warningDTO.startTime != null">
            AND user_outin_.warning_time_ &gt;= #{warningDTO.startTime}
        </if>
        <if test="warningDTO.endTime != null">
            AND user_outin_.warning_time_ &lt;= #{warningDTO.endTime}
        </if>
      union
      SELECT epidemic_.id,
      6 AS warningType,
      epidemic_.status_,
      warning_rule_,
      warning_time_,
      handle_time_,
      handle_remark_,
      handle_user_,
      operate_type,
      user_id_ AS userId
      FROM `app_epidemic_warning` epidemic_
      WHERE epidemic_.tenant_id_ = #{warningDTO.tenantId}
      <if test="warningDTO.userId != null and warningDTO.userId != ''">
        AND epidemic_.user_id_ = #{warningDTO.userId}
      </if>
      <if test="warningDTO.epidemicWarnType != null and warningDTO.epidemicWarnType != '' and warningDTO.epidemicWarnType != 0">
        AND epidemic_.type_ = #{warningDTO.epidemicWarnType}
      </if>
      <if test="warningDTO.userIds != null and warningDTO.userIds.size() > 0">
        AND epidemic_.user_id_ in
        <foreach item="item" index="index" collection="warningDTO.userIds" open="(" separator=","
          close=")">
          #{item}
        </foreach>
      </if>
      <if test="warningDTO.status != null">
        AND epidemic_.status_ = #{warningDTO.status}
      </if>
      <if test="warningDTO.startTime != null">
        AND epidemic_.warning_time_ &gt;= #{warningDTO.startTime}
      </if>
      <if test="warningDTO.endTime != null">
        AND epidemic_.warning_time_ &lt;= #{warningDTO.endTime}
      </if>
        ) warming
        <where>
            <if test="warningDTO.warningTypes != null and warningDTO.warningTypes.size() > 0">
                AND warming.warningType in
                <foreach item="item" index="index" collection="warningDTO.warningTypes" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="warningDTO.status != null">
                AND warming.status_ = #{warningDTO.status}
            </if>
        </where>
        ORDER BY warming.warning_time_ DESC
    </select>

    <update id="handleWarning">
        UPDATE
        <if test="warningType != null and warningType != '' and warningType == 1">
            `app_user_warning`
        </if>
        <if test="warningType != null and warningType != '' and warningType == 2">
            `app_group_warning`
        </if>
        <if test="warningType != null and warningType != '' and warningType == 3">
            `app_age_warning`
        </if>
        <if test="warningType != null and warningType != '' and warningType == 4">
            `app_certificate_warning`
        </if>
        SET `handle_remark_` = #{handleRemark}, `handle_time_` = #{handleTime}, `handle_user_` = #{handleUser}, `modify_time_` = #{modifyTime}, `status_` = 1
        WHERE
            `status_` = 0
          AND `id` = #{id}
    </update>
</mapper>
