<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.VisitorRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.VisitorRecord">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="name_" property="name" />
        <result column="company_" property="company" />
        <result column="plate_number_" property="plateNumber" />
        <result column="id_card_" property="idCard" />
        <result column="phone_" property="phone" />
        <result column="start_place_" property="startPlace" />
        <result column="health_code_status_" property="healthCodeStatus" />
        <result column="twentyfour_test_status_" property="twentyfourTestStatus" />
        <result column="temperature_" property="temperature" />
        <result column="in_time" property="inTime" />
        <result column="out_time" property="outTime" />
        <result column="status_" property="status" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="listPage" resultMap="BaseResultMap">
        SELECT *
        FROM app_visitor_record
        WHERE tenant_id_ = #{visitorRecord.tenantId}
        <if test="visitorRecord.name != null and visitorRecord.name != ''">
            and name_ like CONCAT('%', #{visitorRecord.name}, '%')
        </if>
        <if test="visitorRecord.startTime != null">
            and create_time_ &gt;= #{visitorRecord.startTime}
        </if>
        <if test="visitorRecord.endTime != null">
            and create_time_ &lt;= #{visitorRecord.endTime}
        </if>
        <if test="visitorRecord.status != null ">
            and status_ = #{visitorRecord.status}
        </if>
        ORDER BY create_time_ DESC
    </select>

    <select id="countByStatus" resultType="com.easylinkin.linkappapi.lobar.vo.VisitorRecordStatusVO">
        SELECT count(1) AS num,status_ AS status
        FROM app_visitor_record
        WHERE tenant_id_ = #{visitorRecord.tenantId}
        <if test="visitorRecord.name != null and visitorRecord.name != ''">
            and name_ like CONCAT('%', #{visitorRecord.name}, '%')
        </if>
        <if test="visitorRecord.startTime != null">
            and create_time_ &gt;= #{visitorRecord.startTime}
        </if>
        <if test="visitorRecord.endTime != null">
            and create_time_ &lt;= #{visitorRecord.endTime}
        </if>
        GROUP BY status_
    </select>

    <select id="countByIdCard" resultType="java.lang.Integer">
        SELECT count(DISTINCT id_card_)  AS num
        FROM app_visitor_record
        WHERE tenant_id_ = #{visitorRecord.tenantId}
        <if test="visitorRecord.startTime != null">
            and create_time_ &gt;= #{visitorRecord.startTime}
        </if>
        <if test="visitorRecord.endTime != null">
            and create_time_ &lt;= #{visitorRecord.endTime}
        </if>
    </select>
</mapper>
