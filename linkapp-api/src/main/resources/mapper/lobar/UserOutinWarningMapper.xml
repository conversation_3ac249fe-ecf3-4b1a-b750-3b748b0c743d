<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserOutinWarningMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserOutinWarning">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id_" jdbcType="VARCHAR" property="tenantId"/>
        <result column="user_id_" jdbcType="VARCHAR" property="userId"/>
        <result column="warning_time_" jdbcType="TIMESTAMP" property="warningTime"/>
        <result column="warning_rule_" jdbcType="VARCHAR" property="warningRule"/>
        <result column="type_" jdbcType="INTEGER" property="type"/>
        <result column="create_time_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time_" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="status_" jdbcType="INTEGER" property="status"/>
        <result column="group_id" jdbcType="VARCHAR" property="groupId"/>
        <result column="handle_time_" jdbcType="TIMESTAMP" property="handleTime"/>
        <result column="handle_remark_" jdbcType="VARCHAR" property="handleRemark"/>
        <result column="handle_user_" jdbcType="VARCHAR" property="handleUser"/>
        <result column="operate_type" jdbcType="INTEGER" property="operateType"/>
        <result column="in_clock_id" jdbcType="VARCHAR" property="inClockId"/>
        <result column="out_clock_id" jdbcType="VARCHAR" property="outClockId"/>
        <result column="remark_" jdbcType="LONGVARCHAR" property="remark"/>
    </resultMap>
    <resultMap id="OutinWarningDTOResultMap" type="com.easylinkin.linkappapi.lobar.dto.UserOutinWarningDTO">
        <result column="user_name" property="userName"/>
        <result column="group_name" property="groupName"/>
        <result column="company_project_id_" property="companyProjectId"/>
        <result column="work_type_" property="workType"/>
        <result column="telephone_" property="telephone"/>
        <result column="card_" property="idCard"/>
        <association property="inClock" column="in_clock_id"
                     select="com.easylinkin.linkappapi.lobar.mapper.UserClockMapper.selectById"/>
        <association property="outClock" column="out_clock_id"
                     select="com.easylinkin.linkappapi.lobar.mapper.UserClockMapper.selectById"/>
        <association property="handleUserInfo" column="handle_user_"
                     select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
    </resultMap>

    <select id="queryList" parameterType="com.easylinkin.linkappapi.lobar.dto.UserOutinWarningDTO"
            resultMap="OutinWarningDTOResultMap">
        SELECT uw.*,
               ub.name_ user_name,
               ub.card_,
               g.name_  group_name,
               g.company_project_id_,
               up.work_type_,
               ub.telephone_
        FROM app_user_outin_warning uw
                 left JOIN app_user_project up
                           on uw.user_id_ = up.user_id_ and uw.tenant_id_ = up.tenant_id_
                 left JOIN emp_user_base ub on up.user_id_ = ub.id
                 left JOIN app_group g on up.group_id_ = g.id
        WHERE uw.tenant_id_ = #{userOutinWarningDTO.tenantId}
        <if test="userOutinWarningDTO.companyProjectId != null and userOutinWarningDTO.companyProjectId != ''">
            and g.company_project_id_ = #{userOutinWarningDTO.companyProjectId}
        </if>
        <if test="userOutinWarningDTO.groupId != null and userOutinWarningDTO.groupId != ''">
            and up.group_id_ = #{userOutinWarningDTO.groupId}
        </if>
        <if test="userOutinWarningDTO.workType != null and userOutinWarningDTO.workType != ''">
            and up.work_type_ = #{userOutinWarningDTO.workType}
        </if>
        <if test="userOutinWarningDTO.startTime != null">
            <![CDATA[
            and uw.warning_time_ >= #{userOutinWarningDTO.startTime}
            ]]>
        </if>
        <if test="userOutinWarningDTO.endTime != null">
            <![CDATA[
            and uw.warning_time_ <= #{userOutinWarningDTO.endTime}
            ]]>
        </if>
        <if test="userOutinWarningDTO.status != null">
            and uw.status_ = #{userOutinWarningDTO.status}
        </if>
        <if test="userOutinWarningDTO.userName != null and userOutinWarningDTO.userName != ''">
            and (ub.card_ like concat('%', #{userOutinWarningDTO.userName}, '%') or
                 ub.name_ like concat('%', #{userOutinWarningDTO.userName}, '%'))
        </if>
        ORDER BY uw.modify_time_ DESC
    </select>
</mapper>