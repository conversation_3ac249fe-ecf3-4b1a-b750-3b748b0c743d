<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserRecord">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="record_time_" property="recordTime" />
        <result column="hour_" property="hour" />
        <result column="type_" property="type" />
        <result column="user_id_" property="userId" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="record_type_" property="recordType" />
        <result column="group_id_" property="groupId" />
        <result column="company_project_id_" property="companyProjectId" />
        <result column="on_area_" property="onArea" />
    </resultMap>

<!--    判断需不需要插入 防止高并发-->
    <insert id="checkAndInsert">
      INSERT INTO app_user_record (
        `id`,
        `tenant_id_`,
        `record_time_`,
        `hour_`,
        `type_`,
        `user_id_`,
        `creator_id_`,
        `create_time_`,
        `modify_id_`,
        `modify_time_`,
        `record_type_`,
        `group_id_`,
        `company_project_id_`
      ) SELECT
          #{id},
          #{tenantId},
          #{recordTime},
          #{hour},
          #{type},
          #{userId},
          #{creatorId},
          #{createTime},
          #{modifyId},
          #{modifyTime},
          #{recordType},
          #{groupId},
          #{companyProjectId}
      FROM
        DUAL
      WHERE
        NOT EXISTS ( SELECT id FROM app_user_record
               WHERE
           tenant_id_ = #{tenantId}
       AND `record_time_` = #{recordTime}
       AND `type_` = #{type}
       AND `user_id_` = #{userId});
    </insert>

    <select id="selectAll" resultMap="BaseResultMap">
    SELECT
      r.id,
      r.record_time_,
      r.hour_,
      r.type_,
      r.user_id_,
      r.record_type_,
      r.group_id_,
      r.company_project_id_,
      r.tenant_id_
    FROM
      app_user_record r
    WHERE
      r.type_ = 2
    <if test="tenantId != null and tenantId != ''">
      and r.tenant_id_ = #{tenantId}
    </if>
    <if test="time != null">
      <![CDATA[
      and r.record_time_ > #{time}
      ]]>
    </if>
  </select>

  <select id="getUserMouth" resultMap="BaseResultMap">
    SELECT
      *
    FROM
      app_user_record
    WHERE
      tenant_id_ = #{tenantId}
      AND user_id_ = #{userId}
      AND date_format( record_time_, "%Y-%m" ) = #{mouth}
    ORDER BY
      record_time_
  </select>

  <select id="findCompanyToDay"
    resultType="com.easylinkin.linkappapi.lobar.dto.app.AppUserGateDTO">
    SELECT
      b.name_ companyName,
      b.build_type_ buildType,
      a.id companyProjectId,
      count( DISTINCT d.id ) sum,
      count( DISTINCT e.id ) attendance
    FROM
      app_labor_company_project a
      LEFT JOIN app_labor_company b ON a.company_id_ = b.id
      LEFT JOIN app_group c ON a.id = c.company_project_id_
      AND c.status_ = 1
      LEFT JOIN app_user_project d ON c.id = d.group_id_
      AND d.status_ = 1
      LEFT JOIN app_user_record e ON a.id = e.company_project_id_
      AND e.record_type_ != 0
      AND e.record_time_ BETWEEN #{startTime}
      AND #{endTime}
    WHERE
      a.tenant_id_ = #{tenantId}
      AND a.status_ = 1
    GROUP BY
      a.id
    ORDER BY
      a.modify_time_
  </select>

  <select id="findGroupToDay"
    resultType="com.easylinkin.linkappapi.lobar.dto.app.AppUserGateDTO">
    SELECT
      a.name_ groupName,
      a.id groupId,
      count( DISTINCT b.id ) sum,
      count( DISTINCT e.id ) attendance
    FROM
      app_group a
      LEFT JOIN app_user_project b ON a.id = b.group_id_
      AND b.status_ = 1
      LEFT JOIN app_user_record e ON a.id = e.group_id_
      AND e.record_type_ != 0
      AND e.record_time_ BETWEEN #{startTime}
      AND #{endTime}
    WHERE
      a.tenant_id_ = #{tenantId}
      AND a.status_ = 1
      and a.company_project_id_ = #{companyProjectId}
    GROUP BY
      a.id
    ORDER BY
      a.modify_time_
  </select>

  <select id="findUser" resultType="com.easylinkin.linkappapi.lobar.dto.UserRecordDTO">
    SELECT
      a.user_id_ userId,
      b.photo_ photo,
      b.name_ userName,
      e.name_ companyName,
      c.name_ groupName,
      a.record_time_ recordTime,
      a.record_type_ recordType
    FROM
      app_user_record a
      LEFT JOIN emp_user_base b ON a.user_id_ = b.id
      LEFT JOIN app_group c ON a.group_id_ = c.id
      LEFT JOIN app_labor_company_project d ON a.company_project_id_ = d.id
      LEFT JOIN app_labor_company e ON d.company_id_ = e.id
    WHERE
      a.tenant_id_ = #{tenantId}
      <if test="groupId != null and groupId != ''">
        AND a.group_id_ = #{groupId}
      </if>
      <if test="companyProjectId != null and companyProjectId != ''">
        AND a.company_project_id_ = #{companyProjectId}
      </if>
      <if test="recordType != null">
        AND a.record_type_ = #{recordType}
      </if>
      AND a.record_time_ BETWEEN #{startTime}
      AND #{endTime}
  </select>

  <select id="findCompanyTime"
    resultType="com.easylinkin.linkappapi.lobar.dto.app.AppUserGateDTO">
    SELECT
      c.name_ companyName,
      c.build_type_ buildType,
      a.company_project_id_ companyProjectId,
      sum( a.sum_ ) sum,
      sum(a.on_ ) attendance
    FROM
      app_user_statistics a
      LEFT JOIN app_labor_company_project b ON a.company_project_id_ = b.id
      LEFT JOIN app_labor_company c ON b.company_id_ = c.id
    WHERE
      a.tenant_id_ = #{tenantId}
      AND a.record_time_ BETWEEN #{startTime}
      AND #{endTime}
    GROUP BY
      a.company_project_id_
    ORDER BY
      b.modify_time_
  </select>

  <select id="findGroupTime"
    resultType="com.easylinkin.linkappapi.lobar.dto.app.AppUserGateDTO">
    SELECT
      b.name_ groupName,
      a.group_id_ groupId,
      sum( a.sum_ ) sum,
      sum(a.on_ ) attendance
    FROM
      app_user_statistics a
      LEFT JOIN app_group b ON a.group_id_ = b.id
    WHERE
      a.tenant_id_ = #{tenantId}
      AND a.company_project_id_ = #{companyProjectId}
      AND a.record_time_ BETWEEN #{startTime}
      AND #{endTime}
    GROUP BY
      a.group_id_
    ORDER BY
      b.modify_time_
  </select>

  <select id="findUserToday" resultType="com.easylinkin.linkappapi.lobar.dto.UserRecordDTO">
    SELECT
    a.user_id_ userId,
    b.photo_ photo,
    b.name_ userName,
    e.name_ companyName,
    c.name_ groupName,
    f.record_time_ recordTime,
    IFNULL( f.record_type_, 0 ) recordType
    FROM
    app_user_project a
    LEFT JOIN emp_user_base b ON a.user_id_ = b.id
    LEFT JOIN app_group c ON a.group_id_ = c.id
    LEFT JOIN app_labor_company_project d ON c.company_project_id_ = d.id
    LEFT JOIN app_labor_company e ON d.company_id_ = e.id
    LEFT JOIN app_user_record f ON a.user_id_ = f.user_id_
    AND a.tenant_id_ = f.tenant_id_
    AND f.record_time_ BETWEEN #{startTime}
    AND #{endTime}
    WHERE
    a.tenant_id_ = #{tenantId}
    and a.delete_state_ = 1
--     and a.status_ = 1
    <if test="groupId != null and groupId != ''">
      AND a.group_id_ = #{groupId}
    </if>
    <if test="companyProjectId != null and companyProjectId != ''">
      AND c.company_project_id_ = #{companyProjectId}
    </if>
    <choose>
      <when test="recordType == 0">
        AND (f.record_type_ = #{recordType} or (f.record_type_ is null and a.status_ = 1))
      </when>
      <when test="recordType != null">
        AND (f.record_type_ = #{recordType})
      </when>
      <when test="recordType == null">
        and (a.status_ = 1 or f.record_type_ is not null)
      </when>
    </choose>
    ORDER BY
      a.modify_time_
  </select>

  <select id="countGroupByMonth" parameterType="com.easylinkin.linkappapi.shigongyun.vo.LaborAttendanceVo"
          resultType="java.util.Map">
    select date_format(aur.record_time_, '%Y-%m') as countTime,
           count(*)                               as num
    from app_user_record aur,
         linkapp_tenant lt
    where aur.tenant_id_ = lt.id
      and lt.status = 1
    <![CDATA[ and aur.record_type_ <> 0 ]]>
    <if test="tenantId != null and tenantId != ''">
      and aur.tenant_id_ = #{tenantId}
    </if>
    <if test="startTime != null">
      <![CDATA[
      and aur.record_time_ >= #{startTime}
      ]]>
    </if>
    <if test="endTime != null">
      <![CDATA[
      and aur.record_time_ <= #{endTime}
      ]]>
    </if>
    group by date_format(aur.record_time_, '%Y-%m')
  </select>

  <select id="countNumByParam" resultType="java.lang.Integer">
    select count(*) as num
    from app_user_record aur,
         linkapp_tenant lt
    where aur.tenant_id_ = lt.id
      and lt.status = 1
    <![CDATA[ and aur.record_type_ <> 0 ]]>
    <if test="tenantId != null and tenantId != ''">
      and aur.tenant_id_ = #{tenantId}
    </if>
    <if test="startTime != null">
      <![CDATA[
      and aur.record_time_ >= #{startTime}
      ]]>
    </if>
    <if test="endTime != null">
      <![CDATA[
      and aur.record_time_ <= #{endTime}
      ]]>
    </if>
  </select>

  <select id="analysisMonth"
    resultType="com.easylinkin.linkappapi.lobar.dto.app.LobarUserDTO">
      SELECT
      record_time_ recordTime,
      count( id ) attendance
      FROM
      app_user_record
      WHERE
      tenant_id_ = #{tenantId}
      AND date_sub( curdate( ), INTERVAL 30 DAY ) &lt;= date( record_time_ )
      AND record_type_ != 0
      GROUP BY
      record_time_
  </select>

  <select id="countWorkTypeToDay"
    resultType="com.easylinkin.linkappapi.lobar.entity.UserProject">
    SELECT
      a.user_id_ userId,
      b.work_type_ workType
    FROM
      app_user_record a
      JOIN app_user_project b ON a.user_id_ = b.user_id_
      AND a.tenant_id_ = b.tenant_id_
    WHERE
      a.record_type_ != 0
      AND to_days( a.record_time_ ) = to_days( now( ) )
      <if test="workTypeName != null and workTypeName != ''">
        AND b.work_type_ LIKE concat(#{workTypeName}, '%')
      </if>
      <if test="tenantId != null and tenantId != ''">
        AND a.tenant_id_ = #{tenantId}
      </if>
  </select>

<!--  非当天出勤和在场-->
  <select id="selectByTenantId" resultMap="com.easylinkin.linkappapi.lobar.mapper.UserProjectMapper.BaseResultDTO2">
    SELECT
    a.tenant_id_,
    b.work_type_,
    d.build_type_,
    a.record_type_,
    a.user_id_
    FROM
    app_user_record a
    LEFT JOIN app_user_project b ON a.user_id_ = b.user_id_
    AND a.tenant_id_ = b.tenant_id_
    LEFT JOIN app_labor_company_project c ON a.company_project_id_ = c.id
    LEFT JOIN app_labor_company d ON c.company_id_ = d.id
    where a.record_time_ BETWEEN #{startTime}
    AND #{endTime}
    <if test="tenantId != null and tenantId != ''">
      AND a.tenant_id_ = #{tenantId}
    </if>
    <if test="tenantIds !=null and tenantIds.size>0">
      and a.tenant_id_ in
      <foreach collection="tenantIds" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="countAllByEnterprise" resultMap="BaseResultMap">
    SELECT
    a.*
    FROM
    app_user_record a
    LEFT JOIN linkapp_tenant b ON a.tenant_id_ = b.id
    where a.record_time_ BETWEEN #{startTime}
    AND #{endTime}
    <if test="projectName != null and projectName != ''">
      AND b.platform_project_name LIKE CONCAT('%',#{projectName},'%')
    </if>
    <if test="tenantIds !=null and tenantIds.size>0">
      AND a.tenant_id_ IN
      <foreach collection="tenantIds" index="index" item="tenantId" open="(" separator="," close=")">
        #{tenantId}
      </foreach>
    </if>
    <if test="projectIds !=null and projectIds.size>0">
      and b.project_id in
      <foreach collection="projectIds" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="findAllMoth" resultMap="BaseResultMap">
    SELECT
      user_id_,
      record_time_
    FROM
      app_user_record
    WHERE
      record_type_ != 0
      and tenant_id_ = #{tenantId}
      AND date_format(record_time_, "%Y-%m" ) = #{mouthStr}
      ORDER BY record_time_
  </select>

  <select id="countByProject" resultType="com.easylinkin.linkappapi.lobar.dto.UserRecordDTO">
    SELECT
      a.id,
      a.user_id_,
      a.record_type_,
      a.record_time_
    FROM
      app_user_record a
      LEFT JOIN app_user_project b ON a.user_id_ = b.user_id_
      AND a.tenant_id_ = b.tenant_id_
      LEFT JOIN app_labor_company_project c ON a.company_project_id_ = c.id
      LEFT JOIN app_labor_company d ON c.company_id_ = d.id
    <where>
      <if test="tenantId != null and tenantId != ''">
        AND a.tenant_id_ = #{tenantId}
      </if>
      <if test="userType == 1">
        AND b.work_type_ LIKE '管理人员%' AND d.build_type_ = '13'
      </if>
      <if test="userType == 2">
        AND b.work_type_ LIKE '管理人员%' AND d.build_type_ != '13'
      </if>
      <if test="userType == 3">
        AND (b.work_type_ LIKE '作业人员%' or b.work_type_ LIKE '其他人员%' or b.work_type_ is NULL )
      </if>
      AND a.record_time_ BETWEEN #{startTime}
      AND #{endTime}
    </where>
      ORDER BY a.record_time_
  </select>

  <select id="countEnWorkTypeToDay" resultType="com.easylinkin.linkappapi.lobar.entity.UserProject">
    SELECT
    a.user_id_,
    a.work_type_,
    b.id
    FROM
    app_user_project a
    LEFT JOIN app_user_record b ON a.user_id_ = b.user_id_
    AND a.tenant_id_ = b.tenant_id_
    AND b.record_type_ != 0
    AND to_days( b.record_time_ ) = to_days( now( ) )
    where a.status_ = 1 and a.delete_state_ = 1
<!--    <if test="workTypeName != null and workTypeName != ''">-->
<!--      AND a.work_type_ LIKE concat(#{workTypeName}, '%')-->
<!--    </if>-->
    <if test="tenantId != null and tenantId != ''">
      AND a.tenant_id_ = #{tenantId}
    </if>
  </select>

  <select id="getOnAreaToDay" resultMap="BaseResultMap">
    SELECT
      a.*
    FROM
      app_user_record a
      LEFT JOIN app_labor_company_project b ON a.company_project_id_ = b.id
      LEFT JOIN app_labor_company c ON b.company_id_ = c.id
    WHERE
      a.tenant_id_ = #{tenantId}
      AND TO_DAYS( a.record_time_ ) = TO_DAYS( NOW( ) )
      AND a.on_area_ IN (1,2,3)
      <if test="buildTypes !=null and buildTypes.size>0">
        AND c.build_type_ IN
        <foreach collection="buildTypes" index="index" item="buildType" open="(" separator="," close=")">
          #{buildType}
        </foreach>
      </if>
  </select>

  <select id="findSevenAttendance" resultMap="BaseResultMap">
    SELECT
      record_time_,
      user_id_,
      record_type_
    FROM
      app_user_record
    WHERE
      date_sub( curdate( ), INTERVAL 8 DAY ) >= date( record_time_ )
    <if test="tenantId != null and tenantId != ''">
      and tenant_id_ = #{tenantId}
    </if>
--     GROUP BY
--       record_time_
  </select>

  <select id="getByToday" resultType="com.easylinkin.linkappapi.lobar.dto.UserRecordDTO">
    SELECT
    a.user_id_ userId,
    b.record_type_ recordType,
    a.on_area_ onArea,
    a.work_type_ workType,
    b.group_id_ groupId,
    b.company_project_id_ companyProjectId
    FROM
    app_user_project a
    LEFT JOIN app_user_record b ON a.tenant_id_ = b.tenant_id_
    AND a.user_id_ = b.user_id_ AND to_days( b.record_time_ ) = to_days( now( ) )
    WHERE
    a.delete_state_ = 1 AND a.status_ = 1
    <if test="tenantId != null and tenantId != ''">
      AND a.tenant_id_ = #{tenantId}
    </if>
  </select>

  <select id="findHourMonth" resultMap="BaseResultMap">
    SELECT
    id,
    user_id_,
    record_time_,
    hour_
    FROM
    app_user_record
    WHERE
    tenant_id_ = #{userClockDTO.tenantId}
    AND date_format(record_time_, "%Y-%m" ) = #{userClockDTO.mouthStr}
    <if test="userClockDTO.ids != null and userClockDTO.ids.size() > 0">
      and user_id_ in
      <foreach collection="userClockDTO.ids" index="index" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    ORDER BY record_time_
  </select>
    <select id="findRecordByPage" resultType="com.easylinkin.linkappapi.lobar.dto.UserRecordDTO">
      SELECT
      a.*,
      c.name_ userName
      FROM
      app_user_record a
      LEFT JOIN app_user_project b ON a.user_id_ = b.user_id_
      AND a.tenant_id_ = b.tenant_id_ and b.delete_state_ = 1
      LEFT JOIN emp_user_base c ON a.user_id_ = c.id
      where a.record_time_ BETWEEN #{userClockDTO.startTime}
      AND #{userClockDTO.endTime}
      AND a.tenant_id_ = #{userClockDTO.tenantId}
      <if test="userClockDTO.userWorkType != null and userClockDTO.userWorkType == 1">
        AND (b.work_type_ LIKE '作业人员%' or b.work_type_ LIKE '其他人员%' or b.work_type_ is NULL )
      </if>
      <if test="userClockDTO.userWorkType != null and userClockDTO.userWorkType == 2">
        AND b.work_type_ LIKE '管理人员%'
      </if>
      <if test="userClockDTO.companyProjectId != null and userClockDTO.companyProjectId != ''">
        AND a.company_project_id_ = #{userClockDTO.companyProjectId}
      </if>
      ORDER BY record_time_ DESC
    </select>
</mapper>
