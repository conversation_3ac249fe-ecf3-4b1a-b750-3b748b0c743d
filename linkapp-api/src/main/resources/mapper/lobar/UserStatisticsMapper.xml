<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserStatistics">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="company_project_id_" property="companyProjectId" />
        <result column="record_time_" property="recordTime" />
        <result column="sum_" property="sum" />
        <result column="manage_" property="manage" />
        <result column="work_" property="work" />
        <result column="on_" property="on" />
        <result column="off_" property="off" />
        <result column="hours_" property="hours" />
        <result column="type_" property="type" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <resultMap id="UserStatisticsVo" type="com.easylinkin.linkappapi.lobar.entity.vo.UserStatisticsVo" extends="BaseResultMap">
        <result column="record_time_str_" property="recordTimeStr" />
    </resultMap>

    <select id="queryStatistics" resultMap="BaseResultMap">
        SELECT
        *
        FROM app_user_statistics
        WHERE tenant_id_ = #{clockDTO.tenantId}
        <if test="clockDTO.startTime != null">
            and record_time_ &gt;=  #{clockDTO.startTime}
        </if>
        <if test="clockDTO.endTime != null">
            and record_time_ &lt;=  #{clockDTO.endTime}
        </if>
        and type_ = 2
        ORDER BY modify_time_ DESC
    </select>

  <select id="selectPart" resultType="com.easylinkin.linkappapi.lobar.dto.screen.LobarScreenDTO">
    SELECT
      us.company_project_id_ id,
      CONCAT( ROUND(
                  CASE
                      WHEN #{userStatisticsDTO.userWorkType} = 1 THEN SUM(us.work_)
                      WHEN #{userStatisticsDTO.userWorkType} = 2 THEN SUM(us.manage_)
                      ELSE SUM(us.on_)
                  END
                         / SUM( us.sum_ ) * 100 ), "%" ) part
    FROM
      app_user_statistics us
    WHERE
      us.tenant_id_ = #{userStatisticsDTO.tenantId}
      <if test="userStatisticsDTO.startTime != null">
          and us.record_time_ &gt;=  #{userStatisticsDTO.startTime}
      </if>
      <if test="userStatisticsDTO.endTime != null">
          and us.record_time_ &lt;=  #{userStatisticsDTO.endTime}
      </if>
    GROUP BY
      us.company_project_id_
  </select>

  <select id="averageHour" resultType="java.lang.Double">
    SELECT
    IFNULL(ROUND( SUM( hours_ ) / SUM( sum_ ), 2 ),0)
    FROM
      app_user_statistics
    WHERE
        tenant_id_ = #{userStatisticsDTO.tenantId}
    <if test="userStatisticsDTO.startTime != null">
      and record_time_ &gt;=  #{userStatisticsDTO.startTime}
    </if>
    <if test="userStatisticsDTO.endTime != null">
      and record_time_ &lt;=  #{userStatisticsDTO.endTime}
    </if>
  </select>

  <select id="companyHour" resultType="com.easylinkin.linkappapi.lobar.dto.screen.LobarScreenDTO">
    SELECT
    us.company_project_id_ id,
    IFNULL(ROUND( SUM( hours_ ) / SUM( sum_ ), 2 ),0)  hours
    FROM
    app_user_statistics us
    WHERE
    us.tenant_id_ = #{tenantId}
    GROUP BY
    us.company_project_id_
  </select>

  <select id="findSevenAttendance" resultMap="BaseResultMap">
    SELECT
    record_time_,
    sum( sum_ ) sum_,
    sum( on_ ) on_,
    sum( manage_ ) manage_,
    sum( work_ ) work_
    FROM
    app_user_statistics
    WHERE
    date_sub( curdate( ), INTERVAL 8 DAY ) &lt; date( record_time_ )
    <if test="tenantId != null and tenantId != ''">
      and tenant_id_ = #{tenantId}
    </if>
    GROUP BY record_time_
  </select>

    <select id="staticAttendance" resultMap="UserStatisticsVo">
        SELECT
        DATE_FORMAT(record_time_,'%Y%m%d') record_time_str_,
        sum( sum_ ) sum_,
        sum( on_ ) on_
        FROM
        app_user_statistics
        <where>
            <if test="tenantId != null and tenantId != ''">
                and tenant_id_ = #{tenantId}
            </if>
            <if test="startTime != null">
                and record_time_ &gt;=  #{startTime}
            </if>
            <if test="endTime != null">
                and record_time_ &lt;=  #{endTime}
            </if>
        </where>
        GROUP BY record_time_
    </select>

    <select id="selectMaxTime" resultType="java.util.Date">
        SELECT max(record_time_) FROM app_user_statistics
    <where>
        <if test="tenantId != null and tenantId != ''">
            and tenant_id_ = #{tenantId}
        </if>
    </where>
    </select>

    <select id="findNumByReportId" resultType="java.lang.Integer">
        SELECT
            sum( sum_ )
        FROM
            app_user_statistics a
                JOIN app_report b ON a.tenant_id_ = b.tenant_id_
                AND date_format( a.record_time_, "%Y-%m" ) = date_format( b.start_time_, "%Y-%m" )
        WHERE
            b.id = #{reportId}
    </select>

    <select id="getUserNumByTime" resultType="java.lang.Integer">
        SELECT
            sum( sum_ )
        FROM
            app_user_statistics a
        WHERE
            a.tenant_id_ = #{tenantId}
          AND date_format( a.record_time_, "%Y-%m" ) = date_format( #{time}, "%Y-%m" )
    </select>

</mapper>
