<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.UserBlacklistMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.UserBlacklist">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="user_id_" property="userId" />
        <result column="type_" property="type" />
        <result column="reason_" property="reason" />
        <result column="url_" property="url" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="enterprise_source_type_" property="enterpriseSourceType" />
        <result column="enterprise_create_user_name_" property="enterpriseCreateUserName" />
        <result column="enterprise_create_user_id_" property="enterpriseCreateUserId" />
        <result column="enterprise_organization_id_" property="enterpriseOrganizationId" />
    </resultMap>

    <select id="queryListByPage" resultType="com.easylinkin.linkappapi.lobar.dto.UserBlacklistDTO">
        SELECT
          a.id,
          b.name_ userName,
          b.card_ card,
          a.create_time_ createTime,
          a.reason_ reason,
          a.tenant_id_ tenantId,
          a.approval_status_ approvalStatus,
          c.platform_project_name projectName,
          if(a.enterprise_source_type_ = 2,a.enterprise_create_user_name_,d.nickname) creatorName,
          a.enterprise_organization_id_ enterpriseOrganizationId
        FROM
          app_user_blacklist a
          LEFT JOIN emp_user_base b ON a.user_id_ = b.id
          LEFT JOIN linkapp_tenant c ON a.tenant_id_ = c.id
          LEFT JOIN linkapp_user d ON a.creator_id_ = d.id
        <where>
          <if test="dto.projectName != null and dto.projectName != ''">
            AND c.platform_project_name LIKE CONCAT('%',#{dto.projectName},'%')
          </if>
<!--          <if test="dto.tenantId != null and dto.tenantId != ''">-->
<!--            AND a.tenant_id_ = #{dto.tenantId}-->
<!--          </if>-->
          <if test="dto.userName != null and dto.userName != ''">
              AND b.name_ LIKE CONCAT('%',#{dto.userName},'%')
          </if>
          <if test="dto.type != null">
              AND a.type_ = #{dto.type}
          </if>
<!--          <if test="dto.enterpriseSourceType != null">-->
<!--            AND a.enterprise_source_type_ = #{dto.enterpriseSourceType}-->
<!--          </if>-->
          <if test="dto.srcOrganizationId != null">
            AND a.enterprise_organization_id_ = #{dto.srcOrganizationId}
          </if>
        </where>
        ORDER BY
          a.create_time_ DESC
    </select>

    <select id="getBlackNumBy" resultType="int">
        select count(*)
        from app_user_blacklist aub
        where aub.tenant_id_ = #{tenantId}
          and aub.user_id_ = #{userId}
    </select>
  <select id="countByCard" resultType="java.lang.Integer">
    select count(*)
        from app_user_blacklist a
         LEFT JOIN emp_user_base b ON a.user_id_ = b.id
    where b.card_ = #{card}
  </select>
</mapper>
