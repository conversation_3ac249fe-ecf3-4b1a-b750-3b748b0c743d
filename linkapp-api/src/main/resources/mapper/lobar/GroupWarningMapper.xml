<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.lobar.mapper.GroupWarningMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.lobar.entity.GroupWarning">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="group_id_" property="groupId" />
        <result column="warning_time_" property="warningTime" />
        <result column="warning_rule_" property="warningRule" />
        <result column="detail_" property="detail" />
        <result column="attendance_" property="attendance" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="status_" property="status" />
        <result column="handle_time_" property="handleTime" />
        <result column="handle_remark_" property="handleRemark" />
        <result column="operate_type" property="operateType" />
    </resultMap>
    <resultMap id="GroupWarningDtoResultMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.lobar.dto.GroupWarningDTO">
        <result column="groupName" property="groupName"/>
        <result column="companyName" property="companyName"/>
        <result column="leadName" property="leadName"/>
        <result column="telephone" property="telephone"/>
        <association property="handleUserInfo" column="handle_user_" select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
    </resultMap>

    <select id="queryList" resultMap="GroupWarningDtoResultMap">
        SELECT gw.*,
               g.name_       groupName,
               lc.name_      companyName,
               ub.name_      leadName,
               ub.telephone_ telephone
        FROM app_group_warning gw
                 left JOIN app_group g on gw.group_id_ = g.id
                 left JOIN app_labor_company_project lcp on g.company_project_id_ = lcp.id
                 left JOIN app_labor_company lc on lcp.company_id_ = lc.id
                 left JOIN emp_user_base ub on g.lead_user_id_ = ub.id
        WHERE gw.tenant_id_ = #{groupWarningDTO.tenantId}
        <if test="groupWarningDTO.groupName != null and groupWarningDTO.groupName != ''">
            and g.name_ like CONCAT('%', #{groupWarningDTO.groupName}, '%')
        </if>
        <if test="groupWarningDTO.leadName != null and groupWarningDTO.leadName != ''">
            and ub.name_ like CONCAT('%', #{groupWarningDTO.leadName}, '%')
        </if>
        <if test="groupWarningDTO.startTime != null">
            and gw.warning_time_ &gt;= #{groupWarningDTO.startTime}
        </if>
        <if test="groupWarningDTO.endTime != null">
            and gw.warning_time_ &lt;= #{groupWarningDTO.endTime}
        </if>
        <if test="groupWarningDTO.status != null ">
            and gw.status_ = #{groupWarningDTO.status}
        </if>
        ORDER BY gw.modify_time_ DESC
    </select>

</mapper>
