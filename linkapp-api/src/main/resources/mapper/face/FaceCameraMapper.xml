<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.facerecognition.mapper.FaceCameraMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.facerecognition.entity.FaceCamera">
        <id column="id" property="id"/>
        <result column="device_code" property="deviceCode"/>
        <result column="position" property="position"/>
        <result column="remarks" property="remarks"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="online_state" property="status"/>
        <result column="unit_name" property="unitName"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="getFaceCameras" resultMap="BaseResultMap">

        select lfc.id, lfc.device_code, lfc.position, lfc.remarks, lfc.tenant_id, lfc.create_time, ld.online_state, ldu.name as unit_name
               from linkapp_face_camera lfc
            left join linkapp_device ld on ld.code = lfc.device_code and ld.delete_state = '1'
            left join linkapp_device_unit ldu on ldu.id = ld.device_unit_id
            <where>
                lfc.tenant_id = #{camera.tenantId}
                <if test="camera.position != null and camera.position != ''">
                    and lfc.position like CONCAT('%', #{camera.position}, '%')
                </if>
                <if test="camera.status != null and camera.status != ''">
                    and ld.online_state = #{camera.status}
                </if>
            </where>
    </select>

</mapper>
