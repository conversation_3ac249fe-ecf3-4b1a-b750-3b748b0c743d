<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.buildcheckinfo.dao.BuildCheckInfoMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.buildcheckinfo.entity.BuildCheckInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="tenant_project_id" jdbcType="BIGINT" property="tenantProjectId"/>
        <result column="check_title" jdbcType="VARCHAR" property="checkTitle"/>
        <result column="check_content" jdbcType="VARCHAR" property="checkContent"/>
        <result column="check_level" jdbcType="INTEGER" property="checkLevel"/>
        <result column="check_org_code" jdbcType="VARCHAR" property="checkOrgCode"/>
        <result column="check_org_name" jdbcType="VARCHAR" property="checkOrgName"/>
        <result column="check_type" jdbcType="INTEGER" property="checkType"/>
        <result column="check_model" jdbcType="INTEGER" property="checkModel"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_build_check_info
        where tenant_id = #{appBuildCheckInfo.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_build_check_info
        where id = #{id}
    </select>

    <select id="countGroupByLevelMonth" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
            resultType="java.util.Map">
        select abci.check_level                      as checkLevel,
               date_format(abci.check_time, '%Y-%m') as checkMonth,
               count(*)                              as num
        from app_build_check_info abci,
             app_tenant_project atp
        where abci.tenant_project_id = atp.id
          and atp.delete_state = 1
          and abci.delete_state = 1
        <if test="startTime != null">
            <![CDATA[
            and abci.check_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and abci.check_time <= #{endTime}
            ]]>
        </if>
        <if test="checkLevelList != null and checkLevelList.size() != 0">
            and abci.check_level in
            <foreach collection="checkLevelList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by abci.check_level, date_format(abci.check_time, '%Y-%m')
    </select>

    <select id="countGroupByTypeMonth" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
            resultType="java.util.Map">
        select abci.check_type as checkType,
               count(*)        as num
        from app_build_check_info abci
        where abci.delete_state = 1
        <if test="tenantId != null and tenantId != ''">
            and abci.tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="id != null and id != ''">
            and abci.tenant_project_id = #{id}
        </if>
        group by abci.check_type
    </select>
</mapper>
