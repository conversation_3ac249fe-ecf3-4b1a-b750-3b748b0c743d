<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.person.mapper.PersonRefDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.person.entity.PersonRefDevice">
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="person_id_" property="personId"/>
        <result column="device_id_" property="deviceId"/>
        <result column="device_code_" property="deviceCode"/>
        <result column="device_name_" property="deviceName"/>
        <result column="delete_state" property="deleteState"/>
        <result column="create_id_" property="createId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
        <result column="online_state" property="onlineState"/>
        <result column="alarm_state" property="alarmState"/>
    </resultMap>

    <!-- 设备列表查询映射结果 -->
    <resultMap id="DeviceListVOMap" type="com.easylinkin.linkappapi.person.entity.PersonRefDevice$DeviceListVO">
        <!-- PersonRefDevice基础字段 -->
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="person_id_" property="personId"/>
        <result column="device_id_" property="deviceId"/>
        <result column="device_code_" property="deviceCode"/>
        <result column="device_name_" property="deviceName"/>
        <result column="delete_state" property="deleteState"/>
        <result column="create_id_" property="createId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>

        <!-- 设备扩展字段 -->
        <result column="device_type_name" property="deviceTypeName"/>
        <result column="online_state" property="onlineState"/>
        <result column="online_state_text" property="onlineStateText"/>
        <result column="alarm_state" property="alarmState"/>

        <!-- 人员扩展字段 -->
        <result column="person_name" property="personName"/>
        <result column="person_id_card" property="personIdCard"/>

        <!-- 计算字段 -->
        <result column="is_bind" property="isBind"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询设备列表（包含绑定人员信息） -->
    <select id="selectDeviceListPage" resultMap="DeviceListVOMap">
        SELECT
            d.id AS device_id_,
            d.code AS device_code_,
            d.name AS device_name_,
            d.tenant_id AS tenant_id_,
            dt.name AS device_type_name,
            d.online_state,
            CASE
                WHEN d.online_state = 1 THEN '在线'
                ELSE '离线'
            END AS online_state_text,
            d.status as alarm_state,

            -- 绑定关系字段
            prd.id_,
            prd.person_id_,
            prd.delete_state,
            prd.create_id_,
            prd.create_time_,
            prd.modify_id_,
            prd.modify_time_,
            prd.remark_,

            -- 人员信息
            p.real_name AS person_name,
            p.ids_no AS person_id_card,

            -- 计算字段
            CASE
                WHEN prd.id_ IS NOT NULL THEN 1
                ELSE 0
            END AS is_bind,
            (SELECT MAX(modify_time_) FROM rail_person_ref_device WHERE device_id_ = d.id ) AS update_time

        FROM linkapp_device d
        INNER JOIN linkapp_device_unit du ON d.device_unit_id = du.id
        INNER JOIN linkapp_device_type dt ON du.device_type_id = dt.id
        LEFT JOIN rail_person_ref_device prd ON d.id = prd.device_id_ AND prd.delete_state = 0
        LEFT JOIN rail_linkapp_roster_personnel p ON prd.person_id_ = p.id

        <where>
            d.delete_state = 1
            AND d.tenant_id = #{param.tenantId}
            AND dt.name IN ('手持终端', '智能安全帽')

            <if test="param.deviceKeyword != null and param.deviceKeyword != ''">
                AND (
                    d.code LIKE CONCAT('%', #{param.deviceKeyword}, '%')
                    OR d.name LIKE CONCAT('%', #{param.deviceKeyword}, '%')
                )
            </if>

            <if test="param.personKeyword != null and param.personKeyword != ''">
                AND p.real_name LIKE CONCAT('%', #{param.personKeyword}, '%')
            </if>
        </where>

        ORDER BY d.create_time DESC
    </select>

    <select id="selectPersonList" resultMap="BaseResultMap">
        SELECT
            prd.id_,
            prd.person_id_,
            prd.device_id_,
            prd.device_code_,
            prd.device_name_,
            prd.tenant_id_,
            prd.delete_state,
            prd.create_id_,
            prd.create_time_,
            prd.modify_id_,
            prd.modify_time_,
            prd.remark_,
            d.online_state,
            d.status as alarm_state
        FROM linkapp_device d
            INNER JOIN rail_person_ref_device prd ON prd.device_id_ = d.id
        WHERE prd.person_id_ IS NOT NULL
          AND prd.delete_state = 0
          AND prd.tenant_id_ = #{tenantId}
          AND d.delete_state = 1
          AND d.tenant_id = #{tenantId}
        GROUP BY prd.person_id_
        ORDER BY prd.create_time_ DESC
    </select>

</mapper>