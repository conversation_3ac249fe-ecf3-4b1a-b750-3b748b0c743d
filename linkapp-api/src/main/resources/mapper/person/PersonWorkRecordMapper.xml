<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.person.mapper.PersonWorkRecordMapper">

    <!-- 轨迹列表查询映射结果 -->
    <resultMap id="TrackListVOMap" type="com.easylinkin.linkappapi.person.entity.PersonWorkRecord$TrackListVO">
        <result column="work_record_id" property="workRecordId"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_code" property="deviceCode"/>
        <result column="device_name" property="deviceName"/>
        <result column="person_id" property="personId"/>
        <result column="person_name" property="personName"/>
        <result column="person_phone" property="personPhone"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="work_time" property="workTime"/>
    </resultMap>

    <!-- 分页查询轨迹列表 -->
    <select id="selectTrackListPage" resultMap="TrackListVOMap">
        SELECT
            pwr.id_ as work_record_id,
            prd.device_id_ as device_id,
            prd.device_code_ as device_code,
            prd.device_name_ as device_name,
            pwr.person_id_ as person_id,
            rp.real_name as person_name,
            rp.link_phone as person_phone,
            pwr.start_time_ as start_time,
            pwr.end_time_ as end_time,
            pwr.work_time_ as work_time

        FROM rail_person_work_record pwr
        INNER JOIN rail_person_ref_device prd ON pwr.person_id_ = prd.person_id_ AND prd.delete_state = 0
        INNER JOIN linkapp_device d ON prd.device_id_ = d.id AND d.delete_state = 1
        INNER JOIN rail_linkapp_roster_personnel rp ON pwr.person_id_ = rp.id

        <where>
            pwr.tenant_id_ = #{param.tenantId}

            <if test="param.keyword != null and param.keyword != ''">
                AND (
                    prd.device_code_ LIKE CONCAT('%', #{param.keyword}, '%')
                    OR prd.device_name_ LIKE CONCAT('%', #{param.keyword}, '%')
                    OR rp.real_name LIKE CONCAT('%', #{param.keyword}, '%')
                    OR rp.link_phone LIKE CONCAT('%', #{param.keyword}, '%')
                )
            </if>

            <if test="param.startTime != null">
                AND pwr.start_time_ >= #{param.startTime}
            </if>

            <if test="param.endTime != null">
                AND (pwr.end_time_ IS NULL OR pwr.end_time_  <![CDATA[<=]]>  #{param.endTime})
            </if>

            <if test="param.personId != null">
                AND pwr.person_id_ = #{param.personId}
            </if>
        </where>

        ORDER BY pwr.start_time_ DESC
    </select>

</mapper>