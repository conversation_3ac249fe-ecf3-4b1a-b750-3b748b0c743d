<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.towerCrane.mapper.TowerCraneCircleWorkRecordMapper">
    <resultMap id="TowerCraneCircleWorkRecordResultMap" type="com.easylinkin.linkappapi.towerCrane.entity.TowerCraneCircleWorkRecord">
        <id property="id" column="id_" />
        <result property="tenantId" column="tenant_id_" />
        <result property="mechanicalId" column="mechanical_id_" />
        <result property="deviceId" column="device_id_" />
        <result property="deviceCode" column="device_code_" />
        <result property="deviceName" column="device_name_" />
        <result property="startTime" column="start_time_" />
        <result property="endTime" column="end_time_" />
        <result property="maxWindSpeed" column="max_wind_speed_" />
        <result property="maxDepth" column="max_depth_" />
        <result property="maxHeight" column="max_height_" />
        <result property="maxRange" column="max_range_" />
        <result property="maxElevation" column="max_elevation_" />
        <result property="maxTravel" column="max_travel_" />
        <result property="maxWeight" column="max_weight_" />
        <result property="maxTorque" column="max_torque_" />
        <result property="maxTorquePercentage" column="max_torque_percentage_" />
        <result property="maxRotation" column="max_rotation_" />
        <result property="runtime" column="runtime_" />
        <result property="uploadTime" column="upload_time_" />
        <result property="createId" column="create_id_" />
        <result property="createTime" column="create_time_" />
        <result property="modifyId" column="modify_id_" />
        <result property="modifyTime" column="modify_time_" />
        <result property="remark" column="remark_" />
    </resultMap>
    <sql id="Base_Column_List">
        id_,
        tenant_id_,
        mechanical_id_,
        device_id_,
        device_code_,
        device_name_,
        start_time_,
        end_time_,
        max_wind_speed_,
        max_depth_,
        max_height_,
        max_range_,
        max_elevation_,
        max_travel_,
        max_weight_,
        max_torque_,
        max_torque_percentage_,
        max_rotation_,
        runtime_,
        upload_time_,
        create_id_,
        create_time_,
        modify_id_,
        modify_time_,
        remark_
    </sql>
    <select id="findPage" resultMap="TowerCraneCircleWorkRecordResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM rail_tower_crane_work_record a
        WHERE 1=1
        <if test="customQueryParams.tenantId!= null and customQueryParams.tenantId != ''">
            AND a.tenant_id_ = #{customQueryParams.tenantId}
        </if>
        <if test="customQueryParams.mechanicalId!= null and customQueryParams.mechanicalId != ''">
            AND a.mechanical_id_ = #{customQueryParams.mechanicalId}
        </if>
        <if test="customQueryParams.queryStartTime!= null and customQueryParams.queryStartTime != ''">
            AND a.upload_time_ <![CDATA[>=]]> #{customQueryParams.queryStartTime}
        </if>
        <if test="customQueryParams.queryEndTime!= null and customQueryParams.queryEndTime != ''">
            AND a.upload_time_ <![CDATA[<=]]> #{customQueryParams.queryEndTime}
        </if>
        ORDER BY a.upload_time_ DESC
    </select>
</mapper>