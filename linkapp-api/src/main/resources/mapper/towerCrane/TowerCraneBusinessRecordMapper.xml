<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.towerCrane.mapper.TowerCraneBusinessRecordMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.towerCrane.entity.TowerCraneBusinessRecord">
        <id property="id" column="id_" />
        <result property="tenantId" column="tenant_id_" />
        <result property="mechanicalId" column="mechanical_id_" />
        <result property="deviceId" column="device_id_" />
        <result property="deviceCode" column="device_code_" />
        <result property="deviceName" column="device_name_" />
        <result property="weight" column="weight_" />
        <result property="alarmWeight" column="alarm_weight_" />
        <result property="windSpeed" column="wind_speed_" />
        <result property="alarmWindSpeed" column="alarm_wind_speed_" />
        <result property="range" column="range_" />
        <result property="rotation" column="rotation_" />
        <result property="slide" column="slide_" />
        <result property="alarmSlide" column="alarm_slide_" />
        <result property="torquePercentage" column="torque_percentage_" />
        <result property="alarmTorquePercentage" column="alarm_torque_percentage_" />
        <result property="horizontal" column="horizontal_" />
        <result property="alarmHorizontal" column="alarm_horizontal_" />
        <result property="vertical" column="vertical_" />
        <result property="alarmVertical" column="alarm_vertical_" />
        <result property="depth" column="depth_" />
        <result property="height" column="height_" />
        <result property="phi" column="phi_" />
        <result property="cycleCount" column="cycle_count_" />
        <result property="cycleStatus" column="cycle_status_" />
        <result property="allTime" column="all_time_" />
        <result property="createId" column="create_id_" />
        <result property="createTime" column="create_time_" />
        <result property="modifyId" column="modify_id_" />
        <result property="modifyTime" column="modify_time_" />
        <result property="remark" column="remark_" />
        <result property="alarmState" column="alarm_state_" />
        <result property="uploadTime" column="upload_time_" />
    </resultMap>
    <sql id="Base_Column_List">
        a.id_,
        a.tenant_id_,
        a.mechanical_id_,
        a.device_id_,
        a.device_code_,
        a.device_name_,
        a.weight_,
        a.alarm_weight_,
        a.wind_speed_,
        a.alarm_wind_speed_,
        a.range_,
        a.rotation_,
        a.slide_,
        a.alarm_slide_,
        a.torque_percentage_,
        a.alarm_torque_percentage_,
        a.horizontal_,
        a.alarm_horizontal_,
        a.vertical_,
        a.alarm_vertical_,
        a.depth_,
        a.height_,
        a.phi_,
        a.cycle_count_,
        a.cycle_status_,
        a.all_time_,
        a.create_id_,
        a.create_time_,
        a.modify_id_,
        a.modify_time_,
        a.remark_,
        a.alarm_state_,
        a.upload_time_
    </sql>

    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM rail_tower_crane_business_record a
        WHERE 1=1
        <if test="customQueryParams.tenantId!= null and customQueryParams.tenantId != ''">
            AND a.tenant_id_ = #{customQueryParams.tenantId}
        </if>
        <if test="customQueryParams.mechanicalId!= null and customQueryParams.mechanicalId != ''">
            AND a.mechanical_id_ = #{customQueryParams.mechanicalId}
        </if>
        <if test="customQueryParams.startTime!= null and customQueryParams.startTime != ''">
            AND a.upload_time_ <![CDATA[>=]]> #{customQueryParams.startTime}
        </if>
        <if test="customQueryParams.endTime!= null and customQueryParams.endTime != ''">
            AND a.upload_time_ <![CDATA[<=]]> #{customQueryParams.endTime}
        </if>
        <if test="customQueryParams.keyword!= null and customQueryParams.keyword != ''">
            AND (a.device_code_ LIKE CONCAT('%', #{customQueryParams.keyword}, '%') OR a.device_name_ LIKE CONCAT('%', #{customQueryParams.keyword}, '%'))
        </if>
        ORDER BY a.upload_time_ DESC
    </select>

    <select id="sumWeightByDay" resultType="java.lang.Double">
        SELECT
            COALESCE(SUM(a.max_weight_), 0) AS weight_sum
        FROM rail_tower_crane_work_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
        <if test="day!= null">
            AND DATE_FORMAT(a.upload_time_, '%Y-%m-%d') = DATE_FORMAT(#{day}, '%Y-%m-%d')
        </if>
    </select>

    <select id="countMinCycleCountByDay" resultType="java.lang.Long">
        SELECT
             a.cycle_count_
        FROM rail_tower_crane_business_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
        <if test="day!= null">
            AND DATE_FORMAT(a.upload_time_, '%Y-%m-%d') = DATE_FORMAT(#{day}, '%Y-%m-%d')
        </if>
        ORDER BY a.upload_time_ DESC
        LIMIT 1
    </select>

    <select id="countMaxCycleCountByDay" resultType="java.lang.Long">
        SELECT
        a.cycle_count_
        FROM rail_tower_crane_business_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
        <if test="day!= null">
            AND DATE_FORMAT(a.upload_time_, '%Y-%m-%d') =  DATE_FORMAT(#{day}, '%Y-%m-%d')
        </if>
        ORDER BY a.upload_time_ ASC
        LIMIT 1
    </select>

    <select id="getTotalWeight" resultType="java.lang.Double">
        SELECT
            COALESCE(SUM(a.max_weight_), 0) AS total_weight
        FROM rail_tower_crane_work_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
    </select>

    <select id="getTotalCycleCount" resultType="java.lang.Long">
        SELECT
            COALESCE(MAX(a.cycle_count_), 0) AS total_cycle_count
        FROM rail_tower_crane_business_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
    </select>

    <select id="countMinAllTimeByDay" resultType="java.lang.Double">
        SELECT
        a.all_time_
        FROM rail_tower_crane_business_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
        <if test="day!= null">
            AND DATE_FORMAT(a.upload_time_, '%Y-%m-%d') = DATE_FORMAT(#{day}, '%Y-%m-%d')
        </if>
        ORDER BY a.upload_time_ DESC
        LIMIT 1
    </select>

    <select id="countMaxAllTimeByDay" resultType="java.lang.Double">
        SELECT
        a.all_time_
        FROM rail_tower_crane_business_record a
        WHERE 1=1
        <if test="tenantId!= null and tenantId != ''">
            AND a.tenant_id_ = #{tenantId}
        </if>
        <if test="mechanicalId!= null and mechanicalId != ''">
            AND a.mechanical_id_ = #{mechanicalId}
        </if>
        <if test="day!= null">
            AND DATE_FORMAT(a.upload_time_, '%Y-%m-%d') =  DATE_FORMAT(#{day}, '%Y-%m-%d')
        </if>
        ORDER BY a.upload_time_ ASC
        LIMIT 1
    </select>

</mapper>