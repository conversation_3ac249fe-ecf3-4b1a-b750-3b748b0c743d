<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.jkmonior.mapper.JkMonitorItemConfigMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.jkmonior.entity.JkMonitorItemConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="parent_ids" jdbcType="VARCHAR" property="parentIds" />
    <result column="x_axis" jdbcType="DOUBLE" property="xAxis" />
    <result column="y_axis" jdbcType="DOUBLE" property="yAxis" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_jk_monitor_item_config where tenant_id = #{appJkMonitorItemConfig.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_jk_monitor_item_config where id = #{id}
    </select>


</mapper>
