<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.jkmonior.mapper.JkMonitorConfigMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.jkmonior.entity.JkMonitorConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="source_provider_name" jdbcType="VARCHAR" property="sourceProviderName" />
    <result column="item_monitor_url" jdbcType="VARCHAR" property="itemMonitorUrl" />
    <result column="data_monitor_url" jdbcType="VARCHAR" property="dataMonitorUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="craft_video_name" jdbcType="VARCHAR" property="craftVideoName" />
    <result column="craft_video_url" jdbcType="VARCHAR" property="craftVideoUrl" />
    <result column="site_video_device_id" jdbcType="VARCHAR" property="siteVideoDeviceId" />
    <result column="jk_img" jdbcType="VARCHAR" property="jkImg" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_jk_monitor_config where tenant_id = #{appJkMonitorConfig.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_jk_monitor_config where id = #{id}
    </select>


</mapper>
