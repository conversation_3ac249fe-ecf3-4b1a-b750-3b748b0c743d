<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.deviceunit.mapper.DeviceUnitMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="DeviceUnitWithAttrs" type="com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit">
    <id column="id" property="id"/>
    <result column="company_id" property="companyId"/>
    <result column="name" property="name"/>
    <result column="device_type_id" property="deviceTypeId"/>
    <result column="code" property="code"/>
    <result column="remark" property="remark"/>
    <result column="icon" property="icon"/>
    <result column="identification" property="identification"/>
    <result column="device_life" property="deviceLife"/>
    <result column="repair_cycle" property="repairCycle"/>
    <result column="offline_time" property="offlineTime"/>
    <result column="version" property="version"/>
    <result column="physics_model" property="physicsModel"/>
    <collection property="deviceType" column="device_type_id" select="getDeviceTypeById" ofType="com.easylinkin.linkappapi.devicetype.entity.DeviceType">
    </collection>
    <collection property="attributeList" column="id" select="getAttrsById" ofType="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute">
    </collection>
    <collection property="deviceServiceList" column="id" select="getServicesById" ofType="com.easylinkin.linkappapi.deviceservice.entity.DeviceServices">
    </collection>

  </resultMap>


  <resultMap id="DeviceUnitMap" type="com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit">
    <id column="id" property="id"/>
    <result column="company_id" property="companyId"/>
    <result column="name" property="name"/>
    <result column="device_type_id" property="deviceTypeId"/>
    <result column="code" property="code"/>
    <result column="remark" property="remark"/>
    <result column="icon" property="icon"/>
    <result column="identification" property="identification"/>
    <result column="device_life" property="deviceLife"/>
    <result column="repair_cycle" property="repairCycle"/>
    <result column="offline_time" property="offlineTime"/>
    <result column="version" property="version"/>
    <result column="physicsModel" property="physicsModel"/>
  </resultMap>

  <resultMap id="getAttrsByIdMap" type="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="identifier" property="identifier"/>
    <result column="name" property="name"/>
    <result column="unit" property="unit"/>
    <result column="ico_path" property="icoPath"/>
    <result column="type" property="type"/>
    <result column="specs" property="specs"/>
    <result column="sort_no" property="sortNo"/>
    <result column="description" property="description"/>
    <result column="is_show" property="isShow"/>
    <result column="expression" property="expression"/>
    <result column="version" property="version"/>
    <result column="dict_code" property="dictCode"/>
    <collection property="childAttributeList" column="id" select="getAttrsNodeById" ofType="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute">
    </collection>
  </resultMap>


  <resultMap id="getSerivceByIdMap" type="com.easylinkin.linkappapi.deviceservice.entity.DeviceServices">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="identifier" property="identifier"/>
    <result column="name" property="name"/>
    <result column="type" property="type"/>
    <result column="version" property="version"/>
    <result column="sort_no" property="sortNo"/>
    <collection property="paramList" column="id" select="getParmsById" ofType="com.easylinkin.linkappapi.deviceparm.entity.DeviceParm">
    </collection>
  </resultMap>

  <resultMap id="getParmsByIdMap" type="com.easylinkin.linkappapi.deviceparm.entity.DeviceParm">
    <id column="id" property="id"/>
    <result column="device_service_id" property="deviceServiceId"/>
    <result column="identifier" property="identifier"/>
    <result column="name" property="name"/>
    <result column="value" property="value"/>
    <result column="unit" property="unit"/>
    <result column="specs" property="specs"/>
    <result column="sort_no" property="sortNo"/>
    <result column="version" property="version"/>
    <result column="description" property="description"/>
    <collection property="childParamList" column="id" select="getParmsNodeById" ofType="com.easylinkin.linkappapi.deviceparm.entity.DeviceParm">
    </collection>
  </resultMap>

  <select id="getDeviceTypeById" resultType="com.easylinkin.linkappapi.devicetype.entity.DeviceType">
    select * from linkapp_device_type where id = #{deviceTypeId}
  </select>

  <select id="getAttrsById" resultMap="getAttrsByIdMap">
    select * from linkapp_device_attribute where device_unit_id = #{id} order by sort_no asc
  </select>

  <select id="getAttrsNodeById" resultMap="getAttrsByIdMap">
    select * from linkapp_device_attribute where parent_id = #{id} order by sort_no asc
  </select>

  <select id="getServicesById" resultMap="getSerivceByIdMap">
    select * from linkapp_device_service where device_unit_id = #{id} order by sort_no asc
  </select>

  <select id="getParmsById" resultMap="getParmsByIdMap">
    select * from linkapp_device_parm where device_service_id = #{id} order by sort_no asc
  </select>
  <select id="getParmsNodeById" resultMap="getParmsByIdMap">
    select * from linkapp_device_parm where parent_id = #{id} order by sort_no asc
  </select>

  <select id="getDeviceUnitWithAttrs" resultMap="DeviceUnitWithAttrs">
    select * from linkapp_device_unit
    <where>
      <if test="id!=null and id!=''">
        AND id = #{id}
      </if>
      <if test="code!=null and code!=''">
        AND code = #{code}
      </if>
      <if test="deviceTypeId!=null and deviceTypeId!=''">
        AND device_type_id = #{deviceTypeId}
      </if>
      <if test="version!=null and version!=''">
        AND version = #{version}
      </if>
    </where>
  </select>

  <select id="selectDeviceUnits" resultMap="DeviceUnitWithAttrs">
    select * from linkapp_device_unit
    <where>
      <if test="deviceUnit.id!=null and deviceUnit.id!=''">
        AND id = #{deviceUnit.id}
      </if>
      <if test="deviceUnit.code!=null and deviceUnit.code!=''">
        AND code LIKE CONCAT('%',#{deviceUnit.code},'%')
      </if>
      <if test="deviceUnit.name!=null and deviceUnit.name!=''">
        AND name LIKE CONCAT('%',#{deviceUnit.name},'%')
      </if>
      <if test="deviceUnit.deviceTypeId!=null and deviceUnit.deviceTypeId!=''">
        AND device_type_id = #{deviceUnit.deviceTypeId}
      </if>
      <if test="deviceUnit.deviceTypeName!=null and deviceUnit.deviceTypeName!=''">
        AND device_type_name LIKE CONCAT('%',#{deviceUnit.deviceTypeName},'%')
      </if>
      <if test="deviceUnit.version!=null and deviceUnit.version!=''">
        AND version = #{deviceUnit.version}
      </if>
      <if test="deviceUnit.deviceUnitIds != null and deviceUnit.deviceUnitIds != ''">
        and id in
        <foreach collection="deviceUnit.deviceUnitIds" item="item" open="(" separator="," close=")">
	     #{item}
	    </foreach>
      </if>
      	and delete_state = 1
    </where>
    ORDER BY code, create_time desc
  </select>

  <select id="selectDeviceUnitsLatestVersion" resultMap="DeviceUnitWithAttrs">
    select ldu.* from linkapp_device_unit ldu
    INNER JOIN (
    SELECT
    code,
    MAX(create_time) create_time2
    FROM
    linkapp_device_unit
    GROUP BY
    code
    ) ldu2 ON ldu.code = ldu2.code and ldu.create_time = ldu2.create_time2
    <where>
      <if test="deviceUnit.id!=null and deviceUnit.id!=''">
        AND ldu.id = #{deviceUnit.id}
      </if>
      <if test="deviceUnit.code!=null and deviceUnit.code!=''">
        AND ldu.code LIKE CONCAT('%',#{deviceUnit.code},'%')
      </if>
      <if test="deviceUnit.name!=null and deviceUnit.name!=''">
        AND ldu.name LIKE CONCAT('%',#{deviceUnit.name},'%')
      </if>
      <if test="deviceUnit.deviceTypeId!=null and deviceUnit.deviceTypeId!=''">
        AND ldu.device_type_id = #{deviceUnit.deviceTypeId}
      </if>
      <if test="deviceUnit.deviceTypeName!=null and deviceUnit.deviceTypeName!=''">
        AND ldu.device_type_name LIKE CONCAT('%',#{deviceUnit.deviceTypeName},'%')
      </if>
      <if test="deviceUnit.version!=null and deviceUnit.version!=''">
        AND ldu.version = #{deviceUnit.version}
      </if>
      <if test="deviceUnit.deviceUnitIds != null and deviceUnit.deviceUnitIds != ''">
        and ldu.id in
        <foreach collection="deviceUnit.deviceUnitIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and ldu.delete_state = 1
    </where>
    ORDER BY ldu.code, ldu.create_time desc
  </select>

  <select id="selectApplicationDeviceUnitByDeviceType" resultMap="DeviceUnitMap">
    select * from linkapp_device_unit
    <where>
      <if test="id!=null and id!=''">
        AND id = #{id}
      </if>
      <if test="code!=null and code!=''">
        AND code LIKE CONCAT('%',#{code},'%')
      </if>
      <if test="name!=null and name!=''">
        AND name LIKE CONCAT('%',#{name},'%')
      </if>
      <if test="deviceTypeId!=null and deviceTypeId!=''">
        AND device_type_id = #{deviceTypeId}
      </if>
      <if test="version!=null and version!=''">
        AND version = #{version}
      </if>
      <if test="deviceTypeName!=null and deviceTypeName!=''">
        AND device_type_name LIKE CONCAT('%',#{deviceTypeName},'%')
      </if>
      <if test="deviceUnitIds != null and deviceUnitIds != ''">
        and id in
        <foreach collection="deviceUnitIds" item="item" open="(" separator="," close=")">
	     #{item}
	    </foreach>
      </if>
    </where>
  </select>

  <select id="selectDeviceUnit" resultMap="DeviceUnitWithAttrs">
    select * from linkapp_device_unit
    <where>
      <if test="id!=null and id!=''">
        AND id = #{id}
      </if>
      <if test="code!=null and code!=''">
        AND code = #{code}
      </if>
      <if test="version!=null and version!=''">
        AND version = #{version}
      </if>
      <if test="deviceTypeId!=null and deviceTypeId!=''">
        AND device_type_id = #{deviceTypeId}
      </if>
      <if test="deviceTypeName!=null and deviceTypeName!=''">
        AND device_type_name LIKE CONCAT('%',#{deviceTypeName},'%')
      </if>
    </where>
  </select>

  <select id="selectVisualizationConfig" resultType="string">
  	select visualization_config from linkapp_device_unit where id = #{id}
  </select>

  <select id="selectDeviceUnitVersionList" resultType="string">
  	select DISTINCT t.version FROM linkapp_device_unit t
  	<where>
  		<if test="code!=null and code!=''">
        	AND code = #{code}
        </if>
        AND t.delete_state = 1
        order by t.create_time desc
  	</where>
  </select>

  <select id="selectPhysicsModelByDeviceCode" resultType="String">
    select ldu.physics_model
    from linkapp_device ld
                 left join linkapp_device_unit ldu on ld.device_unit_id = ldu.id
    where ld.code = #{code}
      and ld.delete_state = 1
  </select>

  <select id="selectDeviceUnitByTypeNames" resultMap="DeviceUnitMap">
    select *
    from linkapp_device_unit
    where device_type_id in (
      select id
      from linkapp_device_type
      where name in
      <foreach collection="deviceTypeNames" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    )
  </select>

</mapper>
