<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.monitorDailyReport.mapper.MonitorDailyReportMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.monitorDailyReport.vo.MonitorDailyReportVo">
    <id column="id" property="id"/>
    <result column="tenant_id" property="tenantId" />
    <result column="monitor_date" property="monitorDate" />
    <result column="upload_type" property="uploadType" />
    <result column="report_name" property="reportName" />
    <result column="repoort_url" property="reportUrl" />
    <result column="upload_uid" property="uploadUid" />
    <result column="upload_time" property="uploadTime" />
    <result column="remark" property="remark" />
    <result column="is_deleted" property="isDeleted" />
    <result column="uploadUserName" property="uploadUserName" />
    <result column="tenantName" property="tenantName" />
  </resultMap>
  <sql id="monitorDailyReportMap">
    rmdr.*,
    lu.real_name as uploadUserName,
    lt.platform_project_name as tenantName
  </sql>

  <select id="selectList" resultMap="BaseResultMap">
    select <include refid="monitorDailyReportMap" />
    from rail_monitor_daily_report rmdr
    LEFT JOIN rail_linkapp_roster_personnel lu on lu.id = rmdr.upload_uid
    left join linkapp_tenant lt on rmdr.tenant_id = lt.id
    where rmdr.is_deleted = 0
   <if test="monitorDailyReportDto != null">
     <if test="monitorDailyReportDto.id != null">
       and rmdr.id = #{monitorDailyReportDto.id}
     </if>
     <if test="monitorDailyReportDto.uploadType != null">
       and rmdr.upload_type = #{monitorDailyReportDto.uploadType}
     </if>
     <if test="monitorDailyReportDto.startDate!=null">
       and rmdr.monitor_date &gt;= #{monitorDailyReportDto.startDate}
     </if>
     <if test="monitorDailyReportDto.endDate!=null">
       and rmdr.monitor_date  &lt;= #{monitorDailyReportDto.endDate}
     </if>
     <if test="monitorDailyReportDto.uploadUserName != null and monitorDailyReportDto.uploadUserName != ''">
       and lu.real_name like CONCAT('%',#{monitorDailyReportDto.uploadUserName},'%')
     </if>
   </if>
    order by rmdr.upload_time DESC
  </select>

</mapper>
