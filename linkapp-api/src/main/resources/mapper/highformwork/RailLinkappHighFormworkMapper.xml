<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.highformwork.mapper.RailLinkappHighFormworkMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.highformwork.vo.RailLinkappHighFormworkVO">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="point_id" property="pointId" />
        <result column="monitoring_point_file" property="monitoringPointFile" />
        <result column="danger" property="danger" />
        <result column="test_unit_name" property="testUnitName" />
        <result column="test_person" property="testPerson" />
        <result column="test_scheme_file" property="testSchemeFile" />
        <result column="test_person_phone" property="testPersonPhone" />
        <result column="overview" property="overview" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="pointName" property="pointName" />
    </resultMap>
    <select id="getList" resultMap="BaseResultMap">
       select a.*,b.point_name_ as pointName  from rail_linkapp_high_formwork a left join
        rail_project_point b on a.id = b.id
       <where>
           1=1
           <if test="entity.tenantId != null and entity.tenantId != '' ">
               and a.tenant_id=#{entity.tenantId}
           </if>
           <if test="entity.id != null  ">
               and a.id=#{entity.id}
           </if>
           <if test="entity.danger != null  ">
               and a.danger=#{entity.danger}
           </if>
           <if test="entity.name != null and entity.name != '' ">
               and a.name like CONCAT('%',#{entity.name},'%')
           </if>
       </where>
       order by a.create_time desc
    </select>


</mapper>