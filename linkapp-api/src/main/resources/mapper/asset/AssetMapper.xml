<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.asset.mapper.AssetMapper">
  <select id="getAsset" resultType="com.easylinkin.linkappapi.asset.entity.Asset" parameterType="com.easylinkin.linkappapi.asset.entity.Asset">
    SELECT a.id,
           a.asset_name,
           a.asset_area_id,
           a.asset_desc,
           a.modify_time,
           a.latitude,
           a.longitude,
           a.indoor_location,
           b.area_name,
           b.area_path,
           d.id         as spaceId,
           d.space_name as assetSpaceName,
           d.tenant_id  as tenantId
    FROM linkapp_asset a
           LEFT JOIN linkapp_area b ON (a.asset_area_id = b.id)
           LEFT JOIN linkapp_space d ON (b.space_id = d.id)
    <where>
      a.delete_state = 1
      <if test="asset.id != null and asset.id != ''">
        and a.id = #{asset.id}
      </if>
      <if test="asset.assetName != null and asset.assetName != ''">
        and a.asset_name LIKE CONCAT('%', #{asset.assetName}, '%')
      </if>
      <if test="asset.areaPath != null and asset.areaPath != ''">
        and (b.area_path = #{asset.areaPath} or b.area_path like concat(#{asset.areaPath},':%'))
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(asset.managerId)">
        and a.id in (select asset_id from linkapp_asset_ref_user where user_id = #{asset.managerId})
      </if>
      order by a.modify_time desc
    </where>
  </select>

  <select id="getAssets" resultType="com.easylinkin.linkappapi.asset.entity.Asset" parameterType="com.easylinkin.linkappapi.asset.entity.Asset">
    SELECT a.id,
           a.asset_name,
           a.asset_area_id,
           a.asset_desc,
           a.modify_time,
           a.latitude,
           a.longitude,
           a.indoor_location,
           b.area_name,
           b.area_path,
           d.id         as spaceId,
           d.space_name as assetSpaceName,
           d.tenant_id  as tenantId
    FROM linkapp_asset a
           LEFT JOIN linkapp_area b ON (a.asset_area_id = b.id)
           LEFT JOIN linkapp_space d ON (b.space_id = d.id)
    <where>
      a.delete_state = 1
      <if test="asset.id != null and asset.id != ''">
        and a.id = #{asset.id}
      </if>
      <if test="asset.assetName != null and asset.assetName != ''">
        and a.asset_name LIKE CONCAT('%', #{asset.assetName}, '%')
      </if>
      <if test="asset.areaPath != null and asset.areaPath != ''">
        and (b.area_path = #{asset.areaPath} or b.area_path like concat(#{asset.areaPath},':%'))
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(asset.managerId)">
        and a.id in (select asset_id from linkapp_asset_ref_user where user_id = #{asset.managerId})
      </if>
      order by a.modify_time desc
    </where>
  </select>


  <select id="getAllManagers" resultMap="managerResultMap" parameterType="String">
    SELECT IFNULL(b.nickname, b.username) as userName, b.id as userId
    FROM linkapp_user b
    where b.tenant_id = #{tenantId}
  </select>

  <resultMap id="managerResultMap" type="com.easylinkin.linkappapi.asset.entity.UserVo">
    <result column="userName" property="userName"/>
    <result column="userId" property="userId"/>
  </resultMap>

  <select id="getManagers" resultType="String" parameterType="String">
    SELECT IFNULL(b.nickname, b.username) as userName
    FROM linkapp_user b
    WHERE FIND_IN_SET(b.id, #{userId})
  </select>

  <select id="getManagersByAssertId" resultType="com.easylinkin.linkappapi.asset.entity.UserVo"
    parameterType="String">
    SELECT ifnull(b.nickname, b.username) as userName, b.id as userId
    FROM linkapp_user b
    WHERE b.id in (
      select user_id
      from linkapp_asset_ref_user
      where asset_id = #{assetId}
      )
  </select>


  <select id="getDvicePageByAssetId" resultType="com.easylinkin.linkappapi.device.entity.Device">
    SELECT a.id,
           a.code,
           a.name,
           a.online_state AS onlineState,
           a.area_path    AS areaPath,
           a.status,
           b.name         as deviceUnitName,
           b.code         as deviceUnitCode,
           b.version      as deviceUnitVersion,
           c.name         as deviceTypeName,
           d.id           AS areaId,
           d.area_name    AS areaName,
           f.id           as spaceId,
           f.space_name   as spaceName
    FROM linkapp_device a
           LEFT JOIN linkapp_device_unit b ON a.device_unit_id = b.id
           LEFT JOIN linkapp_device_type c ON b.device_type_id = c.id
           LEFT JOIN linkapp_area d ON a.area_id = d.id
           LEFT JOIN linkapp_space f ON (d.space_id = f.id)
           inner join
           (
             SELECT g.device_id as id
             FROM linkapp_asset_ref_device g
                    INNER JOIN linkapp_asset h ON g.asset_id = h.id
                    INNER JOIN linkapp_area k ON h.asset_area_id = k.id
             WHERE g.asset_id = #{adeviceVo.assetId}
             ) devInfo on a.id = devInfo.id
      where
      a.delete_state = 1
    <if test="adeviceVo.code != null and adeviceVo.code != ''">
      and a.code LIKE CONCAT(#{adeviceVo.code}, '%')
    </if>
    <if test="adeviceVo.status != null and adeviceVo.status != ''">
      and a.status = #{adeviceVo.status}
    </if>
    <if test="adeviceVo.deviceTypeId != null and adeviceVo.deviceTypeId != ''">
      and b.device_type_id = #{adeviceVo.deviceTypeId}
    </if>
    group by a.code
  </select>


  <select id="getUnCoonectDviceByAreaId" resultType="com.easylinkin.linkappapi.device.entity.Device">
    SELECT a.id           as id,
           a.code         as code,
           a.name,
           a.online_state AS onlineState,
           a.area_path,
           a.status,
           b.name         as deviceUnitName,
           b.code         as deviceUnitCode,
           b.version      as deviceUnitVersion,
           c.name         as deviceTypeName,
           d.id           as areaId,
           d.area_name    AS areaName,
           f.id           as spaceId,
           f.space_name   as spaceName
    FROM linkapp_device a
           LEFT JOIN linkapp_device_unit b ON a.device_unit_id = b.id
           LEFT JOIN linkapp_device_type c ON b.device_type_id = c.id
           LEFT JOIN linkapp_area d ON a.area_id = d.id
           LEFT JOIN linkapp_space f ON (d.space_id = f.id)
      WHERE
      a.delete_state = 1
    <if test="adeviceVo.code != null and adeviceVo.code != ''">
      AND a.code like concat('%', #{adeviceVo.code}, '%')
    </if>
    <if test="adeviceVo.status != null and adeviceVo.status != ''">
      AND a.status = #{adeviceVo.status}
    </if>
    <if test="adeviceVo.deviceTypeId != null and adeviceVo.deviceTypeId != ''">
      AND b.device_type_id = #{adeviceVo.deviceTypeId}
    </if>
    and a.id NOT IN (
      select device_id
      from linkapp_asset_ref_device
      )
    <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(adeviceVo.areaId)">
      AND d.id = #{adeviceVo.areaId}
    </if>
  </select>


  <select id="queryDayAndMonthCreates" resultType="map">
    SELECT day.daynumber, month.monthnumber
    FROM (
           SELECT count(*) daynumber
           FROM linkapp_asset c
           WHERE TO_DAYS(NOW()) - TO_DAYS(c.create_time) = 1
           ) day,
         (
           SELECT count(*) monthnumber
           FROM linkapp_asset d
           WHERE PERIOD_DIFF(date_format(now(), '%Y%m'), date_format(d.create_time, '%Y%m')) = 0
           ) month
  </select>
</mapper>
