<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.jzsmokewarning.mapper.SmokeWarningMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.jzsmokewarning.entity.SmokeWarning">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="device_code" property="deviceCode"/>
        <result column="device_name" property="deviceName"/>
        <result column="area_path" property="areaPath"/>
        <result column="cycle" property="cycle"/>
        <result column="data_count" property="dataCount"/>
        <result column="average_value" property="averageValue"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    <select id="getSmokeCollectionPage" resultType="com.easylinkin.linkappapi.jzsmokewarning.entity.SmokeWarning">
        select sw.* from smoke_warning sw
        <where>
            <if test="smokeWarning.startTime!=null">
                and sw.start_time &gt;= #{smokeWarning.startTime}
            </if>
            <if test="smokeWarning.endTime!=null">
                and sw.end_time &lt; #{smokeWarning.endTime}
            </if>
            <if test="smokeWarning.deviceCode!=null">
                and sw.device_code like CONCAT('%', #{smokeWarning.deviceCode}, '%')
            </if>
        </where>
        <choose>
            <when test="sort != null and sort.size() > 0">
                ORDER BY
                <trim suffixOverrides=",">
                    <foreach collection="sort" item="item" index="index">
                        ${item.field} ${item.sortRule},
                    </foreach>
                </trim>
            </when>
            <otherwise>
                ORDER BY sw.create_time DESC
            </otherwise>
        </choose>
    </select>
</mapper>
