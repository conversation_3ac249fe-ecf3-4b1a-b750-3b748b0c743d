<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.QualityAppraisingExcellentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.QualityAppraisingExcellent">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="imgs" property="imgs" />
        <result column="check_part_id" property="checkPartId" />
        <result column="problem_type_id" property="problemTypeId" />
        <result column="content_" property="content" />
        <result column="sub_org_type" property="subOrgType" />
        <result column="sub_org_id" property="subOrgId" />
        <result column="sub_org_name" property="subOrgName" />
        <result column="sub_group_id" property="subGroupId" />
        <result column="sub_group_name" property="subGroupName" />
        <result column="noticer_ids" property="noticerIds" />
        <result column="noticer_names" property="noticerNames" />
        <result column="appraising_time" property="appraisingTime" />
        <result column="creator" property="creator" />
        <result column="modifier" property="modifier" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_state" property="deleteState" />
    </resultMap>

    <select id="selectDtoPage" resultType="com.easylinkin.linkappapi.quality.dto.QualityAppraisingExcellentDTO">
        SELECT
            a.*,
            pt.full_name_ AS problemTypeFullName,
            qp.full_name_ AS checkPartFullName,
            u.nickname AS creatorName
        FROM
            app_quality_appraising_excellent a
            LEFT JOIN app_problem_type pt ON pt.id = a.problem_type_id
            LEFT JOIN app_quality_position qp ON qp.id = a.check_part_id
            LEFT JOIN linkapp_user u ON a.creator = u.id
        WHERE
        a.delete_state = 1
        <if test="customQueryParams.tenantId != null and customQueryParams.tenantId != ''">
            AND a.tenant_id = #{customQueryParams.tenantId}
        </if>
        <if test="customQueryParams.startTime != null">
            <![CDATA[
              AND a.create_time >= #{customQueryParams.startTime}
              ]]>
        </if>
        <if test="customQueryParams.endTime != null">
            <![CDATA[
              AND a.create_time <= #{customQueryParams.endTime}
              ]]>
        </if>
        <if test="customQueryParams.problemTypeId != null and customQueryParams.problemTypeId != ''">
            AND a.problem_type_id IN (SELECT apt.id
            FROM app_problem_type apt
            WHERE apt.id = #{customQueryParams.problemTypeId} OR
            apt.parent_id_ = #{customQueryParams.problemTypeId})
        </if>
        <if test="customQueryParams.checkPartId != null and customQueryParams.checkPartId != ''">
            AND a.check_part_id = #{customQueryParams.checkPartId}
        </if>
        <if test="customQueryParams.id != null and customQueryParams.id != ''">
            AND a.id = #{customQueryParams.id}
        </if>
        ORDER BY
          a.create_time DESC
    </select>

    <select id="selectPhotoAlbumByPage" resultType="com.easylinkin.linkappapi.quality.dto.QualityAppraisingExcellentPhotoAlbumDTO">
        SELECT
          a.id,
          a.imgs,
          pt.full_name_ AS problemTypeFullName,
          qp.full_name_ AS checkPartFullName,
          a.appraising_time AS appraisingTime,
          a.sub_org_name AS subOrgName,
          a.sub_group_name AS subGroupName,
          a.content_ AS content,
          a.noticer_names AS noticerNames,
          u.nickname AS creator,
          a.create_time AS createTime,
          tenant.platform_project_name AS projectName
        FROM
          app_quality_appraising_excellent a
          LEFT JOIN app_problem_type pt ON pt.id = a.problem_type_id
          LEFT JOIN app_quality_position qp ON qp.id = a.check_part_id
          LEFT JOIN linkapp_user u ON a.creator = u.id
          LEFT JOIN linkapp_tenant tenant ON tenant.id = a.tenant_id
        WHERE
          a.delete_state = 1
          and tenant.id is not null
          AND a.imgs IS NOT NULL
        <if test="photoAlbumQueryDTO.tenantId != null and photoAlbumQueryDTO.tenantId != ''">
            AND a.tenant_id = #{photoAlbumQueryDTO.tenantId}
        </if>
        <if test="photoAlbumQueryDTO.createId != null and photoAlbumQueryDTO.createId != ''">
            AND a.creator = #{photoAlbumQueryDTO.createId}
        </if>
        <if test="photoAlbumQueryDTO.subOrgId != null and photoAlbumQueryDTO.subOrgId != ''">
            AND a.sub_org_id = #{photoAlbumQueryDTO.subOrgId}
        </if>
        <if test="photoAlbumQueryDTO.subGroupId != null and photoAlbumQueryDTO.subGroupId != ''">
            AND a.sub_group_id = #{photoAlbumQueryDTO.subGroupId}
        </if>
        <if test="photoAlbumQueryDTO.problemTypeId != null and photoAlbumQueryDTO.problemTypeId != ''">
            AND a.problem_type_id IN (SELECT apt.id
            FROM app_problem_type apt
            WHERE apt.id = #{photoAlbumQueryDTO.problemTypeId} OR
            apt.parent_id_ = #{photoAlbumQueryDTO.problemTypeId})
        </if>
        <if test="photoAlbumQueryDTO.checkPartId != null and photoAlbumQueryDTO.checkPartId != ''">
            AND a.check_part_id = #{photoAlbumQueryDTO.checkPartId}
        </if>
        <if test="photoAlbumQueryDTO.startTime != null ">
            and a.create_time &gt;= #{photoAlbumQueryDTO.startTime}
        </if>
        <if test="photoAlbumQueryDTO.endTime != null">
            and a.create_time &lt;= #{photoAlbumQueryDTO.endTime}
        </if>
        <if test="photoAlbumQueryDTO.ids != null and photoAlbumQueryDTO.ids.size()>0">
            AND a.id IN
            <foreach item="item" index="index" collection="photoAlbumQueryDTO.ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY a.create_time
        <choose>
            <when test="photoAlbumQueryDTO.sortAsc">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>
</mapper>
