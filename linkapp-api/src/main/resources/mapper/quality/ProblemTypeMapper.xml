<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.ProblemTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.ProblemType">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="parent_id_" property="parentId" />
        <result column="full_id_" property="fullId" />
        <result column="full_name_" property="fullName" />
        <result column="name_" property="name" />
        <result column="code_" property="code" />
        <result column="level_" property="level" />
        <result column="is_default_" property="isDefault" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="src_id_" property="srcId" />
    </resultMap>

    <update id="updateByTypeNameChange">
        update app_problem_type
        set full_name_ = concat(#{newNameBefore,jdbcType=VARCHAR}, substr(full_name_, (#{len} + 1)))
        where parent_id_ = #{parentId} and tenant_id_ is null
    </update>

    <update id="updateOfSync">
        update app_problem_type apt inner join app_problem_type apt2 on apt.parent_id_ = apt2.src_id_ and
                                                                        apt.tenant_id_ = apt2.tenant_id_
        set apt.parent_id_ = apt2.id
        where apt.id in
        <foreach collection="needUpdateIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>
