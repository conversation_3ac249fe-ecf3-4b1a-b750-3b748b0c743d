<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.ActualMeasureValueMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.ActualMeasureValue">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="info_id" jdbcType="BIGINT" property="infoId" />
    <result column="details_id" jdbcType="BIGINT" property="detailsId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="benchmark_value" jdbcType="VARCHAR" property="benchmarkValue" />
    <result column="group_no" jdbcType="VARCHAR" property="groupNo" />
    <result column="actual_value" jdbcType="VARCHAR" property="actualValue" />
    <result column="actual_state" jdbcType="VARCHAR" property="actualState" />
    <result column="sort_no" jdbcType="INTEGER" property="sortNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
  </resultMap>
  <resultMap id="ValueDtoResultMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.quality.dto.ActualMeasureValueDTO">
    <association property="actualMeasureItem" column="item_id" select="com.easylinkin.linkappapi.quality.mapper.ActualMeasureItemMapper.getOneById" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_quality_actual_measure_value where tenant_id = #{appQualityActualMeasureValue.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_quality_actual_measure_value where id = #{id}
    </select>

  <select id="selectValueListByDetailsId" parameterType="long" resultMap="ValueDtoResultMap">
    select aqamv.*
    from app_quality_actual_measure_value aqamv
    where aqamv.delete_state = 1
      and aqamv.details_id = #{detailsId}
  </select>
</mapper>
