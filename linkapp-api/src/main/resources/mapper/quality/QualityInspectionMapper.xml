<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.QualityInspectionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.QualityInspection">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="url_" property="url" />
        <result column="position_id_" property="positionId" />
        <result column="problem_id_" property="problemId" />
        <result column="explain_" property="explain" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <resultMap id="BaseResultMapDTO" type="com.easylinkin.linkappapi.quality.dto.QualityInspectionDTO" extends="BaseResultMap">
      <result column="nickname" property="nickname" />
      <result column="phone" property="phone" />
      <result column="positionName" property="positionName" />
      <result column="problemName" property="problemName" />
      <result column="level" property="level" />
    </resultMap>

  <select id="queryListByPage" resultMap="BaseResultMapDTO">
        SELECT
          a.*,
          d.nickname,
          d.phone
        FROM
          app_quality_inspection a
          LEFT JOIN app_problem b ON a.problem_id_ = b.id
          LEFT JOIN app_problem_type c ON b.problem_type_id_ = c.id
          LEFT JOIN linkapp_user d ON a.creator_id_ = d.id
        <where>
            <if test="qualityInspectionDTO.tenantIds != null and qualityInspectionDTO.tenantIds.size() != 0">
                and a.tenant_id_
                in
                <foreach collection="qualityInspectionDTO.tenantIds" item="item" close=")" separator="," open="(">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
          <if test="qualityInspectionDTO.tenantId != null">
              and a.tenant_id_ = #{qualityInspectionDTO.tenantId}
          </if>
          <if test="qualityInspectionDTO.startTime != null">
              and a.create_time_ &gt;=  #{qualityInspectionDTO.startTime}
          </if>
          <if test="qualityInspectionDTO.endTime != null">
              and a.create_time_ &lt;=  #{qualityInspectionDTO.endTime}
          </if>
          <if test="qualityInspectionDTO.queryType == 1">
              and a.creator_id_ = #{qualityInspectionDTO.creatorId}
          </if>
          <if test="qualityInspectionDTO.positionId != null">
             and a.position_id_ = #{qualityInspectionDTO.positionId}
          </if>
          <if test="qualityInspectionDTO.problemTypeId != null">
             and (c.id = #{qualityInspectionDTO.problemTypeId}
                  or
                  c.parent_id_ = #{qualityInspectionDTO.problemTypeId})
          </if>
        </where>
        ORDER BY
          a.modify_time_ DESC
  </select>
  <select id="findById" resultMap="BaseResultMapDTO">
    SELECT
      a.*,
      b.nickname,
      b.phone,
      c.full_name_ problemName,
      d.full_name_ positionName,
      c.level_ level
    FROM
      app_quality_inspection a
      LEFT JOIN linkapp_user b ON a.creator_id_ = b.id
      LEFT JOIN app_problem c ON a.problem_id_ = c.id
      LEFT JOIN app_quality_position d ON a.position_id_ = d.id
    WHERE
      a.id = #{id}
  </select>

    <select id="countByTenantStatue" parameterType="ArrayList" resultType="java.util.Map">
        select aqi.tenant_id_ as tenantId,
               5              as type,
               count(*)       as num
        from app_quality_inspection aqi
        where 1 = 1
        <if test="tenantIds != null and tenantIds.size() != 0">
            and aqi.tenant_id_ in
            <foreach collection="tenantIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by aqi.tenant_id_
        union all
        (
        select aqqi.tenant_id,
               aqqi.question_state as type,
               count(*)            as num
        from app_quality_question_info aqqi
        where aqqi.delete_state = 1
        <if test="tenantIds != null and tenantIds.size() != 0">
            and aqqi.tenant_id in
            <foreach collection="tenantIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by aqqi.tenant_id,
                 aqqi.question_state
        )
    </select>
</mapper>
