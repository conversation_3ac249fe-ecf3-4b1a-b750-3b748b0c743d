<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.ActualMeasureInfoMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.ActualMeasureInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="position_id" jdbcType="INTEGER" property="positionId"/>
        <result column="measure_time" jdbcType="TIMESTAMP" property="measureTime"/>
        <result column="sub_org_id" jdbcType="VARCHAR" property="subOrgId"/>
        <result column="sub_org_name" jdbcType="VARCHAR" property="subOrgName"/>
        <result column="sub_group_id" jdbcType="VARCHAR" property="subGroupId"/>
        <result column="sub_group_name" jdbcType="VARCHAR" property="subGroupName"/>
        <result column="sub_org_type" jdbcType="VARCHAR" property="subOrgType"/>
        <result column="actual_org_type" jdbcType="INTEGER" property="actualOrgType"/>
        <result column="detail_memo" jdbcType="VARCHAR" property="detailMemo"/>
        <result column="storage_type" jdbcType="INTEGER" property="storageType"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>
    <resultMap id="InfoDtoResultMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.quality.dto.ActualMeasureInfoDTO">
        <association property="subOrgInfo" column="sub_org_id"
                     select="com.easylinkin.linkappapi.lobar.mapper.LaborCompanyMapper.selectById"/>
        <association property="subGroupInfo" column="sub_group_id"
                     select="com.easylinkin.linkappapi.lobar.mapper.GroupMapper.selectById"/>
        <association property="createUserInfo" column="creator"
                     select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
        <association property="position" column="position_id"
                     select="com.easylinkin.linkappapi.quality.mapper.QualityPositionMapper.selectById"/>
        <collection property="detailsList" column="id"
                    select="com.easylinkin.linkappapi.quality.mapper.ActualMeasureDetailsMapper.selectDetailsDtoByInfoId"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_quality_actual_measure_info
        where tenant_id = #{appQualityActualMeasureInfo.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_quality_actual_measure_info
        where id = #{id}
    </select>

    <select id="selectCountByPositionId" parameterType="long" resultType="int">
        select count(*)
        from app_quality_actual_measure_info aqami
        where aqami.delete_state = 1
          and aqami.storage_type = 2
        <if test="positionId != null">
            and aqami.position_id = #{positionId}
        </if>
    </select>

    <sql id="selectDtoPageWhere">
        <if test="actualMeasureInfoVo.storageType != null">
            and aqami.storage_type = #{actualMeasureInfoVo.storageType}
        </if>
        <if test="actualMeasureInfoVo.tenantId != null and actualMeasureInfoVo.tenantId != ''">
            and aqami.tenant_id = #{actualMeasureInfoVo.tenantId}
        </if>
        <if test="actualMeasureInfoVo.startTime != null">
            <![CDATA[
            and aqami.measure_time >= #{actualMeasureInfoVo.startTime}
            ]]>
        </if>
        <if test="actualMeasureInfoVo.endTime != null">
            <![CDATA[
            and aqami.measure_time <= #{actualMeasureInfoVo.endTime}
            ]]>
        </if>
        <if test="actualMeasureInfoVo.paramKey != null and actualMeasureInfoVo.paramKey != ''">
            <![CDATA[
            and aqami.creator in (select lu.id from linkapp_user lu where lu.tenant_id = #{actualMeasureInfoVo.tenantId} and lu.nickname like concat('%',
                #{actualMeasureInfoVo.paramKey}, '%'))
            ]]>
        </if>
        <if test="actualMeasureInfoVo.creator != null and actualMeasureInfoVo.creator != ''">
            and aqami.creator =
                #{actualMeasureInfoVo.creator}
        </if>
        <if test="actualMeasureInfoVo.typeId != null">
            and aqamd.type_id in (select t.id from app_quality_actual_measure_type t where t.delete_state = 1 and t.full_id like concat('%/',
                #{actualMeasureInfoVo.typeId}, '/%'))
        </if>
        <if test="actualMeasureInfoVo.positionId != null">
            and aqami.position_id in (select aqp.id
                from app_quality_position aqp
                where aqp.full_id_ like concat('%/',
                #{actualMeasureInfoVo.positionId}, '/%'))
        </if>
        <if test="actualMeasureInfoVo.subName != null and actualMeasureInfoVo.subName != ''">
            and (aqami.sub_org_name like concat('%',
                #{actualMeasureInfoVo.subName}, '%') or
                aqami.sub_group_name like concat('%',
                #{actualMeasureInfoVo.subName}, '%'))
        </if>
        <if test="actualMeasureInfoVo.actualOrgType != null and actualMeasureInfoVo.actualOrgType != ''">
            and aqami.actual_org_type =
                #{actualMeasureInfoVo.actualOrgType}
        </if>
    </sql>
    <select id="selectDtoPage" parameterType="com.easylinkin.linkappapi.quality.entity.vo.ActualMeasureInfoVo"
            resultMap="InfoDtoResultMap">
        select distinct aqami.*
        from app_quality_actual_measure_info aqami,
             app_quality_actual_measure_details aqamd
        where aqami.delete_state = 1
          and aqami.id = aqamd.info_id
        <include refid="selectDtoPageWhere"/>

        order by aqami.measure_time desc
    </select>

    <select id="selectOneDto" resultMap="InfoDtoResultMap">
        select aqami.*
        from app_quality_actual_measure_info aqami
        where aqami.id = #{id}
    </select>

    <select id="selectDtoPageCount" parameterType="com.easylinkin.linkappapi.quality.entity.vo.ActualMeasureInfoVo"
            resultType="int">
        select count(*)
        from
        (
        select distinct aqami.*
        from app_quality_actual_measure_info aqami,
             app_quality_actual_measure_details aqamd
        where aqami.delete_state = 1
          and aqami.id = aqamd.info_id
        <include refid="selectDtoPageWhere"/>
        ) a
    </select>

    <select id="selectTopByOrg" resultType="com.easylinkin.linkappapi.quality.dto.ActualMeasureTopDTO">
        select temp.name_                                         as item,
               ROUND(100 * temp.qualifiedNum / temp.actualNum, 2) as passRate
        from (select sum(aqamd.qualified_num) as qualifiedNum,
                     sum(aqamd.actual_num)    as actualNum,
                     alc.name_                as name_
              from app_quality_actual_measure_info aqami
                       inner join
                   app_quality_actual_measure_details aqamd on
                       aqamd.info_id = aqami.id

                       inner join app_labor_company alc on
                  aqami.sub_org_id = alc.id
        where aqami.storage_type = 2
          and aqami.delete_state = 1
          and aqamd.delete_state = 1
        <if test="actualMeasureInfo != null and actualMeasureInfo.tenantId != null and actualMeasureInfo.tenantId != ''">
            and aqami.tenant_id = #{actualMeasureInfo.tenantId,jdbcType=VARCHAR}
        </if>
        group by alc.name_) temp
        where temp.actualNum > 0
        order by passRate desc
    </select>

    <select id="selectTopByType" resultType="com.easylinkin.linkappapi.quality.dto.ActualMeasureTopDTO">
        select aqamt.full_name                          as item,
               ROUND(100 * qualifiedNum / actualNum, 2) as passRate
        from (select sum(aqamd.qualified_num) as qualifiedNum,
                     sum(aqamd.actual_num)    as actualNum,
                     aqamd.type_id            as typeId
              from app_quality_actual_measure_info aqami
                       inner join
                   app_quality_actual_measure_details aqamd on
                       aqamd.info_id = aqami.id
            where aqami.delete_state =1 and aqamd.delete_state = 1
                    and aqami.storage_type = 2
                <if test="actualMeasureInfo != null and actualMeasureInfo.tenantId != null and actualMeasureInfo.tenantId != ''">
                    and aqami.tenant_id = #{actualMeasureInfo.tenantId,jdbcType=VARCHAR}
                </if>
              group by aqamd.type_id) temp
                 inner join app_quality_actual_measure_type aqamt on
            temp.typeId = aqamt.id
        where temp.actualNum > 0
        order by passRate desc
    </select>

    <select id="selectCountBy" resultType="int">
        select count(*)
        from app_quality_actual_measure_info aqami
        where aqami.delete_state = 1
          and aqami.storage_type = 2
        <if test="positionId != null">
            and aqami.position_id = #{positionId}
        </if>
        <if test="tenantId != null and tenantId != ''">
            and aqami.tenant_id = #{tenantId}
        </if>
    </select>
</mapper>
