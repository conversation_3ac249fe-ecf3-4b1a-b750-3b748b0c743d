<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.QualityQuestionInfoMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.QualityQuestionInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="check_part_id" jdbcType="INTEGER" property="checkPartId" />
    <result column="question_describe_id" jdbcType="INTEGER" property="questionDescribeId" />
    <result column="question_level" jdbcType="INTEGER" property="questionLevel" />
    <result column="urgent_level" jdbcType="INTEGER" property="urgentLevel" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="sub_org_type" jdbcType="INTEGER" property="subOrgType" />
    <result column="sub_org_id" jdbcType="VARCHAR" property="subOrgId" />
    <result column="sub_org_name" jdbcType="VARCHAR" property="subOrgName" />
    <result column="sub_group_id" jdbcType="VARCHAR" property="subGroupId" />
    <result column="sub_group_name" jdbcType="VARCHAR" property="subGroupName" />
    <result column="rectifier_id" jdbcType="INTEGER" property="rectifierId" />
    <result column="rectifier_name" jdbcType="VARCHAR" property="rectifierName" />
    <result column="rectifi_site" jdbcType="INTEGER" property="rectifiSite" />
    <result column="rectifi_limit_time" jdbcType="TIMESTAMP" property="rectifiLimitTime" />
    <result column="rectifi_target" jdbcType="VARCHAR" property="rectifiTarget" />
    <result column="review_limit_time" jdbcType="TIMESTAMP" property="reviewLimitTime" />
    <result column="reviewer_id" jdbcType="INTEGER" property="reviewerId" />
    <result column="reviewer_name" jdbcType="VARCHAR" property="reviewerName" />
    <result column="noticer_ids" jdbcType="VARCHAR" property="noticerIds" />
    <result column="noticer_names" jdbcType="VARCHAR" property="noticerNames" />
    <result column="imgs" jdbcType="VARCHAR" property="imgs" />
    <result column="question_state" jdbcType="INTEGER" property="questionState" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
  </resultMap>
  <resultMap id="QuestionInfoDtoMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.quality.dto.QualityQuestionInfoDto">
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <association property="position" column="check_part_id" select="com.easylinkin.linkappapi.quality.mapper.QualityPositionMapper.selectById"/>
    <association property="problem" column="question_describe_id" select="com.easylinkin.linkappapi.quality.mapper.ProblemMapper.selectById" />
    <association property="appUser" column="creator" select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById" />
    <collection property="dealRecordList" column="id" select="com.easylinkin.linkappapi.quality.mapper.QualityQuestionDealRecordMapper.selectDealListByQuestionId" />
  </resultMap>
  <resultMap id="QuestionInfoOneDtoMap" extends="QuestionInfoDtoMap" type="com.easylinkin.linkappapi.quality.dto.QualityQuestionInfoDto">
    <association property="commentNum" column="id" select="com.easylinkin.linkappapi.quality.mapper.QualityQuestionCommentMapper.selectNumByQuestionId" />
    <association property="rectifierInfo" column="rectifier_id"  select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById" />
    <association property="reviewerInfo" column="reviewer_id"  select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById" />
  </resultMap>

  <resultMap id="QualityQuestionStatisticsDTO" type="com.easylinkin.linkappapi.quality.dto.QualityQuestionStatisticsDTO">
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="question_total_num" jdbcType="INTEGER" property="questionTotalNum" />
    <result column="wait_rectify_num" jdbcType="INTEGER" property="waitRectifyNum" />
    <result column="wait_beReview_num" jdbcType="INTEGER" property="waitBeReviewNum" />
    <result column="qualified_num" jdbcType="INTEGER" property="qualifiedNum" />
    <result column="not_quality_num" jdbcType="INTEGER" property="notQualityNum" />
    <result column="check_num" jdbcType="INTEGER" property="checkNum" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_quality_question_info where tenant_id = #{appQualityQuestionInfo.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_quality_question_info where id = #{id}
    </select>

  <select id="getByProblemType"
    resultType="com.easylinkin.linkappapi.quality.dto.QualityScreenDTO">
    SELECT
      c.full_name_ name,
      count(a.id) number
    FROM
      app_quality_question_info a
      LEFT JOIN app_problem b ON a.question_describe_id = b.id
      LEFT JOIN app_problem_type c ON b.problem_type_id_ = c.id
    WHERE
      a.delete_state = 1
      AND a.tenant_id = #{tenantId}
      AND a.create_time  BETWEEN #{startTime}
      AND #{endTime}
    GROUP BY
      c.parent_id_
  </select>
  <select id="getBySubOrg" resultType="com.easylinkin.linkappapi.quality.entity.vo.QualityQuestionInfoVo">
    SELECT
      a.id,
      a.sub_org_id subOrgId,
      c.name_ subOrgName,
      a.urgent_level urgentLevel,
	    a.rectifi_limit_time rectifiLimitTime
    FROM
      app_quality_question_info a
      LEFT JOIN app_labor_company_project b ON a.sub_org_id = b.id
      LEFT JOIN app_labor_company c ON b.company_id_ = c.id
    WHERE
      a.tenant_id = #{tenantId}
      AND delete_state = 1
  </select>
  <select id="getByMonth"
    resultType="com.easylinkin.linkappapi.quality.dto.QualityScreenDTO">
    SELECT
        COUNT( 1 ) AS number,
        DATE_FORMAT( create_time, "%Y-%m-%d %H:00:00" ) AS startTime
    FROM
        app_quality_question_info
    WHERE
        tenant_id = #{tenantId}
        and delete_state = 1
    AND DATE_SUB( CURDATE( ), INTERVAL 30 DAY ) &lt; date( create_time )
        GROUP BY DATE_FORMAT( create_time, '%Y-%m-%d' );
  </select>

  <select id="selectDtoPage" parameterType="com.easylinkin.linkappapi.quality.entity.vo.QualityQuestionInfoVo"
          resultMap="QuestionInfoDtoMap">
    select aqqi.*
    from app_quality_question_info aqqi
    where aqqi.delete_state = 1
    <if test="param2.tenantIds != null and param2.tenantIds.size() != 0">
      and aqqi.tenant_id
        in
      <foreach collection="param2.tenantIds" item="item" close=")" separator="," open="(">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="param2.tenantId != null and param2.tenantId != ''">
      and aqqi.tenant_id = #{param2.tenantId}
    </if>
    <if test="param2.questionLevel != null">
      and aqqi.question_level = #{param2.questionLevel}
    </if>
    <if test="param2.questionState != null and param2.questionState > 0">
      and aqqi.question_state = #{param2.questionState}
    </if>
    <if test="param2.urgentLevel != null">
      and aqqi.urgent_level = #{param2.urgentLevel}
    </if>
    <if test="param2.startTime != null">
      <![CDATA[
      and aqqi.create_time >= #{param2.startTime}
      ]]>
    </if>
    <if test="param2.endTime != null">
      <![CDATA[
      and aqqi.create_time <= #{param2.endTime}
      ]]>
    </if>
    <if test="param2.checkPartId != null">
      and aqqi.check_part_id = #{param2.checkPartId}
    </if>
    <if test="param2.questionDescribeId != null">
      and aqqi.question_describe_id = #{param2.questionDescribeId}
    </if>
    <if test="param2.paramKey != null and param2.paramKey != ''">
      and (aqqi.sub_org_name like concat('%', #{param2.paramKey}, '%') or
           aqqi.sub_group_name like concat('%', #{param2.paramKey}, '%'))
    </if>
    <if test="param2.creator != null and param2.creator != ''">
      and aqqi.creator = #{param2.creator}
    </if>
    <if test="param2.rectifierId != null">
      and aqqi.rectifier_id = #{param2.rectifierId}
    </if>
    <if test="param2.reviewerId != null">
      and aqqi.reviewer_id = #{param2.reviewerId}
    </if>
    <if test="param2.dataType != null and param2.dataType != ''">
      <if test="param2.dataType == 'rectifiNum'">
        <if test="param2.questionState != null">
          <if test="param2.questionState == -1">
            and aqqi.question_state in (1, 4)
          </if>
          <if test="param2.questionState == -2">
            and aqqi.question_state in (2, 3)
          </if>
        </if>
        order by aqqi.rectifi_limit_time, aqqi.create_time
      </if>
      <if test="param2.dataType == 'reviewNum'">
        <if test="param2.questionState != null">
          <if test="param2.questionState == -1">
            and aqqi.question_state = 2
          </if>
          <if test="param2.questionState == -2">
            and aqqi.question_state in (3, 4)
          </if>
        </if>
        <if test="param2.questionState == null or param2.questionState == ''">
          and aqqi.question_state in (2,3, 4)
        </if>
        order by aqqi.review_limit_time,aqqi.create_time
      </if>
    </if>

    <if test="param2.dataType == null or param2.dataType == ''">
      order by aqqi.create_time desc
    </if>
  </select>

  <select id="selectDtoOne" parameterType="long" resultMap="QuestionInfoOneDtoMap">
    select aqqi.* from app_quality_question_info aqqi where aqqi.id = #{id}
  </select>

  <select id="countNumByState" parameterType="com.easylinkin.linkappapi.quality.entity.vo.QualityQuestionInfoVo" resultType="java.util.Map">
    select
      aqqi.question_state as questionState,
      count(*) as num
    from
      app_quality_question_info aqqi
    where
      aqqi.delete_state = 1
      <if test="rectifierId != null">
        and aqqi.rectifier_id = #{rectifierId}
      </if>
      <if test="reviewerId != null">
        and aqqi.reviewer_id = #{reviewerId}
      </if>
    group by aqqi.question_state
  </select>

<!--  巡检台账-->
  <select id="findAccountList" resultMap="QuestionInfoDtoMap">
    SELECT
      *
    FROM
      (
    SELECT
      1 type,
      a.id,
      a.check_part_id,
      a.question_describe_id,
      a.question_level,
      a.urgent_level,
      a.question_state,
      a.creator,
      a.create_time,
      a.imgs,
      a.rectifier_id,
      a.rectifier_name,
      a.reviewer_id,
      a.reviewer_name,
      a.noticer_names,
      a.rectifi_target,
      a.rectifi_limit_time,
      a.review_limit_time
    FROM
      app_quality_question_info a
      <if test="dto.subOrgName != null and dto.subOrgName != ''">
        LEFT JOIN app_labor_company_project b ON a.sub_org_id = b.id
        LEFT JOIN app_labor_company c ON b.company_id_ = c.id
        LEFT JOIN app_group d ON a.sub_group_id = d.id
      </if>
    WHERE
      a.delete_state = 1
      and a.tenant_id = #{dto.tenantId}
      <if test="dto.subOrgName != null and dto.subOrgName != ''">
        and (c.name_ like  CONCAT('%',#{dto.subOrgName},'%')
             or d.name_ like CONCAT('%',#{dto.subOrgName},'%'))
      </if>
    <if test="dto.subOrgName == null || dto.subOrgName == ''">
    UNION ALL
      SELECT
        2 type,
        a.id,
        a.position_id_ check_part_id,
        a.problem_id_ question_describe_id,
        b.level_ question_level,
        NULL urgent_level,
        5 question_state,
        a.creator_id_ creator,
        a.create_time_ create_time,
        a.url_ imgs,
        NULL rectifier_id,
        NULL rectifier_name,
        NULL reviewer_id,
        NULL reviewer_name,
        NULL noticer_names,
        NULL rectifi_target,
        NULL rectifi_limit_time,
        NULL review_limit_time
      FROM
        app_quality_inspection a
      LEFT JOIN app_problem b ON a.problem_id_ = b.id
      WHERE a.tenant_id_ = #{dto.tenantId}
    </if>
      ) y
      LEFT JOIN app_problem x ON y.question_describe_id = x.id
      LEFT JOIN app_problem_type z ON x.problem_type_id_ = z.id
      <where>
        <if test="dto.startTime != null">
          and y.create_time &gt;=  #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
          and y.create_time &lt;=  #{dto.endTime}
        </if>
        <if test="dto.creator != null">
          and y.creator =  #{dto.creator}
        </if>
        <if test="dto.questionLevel != null">
          and y.question_level =  #{dto.questionLevel}
        </if>
        <if test="dto.urgentLevel != null">
          and y.urgent_level =  #{dto.urgentLevel}
        </if>
        <if test="dto.checkPartId != null">
          and y.check_part_id =  #{dto.checkPartId}
        </if>
        <if test="dto.problemTypeId != null">
          and (z.id = #{dto.problemTypeId}
          or
          z.parent_id_ = #{dto.problemTypeId})
        </if>
        <if test="dto.questionState != null">
          and y.question_state =  #{dto.questionState}
        </if>
        <if test="dto.id != null">
          and y.id =  #{dto.id}
        </if>
        <if test="dto.type != null">
          and y.type =  #{dto.type}
        </if>
      </where>
    ORDER BY
      y.create_time DESC
  </select>


  <select id="findOpenApiAccountList" resultMap="QuestionInfoDtoMap">
    SELECT
    *
    FROM
    (
    SELECT
    1 type,
    a.id,
    a.check_part_id,
    a.question_describe_id,
    a.question_level,
    a.urgent_level,
    a.question_state,
    a.creator,
    a.create_time,
    a.imgs,
    a.rectifier_id,
    a.rectifier_name,
    a.reviewer_id,
    a.reviewer_name,
    a.noticer_names,
    a.rectifi_target,
    a.rectifi_limit_time,
    a.review_limit_time,
    lt.platform_project_name projectName
    FROM
    app_quality_question_info a
    <if test="dto.subOrgName != null and dto.subOrgName != ''">
      LEFT JOIN app_labor_company_project b ON a.sub_org_id = b.id
      LEFT JOIN app_labor_company c ON b.company_id_ = c.id
      LEFT JOIN app_group d ON a.sub_group_id = d.id
    </if>
      JOIN linkapp_tenant lt on lt.id = a. tenant_id and lt.project_id in
      <foreach collection="dto.projectIds" item="projectId" index="index" open="("
        close=")" separator=",">
        #{projectId}
      </foreach>
    WHERE
    a.delete_state = 1
    <if test="dto.subOrgName != null and dto.subOrgName != ''">
      and (c.name_ like  CONCAT('%',#{dto.subOrgName},'%')
      or d.name_ like CONCAT('%',#{dto.subOrgName},'%'))
    </if>
    <if test="dto.questionContent != null and dto.questionContent != ''">
      and a.question_describe_id in
      <foreach collection="dto.questionDescribeIds" item="question_describe_id" index="index" open="("
        close=")" separator=",">
        #{question_describe_id}
      </foreach>
    </if>
    <if test="dto.subOrgName == null || dto.subOrgName == ''">
      UNION ALL
      SELECT
      2 type,
      a.id,
      a.position_id_ check_part_id,
      a.problem_id_ question_describe_id,
      b.level_ question_level,
      NULL urgent_level,
      5 question_state,
      a.creator_id_ creator,
      a.create_time_ create_time,
      a.url_ imgs,
      NULL rectifier_id,
      NULL rectifier_name,
      NULL reviewer_id,
      NULL reviewer_name,
      NULL noticer_names,
      NULL rectifi_target,
      NULL rectifi_limit_time,
      NULL review_limit_time,
      lt.platform_project_name projectName
      FROM
      app_quality_inspection a
      LEFT JOIN app_problem b ON a.problem_id_ = b.id
      JOIN linkapp_tenant lt on lt.id = a. tenant_id_ and lt.project_id in
      <foreach collection="dto.projectIds" item="projectId" index="index" open="("
        close=")" separator=",">
        #{projectId}
      </foreach>
      <if test="dto.questionContent != null and dto.questionContent != ''">
        where  a.problem_id_ in
        <foreach collection="dto.questionDescribeIds" item="question_describe_id" index="index" open="("
          close=")" separator=",">
          #{question_describe_id}
        </foreach>
      </if>
    </if>
    ) y
    LEFT JOIN app_problem x ON y.question_describe_id = x.id
    LEFT JOIN app_problem_type z ON x.problem_type_id_ = z.id
    <where>
      <if test="dto.startTime != null">
        and y.create_time &gt;=  #{dto.startTime}
      </if>
      <if test="dto.endTime != null">
        and y.create_time &lt;=  #{dto.endTime}
      </if>
      <if test="dto.creator != null">
        and y.creator =  #{dto.creator}
      </if>
      <if test="dto.questionLevel != null">
        and y.question_level =  #{dto.questionLevel}
      </if>
      <if test="dto.urgentLevel != null">
        and y.urgent_level =  #{dto.urgentLevel}
      </if>
      <if test="dto.checkPartId != null">
        and y.check_part_id =  #{dto.checkPartId}
      </if>
      <if test="dto.problemTypeId != null">
        and (z.id = #{dto.problemTypeId}
        or
        z.parent_id_ = #{dto.problemTypeId})
      </if>
      <if test="dto.dangerTypeName != null and dto.dangerTypeName != ''">
        and (z.id in
        <foreach collection="dto.problemTypeIds" item="question_describe_id" index="index"
          open="("
          close=")" separator=",">
          #{question_describe_id}
        </foreach>
         or  z.parent_id_ in
        <foreach collection="dto.problemTypeIds" item="question_describe_id" index="index"
          open="("
          close=")" separator=",">
          #{question_describe_id}
        </foreach>)
      </if>
      <if test="dto.questionState != null">
        and y.question_state =  #{dto.questionState}
      </if>
      <if test="dto.id != null">
        and y.id =  #{dto.id}
      </if>
      <if test="dto.type != null">
        and y.type =  #{dto.type}
      </if>
    </where>
    ORDER BY
    y.create_time DESC
  </select>

  <select id="selectPhotoAlbumByPage" resultType="com.easylinkin.linkappapi.quality.dto.QualityQuestionPhotoAlbumDTO">
    SELECT qq.*
    FROM (
      SELECT
        q.id,
        q.imgs,
        q.question_state AS questionState,
        p.full_name_ AS problemFullName,
        pt.full_name_ AS problemTypeFullName,
        p.content_ AS questionDescribe,
        p1.full_name_ AS checkPartFullName,
        q.question_level AS questionLevel,
        q.urgent_level AS urgentLevel,
        q.sub_org_name AS subOrgName,
        q.sub_group_name AS subGroupName,
        b.nickname AS creator,
        q.create_time AS createTime,
        group_concat( r.deal_imgs ) AS dealImgs,
        tenant.platform_project_name as projectName
      FROM app_quality_question_info q
      LEFT JOIN app_problem p on q.question_describe_id = p.id
      INNER JOIN app_problem_type pt ON pt.id = p.problem_type_id_
      LEFT JOIN app_quality_position p1 on q.check_part_id = p1.id
      LEFT JOIN linkapp_user b ON q.creator = b.id
      LEFT JOIN app_quality_question_deal_record r ON r.question_id = q.id
      AND r.deal_imgs IS NOT NULL
      AND r.delete_state = 1
      LEFT JOIN linkapp_tenant tenant on tenant.id = q.tenant_id
      <where>
        q.delete_state = 1
        <if test="photoAlbumQueryDTO.tenantId != null and photoAlbumQueryDTO.tenantId != ''">
          AND q.tenant_id = #{photoAlbumQueryDTO.tenantId}
        </if>
        <if test="photoAlbumQueryDTO.createId != null and photoAlbumQueryDTO.createId != ''">
          AND q.creator = #{photoAlbumQueryDTO.createId}
        </if>
        <if test="photoAlbumQueryDTO.subOrgId != null and photoAlbumQueryDTO.subOrgId != ''">
          AND q.sub_org_id = #{photoAlbumQueryDTO.subOrgId}
        </if>
        <if test="photoAlbumQueryDTO.subGroupId != null and photoAlbumQueryDTO.subGroupId != ''">
          AND q.sub_group_id = #{photoAlbumQueryDTO.subGroupId}
        </if>
        <if test="photoAlbumQueryDTO.checkPartId != null and photoAlbumQueryDTO.checkPartId != ''">
          AND q.check_part_id = #{photoAlbumQueryDTO.checkPartId}
        </if>
        <if test="photoAlbumQueryDTO.questionDescribeId != null and photoAlbumQueryDTO.questionDescribeId != ''">
          and q.question_describe_id in (select p.id
          from app_problem p,
          app_problem_type apt
          where p.problem_type_id_ = apt.id
          and (apt.id = #{photoAlbumQueryDTO.questionDescribeId} or
          apt.parent_id_ = #{photoAlbumQueryDTO.questionDescribeId}))
        </if>
        <if test="photoAlbumQueryDTO.startTime != null ">
          and q.create_time &gt;= #{photoAlbumQueryDTO.startTime}
        </if>
        <if test="photoAlbumQueryDTO.endTime != null">
          and q.create_time &lt;= #{photoAlbumQueryDTO.endTime}
        </if>
        <if test="photoAlbumQueryDTO.ids != null and photoAlbumQueryDTO.ids.size()>0">
          AND q.id IN
          <foreach item="item" index="index" collection="photoAlbumQueryDTO.ids" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
      </where>
      GROUP BY q.id
    ) AS qq
    WHERE qq.imgs IS NOT NULL
    OR qq.dealImgs IS NOT NULL
    ORDER BY qq.createTime
    <choose>
      <when test="photoAlbumQueryDTO.sortAsc">
        ASC
      </when>
      <otherwise>
        DESC
      </otherwise>
    </choose>
  </select>

  <select id="countQualityQuestionBySubOrg" resultType="com.easylinkin.linkappapi.quality.dto.QualityQuestionStatisticsBySubOrgDTO">
    SELECT
      q.sub_org_id,
      q.sub_org_name,
      q.sub_group_id,
      q.sub_group_name,
      count( q.id ) AS allCount,
      (
        SELECT
        count( q1.id )
        FROM
        app_quality_question_info AS q1
        WHERE
        q1.urgent_level = 1
        AND q.sub_org_id = q1.sub_org_id
        AND q.sub_group_id <![CDATA[ <=> ]]> q1.sub_group_id
        AND q1.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q1.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q1.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q1.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS level1Count,
      (
        SELECT
        count( q2.id )
        FROM
        app_quality_question_info AS q2
        WHERE
        q2.urgent_level = 2
        AND q.sub_org_id = q2.sub_org_id
        AND q.sub_group_id <![CDATA[ <=> ]]> q2.sub_group_id
        AND q2.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q2.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q2.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q2.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS level2Count,
      (
        SELECT
        count( q3.id )
        FROM
        app_quality_question_info AS q3
        WHERE
        q3.urgent_level = 3
        AND q.sub_org_id = q3.sub_org_id
        AND q.sub_group_id <![CDATA[ <=> ]]> q3.sub_group_id
        AND q3.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q3.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q3.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q3.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS level3Count,
      (
        SELECT
        count( q4.id )
        FROM
        app_quality_question_info AS q4
        WHERE
        q4.question_state = 3
        AND q.sub_org_id = q4.sub_org_id
        AND q.sub_group_id <![CDATA[ <=> ]]> q4.sub_group_id
        AND q4.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q4.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q4.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q4.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS state3
    FROM
      app_quality_question_info AS q
    WHERE
      q.delete_state = 1
      <if test="queryDTO.tenantId != null ">
        AND q.tenant_id = #{queryDTO.tenantId}
      </if>
      <if test="queryDTO.startTime != null ">
        AND q.create_time &gt;= #{queryDTO.startTime}
      </if>
      <if test="queryDTO.endTime != null">
        AND q.create_time &lt;= #{queryDTO.endTime}
      </if>
    GROUP BY
      q.sub_org_id,
      q.sub_group_id
  </select>

  <select id="countQualityQuestionByProblemType" resultType="com.easylinkin.linkappapi.quality.dto.QualityQuestionStatisticsByProblemTypeDTO">
    SELECT
      ptp.id,
      ptp.`full_name_`,
      count( q.id ) AS allCount,
      (
        count( q.id ) / (
        SELECT
        count( q.id )
        FROM
        app_quality_question_info AS q
        INNER JOIN app_problem p ON p.id = q.question_describe_id
        INNER JOIN app_problem_type pt ON pt.id = p.problem_type_id_
        INNER JOIN app_problem_type ptp ON ptp.id = pt.parent_id_
        WHERE
        q.delete_state = 1
        AND q.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q.create_time &lt;= #{queryDTO.endTime}
        </if>      )
      ) AS percent,
      (
        SELECT
        count( q1.id )
        FROM
        app_quality_question_info AS q1
        INNER JOIN app_problem p1 ON p1.id = q1.question_describe_id
        INNER JOIN app_problem_type pt1 ON pt1.id = p1.problem_type_id_
        INNER JOIN app_problem_type ptp1 ON ptp1.id = pt1.parent_id_
        WHERE
        q1.urgent_level = 1
        AND ptp.id = ptp1.id
        AND q1.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q1.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q1.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q1.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS level1Count,
      (
        SELECT
        count( q2.id )
        FROM
        app_quality_question_info AS q2
        INNER JOIN app_problem p2 ON p2.id = q2.question_describe_id
        INNER JOIN app_problem_type pt2 ON pt2.id = p2.problem_type_id_
        INNER JOIN app_problem_type ptp2 ON ptp2.id = pt2.parent_id_
        WHERE
        q2.urgent_level = 2
        AND ptp.id = ptp2.id
        AND q2.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q2.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q2.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q2.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS level2Count,
      (
        SELECT
        count( q3.id )
        FROM
        app_quality_question_info AS q3
        INNER JOIN app_problem p3 ON p3.id = q3.question_describe_id
        INNER JOIN app_problem_type pt3 ON pt3.id = p3.problem_type_id_
        INNER JOIN app_problem_type ptp3 ON ptp3.id = pt3.parent_id_
        WHERE
        q3.urgent_level = 3
        AND ptp.id = ptp3.id
        AND q3.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q3.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q3.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q3.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS level3Count,
      (
        SELECT
        count( q4.id )
        FROM
        app_quality_question_info AS q4
        INNER JOIN app_problem p4 ON p4.id = q4.question_describe_id
        INNER JOIN app_problem_type pt4 ON pt4.id = p4.problem_type_id_
        INNER JOIN app_problem_type ptp4 ON ptp4.id = pt4.parent_id_
        WHERE
        q4.question_state = 3
        AND ptp.id = ptp4.id
        AND q4.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q4.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q4.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q4.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS state3
    FROM
      app_quality_question_info AS q
      INNER JOIN app_problem p ON p.id = q.question_describe_id
      INNER JOIN app_problem_type pt ON pt.id = p.problem_type_id_
      INNER JOIN app_problem_type ptp ON ptp.id = pt.parent_id_
    WHERE
      q.delete_state = 1
      <if test="queryDTO.tenantId != null ">
        AND q.tenant_id = #{queryDTO.tenantId}
      </if>
      <if test="queryDTO.startTime != null ">
        AND q.create_time &gt;= #{queryDTO.startTime}
      </if>
      <if test="queryDTO.endTime != null">
        AND q.create_time &lt;= #{queryDTO.endTime}
      </if>
    GROUP BY
      ptp.id
  </select>

  <select id="countQualityQuestionByCheckPart" resultType="com.easylinkin.linkappapi.quality.dto.QualityQuestionStatisticsByCheckPartDTO">
    SELECT
      q.check_part_id,
      p.`full_name_`,
      count( q.id ) AS allCount,
      (
        SELECT
        count( q1.id )
        FROM
        app_quality_question_info AS q1
        LEFT JOIN app_quality_position AS p1 ON q1.check_part_id = p1.id
        WHERE
        q1.question_state = 1
        AND q.check_part_id = q1.check_part_id
        AND q1.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q1.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q1.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q1.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS state1,
      (
        SELECT
        count( q2.id )
        FROM
        app_quality_question_info AS q2
        LEFT JOIN app_quality_position AS p2 ON q2.check_part_id = p2.id
        WHERE
        q2.question_state = 2
        AND q.check_part_id = q2.check_part_id
        AND q2.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q2.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q2.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q2.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS state2,
      (
        SELECT
        count( q3.id )
        FROM
        app_quality_question_info AS q3
        LEFT JOIN app_quality_position AS p3 ON q3.check_part_id = p3.id
        WHERE
        q3.question_state = 3
        AND q.check_part_id = q3.check_part_id
        AND q3.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q3.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q3.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q3.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS state3,
      (
        SELECT
        count( q4.id )
        FROM
        app_quality_question_info AS q4
        LEFT JOIN app_quality_position AS p4 ON q4.check_part_id = p4.id
        WHERE
        q4.question_state = 4
        AND q.check_part_id = q4.check_part_id
        AND q4.delete_state = 1
        <if test="queryDTO.tenantId != null ">
          AND q4.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.startTime != null ">
          AND q4.create_time &gt;= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
          AND q4.create_time &lt;= #{queryDTO.endTime}
        </if>
      ) AS state4,
      (
        ( SELECT
          count( q4.id )
          FROM
          app_quality_question_info AS q4
          LEFT JOIN app_quality_position AS p4 ON q4.check_part_id = p4.id
          WHERE
          q4.question_state = 3
          AND q.check_part_id = q4.check_part_id
          AND q4.delete_state = 1
          <if test="queryDTO.tenantId != null ">
            AND q4.tenant_id = #{queryDTO.tenantId}
          </if>
          <if test="queryDTO.startTime != null ">
            AND q4.create_time &gt;= #{queryDTO.startTime}
          </if>
          <if test="queryDTO.endTime != null">
            AND q4.create_time &lt;= #{queryDTO.endTime}
          </if>
          ) / count( q.id )
      ) AS percent
    FROM
      app_quality_question_info AS q
      LEFT JOIN app_quality_position AS p ON q.check_part_id = p.id
    WHERE
      q.delete_state = 1
      <if test="queryDTO.tenantId != null ">
        AND q.tenant_id = #{queryDTO.tenantId}
      </if>
      <if test="queryDTO.startTime != null ">
        AND q.create_time &gt;= #{queryDTO.startTime}
      </if>
      <if test="queryDTO.endTime != null">
        AND q.create_time &lt;= #{queryDTO.endTime}
      </if>
    GROUP BY
      q.check_part_id
  </select>

  <select id="getCount" resultType="java.lang.Integer">
    SELECT
    count(1)
    FROM
    (
    SELECT
    1 type,
    a.id,
    a.check_part_id,
    a.question_describe_id,
    a.question_level,
    a.urgent_level,
    a.question_state,
    a.creator,
    a.create_time,
    a.imgs,
    a.rectifier_name,
    a.reviewer_name,
    a.noticer_names,
    a.rectifi_target,
    a.rectifi_limit_time,
    a.review_limit_time
    FROM
    app_quality_question_info a
    <if test="dto.subOrgName != null and dto.subOrgName != ''">
      LEFT JOIN app_labor_company_project b ON a.sub_org_id = b.id
      LEFT JOIN app_labor_company c ON b.company_id_ = c.id
      LEFT JOIN app_group d ON a.sub_group_id = d.id
    </if>
    WHERE
    a.delete_state = 1
    and a.tenant_id = #{dto.tenantId}
    <if test="dto.subOrgName != null and dto.subOrgName != ''">
      and (c.name_ like  CONCAT('%',#{dto.subOrgName},'%')
      or d.name_ like CONCAT('%',#{dto.subOrgName},'%'))
    </if>
    <if test="dto.subOrgName == null || dto.subOrgName == ''">
      UNION ALL
      SELECT
      2 type,
      a.id,
      a.position_id_ check_part_id,
      a.problem_id_ question_describe_id,
      b.level_ question_level,
      NULL urgent_level,
      5 question_state,
      a.creator_id_ creator,
      a.create_time_ create_time,
      a.url_ imgs,
      NULL rectifier_name,
      NULL reviewer_name,
      NULL noticer_names,
      NULL rectifi_target,
      NULL rectifi_limit_time,
      NULL review_limit_time
      FROM
      app_quality_inspection a
      LEFT JOIN app_problem b ON a.problem_id_ = b.id
      WHERE a.tenant_id_ = #{dto.tenantId}
    </if>
    ) y
    LEFT JOIN app_problem x ON y.question_describe_id = x.id
    LEFT JOIN app_problem_type z ON x.problem_type_id_ = z.id
    <where>
      <if test="dto.startTime != null">
        and y.create_time &gt;=  #{dto.startTime}
      </if>
      <if test="dto.endTime != null">
        and y.create_time &lt;=  #{dto.endTime}
      </if>
      <if test="dto.creator != null">
        and y.creator =  #{dto.creator}
      </if>
      <if test="dto.questionLevel != null">
        and y.question_level =  #{dto.questionLevel}
      </if>
      <if test="dto.urgentLevel != null">
        and y.urgent_level =  #{dto.urgentLevel}
      </if>
      <if test="dto.checkPartId != null">
        and y.check_part_id =  #{dto.checkPartId}
      </if>
      <if test="dto.problemTypeId != null">
        and (z.id = #{dto.problemTypeId}
        or
        z.parent_id_ = #{dto.problemTypeId})
      </if>
      <if test="dto.questionState != null">
        and y.question_state =  #{dto.questionState}
      </if>
    </where>
    ORDER BY
    y.create_time DESC
  </select>

  <select id="selectQualityCheckTrend" parameterType="com.easylinkin.linkappapi.quality.entity.vo.QualityScreenPageVo" resultType="map">
    select tt.create_time,tt.type, count(*) as num
    from (select date_format(aqi.create_time_, '%Y-%m-%d') as create_time, aqi.id,1 as type
          from app_quality_inspection aqi left join linkapp_tenant lt on aqi.tenant_id_ = lt.id
          where lt.id is not null
        <![CDATA[ and aqi.create_time_ >= #{startTime}
            and aqi.create_time_ <= #{endTime}
        ]]>
    union all
          (select date_format(aqqi.create_time, '%Y-%m-%d') as create_time, aqqi.id,2 as type
           from app_quality_question_info aqqi left join linkapp_tenant lt on aqqi.tenant_id = lt.id
           where lt.id is not null and aqqi.delete_state = 1
        <![CDATA[ and aqqi.create_time >= #{startTime}
             and aqqi.create_time <= #{endTime}
        ]]>
    )) tt
    group by tt.create_time,tt.type
  </select>

  <select id="countByFirstLevelProblemName"
          parameterType="com.easylinkin.linkappapi.quality.entity.vo.QualityScreenPageVo" resultType="java.util.Map">

    select
    left(ap.full_name_,
    LOCATE('/', ap.full_name_)-1) as name,
    count(*) as num
    from
    (
    select
    aqi.id, aqi.create_time_ as create_time, aqi.problem_id_ as problem_id
    from
    app_quality_inspection aqi left join linkapp_tenant lt on aqi.tenant_id_ = lt.id
    where lt.id is not null
    <![CDATA[ and aqi.create_time_ >= #{startTime}
      and aqi.create_time_ <= #{endTime} ]]>
    union all
    (
    select
    aqqi.id, aqqi.create_time as create_time, aqqi.question_describe_id as problem_id
    from
    app_quality_question_info aqqi left join linkapp_tenant lt on aqqi.tenant_id  = lt.id
    where lt.id is not null and aqqi.delete_state = 1
    <![CDATA[ and aqqi.create_time >= #{startTime}
      and aqqi.create_time <= #{endTime} ]]>
    )) tt
    left join app_problem ap
    on
    tt.problem_id = ap.id
    where
    ap.full_name_ is not null
    group by
    name
    order by num desc
  </select>

  <select id="questionCountGroupByLabor" parameterType="com.easylinkin.linkappapi.quality.entity.vo.QualityScreenPageVo"
          resultType="java.util.Map">
    select distinct alc.id,
           alc.name_ as subCompanyName,
           t.*
    from app_labor_company alc
           left join app_labor_company_project alcp on
      alc.id = alcp.company_id_
           left join (select q.sub_org_id     as companyProjectId,
                             q.sub_org_name   as companyProjectName,
                             q.question_state as questionState,
                             count(*)         as num
                      from app_quality_question_info q
                        left join linkapp_tenant lt on
                            q.tenant_id = lt.id
                      where q.delete_state = 1
                        and lt.id is not null
          <![CDATA[ and q.create_time >= #{startTime}
                        and q.create_time <= #{endTime} ]]>
    group by q.sub_org_id,q.question_state) t on
      alcp.id = t.companyProjectId
  </select>
  <select id="getOpenApiCount" resultMap="QualityQuestionStatisticsDTO">
    select info.tenant_id,lt.platform_project_name,lt.project_id ,count(0) as question_total_num,  -- 问题总数
    ifNull(sum(case when info.question_state=1 then 1 else 0 end),0) as wait_rectify_num,   -- 待整改
    ifNull(sum(case when info.question_state=2 then 1 else 0 end),0) as wait_beReview_num,  -- 待复查
    ifNull(sum(case when info.question_state=3 then 1 else 0 end),0) as qualified_num, -- 已合格
    ifNull(sum(case when info.question_state=4 then 1 else 0 end),0) as not_quality_num -- 不合格
    FROM
    app_quality_question_info info
    join linkapp_tenant lt on info .tenant_id =lt.id and lt.project_id in
    <foreach collection="qualityQuestionInfoDto.projectIds" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
    WHERE
    delete_state = 1
    <if test="qualityQuestionInfoDto.startTime!=null">
      and info.create_time &gt;= #{qualityQuestionInfoDto.startTime}
    </if>
    <if test="qualityQuestionInfoDto.endTime!=null">
      and info.create_time &lt;= #{qualityQuestionInfoDto.endTime}
    </if>
    group by tenant_id
  </select>
  <select id="getOpenApiCheckNumCount" resultMap="QualityQuestionStatisticsDTO">
    select lt.id as tenant_id,lt.platform_project_name ,lt.project_id ,count(0) as check_num
        from app_quality_inspection inspection
        join linkapp_tenant lt on inspection .tenant_id_ = lt .id and lt.project_id in
      <foreach collection="qualityQuestionInfoDto.projectIds" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
    <where>
      <if test="qualityQuestionInfoDto.startTime!=null">
        and inspection.create_time_ &gt;= #{qualityQuestionInfoDto.startTime}
      </if>
      <if test="qualityQuestionInfoDto.endTime!=null">
        and inspection.create_time_ &lt;= #{qualityQuestionInfoDto.endTime}
      </if>
    </where>
    group by tenant_id
  </select>
  <select id="findList" resultMap="QuestionInfoDtoMap">
    SELECT
      1 type,
      a.id,
      a.check_part_id,
      a.question_describe_id,
      a.question_level,
      a.urgent_level,
      a.question_state,
      a.creator,
      a.create_time,
      a.imgs,
      a.rectifier_id,
      a.rectifier_name,
      a.reviewer_id,
      a.reviewer_name,
      a.noticer_names,
      a.rectifi_target,
      a.rectifi_limit_time,
      a.review_limit_time
    FROM
      app_quality_question_info a
    WHERE
      a.delete_state = 1
      and a.tenant_id = #{qualityQuestionInfoDto.tenantId}
      <if test="qualityQuestionInfoDto.startTime!=null">
      and a.create_time &gt;= #{qualityQuestionInfoDto.startTime}
    </if>
    <if test="qualityQuestionInfoDto.endTime!=null">
      and a.create_time &lt;= #{qualityQuestionInfoDto.endTime}
    </if>
  </select>

  <select id="findOverByTenantIds" resultType="com.easylinkin.linkappapi.quality.dto.QualityQuestionInfoDto">
    SELECT
      a.id,
      a.tenant_id tenantId,
      a.rectifier_id rectifierId,
      a.rectifi_limit_time rectifiLimitTime,
      a.review_limit_time reviewLimitTime,
      a.reviewer_id reviewerId,
      a.question_state questionState,
      b.content_ questionContent
    FROM
        app_quality_question_info a
    LEFT JOIN app_problem b ON a.question_describe_id = b.id
    WHERE
      a.delete_state = 1
      <if test="tenantIds != null and tenantIds.size() != 0">
        AND tenant_id IN
        <foreach collection="tenantIds" item="tenantId" index="index" open="(" separator="," close=")">
          #{tenantId}
        </foreach>
      </if>
      AND (
        ( a.question_state = 1 AND
            <![CDATA[
                a.rectifi_limit_time <= now( )
            ]]>
           )
        OR
        ( a.question_state = 2 AND
            <![CDATA[
              a.review_limit_time <= now( )
            ]]>
         )
      )
  </select>

  <select id="findHueList" resultType="com.easylinkin.linkappapi.quality.dto.QualityQuestionInfoDto">
    SELECT
      a.id,
      a.tenant_id tenantId,
      a.rectifier_id rectifierId,
      a.rectifi_limit_time rectifiLimitTime,
      a.review_limit_time reviewLimitTime,
      a.reviewer_id reviewerId,
      a.question_state questionState,
      b.content_ questionContent
    FROM
        app_quality_question_info a
    LEFT JOIN app_problem b ON a.question_describe_id = b.id
    LEFT JOIN app_quality_config c ON a.tenant_id = c.tenant_id_
    WHERE
        a.delete_state = 1
      AND (
        (
          a.question_state = 1
          AND c.rectify_expire_ = 1
          AND TIMESTAMPDIFF( HOUR, now( ), a.rectifi_limit_time ) + 1 <![CDATA[ = ]]> c.rectify_expire_param_
        )
      OR (
          a.question_state = 2
          AND c.check_expire_ = 1
          AND TIMESTAMPDIFF( HOUR, now( ), a.review_limit_time ) + 1 <![CDATA[ = ]]> c.check_expire_param_
        )
      )
  </select>

  <select id="queClassStatis" resultType="com.easylinkin.linkappapi.quality.dto.QuestionClassDTO">
    SELECT
    a.name problemName,
    a.num problemNum,
    CONCAT( ROUND( a.num / b.total * 100, 2 ), '', '%' ) AS percent
    FROM
    (select
    SUBSTRING_INDEX(SUBSTRING_INDEX(ap.full_name_, '/', 2), '/', -1) name,
    count(*) as num
    from
    (
    select
    aqi.id, aqi.create_time_ as create_time, aqi.problem_id_ as problem_id
    from
    app_quality_inspection aqi left join linkapp_tenant lt on aqi.tenant_id_ = lt.id
    where lt.id is not null
    and aqi.create_time_ &gt;= #{startTime}
    and aqi.create_time_ &lt;= #{endTime}
    union all
    (
    select
    aqqi.id, aqqi.create_time as create_time, aqqi.question_describe_id as problem_id
    from
    app_quality_question_info aqqi left join linkapp_tenant lt on aqqi.tenant_id  = lt.id
    where lt.id is not null and aqqi.delete_state = 1
    and aqqi.create_time &gt;= #{startTime}
    and aqqi.create_time &lt;= #{endTime}
    )) tt
    left join app_problem ap
    on
    tt.problem_id = ap.id
    where
    ap.full_name_ is not null
    group by name) a, (select count(*) total FROM
    (
    select
    aqi.id, aqi.create_time_ as create_time, aqi.problem_id_ as problem_id
    from
    app_quality_inspection aqi left join linkapp_tenant lt on aqi.tenant_id_ = lt.id
    where lt.id is not null
    and aqi.create_time_ &gt;= #{startTime}
    and aqi.create_time_ &lt;= #{endTime}
    union all
    (
    select
    aqqi.id, aqqi.create_time as create_time, aqqi.question_describe_id as problem_id
    from
    app_quality_question_info aqqi left join linkapp_tenant lt on aqqi.tenant_id  = lt.id
    where lt.id is not null and aqqi.delete_state = 1
    and aqqi.create_time &gt;= #{startTime}
    and aqqi.create_time &lt;= #{endTime}
    )) tt
    left join app_problem ap
    on
    tt.problem_id = ap.id
    where
    ap.full_name_ is not null) b
    order by problemNum desc
  </select>

  <select id="projectQuestionStatis" resultType="com.easylinkin.linkappapi.quality.dto.ProjectQuestionDTO">
    SELECT a.projectName,
    COUNT(*) problemNum
    FROM
    (select
    tt.projectName,
    SUBSTRING_INDEX(SUBSTRING_INDEX(ap.full_name_, '/', 2), '/', -1) problemName
    from
    (
    select
    aqi.id, aqi.create_time_ as create_time, aqi.problem_id_ as problem_id, lt.platform_project_name projectName
    from
    app_quality_inspection aqi left join linkapp_tenant lt on aqi.tenant_id_ = lt.id
    where lt.id is not null
    and aqi.create_time_ &gt;= #{startTime}
    and aqi.create_time_ &lt;= #{endTime}
    union all
    (
    select
    aqqi.id, aqqi.create_time as create_time, aqqi.question_describe_id as problem_id, lt.platform_project_name projectName
    from
    app_quality_question_info aqqi left join linkapp_tenant lt on aqqi.tenant_id  = lt.id
    where lt.id is not null and aqqi.delete_state = 1
    and aqqi.create_time &gt;= #{startTime}
    and aqqi.create_time &lt;= #{endTime}
    )) tt
    left join app_problem ap
    on
    tt.problem_id = ap.id
    where
    ap.full_name_ is not null) a
    where 1=1
    <if test="problemName != null and problemName != ''">
      and a.problemName = #{problemName}
    </if>
    group by a.projectName
    order by problemNum desc
  </select>
</mapper>
