<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.QualityConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.QualityConfig">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="rectify_" property="rectify" />
        <result column="check_" property="check" />
        <result column="check_end_" property="checkEnd" />
        <result column="rectify_expire_" property="rectifyExpire" />
        <result column="rectify_expire_param_" property="rectifyExpireParam" />
        <result column="rectify_over_" property="rectifyOver" />
        <result column="rectify_over_param_" property="rectifyOverParam" />
        <result column="rectify_upper_" property="rectifyUpper" />
        <result column="rectify_upper_param_" property="rectifyUpperParam" />
        <result column="rectify_upper_time_" property="rectifyUpperTime" />
        <result column="check_expire_" property="checkExpire" />
        <result column="check_expire_param_" property="checkExpireParam" />
        <result column="check_over_" property="checkOver" />
        <result column="check_over_param_" property="checkOverParam" />
        <result column="check_upper_" property="checkUpper" />
        <result column="check_upper_param_" property="checkUpperParam" />
        <result column="check_upper_time_" property="checkUpperTime" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

</mapper>
