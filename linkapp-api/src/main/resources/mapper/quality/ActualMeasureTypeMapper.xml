<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.ActualMeasureTypeMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.ActualMeasureType">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="full_name" jdbcType="VARCHAR" property="fullName"/>
        <result column="full_id" jdbcType="VARCHAR" property="fullId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="sort_no" jdbcType="INTEGER" property="sortNo"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
        <result column="enterprise_type_id" jdbcType="INTEGER" property="enterpriseTypeId"/>
        <result column="item_type" jdbcType="INTEGER" property="itemType"/>
    </resultMap>
    <resultMap id="ActualMeasureTypeDtoMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.quality.dto.ActualMeasureTypeDto">
        <collection property="itemList" column="id"
                    select="com.easylinkin.linkappapi.quality.mapper.ActualMeasureItemMapper.selectItemBy"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_quality_actual_measure_type
        where tenant_id = #{appQualityActualMeasureType.tenantId}
        and delete_state = 1
        <if test="appQualityActualMeasureType.parentId != null and appQualityActualMeasureType.parentId != ''">
            and parent_id = #{appQualityActualMeasureType.parentId}
        </if>
        order by sort_no, create_time
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_quality_actual_measure_type
        where id = #{id}
    </select>

    <select id="selectTypeDtoList" parameterType="com.easylinkin.linkappapi.quality.entity.vo.ActualMeasureTypeVo"
            resultMap="ActualMeasureTypeDtoMap">
        select aqamt.*
        from app_quality_actual_measure_type aqamt
        where aqamt.delete_state = 1
        <if test="tenantId != null and tenantId != ''">
            and aqamt.tenant_id = #{tenantId}
        </if>
        <if test="tenantId == null or tenantId == ''">
            and aqamt.tenant_id is null
        </if>
        order by aqamt.id
    </select>

    <update id="updateTypeFullName" parameterType="com.easylinkin.linkappapi.quality.entity.vo.ActualMeasureTypeVo">
        update app_quality_actual_measure_type
        set full_name = replace(full_name, #{oldName}, #{newName}),modify_time = #{modifyTime},modifier=#{modifier}
        where full_id like concat('%', #{fullId}, '%')
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}
        </if>
        <if test="tenantId == null or tenantId == ''">
            and tenant_id is null
        </if>
    </update>

    <select id="selectByParentId" resultMap="BaseResultMap">
        select * from app_quality_actual_measure_type t where t.parent_id = #{parentId}
    </select>

    <select id="actualMeasureTypeCount" parameterType="com.easylinkin.linkappapi.quality.entity.vo.QualityScreenPageVo"
            resultType="java.util.Map">
        select
        left(t.full_name,
        LOCATE('/', t.full_name)-1) as finName,
        count(*) as num
        from
        app_quality_actual_measure_type t
        left join
        app_quality_actual_measure_details d
        on
        t.id = d.type_id
        left join
        app_quality_actual_measure_info i
        on
        i.id = d.info_id
        left join linkapp_tenant lt2
        on i.tenant_id = lt2.id
        where
        i.storage_type = 2
        and d.delete_state = 1
        and i.delete_state = 1
        and lt2.id is not null
        <![CDATA[
          and i.measure_time >= #{startTime}
          and i.measure_time <= #{endTime}
            ]]>
        group by
        finName
        order by num desc
    </select>

    <select id="countTypeGroupByTeanant" parameterType="ArrayList" resultType="java.util.Map">
        select i.tenant_id                                   as tenantId,
        count(*)                                      as num,
        ifnull((select count(*)
        from app_quality_actual_measure_value v
        left join app_quality_actual_measure_item e on v.item_id = e.id
        left join app_quality_actual_measure_info f on v.info_id = f.id
        where f.tenant_id = i.tenant_id
        and v.actual_state = 1
        and f.storage_type = 2
        and f.delete_state = 1), 0)         as actualTypeOkNum,
        ifnull((select count(*)
        from app_quality_actual_measure_value v
        left join app_quality_actual_measure_item e on v.item_id = e.id
        left join app_quality_actual_measure_info f on v.info_id = f.id
        where f.tenant_id = i.tenant_id
        and f.storage_type = 2
        and f.delete_state = 1
        and v.actual_state is not null), 0) as actualTypeTotalNum
        from app_quality_actual_measure_details d
        left join app_quality_actual_measure_type t on
        d.type_id = t.id
        left join app_quality_actual_measure_info i
        on
        i.id = d.info_id

        where i.storage_type = 2
        and d.delete_state = 1
        and i.delete_state = 1
        <if test="tenantIds != null and tenantIds.size() != 0">
            and i.tenant_id in
            <foreach collection="tenantIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by i.tenant_id
    </select>

    <select id="actualMeasureCountTop" parameterType="com.easylinkin.linkappapi.quality.entity.vo.QualityScreenPageVo"
            resultType="java.util.Map">
        select lt.id,
        lt.platform_project_name                                                                   as platformProjectName,
        ifnull(tt.num, 0)                                                                          as num,
        tt.actualTypeOkNum,
        tt.actualTypeTotalNum,
        convert(ifnull(actualTypeOkNum, 0) * 100 / ifnull(actualTypeTotalNum, 1), decimal (10, 2)) as okRate
        from linkapp_tenant lt
        left join (select i.tenant_id                       as tenantId,
        count(*)                          as num,
        (select count(*)
        from app_quality_actual_measure_value v
        left join app_quality_actual_measure_item e on
        v.item_id = e.id
        left join app_quality_actual_measure_info f on
        v.info_id = f.id
        left join linkapp_tenant lt on
        f.tenant_id = lt.id
        where e.tenant_id = i.tenant_id
        and v.actual_state = 1
        and f.delete_state = 1
        and f.storage_type = 2
        and lt.id is not null
        <![CDATA[
                                              and f.measure_time >= #{startTime}
                                              and f.measure_time <= #{endTime}
                                        ]]>
        )        as actualTypeOkNum,
        (select count(*)
        from app_quality_actual_measure_value v
        left join app_quality_actual_measure_item e on
        v.item_id = e.id
        left join app_quality_actual_measure_info f on
        v.info_id = f.id
        left join linkapp_tenant lt on
        f.tenant_id = lt.id
        where e.tenant_id = i.tenant_id
        and v.actual_state is not null
        and f.storage_type = 2
        and f.delete_state = 1
        and lt.id is not null
        <![CDATA[
                                              and f.measure_time >= #{startTime}
                                              and f.measure_time <= #{endTime}
                                        ]]>
        ) as actualTypeTotalNum
        from app_quality_actual_measure_details d
        left join app_quality_actual_measure_type t on
        d.type_id = t.id
        left join app_quality_actual_measure_info i on
        i.id = d.info_id
        left join linkapp_tenant lt2 on
        i.tenant_id = lt2.id
        where i.storage_type = 2
        and d.delete_state = 1
        and i.delete_state = 1
        and lt2.id is not null
        <![CDATA[
                              and i.measure_time >= #{startTime}
                              and i.measure_time <= #{endTime}
                ]]>
        group by i.tenant_id) tt on
        lt.id = tt.tenantId
        order by okRate desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="actualMeasureTypeCountAndRate" resultType="java.util.Map">
        select ifnull(count(*), 0)                          as num,
               ifnull((select count(*)
                       from app_quality_actual_measure_value v
                                left join app_quality_actual_measure_item e on
                           v.item_id = e.id
                                left join app_quality_actual_measure_info f on
                           v.info_id = f.id
                                left join linkapp_tenant lt on
                           f.tenant_id = lt.id
                       where f.tenant_id = i.tenant_id
                         and v.actual_state = 1
                         and f.delete_state = 1
                         and f.storage_type = 2
                         and lt.id is not null
                      ), 0)        as actualTypeOkNum,
               ifnull((select count(*)
                       from app_quality_actual_measure_value v
                                left join app_quality_actual_measure_item e on
                           v.item_id = e.id
                                left join app_quality_actual_measure_info f on
                           v.info_id = f.id
                                left join linkapp_tenant lt on
                           f.tenant_id = lt.id
                       where f.tenant_id = i.tenant_id
                         and f.delete_state = 1
                         and f.storage_type = 2
                         and v.actual_state is not null
                         and lt.id is not null
                      ), 0) as actualTypeTotalNum
        from app_quality_actual_measure_details d
                 left join app_quality_actual_measure_type t on
            d.type_id = t.id
                 left join app_quality_actual_measure_info i
                           on i.id = d.info_id
                 left join linkapp_tenant lt2
                           on i.tenant_id = lt2.id
        where i.storage_type = 2
          and d.delete_state = 1
          and i.delete_state = 1
          and lt2.tenant_id is not null
    </select>
</mapper>
