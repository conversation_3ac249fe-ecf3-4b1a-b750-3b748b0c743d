<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.ActualMeasureDetailsMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.ActualMeasureDetails">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="info_id" jdbcType="BIGINT" property="infoId"/>
        <result column="type_id" jdbcType="BIGINT" property="typeId"/>
        <result column="imgs" jdbcType="VARCHAR" property="imgs"/>
        <result column="pass_rate" jdbcType="DECIMAL" property="passRate"/>
        <result column="actual_num" jdbcType="INTEGER" property="actualNum"/>
        <result column="qualified_num" jdbcType="INTEGER" property="qualifiedNum"/>
        <result column="breaking_num" jdbcType="INTEGER" property="breakingNum"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>
    <resultMap id="DetailsDtoResultMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.quality.dto.ActualMeasureDetailsDTO">
        <association property="actualMeasureType" column="type_id" select="com.easylinkin.linkappapi.quality.mapper.ActualMeasureTypeMapper.getOneById"/>
        <collection property="actualMeasureValueList" column="id" select="com.easylinkin.linkappapi.quality.mapper.ActualMeasureValueMapper.selectValueListByDetailsId" />
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_quality_actual_measure_details
        where tenant_id = #{appQualityActualMeasureDetails.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_quality_actual_measure_details
        where id = #{id}
    </select>

    <select id="selectDetailsDtoByInfoId" parameterType="long" resultMap="DetailsDtoResultMap">
        select aqamd.*
        from app_quality_actual_measure_details aqamd
        where aqamd.delete_state = 1
          and aqamd.info_id = #{infoId}
    </select>
</mapper>
