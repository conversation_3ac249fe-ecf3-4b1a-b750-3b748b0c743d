<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.QualityQuestionCommentMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.QualityQuestionComment">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="question_id" jdbcType="BIGINT" property="questionId"/>
        <result column="step" jdbcType="INTEGER" property="step"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>
    <resultMap id="QuestionCommentDtoMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.quality.dto.QualityQuestionCommentDto">
        <association property="appUser" column="creator" select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById" />
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_quality_question_comment
        where tenant_id = #{appQualityQuestionComment.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_quality_question_comment
        where id = #{id}
    </select>

    <select id="selectNumByQuestionId" parameterType="long" resultType="java.lang.Integer">
        select count(*)
        from app_quality_question_comment aqqc
        where aqqc.question_id = 1
          and aqqc.delete_state = #{questionId}
    </select>

    <select id="getQuestionComment" parameterType="long" resultMap="QuestionCommentDtoMap">
        select
            aqqc.*
        from
            app_quality_question_comment aqqc
        where
            aqqc.question_id = #{questionId}
          and aqqc.delete_state = 1
        order by aqqc.create_time
    </select>
</mapper>
