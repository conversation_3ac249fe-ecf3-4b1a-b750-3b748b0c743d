<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.QualityQuestionDealRecordMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.QualityQuestionDealRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="question_id" jdbcType="BIGINT" property="questionId" />
    <result column="deal_type" jdbcType="INTEGER" property="dealType" />
    <result column="step" jdbcType="INTEGER" property="step" />
    <result column="deal_time" jdbcType="TIMESTAMP" property="dealTime" />
    <result column="deal_memo" jdbcType="VARCHAR" property="dealMemo" />
    <result column="before_state" jdbcType="INTEGER" property="beforeState" />
    <result column="after_stste" jdbcType="INTEGER" property="afterStste" />
    <result column="pass_type" jdbcType="INTEGER" property="passType" />
    <result column="deal_imgs" jdbcType="VARCHAR" property="dealImgs" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
    <result column="nickname" jdbcType="VARCHAR" property="creatorName" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_quality_question_deal_record where tenant_id = #{appQualityQuestionDealRecord.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_quality_question_deal_record where id = #{id}
    </select>
  <select id="getByMonth"
    resultType="com.easylinkin.linkappapi.quality.dto.QualityScreenDTO">
    SELECT
     COUNT( DISTINCT a.question_id ) AS changeNum,
     DATE_FORMAT( a.deal_time, "%Y-%m-%d %H:00:00" ) AS startTime
    FROM
     app_quality_question_deal_record a
    LEFT JOIN app_quality_question_info b ON a.question_id = b.id
    WHERE
     b.tenant_id = #{tenantId}
     AND a.delete_state = 1
     AND a.deal_type = 1
     AND DATE_SUB( CURDATE( ), INTERVAL 30 DAY ) &lt; date( a.deal_time )
     GROUP BY
     DATE_FORMAT( a.deal_time, '%Y-%m-%d' );
  </select>
  <select id="findByTenantId" resultMap="BaseResultMap">
    SELECT
      a.*
    FROM
      app_quality_question_deal_record a
      LEFT JOIN app_quality_question_info b ON a.question_id = b.id
    WHERE
      b.tenant_id = #{tenantId}
      AND a.delete_state = 1
      AND a.deal_type = 1
    ORDER BY
      a.deal_time
  </select>

  <select id="selectDealListByQuestionId" resultMap="BaseResultMap">
      select
          aqqdr.*,
          u.nickname
      from
          app_quality_question_deal_record aqqdr
          left JOIN linkapp_user u on aqqdr.creator = u.id
      where
          aqqdr.delete_state = 1
        and aqqdr.question_id = #{questionId}
      order by aqqdr.step
  </select>
</mapper>
