<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.QualityPositionMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.QualityPosition">
        <id column="id" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="parent_id_" property="parentId"/>
        <result column="full_id_" property="fullId"/>
        <result column="full_name_" property="fullName"/>
        <result column="name_" property="name"/>
        <result column="code_" property="code"/>
        <result column="level_" property="level"/>
        <result column="rectify_id_" property="rectifyId"/>
        <result column="notice_id_" property="noticeId"/>
        <result column="subcontractor_" property="subcontractor"/>
        <result column="subcontractor_type_" property="subcontractorType"/>
        <result column="order_" property="order"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
        <result column="type_" property="type"/>
    </resultMap>
    <resultMap id="ActualMeasurePositionResultMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.quality.dto.ActualMeasurePositionDTO">
        <association property="actualMeasureNum" column="{positionId=id,tenantId=tenant_id_}"
                     select="com.easylinkin.linkappapi.quality.mapper.ActualMeasureInfoMapper.selectCountBy"/>
    </resultMap>
    <resultMap id="ConcretePositionResultMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.concretestrength.dto.ConcreteStrengthPositionDTO">
        <association property="concreteStrengthNum" column="{positionId=id,tenantId=tenant_id_}"
                     select="com.easylinkin.linkappapi.concretestrength.mapper.ConcreteStrengthInfoMapper.selectCountBy"/>
    </resultMap>

    <select id="queryListWithMeasureNum" parameterType="com.easylinkin.linkappapi.quality.dto.QualityPositionDTO"
            resultMap="ActualMeasurePositionResultMap">
        select aqp.*
        from app_quality_position aqp
        where 1 = 1
          and aqp.type_ = 1
        <if test="tenantId != null and tenantId != ''">
            and aqp.tenant_id_ = #{tenantId}
        </if>
        <if test="fullName != null and fullName != ''">
            and aqp.full_name_ like concat('%', #{fullName}, '%')
        </if>
    </select>

    <select id="queryListWithConcreteNum" parameterType="com.easylinkin.linkappapi.quality.dto.QualityPositionDTO"
            resultMap="ConcretePositionResultMap">
        select aqp.*
        from app_quality_position aqp
        where 1 = 1
          and aqp.type_ = 1
        <if test="tenantId != null and tenantId != ''">
            and aqp.tenant_id_ = #{tenantId}
        </if>
        <if test="fullName != null and fullName != ''">
            and aqp.full_name_ like concat('%', #{fullName}, '%')
        </if>
    </select>

    <select id="queryByParam" parameterType="com.easylinkin.linkappapi.quality.entity.vo.QualityPositionVo"
            resultMap="BaseResultMap">
        select p.*
        from app_quality_position p
        where p.tenant_id_ = #{tenantId}
        <if test="type != null">
            and p.type_ = #{type}
        </if>
        <if test="paramKey != null and paramKey != ''">
            and locate(concat('/', p.id, '/'),
            (
            select GROUP_CONCAT(aqp.full_id_)
            from app_quality_position aqp
            where aqp.tenant_id_ = #{tenantId}
            <if test="type != null">
                and aqp.type_ = #{type}
            </if>
            and aqp.full_name_ like concat('%', #{paramKey}, '%'))) > 0
        </if>
    </select>
</mapper>
