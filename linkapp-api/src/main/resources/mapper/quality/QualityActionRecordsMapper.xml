<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.QualityActionRecordsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.QualityActionRecords">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="link_id_" property="linkId" />
        <result column="action_type_" property="actionType" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <resultMap id="BaseResultMapDTO" type="com.easylinkin.linkappapi.quality.dto.QualityActionRecordsDTO" extends="BaseResultMap">
        <result column="nickname" property="creatorName" />
        <result column="address" property="address" />
    </resultMap>


    <select id="queryListByPage" resultMap="BaseResultMapDTO">
        SELECT
            a.*,
            b.nickname,
            b.address
        FROM
            app_quality_action_records a
        LEFT JOIN linkapp_user b ON a.creator_id_ = b.id
        <where>
            <if test="entity.linkId != null">
                AND a.link_id_ = #{entity.linkId}
            </if>
            <if test="entity.id != null">
                AND a.id = #{entity.id}
            </if>
        </where>
        ORDER BY
            a.create_time_ DESC
    </select>

</mapper>
