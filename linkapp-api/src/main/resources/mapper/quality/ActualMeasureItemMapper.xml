<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.ActualMeasureItemMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.ActualMeasureItem">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="measure_type_id" jdbcType="BIGINT" property="measureTypeId"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="qualified_standard" jdbcType="VARCHAR" property="qualifiedStandard"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="standard_type" jdbcType="INTEGER" property="standardType"/>
        <result column="cal_point_num" jdbcType="INTEGER" property="calPointNum"/>
        <result column="group_cal_point_num" jdbcType="INTEGER" property="groupCalPointNum"/>
        <result column="breaking_point_standard" jdbcType="VARCHAR" property="breakingPointStandard"/>
        <result column="measure_memo" jdbcType="VARCHAR" property="measureMemo"/>
        <result column="algorithm" jdbcType="INTEGER" property="algorithm"/>
        <result column="design_value" jdbcType="VARCHAR" property="designValue"/>
        <result column="sort_no" jdbcType="INTEGER" property="sortNo"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
        <result column="enterprise_item_id" jdbcType="BIGINT" property="enterpriseItemId"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_quality_actual_measure_item where delete_state = 1
        <if test="appQualityActualMeasureItem.tenantId != null and appQualityActualMeasureItem.tenantId != ''">
            and tenant_id = #{appQualityActualMeasureItem.tenantId}
        </if>
        <if test="appQualityActualMeasureItem.measureTypeId != null">
            and measure_type_id = #{appQualityActualMeasureItem.measureTypeId}
        </if>
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_quality_actual_measure_item
        where id = #{id}
    </select>

    <select id="selectItemBy" resultMap="BaseResultMap">
        select aqami.*
        from app_quality_actual_measure_item aqami where aqami.delete_state = 1
        <if test="measureTypeId != null">
            and aqami.measure_type_id = #{measureTypeId}
        </if>
    </select>

    <select id="selectUseInfoNum" parameterType="com.easylinkin.linkappapi.quality.entity.vo.ActualMeasureItemVo" resultType="int">
        select
            count(*)
        from
            app_quality_actual_measure_type b ,
            app_quality_actual_measure_item c ,
            app_quality_actual_measure_details d
        where
            b.id = d.type_id
          and c.measure_type_id = b.id
            <if test="measureTypeId != null">
                and b.id = #{measureTypeId}
            </if>
          <if test="id != null">
              and c.id = #{id}
          </if>
    </select>
</mapper>
