<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.ProblemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.Problem">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="problem_type_id_" property="problemTypeId" />
        <result column="full_id_" property="fullId" />
        <result column="full_name_" property="fullName" />
        <result column="level_" property="level" />
        <result column="content_" property="content" />
        <result column="change_requirement_" property="changeRequirement" />
        <result column="change_Limit_" property="changeLimit" />
        <result column="disable_" property="disable" />
        <result column="code_" property="code" />
        <result column="order_" property="order" />
        <result column="is_default_" property="isDefault" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="src_id_" property="srcId" />
    </resultMap>

    <update id="updateByTypeNameChange">
        update app_problem
        set full_name_ = concat(#{newNameBefore,jdbcType=VARCHAR}, substr(full_name_, (#{len} + 1)))
        where full_name_ like concat(#{typeFullName,jdbcType=VARCHAR}, '/%') and tenant_id_ is null
    </update>

    <delete id="deleteByTypeFullName">
        delete
        from app_problem
        <where>
            tenant_id_ is null
            and full_name_ like concat(#{fullName,jdbcType=VARCHAR}, '/%')
        </where>
    </delete>

    <update id="updateOfSync">
        update app_problem apt inner join app_problem_type apt2 on apt.problem_type_id_ = apt2.src_id_ and
        apt.tenant_id_ = apt2.tenant_id_
        set apt.problem_type_id_ = apt2.id
        where apt.id in
        <foreach collection="needUpdateIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>
