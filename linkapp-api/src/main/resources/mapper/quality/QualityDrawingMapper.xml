<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.quality.mapper.QualityDrawingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.quality.entity.QualityDrawing">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="position_id_" property="positionId" />
        <result column="name_" property="name" />
        <result column="url_" property="url" />
        <result column="size_" property="size" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>
    <resultMap id="BaseResultMapDTO" type="com.easylinkin.linkappapi.quality.dto.QualityDrawingDTO" extends="BaseResultMap">
        <result column="nickname" property="creatorName" />
    </resultMap>

  <select id="queryListByPage" resultMap="BaseResultMapDTO">
    SELECT
      a.*,
      b.nickname
    FROM
      app_quality_drawing a
      LEFT JOIN linkapp_user b ON a.creator_id_ = b.id
    WHERE
      a.tenant_id_ = #{qualityDrawing.tenantId}
      AND a.position_id_ = #{qualityDrawing.positionId}
    ORDER BY
      a.modify_time_ DESC
  </select>

</mapper>
