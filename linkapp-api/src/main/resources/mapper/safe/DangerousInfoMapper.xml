<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.safe.dao.DangerousInfoMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.safe.entity.DangerousInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="describe_str" jdbcType="VARCHAR" property="describeStr"/>
        <result column="type_id" jdbcType="BIGINT" property="typeId"/>
        <result column="type_describe_id" jdbcType="BIGINT" property="typeDescribeId"/>
        <result column="super_dangerous" jdbcType="INTEGER" property="superDangerous"/>
        <result column="work_area_responsible" jdbcType="VARCHAR" property="workAreaResponsible"/>
        <result column="sub_area_responsible" jdbcType="VARCHAR" property="subAreaResponsible"/>
        <result column="sub_responsible_org" jdbcType="VARCHAR" property="subResponsibleOrg"/>
        <result column="plan_start_time" jdbcType="TIMESTAMP" property="planStartTime"/>
        <result column="plan_end_time" jdbcType="TIMESTAMP" property="planEndTime"/>
        <result column="actual_start_time" jdbcType="TIMESTAMP" property="actualStartTime"/>
        <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime"/>
        <result column="program_state" jdbcType="INTEGER" property="programState"/>
        <result column="construction_state" jdbcType="INTEGER" property="constructionState"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>
    <resultMap id="DangerousInfoDtoMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.safe.dto.DangerousInfoDto">
        <association property="typeInfo" column="type_id"
                     select="com.easylinkin.linkappapi.safe.dao.DangerousDictMapper.getOneById"/>
        <association property="typeDescribeInfo" column="type_describe_id"
                     select="com.easylinkin.linkappapi.safe.dao.DangerousDictMapper.getOneById"/>
        <collection property="workAreaResponsibleList" ofType="com.easylinkin.linkappapi.security.entity.LinkappUser"
                    column="work_area_responsible"
                    select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectUsersByIdsStr"/>
        <collection property="subAreaResponsibleList" column="sub_area_responsible"
                    select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectUsersByIdsStr"/>
        <collection property="subResponsibleOrgList" column="sub_responsible_org"
                    select="com.easylinkin.linkappapi.lobar.mapper.LaborCompanyMapper.selectCompanysByIdsStr"/>
    </resultMap>
    <resultMap id="DangerousInfoDtoAndProcessFileMap" extends="DangerousInfoDtoMap" type="com.easylinkin.linkappapi.safe.dto.DangerousInfoDto">
        <collection property="processList" column="id" select="com.easylinkin.linkappapi.safe.dao.DangerousProcessMapper.selectProcessListByDangerousId"/>
        <collection property="dangerousFileList" column="id" select="com.easylinkin.linkappapi.safe.dao.DangerousFileMapper.selectProcessFileByDangerousId"/>
        <collection property="hiddenDangerDTOList" column="id" select="com.easylinkin.linkappapi.safe.dao.HiddenDangerMapper.selectHiddenDangerByDangerousRef" />
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_dangerous_info
        where tenant_id = #{appDangerousInfo.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_dangerous_info
        where id = #{id}
    </select>

    <select id="selectDtoPage" parameterType="com.easylinkin.linkappapi.safe.entity.vo.DangerousInfoVo"
            resultMap="DangerousInfoDtoMap">
        select adi.*
        from app_dangerous_info adi
        where adi.delete_state = 1
        <if test="param2.tenantId != null and param2.tenantId != ''">
            and adi.tenant_id = #{param2.tenantId}
        </if>
        <if test="param2.programState != null">
            and adi.program_state = #{param2.programState}
        </if>
        <if test="param2.constructionState != null">
            and adi.construction_state = #{param2.constructionState}
        </if>
        <if test="param2.superDangerous != null">
            and adi.super_dangerous = #{param2.superDangerous}
        </if>
        <if test="param2.name != null and param2.name != ''">
            and adi.name LIKE CONCAT('%', #{param2.name}, '%')
        </if>
        <if test="param2.idList != null and param2.idList.size() != 0">
            and adi.id in
            <foreach collection="param2.idList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        order by adi.create_time desc
    </select>

    <select id="selectOneDtoById" parameterType="long" resultMap="DangerousInfoDtoAndProcessFileMap">
        select adi.*
        from app_dangerous_info adi
        where adi.id = #{id}
    </select>

    <select id="selectDangerousStatistic" resultType="com.easylinkin.linkappapi.openapi.dto.BigScreenStatisticDTO">
        select add2.memo as name,
               count(*)  as countNum
        from app_dangerous_info adi
                 inner join app_dangerous_dict add2 on add2.id = adi.type_id
                left join linkapp_tenant lt on
                    adi.tenant_id = lt.id
            <where>
                adi.delete_state=1
                and adi.actual_end_time is null
                and lt.id is not null
                <if test="dangerousInfo!=null and dangerousInfo.superDangerous!=null">
                    and adi.super_dangerous = #{dangerousInfo.superDangerous,jdbcType=INTEGER}
                </if>
            </where>
        group by add2.memo
        order by countNum desc,name
    </select>

    <select id="selectDangerProjectNum" resultType="com.easylinkin.linkappapi.openapi.dto.ProjectStatisticDTO">
        select
            lt.project_id as id,
        count(*)  as dangerProjectNum
        from app_dangerous_info adi
            inner join linkapp_tenant lt on adi.tenant_id = lt.id
        where adi.delete_state=1
        group by lt.project_id
    </select>

</mapper>
