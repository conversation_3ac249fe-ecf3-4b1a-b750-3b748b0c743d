<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.safe.dao.InspectionTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.safe.entity.InspectionTask">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="plan_id_" property="planId" />
        <result column="start_time_" property="startTime" />
        <result column="end_time_" property="endTime" />
        <result column="status_" property="status" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>
    <resultMap id="BaseResultMapDTO" type="com.easylinkin.linkappapi.safe.dto.InspectionTaskDTO" extends="BaseResultMap">
        <result column="user_ids_" property="userIds" />
        <result column="name_" property="name" />
        <result column="type_" property="type" />
    </resultMap>
  <select id="queryListByPage" resultMap="BaseResultMapDTO">
    SELECT
      a.id,
      a.tenant_id_,
      a.plan_id_,
      a.start_time_,
      a.end_time_,
      a.status_,
      b.creator_id_,
      a.create_time_,
      a.modify_id_,
      a.modify_time_,
      b.remark_,
      b.user_ids_,
      b.name_,
      b.type_
    FROM
      app_inspection_task a
      LEFT JOIN app_inspection_plan b ON a.plan_id_ = b.id
    WHERE
      a.tenant_id_ = #{inspectionTask.tenantId}
      <if test="inspectionTask.planId != null ">
          AND a.plan_id_ = #{inspectionTask.planId}
      </if>
      <if test="inspectionTask.id != null ">
          AND a.id = #{inspectionTask.id}
      </if>
      <if test="inspectionTask.creatorId != null ">
          AND find_in_set(#{inspectionTask.creatorId}, b.user_ids_ )
      </if>
      <if test="inspectionTask.type != null ">
        AND b.type_ = #{inspectionTask.type}
      </if>
      <if test="inspectionTask.status != null ">
        AND a.status_ = #{inspectionTask.status}
      </if>
      <if test="inspectionTask.startTime != null ">
        AND a.create_time_ &gt;= #{inspectionTask.startTime}
      </if>
      <if test="inspectionTask.endTime != null ">
        AND a.create_time_ &lt;=  #{inspectionTask.endTime}
      </if>
      <if test="inspectionTask.myType != null ">
        AND (a.status_ = 1 or a.status_ = 2 and a.end_time_ &gt;= now())
      </if>
    ORDER BY
      a.create_time_ DESC
  </select>

</mapper>
