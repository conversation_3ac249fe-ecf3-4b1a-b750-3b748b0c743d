<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.safe.dao.DangerousFileMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.safe.entity.DangerousFile">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dangerous_id" jdbcType="BIGINT" property="dangerousId"/>
        <result column="process_id" jdbcType="BIGINT" property="processId"/>
        <result column="step_no" jdbcType="INTEGER" property="stepNo"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="file_size" jdbcType="INTEGER" property="fileSize"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_dangerous_file
        where tenant_id = #{appDangerousFile.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_dangerous_file
        where id = #{id}
    </select>

    <select id="selectProcessFileByDangerousId" parameterType="long" resultMap="BaseResultMap">
        select adf.*
        from app_dangerous_file adf
        where adf.delete_state = 1
          and adf.dangerous_id = #{dangerousInfoId}
        order by adf.step_no
    </select>
</mapper>
