<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.safe.dao.HiddenConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.safe.entity.HiddenConfig">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="rectify_" property="rectify" />
        <result column="check_" property="check" />
        <result column="check_end_" property="checkEnd" />
        <result column="task_expire_" property="taskExpire" />
        <result column="expire_param_" property="expireParam" />
        <result column="task_over_" property="taskOver" />
        <result column="over_param_" property="overParam" />
        <result column="task_upper_" property="taskUpper" />
        <result column="upper_param_" property="upperParam" />
        <result column="upper_time_" property="upperTime" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

</mapper>
