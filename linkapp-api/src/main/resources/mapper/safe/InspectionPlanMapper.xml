<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.safe.dao.InspectionPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.safe.entity.InspectionPlan">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="name_" property="name" />
        <result column="type_" property="type" />
        <result column="basis_" property="basis" />
        <result column="user_ids_" property="userIds" />
        <result column="start_time_" property="startTime" />
        <result column="end_time_" property="endTime" />
        <result column="status_" property="status" />
        <result column="publishing_" property="publishing" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>
    <resultMap id="BaseResultMapDto" type="com.easylinkin.linkappapi.safe.dto.InspectionPlanDTO" extends="BaseResultMap">
        <result column="nickname" property="creatorName" />
    </resultMap>
    <select id="queryListByPage" resultMap="BaseResultMapDto">
        SELECT
          a.*,
          b.nickname
        FROM
          app_inspection_plan a
          LEFT JOIN linkapp_user b ON a.creator_id_ = b.id
        WHERE
          a.tenant_id_ = #{inspectionPlan.tenantId}
        <if test="inspectionPlan.name != null and inspectionPlan.name != ''">
            AND a.name_ like  CONCAT('%',#{inspectionPlan.name},'%')
        </if>
        <if test="inspectionPlan.status != null">
            AND a.status_ = #{inspectionPlan.status}
        </if>
        <if test="inspectionPlan.type != null">
            AND a.type_ = #{inspectionPlan.type}
        </if>
        ORDER BY a.create_time_ DESC
    </select>

</mapper>
