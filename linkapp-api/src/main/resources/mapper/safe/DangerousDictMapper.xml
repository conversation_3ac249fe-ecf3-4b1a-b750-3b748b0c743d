<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.safe.dao.DangerousDictMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.safe.entity.DangerousDict">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="dangerous_type" jdbcType="INTEGER" property="dangerousType" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="super_dangerous" jdbcType="INTEGER" property="superDangerous" />
    <result column="sort_no" jdbcType="INTEGER" property="sortNo" />
    <result column="use_state" jdbcType="INTEGER" property="useState" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
  </resultMap>
  <resultMap id="DangerousDtoMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.safe.dto.DangerousDictDto">
    <association property="parentInfo" column="parent_id" select="com.easylinkin.linkappapi.safe.dao.DangerousDictMapper.getOneById" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_dangerous_dict where tenant_id = #{appDangerousDict.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_dangerous_dict where id = #{id}
    </select>

  <select id="getDataByParam" parameterType="com.easylinkin.linkappapi.safe.entity.DangerousDict"
          resultMap="DangerousDtoMap">
    select d.*
    from app_dangerous_dict d
    <where>
      <if test="param2.dangerousType != null">
        and d.dangerous_type = #{param2.dangerousType}
      </if>
      <if test="param2.parentId != null">
        and d.parent_id = #{param2.parentId}
      </if>
    </where>
    order by d.sort_no
  </select>
</mapper>
