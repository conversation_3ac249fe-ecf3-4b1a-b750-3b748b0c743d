<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.safe.dao.DangerousProcessMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.safe.entity.DangerousProcess">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dangerous_id" jdbcType="BIGINT" property="dangerousId" />
    <result column="step_no" jdbcType="INTEGER" property="stepNo" />
    <result column="process_name" jdbcType="INTEGER" property="processName" />
    <result column="process_state" jdbcType="INTEGER" property="processState" />
    <result column="change_end_time" jdbcType="TIMESTAMP" property="changeEndTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_dangerous_process where tenant_id = #{appDangerousProcess.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_dangerous_process where id = #{id}
    </select>

  <select id="selectProcessListByDangerousId" parameterType="long" resultMap="BaseResultMap">
    select
      adp.*
    from
      app_dangerous_process adp
    where
      adp.delete_state = 1
      and adp.dangerous_id = #{dangerousInfoId}
    order by
      adp.step_no
  </select>
</mapper>
