<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.safe.dao.DangerousDangerRefMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.safe.entity.DangerousDangerRef">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dangerous_id" jdbcType="BIGINT" property="dangerousId" />
    <result column="danger_id" jdbcType="VARCHAR" property="dangerId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_dangerous_danger_ref where tenant_id = #{appDangerousDangerRef.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_dangerous_danger_ref where id = #{id}
    </select>


</mapper>
