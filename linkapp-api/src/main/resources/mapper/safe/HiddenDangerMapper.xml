<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.easylinkin.linkappapi.safe.dao.HiddenDangerMapper">
	<resultMap id="BaseResultMap"
		type="com.easylinkin.linkappapi.safe.entity.HiddenDanger">
		<!-- WARNING - @mbg.generated -->
		<result column="id" jdbcType="VARCHAR" property="id" />
		<result column="hidden_danger_id" jdbcType="VARCHAR"
			property="hiddenDangerId" />
		<result column="is_scene_rectify" jdbcType="INTEGER"
			property="isSceneRectify" />
		<result column="supplement_remarks" jdbcType="VARCHAR"
			property="supplementRemarks" />
		<result column="rectify_requirements" jdbcType="VARCHAR"
			property="rectifyRequirements" />
		<result column="link_unit_id" jdbcType="VARCHAR"
			property="linkUnitId" />
		<result column="rectify_uid" jdbcType="VARCHAR"
			property="rectifyUid" />
		<result column="rectify_end_time" jdbcType="TIMESTAMP"
			property="rectifyEndTime" />
		<result column="check_uid" jdbcType="VARCHAR"
			property="checkUid" />
		<result column="scene_photo" jdbcType="VARCHAR"
			property="scenePhoto" />
		<result column="is_overdue" jdbcType="INTEGER"
			property="isOverdue" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="create_uid" jdbcType="VARCHAR"
			property="createUid" />
		<result column="create_time" jdbcType="TIMESTAMP"
			property="createTime" />
		<result column="check_part_id" jdbcType="INTEGER" property="checkPartId" />
		<result column="noticer_ids" jdbcType="VARCHAR" property="noticerIds" />
		<result column="noticer_names" jdbcType="VARCHAR" property="noticerNames" />
		<result column="task_id_" jdbcType="INTEGER" property="taskId" />
		<result column="type_" jdbcType="INTEGER" property="type" />
		<result column="enterprise_check_type" jdbcType="INTEGER" property="enterpriseCheckType" />
		<result column="enterprise_source_type" jdbcType="INTEGER" property="enterpriseSourceType" />
		<result column="enterprise_create_user_name" jdbcType="VARCHAR" property="enterpriseCreateUserName" />
		<result column="enterprise_organization_id" jdbcType="BIGINT" property="enterpriseOrganizationId" />
		<result column="enterprise_create_user_id" jdbcType="BIGINT" property="enterpriseCreateUserId" />
		<result column="enterprise_safety_check_id" jdbcType="BIGINT" property="enterpriseSafetyCheckId" />
		<result column="enterprise_create_user_phone" jdbcType="VARCHAR" property="enterpriseCreateUserPhone" />
	</resultMap>

	<resultMap id="BaseResultDTO"
		type="com.easylinkin.linkappapi.safe.dto.HiddenDangerDTO"
		extends="BaseResultMap">
		<result column="creator" property="creator" />
		<result column="rectifer" property="rectifer" />
		<result column="checker" property="checker" />
		<result column="createPhone" property="createPhone" />
		<result column="rectiferPhone" property="rectiferPhone" />
		<result column="checkerPhone" property="checkerPhone" />
		<result column="beOverdue" property="beOverdue" />
		<result column="unitName" property="unitName" />
		<result column="content" property="content" />
		<result column="level" property="level" />
		<result column="fullName" property="fullName" />
		<result column="checkPartFullName" property="checkPartFullName"/>
	</resultMap>

	<resultMap id="BaseResultCountDTO"
		type="com.easylinkin.linkappapi.safe.dto.HiddenDangerCountDTO"
		extends="BaseResultMap">
		<result column="totalNum" property="totalNum" />
		<result column="waitRectifyNum" property="waitRectifyNum" />
		<result column="waitCheckNum" property="waitCheckNum" />
		<result column="qualifiedNum" property="qualifiedNum" />
		<result column="overdunNum" property="overdunNum" />
		<result column="inTimeFinishedNum" property="inTimeFinishedNum" />
		<result column="linkUnitName" property="linkUnitName" />
		<result column="fullName" property="fullName" />
		<result column="username" property="userName" />
		<result column="nickname" property="nickName" />
		<result column="address" property="address" />
		<result column="createTimeStr" property="createTimeStr" />
		<result column="level" property="level" />
		<result column="dangerTypeId" property="dangerTypeId" />
		<result column="finishRate" property="finishRate" />
		<result column="inTimeFinishedRate"
			property="inTimeFinishedRate" />
	</resultMap>
	<resultMap id="TenantBaseResultCountDTO" extends="BaseResultCountDTO" type="com.easylinkin.linkappapi.safe.dto.HiddenDangerCountDTO">
		<association property="linkappTenant" column="tenant_id" select="com.easylinkin.linkappapi.tenant.mapper.LinkappTenantMapper.getById" />
	</resultMap>

	<select id="selectPage" resultMap="BaseResultMap">
		select * from
		app_hidden_danger
		where 1=1 and type_ = 1 order by create_time desc
	</select>

	<select id="selectListByPage" resultMap="BaseResultDTO">
		select a.*,(case when ((a.`status`!=2 and a.rectify_end_time &lt;
		NOW()) or (a.`status`=2 and a.close_time &gt; a.rectify_end_time))
		then 1 else 0 end ) as beOverdue,b.nickname as creator,b.phone
		as
		createPhone,b1.nickname as
		rectifer,b1.phone as rectiferPhone,
		b2.nickname as checker,b2.phone as
		checkerPhone,lc.name_ as
		unitName,d.`level`,d.content,t.full_name as fullName,
		qp.full_name_ AS checkPartFullName
		from
		app_hidden_danger a
		LEFT JOIN linkapp_user b on
		a.create_uid=b.id
		left
		JOIN linkapp_user b1
		on
		a.rectify_uid=b1.id
		left
		JOIN linkapp_user b2 on
		a.check_uid=b2.id
		left join app_labor_company lc on
		a.link_unit_id=lc.id
		left join app_danger d on a.hidden_danger_id=d.id
		left join app_danger_type t on d.danger_type_id=t.danger_type_id
		left join app_quality_position qp ON qp.id = a.check_part_id
		left join linkapp_tenant lt on a.tenant_id = lt.id
		left join app_danger_type adt
		on left (d.full_id,locate('/',d.full_id)-1) = adt.full_id
		AND a.tenant_id = adt.tenant_id
		where
		1=1
		and lt.id is not null
		<if test="hiddenDangerDTO != null">
			<if test="hiddenDangerDTO.enterpriseSafetyCheckId != null">
				and a.enterprise_safety_check_id = #{hiddenDangerDTO.enterpriseSafetyCheckId}
			</if>
			<if test="hiddenDangerDTO.paramKey != null and hiddenDangerDTO.paramKey != ''">
				and (a.enterprise_create_user_name like concat('%', #{hiddenDangerDTO.paramKey}, '%') or
					 a.create_uid in (select lu.id
									  from linkapp_user lu
									  where lu.delete_state = 1
										and lu.nickname like concat('%', #{hiddenDangerDTO.paramKey}, '%')))
			</if>
			<if test="hiddenDangerDTO.level != null">
				and d.level = #{hiddenDangerDTO.level}
			</if>
			<if test="hiddenDangerDTO.enterpriseCheckType != null">
				and a.enterprise_check_type = #{hiddenDangerDTO.enterpriseCheckType}
			</if>
			<if test="hiddenDangerDTO.dangerTypeName != null and hiddenDangerDTO.dangerTypeName != ''">
				and t.full_name like concat('%',#{hiddenDangerDTO.dangerTypeName},'%')
			</if>
			<if test="hiddenDangerDTO.linkappTenant != null">
				<if test="hiddenDangerDTO.linkappTenant.platformProjectName != null and hiddenDangerDTO.linkappTenant.platformProjectName != '' ">
					and a.tenant_id in (select lt.id from linkapp_tenant lt where lt.platform_project_name like concat('%',#{hiddenDangerDTO.linkappTenant.platformProjectName},'%'))
				</if>
			</if>
		</if>
		<if test="hiddenDangerDTO.type != null">
			and a.type_ = #{hiddenDangerDTO.type}
		</if>
		<if test="hiddenDangerDTO.taskId != null ">
			and a.task_id_ = #{hiddenDangerDTO.taskId}
		</if>
		<if test="hiddenDangerDTO.id != null ">
			and a.id=#{hiddenDangerDTO.id}
		</if>
		<if test="hiddenDangerDTO.status != null ">
			and a.status=#{hiddenDangerDTO.status}
		</if>
		<if test="hiddenDangerDTO.startTime != null ">
			<![CDATA[ and a.create_time >= #{hiddenDangerDTO.startTime} ]]>
		</if>
		<if test="hiddenDangerDTO.endTime != null">
			<![CDATA[ and a.create_time <= #{hiddenDangerDTO.endTime} ]]>
		</if>
		<if
			test="hiddenDangerDTO.linkUnitId != null and hiddenDangerDTO.linkUnitId !=''">
			and a.link_unit_id= #{hiddenDangerDTO.linkUnitId}
		</if>
		<if
			test="hiddenDangerDTO.rectifyUid != null and hiddenDangerDTO.rectifyUid !=''">
			and a.rectify_uid= #{hiddenDangerDTO.rectifyUid}
		</if>
		<if
			test="hiddenDangerDTO.checkUid != null and hiddenDangerDTO.checkUid !=''">
			and a.check_uid= #{hiddenDangerDTO.checkUid}
		</if>
		<if
			test="hiddenDangerDTO.tenantId != null and hiddenDangerDTO.tenantId !=''">
			and a.tenant_id= #{hiddenDangerDTO.tenantId}
		</if>
		<if
			test="hiddenDangerDTO.content != null and hiddenDangerDTO.content != ''">
			and d.content like CONCAT('%', #{hiddenDangerDTO.content}, '%')
		</if>
		<if test="hiddenDangerDTO.organizationIds != null and hiddenDangerDTO.organizationIds != ''">
			and a.enterprise_organization_id in (${hiddenDangerDTO.organizationIds})
		</if>
		<if test="hiddenDangerDTO.linkappTenantList != null and hiddenDangerDTO.linkappTenantList.size() != 0">
			and a.tenant_id in
			<foreach collection="hiddenDangerDTO.linkappTenantList" item="item" index="index" open="(" separator="," close=")">
				#{item.id}
			</foreach>
		</if>
		<if test="hiddenDangerDTO.dangerTypeId != null">
			and adt.full_id = #{hiddenDangerDTO.dangerTypeId}
		</if>
		<if test="hiddenDangerDTO.createTimeStr!= null and hiddenDangerDTO.createTimeStr!= ''">
			and DATE_FORMAT(a.create_time,   '%Y-%m-%d') = #{hiddenDangerDTO.createTimeStr}
		</if>
		order by create_time desc
	</select>

	<select id="getOneById" resultMap="BaseResultMap">
		select * from
		app_hidden_danger
		where id = #{id}
	</select>

	<select id="getStatistics" resultMap="BaseResultCountDTO">
		select count(0) as
		totalNum,
		ifNull(sum(case when status=0 then 1 else 0
		end),0) as
		waitRectifyNum,
		ifNull(sum(case when status=1 then 1 else 0
		end),0) as
		waitCheckNum,
		ifNull(sum(case
		when status=2 then 1 else 0
		end),0) as
		qualifiedNum,
		ifNull(sum(case when
		(rectify_end_time &lt;
		NOW() and
		`status`!=2) then 1 else 0 end),0) as
		overdunNum,
		ifNull(sum(case when
		(close_time
		&lt;= rectify_end_time and `status`=2)
		then 1 else 0
		end),0) as
		inTimeFinishedNum
		from app_hidden_danger a
		left join app_danger d on a.hidden_danger_id=d.id
		<where>
		-- 		暂时查隐患
		a.type_ = 1
		<if test="tenantId != null and tenantId != ''">
			and a.tenant_id=#{tenantId}
		</if>
		<if test="endTime!=null">
			and a.create_time &lt;= #{endTime}
		</if>
		<if test="startTime!=null">
			and a.create_time &gt;= #{startTime}
		</if>
		<if test="content != null and content != ''">
			and d.content like CONCAT('%', #{content}, '%')
		</if>
		<if test="linkUnitId != null and linkUnitId != ''">
			and a.link_unit_id =#{linkUnitId}
		</if>
		<if test="rectifyUid != null and rectifyUid != ''">
			and a.rectify_uid =#{rectifyUid}
		</if>
		</where>
	</select>

	<select id="getUnitIdStatistics" resultMap="BaseResultCountDTO">
		select link_unit_id,company.name_ as linkUnitName,count(0) as
		totalNum,
		ifNull(sum(case when status=0 then 1 else 0
		end),0) as
		waitRectifyNum,
		ifNull(sum(case when status=1 then 1 else 0
		end),0) as
		waitCheckNum,
		ifNull(sum(case
		when status=2 then 1 else 0
		end),0) as
		qualifiedNum,
		ifNull(sum(case when
		(rectify_end_time &lt;
		NOW() and
		`status`!=2) then 1 else 0 end),0) as
		overdunNum,
		ifNull(sum(case when
		(close_time
		&lt;= rectify_end_time and `status`=2)
		then 1 else 0
		end),0) as
		inTimeFinishedNum
		from app_hidden_danger danger
		left join app_labor_company  company on company.id = danger.link_unit_id
		<where>
		-- 		暂时查隐患
		danger.type_ = 1
		<if test="tenantId != null and tenantId != ''">
			and danger.tenant_id = #{tenantId}
		</if>
		<if test="endTime!=null">
			and danger.create_time &lt;= #{endTime}
		</if>
		<if test="startTime!=null">
			and danger.create_time &gt;= #{startTime}
		</if>
		</where>
		group by link_unit_id;
	</select>

	<select id="getTypeCount" resultMap="BaseResultCountDTO">
		select adt.full_name fullName, adt.full_id dangerTypeId,count(0) as
		totalNum,
		ifNull(sum(case when status=0 then 1 else 0
		end),0) as
		waitRectifyNum,
		ifNull(sum(case when status=1 then 1 else 0
		end),0) as
		waitCheckNum,
		ifNull(sum(case
		when status=2 then 1 else 0
		end),0) as
		qualifiedNum,
		ifNull(sum(case when
		(rectify_end_time &lt;
		NOW() and
		`status`!=2) then 1 else 0 end),0) as
		overdunNum,
		ifNull(sum(case when
		(close_time
		&lt;= rectify_end_time and `status`=2)
		then 1 else 0
		end),0) as
		inTimeFinishedNum
		from app_hidden_danger danger
		left join app_danger ad on ad.id = danger.hidden_danger_id
		left join app_danger_type adt
		on left (ad.full_id,locate('/',ad.full_id)-1) = adt.full_id
		<where>
			-- 		暂时查隐患
			danger.type_ = 1
		<if test="tenantId != null and tenantId != ''">
			and danger.tenant_id=#{tenantId}
			and adt.tenant_id =#{tenantId}
		</if>
		<if test="endTime!=null">
			and danger.create_time &lt;= #{endTime}
		</if>
		<if test="startTime!=null">
			and danger.create_time &gt;= #{startTime}
		</if>
		</where>
		group by adt.full_id;
	</select>

	<select id="getPartCount" resultMap="BaseResultCountDTO">
		select aqp.full_name_ fullName, count(0) as
							 totalNum,
			   ifNull(sum(case when status=0 then 1 else 0
				   end),0) as
							 waitRectifyNum,
			   ifNull(sum(case when status=1 then 1 else 0
				   end),0) as
							 waitCheckNum,
			   ifNull(sum(case
							  when status=2 then 1 else 0
				   end),0) as
							 qualifiedNum,
			   ifNull(sum(case when
								   (rectify_end_time &lt;
									NOW() and
									`status`!=2) then 1 else 0 end),0) as
							 overdunNum,
			   ifNull(sum(case when
								   (close_time
										&lt;= rectify_end_time and `status`=2)
								   then 1 else 0
				   end),0) as
							 inTimeFinishedNum
		from app_hidden_danger danger
				 left join app_danger ad on ad.id = danger.hidden_danger_id
				 left join app_quality_position aqp on aqp.id = danger.check_part_id
		<where>
			<!-- 暂时查隐患-->
			danger.type_ = 1 and aqp.full_name_ is not null
			<if test="tenantId != null">
				and danger.tenant_id = #{tenantId}
			</if>
		  <if test="endTime!=null">
			  and danger.create_time &lt;= #{endTime}
		  </if>
		  <if test="startTime!=null">
			  and danger.create_time &gt;= #{startTime}
		  </if>
		</where>
		group by danger.check_part_id order by totalNum desc
	</select>

	<select id="getPersonCount" resultMap="BaseResultCountDTO">
		select rectify_uid, u.nickname ,u.username ,u.address ,count(0) as
		totalNum,
		ifNull(sum(case when status=0 then 1 else 0
		end),0) as
		waitRectifyNum,
		ifNull(sum(case when status=1 then 1 else 0
		end),0) as
		waitCheckNum,
		ifNull(sum(case
		when status=2 then 1 else 0
		end),0) as
		qualifiedNum,
		ifNull(sum(case when
		(rectify_end_time &lt;
		NOW() and
		`status`!=2) then 1 else 0 end),0) as
		overdunNum,
		ifNull(sum(case when
		(close_time
		&lt;= rectify_end_time and `status`=2)
		then 1 else 0
		end),0) as
		inTimeFinishedNum
		from app_hidden_danger danger
		left join linkapp_user u on danger .rectify_uid =u.id
		where danger.tenant_id=#{tenantId}
		-- 		暂时查隐患
		and danger.type_ = 1
		group by rectify_uid;
	</select>

	<select id="getMonthCount" resultMap="BaseResultCountDTO">
		select DATE_FORMAT(danger.create_time,   '%Y-%m') as createTimeStr, ad.level ,count(0) as
		totalNum,
		ifNull(sum(case when status=0 then 1 else 0
		end),0) as
		waitRectifyNum,
		ifNull(sum(case when status=1 then 1 else 0
		end),0) as
		waitCheckNum,
		ifNull(sum(case
		when status=2 then 1 else 0
		end),0) as
		qualifiedNum,
		ifNull(sum(case when
		(rectify_end_time &lt;
		NOW() and
		`status`!=2) then 1 else 0 end),0) as
		overdunNum,
		ifNull(sum(case when
		(close_time
		&lt;= rectify_end_time and `status`=2)
		then 1 else 0
		end),0) as
		inTimeFinishedNum
		from app_hidden_danger danger
		left join app_danger ad on danger .hidden_danger_id =ad .id
		where danger.tenant_id=#{tenantId}
		-- 		暂时查隐患
		and danger.type_ = 1
			and danger.create_time &lt;= #{endTime}
			and danger.create_time &gt;= #{startTime}
		group by createTimeStr,level ;
	</select>

	<select id="getDayCount" resultMap="BaseResultCountDTO">
		select DATE_FORMAT(danger.create_time,   '%Y-%m-%d') as createTimeStr ,count(0) as
		totalNum,
		ifNull(sum(case when status=0 then 1 else 0
		end),0) as
		waitRectifyNum,
		ifNull(sum(case when status=1 then 1 else 0
		end),0) as
		waitCheckNum,
		ifNull(sum(case
		when status=2 then 1 else 0
		end),0) as
		qualifiedNum,
		ifNull(sum(case when
		(rectify_end_time &lt;
		NOW() and
		`status`!=2) then 1 else 0 end),0) as
		overdunNum,
		ifNull(sum(case when
		(close_time
		&lt;= rectify_end_time and `status`=2)
		then 1 else 0
		end),0) as
		inTimeFinishedNum
		from app_hidden_danger danger
		where danger.tenant_id=#{tenantId}
		-- 		暂时查隐患
		and danger.type_ = 1
			and danger.create_time &lt;= #{endTime}
			and danger.create_time &gt;= #{startTime}
		group by createTimeStr ;
	</select>

	<select id="selectHiddenDangerByDangerousRef" resultMap="BaseResultDTO">
		select a.*,
			<![CDATA[
			   (case
					when ((a.`status` != 2
		and a.rectify_end_time <
			NOW())
						or (a.`status` = 2
							and a.close_time >
								a.rectify_end_time))
						then 1
					else 0
				   end)    as beOverdue,
			]]>

		b.nickname  as creator,
			   b.phone
						   as
							  createPhone,
			   b1.nickname as
							  rectifer,
			   b1.phone    as rectiferPhone,
			   b2.nickname as checker,
			   b2.phone    as
							  checkerPhone,
			   lc.name_    as
							  unitName,
			   d.`level`,
			   d.content,
			   t.full_name as fullName
		from app_hidden_danger a
				 left join linkapp_user b on
			a.create_uid = b.id
				 left join linkapp_user b1
						   on
							   a.rectify_uid = b1.id
				 left join linkapp_user b2 on
			a.check_uid = b2.id
				 left join app_labor_company lc on
			a.link_unit_id = lc.id
				 left join app_danger d on
			a.hidden_danger_id = d.id
				 left join app_danger_type t on
			d.danger_type_id = t.danger_type_id
				 left join app_dangerous_danger_ref addr on
			a.id = addr.danger_id
		where 1 = 1
		-- 		暂时查隐患
		and a.type_ = 1
		<if test="_parameter != null">
			and addr.dangerous_id = #{dangerousInfoId}
		</if>
		order by create_time desc
	</select>

	<select id="selectPhotoAlbumByPage" resultType="com.easylinkin.linkappapi.safe.dto.HiddenDangerPhotoAlbumDTO">
		SELECT
			p.*
		FROM
			(
			SELECT
				a.id,
				a.scene_photo AS scenePhoto,
				a.status AS status,
				t.full_name AS fullName,
				d.content AS content,
				d.level AS level,
				lc.name_ AS companyName,
				b.nickname AS creator,
				group_concat( i.check_photo ) AS checkPhotos,
		    group_concat( i.rectify_photo ) AS rectifyPhotos,
				a.create_time AS createTime,
		    tenant.platform_project_name AS projectName
			FROM
				app_hidden_danger a
				LEFT JOIN linkapp_user b ON a.create_uid = b.id
				LEFT JOIN app_labor_company lc ON a.link_unit_id = lc.id
				LEFT JOIN app_danger d ON a.hidden_danger_id = d.id
				LEFT JOIN app_danger_type t ON d.danger_type_id = t.danger_type_id
				LEFT JOIN app_hidden_danger_info i ON i.check_account_id = a.id
				AND (i.`check_photo` IS NOT NULL OR i.rectify_photo IS NOT NULL)
				LEFT JOIN linkapp_tenant tenant on tenant.id = a.tenant_id
			<where>
				1=1
				-- 		暂时查隐患
				and a.type_ = 1
				<if test="queryDTO.tenantId != null and queryDTO.tenantId != ''">
					AND a.tenant_id = #{queryDTO.tenantId}
				</if>
				<if test="queryDTO.createId != null and queryDTO.createId != ''">
					AND a.create_uid = #{queryDTO.createId}
				</if>
				<if test="queryDTO.companyId != null and queryDTO.companyId != ''">
					AND lc.id = #{queryDTO.companyId}
				</if>
				<if test="queryDTO.dangerTypeId != null and queryDTO.dangerTypeId != ''">
					AND (
					t.parent_id = #{queryDTO.dangerTypeId}
					OR t.danger_type_id = #{queryDTO.dangerTypeId}
					OR d.id = #{queryDTO.dangerTypeId}
					)
				</if>
				<if test="queryDTO.startTime != null ">
					AND a.create_time &gt;= #{queryDTO.startTime}
				</if>
				<if test="queryDTO.endTime != null">
					AND a.create_time &lt;= #{queryDTO.endTime}
				</if>
				<if test="queryDTO.ids != null and queryDTO.ids.size()>0">
					AND a.id IN
					<foreach item="item" index="index" collection="queryDTO.ids" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
			</where>

			GROUP BY
				a.id
			) p
		WHERE
			p.scenePhoto IS NOT NULL
			OR p.checkPhotos IS NOT NULL
			OR p.rectifyPhotos IS NOT NULL
    ORDER BY p.createTime
    <choose>
      <when test="queryDTO.sortAsc">
        ASC
      </when>
      <otherwise>
        DESC
      </otherwise>
    </choose>
	</select>

	<select id="getMyWaitCheckNum" resultType="java.lang.Integer">
		select
		ifNull(sum(case when status=1 then 1 else 0
		end),0)

		from app_hidden_danger

		where tenant_id=#{tenantId}
		-- 		暂时查隐患
		and type_ = 1
		and check_uid = #{id}
	</select>

	<select id="getMyWaitRectifyNum" resultType="java.lang.Integer">
		select
		ifNull(sum(case when status=0 then 1 else 0
		end),0)
		from app_hidden_danger
		where tenant_id=#{tenantId}
		-- 		暂时查隐患
		and type_ = 1
		and rectify_uid = #{id}
	</select>

	<select id="countGroupByCheckAndStatus" parameterType="com.easylinkin.linkappapi.openapi.vo.SafetyCheckHiddenDangerPageVo" resultMap="BaseResultCountDTO">
		select
			ahd.enterprise_safety_check_id,
			ahd.status,
			count(*) as totalNum
		from
			app_hidden_danger ahd
		where
			ahd.enterprise_safety_check_id is not null
		  and ahd.status is not null
			<if test="ids != null and ids != ''">
				and ahd.enterprise_safety_check_id in (${ids})
			</if>
		group by ahd.enterprise_safety_check_id,ahd.status
	</select>

    <select id="getCountEnterprise" parameterType="com.easylinkin.linkappapi.safe.dto.HiddenDangerDTO"
            resultMap="BaseResultCountDTO">
        select count(0)                       as
                   totalNum,
               ifNull(sum(case
                              when a.status = 0 then 1
                              else 0
                   end), 0)                   as
                   waitRectifyNum,
               ifNull(sum(case
                              when a.status = 1 then 1
                              else 0
                   end), 0)                   as
                   waitCheckNum,
               ifNull(sum(case
                              when a.status = 2 then 1
                              else 0
                   end), 0)                   as
                   qualifiedNum,
               ifNull(sum(case
                              when
                                  (rectify_end_time &lt;
                                   NOW() and
                                   a.status !=2) then 1
                              else 0 end), 0) as
                   overdunNum,
               ifNull(sum(case
                              when
                                  (close_time
                                       &lt;= rectify_end_time and a.status = 2)
                                  then 1
                              else 0
                   end), 0)                   as
                   inTimeFinishedNum
        from app_hidden_danger a
                 left JOIN linkapp_user b1
                           on
                               a.rectify_uid = b1.id
                 left JOIN linkapp_user b2 on
            a.check_uid = b2.id
                 left join app_labor_company lc on
            a.link_unit_id = lc.id
                 left join app_danger d on a.hidden_danger_id = d.id
                 left join app_danger_type t on d.danger_type_id = t.danger_type_id
                 left join app_quality_position qp ON qp.id = a.check_part_id
				left join linkapp_tenant lt on
					a.tenant_id = lt.id
        where 1 = 1
			and lt.id is not null
        <if test="hiddenDangerDTO != null">
            <if test="hiddenDangerDTO.enterpriseSafetyCheckId != null">
                and a.enterprise_safety_check_id = #{hiddenDangerDTO.enterpriseSafetyCheckId}
            </if>
            <if test="hiddenDangerDTO.paramKey != null and hiddenDangerDTO.paramKey != ''">
                and (a.enterprise_create_user_name like concat('%', #{hiddenDangerDTO.paramKey}, '%') or
                     a.create_uid in (select lu.id
                                      from linkapp_user lu
                                      where lu.delete_state = 1
                                        and lu.nickname like concat('%', #{hiddenDangerDTO.paramKey}, '%')))
            </if>
            <if test="hiddenDangerDTO.level != null">
                and d.level = #{hiddenDangerDTO.level}
            </if>
            <if test="hiddenDangerDTO.enterpriseCheckType != null">
                and a.enterprise_check_type = #{hiddenDangerDTO.enterpriseCheckType}
            </if>
            <if test="hiddenDangerDTO.dangerTypeName != null and hiddenDangerDTO.dangerTypeName != ''">
                and t.full_name like concat('%', #{hiddenDangerDTO.dangerTypeName}, '%')
            </if>
            <if test="hiddenDangerDTO.linkappTenant != null">
                <if test="hiddenDangerDTO.linkappTenant.platformProjectName != null and hiddenDangerDTO.linkappTenant.platformProjectName != ''">
                    and a.tenant_id in (select lt.id
                                        from linkapp_tenant lt
                                        where lt.platform_project_name like
                                              concat('%', #{hiddenDangerDTO.linkappTenant.platformProjectName}, '%'))
                </if>
            </if>
        </if>
        <if test="hiddenDangerDTO.type != null">
            and a.type_ = #{hiddenDangerDTO.type}
        </if>
        <if test="hiddenDangerDTO.taskId != null">
            and a.task_id_ = #{hiddenDangerDTO.taskId}
        </if>
        <if test="hiddenDangerDTO.id != null">
            and a.id = #{hiddenDangerDTO.id}
        </if>
        <if test="hiddenDangerDTO.status != null">
            and a.status = #{hiddenDangerDTO.status}
        </if>
        <if test="hiddenDangerDTO.startTime != null">
            <![CDATA[
            and a.create_time >= #{hiddenDangerDTO.startTime}
            ]]>
        </if>
        <if test="hiddenDangerDTO.endTime != null">
            <![CDATA[
            and a.create_time <= #{hiddenDangerDTO.endTime}
            ]]>
        </if>
        <if test="hiddenDangerDTO.linkUnitId != null and hiddenDangerDTO.linkUnitId != ''">
            and a.link_unit_id = #{hiddenDangerDTO.linkUnitId}
        </if>
        <if test="hiddenDangerDTO.rectifyUid != null and hiddenDangerDTO.rectifyUid != ''">
            and a.rectify_uid = #{hiddenDangerDTO.rectifyUid}
        </if>
        <if test="hiddenDangerDTO.checkUid != null and hiddenDangerDTO.checkUid != ''">
            and a.check_uid = #{hiddenDangerDTO.checkUid}
        </if>
        <if test="hiddenDangerDTO.tenantId != null and hiddenDangerDTO.tenantId != ''">
            and a.tenant_id = #{hiddenDangerDTO.tenantId}
        </if>
        <if test="hiddenDangerDTO.content != null and hiddenDangerDTO.content != ''">
            and d.content like CONCAT('%', #{hiddenDangerDTO.content}, '%')
		</if>
		<if test="hiddenDangerDTO.organizationIds != null and hiddenDangerDTO.organizationIds != ''">
			and a.enterprise_organization_id in (${hiddenDangerDTO.organizationIds})
		</if>
		<if test="hiddenDangerDTO.linkappTenantList != null and hiddenDangerDTO.linkappTenantList.size() != 0">
			and a.tenant_id in
			<foreach collection="hiddenDangerDTO.linkappTenantList" item="item" index="index" open="(" separator="," close=")">
				#{item.id}
			</foreach>
		</if>
	</select>

	<select id="getTenantIdCountList" parameterType="com.easylinkin.linkappapi.safe.dto.HiddenDangerDTO"
			resultMap="TenantBaseResultCountDTO">
		select lt.id                          as tenant_id,
			   ifnull(t.totalNum, 0)          as totalNum,
			   ifnull(t.waitRectifyNum, 0)    as waitRectifyNum,
			   ifnull(t.waitCheckNum, 0)      as waitCheckNum,
			   ifnull(t.qualifiedNum, 0)      as qualifiedNum,
			   ifnull(t.overdunNum, 0)        as overdunNum,
			   ifnull(t.inTimeFinishedNum, 0) as inTimeFinishedNum
		from
		linkapp_tenant lt
			left join (
		select a.tenant_id
				,
			   count(0)                       as
				   totalNum,
			   ifNull(sum(case
							  when status = 0 then 1
							  else 0
				   end), 0)                   as
				   waitRectifyNum,
			   ifNull(sum(case
							  when status = 1 then 1
							  else 0
				   end), 0)                   as
				   waitCheckNum,
			   ifNull(sum(case
							  when status = 2 then 1
							  else 0
				   end), 0)                   as
				   qualifiedNum,
			   ifNull(sum(case
							  when
								  (rectify_end_time &lt;
								   NOW() and
								   `status`!=2) then 1
							  else 0 end), 0) as
				   overdunNum,
			   ifNull(sum(case
							  when
								  (close_time
									   &lt;= rectify_end_time and `status` = 2)
								  then 1
							  else 0
				   end), 0)                   as
				   inTimeFinishedNum
		from app_hidden_danger a
				 left JOIN linkapp_user b1
						   on
							   a.rectify_uid = b1.id
				 left JOIN linkapp_user b2 on
			a.check_uid = b2.id
				 left join app_labor_company lc on
			a.link_unit_id = lc.id
				 left join app_danger d on a.hidden_danger_id = d.id
				 left join app_danger_type t on d.danger_type_id = t.danger_type_id
				 left join app_quality_position qp ON qp.id = a.check_part_id
		where 1 = 1
		<if test="hiddenDangerDTO != null">
			<if test="hiddenDangerDTO.enterpriseSafetyCheckId != null">
				and a.enterprise_safety_check_id = #{hiddenDangerDTO.enterpriseSafetyCheckId}
			</if>
			<if test="hiddenDangerDTO.paramKey != null and hiddenDangerDTO.paramKey != ''">
				and (a.enterprise_create_user_name like concat('%', #{hiddenDangerDTO.paramKey}, '%') or
					 a.create_uid in (select lu.id
									  from linkapp_user lu
									  where lu.delete_state = 1
										and lu.nickname like concat('%', #{hiddenDangerDTO.paramKey}, '%')))
			</if>
			<if test="hiddenDangerDTO.level != null">
				and d.level = #{hiddenDangerDTO.level}
			</if>
			<if test="hiddenDangerDTO.enterpriseCheckType != null">
				and a.enterprise_check_type = #{hiddenDangerDTO.enterpriseCheckType}
			</if>
			<if test="hiddenDangerDTO.dangerTypeName != null and hiddenDangerDTO.dangerTypeName != ''">
				and t.name like concat('%', #{hiddenDangerDTO.dangerTypeName}, '%')
			</if>
			<if test="hiddenDangerDTO.linkappTenant != null">
				<if test="hiddenDangerDTO.linkappTenant.platformProjectName != null and hiddenDangerDTO.linkappTenant.platformProjectName != ''">
					and a.tenant_id in (select lt.id
										from linkapp_tenant lt
										where lt.platform_project_name like
											  concat('%', #{hiddenDangerDTO.linkappTenant.platformProjectName}, '%'))
				</if>
			</if>
			<if test="hiddenDangerDTO.linkappTenantList != null and hiddenDangerDTO.linkappTenantList.size() != 0">
				and a.tenant_id in
				<foreach collection="hiddenDangerDTO.linkappTenantList" item="item" index="index" open="(" separator=","
						 close=")">
					#{item.id}
				</foreach>
			</if>
		</if>
		<if test="hiddenDangerDTO.type != null">
			and a.type_ = #{hiddenDangerDTO.type}
		</if>
		<if test="hiddenDangerDTO.taskId != null">
			and a.task_id_ = #{hiddenDangerDTO.taskId}
		</if>
		<if test="hiddenDangerDTO.id != null">
			and a.id = #{hiddenDangerDTO.id}
		</if>
		<if test="hiddenDangerDTO.status != null">
			and a.status = #{hiddenDangerDTO.status}
		</if>
		<if test="hiddenDangerDTO.startTime != null">
			<![CDATA[
			and a.create_time >= #{hiddenDangerDTO.startTime}
			]]>
		</if>
		<if test="hiddenDangerDTO.endTime != null">
			<![CDATA[
			and a.create_time <= #{hiddenDangerDTO.endTime}
			]]>
		</if>
		<if test="hiddenDangerDTO.linkUnitId != null and hiddenDangerDTO.linkUnitId != ''">
			and a.link_unit_id = #{hiddenDangerDTO.linkUnitId}
		</if>
		<if test="hiddenDangerDTO.rectifyUid != null and hiddenDangerDTO.rectifyUid != ''">
			and a.rectify_uid = #{hiddenDangerDTO.rectifyUid}
		</if>
		<if test="hiddenDangerDTO.checkUid != null and hiddenDangerDTO.checkUid != ''">
			and a.check_uid = #{hiddenDangerDTO.checkUid}
		</if>
		<if test="hiddenDangerDTO.tenantId != null and hiddenDangerDTO.tenantId != ''">
			and a.tenant_id = #{hiddenDangerDTO.tenantId}
		</if>
		<if test="hiddenDangerDTO.content != null and hiddenDangerDTO.content != ''">
			and d.content like CONCAT('%', #{hiddenDangerDTO.content}, '%')
		</if>
		and a.tenant_id is not null
		group by a.tenant_id
		) t on lt.id = t.tenant_id
		where 1=1
		<if test="hiddenDangerDTO.linkappTenantList != null and hiddenDangerDTO.linkappTenantList.size() != 0">
			and lt.id in
			<foreach collection="hiddenDangerDTO.linkappTenantList" item="item" index="index" open="(" separator="," close=")">
				#{item.id}
			</foreach>
		</if>
		order by totalNum desc,lt.create_time desc
	</select>

	<select id="selectSafetyCheckTrend" resultType="com.easylinkin.linkappapi.safe.dto.SafetyCheckTrendDTO">
		<!--		select count(*) from app_hidden_danger where tenant_id = '405cd2c8633ecbee1c5f61c4a87c9019'-->
		<!--		select weekofyear( create_time) ,count(*)from app_hidden_danger group by weekofyear( create_time);-->

		<!--		select count(*)from app_hidden_danger where create_time >='2022-11-28' and create_time <'2022-12-05';-->

		<!--		select count(*)from app_hidden_danger where create_time >='2022-11-21' and create_time <'2022-11-28';-->

		<!--		select weekofyear('2022-01-03') ;-->
		<!--		select weekofyear('2022-11-28') ;-->
		select date(d.create_time) as dateStr, count(*) as countNum
		from app_hidden_danger d left join linkapp_tenant lt on d.tenant_id = lt.id
		<where>
			d.type_ = 1
			  and d.create_time <![CDATA[>=]]> #{start,jdbcType=TIMESTAMP}
			  and d.create_time <![CDATA[<=]]> #{end,jdbcType=TIMESTAMP}
			<if test="hiddenDanger != null and hiddenDanger.enterpriseSourceType == null">
				and d.enterprise_source_type is null
			</if>
			<if test="hiddenDanger != null and hiddenDanger.enterpriseSourceType != null and hiddenDanger.enterpriseSourceType.equals(2)">
				and d.enterprise_source_type = 2
			</if>
			and lt.id is not null
		</where>
		group by date(d.create_time)
	</select>

	<select id="selectSafetyCheckCategoryByName"
			resultType="com.easylinkin.linkappapi.openapi.dto.BigScreenStatisticDTO">
		select count(*)   as countNum,
		left(ad.full_name, LOCATE('/',ad.full_name)-1) as name_
		from app_hidden_danger ha
				 inner join app_danger ad on ha.hidden_danger_id = ad.id
			left join linkapp_tenant lt on
				ha.tenant_id = lt.id
		<where>
			ha.type_ = 1
			and lt.id is not null
			<if test="start != null">
				and ha.create_time <![CDATA[>=]]> #{start,jdbcType=TIMESTAMP}
			</if>
			<if test="end != null">
				and ha.create_time <![CDATA[<=]]> #{end,jdbcType=TIMESTAMP}
			</if>
		</where>
		group by name_
		order by countNum desc
	</select>

	<select id="selectSafetyCheckCategory" resultType="com.easylinkin.linkappapi.safe.dto.SafetyCheckCategory">
		select projectCheckCount,
			   round(done / projectCheckCount * 100, 2)     as projectCloseRate,
			   enterpriseCheckCount,
			   round(done2 / enterpriseCheckCount * 100, 2) as enterpriseCloseRate
		from ((select count(*) as projectCheckCount
			   from app_hidden_danger d left join linkapp_tenant lt on d.tenant_id = lt.id
		<!--		检查和页面是不是对应的 -->
		<!--			   where  tenant_id = '405cd2c8633ecbee1c5f61c4a87c9019' and type_= 1) tmp1,-->
		where d.enterprise_source_type is null and lt.id is not null
		  and d.type_ = 1
			<if test="start != null">
				and d.create_time <![CDATA[>=]]> #{start,jdbcType=TIMESTAMP}
			</if>
			<if test="end != null">
				and d.create_time <![CDATA[<=]]> #{end,jdbcType=TIMESTAMP}
			</if>
		  ) tmp1,
			(select count(*) as done
			 from app_hidden_danger d left join linkapp_tenant lt on d.tenant_id = lt.id
			 where d.enterprise_source_type is null
			   and d.type_ = 1
			   and d.status = 2
				and lt.id is not null
				<if test="start != null">
					and d.create_time <![CDATA[>=]]> #{start,jdbcType=TIMESTAMP}
				</if>
				<if test="end != null">
					and d.create_time <![CDATA[<=]]> #{end,jdbcType=TIMESTAMP}
				</if>
			) tmp2,
			(select count(*) as enterpriseCheckCount
			 from app_hidden_danger d left join linkapp_tenant lt on d.tenant_id = lt.id
			 where d.enterprise_source_type = 2
			   and d.type_ = 1
				and lt.id is not null
				<if test="start != null">
					and d.create_time <![CDATA[>=]]> #{start,jdbcType=TIMESTAMP}
				</if>
				<if test="end != null">
					and d.create_time <![CDATA[<=]]> #{end,jdbcType=TIMESTAMP}
				</if>
			) tmp3,
			(select count(*) as done2
			 from app_hidden_danger d left join linkapp_tenant lt on d.tenant_id = lt.id
			 where d.enterprise_source_type = 2
			   and d.type_ = 1
			   and d.status = 2
				and lt.id is not null
				<if test="start != null">
					and d.create_time <![CDATA[>=]]> #{start,jdbcType=TIMESTAMP}
				</if>
				<if test="end != null">
					and d.create_time <![CDATA[<=]]> #{end,jdbcType=TIMESTAMP}
				</if>
			) tmp4
			)
	</select>

	<select id="selectCheckNumAndCloseRate" resultType="com.easylinkin.linkappapi.openapi.dto.ProjectStatisticDTO">
		select tmp1.id                                       as id,
			   tmp1.checkNum,
			   round(tmp2.closeNum * 100 / tmp1.checkNum, 2) as closeRate
		from (select lt.project_id as id,
					 count(ahd.id) as checkNum
			  from app_hidden_danger ahd
					   inner join linkapp_tenant lt on lt.id = ahd.tenant_id
			  where type_ = 1
			  group by lt.project_id) tmp1

				 left join
			 (select lt.project_id as id,
					 count(ahd.id) as closeNum
			  from app_hidden_danger ahd
					   inner join linkapp_tenant lt on lt.id = ahd.tenant_id
			  where type_ = 1
				and ahd.status = 2
			  group by lt.project_id) tmp2 on tmp2.id = tmp1.id
	</select>

	<select id="countGroupByLevelMonth" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
			resultType="java.util.Map">
		select date_format(h.create_time, '%Y-%m') as checkMonth,
			   ifnull(h.enterprise_source_type, 1) as checkLevel,
			   count(*)                            as num
		from app_hidden_danger h
				 left join linkapp_tenant lt on
			h.tenant_id = lt.id
		where h.type_ = 1
		  and lt.id is not null
		<if test="startTime != null">
			<![CDATA[
			and h.create_time >= #{startTime}
			]]>
		</if>
		<if test="endTime != null">
			<![CDATA[
			and h.create_time <= #{endTime}
			]]>
		</if>
		group by checkMonth,
				 h.enterprise_source_type
	</select>

	<select id="findOverByTenantIds" resultType="com.easylinkin.linkappapi.safe.dto.HiddenDangerDTO">
		SELECT
		a.id,
		a.tenant_id tenantId,
		a.rectify_end_time rectifyEndTime,
		a.rectify_uid rectifyUid,
		a.check_uid checkUid,
		a.status,
		b.content
		FROM
		app_hidden_danger a
		LEFT JOIN app_danger b ON a.hidden_danger_id = b.id
		WHERE
		<if test="tenantIds != null and tenantIds.size() != 0">
			a.tenant_id IN
			<foreach collection="tenantIds" item="tenantId" index="index" open="(" separator="," close=")">
				#{tenantId}
			</foreach>
		</if>
		AND (a.`status` = 0 or a.`status` = 1)
		<![CDATA[
			AND a.rectify_end_time < now()
		]]>
	</select>

	<select id="findHueList" resultType="com.easylinkin.linkappapi.safe.dto.HiddenDangerDTO">
		SELECT
		a.id,
		a.tenant_id tenantId,
		a.rectify_end_time rectifyEndTime,
		a.rectify_uid rectifyUid,
		a.check_uid checkUid,
		a.status,
		b.content
		FROM
		app_hidden_danger a
		LEFT JOIN app_danger b ON a.hidden_danger_id = b.id
		left JOIN app_hidden_config c on a.tenant_id = c.tenant_id_
		WHERE
		(a.`status` = 0 or a.`status` = 1)
		and c.task_expire_ = 1
		and TIMESTAMPDIFF(HOUR,now(),a.rectify_end_time)+1 <![CDATA[ = ]]> c.expire_param_
-- 		and TIMESTAMPDIFF(HOUR,now(),a.rectify_end_time)+1 <![CDATA[ > ]]> c.expire_param_-1
	</select>
</mapper>
