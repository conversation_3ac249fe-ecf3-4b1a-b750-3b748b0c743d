<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.safe.dao.HiddenDangerInfoMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.safe.entity.HiddenDangerInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="check_account_id" jdbcType="VARCHAR" property="checkAccountId" />
    <result column="rectify_result" jdbcType="INTEGER" property="rectifyResult" />
    <result column="rectify_photo" jdbcType="VARCHAR" property="rectifyPhoto" />
    <result column="rectify_remark" jdbcType="VARCHAR" property="rectifyRemark" />
    <result column="rectify_time" jdbcType="TIMESTAMP" property="rectifyTime" />
    <result column="check_result" jdbcType="INTEGER" property="checkResult" />
    <result column="check_remark" jdbcType="VARCHAR" property="checkRemark" />
    <result column="check_photo" jdbcType="VARCHAR" property="checkPhoto" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="is_end" jdbcType="INTEGER" property="isEnd" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_hidden_danger_info where 1=1 order by create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_hidden_danger_info where id = #{id}
    </select>

 	<select id="selectByHiddenDangerId" resultMap="BaseResultMap">
        select * from app_hidden_danger_info 
        where check_account_id=#{id}
        
        order by rectify_time  desc
       
    </select>

</mapper>
