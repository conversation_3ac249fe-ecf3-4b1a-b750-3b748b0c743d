<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.devicemanager.mapper.WireropeMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.devicemanager.entity.WireropeTitleEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="data" property="data"/>
    </resultMap>

    <select id="getWireropeInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select `id`,`device_id`,`device_name`,`data`
        from sensor_device where device_id = #{deviceId}
    </select>

    <select id="getAlertMode" resultType="java.lang.Integer" parameterType="java.lang.String" >
        select count(*) from sensor_device_data where device_id = #{deviceId} and level > 3
    </select>

    <resultMap id="WireropeResult" type="com.easylinkin.linkappapi.devicemanager.entity.WireropeEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_code" property="deviceCode"/>
        <result column="data" property="data"/>
        <result column="time" property="time"/>
        <result column="result" property="result"/>
        <result column="level" property="level"/>
        <result column="remark" property="remark"/>
        <result column="unit" property="unit"/>
        <result column="device_name" property="deviceName"/>
    </resultMap>

    <sql id="wirerope_column">
        a.id, a.device_id, a.data, a.level, a.remark, a.time
    </sql>

    <select id="getHistoryAlertList" resultMap="WireropeResult" >
        select <include refid="wirerope_column"></include>,a.result, b.device_name as device_name
        from sensor_device_data a
        LEFT JOIN sensor_device b on a.device_id = b.device_id
        where a.device_id = #{model.deviceId} and level > 3
        <if test="model.name != null and model.name != ''">
            and b.device_name like concat('%',#{model.name},'%')
        </if>
        <if test="model.startTime != null and model.startTime != ''">
            and a.time >= #{model.startTime}
        </if>
        <if test="model.endTime != null and model.endTime != ''">
            and a.time &lt; #{model.endTime}
        </if>
        order by time desc
    </select>

    <select id="getWireropeList" resultMap="WireropeResult" >
        select <include refid="wirerope_column"></include>,ROUND(a.result * c.rule,c.decimals) as result, c.unit
        from sensor_device_data a
        INNER JOIN sensor_device c on a.device_id = c.device_id
        where a.device_id = #{deviceId} and a.time between #{startTime} and #{endTime} order by a.time desc
    </select>

    <resultMap id="SensorResult" type="com.easylinkin.linkappapi.devicemanager.entity.SensorEntity">
        <result column="id" property="id"/>
        <result column="device_type_name" property="deviceTypeName"/>
        <result column="code" property="code"/>
        <result column="time" property="time"/>
        <result column="result" property="result"/>
        <result column="unit" property="unit"/>
        <result column="decimals" property="decimals"/>
    </resultMap>

    <select id="sensorMonitoring" resultMap="SensorResult" >
        SELECT c.device_id as id,c.device_type_id as code,c.device_type_name,a.time,ROUND(b.result * c.rule,c.decimals) as result,c.unit,b.level as status
        FROM
        (SELECT device_id,MAX(time) AS time FROM sensor_device_data GROUP BY device_id) a
        INNER JOIN sensor_device_data b on a.device_id = b.device_id and a.time = b.time
        INNER JOIN sensor_device c on a.device_id = c.device_id
        WHERE c.tenant_id = #{tenantId}
        and c.device_type_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <resultMap id="SensorConfig" type="com.easylinkin.linkappapi.devicemanager.entity.SensorDevice">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="max" property="max"/>
        <result column="min" property="min"/>
        <result column="unit" property="unit"/>
        <result column="rule" property="rule"/>
        <result column="decimals" property="decimals"/>
    </resultMap>

    <select id="getAlertConfig" resultMap="SensorConfig">
        select a.device_id,a.device_name,a.max,a.min,a.unit,a.rule from sensor_device a
        WHERE a.tenant_id = #{tenantId}
        and a.device_type_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="sensorAlertHistory" resultMap="WireropeResult" >
        select <include refid="wirerope_column"></include>,ROUND(a.result * c.rule,c.decimals) as result,c.device_code as device_code, c.unit
        from sensor_device_data a
        INNER JOIN sensor_device c on a.device_id = c.device_id
        WHERE c.tenant_id = #{tenantId}
        and c.device_type_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="model.name != null and model.name != ''">
            and a.remark like concat('%',#{model.name},'%')
        </if>
        order by a.time desc
    </select>

    <select id="sensorDataList" resultMap="WireropeResult" >
        select <include refid="wirerope_column"></include>,ROUND(a.result * c.rule,c.decimals) as result
        from sensor_device_data a
        INNER JOIN sensor_device c on a.device_id = c.device_id
        where a.device_id = #{deviceId}
        <if test="startTime != null and startTime != ''">
            and a.time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and a.time &lt; #{endTime}
        </if>
        order by a.time
    </select>

    <resultMap id="DeviceUnit" type="com.easylinkin.linkappapi.devicemanager.entity.DeviceUnit">
        <result column="id" property="id"/>
        <result column="device_type_id" property="deviceId"/>
        <result column="device_type_name" property="deviceName"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
    </resultMap>

    <select id="deviceList" resultMap="DeviceUnit">
        select device_id id,device_type_id,device_type_name,device_name name,device_code code
        from sensor_device
        where
            device_status = '1'
          and device_type_id = #{deviceTypeId}
          and tenant_id = #{tenantId}
        order by id
    </select>
</mapper>
