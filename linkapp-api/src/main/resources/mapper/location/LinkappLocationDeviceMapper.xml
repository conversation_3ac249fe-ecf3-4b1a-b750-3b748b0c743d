<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.location.mapper.LinkappLocationDeviceMapper">
    <update id="updateDeviceOnLineStatus">
        UPDATE linkapp_location_device
        SET online_state_ = 0
        WHERE latest_reporting_time_ &lt; NOW() - INTERVAL 30 MINUTE
    </update>

    <select id="queryList" resultType="com.easylinkin.linkappapi.location.entity.vo.LocationDeviceVo">
        SELECT LLD.id,
               LLD.device_code_           deviceCode,
               LLD.bind_state_            bindState,
               AUP.id                     userId,
               AUP.user_name_             userName,
               G.name_                    groupName,
               AL.name_                   companyName,
               LLD.device_type_           deviceType,
               CONCAT(LLD.battery_, '%')  battery,
               LLD.online_state_          onlineState,
               LLD.wearing_state_         wearingState,
               LLD.location_type_         locationType,
               LLD.indoor_location_       indoorLocation,
               LLD.longitude_             longitude,
               LLD.latitude_              latitude,
               LLD.latest_reporting_time_ locationTime,
               AUP.on_area_               peopleArea,
               AUP.work_type_             workTypeName,
               AFB.floor_id_              floorId,
               ub.card_                   cardNo
        FROM linkapp_location_device LLD
                 LEFT JOIN app_user_project AUP ON LLD.user_id_ = AUP.id
                 LEFT JOIN app_group G on AUP.group_id_ = G.id
                 LEFT JOIN app_labor_company_project AP on G.company_project_id_ = AP.id
                 LEFT JOIN app_labor_company AL on AP.company_id_ = AL.id
                 LEFT JOIN app_floor_beacon AFB ON LLD.label_code_ = AFB.device_code_ AND LLD.tenant_id_ = AFB.tenant_id_
                 LEFT JOIN emp_user_base ub  ON AUP.user_id_ = ub.id
        <where>
            AND LLD.delete_state_ = 1
            AND LLD.tenant_id_ = #{query.tenantId}
            <if test="query.deviceCode != null and query.deviceCode != ''">
                AND LLD.device_code_ LIKE CONCAT('%', #{query.deviceCode}, '%')
            </if>

            <if test="query.userName != null and query.userName != ''">
                AND AUP.user_name_ LIKE CONCAT('%', #{query.userName}, '%')
            </if>

            <if test="query.bindState != null">
                AND LLD.bind_state_ = #{query.bindState}
            </if>

            <if test="query.onlineState != null">
                AND LLD.online_state_ = #{query.onlineState}
            </if>

            <if test="query.wearingState != null">
                AND LLD.wearing_state_ = #{query.wearingState}
            </if>

            <if test="query.locationStartTime != null">
                AND LLD.latest_reporting_time_ &gt;= #{query.locationStartTime}
            </if>

            <if test="query.locationEndTime != null">
                AND LLD.latest_reporting_time_ &lt;= #{query.locationEndTime}
            </if>

            <if test="query.locationType != null">
                AND LLD.location_type_ = #{query.locationType}
            </if>
        </where>
        ORDER BY LLD.modify_time_ DESC
    </select>

    <select id="queryProjectDevice" resultType="com.easylinkin.linkappapi.device.entity.Device">
        SELECT ld.*
        FROM linkapp_device ld
                 LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
        WHERE 1=1
        AND ld.delete_state = 1

        <if test="tenantId != null and tenantId != ''">
            AND ld.tenant_id = #{tenantId}
        </if>

        <if test="deviceTypeName != null and deviceTypeName != ''">
            AND u.device_type_name = #{deviceTypeName}
        </if>
        ORDER BY ld.modify_time DESC
    </select>

    <select id="queryPeopleInfo" resultType="com.easylinkin.linkappapi.location.entity.vo.PeopleInfoVo">
        SELECT AUP.id,
        UB.photo_ photo,
        AUP.user_name_ userName,
        AUP.gender_ gender,
        AUP.birthday_ birthday,
        UB.card_ cardNo,
        UB.telephone_ telephone,
        AL.name_ companyName,
        G.name_ groupName,
        CONCAT(SUBSTRING_INDEX(AUP.work_type_, '/', 1), '/', SUBSTRING_INDEX(AUP.work_type_, '/', -1)) workTypeName,
        CASE SUBSTRING_INDEX(SUBSTRING_INDEX(AUP.work_type_, '/', 2), '/', -1)
        WHEN '特种作业人员' THEN '1'
        ELSE '0' END isSpecial,
        AUP.join_time_ joinTime,
        IFNULL(AUP.edu_state_, '0') eduState
        FROM app_user_project AUP
        LEFT JOIN emp_user_base UB ON AUP.user_id_ = UB.id
        LEFT JOIN app_group G ON AUP.group_id_ = G.id
        LEFT JOIN app_labor_company_project AP ON G.company_project_id_ = AP.id
        LEFT JOIN app_labor_company AL ON AP.company_id_ = AL.id
        <where>
            AUP.tenant_id_ = #{tenantId}
            AND AUP.id = #{userId}
        </where>
    </select>

    <select id="queryIndoorPoints" resultType="com.easylinkin.linkappapi.location.entity.vo.IndoorPointsVo">
        SELECT AB.id         buildingId,
               AB.NAME       buildingName,
               AB.longitude_ longitude,
               AB.latitude_  latitude,
               AB.coordinate_type_ coordinateType,
               AB.coordinate_ coordinate,
               COUNT(LLD.id) num
        FROM app_building AB
                 LEFT JOIN app_floor AF ON AB.id = AF.building_id
                 LEFT JOIN app_floor_beacon AFB ON AF.id = AFB.floor_id_
                 LEFT JOIN linkapp_location_device LLD ON AFB.device_code_ = LLD.label_code_
            AND AFB.tenant_id_ = LLD.tenant_id_
            AND LLD.bind_state_ = 1
            AND LLD.online_state_ = 1
            AND LLD.delete_state_ = 1
            AND LLD.location_type_ = 0
        WHERE AB.tenant_id = #{tenantId}
        GROUP BY AB.id
        ORDER BY AB.id
    </select>

    <select id="queryOutdoorPoints" resultType="com.easylinkin.linkappapi.location.entity.vo.OutdoorPointsVo">
        SELECT LLD.user_id_ userId,
               SUBSTRING_INDEX(AUP.work_type_, '/', 1) workTypeName,
               LLD.longitude_ longitude,
               LLD.latitude_ latitude
        FROM linkapp_location_device LLD
        LEFT JOIN app_user_project AUP ON LLD.user_id_ = AUP.id
        <where>
            LLD.tenant_id_ = #{tenantId}
            AND LLD.bind_state_ = 1
            AND LLD.online_state_ = 1
            AND LLD.delete_state_ = 1
            AND LLD.location_type_ = 1
        </where>
    </select>

    <select id="queryIndoorLocationInfo" resultType="com.easylinkin.linkappapi.location.entity.vo.IndoorLocationVo">
        SELECT LLD.user_id_           userId,
               AUP.user_name_         userName,
               al.name_               companyName,
               SUBSTRING_INDEX(AUP.work_type_, '/', 1) workTypeName,
               LLD.x_position_ xPosition,
               LLD.y_position_ yPosition
        FROM linkapp_location_device LLD
                 LEFT JOIN app_floor_beacon AFB
                           ON LLD.label_code_ = AFB.device_code_ AND LLD.tenant_id_ = AFB.tenant_id_
                 LEFT JOIN app_user_project AUP ON LLD.user_id_ = AUP.id
                 left JOIN app_group g on AUP.group_id_ = g.id
                 left JOIN app_labor_company_project ap on g.company_project_id_ = ap.id
                 left JOIN app_labor_company al on ap.company_id_ = al.id
        WHERE LLD.tenant_id_ = #{tenantId}
          AND AFB.floor_id_ = #{floorId}
          AND LLD.bind_state_ = 1
          AND LLD.online_state_ = 1
          AND LLD.delete_state_ = 1
          AND LLD.location_type_ = 0
    </select>

    <select id="queryFloorInfo" resultType="com.easylinkin.linkappapi.location.vo.AppFloorBeaconVO">
        SELECT AF.file_url_                              fileUrl,
               CONCAT_WS('/', ab.name, af.name, null) as location
        FROM app_floor AF
                 LEFT JOIN app_building ab on af.building_id = ab.id
        where af.id = #{floorId}
    </select>

    <select id="queryUserProjectInfo" resultType="com.easylinkin.linkappapi.lobar.dto.UserProjectDTO">
        SELECT u.*,
        ub.card_a_,
        ub.telephone_ telephone,
        ub.card_,
        ub.nation_
        FROM app_user_project u
        LEFT JOIN emp_user_base ub ON u.user_id_ = ub.id
        <where>
            u.delete_state_ = 1
            AND u.tenant_id_ = #{param1}
            AND ub.card_ = #{param2}
        </where>
    </select>

</mapper>
