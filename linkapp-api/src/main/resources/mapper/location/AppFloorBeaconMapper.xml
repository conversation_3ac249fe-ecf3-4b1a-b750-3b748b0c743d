<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.location.mapper.AppFloorBeaconMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.location.entity.AppFloorBeacon">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId"/>
        <result column="device_code_" property="deviceCode"/>
        <result column="bind_time_" property="bindTime"/>
        <result column="state_" property="state" />
        <result column="battery_electricity_" property="batteryElectricity" />
        <result column="floor_id_" property="floorId" />
        <result column="beacon_x_position_" property="beaconXPosition" />
        <result column="beacon_y_position_" property="beaconYPosition" />
        <result column="create_time_" property="createTime"/>
        <result column="modify_time_" property="modifyTime" />
    </resultMap>

    <resultMap id="AppFloorBeaconVOResultMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.location.vo.AppFloorBeaconVO">
        <result column="start_time_" property="startTime"/>
        <result column="end_time_" property="endTime" />
        <result column="creator" property="creator" />
        <result column="location" property="location" />
        <result column="floors" property="floors" />
        <result column="file_url" property="fileUrl" />
        <result column="minHeight" property="minHeight" />
        <result column="maxHeight" property="maxHeight" />
        <result column="longitude_" property="longitude" />
        <result column="latitude_" property="latitude" />
    </resultMap>

    <select id="getBeaconList" resultMap="AppFloorBeaconVOResultMap">
        select a.*,
               CONCAT_WS('/', c.name, b.name, null) as location,
               d.nickname as creator,
               b.file_url_ as file_url,
               b.building_standard_height as minHeight,
               b.building_standard_height + 3 as maxHeight
        from app_floor_beacon as a
        left join app_floor as b on a.floor_id_ = b.id
        left join app_building as c on b.building_id = c.id
        left join linkapp_user as d on a.creator_id_ = d.id
        where 1=1
        <if test="appFloorBeaconVO.tenantId != null and appFloorBeaconVO.tenantId != ''">
            and a.tenant_id_ = #{appFloorBeaconVO.tenantId}
        </if>
        <if test="appFloorBeaconVO.id != null">
            and a.id = #{appFloorBeaconVO.id}
        </if>
        <if test="appFloorBeaconVO.deviceCode != null and appFloorBeaconVO.deviceCode != ''">
            and a.device_code_ like CONCAT('%',#{appFloorBeaconVO.deviceCode},'%')
        </if>
        <if test="appFloorBeaconVO.floors != null and appFloorBeaconVO.floors != ''">
            and a.floor_id_ in
            <foreach collection="appFloorBeaconVO.floors.split(',')" item="id" open="(" close=")" separator="," index="i">
                #{id}
            </foreach>
        </if>
        <if test="appFloorBeaconVO.state != null">
            and a.state_ = #{appFloorBeaconVO.state}
        </if>
        <if test="appFloorBeaconVO.startTime != null">
            and a.bind_time_ >= #{appFloorBeaconVO.startTime}
        </if>
        <if test="appFloorBeaconVO.endTime != null">
            and a.bind_time_ &lt;= #{appFloorBeaconVO.endTime}
        </if>
        order by a.modify_time_ DESC
    </select>

    <select id="getBeaconListByCode" resultMap="AppFloorBeaconVOResultMap">
        select a.*,
        c.longitude_,
        c.latitude_
        from app_floor_beacon as a
        left join app_floor as b on a.floor_id_ = b.id
        left join app_building as c on b.building_id = c.id
        left join linkapp_user as d on a.creator_id_ = d.id
        where 1=1
        <if test="appFloorBeaconVO.tenantId != null and appFloorBeaconVO.tenantId != ''">
            and a.tenant_id_ = #{appFloorBeaconVO.tenantId}
        </if>
        <if test="appFloorBeaconVO.deviceCode != null and appFloorBeaconVO.deviceCode != ''">
            and a.device_code_ = #{appFloorBeaconVO.deviceCode}
        </if>
    </select>
</mapper>
