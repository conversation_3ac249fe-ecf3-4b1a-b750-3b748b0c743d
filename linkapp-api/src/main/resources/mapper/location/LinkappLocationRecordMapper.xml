<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.location.mapper.LinkappLocationRecordMapper">

    <select id="queryLocationRecordList" resultType="com.easylinkin.linkappapi.location.entity.vo.LocationRecordVo">
        SELECT llr.*,
               AUP.user_name_             userName,
               G.name_                    groupName,
               AL.name_                   companyName
        FROM linkapp_location_record llr
        LEFT JOIN app_user_project aup ON llr.user_info_ = aup.id
        LEFT JOIN app_group G on AUP.group_id_ = G.id
        LEFT JOIN app_labor_company_project AP on G.company_project_id_ = AP.id
        LEFT JOIN app_labor_company AL on AP.company_id_ = AL.id
        <where>
            llr.tenant_id_ = #{query.tenantId}
            <if test="query.deviceCode != null and query.deviceCode != ''">
                AND llr.device_code_ LIKE CONCAT('%', #{query.deviceCode}, '%')
            </if>

            <if test="query.beginTime != null and query.beginTime != ''">
                AND llr.create_time_ &gt;= #{query.beginTime}
            </if>

            <if test="query.endTime != null and query.endTime != ''">
                AND llr.create_time_ &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY llr.create_time_ DESC
    </select>
</mapper>
