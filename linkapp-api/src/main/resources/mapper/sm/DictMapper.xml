<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.easylinkin.sm.mapper.DictMapper">
  <resultMap id="dictItemMap" type="com.easylinkin.sm.entity.Dict">
    <id column="id_" property="id"/>
    <result column="name_" property="name"/>
    <result column="code_" property="code"/>
    <result column="level_" property="level"/>
    <result column="parent_id_" property="parentId"/>
    <result column="ext_" property="ext"/>
    <result column="order_no_" property="orderNo"/>
    <result column="display_" property="display"/>
    <result column="remark_" property="remark"/>
  </resultMap>

  <select id="getTreeData" resultType="com.easylinkin.sm.entity.Dict">
    SELECT
    c.id_ id,
    (CASE WHEN (i.value_ IS NULL OR i.value_ = '') THEN c.name_ ELSE i.value_ END) name,
    c.code_ code,
    c.parent_id_ parentId,
    p.name_ parentName,
    c.order_no_ orderNo,
    c.remark_ remark
    FROM sm_dict c
    LEFT JOIN sm_dict p ON c.parent_id_ = p.id_
    LEFT JOIN sm_i18n i ON c.id_ = i.key_ AND i.language_ = #{language} AND i.module_ = #{module}
    WHERE 1=1
    <if test="null!= parentId">
      AND c.parent_id_ = #{parentId}
    </if>
    ORDER BY c.order_no_
  </select>


</mapper>