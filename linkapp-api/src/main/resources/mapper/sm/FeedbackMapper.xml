<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.easylinkin.sm.mapper.FeedbackMapper">

  <select id="getFeedback" resultType="com.easylinkin.sm.entity.Feedback">
    SELECT feedback_id_ as id,modifier_,customer_id_,modify_time_ FROM sm_feedback
    where 1=1
    <if test="null != modifier and ''!= modifier">
      and modifier_ like "%"#{modifier}"%"
    </if>
    <if test="null != customerId and ''!= customerId">
      and customer_id_ = #{customerId}
    </if>
    <if test="null != beginTime and ''!= beginTime">
      and modify_time_ between  #{beginTime} and  #{endTime}
    </if>
    order by modify_time_ desc
  </select>

  <select id="getDetails" resultType="java.lang.String">
    select feedback_information_ from sm_feedback where feedback_id_ = #{id}
  </select>

</mapper>
