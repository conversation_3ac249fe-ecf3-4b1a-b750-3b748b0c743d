<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.easylinkin.sm.mapper.DepartmentMapper">
  <resultMap id="deptMap" type="com.easylinkin.sm.entity.Department">
    <id column="id_" property="id"/>
    <result column="name_" property="name"/>
    <result column="code_" property="code"/>
    <result column="search_code_" property="searchCode"/>
    <result column="level_" property="level"/>
    <result column="parent_id_" property="parentId"/>
    <result column="customer_id_" property="customerId"/>
    <result column="order_no_" property="orderNo"/>
    <result column="display_" property="display"/>
    <result column="remark_" property="remark"/>
  </resultMap>

  <select id="findOne" resultMap="deptMap">
    SELECT
    sd.id_,
    sd.name_,
    sd.code_,
    sd.search_code_,
    sd.parent_id_,
    sd.customer_id_,
    sc.name_ customerName,
    sd.order_no_,
    sd.remark_,
    sd.creator_,
    sd.create_time_,
    sd.display_
    FROM
    sm_department sd
    LEFT JOIN sm_customer sc ON sc.id_ = sd.customer_id_
    WHERE
    1 = 1
    <if test="null != id">
      AND sd.id_ = #{id}
    </if>
  </select>

  <select id="getDeptPage" resultMap="deptMap">
    SELECT
    sd.id_,
    sd.name_,
    sd.code_,
    sd.search_code_,
    sd.parent_id_,
    sdd.name_ parentName,
    sd.customer_id_,
    sc.name_ customerName,
    sd.order_no_,
    sd.remark_,
    sd.creator_,
    sd.create_time_,
    sd.display_
    FROM
    sm_department sd
    LEFT JOIN sm_department sdd ON sd.parent_id_ = sdd.id_
    LEFT JOIN sm_customer sc ON sc.id_ = sd.customer_id_
    WHERE
    1 = 1
    <if test="null != queryParams.searchCode and queryParams.searchCode.length != 0">
      AND (
      sd.search_code_ LIKE CONCAT(#{queryParams.searchCode},'%')
      AND sd.search_code_ <![CDATA[ <> ]]> #{queryParams.searchCode}
      )
    </if>
    <if test="null != queryParams.name and queryParams.name.length != 0">
      AND sd.name_ LIKE CONCAT('%',#{queryParams.name},'%')
    </if>
    <if test="null != queryParams.parentId">
      AND sd.parent_id_ = #{queryParams.parentId}
    </if>
    <if test="null != queryParams.display">
      AND sd.display_ = #{queryParams.display}
    </if>
    ORDER BY sd.order_no_
  </select>


</mapper>