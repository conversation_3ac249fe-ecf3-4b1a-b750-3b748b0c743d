<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.easylinkin.sm.mapper.DictItemMapper">
  <resultMap id="dictItemMap" type="com.easylinkin.sm.entity.DictItem">
    <id column="id_" property="id"/>
    <result column="name_" property="name"/>
    <result column="dict_id_" property="dictId"/>
    <result column="code_" property="code"/>
    <result column="level_" property="level"/>
    <result column="parent_id_" property="parentId"/>
    <result column="ext_" property="ext"/>
    <result column="order_no_" property="orderNo"/>
    <result column="display_" property="display"/>
    <result column="remark_" property="remark"/>
  </resultMap>

  <select id="getDictItemPage" resultType="com.easylinkin.sm.entity.DictItem">
    SELECT
    c.id_ id,
    c.name_ name,
    c.code_ code,
    c.parent_id_ parentId,
    p.name_ parentName,
    c.dict_id_ dictId,
    d.name_ dictName,
    c.order_no_ orderNo,
    c.remark_ remark
    FROM sm_dict_item c
    LEFT JOIN sm_dict_item p ON c.parent_id_ = p.id_
    LEFT JOIN sm_dict d ON c.dict_id_ = d.id_
    WHERE 1=1
    <if test="null!= queryParams.name and queryParams.name.length!=0">
      AND c.name_ like CONCAT('%',#{queryParams.name},'%')
    </if>
    <if test="null!= queryParams.code and queryParams.code.length!=0">
      AND c.code_ like CONCAT('%',#{queryParams.code},'%')
    </if>
    <if test="null!= queryParams.dictId">
      AND c.dict_id_ = #{queryParams.dictId}
    </if>
    <if test="null!= queryParams.parentId">
      AND c.parent_id_ = #{queryParams.parentId}
    </if>
    ORDER BY c.order_no_
  </select>

  <!-- 获取指定字典分类字典数据 -->
  <select id="findDicItemList" resultType="com.easylinkin.sm.entity.DictItem">
    SELECT
    c.id_ id,
    c.parent_id_ parentId,
    c.dict_id_ dictId,
    (CASE
    WHEN (i.value_ IS NULL OR i.value_ = '') THEN c.name_
    ELSE i.value_ END) name,
    c.code_ code,
    c.order_no_ orderNo,
    c.remark_ remark
    FROM sm_dict_item c
    LEFT JOIN sm_dict d ON c.dict_id_ = d.id_
    LEFT JOIN sm_i18n i ON c.id_ = i.key_ AND i.language_ = #{language} AND i.module_ = #{module}
    WHERE d.code_ = #{code}
    ORDER BY c.order_no_
  </select>

  <!-- 获取指定字典分类下的所有字典父类 -->
  <select id="findParentDicItemList" resultType="com.easylinkin.sm.entity.DictItem">
    SELECT
    c.id_ id,
    c.parent_id_ parentId,
    c.dict_id_ dictId,
    c.name_ name,
    c.code_ code,
    c.order_no_ orderNo,
    c.remark_ remark
    FROM sm_dict_item c
    WHERE EXISTS (
    SELECT d.parent_id_
    FROM sm_dict d
    LEFT JOIN sm_dict_item i ON i.dict_id_ = d.id_
    WHERE c.dict_id_ = d.parent_id_
    AND d.id_ = #{id}
    )
    ORDER BY c.order_no_
  </select>

</mapper>