<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.easylinkin.sm.mapper.LogMapper">

  <select id="getLogModules" resultType="java.util.HashMap">
    select sl.module_id_,i.value_ from sm_log sl  LEFT JOIN sm_i18n i ON sl.module_id_ = i.key_ where i.module_ = #{module} and i.language_ = #{language}
  </select>

  <select id="getAllLog" resultType="com.easylinkin.sm.entity.Log">
    SELECT log_id_ as id,modifier_,module_id_,content_,ip_,status_,modify_time_ FROM sm_log
    where 1=1
    <if test="null != modifier and ''!= modifier">
      and modifier_ like "%"#{modifier}"%"
    </if>
    <if test="null != customerId and ''!= customerId">
      and customer_id_ = #{customerId}
    </if>
    <if test="null != moduleId and ''!= moduleId">
      and module_id_ = #{moduleId}
    </if>
    <if test="null != status">
      and status_ = #{status, jdbcType=BOOLEAN}
    </if>
    <if test="null != beginTime and ''!= beginTime">
      and modify_time_ between  #{beginTime} and  #{endTime}
    </if>
    order by modify_time_ desc
  </select>

  <select id="getDetails" resultType="java.util.Map">
    select request_information_,response_information_,fail_information_ FROM sm_log  where log_id_ = #{id}
  </select>

</mapper>
