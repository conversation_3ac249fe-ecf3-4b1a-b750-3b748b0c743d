<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.reportCenter.mapper.ReportAiDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.reportCenter.entity.ReportAiDetail">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="report_id_" property="reportId" />
        <result column="link_id_" property="linkId" />
        <result column="event_" property="event" />
        <result column="num_" property="num" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="findByLinkId" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            app_report_ai_detail
        WHERE
            link_id_ = #{linkId}
    </select>

</mapper>
