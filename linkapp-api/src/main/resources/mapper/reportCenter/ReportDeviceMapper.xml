<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.reportCenter.mapper.ReportDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.reportCenter.entity.ReportDevice">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="report_id_" property="reportId" />
        <result column="device_type_id_" property="deviceTypeId" />
        <result column="num_" property="num" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.easylinkin.linkappapi.reportCenter.entity.vo.ReportDeviceVo">
        <result column="device_type_name" property="deviceTypeName" />
    </resultMap>

    <select id="findDevice" resultMap="BaseResultMapVo">
        SELECT
            a.*,
            b.`name` device_type_name
        FROM
            app_report_device a
                LEFT JOIN linkapp_device_type b ON a.device_type_id_ = b.id
        WHERE
            a.report_id_ = #{reportId}
        ORDER BY
            a.num_
    </select>

</mapper>
