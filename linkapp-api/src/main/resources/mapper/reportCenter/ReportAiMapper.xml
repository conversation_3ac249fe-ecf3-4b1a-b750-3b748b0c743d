<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.reportCenter.mapper.ReportAiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.reportCenter.entity.ReportAi">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="report_id_" property="reportId" />
        <result column="device_code" property="deviceCode" />
        <result column="day_" property="day" />
        <result column="num_" property="num" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.easylinkin.linkappapi.reportCenter.entity.vo.ReportAiVo">
        <collection property="detailList" column="id"
                    select="com.easylinkin.linkappapi.reportCenter.mapper.ReportAiDetailMapper.findByLinkId"/>
    </resultMap>

    <select id="findByReportId" resultMap="BaseResultMapVo">
        SELECT
            *
        FROM
            app_report_ai
        WHERE
            report_id_ = #{reportId}
    </select>

</mapper>
