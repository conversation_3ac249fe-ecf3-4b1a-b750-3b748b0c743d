<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.reportCenter.mapper.ReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.reportCenter.entity.Report">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="name_" property="name" />
        <result column="start_time_" property="startTime" />
        <result column="end_time_" property="endTime" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="findLast" resultMap="BaseResultMap">
        SELECT
            a.*
        FROM
            app_report a
        JOIN app_report b ON a.start_time_ = DATE_ADD( b.start_time_, INTERVAL - 1 MONTH )
        AND a.tenant_id_ = b.tenant_id_
        WHERE
            b.id = #{id}
    </select>

</mapper>
