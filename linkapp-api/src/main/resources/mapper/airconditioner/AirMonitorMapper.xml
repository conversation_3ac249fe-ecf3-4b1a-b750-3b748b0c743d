<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.airconditioner.mapper.AirMonitorMapper">

    <select id="getAttributesList" resultMap="getAttributesListMap">
        select temp.* from (
        SELECT ldas.id,
        ldas.prop_code,
        ldas.prop_value       AS vo_prop_value,
        ldas.parent_prop_code AS vo_parent_prop_code,
        lda.device_unit_id,
        lda.name              AS vo_prop_name,
        lda.unit              AS vo_unit,
        lda.specs,
        <choose>
            <when test="pTenantId != null and pTenantId != ''">
                ifnull(ldatc.is_show, lda.is_show) is_show,
                ifnull(ldatc.sort_no, lda.sort_no) sort_no,
            </when>
            <otherwise>
                lda.is_show,
                lda.sort_no,
            </otherwise>
        </choose>
        lda.ico_path
        FROM linkapp_device_attribute_status ldas
        LEFT JOIN linkapp_device_attribute lda ON lda.device_unit_id = #{pDeviceUnitId} AND ldas.prop_code = lda.identifier
        <if test="pTenantId != null and pTenantId != ''">
            left join linkapp_device_attribute_tenant_config ldatc on ldatc.device_attribute_id = lda.id and ldatc.tenant_id = #{pTenantId,jdbcType=VARCHAR}
        </if>
        LEFT JOIN linkapp_device d ON ldas.device_code = d.code AND d.delete_state = 1
        LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
        WHERE ldas.version = u.version
        AND ldas.device_code = #{pCode}
        ) temp
        where temp.is_show = 1
        ORDER BY temp.sort_no ASC
    </select>

    <select id="getAirDevicePage" resultMap="MonitorDevicesMap">
        SELECT
            d_.*
        FROM
        (
            SELECT
            d.id,
            d.online_state,
            d.name,
            d.code,
            d.remark,
            d.device_unit_id,
            d.create_time,
            d.tenant_id,
            d.status,
            u.device_type_name AS d_deviceTypeName,
            u.code             AS d_device_unit_code,
            u.name             AS deviceUnitName,
            u.version          AS deviceUnitVersion,
            ldt.ico_path
            FROM
            <if test="amq.airConditionStatus != null and amq.airConditionStatus == 5">
                (
                select dChild.* from
                device_ref_area_scope dras left join linkapp_device dChild on dras.device_code=dChild.code
                <where>
                    <if test="amq.functionIdentifier != null and amq.functionIdentifier != ''">
                        and dras.function_identifier = #{amq.functionIdentifier}
                    </if>
                    <if test="amq.tenantId != null and amq.tenantId != ''">
                        and dras.tenant_id = #{amq.tenantId}
                    </if>
                    <if test="amq.areaTreeVos != null">
                        and dras.area_id in
                        <foreach collection="amq.areaTreeVos" separator="," open="(" close=")" item="areaTree">
                            #{areaTree.id}
                        </foreach>
                    </if>
                    and exists (select 1 from regular_temperature_control_ref_device rtcrd  left join regular_temperature_control_info i on rtcrd.regular_tci_id=i.id
                    where i.delete_status=0 and dchild.code=rtcrd.device_code)
                </where>
                group by dChild.id
                ) d
            </if>
            <if test="amq.airConditionStatus != null and amq.airConditionStatus != 5">
                (
                select
                dChild.*
                from
                device_ref_area_scope dras left join linkapp_device dChild on dras.device_code=dChild.code
                <where>
                    <if test="amq.functionIdentifier != null and amq.functionIdentifier != ''">
                        and dras.function_identifier = #{amq.functionIdentifier}
                    </if>
                    <if test="amq.tenantId != null and amq.tenantId != ''">
                        and dras.tenant_id = #{amq.tenantId}
                    </if>
                    <if test="amq.areaTreeVos != null">
                        and dras.area_id in
                        <foreach collection="amq.areaTreeVos" separator="," open="(" close=")" item="areaTree">
                            #{areaTree.id}
                        </foreach>
                    </if>
                </where>
                group by dChild.id
                ) d
            </if>
            LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
            left join linkapp_device_type ldt on ldt.id = u.device_type_id
            where
            d.delete_state = 1
            ORDER BY d.create_time DESC
        ) d_ left join linkapp_device_attribute_status ldas on d_.code=ldas.device_code and ldas.version=d_.deviceUnitVersion
        <where>
            <if test="amq.status != null">
                and d_.status = #{amq.status}
            </if>
            <if test="amq.propCode != null ">
                and ldas.prop_code = #{amq.propCode}
            </if>
            <if test="amq.propValue != null and amq.propCode != 'lock_cmd'">
                and ldas.prop_value = #{amq.propValue}
            </if>
            <if test="amq.propValue != null and amq.propCode == 'lock_cmd'">
                and
                ldas.prop_value <![CDATA[<>]]> #{amq.propValue}
            </if>
            <if test="amq.deviceCode != null and amq.deviceCode != ''">
                and d_.code = #{amq.deviceCode}
            </if>
        </where>
        group by d_.id
</select>

    <select id="checkTimerCountByDeviceCodeList" resultType="com.easylinkin.linkappapi.airconditioner.entity.ext.DeviceTimerCount">
        SELECT
            rd.device_code,
            count(*) num
        FROM
            regular_temperature_control_info rtci
                LEFT JOIN regular_temperature_control_ref_device rd ON rtci.id = rd.regular_tci_id
        WHERE
            rtci.delete_status = 0
            <if test="deviceCodeList != null">
                and rd.device_code in
                <foreach collection="deviceCodeList" separator="," open="(" close=")" item="deviceCode">
                    #{deviceCode}
                </foreach>
            </if>
        group by rd.device_code;
    </select>


<!-- 根据设备在线状态 统计设备数 -->
    <select id="getAirConditionStatistic" resultType="java.lang.Integer">
        select count(*) from (
            SELECT
            d_.*
            FROM
            (
            SELECT
            d.id,
            d.name,
            d.code,
            d.remark,
            d.device_unit_id,
            d.create_time,
            d.tenant_id,
            d.status,
            u.device_type_name AS d_deviceTypeName,
            u.code             AS d_device_unit_code,
            u.name             AS deviceUnitName,
            u.version          AS deviceUnitVersion,
            ldt.ico_path
            FROM
            <if test="amq.airConditionStatus != null and amq.airConditionStatus == 5">
                (
                select dChild.* from
                device_ref_area_scope dras left join linkapp_device dChild on dras.device_code=dChild.code
                <where>
                    <if test="amq.functionIdentifier != null and amq.functionIdentifier != ''">
                        and dras.function_identifier = #{amq.functionIdentifier}
                    </if>
                    <if test="amq.tenantId != null and amq.tenantId != ''">
                        and dras.tenant_id = #{amq.tenantId}
                    </if>
                    <if test="amq.areaTreeVos != null">
                        and dras.area_id in
                        <foreach collection="amq.areaTreeVos" separator="," open="(" close=")" item="areaTree">
                            #{areaTree.id}
                        </foreach>
                        <!-- and dras.area_path in
                         <foreach collection="amq.areaTreeVos" separator="," open="(" close=")" item="areaTree">
                             #{areaTree.areaPath}
                         </foreach>-->
                    </if>
                    and exists (select 1 from regular_temperature_control_ref_device rtcrd  left join regular_temperature_control_info i on rtcrd.regular_tci_id=i.id
                    where i.delete_status=0 and dchild.code=rtcrd.device_code)
                </where>
                group by dChild.id
                ) d
            </if>
            <if test="amq.airConditionStatus != null and amq.airConditionStatus != 5">
                (
                select
                dChild.*
                from
                device_ref_area_scope dras left join linkapp_device dChild on dras.device_code=dChild.code
                <where>
                    <if test="amq.functionIdentifier != null and amq.functionIdentifier != ''">
                        and dras.function_identifier = #{amq.functionIdentifier}
                    </if>
                    <if test="amq.tenantId != null and amq.tenantId != ''">
                        and dras.tenant_id = #{amq.tenantId}
                    </if>
                    <if test="amq.areaTreeVos != null">
                        and dras.area_id in
                        <foreach collection="amq.areaTreeVos" separator="," open="(" close=")" item="areaTree">
                            #{areaTree.id}
                        </foreach>
                        <!--and dras.area_path in
                        <foreach collection="amq.areaTreeVos" separator="," open="(" close=")" item="areaTree">
                            #{areaTree.areaPath}
                        </foreach>-->
                    </if>
                </where>
                group by dChild.id
                ) d
            </if>
            LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
            left join linkapp_device_type ldt on ldt.id = u.device_type_id
            where
            d.delete_state = 1
            <!-- and u.device_type_name='空调控制器' -->
            <!-- and u.code='KTKZQ-NBA-01-0036' -->
            <!--  and u.device_type_id='9674a6b0f3789780888e92ab15006e74' -->
            ORDER BY d.create_time DESC
            ) d_ left join linkapp_device_attribute_status ldas on d_.code=ldas.device_code and ldas.version=d_.deviceUnitVersion
            <where>
                <if test="amq.status != null">
                    and d_.status = #{amq.status}
                </if>
                <if test="amq.propCode != null ">
                    and ldas.prop_code = #{amq.propCode}
                </if>
                <if test="amq.propValue != null and amq.propCode != 'lock_cmd'">
                    and ldas.prop_value = #{amq.propValue}
                </if>
                <if test="amq.propValue != null and amq.propCode == 'lock_cmd'">
                    and
                    ldas.prop_value <![CDATA[<>]]> #{amq.propValue}
                </if>
            </where>
            group by d_.id
    ) count_num
</select>


<resultMap id="MonitorDevicesMap" type="com.easylinkin.linkappapi.airconditioner.entity.AirDevice">
 <id column="id" property="id"/>
 <result column="code" property="code"/>
 <result column="name" property="name"/>
 <result column="status" property="status"/>
 <result column="online_state" property="onlineState"/>
 <result column="tenant_id" property="tenantId"/>
 <result column="device_unit_id" property="deviceUnitId"/>
 <result column="d_device_unit_code" property="deviceUnitCode"/>
 <result column="area_path" property="areaPath"/>
 <result column="d_deviceTypeName" property="deviceTypeName"/>
 <result column="deviceUnitVersion" property="deviceUnitVersion"/>
 <association property="deviceType" javaType="com.easylinkin.linkappapi.devicetype.entity.DeviceType">
     <result column="ico_path" property="icoPath"/>
 </association>
 <collection property="deviceAttributeStatusList" column="{pDeviceUnitId=device_unit_id,pCode=code,pTenantId = tenant_id}" select="getAttributesList" javaType="java.util.ArrayList" ofType="com.easylinkin.linkappapi.airconditioner.entity.AirDeviceAttributeStatus.AirDeviceAttributeStatus">
 </collection>
</resultMap>

<resultMap id="getAttributesListMap" type="com.easylinkin.linkappapi.airconditioner.entity.AirDeviceAttributeStatus">
 <id column="id" property="id"/>
 <result column="device_unit_id" property="deviceUnitId"/>
 <result column="prop_code" property="propCode"/>
 <result column="vo_prop_name" property="propName"/>
 <result column="vo_unit" property="unit"/>
 <result column="specs" property="specs"/>
 <result column="sort_no" property="sortNo"/>
 <result column="is_show" property="isShow"/>
 <result column="vo_prop_value" property="propValue"/>
 <result column="vo_parent_prop_code" property="parentPropCode"/>
 <result column="vo_array_Index" property="arrayIndex"/>
</resultMap>



</mapper>
