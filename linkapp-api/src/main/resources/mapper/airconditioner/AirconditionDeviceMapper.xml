<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.airconditioner.mapper.AirconditionDeviceMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.airconditioner.entity.AirconditionDevice">
    <!--@mbg.generated-->
    <!--@Table ac_aircondition_device-->
    <id column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="host_device_id" jdbcType="VARCHAR" property="hostDeviceId" />
    <result column="low_fan" jdbcType="DECIMAL" property="lowFan" />
    <result column="mid_fan" jdbcType="DECIMAL" property="midFan" />
    <result column="high_fan" jdbcType="DECIMAL" property="highFan" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    device_id, device_code, device_name, host_device_id, low_fan, mid_fan, high_fan,tenant_id, create_time, update_time
  </sql>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into ac_aircondition_device
    (device_id, device_code, device_name, host_device_id, low_fan, mid_fan, high_fan,tenant_id,create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deviceId,jdbcType=VARCHAR}, #{item.deviceCode,jdbcType=VARCHAR}, #{item.deviceName,jdbcType=VARCHAR},
        #{item.hostDeviceId,jdbcType=VARCHAR}, #{item.lowFan,jdbcType=DECIMAL}, #{item.midFan,jdbcType=DECIMAL},
        #{item.highFan,jdbcType=DECIMAL},#{item.tenantId,jdbcType=VARCHAR},#{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>