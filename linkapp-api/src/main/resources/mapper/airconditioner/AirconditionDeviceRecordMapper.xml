<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.airconditioner.mapper.AirconditionDeviceRecordMapper">

  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.airconditioner.entity.AirconditionDeviceRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="aircondition_device_id" jdbcType="VARCHAR" property="airconditionDeviceId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="fan_speed" jdbcType="TINYINT" property="fanSpeed" />
    <result column="duration" jdbcType="DECIMAL" property="duration" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>

  <resultMap id="BaseResultMapSum" type="com.easylinkin.linkappapi.airconditioner.entity.AirconditionDeviceRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="aircondition_device_id" jdbcType="VARCHAR" property="airconditionDeviceId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="fan_speed" jdbcType="TINYINT" property="fanSpeed" />
    <result column="duration" jdbcType="DECIMAL" property="duration" />
    <result column="low_fan" jdbcType="DECIMAL" property="lowFan" />
    <result column="mid_fan" jdbcType="DECIMAL" property="midFan" />
    <result column="high_fan" jdbcType="DECIMAL" property="highFan" />
    <result column="low_fan_duration" jdbcType="DECIMAL" property="lowFanDuration" />
    <result column="mid_fan_duration" jdbcType="DECIMAL" property="midFanDuration" />
    <result column="high_fan_duration" jdbcType="DECIMAL" property="highFanDuration" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="host_device_id"  jdbcType="VARCHAR" property="hostDeviceId"/>
    <result column="device_name"  jdbcType="VARCHAR" property="deviceName"/>
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="wattmeter_id" jdbcType="VARCHAR" property="wattmeterId" />
  </resultMap>


  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, aircondition_device_id, `type`, fan_speed, duration, create_time,
    update_time,tenant_id
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into ac_aircondition_device_record
    (aircondition_device_id, `type`, fan_speed, duration, create_time,
      update_time,tenant_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.airconditionDeviceId,jdbcType=VARCHAR}, #{item.type,jdbcType=TINYINT}, #{item.fanSpeed,jdbcType=TINYINT},
        #{item.duration,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.updateTime,jdbcType=TIMESTAMP},#{item.tenantId,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="selectAllByAirconditionDeviceIdGropuBy" parameterType="java.util.List" resultMap="BaseResultMap">
      select id,aircondition_device_id, `type`, fan_speed, duration, create_time,update_time
         from ac_aircondition_device_record where id in(
         select SUBSTRING_INDEX(group_concat(id order by update_time DESC),',',1) as id
         from ac_aircondition_device_record
         <where>
            <!--and (duration = 0 or duration is null)-->
             <!--查询未完成数据-->
             (complete_status != 1 or complete_status is null)
           <if test="list != null and list.size() != 0">
                and aircondition_device_id in
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                  #{item}
                </foreach>
           </if>
           <if test="airconditionDeviceRecord.tenantId != null and airconditionDeviceRecord.tenantId != ''">
              and  tenant_id=#{airconditionDeviceRecord.tenantId,jdbcType=VARCHAR}
           </if>
         </where>
         group by aircondition_device_id)
          <if test="airconditionDeviceRecord.tenantId != null and airconditionDeviceRecord.tenantId != ''">
            and  tenant_id=#{airconditionDeviceRecord.tenantId,jdbcType=VARCHAR}
          </if>
         ORDER BY update_time desc;
  </select>

  <update id="updateBatchSelective">
    <!--@mbg.generated-->
    update ac_aircondition_device_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="aircondition_device_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.airconditionDeviceId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.airconditionDeviceId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.type != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.type,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="fan_speed = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fanSpeed != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.fanSpeed,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="duration = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.duration != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.duration,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="complete_status = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                <if test="item.completeStatus != null">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.completeStatus,jdbcType=TINYINT}
                </if>
            </foreach>
        </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
    <if test="tenantId != null and tenantId != ''">
       and  tenant_id=#{tenantId,jdbcType=VARCHAR}
    </if>

  </update>

  <select id="selectAllByAirconditionDeviceIdPage" resultMap="BaseResultMapSum">
        select adr.update_time,ad.device_id aircondition_device_id,ad.device_name,adr.fan_speed
        ,type,ad.host_device_id,hd.wattmeter_id
        ,IFNULL(ad.low_fan,0) low_fan
        ,IFNULL(ad.mid_fan,0) mid_fan
        ,IFNULL(ad.high_fan,0) high_fan
        ,IFNULL(adr.low_fan_duration,0) low_fan_duration
        ,IFNULL(adr.mid_fan_duration,0) mid_fan_duration
        ,IFNULL(adr.high_fan_duration,0) high_fan_duration
        from (
          select ras.device_code device_id
          ,ras.device_code
          ,ld.`name` device_name
          ,aad.host_device_id
          ,aad.low_fan
          ,aad.mid_fan
          ,aad.high_fan
          ,ras.tenant_id
          ,aad.create_time
          ,aad.update_time
          from device_ref_area_scope ras
          left join ac_aircondition_device aad on ras.device_code=aad.device_id and ras.tenant_id=aad.tenant_id
          left join linkapp_device ld on ras.device_code=ld.`code` and ras.tenant_id=ld.tenant_id
        <where>
              ld.delete_state=1
              <if test="airconditionDeviceRecord.functionIdentifier != null and airconditionDeviceRecord.functionIdentifier != ''">
                  and ras.function_identifier = #{airconditionDeviceRecord.functionIdentifier}
              </if>
              <if test="airconditionDeviceRecord.tenantId != null and airconditionDeviceRecord.tenantId != ''">
                  and ras.tenant_id=#{airconditionDeviceRecord.tenantId}
              </if>
              <if test="airconditionDeviceRecord.areaPath != null and airconditionDeviceRecord.areaPath.size() gt 0 ">
                  <foreach collection="airconditionDeviceRecord.areaPath" item="areaPath" open="and (" separator="or" close=")">
                       (ras.area_path = #{areaPath} or ras.area_path like concat(#{areaPath},':%'))
                  </foreach>
              </if>
        </where>
        )  ad
        left join (
        select id,aircondition_device_id,update_time,type,fan_speed,tenant_id,
        sum(if(fan_speed = 1,duration,0)) low_fan_duration,
        sum(if(fan_speed = 2,duration,0)) mid_fan_duration,
        sum(if(fan_speed = 3,duration,0)) high_fan_duration
        from ac_aircondition_device_record
        <where>
          <if test="airconditionDeviceRecord.queryStartTime != null and airconditionDeviceRecord.queryEndTime != null">
            and  (update_time <![CDATA[ >= ]]>#{airconditionDeviceRecord.queryStartTime}  and update_time <![CDATA[ <= ]]>#{airconditionDeviceRecord.queryEndTime})
          </if>
          <if test="airconditionDeviceRecord.tenantId != null and airconditionDeviceRecord.tenantId != ''">
            and tenant_id=#{airconditionDeviceRecord.tenantId}
          </if>
        </where>
        group by aircondition_device_id
          ) adr  on adr.aircondition_device_id=ad.device_id and ad.tenant_id=adr.tenant_id
          left join ac_host_device hd on ad.host_device_id=hd.device_id and ad.tenant_id=hd.tenant_id
      order by ad.update_time
  </select>
</mapper>