<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.airconditioner.mapper.TimingTemperatureControlMapper">

    <resultMap id="regularTemperatureControlInfoMap" type="com.easylinkin.linkappapi.airconditioner.entity.RegularTemperatureControlInfo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
        <result column="delete_status" property="deleteStatus"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
    </resultMap>

    <select id="getPage" resultType="com.easylinkin.linkappapi.airconditioner.entity.ext.RegularTemperatureControlExt">
        select
        i.id,
        i.`name`,
        i.description,
        i.status,
        i.create_time createTime,
        je.job_name ruleEngineId
        from
        regular_temperature_control_info i left join regular_temperature_control_execute_detail d on i.id=d.regular_tci_id
        left join linkapp_job_entity je on d.job_id=je.id
        <where>
            i.delete_status=0
            <if test="rtce.tenantId != null">
                and i.tenant_id=#{rtce.tenantId}
            </if>
            <if test="rtce.status != null">
                and i.status=#{rtce.status}
            </if>
            <if test="rtce.name != null and rtce.name != ''">
                <bind name="likeName" value=" '%' +rtce.name+ '%' " />
                and i.name like #{likeName}
            </if>
        </where>
        group by i.create_time desc
    </select>

    <select id="getById" resultType="com.easylinkin.linkappapi.airconditioner.entity.ext.RegularTemperatureControlExt">
        select
        i.id,
        i.`name`,
        i.description,
        i.status,
        i.tenant_id tenantId,
        i.create_time createTime,
        d.execute_time executeTime,
        d.execute_data executeData,
        d.start_time startTime,
        d.end_time endTime,
        d.week_execute_flag weekExecuteFlag,
        d.not_execute_date notExecuteDate,
        d.job_id jobId
        from
        regular_temperature_control_info i left join regular_temperature_control_execute_detail d
        on i.id=d.regular_tci_id
        <where>
            i.delete_status=0 and i.id=#{id}
        </where>
    </select>

    <select id="getDeviceInfoById" resultMap="selectDevicesMap">
        select
        d.id,
        d.code,
        d.name,
        d.status,
        d.battery,
        d.remark,
        d.company_id,
        d.project_id,
        d.device_unit_id,
        d.install_time,
        s.area_id,
        s.area_path,
        d.create_time
        from
        regular_temperature_control_ref_device rd left join linkapp_device d on d.code=rd.device_code
        left join device_ref_area_scope s on s.device_code=d.code
        <where>
            s.function_identifier='air_conditioner'
            and rd.regular_tci_id=#{id}
        </where>
        group by d.code
    </select>

    <!--<update id="batchUpdateStatus" >
        <foreach collection="rtceList" item="rtce" index="index" open="" close="" separator=";">
            update
                regular_temperature_control_info
            <set>
                <if test="rtce.status != null">
                    status = #{rtce.status}
                </if>
                <if test="rtce.deleteStatus != null">
                    delete_status = #{rtce.deleteStatus}
                </if>
            </set>
            <where>
                id = #{rtce.id}
            </where>
        </foreach>
    </update>-->

    <update id="updateStatus" >
        update
        regular_temperature_control_info
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="deleteStatus != null">
                delete_status = #{deleteStatus},
            </if>
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <update id="updateInfo" >
        update
        regular_temperature_control_info
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
            <if test="modifier != null">
                modifier = #{modifier},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="deleteStatus != null">
                delete_status = #{deleteStatus},
            </if>
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <update id="updateDetail" >
        update
        regular_temperature_control_execute_detail
        <set>
            <if test="regularTciId != null">
                regular_tci_id = #{regularTciId},
            </if>
            <if test="executeTime != null">
                execute_time = #{executeTime},
            </if>
            <if test="executeData != null">
                execute_data = #{executeData},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="weekExecuteFlag != null">
                week_execute_flag = #{weekExecuteFlag},
            </if>
            <if test="notExecuteDate != null">
                not_execute_date = #{notExecuteDate},
            </if>
            <if test="cronExpression != null">
                cron_expression = #{cronExpression},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
            <if test="modifier != null">
                modifier = #{modifier},
            </if>
            <if test="jobId != null">
                job_id = #{jobId},
            </if>
        </set>
        <where>
            regular_tci_id = #{regularTciId}
        </where>
    </update>

    <insert id="addInfo"  keyProperty="id" useGeneratedKeys="true" parameterType="com.easylinkin.linkappapi.airconditioner.entity.ext.RegularTemperatureControlExt">
        insert into regular_temperature_control_info
        (name, description, tenant_id, create_time, modify_time, creator, modifier, status, delete_status)
        values
        (
            #{rtce.name},
            #{rtce.description},
            #{rtce.tenantId},
            #{rtce.createTime},
            #{rtce.modifyTime},
            #{rtce.creator},
            #{rtce.modifier},
            #{rtce.status},
            #{rtce.deleteStatus}
        )
    </insert>

    <insert id="addDetail" parameterType="com.easylinkin.linkappapi.airconditioner.entity.ext.RegularTemperatureControlExt">
        insert into regular_temperature_control_execute_detail
        (regular_tci_id, execute_time, execute_data, start_time, end_time, week_execute_flag, not_execute_date, cron_expression,
         tenant_id, create_time, modify_time, creator, modifier, job_id)
        values
        (
            #{rtce.regularTciId},
            #{rtce.executeTime},
            #{rtce.executeData},
            #{rtce.startTime},
            #{rtce.endTime},
            #{rtce.weekExecuteFlag},
            #{rtce.notExecuteDate},
            #{rtce.cronExpression},
            #{rtce.tenantId},
            #{rtce.createTime},
            #{rtce.modifyTime},
            #{rtce.creator},
            #{rtce.modifier},
            #{rtce.jobId}
        )
    </insert>

    <select id="validRepeat" resultMap="regularTemperatureControlInfoMap">
        select
        id,
        `name`,
        description,
        status,
        create_time
        from
        regular_temperature_control_info
        <where>
            delete_status=0
            <if test="rtci.tenantId != null">
                and tenant_id=#{rtci.tenantId}
            </if>
            <if test="rtci.name != null and rtci.name != ''">
                and name =#{rtci.name}
            </if>
        </where>
    </select>

    <select id="validRepeatExcludeSourceName" resultMap="regularTemperatureControlInfoMap">
        select
        id,
        `name`,
        description,
        status,
        create_time
        from
        regular_temperature_control_info
        <where>
            delete_status=0
            and id != #{rtci.id}
            <if test="rtci.tenantId != null">
                and tenant_id=#{rtci.tenantId}
            </if>
            <if test="rtci.name != null and rtci.name != ''">
                and name =#{rtci.name}
            </if>
        </where>
    </select>

    <insert id="bindDevice" parameterType="com.easylinkin.linkappapi.airconditioner.entity.ext.RegularTemperatureControlExt">
        insert into regular_temperature_control_ref_device
        (regular_tci_id, device_code)
        values
        <foreach collection="deviceList" item="device" separator=",">
            (
            #{id},
            #{device.code}
            )
        </foreach>
    </insert>

    <!-- 先解绑, 在insert -->
    <delete id="unbindDevice">
        delete from regular_temperature_control_ref_device where regular_tci_id=#{id}
    </delete>

    <select id="getRefDeviceInfo" resultType="com.easylinkin.linkappapi.airconditioner.entity.ext.RefDeviceInfo">
        select
        rd.regular_tci_id id,
        d.code,
        d.device_unit_id deviceUnitId
        from
        regular_temperature_control_ref_device rd left join linkapp_device d on rd.device_code=d.`code`
        left join linkapp_device_unit du on d.device_unit_id=du.id
        <where>
            rd.regular_tci_id=#{id}
        </where>
        group by d.code
    </select>

    <resultMap id="selectDevicesMap" type="com.easylinkin.linkappapi.device.entity.Device">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="delete_state" property="deleteState"/>
        <result column="linkthing_delete" property="linkthingDelete"/>
        <result column="online_state" property="onlineState"/>
        <result column="remark" property="remark"/>
        <result column="indoor_location" property="indoorLocation"/>
        <result column="alarm_switch" property="alarmSwitch"/>
        <result column="device_unit_id" property="deviceUnitId"/>
        <result column="install_time" property="installTime"/>
        <result column="repair_time" property="repairTime"/>
        <result column="next_repair_time" property="nextRepairTime"/>
        <result column="last_push_time" property="lastPushTime"/>
        <result column="create_time" property="createTime"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="area_id" property="areaId"/>
        <result column="latitude" property="latitude"/>
        <result column="longitude" property="longitude"/>
        <result column="site" property="site"/>
        <result column="area_path" property="areaPath"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="spaceName" property="spaceName"/>
        <result column="deviceTypeName" property="deviceTypeName"/>
        <result column="areaName" property="areaName"/>
        <result column="deviceUnitName" property="deviceUnitName"/>
        <result column="deviceUnitCode" property="deviceUnitCode"/>
        <result column="deviceUnitVersion" property="deviceUnitVersion"/>
        <result column="spaceId" property="spaceId"/>
        <result column="deviceTypeId" property="deviceTypeId"/>
    </resultMap>

</mapper>
