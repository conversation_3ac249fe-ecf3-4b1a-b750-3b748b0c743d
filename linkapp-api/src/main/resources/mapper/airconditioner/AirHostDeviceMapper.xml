<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.airconditioner.mapper.AirHostDeviceMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.airconditioner.entity.AirHostDevice">
  <!--@mbg.generated-->
  <!--@Table dev-linkapp-mysql.ac_host_device-->
  <id column="device_id" jdbcType="VARCHAR" property="deviceId" />
  <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
  <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
  <result column="wattmeter_id" jdbcType="VARCHAR" property="wattmeterId" />
  <result column="switch_state" jdbcType="TINYINT" property="switchState" />
  <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
</resultMap>

  <!--<resultMap id="BaseResultMapGroupBy" type="com.easylinkin.linkappapi.airconditioner.entity.AirHostDevice">-->
    <!--<id column="device_id" jdbcType="VARCHAR" property="deviceId" />-->
    <!--<result column="device_code" jdbcType="VARCHAR" property="deviceCode" />-->
    <!--<result column="device_name" jdbcType="VARCHAR" property="deviceName" />-->
    <!--<result column="wattmeter_id" jdbcType="VARCHAR" property="wattmeterId" />-->
    <!--<result column="switch_state" jdbcType="TINYINT" property="switchState" />-->
    <!--<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />-->
    <!--<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />-->
    <!--<collection property="airconditionDeviceRecordList"-->
                <!--ofType="com.easylinkin.linkappapi.airconditioner.entity.AirconditionDeviceRecord">-->
      <!--<id column="id" jdbcType="BIGINT" property="id" />-->
      <!--<result column="aircondition_device_id" jdbcType="VARCHAR" property="airconditionDeviceId" />-->
      <!--<result column="type" jdbcType="TINYINT" property="type" />-->
      <!--<result column="fan_speed" jdbcType="TINYINT" property="fanSpeed" />-->
      <!--<result column="duration" jdbcType="DECIMAL" property="duration" />-->
      <!--<result column="low_fan" jdbcType="DECIMAL" property="lowFan" />-->
      <!--<result column="mid_fan" jdbcType="DECIMAL" property="midFan" />-->
      <!--<result column="high_fan" jdbcType="DECIMAL" property="highFan" />-->
      <!--<result column="low_fan_duration" jdbcType="DECIMAL" property="lowFanDuration" />-->
      <!--<result column="mid_fan_duration" jdbcType="DECIMAL" property="midFanDuration" />-->
      <!--<result column="high_fan_duration" jdbcType="DECIMAL" property="highFanDuration" />-->
      <!--<result column="record_create_time" jdbcType="TIMESTAMP" property="createTime" />-->
      <!--<result column="record_update_time" jdbcType="TIMESTAMP" property="updateTime" />-->
    <!--</collection>-->
  <!--</resultMap>-->

  <!--<select id="selectGroupByStatistics" resultMap="BaseResultMapGroupBy">-->
      <!--select hd.*,adr.* from ac_host_device hd-->
       <!--left join (select adr.aircondition_device_id,ad.low_fan,ad.mid_fan,ad.high_fan,adr.fan_speed,type-->
              <!--,adr.low_fan_duration,adr.mid_fan_duration,adr.high_fan_duration,ad.host_device_id-->
          <!--from (-->
            <!--select id,aircondition_device_id,type,fan_speed,-->
            <!--sum(if(fan_speed = 1,duration,0)) low_fan_duration,-->
            <!--sum(if(fan_speed = 2,duration,0)) mid_fan_duration,-->
            <!--sum(if(fan_speed = 3,duration,0)) high_fan_duration-->
            <!--from ac_aircondition_device_record-->
            <!--<where>-->
              <!--<if test="airHostDevice.queryStartTime != null and airHostDevice.queryEndTime != null">-->
                <!--and  (update_time <![CDATA[ >= ]]>#{airHostDevice.queryStartTime}  and update_time <![CDATA[ <= ]]>#{airHostDevice.queryEndTime})-->
              <!--</if>-->
            <!--</where>-->
            <!--group by aircondition_device_id-->
            <!--&lt;!&ndash;<if test="page != null">&ndash;&gt;-->
              <!--&lt;!&ndash;limit  #{page.current},#{page.size}&ndash;&gt;-->
            <!--&lt;!&ndash;</if>&ndash;&gt;-->
          <!--) adr-->
           <!--left join ac_aircondition_device  ad on adr.aircondition_device_id=ad.device_id) adr-->
		<!--on hd.device_id=adr.host_device_id-->
  <!--</select>-->


  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    device_id, device_code, device_name, wattmeter_id, switch_state, create_time, update_time,tenant_id
  </sql>
  <insert id="batchInsert" keyColumn="device_id" keyProperty="deviceId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into ac_host_device
    (device_id,device_code, device_name, wattmeter_id, switch_state, create_time, update_time,tenant_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deviceId,jdbcType=VARCHAR},#{item.deviceCode,jdbcType=VARCHAR}, #{item.deviceName,jdbcType=VARCHAR}, #{item.wattmeterId,jdbcType=VARCHAR},
        #{item.switchState,jdbcType=TINYINT},
       #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},#{item.tenantId,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>