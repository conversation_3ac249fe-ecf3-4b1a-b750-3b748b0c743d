<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.project.mapper.ProjectMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.project.entity.Project">
    <id column="id" property="id"/>
    <result column="project_name" property="projectName"/>
    <result column="project_code" property="projectCode"/>
    <result column="status" property="status"/>
    <result column="remark" property="remark"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>

</mapper>
