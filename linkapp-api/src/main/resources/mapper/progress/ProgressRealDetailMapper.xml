<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.progress.mapper.ProgressRealDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.progress.entity.ProgressRealDetail">
        <id column="id_" property="id" />
        <result column="progress_task_id_" property="progressTaskId" />
        <result column="real_start_time_" property="realStartTime" />
        <result column="real_end_time_" property="realEndTime" />
        <result column="percentage" property="percentage" />
        <result column="build_describe_" property="buildDescribe" />
        <result column="imgs_" property="imgs" />
        <result column="drawing_files_" property="drawingFiles" />
        <result column="creator_" property="creator" />
        <result column="create_time_" property="createTime" />
        <result column="modifier_" property="modifier" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>
    <resultMap id="RealProgressDtoMap" extends="com.easylinkin.linkappapi.progress.mapper.ProgressInfoMapper.BaseResultMap" type="com.easylinkin.linkappapi.progress.entity.dto.ProgressRealDTO">
        <association property="joinModelNum" column="id_" select="com.easylinkin.linkappapi.progress.mapper.ProgressIntegrateRefMapper.queryJoinModelNum" />
        <collection property="progressRealDetailList" column="id_" select="queryDetailListByTaskId" />
        <collection property="parentProgressList" column="parent_ids_" select="com.easylinkin.linkappapi.progress.mapper.ProgressInfoMapper.queryByParentIdsStr"/>
    </resultMap>

    <resultMap id="ProgressRecordVoMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.bigScreen.vo.ProgressRecordVo">
        <association property="bimIntegrateItemList" javaType="java.util.List" column="progress_task_id_" select="com.easylinkin.linkappapi.systemsettings.mapper.BimIntegrateItemMapper.queryProgressBindIntegrateList"/>
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_progress_real_detail where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_progress_real_detail where id_ = #{id} 
    </select>

    <select id="getDtoPage" parameterType="com.easylinkin.linkappapi.progress.entity.vo.ProgressRealDetailVo" resultMap="RealProgressDtoMap" >
        select tt.*
        from (select t1.*, ifnull(t2.isParent, 0) as isParent
              from app_progress_info t1
                       LEFT JOIN (SELECT count(1) isParent, api.parent_id_
                                  from app_progress_info api
                                  where api.type_ = 2
                                    and api.deleted_ = 0
                                  GROUP BY api.parent_id_) t2
                                 on t1.id_ = t2.parent_id_
              where t1.deleted_ = 0
                and t1.type_ = 2) tt
        where 1=1 <!--tt.isParent = 0-->
        <if test="progressRealDetailVo.progressInfo != null">
            <if test="progressRealDetailVo.progressInfo.tenantId != null and progressRealDetailVo.progressInfo.tenantId != ''">
                and tt.tenant_id_ = #{progressRealDetailVo.progressInfo.tenantId}
            </if>
            <if test="progressRealDetailVo.progressInfo.parentIds != null and progressRealDetailVo.progressInfo.parentIds != ''">
                and tt.parent_ids_ like concat('', #{progressRealDetailVo.progressInfo.parentIds}, '%')
            </if>
        </if>
        order by tt.create_time_
    </select>
    <select id="queryDetailListByTaskId" resultMap="BaseResultMap">
        select * from app_progress_real_detail where progress_task_id_ = #{progressTaskId} and deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="lastProgressRecordPage" parameterType="com.easylinkin.linkappapi.bigScreen.vo.ProgressCountParamVo" resultMap="ProgressRecordVoMap" >
        select
            aprd.*
        from
            app_progress_real_detail aprd ,
            app_progress_info api
        where
            aprd.progress_task_id_ = api.id_
          and aprd.deleted_ = 0
          and api.deleted_ = 0
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and api.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>
        order by aprd.create_time_ desc
    </select>
</mapper>
