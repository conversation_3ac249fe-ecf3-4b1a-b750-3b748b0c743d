<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.progress.mapper.ProgressWarnMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.progress.entity.ProgressWarn">
        <id column="id_" property="id"/>
        <result column="progress_id_" property="progressId"/>
        <result column="real_start_time_" property="realStartTime"/>
        <result column="real_end_time_" property="realEndTime"/>
        <result column="real_percentage_" property="realPercentage"/>
        <result column="warn_state_" property="warnState"/>
        <result column="plan_work_day_" property="planWorkDay"/>
        <result column="start_state_" property="startState"/>
        <result column="creator_" property="creator"/>
        <result column="create_time_" property="createTime"/>
        <result column="modifier_" property="modifier"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="deleted_" property="deleted"/>
    </resultMap>

    <resultMap id="ProgressWarnDtoMap"
               extends="com.easylinkin.linkappapi.progress.mapper.ProgressInfoMapper.BaseResultMap"
               type="com.easylinkin.linkappapi.progress.entity.dto.ProgressInfoWarnDTO">
        <association property="progressLagInfo" column="id_"
                     select="com.easylinkin.linkappapi.progress.mapper.ProgressLagInfoMapper.getOneByProgressId"/>
        <association property="progressWarn" column="id_" select="getOneByProgressId"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_progress_warn
        where deleted_ = 0
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_progress_warn
        where id_ = #{id}
    </select>

    <select id="selectDtoPage" parameterType="com.easylinkin.linkappapi.progress.entity.vo.ProgressWarnVo"
            resultMap="ProgressWarnDtoMap">
        select api.*
        from app_progress_info api
        where api.deleted_ = 0
          and api.type_ = 2
        <if test="progressWarnVo.progressInfo != null">
            <if test="progressWarnVo.progressInfo.parentIds != null and progressWarnVo.progressInfo.parentIds != ''">
                and api.parent_ids_ like concat('', #{progressWarnVo.progressInfo.parentIds}, '%')
            </if>
        </if>
        order by api.create_time_
    </select>

    <select id="getOneByProgressId" resultMap="BaseResultMap">
        select w.*
        from app_progress_warn w
        where w.deleted_ = 0
          and w.progress_id_ = #{progressId}
        order by w.create_time_ desc
        limit 1
    </select>

    <select id="selectByProgressId" resultMap="BaseResultMap">
        select
            apw.*
        from
            app_progress_warn apw
        where
            apw.deleted_ = 0
          and apw.progress_id_ = #{progressId}
        order by apw.create_time_ desc
        limit 1
    </select>
</mapper>
