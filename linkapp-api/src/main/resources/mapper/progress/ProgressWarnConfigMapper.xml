<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.progress.mapper.ProgressWarnConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.progress.entity.ProgressWarnConfig">
        <id column="id_" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="finish_less_" property="finishLess" />
        <result column="finish_more_" property="finishMore" />
        <result column="undone_less_" property="undoneLess" />
        <result column="undone_more_" property="undoneMore" />
        <result column="parent_finish_less_" property="parentFinishLess" />
        <result column="parent_finish_more_" property="parentFinishMore" />
        <result column="parent_undone_less_" property="parentUndoneLess" />
        <result column="parent_undone_more_" property="parentUndoneMore" />
        <result column="creator_" property="creator" />
        <result column="create_time_" property="createTime" />
        <result column="modifier_" property="modifier" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_progress_warn_config where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_progress_warn_config where id_ = #{id} 
    </select>


</mapper>
