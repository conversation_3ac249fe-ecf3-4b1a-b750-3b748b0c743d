<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.progress.mapper.ProgressInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.progress.entity.ProgressInfo">
        <id column="id_" property="id"/>
        <result column="name_" property="name"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="code_" property="code"/>
        <result column="start_time_" property="startTime"/>
        <result column="end_time_" property="endTime"/>
        <result column="total_work_" property="totalWork"/>
        <result column="type_" property="type"/>
        <result column="plan_type_" property="planType"/>
        <result column="before_work_" property="beforeWork"/>
        <result column="point_" property="point"/>
        <result column="parent_id_" property="parentId"/>
        <result column="parent_ids_" property="parentIds"/>
        <result column="creator_" property="creator"/>
        <result column="create_time_" property="createTime"/>
        <result column="modifier_" property="modifier"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="deleted_" property="deleted"/>
    </resultMap>
    <resultMap id="ProgressDtoMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.progress.entity.dto.ProgressInfoDTO">
    </resultMap>
    <resultMap id="ScreenProgressResultMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.progress.entity.dto.ScreenProgressInfoDTO">
        <association property="progressWarn" column="id_" select="com.easylinkin.linkappapi.progress.mapper.ProgressWarnMapper.selectByProgressId"/>
        <collection property="integrateList" column="id_" select="com.easylinkin.linkappapi.systemsettings.mapper.BimIntegrateItemMapper.queryProgressBindIntegrateList" />
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_progress_info
        where deleted_ = 0
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_progress_info
        where id_ = #{id}
    </select>

    <select id="selectDtoPage" parameterType="com.easylinkin.linkappapi.progress.entity.vo.ProgressInfoVo"
            resultMap="ProgressDtoMap">
        select api.*
        from app_progress_info api
        where api.deleted_ = 0
        <if test="progressInfoVo.type != null">
          and api.type_ = #{progressInfoVo.type}
        </if>
          and api.tenant_id_ = #{progressInfoVo.tenantId}
        <if test="progressInfoVo.parentIds != null and progressInfoVo.parentIds != ''">
            and api.parent_ids_ like concat('', #{progressInfoVo.parentIds}, '%')
        </if>
        order by api.create_time_
    </select>

    <select id="queryByParentIdsStr" parameterType="string"  resultMap="BaseResultMap">
        select p.*
        from app_progress_info p
        where p.deleted_ = 0 and p.type_ = 2
          and p.id_ in (${parentIdsStr}0)
    </select>


    <select id="screenProgressTaskInfo" parameterType="com.easylinkin.linkappapi.progress.entity.vo.ProgressInfoVo"
            resultMap="ScreenProgressResultMap">
        select
            a.*
        from
            app_progress_info a
        where
            a.deleted_ = 0
        <if test="progressInfoVo.type != null and progressInfoVo.type != ''">
            and a.type_ = #{progressInfoVo.type}
        </if>
        <if test="progressInfoVo.tenantId != null and progressInfoVo.tenantId != ''">
            and a.tenant_id_ = #{progressInfoVo.tenantId}
        </if>
          <if test="progressInfoVo.parentIdsList != null and progressInfoVo.parentIdsList.size() != 0">
                <foreach collection="progressInfoVo.parentIdsList" item="item" index="index" open="and (a.parent_ids_ like concat(''," close=",'%'))" separator=",'%') or a.parent_ids_ like concat('',">
                    '${item}'
                </foreach>
          </if>
        order by a.start_time_ asc
    </select>

    <select id="leafProgressCountGroupByMonthState"
            parameterType="com.easylinkin.linkappapi.bigScreen.vo.ProgressCountParamVo"
            resultType="com.easylinkin.linkappapi.bigScreen.vo.CountInfoVo">
        select r.codeKey      as codeKey,
               r.start_state_ as numStr,
               count(*)       as num
        from
        (
        select api.id_,
               api.name_,
               date_format(api.end_time_, '%Y-%m') as codeKey,
               if(apw.start_state_ = 4,
                  4,
                  0)                               as start_state_
        from
        app_progress_info api
            left join
        (
        select t.id_,
               t.name_ as taskName,
        case
        when exists(
        select 1
        from app_progress_info t1
        where t1.deleted_ = 0
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and t1.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>
        and t.id_ = t1.parent_id_) then 0
            else 1
            end as isLeaf
        from app_progress_info t
        where t.deleted_ = 0
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and t.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>
        ) tt on
            api.id_ = tt.id_
            left join app_progress_warn apw on
            api.id_ = apw.progress_id_
        where api.deleted_ = 0
          and apw.deleted_ = 0
          and api.type_ = 2
          and tt.isLeaf = 1
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and api.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>
        ) r
        group by r.codeKey,
                 numStr
    </select>

    <select id="lagProgressPage" parameterType="com.easylinkin.linkappapi.bigScreen.vo.ProgressCountParamVo" resultType="com.easylinkin.linkappapi.bigScreen.vo.ProgressInfoRecordVo">
        select api.id_                                                                        as id,
               apw.id_                                                                        as warnId,
               api.name_                                                                      as progressName,
               apw.real_percentage_                                                           as realPercentage,
               api.start_time_                                                                as startTime,
               api.end_time_                                                                  as endTime,
               apw.real_end_time_                                                             as realEndTime,
               ifnull(apw.real_end_time_, now())                                              as dynamicRealEndTime,
               abs(timestampdiff(day,
                                 date_format(api.end_time_, '%Y-%m-%d'),
                                 date_format(ifnull(apw.real_end_time_, now()), '%Y-%m-%d'))) as lagDay
        from app_progress_warn apw,
             app_progress_info api
        where apw.progress_id_ = api.id_
          and apw.deleted_ = 0
          and api.deleted_ = 0
          and api.type_ = 2
          and apw.warn_state_ = 3
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and api.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>
        order by lagDay desc
    </select>

    <select id="progressCountGroupByState" parameterType="com.easylinkin.linkappapi.bigScreen.vo.ProgressCountParamVo"
            resultType="com.easylinkin.linkappapi.bigScreen.vo.CountInfoVo">
        select apw.warn_state_ as codeKey,
               count(*)        as num
        from app_progress_info api,
             app_progress_warn apw
        where api.deleted_ = 0
          and apw.deleted_ = 0
          and api.id_ = apw.progress_id_
          and api.type_ = 2
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and api.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>
        group by codeKey
        having codeKey is not null
    </select>

    <select id="progressLagCountGroupByLagState"
            parameterType="com.easylinkin.linkappapi.bigScreen.vo.ProgressCountParamVo"
            resultType="com.easylinkin.linkappapi.bigScreen.vo.CountInfoVo">
        select count(distinct api.id_) as num,
               apli.reason_state_      as codeKey
        from app_progress_info api,
             app_progress_warn apw,
             app_progress_lag_info apli
        where api.deleted_ = 0
          and apw.deleted_ = 0
          and apli.deleted_ = 0
          and api.id_ = apw.progress_id_
          and api.id_ = apli.progress_id_
          and api.type_ = 2
          and apw.warn_state_ = 3
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and api.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>
        group by codeKey
    </select>

    <select id="noLagReasonCount" parameterType="com.easylinkin.linkappapi.bigScreen.vo.ProgressCountParamVo"
            resultType="int">
        select count(distinct api.id_) as num
        from app_progress_warn apw
                 left join
             app_progress_info api on apw.progress_id_ = api.id_
                 left join
             app_progress_lag_info apli on api.id_ = apli.progress_id_
        where api.deleted_ = 0
          and apw.deleted_ = 0
          and api.type_ = 2
          and apw.warn_state_ = 3
          and apli.id_ is null
    </select>

    <select id="queryLeafProgressList" parameterType="com.easylinkin.linkappapi.bigScreen.vo.ProgressCountParamVo"
            resultType="java.util.Map">
        select api.id_ as progressId,
               api.type_ as progressType,
               api.total_work_ as totalWork,
               abii.parent_ids_ as parentIds,
               SUBSTRING_INDEX(substring(abii.parent_ids_, LOCATE(',', abii.parent_ids_, 1) + 1), ',', 2) as buildingId,
               (select sum(aprd.percentage)
                from app_progress_real_detail aprd
                where aprd.deleted_ = 0
                  and aprd.progress_task_id_ = api.id_)                                                   as percentage
        from app_progress_info api,
             app_progress_integrate_ref apir,
             app_bim_integrate_item abii
        where api.deleted_ = 0
          and api.id_ = apir.progress_id_
          and abii.resource_id_ = apir.resource_id_
          and api.type_ = 2
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and api.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>
    </select>

    <select id="buildingLeafProgressCount" parameterType="com.easylinkin.linkappapi.bigScreen.vo.ProgressCountParamVo"
            resultType="com.easylinkin.linkappapi.bigScreen.vo.CountInfoVo">
        select tt.buildingId                                                    as codeKey,
               sum(tt.percentage * tt.totalWork) / sum(ifnull(tt.totalWork, 1)) as numStr,
               tt.integrateId                                                   as num,
               (select b.name
                from app_building b
                where b.id = tt.buildingId)                                     as buildingName
        from
        (
        select n.buildingId,
               n.percentage,
               n.totalWork,
               n.progressId,
               SUBSTRING_INDEX(group_concat(n.integrateId), ',', 1) as integrateId
        from
        (
        select distinct abir.building_id_       as buildingId,
                        ifnull(t.percentage, 0) as percentage,
                        t.totalWork,
                        abii2.id_               as integrateId,
                        t.progressId
        from app_bim_integrate_item abii2,
             app_building_integrate_ref abir,
        (
        select api.id_                                  as progressId,
               api.type_                                as progressType,
               api.total_work_                          as totalWork,
               abii.parent_ids_                         as parentIds,
               SUBSTRING_INDEX(
                       SUBSTRING_INDEX(substring(abii.parent_ids_, LOCATE(',', abii.parent_ids_, 1) + 1), ',', 2), ',',
                       -1)                              as integrateId,
               (select sum(ifnull(aprd.percentage, 0))
                from app_progress_real_detail aprd
                where aprd.deleted_ = 0
                  and aprd.progress_task_id_ = api.id_) as percentage,
               abii.id_                                    id
        from app_progress_info api,
             app_progress_integrate_ref apir,
             app_bim_integrate_item abii
        where api.deleted_ = 0
          and api.id_ = apir.progress_id_
          and abii.resource_id_ = apir.resource_id_
          and api.type_ = 2
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and api.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>
        ) t
        where abir.deleted_ = 0
          and abii2.id_ = t.integrateId
          and abii2.resource_id_ = abir.resource_id_) n
        group by n.buildingId,
                 n.progressId,
                 n.percentage,
                 n.totalWork
        ) tt
        group by codeKey
    </select>

    <select id="allProgressCountGroupByMonthState"
            parameterType="com.easylinkin.linkappapi.bigScreen.vo.ProgressCountParamVo"
            resultType="com.easylinkin.linkappapi.bigScreen.vo.CountInfoVo">
        select r.codeKey      as codeKey,
               r.start_state_ as numStr,
               count(*)       as num
        from
        (
        select api.id_,
               api.name_,
               date_format(api.end_time_, '%Y-%m') as codeKey,
               if(apw.start_state_ = 4,
                  4,
                  0)                               as start_state_
        from app_progress_info api
                 left join app_progress_warn apw
                           on
                               api.id_ = apw.progress_id_
        where api.deleted_ = 0
          and apw.deleted_ = 0
          and api.type_ = 2
        <if test="progressCountParamVo.tenantId != null and progressCountParamVo.tenantId != ''">
            and api.tenant_id_ = #{progressCountParamVo.tenantId}
        </if>) r
    group by r.codeKey,
             numStr
    </select>

    <select id="allScreenProgressInfo" resultType="com.easylinkin.linkappapi.bigScreen.vo.ScreenProgressInfo">
        WITH RECURSIVE task_hierarchy AS (
            SELECT
                id_,
                name_,
                start_time_,
                end_time_,
                parent_id_,
                CAST(name_ AS CHAR(1000)) AS full_path
            FROM app_progress_info
            WHERE parent_id_ = 0 and deleted_ = 0 and tenant_id_ = #{tenantId}
            and type_ = 1 and plan_type_ = 1

            UNION ALL

            SELECT
                t.id_,
                t.name_,
                t.start_time_,
                t.end_time_,
                t.parent_id_,
                CONCAT(th.full_path, '/', t.name_) AS full_path
            FROM app_progress_info t
                     INNER JOIN task_hierarchy th ON t.parent_id_ = th.id_
        )
        select * from (
            SELECT
                th.id_ as id,
                th.full_path AS fullPath,
                th.start_time_ AS startTime,
                th.end_time_ AS endTime,
                pr.real_start_time_ AS realStartTime_,
                pr.real_end_time_ AS realEndTime
            FROM task_hierarchy th
            LEFT JOIN app_progress_real_detail pr ON pr.progress_task_id_ = th.id_
            where th.parent_id_ != 0
            ORDER BY full_path
        ) as a order by a.startTime asc
    </select>

    <select id="getTaskTimeRange" resultType="java.util.Map">
        SELECT
            MIN(start_time_) as earliestStartTime,
            MAX(end_time_) as latestEndTime
        FROM app_progress_info
        WHERE deleted_ = 0
          AND tenant_id_ = #{tenantId}
          AND type_ = 2
          AND start_time_ IS NOT NULL
          AND end_time_ IS NOT NULL
          <if test="planIds != null and planIds.size() > 0">
          AND parent_id_ IN
              <foreach collection="planIds" item="planId" open="(" separator="," close=")">
                  #{planId}
              </foreach>
          </if>
    </select>
</mapper>
