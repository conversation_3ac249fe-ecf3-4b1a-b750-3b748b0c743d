<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.progress.mapper.ProgressIntegrateRefMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.progress.entity.ProgressIntegrateRef">
        <id column="id_" property="id"/>
        <result column="resource_id_" property="resourceId"/>
        <result column="progress_plan_id_" property="progressPlanId"/>
        <result column="progress_id_" property="progressId"/>
        <result column="creator_" property="creator"/>
        <result column="create_time_" property="createTime"/>
        <result column="modifier_" property="modifier"/>
        <result column="modify_time_" property="modifyTime"/>
        <!-- <result column="deleted_" property="deleted"/> -->
    </resultMap>

    <resultMap id="vo" type="com.easylinkin.linkappapi.progress.entity.vo.ProgressIntegrateRefVo">
        <result column="file_id_" property="fileId"/>
        <result column="bim_resource_id_" property="bimResourceId"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_progress_integrate_ref
        where 1=1
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_progress_integrate_ref
        where id_ = #{id}
    </select>

    <select id="queryJoinModelNum" parameterType="long" resultType="integer">
        select count(*)
        from app_progress_integrate_ref  f
        where f.progress_id_ = #{progressId}
    </select>
    <select id="selectVoByProcessId" resultMap="vo">
        select apir.*,abii.bim_resource_id_ ,abii.file_id_ from app_progress_integrate_ref apir
            left join app_bim_integrate_item abii on apir.resource_id_ = abii.resource_id_
        where apir.progress_id_  = #{progressId} and abii.integrate_id_ = #{integrateId}
    </select>
    <select id="selectVo" resultMap="vo">
        select apir.*,abii.bim_resource_id_ ,abii.file_id_ from app_progress_integrate_ref apir
        left join app_bim_integrate_item abii on apir.resource_id_ = abii.resource_id_
        where 1=1
        <if test="planId != null and planId.size() != 0">
        and  apir.progress_plan_id_ in
            <foreach collection="planId" item="item" index="index"
              open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
          and apir.resource_id_  = #{resourceId}
          and abii.integrate_id_ = #{integrateId}
    </select>

    <!-- 根据resourceId查询主计划的关联关系（带租户隔离） -->
    <select id="selectMainPlanByResourceId" resultMap="BaseResultMap">
        SELECT apir.*
        FROM app_progress_integrate_ref apir
        INNER JOIN app_progress_info api ON apir.progress_plan_id_ = api.id_
        WHERE apir.resource_id_ = #{resourceId}
          AND api.tenant_id_ = #{tenantId}
          AND api.type_ = 1
          AND api.plan_type_ = 1
          AND api.deleted_ = 0
        LIMIT 1
    </select>
</mapper>
