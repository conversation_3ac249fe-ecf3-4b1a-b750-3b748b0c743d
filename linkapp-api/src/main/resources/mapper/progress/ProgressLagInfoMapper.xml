<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.progress.mapper.ProgressLagInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.progress.entity.ProgressLagInfo">
        <id column="id_" property="id"/>
        <result column="progress_id_" property="progressId"/>
        <result column="reason_state_" property="reasonState"/>
        <result column="memo_" property="memo"/>
        <result column="creator_" property="creator"/>
        <result column="create_time_" property="createTime"/>
        <result column="modifier_" property="modifier"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="deleted_" property="deleted"/>
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_progress_lag_info
        where deleted_ = 0
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_progress_lag_info
        where id_ = #{id}
    </select>

    <select id="getOneByProgressId" resultMap="BaseResultMap">
        select g.*
        from app_progress_lag_info g
        where g.deleted_ = 0
          and progress_id_ = #{progressId}
        order by create_time_ desc
        limit 1
    </select>
</mapper>
