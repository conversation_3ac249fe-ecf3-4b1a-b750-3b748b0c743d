<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.constructionalbum.mapper.ConstructionAlbumMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.constructionalbum.entity.ConstructionAlbum">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="parent_ids" jdbcType="VARCHAR" property="parentIds"/>
        <result column="source_url" jdbcType="VARCHAR" property="sourceUrl"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>
    <resultMap id="DtoResultMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.constructionalbum.dto.ConstructionAlbumDto">
        <association property="createUser" column="creator"
                     select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
        <association property="faceImg" column="{parentId=id,parentIds=parent_ids,dataType=data_type}"
                     select="com.easylinkin.linkappapi.constructionalbum.mapper.ConstructionAlbumMapper.getLastImgBy"/>
        <association property="imgNum" column="{parentId=id,parentIds=parent_ids,dataType=data_type}"
                     select="com.easylinkin.linkappapi.constructionalbum.mapper.ConstructionAlbumMapper.getImgNum"/>
    </resultMap>
    <resultMap id="ImgDtoResultMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.constructionalbum.dto.ConstructionAlbumDto">
        <association property="createUser" column="creator"
                     select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_construction_album
        where tenant_id = #{constructionAlbum.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_construction_album
        where id = #{id}
    </select>

    <select id="selectDtoPage" parameterType="com.easylinkin.linkappapi.constructionalbum.entity.vo.ConstructionAlbumVo"
            resultMap="DtoResultMap">
        select aca.*
        from app_construction_album aca
        where aca.delete_state = 1
        <if test="constructionAlbumVo.tenantId != null and constructionAlbumVo.tenantId != ''">
            and aca.tenant_id = #{constructionAlbumVo.tenantId}
        </if>
        <if test="constructionAlbumVo.dataType != null">
            and aca.data_type = #{constructionAlbumVo.dataType}
        </if>
        <if test="constructionAlbumVo.parentId != null">
            and aca.parent_id = #{constructionAlbumVo.parentId}
        </if>
        <if test="constructionAlbumVo.startTime != null">
            <![CDATA[
            and aca.create_time >= #{constructionAlbumVo.startTime}
            ]]>
        </if>
        <if test="constructionAlbumVo.endTime != null">
            <![CDATA[
            and aca.create_time <= #{constructionAlbumVo.endTime}
            ]]>
        </if>
        <if test="constructionAlbumVo.creator != null and constructionAlbumVo.creator != ''">
            and aca.creator in (${constructionAlbumVo.creator})
        </if>
        <if test="constructionAlbumVo.paramKey != null and constructionAlbumVo.paramKey != ''">
            and aca.name like concat('%', #{constructionAlbumVo.paramKey}, '%')
        </if>
        <if test="sorts != null and sorts.size() != 0">
            ORDER BY
            <trim suffixOverrides=",">
                <foreach collection="sorts" item="item" index="index">
                    aca.${item.field} ${item.sortRule},
                </foreach>
            </trim>
        </if>
        <if test="sorts == null or sorts.size() == 0">
            order by aca.create_time desc
        </if>
    </select>

    <select id="getLastImgBy" parameterType="com.easylinkin.linkappapi.constructionalbum.entity.ConstructionAlbum"
            resultMap="BaseResultMap">
        select aca.*
        from app_construction_album aca
        where aca.data_type = 3
          and aca.delete_state = 1
        <choose>
            <when test="dataType == 1">
                and aca.parent_ids like concat(#{parentIds}, #{parentId}, ',%')
            </when>
            <when test="dataType == 2">
                and aca.parent_id = #{parentId}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        order by aca.create_time desc,aca.id desc limit 1
    </select>

    <select id="getImgNum" parameterType="com.easylinkin.linkappapi.constructionalbum.entity.ConstructionAlbum"
            resultType="int">
        select count(*)
        from app_construction_album aca
        where aca.data_type = 3
          and aca.delete_state = 1
        <choose>
            <when test="dataType == 1">
                and aca.parent_ids like concat(#{parentIds}, #{parentId}, ',%')
            </when>
            <when test="dataType == 2">
                and aca.parent_id = #{parentId}
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="selectDtoById" parameterType="long" resultMap="DtoResultMap">
        select aca.*
        from app_construction_album aca
        where aca.id = #{id}
    </select>

    <select id="selectChildDtoList" parameterType="long" resultMap="ImgDtoResultMap">
        select aca.*
        from app_construction_album aca
        where aca.delete_state = 1
          and aca.parent_id = #{id}
        order by aca.create_time desc
    </select>
</mapper>
