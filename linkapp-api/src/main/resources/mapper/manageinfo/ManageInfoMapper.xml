<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.manageinfo.dao.ManageInfoMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.manageinfo.entity.ManageInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mange_name" jdbcType="VARCHAR" property="mangeName" />
    <result column="mange_code" jdbcType="VARCHAR" property="mangeCode" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="parent_ids" jdbcType="VARCHAR" property="parentIds" />
    <result column="direst_manage" jdbcType="INTEGER" property="direstManage" />
    <result column="sort_no" jdbcType="INTEGER" property="sortNo" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_manage_info where tenant_id = #{appManageInfo.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_manage_info where id = #{id}
    </select>


</mapper>
