<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.premeeting.mapper.RailPreMeetingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.premeeting.entity.RailPreMeeting">
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="grid_id_" property="gridId"/>
        <result column="meeting_time_" property="meetingTime"/>
        <result column="content_" property="content"/>
        <result column="team_name_" property="teamName"/>
        <result column="people_count_" property="peopleCount"/>
        <result column="supervisor_photo_url_" property="supervisorPhotoUrl"/>
        <result column="leader_photo_url_" property="leaderPhotoUrl"/>
        <result column="safety_officer_photo_url_" property="safetyOfficerPhotoUrl"/>
        <result column="technical_leader_photo_url_" property="technicalLeaderPhotoUrl"/>
        <result column="guard_photo_url_" property="guardPhotoUrl"/>
        <result column="media_files_url_" property="mediaFilesUrl"/>
        <result column="is_deleted_" property="isDeleted"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.easylinkin.linkappapi.premeeting.vo.RailPreMeetingVo" extends="BaseResultMap">
        <result column="grid_name" property="gridName"/>
    </resultMap>

    <!-- 日历查询映射结果 -->
    <resultMap id="CalendarResultMap" type="com.easylinkin.linkappapi.premeeting.vo.RailPreMeetingCalendarVo">
        <result column="date" property="date"/>
        <result column="date_status" property="dateStatus"/>
    </resultMap>

    <!-- 日期网格点映射结果 -->
    <resultMap id="DateGridResultMap" type="com.easylinkin.linkappapi.premeeting.vo.RailPreMeetingDateGridVo">
        <result column="date" property="date"/>
        <result column="grid_id" property="gridId"/>
        <result column="has_record" property="hasRecord"/>
    </resultMap>

    <!-- 网格点切换映射结果 -->
    <resultMap id="GridSwitchResultMap" type="com.easylinkin.linkappapi.premeeting.vo.RailPreMeetingGridSwitchVo">
        <id column="id" property="id"/>
        <result column="enginee_type" property="engineeType"/>
        <result column="number" property="number"/>
        <result column="grid_type" property="gridType"/>
        <result column="grid_name" property="gridName"/>
        <result column="mileage_range" property="mileageRange"/>
        <result column="safety_sup_id" property="safetySupId"/>
        <result column="safety_sup_name" property="safetySupName"/>
        <result column="grid_sec_id" property="gridSecId"/>
        <result column="grid_sec_name" property="gridSecName"/>
        <result column="grid_foreman_id" property="gridForemanId"/>
        <result column="grid_foreman_name" property="gridForemanName"/>
        <result column="grid_siteman_id" property="gridSitemanId"/>
        <result column="grid_siteman_name" property="gridSitemanName"/>
        <result column="staff_number" property="staffNumber"/>
        <result column="construction_team" property="constructionTeam"/>
        <result column="is_sole_duty" property="isSoleDuty"/>
        <result column="hold_cert" property="holdCert"/>
        <result column="is_formal_employee" property="isFormalEmployee"/>
        <result column="notes" property="notes"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="grid_meet_id" property="gridMeetId"/>
        <result column="grid_meet_name" property="gridMeetName"/>
        <result column="announcement_file_url" property="announcementFileUrl"/>
        <result column="has_data" property="hasData"/>
    </resultMap>

    <select id="queryListByPage" resultMap="BaseResultMapVo">
        SELECT t.*,
               g.grid_name
        FROM rail_pre_meeting t
                 LEFT JOIN rail_linkapp_grid_management_info g ON t.grid_id_ = g.id
        WHERE t.tenant_id_ = #{queryVo.tenantId}
        <if test="queryVo.gridId != null">
            AND t.grid_id_ = #{queryVo.gridId}
        </if>
        <if test="queryVo.gridName != null and queryVo.gridName != ''">
            AND g.grid_name like CONCAT('%', #{queryVo.gridName}, '%')
        </if>
        <if test="queryVo.content != null and queryVo.content != ''">
            AND t.content_ like CONCAT('%', #{queryVo.content}, '%')
        </if>
        <if test="queryVo.teamName != null and queryVo.teamName != ''">
            AND t.team_name_ like CONCAT('%', #{queryVo.teamName}, '%')
        </if>
        <if test="queryVo.meetingTimeStart != null">
            AND t.meeting_time_ &gt;= #{queryVo.meetingTimeStart}
        </if>
        <if test="queryVo.meetingTimeEnd != null">
            AND t.meeting_time_ &lt;= #{queryVo.meetingTimeEnd}
        </if>
        ORDER BY t.meeting_time_ DESC
    </select>

    <select id="findById" resultMap="BaseResultMapVo">
        SELECT t.*,
               g.grid_name
        FROM rail_pre_meeting t
                 LEFT JOIN rail_linkapp_grid_management_info g ON t.grid_id_ = g.id
        WHERE 1=1
        <if test="id != null and id != ''">
            AND t.id_ = #{id}
        </if>
    </select>

    <!-- 获取班前会月度数据：按天、按网格统计 -->
    <select id="getCalendarData" resultMap="DateGridResultMap">
        SELECT
            DATE_FORMAT(t.meeting_time_, '%Y-%m-%d') as date,
            t.grid_id_ as grid_id
        FROM rail_pre_meeting t
        WHERE 1=1
        <if test="queryVo.tenantId != null and queryVo.tenantId != ''">
            AND t.tenant_id_ = #{queryVo.tenantId}
        </if>
        <if test="queryVo.yearMonth != null and queryVo.yearMonth != ''">
            AND t.meeting_time_ LIKE CONCAT(#{queryVo.yearMonth}, '%')
        </if>
        GROUP BY DATE_FORMAT(t.meeting_time_, '%Y-%m-%d'), t.grid_id_
        ORDER BY date, grid_id
    </select>

    <!-- 获取网格点切换数据：显示所有网格点及其数据状态 -->
    <select id="getGridSwitchData" resultMap="GridSwitchResultMap">
        SELECT
            g.*,
            CASE
                WHEN COUNT(pm.id_) > 0 THEN 1
                ELSE 0
            END as has_data
        FROM rail_linkapp_grid_management_info g
        LEFT JOIN rail_pre_meeting pm ON g.id = pm.grid_id_
            <if test="tenantId != null and tenantId != ''">
                AND pm.tenant_id_ = #{tenantId}
            </if>
            <if test="startTime != null">
                AND pm.meeting_time_ &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND pm.meeting_time_ &lt;= #{endTime}
            </if>
        WHERE 1=1
        <if test="tenantId != null and tenantId != ''">
            AND g.tenant_id = #{tenantId}
        </if>
        GROUP BY g.id
        ORDER BY g.number ASC
    </select>

    <!-- 查询最新的一条班前会记录（按meetingTime排序） -->
    <select id="findLatestByMeetingTime" resultMap="BaseResultMapVo">
        SELECT t.*,
               g.grid_name
        FROM rail_pre_meeting t
                 LEFT JOIN rail_linkapp_grid_management_info g ON t.grid_id_ = g.id
        WHERE 1=1
        <if test="tenantId != null and tenantId != ''">
            AND t.tenant_id_ = #{tenantId}
        </if>
        ORDER BY t.meeting_time_ DESC
        LIMIT 1
    </select>


    <select id="selectNoPreMeetingDays" resultType="java.lang.String">
        SELECT DATE_ADD(#{assessMonth}, INTERVAL seq DAY) AS missingDate
        FROM (
        SELECT 0 AS seq UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
        SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
        SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION
        SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
        SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION
        SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION
        SELECT 30
        ) AS numbers
        WHERE DATE_ADD(#{assessMonth}, INTERVAL seq DAY) &lt;= LAST_DAY(#{assessMonth})
        AND NOT EXISTS (
        SELECT * FROM (
        SELECT DATE(meeting_time_) AS record_date FROM rail_pre_meeting WHERE grid_id_ = #{gridId} and tenant_id_ = #{tenantId}
        ) AS records
        WHERE records.record_date = DATE_ADD(#{assessMonth}, INTERVAL seq DAY)
        )
        ORDER BY missingDate
    </select>


    <select id="selectNoPreMeetingDaysByGroup" resultType="java.lang.String">

        SELECT DATE_ADD(#{assessMonth}, INTERVAL seq DAY) AS missingDate
        FROM (
        SELECT 0 AS seq UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
        SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
        SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION
        SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
        SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION
        SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION
        SELECT 30
        ) AS numbers
        WHERE DATE_ADD(#{assessMonth}, INTERVAL seq DAY) &lt;= LAST_DAY(#{assessMonth})
        AND NOT EXISTS (
        SELECT * FROM (
        SELECT DATE(meeting_time_) AS record_date FROM rail_pre_meeting WHERE team_name_ = #{assessGroup} and tenant_id_ = #{tenantId}

        ) AS records
        WHERE records.record_date = DATE_ADD(#{assessMonth}, INTERVAL seq DAY)
        )
        ORDER BY missingDate
    </select>



</mapper>
