<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.fillin.mapper.PeopleNumAtFillinMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.fillin.entity.PeopleNumAtFillin">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="project_manage_at_num" jdbcType="INTEGER" property="projectManageAtNum"/>
        <result column="project_manage_out_num" jdbcType="INTEGER" property="projectManageOutNum"/>
        <result column="other_manage_at_num" jdbcType="INTEGER" property="otherManageAtNum"/>
        <result column="other_manage_out_num" jdbcType="INTEGER" property="otherManageOutNum"/>
        <result column="construction_at_num" jdbcType="INTEGER" property="constructionAtNum"/>
        <result column="construction_out_num" jdbcType="INTEGER" property="constructionOutNum"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>

    <resultMap id="DtoResultMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.fillin.dto.PeopleNumAtFillinDto">
        <association property="createUser" column="creator"
                     select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
        <association property="tenantInfo" column="tenant_id"
                     select="com.easylinkin.linkappapi.tenant.mapper.LinkappTenantMapper.getById"/>
    </resultMap>
    <resultMap id="BusinessDtoResultMap"
               extends="com.easylinkin.linkappapi.tenant.mapper.LinkappTenantMapper.BaseResultMap"
               type="com.easylinkin.linkappapi.fillin.dto.TenantPeopleNumAtFillinDto">
        <association property="createUser" column="fillInCreater"
                     select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
        <association property="peopleNumAtFillin" column="tid"
                     select="com.easylinkin.linkappapi.fillin.mapper.PeopleNumAtFillinMapper.selectById"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_people_num_at_fillin
        where tenant_id = #{appPeopleNumAtFillin.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_people_num_at_fillin
        where id = #{id}
    </select>

    <select id="selectDtoPage" parameterType="com.easylinkin.linkappapi.fillin.entity.vo.PeopleNumAtFillinVo"
            resultMap="DtoResultMap">
        select f1.*
        from app_people_num_at_fillin f1,
        (
        select max(f.id) as max_id
        from app_people_num_at_fillin f
                 left join linkapp_tenant lt on
            f.tenant_id = lt.id
        where f.delete_state = 1
        <if test="peopleNumAtFillin.tenantId != null and peopleNumAtFillin.tenantId != ''">
            and f.tenant_id = #{peopleNumAtFillin.tenantId}
        </if>
        <if test="peopleNumAtFillin.paramKey != null and peopleNumAtFillin.paramKey != ''">
            and lt.platform_project_name like concat('%', #{peopleNumAtFillin.paramKey}, '%')
        </if>
        <if test="peopleNumAtFillin.startTime != null">
            <![CDATA[
            and f.create_time >= #{peopleNumAtFillin.startTime}
            ]]>
        </if>
        <if test="peopleNumAtFillin.endTime != null">
            <![CDATA[
            and f.create_time <= #{peopleNumAtFillin.endTime}
            ]]>
        </if>
        group by date_format(f.create_time, '%Y-%m-%d'),
                 f.tenant_id ) f2
        where f1.id = f2.max_id
        order by f1.create_time desc
    </select>

    <select id="selectBusinessDtoPage" parameterType="com.easylinkin.linkappapi.fillin.entity.vo.PeopleNumAtFillinVo"
            resultMap="BusinessDtoResultMap">
        select ltf.id      as tid,
               ltf.creator as fillInCreater,
               lt.*
        from linkapp_tenant lt
            left join (select f1.*
        from app_people_num_at_fillin f1,
        (select max(f.id) as max_id
         from app_people_num_at_fillin f
        where f.delete_state = 1
        <if test="peopleNumAtFillin.startTime != null">
            <![CDATA[
            and f.create_time >= #{peopleNumAtFillin.startTime}
            ]]>
        </if>
        <if test="peopleNumAtFillin.endTime != null">
            <![CDATA[
            and f.create_time <= #{peopleNumAtFillin.endTime}
            ]]>
        </if>
        group by date_format(f.create_time, '%Y-%m-%d'),
                 f.tenant_id ) f2
        where f1.id = f2.max_id) ltf on
            lt.id = ltf.tenant_id
        where  <![CDATA[ lt.platform_project_name <> '武汉建工集团'
        ]]>
        <if test="peopleNumAtFillin.status != null">
            <choose>
                <when test="peopleNumAtFillin.status == 1">
                    and ltf.creator is not null
                </when>
                <when test="peopleNumAtFillin.status == 2">
                    and ltf.creator is null
                </when>
            </choose>
        </if>
        <if test="peopleNumAtFillin.paramKey != null and peopleNumAtFillin.paramKey != ''">
            and lt.platform_project_name like concat('%', #{peopleNumAtFillin.paramKey}, '%')
        </if>
        <!--order by ltf.create_time desc, CONVERT(lt.platform_project_name USING gbk)-->
        order by ltf.create_time desc, lt.create_time desc
    </select>

    <select id="businessDayFillinData" parameterType="com.easylinkin.linkappapi.fillin.entity.vo.PeopleNumAtFillinVo"
            resultMap="BaseResultMap">
        select sum(f1.project_manage_at_num)  as project_manage_at_num,
               sum(f1.project_manage_out_num) as project_manage_out_num,
               sum(f1.other_manage_at_num)    as other_manage_at_num,
               sum(f1.construction_at_num)    as construction_at_num,
               sum(f1.construction_out_num)   as construction_out_num,
               sum(f1.other_manage_out_num)   as other_manage_out_num
        from app_people_num_at_fillin f1,
        (
        select max(f.id) as max_id
        from app_people_num_at_fillin f
        where f.delete_state = 1
        <if test="startTime != null">
            <![CDATA[
            and f.create_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and f.create_time <= #{endTime}
            ]]>
        </if>
        group by date_format(f.create_time, '%Y-%m-%d'),
                 f.tenant_id ) f2
        where f1.id = f2.max_id
    </select>
</mapper>
