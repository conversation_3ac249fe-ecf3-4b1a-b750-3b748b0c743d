<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.keywork.mapper.KeyworkListMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.keywork.vo.KeyworkListVo">
    <id column="id" property="id"/>
    <result column="content_name" property="contentName"/>
    <result column="level" property="level"/>
    <result column="parent_id" property="parentId"/>
    <result column="is_used" property="isUsed"/>
    <result column="update_time" property="updateTime"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="remark" property="remark"/>
  </resultMap>

  <resultMap id="listmap" type="java.util.Map">
    <result property="parentContentName" column="parentContentName" />
    <result property="contentName" column="contentName" />
  </resultMap>

  <sql id="keyworkListMap">
    kwl.*
  </sql>

  <select id="selectKeyworkList" resultMap="BaseResultMap">
    select <include refid="keyworkListMap" />
    from rail_keywork_list kwl
    where 1 = 1
   <if test="keyworkList != null">
     <if test="keyworkList.level != null">
       and kwl.level = #{keyworkList.level}
     </if>
     <if test="keyworkList.parentId != null">
       and kwl.parent_id = #{keyworkList.parentId}
     </if>
     <if test="keyworkList.isUsed != null">
       and kwl.is_used = #{keyworkList.isUsed}
     </if>
     <if test="keyworkList.tenantId != null">
       and kwl.tenant_id = #{keyworkList.tenantId}
     </if>
   </if>
  </select>

  <select id="selectIsUsedListMap" resultType="java.util.Map">
    SELECT
      p.content_name AS parentContentName,
      GROUP_CONCAT(c.content_name SEPARATOR ', ') AS contentName
    FROM
      rail_keywork_list p
        LEFT JOIN
      rail_keywork_list c ON p.id = c.parent_id
    WHERE
      p.parent_id =0  and c.is_used = 1 and p.tenant_id = #{tenantId}
    GROUP BY
      p.id, p.content_name;
  </select>
</mapper>
