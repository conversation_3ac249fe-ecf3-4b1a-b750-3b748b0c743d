<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.keywork.mapper.SafeTableRecordMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.keywork.vo.SafeTableRecordVo">
    <id column="id" property="id"/>
    <result column="table_id" property="tableId"/>
    <result column="item_id" property="itemId"/>
    <result column="check_result" property="checkResult"/>
    <result column="check_user_id" property="checkUserId"/>
    <result column="check_time" property="checkTime"/>
  </resultMap>
  <sql id="safeTableRecordMap">
    rstr.*
  </sql>

  <select id="selectSafeTableRecordList" resultMap="BaseResultMap">
    select <include refid="safeTableRecordMap" />
    from rail_safe_table_record rstr
    where 1 = 1
   <if test="safeTableRecord != null">
     <if test="safeTableRecord.tableId != null">
       and rstr.table_id = #{safeTableRecord.tableId}
     </if>
     <if test="safeTableRecord.itemId != null">
       and rstr.item_id = #{safeTableRecord.itemId}
     </if>
   </if>
  </select>
</mapper>
