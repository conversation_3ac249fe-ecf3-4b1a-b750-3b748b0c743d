<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.keywork.mapper.HiddenTroubleDealMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.keywork.vo.HiddenTroubleDealVo">
    <id column="id" property="id"/>
    <result column="deal_round" property="dealRound"/>
    <result column="trouble_id" property="troubleId"/>
    <result column="deal_type" property="dealType"/>
    <result column="deal_files" property="dealFiles"/>
    <result column="deal_reply" property="dealReply"/>
    <result column="deal_uid" property="dealUid"/>
    <result column="dealUserName" property="dealUserName"/>
    <result column="deal_time" property="dealTime"/>
    <result column="deal_result" property="dealResult"/>
  </resultMap>

  <sql id="hiddenTroubleDealMap">
    rktd.*,
    lu1.real_name as dealUserName
  </sql>

  <select id="selectList" resultMap="BaseResultMap">
    select <include refid="hiddenTroubleDealMap" />
    from rail_hidden_trouble_deal rktd
    LEFT JOIN rail_linkapp_roster_personnel lu1 on lu1.id = rktd.deal_uid
    where 1 = 1
   <if test="hiddenTroubleDealDto != null">
     <if test="hiddenTroubleDealDto.id != null">
       and rktd.id = #{hiddenTroubleDealDto.id}
     </if>
     <if test="hiddenTroubleDealDto.troubleId != null">
       and rktd.trouble_id = #{hiddenTroubleDealDto.troubleId}
     </if>
     <if test="hiddenTroubleDealDto.dealType != null">
       and rktd.deal_type = #{hiddenTroubleDealDto.dealType}
     </if>

     <if test="hiddenTroubleDealDto.dealResult != null">
       and rktd.deal_result = #{hiddenTroubleDealDto.dealResult}
     </if>

     <if test="hiddenTroubleDealDto.startTime!=null">
       and rktd.deal_time &gt;= #{hiddenTroubleDealDto.startTime}
     </if>
     <if test="hiddenTroubleDealDto.endTime!=null">
       and rktd.deal_time &lt;= #{hiddenTroubleDealDto.endTime}
     </if>
   </if>
  </select>

</mapper>
