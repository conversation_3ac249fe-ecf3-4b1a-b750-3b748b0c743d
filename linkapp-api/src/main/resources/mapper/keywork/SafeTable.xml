<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.keywork.mapper.SafeTableMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.keywork.vo.SafeTableVo">
    <id column="id" property="id"/>
    <result column="grid_id" property="gridId"/>
    <result column="keywork_parent_id" property="keyworkParentId"/>
    <result column="keywork_id" property="keyworkId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="table_status" property="tableStatus"/>
    <result column="table_name" property="tableName"/>
    <result column="table_title" property="tableTitle"/>
    <result column="remark" property="remark"/>
    <result column="check_opinion" property="checkOpinion"/>
    <result column="check_time" property="checkTime"/>
    <result column="pre_sign_time" property="preSignTime"/>
    <result column="check_user_id" property="checkUserId"/>
    <result column="check_user_sign" property="checkUserSign"/>
    <result column="create_user_id" property="createUserId"/>
    <result column="has_sign_uid" property="hasSignUid"/>
    <result column="create_time" property="createTime"/>
    <result column="latest_modify_time" property="latestModifyTime"/>
    <result column="gridName" property="gridName"/>
    <result column="tenantName" property="tenantName"/>
    <result column="work_time" property="workTime"/>
    <result column="createUserName" property="createUserName"/>
    <result column="checkUserName" property="checkUserName"/>
    <result column="contentName" property="contentName"/>
    <result column="checkUser" property="checkUser"/>
    <collection property="safeTableRecordVoList" ofType="com.easylinkin.linkappapi.keywork.vo.SafeTableRecordVo">
      <id column="subId" property="id"/>
      <result column="subId" property="subId"/>
      <result column="subTableId" property="subTableId"/>
      <result column="subItemId" property="subItemId"/>
      <result column="subCheckResult" property="subCheckResult"/>
      <result column="subCheckUserId" property="subCheckUserId"/>
      <result column="subCheckTime" property="subCheckTime"/>
      <result column="checkContent" property="checkContent"/>
      <result column="checkRequire" property="checkRequire"/>
      <result column="checkRequireIntro" property="checkRequireIntro"/>
      <result column="subCheckUserName" property="subCheckUserName"/>
    </collection>
  </resultMap>


  <sql id="safeTableMap">
    lt.platform_project_name as tenantName,
    rlgmi.grid_name as gridName,
	rkl.content_name as  contentName,
	rst.*,
    rstr.check_user_id as subCheckUserId,
	rstr.id as subId,
    rstr.check_time as subCheckTime,
    rstr.item_id as subItemId,
    rstr.table_id as subTableId,
    rstr.check_result as subCheckResult,
	rsti.check_content as checkContent,
	rsti.check_require as checkRequire,
	rsti.check_require_intro as checkRequireIntro,
	lu1.real_name as createUserName,
	lu2.real_name as checkUserName,
	lu3.real_name as subCheckUserName
  </sql>

  <select id="selectSafeTableList" resultMap="BaseResultMap">
    select <include refid="safeTableMap" />
    from rail_safe_table rst
    LEFT JOIN rail_safe_table_record rstr ON rst.id = rstr.table_id
    LEFT JOIN rail_safe_table_item rsti ON rsti.id = rstr.item_id
    LEFT JOIN rail_keywork_list rkl ON rkl.id = rst.keywork_id
    LEFT JOIN rail_linkapp_roster_personnel lu1 on lu1.id = rst.create_user_id
    LEFT JOIN rail_linkapp_roster_personnel lu2 on lu2.id = rst.check_user_id
    LEFT JOIN rail_linkapp_roster_personnel lu3 on lu3.id = rstr.check_user_id
    LEFT JOIN rail_linkapp_grid_management_info rlgmi on rlgmi.id = rst.grid_id
    left join linkapp_tenant lt on rst.tenant_id = lt.id
    where 1 = 1
   <if test="safeTableDto != null">
     <if test="safeTableDto.id != null">
       and rst.id = #{safeTableDto.id}
     </if>
     <if test="safeTableDto.keyworkId != null">
       and rst.keywork_id = #{safeTableDto.keyworkId}
     </if>
     <if test="safeTableDto.tableStatus != null">
       and rst.table_status = #{safeTableDto.tableStatus}
     </if>
     <if test="safeTableDto.gridId != null">
       and rst.grid_id = #{safeTableDto.gridId}
     </if>
     <if test="safeTableDto.tenantId != null">
       and rst.tenant_id = #{safeTableDto.tenantId}
     </if>
     <if test="safeTableDto.keyworkParentId != null">
       and rst.keywork_parent_id = #{safeTableDto.keyworkParentId}
     </if>
     <if test="safeTableDto.checkOpinion != null">
       and rst.check_opinion = #{safeTableDto.checkOpinion}
     </if>
     <if test="safeTableDto.checkUserId != null and safeTableDto.checkUserId != ''">
       and rst.check_user_id like contact('%',#{safeTableDto.checkUserId},'%')
     </if>
     <if test="safeTableDto.hasSignUid != null and safeTableDto.hasSignUid != ''">
       and rst.has_sign_uid like contact('%',#{safeTableDto.hasSignUid},'%')
     </if>
     <if test="safeTableDto.createUserId != null">
       and rst.create_user_id = #{safeTableDto.createUserId}
     </if>
     <if test="safeTableDto.createTime!=null">
       and date_format(rst.work_time, '%Y-%m-%d') <![CDATA[=]]> date_format(#{safeTableDto.createTime}, '%Y-%m-%d')
     </if>
   </if>
    ORDER BY rst.table_name
  </select>

  <select id="selectTableListByPage" resultMap="BaseResultMap">
    select
    GROUP_CONCAT(DISTINCT(lu3.real_name) SEPARATOR ', ') AS checkUser,
    rlgmi.grid_name as gridName,
    rkl.content_name as  contentName,
    rst.*,
    rstr.id as subId,
    rstr.check_user_id as subCheckUserId,
    rstr.check_time as subCheckTime,
    rstr.item_id as subItemId,
    rstr.table_id as subTableId,
    rstr.check_result as subCheckResult,
    rsti.check_content as checkContent,
    rsti.check_require as checkRequire,
    rsti.check_require_intro as checkRequireIntro,
    lu1.real_name as createUserName,
    lu2.real_name as checkUserName,
    lu3.real_name as subCheckUserName
    from rail_safe_table rst
    LEFT JOIN rail_safe_table_record rstr ON rst.id = rstr.table_id
    LEFT JOIN rail_safe_table_item rsti ON rsti.id = rstr.item_id
    LEFT JOIN rail_keywork_list rkl ON rkl.id = rst.keywork_id
    LEFT JOIN rail_linkapp_roster_personnel lu1 on lu1.id = rst.create_user_id
    LEFT JOIN rail_linkapp_roster_personnel lu2 on lu2.id = rst.check_user_id
    LEFT JOIN rail_linkapp_roster_personnel lu3 on lu3.id = rstr.check_user_id
    LEFT JOIN rail_linkapp_grid_management_info rlgmi on rlgmi.id = rst.grid_id
    where 1 = 1
    <if test="safeTableDto != null">
      <if test="safeTableDto.id != null">
        and rst.id = #{safeTableDto.id}
      </if>
      <if test="safeTableDto.keyworkId != null">
        and rst.keywork_id = #{safeTableDto.keyworkId}
      </if>
      <if test="safeTableDto.tenantId != null">
        and rst.tenant_id = #{safeTableDto.tenantId}
      </if>
      <if test="safeTableDto.tableStatus != null">
        and rst.table_status = #{safeTableDto.tableStatus}
      </if>
      <if test="safeTableDto.gridId != null">
        and rst.grid_id = #{safeTableDto.gridId}
      </if>
      <if test="safeTableDto.keyworkParentId != null">
        and rst.keywork_parent_id = #{safeTableDto.keyworkParentId}
      </if>
      <if test="safeTableDto.checkOpinion != null">
        and rst.check_opinion = #{safeTableDto.checkOpinion}
      </if>
      <if test="safeTableDto.checkUserId != null and safeTableDto.checkUserId != ''">
        and rst.check_user_id = #{safeTableDto.checkUserId}
      </if>
      <if test="safeTableDto.createUserId != null">
        and rst.create_user_id = #{safeTableDto.createUserId}
      </if>
      <if test="safeTableDto.createTime!=null">
        and date_format(rst.work_time, '%Y-%m-%d') <![CDATA[=]]> date_format(#{safeTableDto.createTime}, '%Y-%m-%d')
      </if>
      <if test="safeTableDto.startTime!=null">
        and rst.work_time &gt;= #{safeTableDto.startTime}
      </if>
      <if test="safeTableDto.endTime!=null">
        and rst.work_time  &lt;= #{safeTableDto.endTime}
      </if>

    </if>

    group By rst.id
    ORDER BY rst.work_time DESC
  </select>


  <select id="selcetTableSignUser" resultType="java.util.Map">
    SELECT
      rstr.id AS subId,
      rstr.table_id AS tableId,
      lu1.real_name AS checkUserName,
      lu2.real_name AS tableUserName,
      DATE_FORMAT(rstr.check_time, '%Y-%m-%d %H:%i:%s') AS subCheckTime,
      rst.check_time AS tableCheckTime,
      rst.check_opinion AS checkOpinion,
      rst.check_user_id AS checkUserId,
      rst.has_sign_uid AS hasSignUid,
      rst.check_time AS checkTime,
      rst.table_status AS tableStatus

    FROM
      rail_safe_table_record rstr
        LEFT JOIN rail_safe_table rst ON rst.id = rstr.table_id
        LEFT JOIN rail_linkapp_roster_personnel lu1 ON lu1.id = rstr.check_user_id
        LEFT JOIN rail_linkapp_roster_personnel lu2 ON lu2.id = rst.check_user_id
    WHERE
      rstr.table_id = #{tableId} and rstr.check_result != 0
    GROUP BY
      rstr.check_user_id
  </select>
</mapper>
