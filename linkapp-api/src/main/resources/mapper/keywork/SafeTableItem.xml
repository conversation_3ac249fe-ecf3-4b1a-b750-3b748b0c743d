<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.keywork.mapper.SafeTableItemMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.keywork.vo.SafeTableItemVo">
    <id column="id" property="id"/>
    <result column="keywork_id" property="keyworkId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="check_content_classify" property="checkContentClassify"/>
    <result column="check_content" property="checkContent"/>
    <result column="check_require" property="checkRequire"/>
    <result column="check_require_intro" property="checkRequireIntro"/>
    <result column="contentName" property="contentName"/>
  </resultMap>
  <sql id="safeTableItemMap">
    rsti.id,
    rsti.keywork_id,
    rsti.check_content_classify,
    rsti.check_content,
    rsti.check_require,
    rsti.check_require_intro,
    rsti.tenant_id,
    kwl.content_name as contentName
  </sql>

  <select id="selectSafeTableItemList" resultMap="BaseResultMap">
    select <include refid="safeTableItemMap" />
    from rail_safe_table_item rsti
    left join rail_keywork_list kwl on rsti.keywork_id = kwl.id
    where 1 = 1
   <if test="safeTableItem != null">
     <if test="safeTableItem.keyworkId != null">
       and rsti.keywork_id = #{safeTableItem.keyworkId}
     </if>
     <if test="safeTableItem.tenantId != null">
       and rsti.tenant_id = #{safeTableItem.tenantId}
     </if>
   </if>
  </select>
</mapper>
