<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.keywork.mapper.ProcessMonitorMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.keywork.vo.ProcessMonitorVo">
    <id column="id" property="id"/>
    <result column="grid_id" property="gridId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="monitor_title" property="monitorTitle"/>
    <result column="monitor_date" property="monitorDate"/>
    <result column="monitor_content" property="monitorContent"/>
    <result column="monitor_file1" property="monitorFile1"/>
    <result column="monitor_file2" property="monitorFile2"/>
    <result column="monitor_file3" property="monitorFile3"/>
    <result column="monitor_file4" property="monitorFile4"/>
    <result column="monitor_file5" property="monitorFile5"/>
    <result column="create_user_id" property="createUserId"/>
    <result column="create_time" property="createTime"/>
    <result column="tenantName" property="tenantName"/>
    <result column="gridName" property="gridName"/>
    <result column="createUserName" property="createUserName"/>

  </resultMap>



  <sql id="processMonitorMap">
    rpm.*,
    lt.platform_project_name as tenantName,
    lu.real_name as createUserName,
    rlgmi.grid_name as gridName
  </sql>

  <select id="selectProcessMontorList" resultMap="BaseResultMap">
    select
    <include refid="processMonitorMap"/>
    from rail_process_monitor rpm
    left join rail_linkapp_roster_personnel lu on rpm.create_user_id = lu.id
    left join linkapp_tenant lt on rpm.tenant_id = lt.id
    LEFT JOIN rail_linkapp_grid_management_info rlgmi on rlgmi.id = rpm.grid_id
    where 1 = 1
    <if test="processMonitorDto != null">
      <if test="processMonitorDto.id != null">
        and rpm.id = #{processMonitorDto.id}
      </if>
      <if test="processMonitorDto.gridId != null">
        and rpm.grid_id = #{processMonitorDto.gridId}
      </if>
      <if test="processMonitorDto.tenantId != null">
        and rpm.tenant_id = #{processMonitorDto.tenantId}
      </if>
      <if test="processMonitorDto.monitorDate!=null ">
        and date_format(rpm.monitor_date, '%Y-%m-%d') <![CDATA[=]]> date_format(#{processMonitorDto.monitorDate},
        '%Y-%m-%d')
      </if>
      <if test="processMonitorDto.year!=null ">
        and year(rpm.monitor_date) = #{processMonitorDto.year}
      </if>
      <if test="processMonitorDto.month!=null ">
        and month(rpm.monitor_date) = #{processMonitorDto.month}
      </if>
    <if test="processMonitorDto.startDate!=null">
        and rpm.monitor_date &gt;= #{processMonitorDto.startDate}
    </if>
    <if test="processMonitorDto.endDate!=null">
        and rpm.monitor_date  &lt;= #{processMonitorDto.endDate}
    </if>
    </if>
    ORDER BY monitor_date desc,create_time desc
  </select>

  <select id="selectDayRecordCount" resultType="java.util.Map">
    SELECT
    calendar.monitorDate,
    COUNT(DISTINCT(records.grid_id)) AS recordCount,
     GROUP_CONCAT(DISTINCT(records.grid_id) SEPARATOR ',') AS gridId
    FROM
    (
    SELECT
    DATE_ADD(
    DATE_FORMAT(#{monitorDate}, '%Y-%m-01'),
    INTERVAL (a.a + (10 * b.a)) DAY
    ) AS monitorDate
    FROM
    (SELECT 0 AS a UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS a
    CROSS JOIN
    (SELECT 0 AS a UNION SELECT 1 UNION SELECT 2 UNION SELECT 3) AS b
    WHERE
    (a.a + (10 * b.a)) &lt; DAY(LAST_DAY(DATE_FORMAT(#{monitorDate}, '%Y-%m-01')))
    ) AS calendar
    LEFT JOIN
    rail_process_monitor records ON  DATE_FORMAT( records.monitor_date, '%Y-%m-%d' ) = DATE_FORMAT(calendar.monitorDate , '%Y-%m-%d' )
      <if test="tenantId != null and tenantId != ''">
            and records.tenant_id = #{tenantId}
        </if>
        <if test="gridIds != null and gridIds != ''">
            and records.grid_id in (${gridIds})
        </if>

    GROUP BY
    calendar.monitorDate
    ORDER BY
    calendar.monitorDate;
  </select>

  <select id="selectStatisticForScreen" resultType="java.util.Map">
    SELECT
      'preMeeting' AS tableName,
      '班前会' AS typeName,
      COUNT(*) AS recordCount
    FROM rail_pre_meeting
    WHERE (DATE(meeting_time_) between DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') and DATE_FORMAT(CURRENT_DATE, '%Y-%m-31'))  and tenant_id_ = #{tenantId}
    UNION ALL
    SELECT
      'processMonitor' AS tableName,
      '过程盯控' AS typeName,
      COUNT(*) AS recordCount
    FROM rail_process_monitor
    WHERE (DATE(monitor_date) between DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') and DATE_FORMAT(CURRENT_DATE, '%Y-%m-31'))  and tenant_id = #{tenantId}
    UNION ALL
    SELECT
      'eveningMeeting' AS tableName,
      '晚交班会' AS typeName,
      COUNT(*) AS recordCount
    FROM rail_evening_meeting
    WHERE (DATE(meeting_time_) between DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') and DATE_FORMAT(CURRENT_DATE, '%Y-%m-31')) and tenant_id_ = #{tenantId}
    UNION ALL
    SELECT
      'gridSecurityLog' AS tableName,
      '网格安全员日志' AS typeName,
      COUNT(*) AS recordCount
    FROM rail_grid_security_log
    WHERE (DATE(log_time_) between DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') and DATE_FORMAT(CURRENT_DATE, '%Y-%m-31')) and tenant_id_ = #{tenantId}
    UNION ALL
    SELECT
      'followRecord' AS tableName,
      '跟班记录' AS typeName,
      COUNT(*) AS recordCount
    FROM rail_follow_record
    WHERE  (DATE(plan_follow_date_) between DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') and DATE_FORMAT(CURRENT_DATE, '%Y-%m-31'))  and tenant_id_ = #{tenantId} AND status_ != 0;
  </select>



</mapper>
