<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.keywork.mapper.HiddenTroubleMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.keywork.vo.HiddenTroubleVo">
    <id property="id" column="id"/>
    <result property="tenantName" column="tenantName"/>
    <result property="tenantId" column="tenant_id"/>
    <result property="troubleNo" column="trouble_no"/>
    <result property="troubleType" column="trouble_type"/>
    <result property="troubleLevel" column="trouble_level"/>
    <result property="troubleStatus" column="trouble_status"/>
    <result property="isOverdue" column="is_overdue"/>
    <result property="dutyGroup" column="duty_group"/>
    <result property="troubleContent" column="trouble_content"/>
    <result property="troubleRemark" column="trouble_remark"/>
    <result property="rectifyRequire" column="rectify_require"/>
    <result property="troubleFiles" column="trouble_files"/>
    <result property="rectifyFiles" column="rectifyFiles"/>
    <result property="troublePart" column="trouble_part"/>
    <result property="rectifyDuration" column="rectify_duration"/>
    <result property="rectifyDeadline" column="rectify_deadline"/>
    <result property="createUserName" column="createUserName"/>
    <result property="createUid" column="create_uid"/>
    <result property="checkUserName" column="checkUserName"/>
    <result property="checkUid" column="check_uid"/>
    <result property="rectifyUserName" column="rectifyUserName"/>
    <result property="rectifyUid" column="rectify_uid"/>
    <result property="reviewUid" column="review_uid"/>
    <result property="reviewUserName" column="reviewUserName"/>
    <result property="createTime" column="create_time"/>
    <result property="checkTime" column="check_time"/>
    <result property="rectifyTime" column="rectify_time"/>
    <result property="reviewTime" column="review_time"/>
    <result property="cancelTime" column="cancel_time"/>
    <collection property="dealVoList" ofType="com.easylinkin.linkappapi.keywork.vo.HiddenTroubleDealVo">
      <result column="dealId" property="dealId"/>
      <result column="deal_round" property="dealRound"/>
      <result column="trouble_id" property="troubleId"/>
      <result column="deal_type" property="dealType"/>
      <result column="deal_files" property="dealFiles"/>
      <result column="deal_reply" property="dealReply"/>
      <result column="deal_uid" property="dealUid"/>
      <result column="deal_time" property="dealTime"/>
      <result column="deal_result" property="dealResult"/>
    </collection>
  </resultMap>

  <sql id="hiddenTroubleMap">
    rht.*,

    lu1.real_name as createUserName,
    lu2.real_name as checkUserName,
    lu3.real_name as rectifyUserName,
    lu4.real_name as reviewUserName,
    lt.platform_project_name as tenantName
  </sql>

  <select id="selectList" resultMap="BaseResultMap">
    select <include refid="hiddenTroubleMap" />
    from rail_hidden_trouble rht
    left join rail_linkapp_roster_personnel lu1 on rht.create_uid = lu1.id
    left join rail_linkapp_roster_personnel lu2 on rht.check_uid = lu2.id
    left join rail_linkapp_roster_personnel lu3 on rht.rectify_uid = lu3.id
    left join rail_linkapp_roster_personnel lu4 on rht.review_uid = lu4.id
    left join linkapp_tenant lt on rht.tenant_id = lt.id
    where 1 = 1
   <if test="hiddenTroubleDto != null">
     <if test="hiddenTroubleDto.troubleType != null and hiddenTroubleDto.troubleType !=''">
       and rht.trouble_type = #{hiddenTroubleDto.troubleType}
     </if>
     <if test="hiddenTroubleDto.troubleLevel != null">
       and rht.trouble_level = #{hiddenTroubleDto.troubleLevel}
     </if>
     <if test="hiddenTroubleDto.troubleStatus != null">
       and rht.trouble_status = #{hiddenTroubleDto.troubleStatus}
     </if>
     <if test="hiddenTroubleDto.notTroubleStatus !=null">
       and rht.trouble_status in (${hiddenTroubleDto.notTroubleStatus})
     </if>
     <if test="hiddenTroubleDto.isOverdue != null">
       and rht.is_overdue = #{hiddenTroubleDto.isOverdue}
     </if>
     <if test="hiddenTroubleDto.tenantId != null and hiddenTroubleDto.tenantId != ''">
       and rht.tenant_id = #{hiddenTroubleDto.tenantId}
     </if>
     <if test="hiddenTroubleDto.createUid != null">
       and rht.create_uid = #{hiddenTroubleDto.createUid}
     </if>
     <if test="hiddenTroubleDto.checkUid != null">
       and rht.check_uid = #{hiddenTroubleDto.checkUid}
     </if>
     <if test="hiddenTroubleDto.reviewUid != null">
       and rht.review_uid = #{hiddenTroubleDto.reviewUid}
     </if>
     <if test="hiddenTroubleDto.id != null">
       and rht.id = #{hiddenTroubleDto.id}
     </if>

     <if test="hiddenTroubleDto.startTime!=null">
       and rht.check_time &gt;= #{hiddenTroubleDto.startTime}
     </if>
     <if test="hiddenTroubleDto.endTime!=null">
       and rht.check_time  &lt;= #{hiddenTroubleDto.endTime}
     </if>

     <if test="hiddenTroubleDto.checkTime != null">
        and  date_format(rht.check_time, '%Y-%m-%d') <![CDATA[=]]> date_format(#{hiddenTroubleDto.checkTime}, '%Y-%m-%d')
     </if>
   </if>
    order by rht.check_time desc
  </select>
  
  <select id="selectOne" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
      rht.*,
      lu1.real_name as createUserName,
      lu2.real_name as checkUserName,
      lu3.real_name as rectifyUserName,
      lu4.real_name as reviewUserName,
      lt.platform_project_name as tenantName
    from rail_hidden_trouble rht
           left join rail_linkapp_roster_personnel lu1 on rht.create_uid = lu1.id
           left join rail_linkapp_roster_personnel lu2 on rht.check_uid = lu2.id
           left join rail_linkapp_roster_personnel lu3 on rht.rectify_uid = lu3.id
           left join rail_linkapp_roster_personnel lu4 on rht.review_uid = lu4.id
           left join linkapp_tenant lt on rht.tenant_id = lt.id
           left join rail_hidden_trouble_deal rhtd on rht.id = rhtd.trouble_id
    where rht.id = #{troubleId}
  </select>

  <select id="selectTroubleByLevelAndStatus" resultType="java.util.Map">
    SELECT

      SUM(CASE WHEN trouble_level = 0 THEN 1 ELSE 0 END) as sum0,
      SUM(CASE WHEN trouble_level = 1 THEN 1 ELSE 0 END) as sum1,
      SUM(CASE WHEN trouble_level = 2 THEN 1 ELSE 0 END) as sum2,

      SUM(CASE WHEN trouble_level = 0 and trouble_status =3 THEN 1 ELSE 0 END) as cancelSum0,
      SUM(CASE WHEN trouble_level = 1 and trouble_status =3 THEN 1 ELSE 0 END) as cancelSum1,
      SUM(CASE WHEN trouble_level = 2 and trouble_status =3 THEN 1 ELSE 0 END) as cancelSum2,

      SUM(CASE WHEN trouble_status = 0 THEN 1 ELSE 0 END) as statusSum0,
      SUM(CASE WHEN trouble_status = 1 THEN 1 ELSE 0 END) as statusSum1,
      SUM(CASE WHEN trouble_status = 2 THEN 1 ELSE 0 END) as statusSum2,
      SUM(CASE WHEN trouble_status = 3 THEN 1 ELSE 0 END) as statusSum3

    FROM
      rail_hidden_trouble
    where tenant_id = #{tenantId}
  </select>

  <select id="selectDateMinAndMax" resultType="java.util.Map">
    SELECT
      DATE_FORMAT(MIN(check_time), '%Y-%m-01') AS minMonth,
      DATE_FORMAT(MAX(check_time), '%Y-%m-01') AS maxMonth,
      PERIOD_DIFF(DATE_FORMAT(MAX(check_time), '%Y%m'), DATE_FORMAT(MIN(check_time), '%Y%m')) AS diffMonths
    FROM rail_hidden_trouble
    WHERE tenant_id = #{tenantId} and check_time IS NOT NULL;
  </select>

  <select id="selectTroubleSumByMonth" resultType="java.util.Map">
    SELECT
      DATE_FORMAT(DATE_ADD(#{startDate}, INTERVAL n.n MONTH), '%Y-%m') AS yearMonth,
      IFNULL(t.cnt, 0) AS sum
    FROM
      (SELECT @rownum := @rownum + 1 AS n
       FROM information_schema.columns, (SELECT @rownum := -1) r
         LIMIT #{diffMonths}
      ) n
        LEFT JOIN (
        SELECT DATE_FORMAT(check_time, '%Y-%m') AS ym, COUNT(*) AS cnt
        FROM rail_hidden_trouble
        WHERE check_time IS NOT NULL and tenant_id = #{tenantId}
        GROUP BY ym
      ) t ON DATE_FORMAT(DATE_ADD(#{startDate}, INTERVAL n.n MONTH), '%Y-%m') = t.ym
    ORDER BY ym;
  </select>


</mapper>
