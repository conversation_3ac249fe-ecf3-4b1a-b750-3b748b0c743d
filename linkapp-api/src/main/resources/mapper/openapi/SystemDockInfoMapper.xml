<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.openapi.mapper.SystemDockInfoMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.openapi.dto.SystemDockInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sysname" jdbcType="VARCHAR" property="sysname" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="data" jdbcType="VARCHAR" property="data" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from system_dock_info where tenant_id = #{systemDockInfo.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from system_dock_info where id = #{id}
    </select>


</mapper>
