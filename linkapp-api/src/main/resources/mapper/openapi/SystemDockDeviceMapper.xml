<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.easylinkin.linkappapi.openapi.mapper.SystemDockDeviceMapper">
	<resultMap id="BaseResultMap"
		type="com.easylinkin.linkappapi.openapi.dto.SystemDockDevice">
		<!-- WARNING - @mbg.generated -->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="device_id" jdbcType="VARCHAR"
			property="deviceId" />
		<result column="type" jdbcType="INTEGER" property="type" />
		<result column="link_id" jdbcType="BIGINT" property="linkId" />
		<result column="is_sync" jdbcType="INTEGER" property="isSync" />
		<result column="deviceName" jdbcType="VARCHAR"
			property="deviceName" />
			<result column="deviceCode" jdbcType="VARCHAR"
			property="deviceCode" />
	</resultMap>

	<select id="selectPage" resultMap="BaseResultMap">
		SELECT a.*,d1.name as deviceName,d1.code as deviceCode from system_dock_device a 
		LEFT JOIN (select * from linkapp_device ) d1 on a.device_id=d1.id
		<where>
			<if
				test="systemDockDevice.linkId!=null ">
				a.link_id=#{systemDockDevice.linkId}
			</if>
			<if
				test="systemDockDevice.type!=null">
				and a.type=#{systemDockDevice.type}
			</if>
			<if
				test="systemDockDevice.deviceId!=null">
				and a.device_id=#{systemDockDevice.deviceId}
			</if>
			<if
				test="systemDockDevice.isSync!=null">
				and a.is_sync=#{systemDockDevice.isSync}
			</if>
			<if
				test="systemDockDevice.deviceCode!=null">
				and d1.code=#{systemDockDevice.deviceCode}
			</if>
		</where>
	</select>

	<select id="getOneById" resultMap="BaseResultMap">
		select * from
		system_dock_device where id = #{id}
	</select>

</mapper>
