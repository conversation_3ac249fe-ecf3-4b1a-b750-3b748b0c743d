<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.openapi.mapper.SystemDockingMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.openapi.dto.SystemDocking">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sys_name" jdbcType="VARCHAR" property="sysName" />
    <result column="appid" jdbcType="VARCHAR" property="appid" />
    <result column="secret" jdbcType="VARCHAR" property="secret" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="createor" jdbcType="VARCHAR" property="createor" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

   <select id="selectPage" resultMap="BaseResultMap">
		select * from system_docking a
		<where>
			<if
				test="systemDocking.tenantId!=null and systemDocking.tenantId!=''">
				a.tenant_id=#{systemDocking.tenantId}
			</if>
			<if
				test="systemDocking.sysName!=null and systemDocking.sysName!=''">
				and a.sys_name=#{systemDocking.sysName}
			</if>
		</where>

		order by modify_time desc, create_time desc
	</select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from system_docking where id = #{id}
    </select>


</mapper>
