<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.investigation.mapper.InvestigationTaskModelMapper">
  <resultMap id="getInvestigationTaskModelWithContentsMap" type="com.easylinkin.linkappapi.investigation.entity.InvestigationTaskModel">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="description" property="description"/>
    <result column="status" property="status"/>
    <result column="cycle" property="cycle"/>
    <result column="execute_time" property="executeTime"/>
    <result column="period" property="period"/>
    <result column="cycle_cron" property="cycleCron"/>
    <result column="start_time" property="startTime"/>
    <result column="end_time" property="endTime"/>
    <result column="tenant_id" property="tenantId"/>
    <collection property="investigationTaskModelContentList" ofType="com.easylinkin.linkappapi.investigation.entity.InvestigationTaskModelContent">
      <id column="itmc_id" property="id"/>
      <result column="investigation_task_model_id" property="investigationTaskModelId"/>
      <result column="investigation_type_id" property="investigationTypeId"/>
      <result column="investigation_type_name" property="investigationTypeName"/>
      <result column="area_id" property="areaId"/>
      <result column="itmc_description" property="description"/>
      <result column="user_id" property="userId"/>
      <result column="sort_no" property="sortNo"/>
    </collection>
  </resultMap>

  <select id="getInvestigationTaskModelWithContents" resultMap="getInvestigationTaskModelWithContentsMap">
    select itm.id,
           itm.status,
           itm.name,
           itm.description,
           itm.cycle,
           itm.execute_time,
           itm.period,
           itm.cycle_cron,
           itm.start_time,
           itm.end_time,
           itm.tenant_id,
           itmc.id          itmc_id,
           itmc.investigation_task_model_id,
           itmc.investigation_type_id,
           itmc.sort_no,
           itmc.area_id,
           itmc.description itmc_description,
           itmc.user_id,
           it.name          investigation_type_name
    from investigation_task_model itm
           left join investigation_task_model_content itmc on itm.id = itmc.investigation_task_model_id
           left join investigation_type it on itmc.investigation_type_id = it.id
    <where>
      it.name is not null
      <if test="id != null">
        and itm.id = #{id,jdbcType=BIGINT}
      </if>
      <if test="id != null">
        and itm.id = #{id,jdbcType=BIGINT}
      </if>
      <if test="tenantId != null and tenantId != ''">
        and it.tenant_id = #{tenantId,jdbcType=BIGINT}
      </if>
    </where>
  </select>
</mapper>
