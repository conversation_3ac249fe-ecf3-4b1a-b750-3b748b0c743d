<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.investigation.mapper.InvestigationTaskModelContentMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.investigation.entity.InvestigationTaskModelContent">
    <id column="id" property="id"/>
    <result column="investigation_task_model_id" property="investigationTaskModelId"/>
    <result column="investigation_type_id" property="investigationTypeId"/>
    <result column="sort_no" property="sortNo"/>
    <result column="area_id" property="areaId"/>
    <result column="description" property="description"/>
    <result column="user_id" property="userId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
  </resultMap>

</mapper>
