<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.investigation.mapper.InvestigationTaskMapper">
  <!--  -->
  <resultMap id="getInvestigationTaskWithDetailsMap" type="com.easylinkin.linkappapi.investigation.entity.InvestigationTask">
    <id column="id" property="id"/>
    <result column="it_name" property="name"/>
    <result column="start_time" property="startTime"/>
    <result column="end_time" property="endTime"/>
    <result column="it_status" property="status"/>
    <result column="investigation_task_model_id" property="investigationTaskModelId"/>
    <collection property="investigationTaskDetails" ofType="com.easylinkin.linkappapi.investigation.entity.InvestigationTaskDetail">
      <id column="itf_id" property="id"/>
      <result column="investigation_task_id" property="investigationTaskId"/>
      <result column="investigation_type_name" property="investigationTypeName"/>
      <result column="face_recognition_status" property="faceRecognitionStatus"/>
      <result column="sort_no" property="sortNo"/>
      <result column="user_id" property="userId"/>
      <result column="area_id" property="areaId"/>
      <result column="view_status" property="viewStatus"/>
      <result column="deal_status" property="dealStatus"/>
      <result column="description" property="description"/>
      <result column="deal_time" property="dealTime"/>
      <result column="facilities_picture" property="facilitiesPicture"/>
      <result column="face_picture" property="facePicture"/>
      <result column="user_name" property="userName"/>
      <result column="area_path" property="areaPath"/>
    </collection>
  </resultMap>

  <select id="getInvestigationTaskWithDetails" resultMap="getInvestigationTaskWithDetailsMap">
    select it.start_time,
           it.`name_`   as it_name,
           it.end_time,
           it.id,
           it.investigation_task_model_id,
           it.`status_` as it_status,
           itf.investigation_task_id,
           itf.area_id,
           itf.user_id,
           itf.deal_time,
           itf.id          itf_id,
           itf.investigation_task_id,
           itf.investigation_type_name,
           itf.sort_no,
           itf.user_id,
           itf.user_name,
           itf.area_id,
           itf.area_path,
           itf.view_status,
           itf.deal_status,
           itf.description,
           itf.deal_time,
           itf.facilities_picture,
           itf.face_recognition_status,
           itf.face_picture
    from investigation_task it
           left join investigation_task_detail itf on it.id = itf.investigation_task_id
    <where>
      <if test="id != null and id != ''">
        and it.id = #{id,jdbcType=BIGINT}
      </if>
    </where>
  </select>

    <select id="getInvestigationBigScreenCount" resultType="java.util.Map" parameterType="com.easylinkin.linkappapi.investigation.entity.InvestigationTask">
        select
         sum(case when TO_DAYS(create_time)=TO_DAYS(NOW()) then 1 else 0 end ) as 'day'
        ,sum(case when YEARWEEK(DATE_FORMAT(create_time,'%Y-%m-%d'),1)=YEARWEEK(NOW(),1) then 1 else 0 end ) as 'week'
        ,sum(case when DATE_FORMAT(create_time,'%Y%m' ) = DATE_FORMAT(CURDATE() ,'%Y%m') then 1 else 0 end ) as 'month'
        ,sum(case when status_=0 then 1 else 0 end) as '0'
        ,sum(case when status_=1 then 1 else 0 end) as '1'
        ,sum(case when status_=2 then 1 else 0 end) as '2'
        from investigation_task
        <where>
            <if test="tenantId != null and tenantId != ''">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>
</mapper>
