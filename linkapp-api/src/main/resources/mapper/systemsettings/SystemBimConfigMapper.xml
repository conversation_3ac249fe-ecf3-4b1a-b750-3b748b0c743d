<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.systemsettings.mapper.SystemBimConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.systemsettings.entity.SystemBimConfig">
        <id column="id_" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="integrate_id_" property="integrateId" />
        <result column="scene_id_" property="sceneId" />
        <result column="creator_" property="creator" />
        <result column="create_time_" property="createTime" />
        <result column="modifier_" property="modifier" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_system_bim_config where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_system_bim_config where id_ = #{id} 
    </select>


</mapper>
