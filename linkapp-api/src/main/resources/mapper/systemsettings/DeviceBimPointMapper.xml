<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.systemsettings.mapper.DeviceBimPointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.systemsettings.entity.DeviceBimPoint">
        <id column="id_" property="id" />
        <result column="device_code_" property="deviceCode" />
        <result column="tenant_id_" property="tenantId" />
        <result column="point_x_" property="pointX" />
        <result column="point_y_" property="pointY" />
        <result column="point_z_" property="pointZ" />
        <result column="resource_id_" property="resourceId" />
        <result column="group_resource_id_" property="groupResourceId" />
        <result column="creator_" property="creator" />
        <result column="create_time_" property="createTime" />
        <result column="modifier_" property="modifier" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <resultMap id="BimDevicePointDtoMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.systemsettings.entity.dto.DeviceBimPointDTO">
        <association property="device" column="device_code_" select="com.easylinkin.linkappapi.device.mapper.DeviceMapper.getDeviceByCode" />
        <association property="deviceType" column="device_code_" select="com.easylinkin.linkappapi.devicetype.mapper.DeviceTypeMapper.getDeviceTypeByDeviceCode" />

    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_device_bim_point where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_device_bim_point where id_ = #{id} 
    </select>

    <select id="selectDtoPage" parameterType="com.easylinkin.linkappapi.systemsettings.entity.vo.DeviceBimPointVo" resultMap="BimDevicePointDtoMap">
        select p.*
        from app_device_bim_point p,
        linkapp_device ld ,
        linkapp_device_unit ldu ,
        linkapp_device_type ldt
        where p.deleted_ = 0
            and p.device_code_ = ld.code
            and ld.device_unit_id = ldu.id
            and ldu.device_type_id = ldt.id
        <if test="bimPointVo.deviceCode != null and bimPointVo.deviceCode != ''">
            and p.device_code_ = #{bimPointVo.deviceCode}
        </if>
        <if test="bimPointVo.groupResourcesIdList != null and bimPointVo.groupResourcesIdList.size() > 0">
            and p.group_resource_id_ in
            <foreach collection="bimPointVo.groupResourcesIdList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="bimPointVo.deviceTypeList != null and bimPointVo.deviceTypeList.size() > 0">
            and ldt.name in
            <foreach collection="bimPointVo.deviceTypeList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item.name}
            </foreach>
        </if>
    </select>
</mapper>
