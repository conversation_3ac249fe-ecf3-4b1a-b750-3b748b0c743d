<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.systemsettings.mapper.BimIntegrateItemMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.systemsettings.entity.BimIntegrateItem">
        <id column="id_" property="id"/>
        <result column="integrate_id_" property="integrateId"/>
        <result column="name_" property="name"/>
        <result column="type_" property="type"/>
        <result column="resource_id_" property="resourceId"/>
        <!-- <result column="parent_resource_id_" property="parentResourceId"/> -->
        <result column="bim_resource_id_" property="bimResourceId"/>
        <!-- <result column="bim_parent_resource_id_" property="bimParentResourceId"/> -->
        <result column="parent_id_" property="parentId"/>
        <result column="parent_ids_" property="parentIds"/>
        <result column="file_id_" property="fileId"/>
        <result column="file_ids_" property="fileIds"/>
        <result column="element_count_" property="elementCount"/>
        <!-- <result column="element_ids_" property="elementIds"/> -->
        <result column="leaf_" property="leaf"/>
        <result column="creator_" property="creator"/>
        <result column="create_time_" property="createTime"/>
        <result column="modifier_" property="modifier"/>
        <result column="modify_time_" property="modifyTime"/>
        <!-- <result column="deleted_" property="deleted"/> -->
    </resultMap>
    <resultMap id="IntegrateProgressMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.progress.entity.dto.BimIntegrateProgressDTO">
        <association property="progressWarn" column="progress_task_id_" select="com.easylinkin.linkappapi.progress.mapper.ProgressWarnMapper.selectByProgressId"/>
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_bim_integrate_item
        where 1=1
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_bim_integrate_item
        where id_ = #{id}
    </select>

    <select id="queryBuildingBindIntegrateList" parameterType="long" resultMap="BaseResultMap">
        select abii.*
        from app_bim_integrate_item abii,
             app_building_integrate_ref abir,
             app_system_bim_config asbc
        where abir.deleted_ = 0
          and asbc.deleted_ = 0
          and abii.integrate_id_ = asbc.integrate_id_
          and abii.resource_id_ = abir.resource_id_
          and abir.building_id_ = #{buildingId}
        <if test="tenantId != null and tenantId != ''">
            and asbc.tenant_id_ = #{tenantId}
        </if>
    </select>

    <select id="queryIntegrateTree"
            parameterType="com.easylinkin.linkappapi.systemsettings.entity.vo.BimIntegrateItemVo"
            resultMap="BaseResultMap">
        SELECT t.*
        FROM app_bim_integrate_item t
        WHERE 1=1
        <if test="bimIntegrateItemVo.integrateId != null and bimIntegrateItemVo.integrateId != ''">
            AND t.integrate_id_ = #{bimIntegrateItemVo.integrateId}
        </if>

        <if test="bimIntegrateItemVo.parentIdStrList != null and bimIntegrateItemVo.parentIdStrList.size() != 0">
            <foreach collection="bimIntegrateItemVo.parentIdStrList" item="item" index="index"
                     open="AND (t.parent_ids_ LIKE concat(''," separator="'%') OR t.parent_ids_ LIKE concat('',"
                     close="'%'))">
                '${item}'
            </foreach>
        </if>

        <if test="bimIntegrateItemVo.typeStrList != null and bimIntegrateItemVo.typeStrList.size() != 0">
            AND t.type_ IN
            <foreach collection="bimIntegrateItemVo.typeStrList" item="item" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryFloorBindIntegrateList" resultMap="BaseResultMap">
        select abii.*
        from app_bim_integrate_item abii,
             app_floor_integrate_ref afir,
             app_system_bim_config asbc
        where asbc.deleted_ = 0
          and abii.integrate_id_ = asbc.integrate_id_
          and abii.resource_id_ = afir.resource_id_
          and afir.floor_id_ = #{floorId}
        <if test="tenantId != null and tenantId != ''">
            and asbc.tenant_id_ = #{tenantId}
        </if>
    </select>

<!--    <select id="queryProgressBindIntegrateList" resultMap="BaseResultMap">-->
<!--        select distinct abii.*-->
<!--        from app_bim_integrate_item abii,-->
<!--             app_system_bim_config asbc,-->
<!--             app_progress_info api,-->
<!--             app_progress_integrate_ref apir-->
<!--        where asbc.deleted_ = 0-->
<!--          and api.deleted_ = 0-->
<!--          and abii.integrate_id_ = asbc.integrate_id_-->
<!--          and asbc.tenant_id_ = api.tenant_id_-->
<!--          and abii.resource_id_ = apir.resource_id_-->
<!--          and api.id_ = apir.progress_id_-->
<!--          and api.id_ = #{progressId}-->
<!--    </select>-->

    <select id="queryProgressBindIntegrateList" resultMap="BaseResultMap">
        select distinct apir.resource_id_
        from
             app_progress_info api,
             app_progress_integrate_ref apir
        where  api.deleted_ = 0
          and api.id_ = apir.progress_id_
          and api.id_ = #{progressId}
    </select>

<!--    <select id="screenRealProgressIntegrateList" parameterType="com.easylinkin.linkappapi.progress.entity.vo.ProgressInfoVo" resultMap="IntegrateProgressMap">-->
<!--        select distinct abii.*, aprd.progress_task_id_-->
<!--        from app_progress_info api,-->
<!--             app_progress_real_detail aprd,-->
<!--             app_progress_integrate_ref apir,-->
<!--             app_bim_integrate_item abii,-->
<!--             app_system_bim_config asbc-->
<!--        where api.deleted_ = 0-->
<!--          and aprd.deleted_ = 0-->
<!--          and asbc.deleted_ = 0-->
<!--          and api.id_ = aprd.progress_task_id_-->
<!--          and api.id_ = apir.progress_id_-->
<!--          and asbc.tenant_id_ = api.tenant_id_-->
<!--          and abii.integrate_id_ = asbc.integrate_id_-->
<!--          and abii.resource_id_ = apir.resource_id_-->
<!--        <if test="progressInfoVo != null">-->
<!--            <if test="progressInfoVo.type != null and progressInfoVo.type != ''">-->
<!--                and api.type_ = #{progressInfoVo.type}-->
<!--            </if>-->
<!--            <if test="progressInfoVo.tenantId != null and progressInfoVo.tenantId != ''">-->
<!--                and api.tenant_id_ = #{progressInfoVo.tenantId}-->
<!--            </if>-->
<!--            <if test="progressInfoVo.planType != null and progressInfoVo.planType != ''">-->
<!--                and api.plan_type_ = #{progressInfoVo.planType}-->
<!--            </if>-->
<!--            <if test="progressInfoVo.parentIds != null and progressInfoVo.parentIds != ''">-->
<!--                and api.parent_ids_ like concat('', #{progressInfoVo.parentIds}, '%')-->
<!--            </if>-->
<!--        </if>-->
<!--    </select>-->

    <select id="screenRealProgressIntegrateList" parameterType="com.easylinkin.linkappapi.progress.entity.vo.ProgressInfoVo" resultMap="IntegrateProgressMap">
        select distinct apir.resource_id_, aprd.progress_task_id_
        from app_progress_info api,
        app_progress_real_detail aprd,
        app_progress_integrate_ref apir
        where api.deleted_ = 0
        and aprd.deleted_ = 0
        and api.id_ = aprd.progress_task_id_
        and api.id_ = apir.progress_id_
        <if test="progressInfoVo != null">
            <if test="progressInfoVo.type != null and progressInfoVo.type != ''">
                and api.type_ = #{progressInfoVo.type}
            </if>
            <if test="progressInfoVo.tenantId != null and progressInfoVo.tenantId != ''">
                and api.tenant_id_ = #{progressInfoVo.tenantId}
            </if>
            <if test="progressInfoVo.planType != null and progressInfoVo.planType != ''">
                and api.plan_type_ = #{progressInfoVo.planType}
            </if>
            <if test="progressInfoVo.parentIds != null and progressInfoVo.parentIds != ''">
                and api.parent_ids_ like concat('', #{progressInfoVo.parentIds}, '%')
            </if>
        </if>
    </select>
</mapper>
