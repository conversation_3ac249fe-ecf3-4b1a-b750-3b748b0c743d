<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.building.mapper.FloorMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.building.entity.Floor">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="building_id" jdbcType="INTEGER" property="buildingId"/>
        <result column="type" jdbcType="SMALLINT" property="type"/>
        <result column="sort_no" jdbcType="INTEGER" property="sortNo"/>
        <result column="building_floor_height" jdbcType="REAL" property="buildingFloorHeight"/>
        <result column="structure_floor_height" jdbcType="REAL" property="structureFloorHeight"/>
        <result column="building_standard_height" jdbcType="REAL" property="buildingStandardHeight"/>
        <result column="structure_standard_height" jdbcType="REAL" property="structureStandardHeight"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_floor
        <where>
            tenant_id = #{appFloor.tenantId}
            <if test="appFloor.buildingId != null">
                and building_id = #{appFloor.buildingId,jdbcType=INTEGER}
            </if>
        </where>
        order by sort_no desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_floor
        where id = #{id}
    </select>
</mapper>
