<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.building.mapper.BuildingIntegrateRefMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.building.entity.BuildingIntegrateRef">
        <id column="id_" property="id" />
        <result column="building_id_" property="buildingId" />
        <result column="resource_id_" property="resourceId" />
        <result column="tenant_id_" property="tenantId" />
        <result column="creator" property="creator" />
        <result column="create_time_" property="createTime" />
        <result column="modifier" property="modifier" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_building_integrate_ref where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_building_integrate_ref where id_ = #{id} 
    </select>


</mapper>
