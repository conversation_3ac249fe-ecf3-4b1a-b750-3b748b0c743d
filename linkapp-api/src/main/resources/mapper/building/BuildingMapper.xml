<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.building.mapper.BuildingMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.building.entity.Building">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="total_building_height" jdbcType="REAL" property="totalBuildingHeight"/>
        <result column="total_floor_height" jdbcType="REAL" property="totalFloorHeight"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
    </resultMap>

    <resultMap id="BuildingFloorVOResultMap" type="com.easylinkin.linkappapi.building.vo.BuildingFloorVO">
        <id column="bid" property="id" />
        <result column="building_name" property="buildingName" />
        <collection property="floors" resultMap="FloorListResultMap" />
    </resultMap>

    <resultMap id="FloorListResultMap" type="com.easylinkin.linkappapi.building.entity.Floor">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="num" property="num" />
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_building where tenant_id = #{appBuilding.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_building where id = #{id}
    </select>

    <select id="getTree" resultMap="BuildingFloorVOResultMap">
        select a.id as bid,
               a.name as building_name,
               b.id,
               b.name
        from app_building as a
        left join app_floor as b on a.id = b.building_id
        where a.tenant_id = #{tenantId}
    </select>

    <select id="getTreeWithNum" resultMap="BuildingFloorVOResultMap">
        SELECT
            a.id as bid,
            a.name as building_name,
            b.id,
            b.name,
            COUNT(d.id) num
        FROM
            app_building a
                LEFT JOIN app_floor b ON a.id = b.building_id
                LEFT JOIN app_floor_beacon c ON b.id = c.floor_id_
                LEFT JOIN linkapp_location_device d ON c.device_code_ = d.label_code_
                AND d.tenant_id_ = a.tenant_id AND d.bind_state_ = 1 AND d.online_state_ = 1
                AND d.delete_state_ = 1 AND d.location_type_ = 0
        WHERE
            a.tenant_id = #{tenantId}
        GROUP BY
            b.id
    </select>
</mapper>
