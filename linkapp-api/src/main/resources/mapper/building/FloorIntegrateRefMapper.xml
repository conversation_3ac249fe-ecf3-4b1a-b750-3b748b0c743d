<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.building.mapper.FloorIntegrateRefMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.building.entity.FloorIntegrateRef">
        <id column="id_" property="id" />
        <!-- <result column="tenant_id_" property="tenantId" /> -->
        <result column="building_id_" property="buildingId" />
        <result column="floor_id_" property="floorId" />
        <result column="resource_id_" property="resourceId" />
        <result column="creator_" property="creator" />
        <result column="create_time_" property="createTime" />
        <result column="modifier_" property="modifier" />
        <result column="modify_time_" property="modifyTime" />
        <!-- <result column="deleted_" property="deleted" /> -->
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_floor_integrate_ref where 1=1 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_floor_integrate_ref where id_ = #{id}
    </select>


</mapper>
