<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.devicetype.mapper.DeviceTypeMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.devicetype.entity.DeviceType">
    <id column="id" property="id"/>
    <result column="parent_id" property="parentId"/>
    <result column="name" property="name"/>
    <result column="description" property="description"/>
    <result column="ico_path" property="icoPath"/>
    <result column="level" property="level"/>
    <result column="search_code" property="searchCode"/>
    <result column="remark" property="remark"/>
    <result column="company_id" property="companyId"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>

  <select id="countGroupByDeviceTypeGlobal" parameterType="com.easylinkin.linkappapi.shigongyun.vo.DeviceCountVo" resultType="java.util.Map">
    select
      ldt.name as deviceTypeName,
      ldt.ico_path as icoPath,
      count(*) as num
    from
      linkapp_device ld ,
      linkapp_device_unit ldu ,
      linkapp_device_type ldt
    where
      ld.device_unit_id = ldu.id
      and ldu.device_type_id = ldt.id
      and ld.delete_state = 1
      and ld.linkthing_delete = 1
    <if test="tenantId != null and tenantId != ''">
      and ld.tenant_id = #{tenantId}
    </if>
    group by ldt.id
  </select>
</mapper>
