<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.application.mapper.ApplicationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.application.entity.Application">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="app_key" property="appKey"/>
        <result column="app_secret" property="appSecret"/>
        <result column="domin" property="domin"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_state" property="deleteState"/>
        <result column="personality_id" property="personalityId"/>

    </resultMap>


    <select id="getApplicationByName" resultMap="BaseResultMap" parameterType="java.lang.String">
		SELECT
        a.id,
        a.personality_id,
        a. NAME,
        a.description,
        a.app_key,
        a.app_secret,
        a.create_time,
        person.domin,
        a.modify_time
        FROM
            linkapp_application a
        LEFT JOIN (
            SELECT
                b.id,
                b.domin
            FROM
                linkapp_personality b
            WHERE
                b.delete_state = 1
        ) person ON a.personality_id = person.id
        WHERE
        a.delete_state = 1
        <if test="name != null and name != ''">
        and    a.name like  CONCAT('%',#{name},'%')
        </if>
        ORDER BY a.modify_time  DESC
    </select>


    <select id="selectApplicationByUser" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        SELECT
        a.*
        FROM
        linkapp_application a
        LEFT JOIN linkapp_tenant b ON a.app_key = b.app_key
        LEFT JOIN linkapp_user c ON c.tenant_id = b.id
        <where>
            <if test="id != null">
                and c.id = #{id}
            </if>
        </where>
    </select>


    <resultMap id="deviceUnitMap" type="com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="name" property="name"/>
        <result column="device_type_id" property="deviceTypeId"/>
        <result column="code" property="code"/>
        <result column="remark" property="remark"/>
        <result column="icon" property="icon"/>
        <result column="identification" property="identification"/>
        <result column="device_life" property="deviceLife"/>
        <result column="repair_cycle" property="repairCycle"/>
        <result column="offline_time" property="offlineTime"/>
    </resultMap>

    <select id="selectApplicationDeviceUnitByUser" resultMap="deviceUnitMap" parameterType="java.lang.String">
        SELECT a.* FROM linkapp_device_unit a
        LEFT JOIN linkapp_application_ref_device_unit b ON a.id = b.device_unit_id AND a.delete_state = 1
        LEFT JOIN linkapp_application c ON b.application_id = c.id AND c.delete_state = 1
        LEFT JOIN linkapp_tenant d ON c.app_key = d.app_key
        LEFT JOIN linkapp_user e ON d.id = e.tenant_id
        <where>
            <if test="id != null">
                and e.id = #{id}
            </if>
        </where>
    </select>


    <select id="selectDeviceUnitByApplication" resultMap="deviceUnitMap"
            parameterType="java.lang.String">
        SELECT a.* FROM linkapp_device_unit a
        LEFT JOIN linkapp_application_ref_device_unit b ON a.id = b.device_unit_id
        LEFT JOIN linkapp_application c ON b.application_id = c.id
        <where>
            <if test="id != null">
                and c.id = #{id}
            </if>
        </where>
    </select>

    <delete id="deleteApplication2DeviceUnits">
		DELETE FROM linkapp_application_ref_device_unit WHERE application_id = #{id}
	</delete>

    <insert id="insertApplication2DeviceUnits">
        insert into linkapp_application_ref_device_unit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="applicationId != null">
                application_id,
            </if>
            <if test="deviceUnitId != null">
                device_unit_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="applicationId != null">
                #{applicationId},
            </if>
            <if test="deviceUnitId != null">
                #{deviceUnitId}
            </if>
        </trim>
    </insert>


    <resultMap id="linkappPrivilegeMap" type="com.easylinkin.linkappapi.security.entity.LinkappPrivilege">
        <id column="id_" property="id"/>
        <result column="parent_id_" property="parentId"/>
        <result column="name_" property="name"/>
        <result column="privilege_code_" property="code"/>
        <result column="description_" property="description"/>
        <result column="level_" property="level"/>
        <result column="target" property="target"/>
        <result column="search_code_" property="searchCode"/>
        <result column="seq_" property="sort"/>
        <result column="type_" property="type"/>
        <result column="url_" property="url"/>
        <result column="is_log_" property="isLog"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time_" property="createTime"/>
        <result column="creator_" property="creator"/>
        <result column="modifier_" property="modifier"/>
        <result column="modify_time_" property="modifyTime"/>
    </resultMap>

    <select id="selectPrivilegeByApplication"
            resultMap="linkappPrivilegeMap"
            parameterType="java.lang.String">
        SELECT a.* FROM linkapp_privilege a
        LEFT JOIN linkapp_application_ref_privilege b ON a.id_ = b.privilege_id
        LEFT JOIN linkapp_application c ON b.application_id = c.id
        <where>
            <if test="id != null">
                and c.id = #{id}
            </if>
        </where>
        order by a.seq_
    </select>

    <select id="selectApplicationPrivilegeByUser" resultMap="linkappPrivilegeMap" parameterType="java.lang.String">
        SELECT a.* FROM linkapp_privilege a
        LEFT JOIN linkapp_application_ref_privilege b ON a.id_ = b.privilege_id
        LEFT JOIN linkapp_application c ON b.application_id = c.id
        LEFT JOIN linkapp_tenant d ON c.app_key = d.app_key
        LEFT JOIN linkapp_user e ON d.id = e.tenant_id
        <where>
            <if test="id != null">
                and e.id = #{id}
            </if>
        </where>
        order by a.seq_
    </select>


    <delete id="deleteApplication2Privileges">
		DELETE FROM linkapp_application_ref_privilege WHERE application_id = #{id}
	</delete>

    <insert id="insertApplication2Privileges">
        insert into linkapp_application_ref_privilege
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="applicationId != null">
                application_id,
            </if>
            <if test="privilegeId != null">
                privilege_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="applicationId != null">
                #{applicationId},
            </if>
            <if test="privilegeId != null">
                #{privilegeId}
            </if>
        </trim>
    </insert>

	
	<delete id="deleteTenant2DeviceUnits">
		DELETE FROM linkapp_tenant_ref_device_unit WHERE device_unit_id = #{deviceUnitId} and tenant_id = #{tenantId}
	</delete>
	
	<delete id="deleteTenant2Privileges">
		DELETE FROM
            linkapp_tenant_ref_privilege
		WHERE
            privilege_id = #{privilegeId}
		    and tenant_id = #{tenantId}
	</delete>
	
	<delete id="deleteRole2Privileges">
		DELETE FROM
		    linkapp_role_ref_privilege
		WHERE
            privilege_id_ = #{privilegeId}
		    and tenant_id = #{tenantId}
	</delete>
	
	<!-- 通用查询映射结果 -->
    <resultMap id="TenantResultMap" type="com.easylinkin.linkappapi.tenant.entity.LinkappTenant">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_key" property="appKey" />
        <result column="app_type" property="appType" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="modifier" property="modifier" />
        <result column="modify_time" property="modifyTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="personality_id" property="personalityId" />
    </resultMap>

	<select id="selectTenantByApplication" resultMap="TenantResultMap" parameterType="java.lang.String">
        SELECT a.* FROM linkapp_tenant a
        <where>
            <if test="appKey != null">
                and a.app_key = #{appKey}
            </if>
        </where>
    </select>

</mapper>
