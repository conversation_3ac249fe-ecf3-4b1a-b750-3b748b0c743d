<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.hcmy.mapper.AreasourceecologyFunctionMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.hcmy.entity.AreasourceecologyFunction">
    <!--@mbg.generated-->
    <!--@Table hcmy_areasourceecology_function-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="area_ids" jdbcType="VARCHAR" property="areaIds" />
    <result column="type" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `year`, area_ids,type
  </sql>
    <select id="findFunctionData" resultType="java.util.LinkedHashMap">
      <![CDATA[
          call hcmy_getfunction_data(#{ids,jdbcType=VARCHAR},#{type,jdbcType=INTEGER})
      ]]>

    </select>

    <select id="findMenuByType" resultType="com.easylinkin.linkappapi.hcmy.entity.AreasourceecologyMenu">
      select m.id,m.dictionary_id dictionaryId,m.type,d.`name` dictionaryName from hcmy_areasourceecology_menu m
      left join hcmy_areasourceecology_dictionary d on d.id=m.dictionary_id
      where m.type=#{type}
      ORDER BY d.seq ASC,d.id ASC
    </select>
  
    <select id="findAreasourceecologyList" resultType="com.easylinkin.linkappapi.hcmy.entity.AreasourceecologyFunction">
      select haf.*,GROUP_CONCAT(haf.area_path) areaPath,
        GROUP_CONCAT(distinct haf.father_area_path) fatherAreaPath,
        GROUP_CONCAT(distinct haf.son_area_path) sonAreaPath,
        GROUP_CONCAT(distinct haf.space_id) spaceIds
      from (
        select haf.*,la.area_path,
          SUBSTRING_INDEX(SUBSTRING_INDEX(la.area_path,':',2),':',-1) father_area_path,
          SUBSTRING_INDEX(la.area_path,':',-1) son_area_path,
          la.space_id
        from hcmy_areasourceecology_function haf
        left join linkapp_area la on FIND_IN_SET(la.id,haf.area_ids) and haf.tenant_id=la.tenant_id
      <where>
        <if test="areasourceecologyFunction.tenantId != null and areasourceecologyFunction.tenantId != '' ">
          and haf.tenant_id = #{areasourceecologyFunction.tenantId,jdbcType=VARCHAR}
        </if>
        <if test="areasourceecologyFunction.type != null ">
          and haf.type = #{areasourceecologyFunction.type,jdbcType=INTEGER}
        </if>
        <if test="areasourceecologyFunction.year != null">
          and haf.year = #{areasourceecologyFunction.year,jdbcType=INTEGER}
        </if>
      </where>
      order by FIND_IN_SET(la.id,haf.area_ids) asc) haf
      group by haf.id
      <if test="areasourceecologyFunction.areaPath != null and areasourceecologyFunction.areaPath != ''">
        HAVING  (areaPath = #{areasourceecologyFunction.areaPath,jdbcType=VARCHAR}
        or areaPath like concat(#{areasourceecologyFunction.areaPath,jdbcType=VARCHAR},',%')
        or areaPath like concat('%',',',#{areasourceecologyFunction.areaPath,jdbcType=VARCHAR})
        or areaPath like concat('%',',',#{areasourceecologyFunction.areaPath,jdbcType=VARCHAR},',','%')
        or areaPath like concat(#{areasourceecologyFunction.areaPath,jdbcType=VARCHAR},':%')
        or areaPath like concat('%',',',#{areasourceecologyFunction.areaPath,jdbcType=VARCHAR},':%'))
      </if>
      order by haf.modify_time desc,haf.create_time desc
    </select>
</mapper>