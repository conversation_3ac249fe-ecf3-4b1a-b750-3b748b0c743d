<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.hcmy.mapper.AreasourceecologyDictionaryFunctionDataMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.hcmy.entity.AreasourceecologyDictionaryFunctionData">
    <!--@mbg.generated-->
    <!--@Table hcmy_areasourceecology_dictionary_function_data-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="menu_id" jdbcType="INTEGER" property="menuId" />
    <result column="number" jdbcType="DECIMAL" property="number" />
    <result column="function_id" jdbcType="INTEGER" property="functionId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dictionary_id, `number`, function_id
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hcmy_areasourceecology_dictionary_function_data
    (menu_id, `number`, function_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.menuId,jdbcType=INTEGER}, #{item.number,jdbcType=DECIMAL}, #{item.functionId,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
</mapper>