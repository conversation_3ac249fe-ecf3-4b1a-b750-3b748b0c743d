<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.hcmy.mapper.AquacultureAreaMapper">

    <resultMap id="aquacultureMap" type="com.easylinkin.linkappapi.hcmy.entity.AquacultureSpace">
        <id column="id" property="id"/>
        <result column="statistic_year" property="statisticYear"/>
        <result column="space_id" property="spaceId"/>
        <result column="spaceName" property="spaceName"/>
        <result column="aquaculture_total_area" property="aquacultureTotalArea"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
        <collection property="aquacultureAreaList" column="{pasId=id}" select="getAquacultureAreaList"
                    javaType="java.util.ArrayList" ofType="com.easylinkin.linkappapi.hcmy.entity.AquacultureArea">
        </collection>
    </resultMap>


    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into hcmy_aquaculture_area
        (as_id, area_id, aquaculture_area)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.asId},
            #{item.areaId},
            #{item.aquacultureArea}
            )
        </foreach>
    </insert>

    <select id="getAquacultureAreaList" resultType="com.easylinkin.linkappapi.hcmy.entity.AquacultureArea">
        SELECT aa.id,
               aa.as_id            asId,
               aa.area_id          areaId,
               aa.aquaculture_area aquacultureArea,
               a.area_name         areaName
        FROM hcmy_aquaculture_area aa
                 LEFT JOIN linkapp_area a ON aa.area_id = a.id
        where aa.as_id = #{pasId}
    </select>

    <select id="getAquacultureSpacePage" resultMap="aquacultureMap">
        SELECT
        aqs.id,
        aqs.statistic_year,
        aqs.space_id,
        aqs.aquaculture_total_area,
        a.area_name spaceName
        FROM
        hcmy_aquaculture_space aqs
        LEFT JOIN linkapp_area a ON aqs.space_id = a.id
        <where>
            <if test="aquacultureSpace.statisticYear != null">
                and aqs.statistic_year=#{aquacultureSpace.statisticYear}
            </if>
            <if test="aquacultureSpace.spaceId != null and aquacultureSpace.spaceId != '' ">
                and aqs.space_id=#{aquacultureSpace.spaceId}
            </if>
            <if test="aquacultureSpace.tenantId != null and aquacultureSpace.tenantId != '' ">
                and aqs.tenant_id=#{aquacultureSpace.tenantId}
            </if>
        </where>
        order by aqs.modify_time desc
    </select>

</mapper>