<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.hcmy.mapper.CarLocusMapper">

    <select id="getStatisticsInfo" resultType="com.easylinkin.linkappapi.positioning.entity.MonitorMapStatistics">
        SELECT
        count(distinct lcm.gps_id) allNum,
        count(distinct case when d.online_state = 1  then lcm.gps_id else null end) onlineNum,
        count(distinct case when d.online_state = 0 then lcm.gps_id else null end) offlineNum
        FROM  linkapp_car_manage lcm
        left  join linkapp_device d on lcm.gps_id=d.id
        WHERE
        d.delete_state = 1
        <if test="tenantId != null and tenantId != ''">
            and lcm.tenant_id=#{tenantId}
        </if>
        <if test="carCode != null and carCode != ''">
            and lcm.code like concat('%', #{carCode},'%')
        </if>
    </select>

    <select id="getDeviceList" resultMap="MonitorDevicesMap">
        SELECT
            d_.*
        FROM
        (
            SELECT
            d.carCode,
            d.area_id,
            d.area_path,
            d.id,
            d.name,
            d.code,
            d.remark,
            d.device_unit_id,
            d.create_time,
            d.online_state,
            d.tenant_id,
            d.status,
            u.device_type_name AS d_deviceTypeName,
            u.code             AS d_device_unit_code,
            u.name             AS deviceUnitName,
            u.version          AS deviceUnitVersion,
            ldt.ico_path
            FROM
            (
                select
                    lcm.code carCode,
                    dChild.*
                from
                linkapp_car_manage lcm left join linkapp_device dChild on lcm.gps_id=dChild.id
                <where>
                    <if test="qmm.tenantId != null and qmm.tenantId != ''">
                        and lcm.tenant_id = #{qmm.tenantId}
                    </if>
                </where>
                group by dChild.id
            ) d
            LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
            left join linkapp_device_type ldt on ldt.id = u.device_type_id
            where
            d.delete_state = 1
            ORDER BY d.create_time DESC
        ) d_ left join linkapp_device_attribute_status ldas on d_.code=ldas.device_code
        <where>
            <if test="qmm.carCode != null and qmm.carCode != ''">
                and d_.carCode like concat('%', #{qmm.carCode},'%')
            </if>
            <if test="qmm.queryStatus != null">
                and d_.online_state = #{qmm.queryStatus}
            </if>
        </where>
        group by d_.id
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="MonitorDevicesMap" type="com.easylinkin.linkappapi.device.entity.Device">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="online_state" property="onlineState"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="area_id" property="areaId"/>
        <result column="area_path" property="areaPath"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="device_unit_id" property="deviceUnitId"/>
        <result column="d_device_unit_code" property="deviceUnitCode"/>
        <result column="area_path" property="areaPath"/>
        <result column="last_push_time" property="lastPushTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="d_deviceTypeName" property="deviceTypeName"/>
        <result column="deviceUnitVersion" property="deviceUnitVersion"/>
        <result column="carCode" property="carCode"/>
    </resultMap>

</mapper>
