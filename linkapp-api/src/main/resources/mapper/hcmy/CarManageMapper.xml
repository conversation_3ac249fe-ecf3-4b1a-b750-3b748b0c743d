<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.hcmy.mapper.CarManageMapper">

    <select id="getPage" resultType="com.easylinkin.linkappapi.hcmy.entity.CarManage">
        SELECT
            cm.id,
            cm.name,
            cm.code,
            ifnull(cm.image, '') image,
            cm.purpose,
            cm.car_owners,
            cm.capacity,
            cm.create_time,
            cm.gps_id,
            d.code deviceCode,
            d.name deviceName
        FROM
            linkapp_car_manage cm
            LEFT JOIN linkapp_device d ON d.id = cm.gps_id AND d.delete_state = '1'
        <where>
            <if test="carManage.purpose != null and carManage.purpose != '' ">
                and cm.purpose=#{carManage.purpose}
            </if>
            <if test="carManage.deviceCode != null and carManage.deviceCode != '' ">
                and d.code like CONCAT('%',#{carManage.deviceCode},'%')
            </if>
            <if test="carManage.code != null and carManage.code != ''">
                and cm.code like CONCAT('%',#{carManage.code},'%')
            </if>
            <if test="carManage.tenantId != null  and  carManage.tenantId != ''">
                and cm.tenant_id = #{carManage.tenantId}
            </if>
        </where>
        order by cm.create_time desc
    </select>

    <select id="getById" resultType="com.easylinkin.linkappapi.hcmy.entity.CarManage">
        SELECT
            cm.id,
            cm.name,
            cm.code,
            cm.image,
            cm.purpose,
            cm.car_owners,
            cm.capacity,
            cm.create_time,
            d.code deviceCode,
            d.name deviceName
        FROM
            linkapp_car_manage cm
            LEFT JOIN linkapp_device d ON d.id = cm.gps_id AND d.delete_state = '1'
        <where>
            cm.id=#{id}
        </where>
    </select>



    <update id="update" parameterType="com.easylinkin.linkappapi.hcmy.entity.CarManage">
        update
        linkapp_car_manage
        <set>
            <if test="carManage.name != null">
                name = #{carManage.name},
            </if>
            <if test="carManage.code != null">
                code = #{carManage.code},
            </if>
            <if test="carManage.carOwners != null">
                car_owners = #{carManage.carOwners},
            </if>
            <if test="carManage.gpsId != null">
                gps_id = #{carManage.gpsId},
            </if>
            <if test="carManage.capacity != null">
                capacity = #{carManage.capacity},
            </if>
            <if test="carManage.creator != null">
                creator = #{carManage.creator},
            </if>
            <if test="carManage.modifier != null">
                modifier = #{carManage.modifier},
            </if>
            <if test="carManage.purpose != null">
                purpose = #{carManage.purpose},
            </if>
            <if test="carManage.image != null">
                image = #{carManage.image},
            </if>
            modify_time = now()
        </set>
        <where>
            id = #{carManage.id}
        </where>
    </update>

    <insert id="add" parameterType="com.easylinkin.linkappapi.hcmy.entity.CarManage">
        insert into linkapp_car_manage
        (name, code, car_owners, gps_id, capacity, purpose, image, create_time,
         creator, modifier, modify_time,tenant_id)
        values
        (
            #{carManage.name},
            #{carManage.code},
            #{carManage.carOwners},
            #{carManage.gpsId},
            #{carManage.capacity},
            #{carManage.purpose},
            #{carManage.image},
            now(),
            #{carManage.creator},
            #{carManage.modifier},
            now(),
            #{carManage.tenantId}
        )
    </insert>

    <delete id="delById" parameterType="Long">
        delete from linkapp_car_manage where id = #{id}
    </delete>

    <select id="getCarManageList" resultType="com.easylinkin.linkappapi.hcmy.entity.CarManage">
        SELECT
            SUBSTRING_INDEX( cm.image, ',', 1 ) image,
            cm.code,
            cm.name,
            CASE
            cm.purpose
            WHEN '1' THEN
            '田间打药'
            WHEN '2' THEN
            '粪便输送'
            WHEN '3' THEN
            '农村洒水' ELSE '田间撒肥'
            END 'purpose',
            cm.car_owners,
            d.CODE deviceCode,
            cm.capacity,
            cm.create_time
        FROM
            linkapp_car_manage cm
            LEFT JOIN linkapp_device d ON d.id = cm.gps_id AND d.delete_state = '1'
        <where>
            <if test="carManage.purpose != null and carManage.purpose != ''">
                and cm.purpose=#{carManage.purpose}
            </if>
            <if test="carManage.deviceCode != null and carManage.deviceCode !=''">
                and d.code like CONCAT('%',#{carManage.deviceCode},'%')
            </if>
            <if test="carManage.code != null and carManage.code != ''">
                and cm.code like CONCAT('%',#{carManage.code},'%')
            </if>
            <if test="carManage.tenantId != null  and  carManage.tenantId != ''">
                and cm.tenant_id = #{carManage.tenantId}
            </if>
        </where>
        order by cm.create_time desc
    </select>

    <insert id="saveBatchCarManages">
        insert into linkapp_car_manage
            (name, code, car_owners, gps_id, capacity, purpose, image, create_time,
             creator, modifier, modify_time,tenant_id)
            values
        <foreach collection="list" index="index" item="carManages" separator=",">
            (#{carManages.name}, #{carManages.code}, #{carManages.carOwners}, #{carManages.gpsId}, #{carManages.capacity}, #{carManages.purpose}, #{carManages.image}, now(), #{carManages.creator}, #{carManages.modifier}, now(),#{carManages.tenantId})
        </foreach>
    </insert>

    <select id="getDeviceIdByCode" parameterType="String" resultType="String">
        SELECT id FROM linkapp_device as a WHERE code = #{deviceCode} AND delete_state = '1' and tenant_id = #{tenantId}
    </select>

</mapper>
