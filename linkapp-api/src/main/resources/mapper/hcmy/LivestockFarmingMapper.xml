<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.hcmy.mapper.LivestockFarmingMapper">

    <select id="getLivestockFarmingPage" resultType="com.easylinkin.linkappapi.hcmy.entity.LivestockFarming">
        SELECT
            f.id,
            f.statistic_year statisticYear,
            f.space_id spaceId,
            f.area_id areaId,
            f.pig_num pigNum,
            f.meat_poultry_num meatPoultryNum,
            f.egg_num eggNum,
            f.sheep_num sheepNum,
            f.cattle_num cattleNum,
            s.area_name spaceName,
            a.area_name areaName
        FROM
            hcmy_livestock_farming f
            LEFT JOIN linkapp_area s ON f.space_id=s.id
            LEFT JOIN linkapp_area a ON f.area_id=a.id
        <where>
            <if test="livestockFarming.statisticYear != null">
                and f.statistic_year=#{livestockFarming.statisticYear}
            </if>
            <if test="livestockFarming.spaceId != null and livestockFarming.spaceId != '' ">
                and f.space_id=#{livestockFarming.spaceId}
            </if>
            <if test="livestockFarming.areaId != null and livestockFarming.areaId != '' ">
                and f.area_id=#{livestockFarming.areaId}
            </if>
            <if test="livestockFarming.tenantId != null and livestockFarming.tenantId != '' ">
                and f.tenant_id=#{livestockFarming.tenantId}
            </if>
        </where>
        order by f.modify_time desc
    </select>

    <select id="getLivestockFarmingStatisticPage" resultType="com.easylinkin.linkappapi.hcmy.entity.LivestockFarmingStatistic">
        SELECT
            f.statistic_year statisticYear,
            s.area_name spaceName,
            ifnull(sum(f.pig_num), 0) pigTotalNum,
            ifnull(sum(f.meat_poultry_num), 0) meatPoultryTotalNum,
            ifnull(sum(f.egg_num), 0) eggTotalNum,
            ifnull(sum(f.sheep_num), 0) sheepTotalNum,
            ifnull(sum(f.cattle_num), 0) cattleTotalNum
        FROM
            hcmy_livestock_farming f
            LEFT JOIN linkapp_area s ON f.space_id = s.id
            LEFT JOIN linkapp_area a ON f.area_id = a.id
        <where>
            <if test="livestockFarming.statisticYear != null">
                and f.statistic_year=#{livestockFarming.statisticYear}
            </if>
            <if test="livestockFarming.spaceId != null and livestockFarming.spaceId != '' ">
                and f.space_id=#{livestockFarming.spaceId}
            </if>
            <if test="livestockFarming.tenantId != null and livestockFarming.tenantId != '' ">
                and f.tenant_id=#{livestockFarming.tenantId}
            </if>
        </where>
        group by
            f.statistic_year,
            f.space_id
        order by f.statistic_year desc
    </select>

</mapper>
