<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.hcmy.mapper.LivestockFarmersMapper">

    <select id="getLivestockFarmersPage" resultType="com.easylinkin.linkappapi.hcmy.entity.LivestockFarmers">
        SELECT
            f.id,
            f.statistic_year statisticYear,
            f.space_id spaceId,
            f.area_id areaId,
            f.shengzhu_farmers_num shengzhuFarmersNum,
            f.jiaqin_farmers_num jiaqinFarmersNum,
            s.area_name spaceName,
            a.area_name areaName
        FROM
            hcmy_livestock_farmers f
            LEFT JOIN linkapp_area s ON f.space_id=s.id
            LEFT JOIN linkapp_area a ON f.area_id=a.id
        <where>
            <if test="livestockFarmers.statisticYear != null">
                and f.statistic_year=#{livestockFarmers.statisticYear}
            </if>
            <if test="livestockFarmers.spaceId != null and livestockFarmers.spaceId != '' ">
                and f.space_id=#{livestockFarmers.spaceId}
            </if>
            <if test="livestockFarmers.areaId != null and livestockFarmers.areaId != '' ">
                and f.area_id=#{livestockFarmers.areaId}
            </if>
            <if test="livestockFarmers.tenantId != null and livestockFarmers.tenantId != '' ">
                and f.tenant_id=#{livestockFarmers.tenantId}
            </if>
        </where>
        order by f.modify_time desc
    </select>

</mapper>
