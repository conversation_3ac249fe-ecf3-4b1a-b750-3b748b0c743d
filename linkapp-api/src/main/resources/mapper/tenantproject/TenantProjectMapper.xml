<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.tenantproject.dao.TenantProjectMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.tenantproject.entity.TenantProject">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="plan_end_date" jdbcType="TIMESTAMP" property="planEndDate"/>
        <result column="progress" jdbcType="VARCHAR" property="progress"/>
        <result column="build_vol" jdbcType="VARCHAR" property="buildVol"/>
        <result column="contract_amount" jdbcType="DECIMAL" property="contractAmount"/>
        <result column="platform_project_name" jdbcType="VARCHAR" property="platformProjectName"/>
        <result column="bidding_unit" jdbcType="VARCHAR" property="biddingUnit"/>
        <result column="construction_unit" jdbcType="VARCHAR" property="constructionUnit"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="manage_id" jdbcType="BIGINT" property="manageId"/>
        <result column="project_type_name" jdbcType="VARCHAR" property="projectTypeName"/>
        <result column="is_class" jdbcType="VARCHAR" property="isClass"/>
        <result column="pc_class" jdbcType="VARCHAR" property="pcClass"/>
        <result column="phid_type" jdbcType="BIGINT" property="phidType"/>
        <result column="fill_dt" jdbcType="TIMESTAMP" property="fillDt"/>
        <result column="project_statue" jdbcType="INTEGER" property="projectStatue"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>
    <resultMap id="TenantProjectDtoMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.tenantproject.dto.TenantProjectDto">
        <association property="linkappTenant" column="{id=tenant_id}"
                     select="com.easylinkin.linkappapi.tenant.mapper.LinkappTenantMapper.selectLinkappTenant"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_tenant_project
        where tenant_id = #{appTenantProject.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_tenant_project
        where id = #{id}
    </select>

    <select id="countNumGroupByClass" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
            resultType="java.util.Map">
        select atp.pc_class as projectClassType,
               count(*)     as num
        from app_tenant_project atp
        where atp.delete_state = 1
          and atp.is_class = 1
        <if test="startTime != null">
            <![CDATA[
            and atp.fill_dt >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and atp.fill_dt <= #{endTime}
            ]]>
        </if>
        group by atp.pc_class
    </select>

    <select id="countNumGroupByType" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
            resultType="java.util.Map">
        select atp.phid_type as projectType,
               count(*)      as num
        from app_tenant_project atp
        where atp.delete_state = 1
        <if test="startTime != null">
            <![CDATA[
            and atp.fill_dt >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and atp.fill_dt <= #{endTime}
            ]]>
        </if>
        group by atp.phid_type
    </select>

    <select id="sumProjectOutput" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
            resultType="java.math.BigDecimal">
        select ifnull(sum(aoi.fee_tax_total), 0) as outputAmount
        from app_tenant_project atp,
             app_output_info aoi
        where atp.id = aoi.tenant_project_id
          and atp.delete_state = 1
          and aoi.delete_state = 1
        <if test="startTime != null">
            <![CDATA[
            and atp.fill_dt >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and atp.fill_dt <= #{endTime}
            ]]>
        </if>
    </select>

    <select id="countNumGroupByStatue" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
            resultType="java.util.Map">
        select atp.project_statue as projectStatue,
               count(*)           as num
        from app_tenant_project atp
        where atp.delete_state = 1
        <if test="startTime != null">
            <![CDATA[
            and atp.fill_dt >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and atp.fill_dt <= #{endTime}
            ]]>
        </if>
        group by atp.project_statue
    </select>

    <select id="selectOneTenantProjectDto" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
            resultMap="TenantProjectDtoMap">
        select atp.*
        from app_tenant_project atp
        <where>
            <if test="id != null and id != ''">
                and atp.id = #{id}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and atp.tenant_id = #{tenantId}
            </if>
        </where>
        limit 1
    </select>

    <select id="selectTenantProjectDtoList" resultMap="TenantProjectDtoMap">
        select atp.*
        from app_tenant_project atp
        where atp.delete_state = 1
        <if test="tenantId != null and tenantId != ''">
            and atp.tenant_id = #{tenantId}
        </if>
    </select>

    <select id="countNumGroupByArea" parameterType="com.easylinkin.linkappapi.shigongyun.vo.TenantProjectCountVo"
            resultType="java.util.Map">
        select
        <if test="dataType != null and dataType != ''">
            <if test="dataType == 1">
                f.provinceName
            </if>
            <if test="dataType == 2">
                f.cityName
            </if>
            <if test="dataType == 3">
                f.areaName
            </if>
        </if>
        as groupName, count(*) as num
        from
        (
        select m.id,
               m.platform_project_name,
               (select mm.mange_name
                from app_manage_info mm
                where mm.id = amm.parent_id) as provinceName,
               amm.mange_name                as cityName,
               m.areaName,
               m.parent_ids,
               m.mid
        from
        app_manage_info amm
            right join (
        select atp.id,
               atp.platform_project_name,
               ami.id         as manageId,
               ami.parent_ids,
               ami.parent_id  as mid,
               ami.mange_name as areaName
        from app_tenant_project atp,
             app_manage_info ami
        where atp.manage_id = ami.id
          and atp.delete_state = 1
        <if test="id != null and id != ''">
            and ami.parent_ids like concat('', #{id}, '%')
        </if>
        ) m on
            amm.id = m.mid) f
        where 1 = 1
        group by
        <if test="dataType != null and dataType != ''">
            <if test="dataType == 1">
                f.provinceName
            </if>
            <if test="dataType == 2">
                f.cityName

            </if>
            <if test="dataType == 3">

                f.areaName
            </if>
        </if>
    </select>
</mapper>
