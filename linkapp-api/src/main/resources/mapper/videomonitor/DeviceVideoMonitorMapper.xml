<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.videomonitor.mapper.DeviceVideoMonitorMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.videomonitor.entity.DeviceVideoMonitor">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, device_code, tenant_id, create_time
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into device_video_monitor
    (device_code, tenant_id, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deviceCode,jdbcType=VARCHAR}, #{item.tenantId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

  <select id="getDeviceVideoMonitor" resultType="com.easylinkin.linkappapi.videomonitor.entity.DeviceVideoMonitorVo">
      SELECT ld.id, ld.name AS deviceName, ld.area_id AS areaId, ld.area_path AS areaPath,  ld.code AS deviceCode, ld.online_state AS onlineState, ld.device_unit_id AS deviceUnitId,dvm.create_time dvmCreate_time
      FROM linkapp_device ld
      INNER JOIN device_video_monitor dvm ON ld.`code` = dvm.device_code
      WHERE
      ld.delete_state = 1
      and dvm.device_code IS NOT NULL
      <if test="device.tenantId != null and device.tenantId != ''">
        and dvm.tenant_id = #{device.tenantId}
      </if>
      and EXISTS (
        select 1 from linkapp_device_attribute lda where ld.device_unit_id = lda.device_unit_id
        and lda.unit='videoMedia'
      )
        <if test="device.name != null and device.name != ''">
          and ld.name like CONCAT('%', #{device.name}, '%')
        </if>
        <if test="device.code != null  and device.code != ''">
            and ld.code like CONCAT('%', #{device.code}, '%')
        </if>
      <if test="device.id != null   and device.id != ''">
          and ld.id = #{device.id}
      </if>
        <if test="device.deviceCodeList != null and device.deviceCodeList.size()>0">
            and ld.code in
            <foreach collection="device.deviceCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="device.onlineState != null">
          and ld.online_state =#{device.onlineState}
        </if>
      <if test="device.areaPath != null and device.areaPath != ''">
          and (ld.area_path = #{device.areaPath} or ld.area_path like concat(#{device.areaPath},'/%'))
      </if>
      <if test="device.moniterCommonQuery != null and device.moniterCommonQuery != ''">
          and (ld.code like CONCAT('%', #{device.moniterCommonQuery}, '%')
          or ld.name like CONCAT('%', #{device.moniterCommonQuery}, '%')
          )
      </if>
      order by dvm.create_time desc
  </select>

  <select id="getAllDeviceVideoMonitor" resultType="com.easylinkin.linkappapi.videomonitor.entity.DeviceVideoMonitorVo">
    SELECT ld.id, ld.name AS deviceName, ld.area_id AS areaId, ld.area_path AS areaPath,  ld.code AS deviceCode, ld.online_state AS onlineState, ld.device_unit_id AS deviceUnitId
    FROM linkapp_device ld
    WHERE
    ld.delete_state = 1
    <if test="device.tenantId != null and device.tenantId != ''">
      and ld.tenant_id = #{device.tenantId}
    </if>
    and EXISTS (
    select 1 from linkapp_device_attribute lda where ld.device_unit_id = lda.device_unit_id
    and lda.unit='videoMedia'
    )
  </select>

    <select id="getDeviceVideoMonitorNotBind" resultType="com.easylinkin.linkappapi.videomonitor.entity.DeviceVideoMonitorVo">
        SELECT ld.id, ld.name AS deviceName, ld.code AS deviceCode, ld.online_state AS onlineState, ld.device_unit_id AS deviceUnitId,ld.area_path areaPath,ldu.`code` deviceUnitCode
        FROM (
        SELECT d_.*
        FROM linkapp_device d_
        WHERE NOT EXISTS (
        SELECT 1
        FROM device_video_monitor dvm
        WHERE d_.`code` = dvm.device_code
        <if test="device.tenantId != null and device.tenantId != ''">
            and dvm.tenant_id = #{device.tenantId}
        </if>
        )
        <if test="device.tenantId != null and device.tenantId != ''">
            and d_.tenant_id = #{device.tenantId}
        </if>
        ) ld
        LEFT JOIN linkapp_device_attribute lda ON ld.device_unit_id = lda.device_unit_id AND ld.delete_state = 1
        left join linkapp_device_unit ldu on ld.device_unit_id=ldu.id and ld.delete_state = 1
        <where>
            lda.unit='videoMedia' and ld.delete_state = 1
            <if test="device.name != null and device.name != ''">
                and ld.name like CONCAT('%', #{device.name}, '%')
            </if>
            <if test="device.code != null  and device.code != ''">
                and ld.code like CONCAT('%', #{device.code}, '%')
            </if>
            <if test="device.onlineState != null">
                and ld.online_state =#{device.onlineState}
            </if>
        </where>
        group by ld.id
    </select>

    <!-- 获取设备下的视频数据list -->
    <select id="getVideoInfoByDevice" resultType="com.easylinkin.linkappapi.webcammanage.entity.DeviceAttributeStatus">
        select
        ld.id,
        ld.name deviceName,
        ld.code deviceCode,
        ld.device_unit_id deviceUnitId,
        ldas.version version,
        ldas.prop_name propName,
        ldas.prop_code propCode,
        ldas.prop_value propValue,
        lda.sort_no sortNo,
        lda.unit
        from
        linkapp_device ld right join linkapp_device_attribute lda on ld.device_unit_id=lda.device_unit_id and ld.delete_state=1
        right join linkapp_device_unit ldu on ld.device_unit_id=ldu.id and ldu.delete_state=1
        right join linkapp_device_attribute_status ldas on ld.code=ldas.device_code and ldu.version=ldas.version and lda.identifier=ldas.prop_code
        <where>
            <!-- lda.unit='videoMedia' and -->
            ld.id=#{deviceId};
        </where>
    </select>


    <select id="getDeviceVideoMonitorTenantsGlobal" resultType="com.easylinkin.linkappapi.videomonitor.entity.DeviceVideoMonitorVo">
         SELECT ld.id, ld.name AS deviceName, ld.area_id AS areaId, ld.area_path AS areaPath,  ld.code AS deviceCode, ld.online_state AS onlineState, ld.device_unit_id AS deviceUnitId,dvm.create_time dvmCreate_time
        FROM linkapp_device ld
        INNER JOIN device_video_monitor dvm ON ld.`code` = dvm.device_code
        WHERE
        ld.delete_state = 1
        and dvm.device_code IS NOT NULL
        <if test="device.tenantIds != null and  device.tenantIds.size()>0">
            and ld.tenant_id in
            <foreach collection="device.tenantIds" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        and EXISTS (
        select 1 from linkapp_device_attribute lda where ld.device_unit_id = lda.device_unit_id
        and lda.unit='videoMedia'
        )
        <if test="device.name != null and device.name != ''">
            and ld.name like CONCAT('%', #{device.name}, '%')
        </if>
        <if test="device.code != null  and device.code != ''">
            and ld.code like CONCAT('%', #{device.code}, '%')
        </if>
        <if test="device.deviceCodeList != null and device.deviceCodeList.size()>0">
            and ld.code in
            <foreach collection="device.deviceCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="device.onlineState != null">
            and ld.online_state =#{device.onlineState}
        </if>
        <if test="device.areaPath != null and device.areaPath != ''">
            and (ld.area_path = #{device.areaPath} or ld.area_path like concat(#{device.areaPath},'/%'))
        </if>
        <if test="device.moniterCommonQuery != null and device.moniterCommonQuery != ''">
            and (ld.code like CONCAT('%', #{device.moniterCommonQuery}, '%')
            or ld.name like CONCAT('%', #{device.moniterCommonQuery}, '%')
            )
        </if>
        order by dvm.create_time desc

    </select>
</mapper>