<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.elasticsearch.mapper.ElasticsearchRealMapper">
<!--  <delete id="deleteAll">-->
<!--    delete-->
<!--    from linkapp_elstic_devenergyreal-->
<!--    where 1 = 1-->
<!--  </delete>-->

<!--  <select id="getRealDeviceList" resultType="com.easylinkin.linkappapi.elasticsearch.entity.DeviceEnergyReal"-->
<!--    parameterType="com.easylinkin.linkappapi.elasticsearch.entity.EsQuerymodel">-->
<!--    SELECT a.dayEnergy  AS dayEnergy,-->
<!--           a.sumEnergy  AS sumEnergy,-->
<!--           b.CODE       AS deviceCode,-->
<!--           d.NAME       AS deviceTypeName,-->
<!--           c.code       AS deviceUnitCode,-->
<!--           e.space_name AS spaceName,-->
<!--           f.area_path  AS areaPath,-->
<!--           e.id         as spaceId,-->
<!--           f.id         as areaId-->
<!--    FROM linkapp_elstic_devenergyreal a-->
<!--           INNER JOIN linkapp_device b ON a.deviceCode = b.CODE-->
<!--           LEFT JOIN linkapp_device_unit c ON b.device_unit_id = c.id-->
<!--           LEFT JOIN linkapp_device_type d ON c.device_type_id = d.id-->
<!--           LEFT JOIN linkapp_area f ON b.area_id = f.id-->
<!--           LEFT JOIN linkapp_space e ON f.space_id = e.id-->
<!--      WHERE-->
<!--      b.delete_state = 1-->
<!--        and a.batchId = #{queryModel.batchId}-->
<!--    <if test="queryModel.deviceCode != null and queryModel.deviceCode != ''">-->
<!--      AND a.deviceCode LIKE CONCAT('%', #{queryModel.deviceCode}, '%')-->
<!--    </if>-->
<!--    <if test="queryModel.areaPath != null and queryModel.areaPath != ''">-->
<!--      AND (f.area_path = #{queryModel.areaPath} or f.area_path like concat(#{queryModel.areaPath},':%'))-->
<!--    </if>-->
<!--  </select>-->


</mapper>
