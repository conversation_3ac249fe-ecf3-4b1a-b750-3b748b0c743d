<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.intelligentrule.mapper.IntelligentRuleExpressionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
        <id column="id" property="id"/>
        <!--    <result column="Intelligent_rule_id" property="intelligentRuleId"/>-->
        <result column="device_attribute_id" property="deviceAttributeId"/>
        <result column="expression" property="expression"/>
        <result column="calculate_sign" property="calculateSign"/>
        <result column="value" property="value"/>
        <result column="sort_no" property="sortNo"/>
        <result column="logic_code" property="logicCode"/>
    </resultMap>


    <delete id="deleteForceByIds">
        delete from linkapp_intelligent_rule_expression where id in
        <foreach collection="ids" item="arr" index="no" open="("
                 separator="," close=")">
            #{arr}
        </foreach>
    </delete>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into linkapp_intelligent_rule_expression
        (id, rule_trigger_id, delete_state, device_attribute_id,
        device_attribute_parent_id, calculate_sign, expression, `value`, sort_no, logic_code,
        rule_condition_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.ruleTriggerId,jdbcType=VARCHAR}, #{item.deleteState,jdbcType=INTEGER},
            #{item.deviceAttributeId,jdbcType=VARCHAR},
            #{item.deviceAttributeParentId,jdbcType=VARCHAR}, #{item.calculateSign,jdbcType=VARCHAR},
            #{item.expression,jdbcType=VARCHAR}, #{item.value,jdbcType=VARCHAR}, #{item.sortNo,jdbcType=INTEGER},
            #{item.logicCode,jdbcType=VARCHAR}, #{item.ruleConditionId,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>
