<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.isoftstone.linkappapi.intelligentrule.mapper.IntelligentRuleMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="getIntelligentRulesMap" type="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRule">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="space_id" property="spaceId"/>
<!--    <result column="type" property="type"/>-->
    <result column="common_rule_item" property="commonRuleItem"/>
<!--    <result column="time_scope_cron" property="timeScopeCron"/>-->
<!--    <result column="name" property="name"/>-->
<!--    <result column="description" property="description"/>-->
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
    <result column="spaceName" property="spaceName"/>
    <result column="creatorName" property="creatorName"/>
    <association property="deviceUnit" javaType="com.isoftstone.linkappapi.deviceunit.entity.DeviceUnit">
      <id column="un_id" property="id"/>
      <result column="un_name" property="name"/>
      <result column="un_code" property="code"/>
      <result column="device_type_id" property="deviceTypeId"/>
      <result column="device_type_name" property="deviceTypeName"/>
      <result column="version" property="version"/>
    </association>
  </resultMap>

  <select id="selectExpressionByRuleId" resultType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRuleExpression" parameterType="java.lang.String">
    SELECT
    e.id ,
    e.device_attribute_id ,
    e.calculate_sign ,
    e.value,
    e.logic_code ,
    e.sort_no,
    e.device_attribute_parent_id
    FROM linkapp_intelligent_rule_expression e
    WHERE e.delete_state = 1 AND
    e.Intelligent_rule_id = #{id}
    order by e.sort_no
  </select>


  <resultMap id="selectExpressionAndAttrByRuleIdMap" type="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    <id column="id" property="id"/>
    <result column="device_attribute_id" property="deviceAttributeId"/>
    <result column="calculate_sign" property="calculateSign"/>
    <result column="value" property="value"/>
    <result column="sort_no" property="sortNo"/>
    <result column="logic_code" property="logicCode"/>
    <association property="deviceAttribute" javaType="com.isoftstone.linkappapi.deviceattribute.entity.DeviceAttribute">
      <id column="att_id" property="id"/>
      <result column="att_device_unit_id" property="deviceUnitId"/>
      <result column="att_identifier" property="identifier"/>
      <result column="att_parent_prop_code" property="parentPropCode"/>
      <result column="att_parent_id" property="parentId"/>
    </association>
  </resultMap>
  <select id="selectExpressionAndAttrByRuleId" resultMap="selectExpressionAndAttrByRuleIdMap" parameterType="java.lang.String">
    SELECT
    e.id ,
    e.device_attribute_id ,
    e.calculate_sign ,
    e.value,
    e.logic_code ,
    e.sort_no,
    att.id as att_id,
    att.device_unit_id att_device_unit_id,
    att.parent_id as att_parent_id,
    att2.identifier as att_parent_prop_code,
    att.identifier att_identifier
    FROM linkapp_intelligent_rule_expression e
    LEFT JOIN linkapp_device_attribute att on att.id = e.device_attribute_id
    LEFT JOIN linkapp_device_attribute att2 on att.parent_id = att2.id
    WHERE e.delete_state = 1 AND
    e.Intelligent_rule_id = #{id}
    order by e.sort_no
  </select>

  <!-- 查询单条 -->
  <resultMap id="getIntelligentRuleMap" type="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRule">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
<!--    <result column="type" property="type"/>-->
<!--    <result column="time_scope_cron" property="timeScopeCron"/>-->
<!--    <result column="name" property="name"/>-->
<!--    <result column="description" property="description"/>-->
    <result column="common_rule_item" property="commonRuleItem"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="space_id" property="spaceId"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>

    <association property="deviceUnit" javaType="com.isoftstone.linkappapi.deviceunit.entity.DeviceUnit">
      <id column="un_id" property="id"/>
      <result column="un_name" property="name"/>
      <result column="un_code" property="code"/>
      <result column="version" property="version"/>
      <result column="device_type_name" property="deviceTypeName"/>
      <result column="device_type_id" property="deviceTypeId"/>
    </association>
    <!--   column = id id是要带过去的参数-->
    <collection property="intelligentRuleExpressionList" column="id" javaType="ArrayList" select="selectExpressionByRuleId" ofType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    </collection>
  </resultMap>


  <select id="getIntelligentRules" resultMap="getIntelligentRulesMap">
    SELECT
    ru.*,
    IFNULL(u.nickname,u.username) as creatorName,
    s.space_name as spaceName,
    un.id un_id,
    un.name un_name,
    un.code un_code,
    un.device_type_name,
    un.version,
    un.device_type_id
    FROM
    linkapp_intelligent_rule ru
    LEFT JOIN linkapp_device_unit un ON ru.device_unit_id = un.id AND un.delete_state = 1
    LEFT join linkapp_user u on u.id = ru.creator
    left join linkapp_space s on ru.space_id = s.id
    WHERE
    ru.delete_state = 1
<!--    <if test="intelligentRule.name!=null and intelligentRule.name!=''">-->
<!--      AND ru.name like CONCAT('%',#{intelligentRule.name},'%')-->
<!--    </if>-->
    <if test="intelligentRule.deviceUnitId!=null and intelligentRule.deviceUnitId!=''">
      AND ru.device_unit_id = #{intelligentRule.deviceUnitId}
    </if>
    <if test="intelligentRule.spaceId!=null and intelligentRule.spaceId!=''">
      AND ru.space_id = #{intelligentRule.spaceId}
    </if>
    ORDER BY ru.modify_time DESC
  </select>

  <select id="getIntelligentRule" resultMap="getIntelligentRuleMap">
    SELECT
    ru.*,
    un.id un_id,
    un.name un_name,
    un.version,
    un.code un_code
    FROM
    linkapp_intelligent_rule ru
    LEFT JOIN linkapp_device_unit un ON ru.device_unit_id = un.id AND un.delete_state = 1
    WHERE
    ru.delete_state = 1 AND ru.id = #{id}
  </select>


  <!-- 根据设备查询开启的告警规则关联 -->
  <resultMap id="intelligentRulesWithExpression" type="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRule">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="space_id" property="spaceId"/>
<!--    <result column="name" property="name"/>-->
<!--    <result column="type" property="type"/>-->
<!--    <result column="time_scope_cron" property="timeScopeCron"/>-->
<!--    <result column="description" property="description"/>-->
    <result column="common_rule_item" property="commonRuleItem"/>
    <!--   column = id id是要带过去的参数-->
    <collection property="intelligentRuleExpressionList" column="id" javaType="ArrayList" select="selectExpressionAndAttrByRuleId" ofType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    </collection>
  </resultMap>


  <!--  查询-->
  <select id="getIntelligentRulesAndExpressions" resultMap="intelligentRulesWithExpression">
    SELECT
    ru.*
    FROM
    linkapp_intelligent_rule ru
    <where>
      ru.delete_state = 1
      <if test='intelligentRule.id!=null and intelligentRule.id!=""'>
        and ru.id = #{intelligentRule.id}
      </if>
      <if test="intelligentRule.deviceUnitId!=null and intelligentRule.deviceUnitId!=''">
        and ru.device_unit_id = #{intelligentRule.deviceUnitId}
      </if>
    </where>
  </select>


  <select id="getIntelligentRulesRelateLinkageConfig" resultType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRule">
    select
    DISTINCT ru.id,
    ru.name
    FROM linkapp_intelligent_rule ru
    LEFT JOIN linkapp_linkage_config c on ru.id = c.intelligent_rule_id
    where ru.delete_state = 1
    and c.delete_state  = 1 and c.id is not null

  </select>


  <select id="getIntelligentRulesWithExpressionByConfigIds" resultMap="intelligentRulesWithExpression">
    SELECT
    ru.*
    FROM
    linkapp_intelligent_rule ru
    left join linkapp_linkage_config c on c.intelligent_rule_id = ru.id
    <where>
      ru.delete_state = 1 and c.delete_state = 1
      <if test="linkageConfigIds!=null and linkageConfigIds.size()>0">
        and c.id in
        <foreach collection="linkageConfigIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

</mapper>
