<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.machinery.mapper.MachineryDeviceRefMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.machinery.entity.MachineryDeviceRef">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="machinery_id" jdbcType="INTEGER" property="machineryId" />
    <result column="machinery_code" jdbcType="VARCHAR" property="machineryCode" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="selectMachineryDeviceBy" resultMap="BaseResultMap">
    select
      amdr.*
    from
      app_machinery_device_ref amdr
    where
      amdr.delete_state = 1
    <if test="machineryId != null">
      and amdr.machinery_id = #{machineryId}
    </if>
    <if test="deviceId != null and deviceId != ''">
      and amdr.device_id = #{deviceId}
    </if>
  </select>
</mapper>