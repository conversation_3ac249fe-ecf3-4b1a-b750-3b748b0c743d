<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.machinery.mapper.ElevatorFloorRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.machinery.entity.ElevatorFloorRecord">
        <id column="id" property="id" />
        <result column="machinery_id" property="machineryId" />
        <result column="device_code" property="deviceCode" />
        <result column="tenant_id_" property="tenantId" />
        <result column="open_time" property="openTime" />
        <result column="close_time" property="closeTime" />
        <result column="door_status" property="doorStatus" />
        <result column="height_" property="height" />
        <result column="building_id" property="buildingId" />
        <result column="building_name" property="buildingName" />
        <result column="floor_id" property="floorId" />
        <result column="floor_name" property="floorName" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="getStatisticsOrderByFloorTop" resultType="com.easylinkin.linkappapi.machinery.vo.ElevatorFloorStatisticsVO">
        SELECT
        record.`machinery_id`,
        ld.name AS deviceName,
        amr.name_ AS machineryName,
        count( * ) stayCount,
        building.name AS buildingName,
        floor.name AS floorName
        FROM
        `app_elevator_floor_record` record
        INNER JOIN app_machinery_record amr ON amr.id = record.machinery_id
        INNER JOIN linkapp_device ld ON ld.code = record.device_code and ld.delete_state = 1
        LEFT JOIN app_building building ON building.id = record.`building_id`
        LEFT JOIN app_floor floor ON floor.id = record.floor_id
        <where>
            door_status = 1
            <if test="tenantId != null and tenantId != ''">
                and record.tenant_id_ = #{tenantId}
            </if>
            <if test="startTime != null">
                and record.`create_time_` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and record.`create_time_` <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="machineryId != null">
                and record.`machinery_id` = #{machineryId}
            </if>
            <if test="code != null">
                and record.`device_code` = #{code}
            </if>
        </where>
        GROUP BY
          record.`building_id`,record.floor_id
        ORDER BY
          stayCount DESC
    </select>

</mapper>
