<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.machinery.mapper.MachineryUserTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.machinery.entity.MachineryUserType">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="type_" property="type" />
        <result column="name_" property="name" />
        <result column="devices_" property="devices" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

  <select id="findList" resultMap="BaseResultMap">
      SELECT
      *
    FROM
      app_machinery_user_type
    WHERE
      tenant_id_ = #{tenantId}
      <if test="name != null and name != ''">
      AND (
          (
          type_ = 2
          AND name_ LIKE CONCAT('%',#{name},'%'))
          or
          (type_ = 1 and SUBSTRING_INDEX(name_ ,'/',-1)  LIKE CONCAT('%',#{name},'%')))
      </if>
      ORDER BY modify_time_ DESC
  </select>

</mapper>
