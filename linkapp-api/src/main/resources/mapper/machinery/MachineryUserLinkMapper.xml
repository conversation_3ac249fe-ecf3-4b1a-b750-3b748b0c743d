<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.machinery.mapper.MachineryUserLinkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.machinery.entity.MachineryUserLink">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="type_id_" property="typeId" />
        <result column="user_id_" property="userId" />
        <result column="certificate_id_" property="certificateId" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

  <select id="queryListByPage"
    resultType="com.easylinkin.linkappapi.machinery.dto.MachineryUserLinkDTO">
      SELECT
      a.id,
      c.photo_ photo,
      c.name_ userName,
      b.work_type_ workType,
      c.gender_ gender,
      c.birthday_ birthday,
      a.certificate_id_ certificateId
    FROM
      app_machinery_user_link a
      LEFT JOIN app_user_project b ON a.user_id_ = b.user_id_
      AND a.tenant_id_ = b.tenant_id_
      LEFT JOIN emp_user_base c ON a.user_id_ = c.id
      WHERE a.type_id_ = #{machineryUserLink.typeId}
      <if test="machineryUserLink.userName != null and machineryUserLink.userName != ''">
          and c.name_ like  CONCAT('%',#{machineryUserLink.userName},'%')
      </if>
        ORDER BY a.modify_time_ DESC
  </select>
  <select id="getUser"
    resultType="com.easylinkin.linkappapi.machinery.dto.MachineryUserLinkDTO">
        SELECT
      a.id,
      c.photo_ photo,
      c.name_ userName,
      b.name_ typeName,
      b.type_ type
    FROM
      app_machinery_user_link a
      LEFT JOIN app_machinery_user_type b ON a.type_id_ = b.id
      LEFT JOIN emp_user_base c ON a.user_id_ = c.id
    WHERE
      b.devices_ LIKE CONCAT('%',#{code},'%')
      AND a.tenant_id_ = #{tenantId}
  </select>

</mapper>
