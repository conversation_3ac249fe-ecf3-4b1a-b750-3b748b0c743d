<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.machinery.mapper.MachineryRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.machinery.entity.MachineryRecord">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="type_code_" property="typeCode" />
        <result column="code_" property="code" />
        <result column="name_" property="name" />
        <result column="is_had_" property="isHad" />
        <result column="supplier_" property="supplier" />
        <result column="join_time_" property="joinTime" />
        <result column="model_" property="model" />
        <result column="manufactor_" property="manufactor" />
        <result column="manufactor_code_" property="manufactorCode" />
        <result column="init_code_" property="initCode" />
        <result column="manufactor_time_" property="manufactorTime" />
        <result column="is_special_" property="isSpecial" />
        <result column="parameter_" property="parameter" />
        <result column="building_id" property="buildingId"/>
        <result column="install_offset" property="installOffset"/>
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <resultMap id="MachineryDeviceDtoMap" type="com.easylinkin.linkappapi.machinery.dto.MachineryIotDeviceDto">
        <collection property="deviceList" column="{id=id,binded=binded}" select="com.easylinkin.linkappapi.device.mapper.DeviceMapper.selectDeviceMachineryDtoNoRefList"/>
    </resultMap>

    <select id="queryMachineryAndDeviceList" parameterType="com.easylinkin.linkappapi.machinery.vo.MachineryDeviceVo"
            resultMap="MachineryDeviceDtoMap">
        select distinct amr.*,
                        #{binded} as binded
        from app_machinery_record amr,
             app_machinery_device_ref amdr
        where amdr.delete_state = 1
          and amr.id = amdr.machinery_id
        <if test="typeCode != null and typeCode != ''">
            and amr.type_code_ = #{typeCode}
        </if>
        <if test="tenantId != null and tenantId != ''">
            and amr.tenant_id_ = #{tenantId}
        </if>
        <if test="device!= null and device.id != null and device.id != ''">
            and amdr.device_id = #{device.id}
        </if>
    </select>
    
    <select id="selectMachineryAndDeviceByConditionGlobal" resultType="com.easylinkin.linkappapi.machinery.dto.MachineryIotDeviceDto">
        SELECT
          amr.*,
          ld.CODE as deviceCode,
          ld.name as deviceName,
          amdr.create_time as deviceRefTime
        FROM
          app_machinery_device_ref amdr
          INNER JOIN app_machinery_record amr ON amr.id = amdr.machinery_id
          INNER JOIN linkapp_device ld ON ld.id = amdr.device_id
          LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
        WHERE
          amdr.delete_state = 1
          AND ld.delete_state = 1
        <if test="typeCode != null and typeCode != ''">
            AND amr.type_code_ = #{typeCode}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND amr.tenant_id_ = #{tenantId}
        </if>
        <if test="unitCodeList != null and unitCodeList.size() > 0">
            AND u.CODE IN
            <foreach collection="unitCodeList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="deviceTypeNameList != null and deviceTypeNameList.size() != 0">
            and u.device_type_name in
            <foreach collection="deviceTypeNameList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="onlineState != null ">
            AND ld.online_state = #{onlineState}
        </if>
        order by deviceRefTime
    </select>
    <select id="selectByTenantId" resultMap="BaseResultMap">
        SELECT * from app_machinery_record
        where tenant_id_ = #{tenantId}
    </select>
</mapper>
