<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.machinery.mapper.ElevatorWorkRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.machinery.entity.ElevatorWorkRecord">
        <id column="id" property="id" />
        <result column="machinery_id" property="machineryId" />
        <result column="device_code" property="deviceCode" />
        <result column="tenant_id_" property="tenantId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="driver_name" property="driverName" />
        <result column="max_weight" property="maxWeight" />
        <result column="weight_percentage" property="weightPercentage" />
        <result column="start_height" property="startHeight" />
        <result column="end_height" property="endHeight" />
        <result column="stroke_height" property="strokeHeight" />
        <result column="rise_direction" property="riseDirection" />
        <result column="average_speed" property="averageSpeed" />
        <result column="max_tilt_x" property="maxTiltX" />
        <result column="max_tilt_y" property="maxTiltY" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="getStatisticsOrderByMachineryGlobal" resultType="com.easylinkin.linkappapi.machinery.vo.ElevatorWorkStatisticsVO">
        SELECT
            DATE_FORMAT( record.`create_time`, '%Y%m%d' ) AS day,
            record.`machinery_id`,
            amr.name_ AS machineryName,
            ld.name AS deviceName,
            count( * ) workCount,
            SUM( max_weight ) weight
        FROM
            `app_elevator_work_record` record
            INNER JOIN linkapp_device ld ON ld.code = record.device_code and ld.delete_state = 1
            INNER JOIN app_machinery_record amr ON amr.id = record.machinery_id
            INNER JOIN app_machinery_device_ref amdr ON amr.id = amdr.machinery_id AND ld.id = amdr.device_id AND amdr.delete_state = 1
        <where>
            1=1
            <if test="tenantId != null and tenantId != ''">
                and record.tenant_id_ = #{tenantId}
            </if>
            <if test="machineryId != null">
                and record.`machinery_id` = #{machineryId}
            </if>
            <if test="code != null">
                and record.`device_code` = #{code}
            </if>
            <if test="startTime != null">
                and record.`create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and record.`create_time` <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="codeList != null and codeList.size() > 0">
                AND record.device_code IN
                <foreach collection="codeList" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="tenantIds != null and tenantIds.size() > 0">
                AND record.tenant_id_ IN
                <foreach collection="tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
        </where>
        GROUP BY
        record.`machinery_id`,
        record.device_code,
        DATE_FORMAT( record.`create_time`, '%Y%m%d' )
    </select>

    <select id="getStatisticsDetailByMachineryGlobal" resultType="com.easylinkin.linkappapi.machinery.vo.ElevatorWorkStatisticsVO">
        SELECT
        DATE_FORMAT( record.`create_time`, '%H' ) AS hour,
        record.`machinery_id`,
        amr.name_ AS machineryName,
        ld.name AS deviceName,
        min(record.`start_time`) AS firstStartTime,
        max(record.`end_time`) AS lastEndTime,
        SUM( convert( timestampdiff( SECOND, record.`start_time`, record.`end_time` ),decimal(15,2)) ) AS period,
        count( * ) workCount,
        SUM( max_weight ) weight
        FROM
        `app_elevator_work_record` record
        INNER JOIN linkapp_device ld ON ld.code = record.device_code and ld.delete_state = 1
        INNER JOIN app_machinery_record amr ON amr.id = record.machinery_id
        INNER JOIN app_machinery_device_ref amdr ON amr.id = amdr.machinery_id AND ld.id = amdr.device_id AND amdr.delete_state = 1
        <where>
            1=1
            <if test="tenantId != null and tenantId != ''">
                and record.tenant_id_ = #{tenantId}
            </if>
            <if test="machineryId != null">
                and record.`machinery_id` = #{machineryId}
            </if>
            <if test="code != null">
                and record.`device_code` = #{code}
            </if>
            <if test="startTime != null">
                and record.`create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and record.`create_time` <![CDATA[<=]]>#{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="codeList != null and codeList.size() > 0">
                AND record.device_code IN
                <foreach collection="codeList" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="tenantIds != null and tenantIds.size() > 0">
                AND record.tenant_id_ IN
                <foreach collection="tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
        </where>
        GROUP BY
        -- 新需求的要求是以设备的维度，假如设备已经在这个项目下的其他机械上使用过，那么这个设备的统计数据就要合并
--         record.`machinery_id`,
        record.device_code,
        DATE_FORMAT( record.`create_time`, '%H' )
    </select>

    <select id="getStatisticsSum" resultType="com.easylinkin.linkappapi.machinery.vo.ElevatorWorkStatisticsVO">
        SELECT
            record.`machinery_id`,
            count( * ) workCount,
            SUM( max_weight ) weight
        FROM
          `app_elevator_work_record` record
        <where>
            1=1
            <if test="tenantId != null and tenantId != ''">
                and record.tenant_id_ = #{tenantId}
            </if>
            <if test="startTime != null">
                and record.`create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and record.`create_time` <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="codeList != null and codeList.size() > 0">
                AND record.device_code IN
                <foreach collection="codeList" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getStatisticsOrderByMachineryTopGlobal" resultType="com.easylinkin.linkappapi.machinery.vo.ElevatorWorkStatisticsVO">
        SELECT
            record.`machinery_id`,
            amr.name_ AS machineryName,
            ld.name AS deviceName,
            count( * ) workCount,
            SUM( max_weight ) weight
        FROM
            `app_elevator_work_record` record
            INNER JOIN app_machinery_record amr ON amr.id = record.machinery_id
            INNER JOIN linkapp_device ld ON ld.code = record.device_code and ld.delete_state = 1
            INNER JOIN app_machinery_device_ref amdr ON amr.id = amdr.machinery_id and ld.id = amdr.device_id and amdr.delete_state = 1
        <where>
            1=1
            <if test="tenantId != null and tenantId != ''">
                and record.tenant_id_ = #{tenantId}
            </if>
            <if test="startTime != null">
                and record.`create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and record.`create_time` <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="codeList != null and codeList.size() > 0">
                AND record.device_code IN
                <foreach collection="codeList" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>
        GROUP BY
            record.`machinery_id`,record.device_code
        ORDER BY
        <if test="orderColumn != null">
            ${orderColumn} DESC
        </if>
    </select>

    <select id="find4Report" resultMap="BaseResultMap">
        SELECT
            a.*
        FROM
            app_elevator_work_record a
        WHERE
            a.tenant_id_ = #{tenantId}
          AND date_format( a.end_time, "%Y-%m" ) = date_format(#{time}, "%Y-%m" )
        ORDER BY a.end_time
    </select>

</mapper>
