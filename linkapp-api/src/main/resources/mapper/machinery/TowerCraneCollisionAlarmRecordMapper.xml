<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.machinery.mapper.TowerCraneCollisionAlarmRecordMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap"
    type="com.easylinkin.linkappapi.machinery.entity.TowerCraneCollisionAlarmRecord">
    <id column="id" property="id"/>
    <result column="machinery_id" property="machineryId"/>
    <result column="device_code" property="deviceCode"/>
    <result column="tenant_id_" property="tenantId"/>
    <result column="height" property="height"/>
    <result column="range_" property="range"/>
    <result column="rotation" property="rotation"/>
    <result column="crane_arm_height" property="craneArmHeight"/>
    <result column="crane_fore_arm_length" property="craneForeArmLength"/>
    <result column="crane_rear_arm_length" property="craneRearArmLength"/>
    <result column="driver_name1" property="driverName1"/>
    <result column="create_time" property="createTime"/>
    <result column="ref_record_id" property="refRecordId"/>
    <result column="machineryName" property="machineryName"/>
  </resultMap>

  <select id="getList" resultMap="BaseResultMap">
    SELECT record.*,amr.name_ AS machineryName FROM app_tower_crane_collision_alarm_record record
    INNER JOIN app_machinery_record amr ON amr.id = record.machinery_id
    WHERE record.ref_record_id is null
    <if test="tenantId != null and tenantId != ''">
      AND record.tenant_id_ = #{tenantId}
    </if>
    <if test="startTime != null">
      AND record.`create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      AND record.`create_time` <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
    </if>
    ORDER BY record.create_time DESC
  </select>

  <select id="getOneByRefRecordId" resultMap="BaseResultMap">
    SELECT record.*,amr.name_ AS machineryName FROM app_tower_crane_collision_alarm_record record
    INNER JOIN app_machinery_record amr ON amr.id = record.machinery_id
    WHERE record.ref_record_id = #{refRecordId}
    LIMIT 1
  </select>

  <select id="selectByMachinery" resultMap="BaseResultMap">
    SELECT record.*,amr.name_ AS machineryName FROM app_tower_crane_collision_alarm_record record
    INNER JOIN app_machinery_record amr ON amr.id = record.machinery_id
    <where>
      <if test="tenantId != null and tenantId != ''">
        AND record.tenant_id_ = #{tenantId}
      </if>
      <if test="machineryId != null">
        AND record.machinery_id = #{machineryId}
      </if>
      <if test="deviceCode != null">
        AND record.device_code = #{deviceCode}
      </if>
      <if test="startTime != null">
        AND record.`create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        AND record.`create_time` <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    ORDER BY record.create_time DESC
  </select>

  <select id="getCollisionAlarmStatisticsByDriveNameTop"
    resultType="com.easylinkin.linkappapi.machinery.vo.CollisionAlarmStatisticsVO">
    SELECT
    record.machinery_id AS machineryId,
    record.device_code AS deviceCode,
    record.driver_name1 AS driverName,
    count( * ) alarmCount,
    (
    SELECT
    count( * ) workCount
    FROM
    `app_tower_crane_work_record` record1
    WHERE
    1 = 1
    AND record1.driver_name1 = record.driver_name1
    AND record1.tenant_id_ = record.tenant_id_
    AND record1.machinery_id = record.machinery_id
    AND record1.device_code = record.device_code
    <if test="startTime != null">
      AND record1.`create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      AND record1.`create_time` <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
    </if>
    ) AS workCount,
    (
    count( * ) / (
    SELECT
    count( * )
    FROM
    app_tower_crane_work_record record1
    WHERE
    1 = 1
    AND record1.driver_name1 = record.driver_name1
    AND record1.tenant_id_ = record.tenant_id_
    AND record1.machinery_id = record.machinery_id
    AND record1.device_code = record.device_code
    <if test="startTime != null">
      AND record1.`create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      AND record1.`create_time` <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
    </if>
    )
    ) AS alarmRate
    FROM
    app_tower_crane_collision_alarm_record record
    <where>
      1=1
      <if test="tenantId != null and tenantId != ''">
        AND record.tenant_id_ = #{tenantId}
      </if>
      <if test="startTime != null">
        AND record.`create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        AND record.`create_time` <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    GROUP BY
    record.driver_name1
    ORDER BY
    alarmRate DESC
  </select>

</mapper>
