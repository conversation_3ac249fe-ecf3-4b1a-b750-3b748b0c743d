<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.concretestrength.mapper.ConcreteStrengthDetailMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.concretestrength.entity.ConcreteStrengthDetail">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="info_id" jdbcType="BIGINT" property="infoId"/>
        <result column="check_face" jdbcType="VARCHAR" property="checkFace"/>
        <result column="check_angle" jdbcType="VARCHAR" property="checkAngle"/>
        <result column="standard_diff" jdbcType="VARCHAR" property="standardDiff"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="check_area" jdbcType="VARCHAR" property="checkArea"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>
    <resultMap id="DetailDtoResultMap" type="com.easylinkin.linkappapi.concretestrength.dto.ConcreteStrengthDetailDTO">
        <collection property="recordList" column="id"
                    select="com.easylinkin.linkappapi.concretestrength.mapper.ConcreteStrengthRecordMapper.selectRecordListByDetailId"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_concrete_strength_detail
        where tenant_id = #{appConcreteStrengthDetail.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_concrete_strength_detail
        where id = #{id}
    </select>

    <select id="selectDtoListByDetail" resultMap="DetailDtoResultMap">
        select acsd.*
        from app_concrete_strength_detail acsd
        where acsd.info_id = #{infoId}
    </select>
</mapper>
