<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.concretestrength.mapper.ConcreteStrengthInfoMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.concretestrength.entity.ConcreteStrengthInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="position_id" jdbcType="INTEGER" property="positionId"/>
        <result column="check_area_num" jdbcType="INTEGER" property="checkAreaNum"/>
        <result column="check_device_code" jdbcType="VARCHAR" property="checkDeviceCode"/>
        <result column="member_code" jdbcType="VARCHAR" property="memberCode"/>
        <result column="member_type" jdbcType="VARCHAR" property="memberType"/>
        <result column="member_name" jdbcType="VARCHAR" property="memberName"/>
        <result column="age" jdbcType="VARCHAR" property="age"/>
        <result column="water_time" jdbcType="TIMESTAMP" property="waterTime"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="design_strength_id" jdbcType="INTEGER" property="designStrengthId"/>
        <result column="design_strength_code" jdbcType="VARCHAR" property="designStrengthCode"/>
        <result column="strength_presumption_val" jdbcType="VARCHAR" property="strengthPresumptionVal"/>
        <result column="strength_presumption_unit" jdbcType="VARCHAR" property="strengthPresumptionUnit"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>
    <resultMap id="SingInfoDtoResultMap" extends="BaseResultMap"
               type="com.easylinkin.linkappapi.concretestrength.dto.ConcreteStrengthInfoDTO">
        <association property="position" column="position_id"
                     select="com.easylinkin.linkappapi.quality.mapper.QualityPositionMapper.selectById"/>
        <association property="checkUser" column="creator"
                     select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectById"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_concrete_strength_info
        where tenant_id = #{appConcreteStrengthInfo.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_concrete_strength_info
        where id = #{id}
    </select>

    <select id="selectCountBy" resultType="int">
        select count(*)
        from app_concrete_strength_info acsi
        where acsi.delete_state = 1
        <if test="tenantId != null and tenantId != ''">
            and acsi.tenant_id = #{tenantId}
        </if>
        <if test="positionId != null">
            and acsi.position_id = #{positionId}
        </if>
    </select>

    <select id="selectInfoDtoList"
            parameterType="com.easylinkin.linkappapi.concretestrength.entity.vo.ConcreteStrengthInfoVo"
            resultMap="SingInfoDtoResultMap">
        select acsi.*
        from app_concrete_strength_info acsi
        where acsi.delete_state = 1
        <if test="concreteStrengthInfoVo.tenantId != null and concreteStrengthInfoVo.tenantId != ''">
            and acsi.tenant_id = #{concreteStrengthInfoVo.tenantId}
        </if>
        <if test="concreteStrengthInfoVo.status != null">
            and acsi.status = #{concreteStrengthInfoVo.status}
        </if>
        <if test="concreteStrengthInfoVo.startTime != null">
            <![CDATA[
            and acsi.check_time >= #{concreteStrengthInfoVo.startTime}
            ]]>
        </if>
        <if test="concreteStrengthInfoVo.endTime != null">
            <![CDATA[
            and acsi.check_time <= #{concreteStrengthInfoVo.endTime}
            ]]>
        </if>
        <if test="concreteStrengthInfoVo.positionId != null">
            and acsi.position_id in (select aqp.id
                                     from app_quality_position aqp
                                     where aqp.full_id_ like concat('%/',
                                                                    #{concreteStrengthInfoVo.positionId}, '/%'))
        </if>
        <if test="concreteStrengthInfoVo.paramKey != null and concreteStrengthInfoVo.paramKey != ''">
            and acsi.creator in (
            select lu.id  from linkapp_user lu where lu.nickname like concat('%',#{concreteStrengthInfoVo.paramKey},'%')
            )
        </if>
        order by acsi.check_time desc, acsi.create_time desc
    </select>

    <select id="selectInfoDto" resultMap="SingInfoDtoResultMap">
        select acsi.*
        from app_concrete_strength_info acsi
        where acsi.id = #{id}
    </select>
</mapper>
