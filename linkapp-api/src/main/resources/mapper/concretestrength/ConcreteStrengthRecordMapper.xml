<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.concretestrength.mapper.ConcreteStrengthRecordMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.concretestrength.entity.ConcreteStrengthRecord">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="info_id" jdbcType="BIGINT" property="infoId"/>
        <result column="detail_id" jdbcType="BIGINT" property="detailId"/>
        <result column="check_no" jdbcType="VARCHAR" property="checkNo"/>
        <result column="check_val" jdbcType="VARCHAR" property="checkVal"/>
        <result column="check_unit" jdbcType="VARCHAR" property="checkUnit"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="delete_state" jdbcType="INTEGER" property="deleteState"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_concrete_strength_record
        where tenant_id = #{appConcreteStrengthRecord.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_concrete_strength_record
        where id = #{id}
    </select>

    <select id="selectRecordListByDetailId" parameterType="long" resultMap="BaseResultMap">
        select acsr.*
        from app_concrete_strength_record acsr
        where acsr.delete_state = 1
          and acsr.detail_id = #{id}
    </select>
</mapper>
