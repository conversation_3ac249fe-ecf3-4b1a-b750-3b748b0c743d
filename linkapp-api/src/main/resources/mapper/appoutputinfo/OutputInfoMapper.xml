<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.outputinfo.dao.OutputInfoMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.outputinfo.entity.OutputInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="tenant_project_id" jdbcType="BIGINT" property="tenantProjectId" />
    <result column="fee_code" jdbcType="VARCHAR" property="feeCode" />
    <result column="fee_name" jdbcType="VARCHAR" property="feeName" />
    <result column="fee_tax_total" jdbcType="DECIMAL" property="feeTaxTotal" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="delete_state" jdbcType="INTEGER" property="deleteState" />
  </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_output_info where tenant_id = #{appOutputInfo.tenantId} order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_output_info where id = #{id}
    </select>


</mapper>
