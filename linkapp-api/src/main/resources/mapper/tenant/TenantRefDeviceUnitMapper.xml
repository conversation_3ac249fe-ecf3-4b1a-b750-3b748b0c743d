<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.tenant.mapper.TenantRefDeviceUnitMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.tenant.entity.TenantRefDeviceUnit">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="tenant_id" property="tenantId"/>
  </resultMap>

  <select id="findByTenantIdAndDeviceUnitCode" resultMap="BaseResultMap">
    select ref.* FROM linkapp_tenant_ref_device_unit ref left join linkapp_device_unit unit on unit.id = ref.device_unit_id
    where ref.tenant_id = #{tenantId} and unit.code = #{deviceUnitCode}
  </select>
</mapper>
