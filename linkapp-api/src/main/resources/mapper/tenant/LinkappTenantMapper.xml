<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.tenant.mapper.LinkappTenantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.tenant.entity.LinkappTenant">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_key" property="appKey" />
        <result column="app_type" property="appType" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="modifier" property="modifier" />
        <result column="modify_time" property="modifyTime" />
        <result column="personality_id" property="personalityId" />
        <result column="platform_project_name" property="platformProjectName" />
        <result column="platform_account" property="platformAccount" />
        <result column="platform_account_name" property="platformAccountName" />
        <result column="app_industry_type" property="appIndustryType" />
        <result column="telephone" property="telephone" />
        <result column="email" property="email" />
        <result column="project_type" property="projectType" />
        <result column="project_status" property="projectStatus" />
        <result column="actual_start_time" property="actualStartTime" />
        <result column="actual_completion_time" property="actualCompletionTime" />
        <result column="project_amount" property="projectAmount" />
        <result column="shigong_unit" property="shigongUnit" />
        <result column="design_unit" property="designUnit" />
        <result column="construction_control_unit" property="constructionControlUnit" />
        <result column="exploration_unit" property="explorationUnit" />
        <result column="project_area" property="projectArea" />
        <result column="quality_excellence_level" property="qualityExcellenceLevel" />
        <result column="safety_excellence_level" property="safetyExcellenceLevel" />
        <result column="project_archive_no" property="projectArchiveNo" />
        <result column="project_img_url" property="projectImgUrl" />
        <result column="project_video_url" property="projectVideoUrl" />
        <result column="project_short_name" property="projectShortName" />
        <result column="project_cross_type" property="projectCrossType" />
        <result column="project_confirm_time" property="projectConfirmTime" />
        <result column="project_init_confirm_time" property="projectInitConfirmTime" />
        <result column="project_design_confirm_time" property="projectDesignConfirmTime" />
        <result column="project_plan_duration" property="projectPlanDuration" />
        <result column="project_build_unit" property="projectBuildUnit" />
        <result column="project_shigong_unit_leader" property="projectShigongUnitLeader" />
        <result column="project_line" property="projectLine" />
        <result column="project_interval_distance" property="projectIntervalDistance" />
        <result column="project_progress" property="projectProgress" />
        <result column="project_transport" property="projectTransport" />
        <result column="project_problem" property="projectProblem" />
        <result column="project_paid_amount" property="projectPaidAmount" />
        <result column="project_contract_duration" property="projectContractDuration" />
        <result column="belong_area" property="belongArea" />
        <result column="surplus_day_upon_suspension" property="surplusDayUponSuspension" />
        <result column="progress_upon_suspension" property="progressUponSuspension" />
        <result column="status_change_time_upon_suspension" property="statusChangeTimeUponSuspension" />
        <result column="tenant_radius" property="tenantRadius" />
        <result column="switch_radius" property="switchRadius" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
    </resultMap>

    <resultMap id="BaseResultMapDTO" type="com.easylinkin.linkappapi.tenant.dto.LinkappTenantDTO" extends="BaseResultMap">
    </resultMap>

    <resultMap id="UserTenantResultMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.tenant.dto.LinkappTenantDTO">
        <association property="linkappUser" column="{tenantId=id,phone=phone}" select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectOneUserByTenantPhone"/>
    </resultMap>
    <resultMap id="UserTenantResultMap2" extends="BaseResultMap" type="com.easylinkin.linkappapi.tenant.dto.LinkappTenantDTO">
        <association property="linkappUser" column="{tenantId=id,username=username}" select="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper.selectOneUserByTenantPhone"/>
    </resultMap>
    <select id="selectLinkappTenant" resultMap="BaseResultMap"
            parameterType="com.easylinkin.linkappapi.tenant.entity.LinkappTenant">
       select
            *
       from linkapp_tenant
       <where>
       			and status = 1
       		<if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and tenant_id = #{tenantId}
            </if>
            <if test="appId != null and appId != ''">
                and app_id = #{appId}
            </if>
            <if test="appType != null and appType != ''">
                and app_type = #{appType}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
        </where>
    </select>

    <select id="selectLinkappTenantList" resultType="com.easylinkin.linkappapi.tenant.entity.LinkappTenant"
            parameterType="com.easylinkin.linkappapi.tenant.entity.LinkappTenant">
        SELECT
        b.username,
        b.nickname,
        b.locked status,
        c.name AS applicationName,
        c.id AS applicationId,
        a.*
        FROM
        linkapp_tenant a
        LEFT JOIN linkapp_user b ON b.tenant_id = a.id AND b.type = 1 AND a.status = 1
        LEFT JOIN linkapp_application c ON c.app_key = a.app_key AND c.delete_state = 1
        <where>
            <if test="tenant.id != null and tenant.id != ''">
                and a.id = #{tenant.id}
            </if>
            <if test="tenant.ids != null and tenant.ids.size()>0">
                and a.id in
                <foreach item="item" index="index" collection="tenant.ids" open="(" separator="," close=")">
                       #{item}
                </foreach>
            </if>
            <if test="tenant.applicationId != null and tenant.applicationId != ''">
                and c.id = #{tenant.applicationId}
            </if>
            <if test="tenant.username != null and tenant.username != ''">
              and b.username LIKE CONCAT('%', #{tenant.username}, '%')
            </if>
            <if test='tenant.status =="2"'>
                and b.locked = 1
            </if>
            <if test='tenant.status =="1"'>
                and b.locked = 0
            </if>
            <if test="tenant.platformProjectName != null and tenant.platformProjectName != ''">
                and a.platform_project_name LIKE CONCAT('%',#{tenant.platformProjectName},'%')
            </if>
            <if test="tenant.appIndustryType != null and tenant.appIndustryType != ''">
                and a.app_industry_type LIKE CONCAT('%',#{tenant.appIndustryType},'%')
            </if>
            <if test="tenant.platformAccount != null and tenant.platformAccount != ''">
                and a.platform_account LIKE CONCAT('%',#{tenant.platformAccount},'%')
            </if>
            <if test="tenant.platformAccountName != null and tenant.platformAccountName != ''">
                and a.platform_account_name LIKE CONCAT('%',#{tenant.platformAccountName},'%')
            </if>
            <if test="tenant.appId != null and tenant.appId != ''">
                and a.app_id = #{tenant.appId}
            </if>
            <if test="tenant.appType != null and tenant.appType != ''">
                and a.app_type = #{tenant.appType}
            </if>
            <if test="tenant.name != null and tenant.name != ''">
                and a.name = #{tenant.name}
            </if>
            <if test="tenant.code != null and tenant.code != ''">
                and a.code = #{tenant.code}
            </if>
            <if test="tenant.projectId != null  and tenant.projectId != ''">
                and a.project_id = #{tenant.projectId,jdbcType=VARCHAR}
            </if>
        </where>
      order by b.modify_time desc, b.create_time desc, a.modify_time desc
    </select>


    <resultMap id="linkappPrivilegeMap" type="com.easylinkin.linkappapi.security.entity.LinkappPrivilege">
        <id column="id_" property="id"/>
        <result column="parent_id_" property="parentId"/>
        <result column="name_" property="name"/>
        <result column="privilege_code_" property="code"/>
        <result column="description_" property="description"/>
        <result column="level_" property="level"/>
        <result column="target" property="target"/>
        <result column="search_code_" property="searchCode"/>
        <result column="seq_" property="sort"/>
        <result column="type_" property="type"/>
        <result column="url_" property="url"/>
        <result column="is_log_" property="isLog"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time_" property="createTime"/>
        <result column="creator_" property="creator"/>
        <result column="modifier_" property="modifier"/>
        <result column="modify_time_" property="modifyTime"/>
    </resultMap>

    <select id="selectPrivilegeByTenant"
            resultMap="linkappPrivilegeMap"
            parameterType="java.lang.String">
        SELECT a.* FROM linkapp_privilege a
        LEFT JOIN linkapp_tenant_ref_privilege b ON a.id_ = b.privilege_id
        LEFT JOIN linkapp_tenant c ON b.tenant_id = c.id
        <where>
            <if test="id != null">
                and c.id = #{id}
            </if>
            <if test="type == null or type == 0">
                and a.flag_=0
            </if>
            <if test="type == 1">
                and a.flag_=1
            </if>
            <if test="type == 2">
                and a.flag_=2
            </if>
        </where>
        order by a.seq_
    </select>

    <select id="selectTenantPrivilegeByUser" resultMap="linkappPrivilegeMap" >
        SELECT
		  DISTINCT a.*
		FROM
		  linkapp_privilege a
		  LEFT JOIN linkapp_tenant_ref_privilege b
		    ON a.id_ = b.privilege_id
		  LEFT JOIN linkapp_tenant c
		    ON b.tenant_id = c.id
		  LEFT JOIN linkapp_user d
		    ON c.id = d.tenant_id
        <where>
            a.type_ != 0
            <if test="id != null">
                and d.id = #{id}
            </if>
            <if test="type == null or type == 0">
                and a.flag_=0
            </if>
            <if test="type == 1">
                and a.flag_=1
            </if>
            <if test="type == 2">
                and a.flag_=2
            </if>
        </where>
        order by a.seq_
    </select>

    <select id="selectApplicationPrivilegeMenuByUser" resultMap="linkappPrivilegeMap" >
        SELECT
		  a.*
		FROM
		  linkapp_privilege a
		  LEFT JOIN linkapp_tenant_ref_privilege b
		    ON a.id_ = b.privilege_id
		  LEFT JOIN linkapp_tenant c
		    ON b.tenant_id = c.id
		  LEFT JOIN linkapp_user d
		    ON c.id = d.tenant_id
        <where>
            a.type_ != 0
            <if test="id != null">
                and d.id = #{id}
            </if>
            <if test="type == null or type == 0">
                and a.flag_=0
            </if>
            <if test="type == 1">
                and a.flag_=1
            </if>
            <if test="type == 2">
                and a.flag_=2
            </if>
            and a.type_ = 1
        </where>
        order by a.seq_
    </select>

    <select id="selectApplicationPrivilegeCustomByUser" resultMap="linkappPrivilegeMap" >
        select
            CASE
            WHEN t.c_name_ IS NOT NULL OR t.c_name_ != '' THEN t.name_
            ELSE t.name_
            END AS name_,
            CASE
            WHEN t.c_seq_ IS NOT NULL THEN t.c_seq_
            ELSE t.seq_
            END AS seq_,
            t.*
        from (
        SELECT
            a.*,
            a_.name_ as c_name_,
            a_.seq_ as c_seq_
        FROM
        linkapp_privilege a
        left join linkapp_privilege_custom a_ on a.id_ = a_.privilege_id_ and a_.tenant_id_ = #{tenantId}
        LEFT JOIN linkapp_tenant_ref_privilege b ON a.id_ = b.privilege_id
        LEFT JOIN linkapp_tenant c ON b.tenant_id = c.id
        LEFT JOIN linkapp_user d ON c.id = d.tenant_id
        <where>
            a.type_ != 0
            <if test="id != null">
                and d.id = #{id}
            </if>
            <if test="type == null or type == 0">
                and a.flag_=0
            </if>
            <if test="type == 1">
                and a.flag_=1
            </if>
            <if test="type == 2">
                and a.flag_=2
            </if>
        </where>

        union all

        SELECT
            a.*,
            a_.name_ as c_name_,
            a_.seq_ as c_seq_
        FROM
        linkapp_privilege a
        left join linkapp_privilege_custom a_ on a.id_ = a_.privilege_id_ and a_.tenant_id_ = #{tenantId}
        LEFT JOIN linkapp_tenant_ref_privilege b ON a.id_ = b.privilege_id
        <where>
            a.type_ = 0
            and b.tenant_id =  #{tenantId}
            <if test="type == null or type == 0">
                and a.flag_=0
            </if>
            <if test="type == 1">
                and a.flag_=1
            </if>
            <if test="type == 2">
                and a.flag_=2
            </if>
        </where>

        ) t

        order by
            CASE
            WHEN t.c_seq_ IS NOT NULL THEN t.c_seq_
            ELSE t.seq_
            END
    </select>

    <delete id="deleteTenant2Privileges">
		DELETE FROM
		    linkapp_tenant_ref_privilege
		WHERE
            tenant_id = #{id}
        <if test="type == null or type == 0">
            and exists ( select 1 from linkapp_privilege p where privilege_id=p.id_ and p.flag_=0)
        </if>
        <if test="type == 1">
            and exists ( select 1 from linkapp_privilege p where privilege_id=p.id_ and p.flag_=1)
        </if>
        <if test="type == 2">
            and exists ( select 1 from linkapp_privilege p where privilege_id=p.id_ and p.flag_=2)
        </if>
	</delete>

    <insert id="insertTenant2Privileges">
        insert into linkapp_tenant_ref_privilege
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="privilegeId != null">
                privilege_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="privilegeId != null">
                #{privilegeId}
            </if>
        </trim>
    </insert>

    <resultMap id="deviceUnitMap" type="com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="name" property="name"/>
        <result column="device_type_id" property="deviceTypeId"/>
        <result column="code" property="code"/>
        <result column="remark" property="remark"/>
        <result column="icon" property="icon"/>
        <result column="identification" property="identification"/>
        <result column="device_life" property="deviceLife"/>
        <result column="repair_cycle" property="repairCycle"/>
        <result column="offline_time" property="offlineTime"/>
    </resultMap>

    <select id="selectTenantDeviceUnitByUser" resultMap="deviceUnitMap" parameterType="java.lang.String">
        SELECT a.* FROM linkapp_device_unit a
        LEFT JOIN linkapp_tenant_ref_device_unit b ON a.id = b.device_unit_id AND a.delete_state = 1
        LEFT JOIN linkapp_tenant c ON c.id = b.tenant_id
        LEFT JOIN linkapp_user d ON c.id = d.tenant_id
        <where>
            <if test="id != null">
                and d.id = #{id}
            </if>
        </where>
    </select>


    <select id="selectDeviceUnitByTenant" resultMap="deviceUnitMap"
            parameterType="java.lang.String">
        SELECT a.* FROM linkapp_device_unit a
        LEFT JOIN linkapp_tenant_ref_device_unit b ON a.id = b.device_unit_id
        LEFT JOIN linkapp_tenant c ON b.tenant_id = c.id
        <where>
            <if test="id != null">
                and c.id = #{id}
            </if>
        </where>
    </select>
    <select id="selectByProjectId" resultMap="BaseResultMap">
        select
        *
        from linkapp_tenant
        <where>
            <if test="projectId != null and projectId != ''">
                and project_id = #{projectId}
            </if>
        </where>
    </select>

    <select id="selectByProjectIds" resultMap="BaseResultMap">
        select *
        from linkapp_tenant
        <where>
            <if test="projectIds != null and projectIds.size() != 0">
                and project_id in
                <foreach collection="projectIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <delete id="deleteTenant2DeviceUnits">
		DELETE FROM linkapp_tenant_ref_device_unit WHERE tenant_id = #{id}
	</delete>

    <insert id="insertTenant2DeviceUnits">
        insert into linkapp_tenant_ref_device_unit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="deviceUnitId != null">
                device_unit_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="deviceUnitId != null">
                #{deviceUnitId}
            </if>
        </trim>
    </insert>

    <delete id="deleteRole2Privileges">
		DELETE FROM
		    linkapp_role_ref_privilege
		WHERE
            privilege_id_ = #{privilegeId}
		    and tenant_id = #{tenantId}
	</delete>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into linkapp_tenant_ref_privilege
        (id, tenant_id, privilege_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.tenantId,jdbcType=VARCHAR}, #{item.privilegeId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

  <select id="selectUserTenantByPhone" parameterType="string" resultMap="UserTenantResultMap">
    select distinct lt.*,lu.phone
      from linkapp_tenant lt,
         linkapp_user lu
    where lt.id = lu.tenant_id
      and lu.delete_state = 1
      and lu.locked = 0
    <if test="phone != null and phone != ''">
      and lu.phone = #{phone}
    </if>
  </select>

    <select id="selectUserTenantByUser" resultMap="UserTenantResultMap2">
        select distinct lt.*,lu.phone,lu.username
        from linkapp_tenant lt,
        linkapp_user lu
        where lt.id = lu.tenant_id
        and lu.delete_state = 1
        <if test="username != null and username != ''">
            and lu.username = #{username}
        </if>
        <if test="tenantId != null and tenantId != ''">
            and lu.tenant_id = #{tenantId}
        </if>
        <if test="locked != null">
            and lu.locked = #{locked}
        </if>
    </select>

    <select id="getLastHasConfigOne" resultMap="BaseResultMap">
        select lt.* from app_config ac,linkapp_tenant lt where ac.tenant_id = lt.id order by ac.create_time desc limit 1
    </select>

    <select id="getById" parameterType="String"  resultMap="BaseResultMap">
        select
            *
        from
            linkapp_tenant
        where
            id = #{tenantId}
    </select>

    <select id="getOneByProjectId" parameterType="long"  resultMap="BaseResultMap">
        select
            *
        from
            linkapp_tenant
        where
            project_id = #{projectId}
    </select>

    <select id="selectByName" resultMap="BaseResultMapDTO">
        select
        *
        from linkapp_tenant
        <where>
            id != '1'
            <if test="queryParams.projectName != null and queryParams.projectName != ''">
                and platform_project_name like  CONCAT('%',#{queryParams.projectName},'%')
            </if>
            <if test="queryParams.id != null and queryParams.id != ''">
                and id = #{queryParams.id}
            </if>
            <if test="queryParams.tenantIds !=null and queryParams.tenantIds.size>0">
                and id in
                <foreach collection="queryParams.tenantIds" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="queryParams.projectIds !=null and queryParams.projectIds.size>0">
                and project_id in
                <foreach collection="queryParams.projectIds" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY project_amount DESC
    </select>

    <select id="getDashboardUrlByProjectIds" resultType="com.easylinkin.linkappapi.openapi.dto.ProjectRefDashboard">
        select lt.id            as tenantId,
               lt.project_id    as projectId,
               ld.dashboard_url as dashboardUrl,
               ld.milestone_setting as milestoneSetting
        from linkapp_tenant lt
                 inner join linkapp_dashboard ld on lt.id = ld.tenant_id
        where ld.delete_state = 1
        <if test="ids != null and ids.size() != 0">
            and lt.project_id in
            <foreach collection="ids" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select * from linkapp_tenant
    </select>

    <select id="selectProjectNamesByTenantIds" resultMap="BaseResultMap">
        select id, platform_project_name
        from linkapp_tenant
        <where>
            <if test="tenantIds != null and tenantIds.size() != 0">
                and id in
                <foreach collection="tenantIds" item="item" close=")" separator="," open="(">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>


</mapper>
