<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridSecurityCheckItemMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.grid.entity.GridSecurityCheckItem">
        <id property="id" column="id_" />
        <result property="parentId" column="parent_id_" />
        <result property="sort" column="sort_" />
        <result property="name" column="name_" />
        <result property="createId" column="create_id_" />
        <result property="createTime" column="create_time_" />
        <result property="modifyId" column="modify_id_" />
        <result property="modifyTime" column="modify_time_" />
        <result property="remark" column="remark_" />
    </resultMap>

    <resultMap id="GridSecurityCheckItemDTOResultMap" type="com.easylinkin.linkappapi.grid.models.dto.GridSecurityCheckItemDTO" >
        <id property="id" column="id_" />
        <result property="name" column="name_" />
        <collection property="checkChildItems" column="id_" ofType="com.easylinkin.linkappapi.grid.entity.GridSecurityCheckItem" select="selectGridSecurityCheckItemByParentId">
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id_, parent_id_, sort_, name_, create_id_, create_time_, modify_id_, modify_time_, remark_
    </sql>

    <select id="selectGridSecurityCheckItemByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rail_grid_security_check_item
        WHERE parent_id_ = #{parentId}
        ORDER BY sort_ ASC
    </select>

    <select id="queryAllCheckItems" resultMap="GridSecurityCheckItemDTOResultMap">
        SELECT id_, name_
        FROM rail_grid_security_check_item
        WHERE parent_id_ IS NULL
        ORDER BY sort_ ASC
    </select>
</mapper>