<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridAppraiseMapper">

    <select id="selectCountByDate" resultType="java.lang.Integer">
        select count(id) from grid_appraise where create_time like concat('%', #{date}, '%')
        and tenant_id = #{tenantId}
    </select>
</mapper>