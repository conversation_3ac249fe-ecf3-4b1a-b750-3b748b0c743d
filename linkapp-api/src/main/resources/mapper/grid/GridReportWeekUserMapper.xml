<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridReportWeekUserMapper">

    <select id="selectListByReportId"
            resultType="com.easylinkin.linkappapi.grid.models.vo.GridReportWeekUserInfoVO">
        SELECT * FROM grid_report_week_user
        WHERE delete_state = 1
          AND report_id = #{reportId}
    </select>
</mapper>