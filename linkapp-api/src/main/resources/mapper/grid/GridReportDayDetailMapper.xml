<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridReportDayDetailMapper">

    <select id="selectListByQuery"
            resultType="com.easylinkin.linkappapi.grid.models.vo.GridReportDayDetailInfoVO">
        SELECT * FROM grid_report_day_detail
        WHERE delete_state = 1
            AND report_id = #{query.reportId}
            <if test="query.reportUserId != null">
                AND report_user_id = #{query.reportUserId}
            </if>
            <if test="query.userId != null">
                AND user_id = #{query.userId}
            </if>
    </select>

    <select id="selectListByWeekQuery"
            resultType="com.easylinkin.linkappapi.grid.models.vo.GridReportDayDetailInfoVO">
        SELECT * FROM grid_report_day_detail a LEFT JOIN grid_report_day b ON a.report_id = b.id
        WHERE a.delete_state = 1 AND b.delete_state = 1
            AND a.tenant_id = #{query.tenantId}
            AND b.submit_deadline >= #{query.start}
            AND b.submit_deadline &lt;= #{query.end}
    </select>

</mapper>