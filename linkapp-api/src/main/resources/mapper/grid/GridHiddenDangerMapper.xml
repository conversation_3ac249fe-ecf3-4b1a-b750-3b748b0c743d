<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridHiddenDangerMapper">

    <select id="pageList" resultType="com.easylinkin.linkappapi.grid.models.vo.GridHiddenDangerListVO">
        SELECT * FROM app_hidden_danger
        <where>
            tenant_id = #{query.operator.tenantId}
            AND grid_id IS NOT NULL
            <if test="query.gridId != null">
                AND grid_id = #{query.gridId}
            </if>
            <if test="query.createTime != null">
                AND create_time = #{query.createTime}
            </if>
            <if test="query.rectifyRequirements != null and query.rectifyRequirements != ''">
                AND rectify_requirements = #{query.rectifyRequirements}
            </if>
            <if test="query.status != null">
                AND `status` = #{query.status}
            </if>
        </where>
    </select>
</mapper>