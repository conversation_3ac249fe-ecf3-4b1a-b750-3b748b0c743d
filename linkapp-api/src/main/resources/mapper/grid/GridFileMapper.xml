<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridFileMapper">

    <sql id="where_query">
        WHERE bus_id = #{query.busId}
        AND bus_type = #{query.busType}
        <choose>
            <when test="query.busSubType != null">
                AND bus_sub_type = #{query.busSubType}
            </when>
            <otherwise>
                AND bus_sub_type = 0
            </otherwise>
        </choose>
    </sql>

    <select id="listInfoByQuery" resultType="com.easylinkin.linkappapi.grid.models.vo.GridFileInfoVO">
        SELECT * FROM grid_file
        <include refid="where_query"/>
    </select>

    <select id="countByQuery" resultType="java.lang.Long">
        SELECT COUNT(1) FROM grid_file
        <include refid="where_query"/>
    </select>

    <select id="listInfoByBatchQuery" resultType="com.easylinkin.linkappapi.grid.models.vo.GridFileInfoVO">
        SELECT * FROM grid_file
        WHERE bus_id IN
        <foreach collection="query.busIds" item="busId" open="(" close=")" separator=",">
            #{busId}
        </foreach>
        <if test="query.busType != null">
            AND bus_type = #{query.busType}
        </if>
        <if test="query.busSubType != null">
            AND bus_sub_type = #{query.busSubType}
        </if>
    </select>

</mapper>