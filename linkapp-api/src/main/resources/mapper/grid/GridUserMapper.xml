<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridUserMapper">

    <select id="queryGridUserSelect" resultType="com.easylinkin.linkappapi.grid.models.vo.GridUserSelectVO">
        select
            gu.id,
            gu.grid_id,
            gu.grid_role_name,
            gu.user_id,
            pu.user_name_ as userName_
        from grid_user gu
        inner join app_user_project pu
        on pu.user_id_ = gu.user_id
        where
            pu.status_ = 1
          and pu.delete_state_ = 1
          and pu.tenant_id_= #{tenantId}
        <if test="gridId != null">
            and gu.grid_id = #{gridId}
        </if>
#         GROUP BY gu.user_id;
    </select>
</mapper>