<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridReportDayUserMapper">

    <select id="selectListByReportId"
            resultType="com.easylinkin.linkappapi.grid.models.vo.GridReportDayUserInfoVO">
        SELECT a.*, b.grid_name
        FROM grid_report_day_user a
        LEFT JOIN grid_info b ON a.grid_id = b.id
        WHERE a.delete_state = 1
        AND a.report_id = #{reportId}
    </select>
</mapper>