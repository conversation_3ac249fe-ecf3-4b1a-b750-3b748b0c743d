<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridReportWeekMapper">

    <select id="pageList" resultType="com.easylinkin.linkappapi.grid.models.vo.GridReportWeekListVO">
        SELECT * FROM grid_report_week
        WHERE delete_state = 1
            AND tenant_id = #{query.operator.tenantId}
        <if test="query.reportName != null and query.reportName != ''">
            AND report_name LIKE CONCAT('%',#{query.reportName},'%')
        </if>
        <if test="query.submitDeadline != null">
            AND submit_deadline >= DATE_FORMAT('%Y-%m-%d 00:00:00', #{query.submitDeadline})
            AND submit_deadline &lt;= DATE_FORMAT('%Y-%m-%d 23:59:59', #{query.submitDeadline})
        </if>
    </select>

    <select id="listByQuery" resultType="com.easylinkin.linkappapi.grid.models.vo.GridReportWeekListVO">
        SELECT * FROM grid_report_week
        WHERE delete_state = 1
            AND tenant_id = #{query.operator.tenantId}
        <if test="query.reportName != null and query.reportName != ''">
            AND report_name LIKE CONCAT('%',#{query.reportName},'%')
        </if>
        <if test="query.submitDeadline != null">
            AND submit_deadline >= DATE_FORMAT('%Y-%m-%d 00:00:00', #{query.submitDeadline})
            AND submit_deadline &lt;= DATE_FORMAT('%Y-%m-%d 23:59:59', #{query.submitDeadline})
        </if>
    </select>

    <select id="selectInfoById" resultType="com.easylinkin.linkappapi.grid.models.vo.GridReportWeekInfoVO">
        SELECT * FROM grid_report_week
        WHERE delete_state = 1
          AND id = #{id}
    </select>

    <select id="existByDate" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM grid_report_week
        WHERE delete_state = 1
          AND tenant_id = #{tenantId}
          AND week_start >= DATE_FORMAT('%Y-%m-%d 00:00:00', #{submitDeadline})
          AND week_end &lt;= DATE_FORMAT('%Y-%m-%d 23:59:59', #{submitDeadline})
    </select>

</mapper>