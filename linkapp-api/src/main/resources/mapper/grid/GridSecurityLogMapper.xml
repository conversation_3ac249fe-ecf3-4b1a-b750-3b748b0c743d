<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.grid.mapper.GridSecurityLogMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.grid.entity.GridSecurityLog">
        <id property="id" column="id_" />
        <result property="tenantId" column="tenant_id_" />
        <result property="gridId" column="grid_id_" />
        <result property="userId" column="user_id_" />
        <result property="userName" column="user_name_" />
        <result property="userSign" column="user_sign_" />
        <result property="logTime" column="log_time_" />
        <result property="logFiles" column="log_files_" />
        <result property="deleteState" column="delete_state_" />
        <result property="createId" column="create_id_" />
        <result property="createTime" column="create_time_" />
        <result property="modifyId" column="modify_id_" />
        <result property="modifyTime" column="modify_time_" />
        <result property="remark" column="remark_" />
        <result property="gridName" column="grid_name" />
    </resultMap>

    <sql id="Base_Column_List">
        a.id_,
        a.tenant_id_,
        a.grid_id_,
        a.user_id_,
        a.user_name_,
        a.user_sign_,
        a.log_time_,
        a.log_files_,
        a.delete_state_,
        a.create_id_,
        a.create_time_,
        a.modify_id_,
        a.modify_time_,
        a.remark_,
        b.grid_name
    </sql>

    <select id="queryPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rail_grid_security_log a
        LEFT JOIN rail_linkapp_grid_management_info b ON a.grid_id_ = b.id
        WHERE a.delete_state_ = 0
        AND a.tenant_id_ = #{customQueryParams.tenantId}
        <if test="customQueryParams.gridId!= null">
            AND a.grid_id_ = #{customQueryParams.gridId}
        </if>
        <if test="customQueryParams.userId!= null">
            AND a.user_id_ = #{customQueryParams.userId}
        </if>
        <if test="customQueryParams.startTime!= null">
            AND a.log_time_ &gt;= #{customQueryParams.startTime}
        </if>
        <if test="customQueryParams.endTime!= null">
            AND a.log_time_ &lt;= #{customQueryParams.endTime}
        </if>
        ORDER BY a.log_time_ DESC
    </select>

    <select id="queryById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rail_grid_security_log a
        LEFT JOIN rail_linkapp_grid_management_info b ON a.grid_id_ = b.id
        WHERE a.delete_state_ = 0
        AND a.id_ = #{id}
    </select>

    <select id="selectNoLogDays" resultType="java.lang.String">

        SELECT DATE_ADD(#{assessMonth}, INTERVAL seq DAY) AS missingDate
        FROM (
        SELECT 0 AS seq UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
        SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
        SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION
        SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
        SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION
        SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION
        SELECT 30
        ) AS numbers
        WHERE DATE_ADD(#{assessMonth}, INTERVAL seq DAY) &lt;= LAST_DAY(#{assessMonth})
        AND NOT EXISTS (
        SELECT * FROM (
        SELECT DATE(create_time_) AS record_date FROM rail_grid_security_log WHERE grid_id_ = #{gridId} and tenant_id_ = #{tenantId}

        ) AS records
        WHERE records.record_date = DATE_ADD(#{assessMonth}, INTERVAL seq DAY)
        )
        ORDER BY missingDate
    </select>
</mapper>