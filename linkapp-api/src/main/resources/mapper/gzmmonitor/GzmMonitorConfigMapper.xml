<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.gzmmonitor.mapper.GzmMonitorConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.gzmmonitor.entity.GzmMonitorConfig">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="gzm_overview" property="gzmOverview" />
        <result column="gzm_url_" property="gzmUrl" />
        <result column="creator" property="creator" />
        <result column="modifier" property="modifier" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_state" property="deleteState" />
    </resultMap>

</mapper>
