<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.gzmmonitor.mapper.GzmMonitorItemConfigMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap"
    type="com.easylinkin.linkappapi.gzmmonitor.entity.GzmMonitorItemConfig">
    <id column="id" property="id"/>
    <result column="device_code" property="deviceCode"/>
    <result column="tenant_id_" property="tenantId"/>
    <result column="x_axis" property="xaxis"/>
    <result column="y_axis" property="yaxis"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
  </resultMap>

  <resultMap id="BaseResultMapVO"
    type="com.easylinkin.linkappapi.gzmmonitor.vo.GzmMonitorItemConfigVO" extends="BaseResultMap">
    <result column="deviceName" property="deviceName"/>
    <result column="deviceUnitVersion" property="deviceUnitVersion"/>
    <result column="deviceId" property="deviceId"/>
  </resultMap>

  <select id="selectAllByTenantId" resultMap="BaseResultMapVO">
    select agmic.*, ld.id as deviceId ,ld.name as deviceName, u.version as deviceUnitVersion
    from app_gzm_monitor_item_config agmic
           left join linkapp_device ld on ld.code = agmic.device_code
           left join linkapp_device_unit u on ld.device_unit_id = u.id
    where agmic.tenant_id_ = #{tenantId}
      and agmic.delete_state = 1
      and ld.delete_state = 1
  </select>
</mapper>
