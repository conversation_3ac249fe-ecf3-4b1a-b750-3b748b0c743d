<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.webcammanage.mapper.DataAnalystMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.webcammanage.entity.DataAnalystBean">
        <id column="id" property="id"/>
        <result column="remarks" property="remarks"/>
        <result column="status" property="status"/>
        <result column="dev_status" property="devStatus"/>
        <result column="feedstock_time" property="feedstockTime"/>
        <result column="press_time" property="pressTime"/>
        <result column="paini_num" property="painiNum"/>
        <result column="jiayao_num" property="jiayaoNum"/>
        <result column="jinyao_num" property="jinyaoNum"/>
        <result column="location" property="location"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectDataAnalystPage" resultMap="BaseResultMap" >
        select
            id,
            remarks,
            status,
            dev_status,
            feedstock_time,
            press_time,
            paini_num,
            jiayao_num,
            jinyao_num,
            location,
            create_time,
            update_time
        from
            linkapp_data_analyst
        <where>
            status = 1
            <if test="record.devStatus != null">
                and dev_status = #{record.devStatus}
            </if>
            <if test="record.createTimeStart != null">
                   and create_time &gt;= #{record.createTimeStart}
           </if>
           <if test="record.createTimeEnd != null">
                   and create_time &lt;= #{record.createTimeEnd}
           </if>
        </where>
        order by create_time desc
    </select>

    <insert id="insertDataAnalystRecord">
        insert into linkapp_data_analyst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="devStatus != null">
                dev_status,
            </if>
            <if test="feedstockTime != null">
                feedstock_time,
            </if>
            <if test="pressTime != null">
                press_time,
            </if>
            <if test="painiNum != null">
                paini_num,
            </if>
            <if test="jiayaoNum != null">
                jiayao_num,
            </if>
            <if test="jinyaoNum != null">
                jinyao_num,
            </if>
            <if test="location != null">
                location,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="remarks != null">
                #{remarks},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="devStatus != null">
                #{devStatus},
            </if>
            <if test="feedstockTime != null">
                #{feedstockTime},
            </if>
            <if test="pressTime != null">
                #{pressTime},
            </if>
            <if test="painiNum != null">
                #{painiNum},
            </if>
            <if test="jiayaoNum != null">
                #{jiayaoNum},
            </if>
            <if test="jinyaoNum != null">
                #{jinyaoNum},
            </if>
            <if test="location != null">
                #{location},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <update id="updateDataAnalystRecord" >
        update
            linkapp_data_analyst
        <set>
            <if test="remarks != null">
                remarks = #{remarks},
            </if>
            <if test="devStatus != null">
                dev_status = #{devStatus},
            </if>
            <if test="feedstockTime != null">
                feedstock_time = #{feedstockTime},
            </if>
            <if test="pressTime != null">
                press_time = #{pressTime},
            </if>
            <if test="painiNum != null">
                paini_num = #{painiNum},
            </if>
            <if test="jiayaoNum != null">
                jiayao_num = #{jiayaoNum},
            </if>
            <if test="jinyaoNum != null">
                jinyao_num = #{jinyaoNum},
            </if>
            <if test="location != null">
                location = #{location},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateTime == null">
                update_time = now(),
            </if>
        </set>
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
        </where>
    </update>

    <update id="deleteDataAnalystRecord">
        update
            linkapp_data_analyst
        set
            status=2,
            update_time = now()
        where id=#{id}
    </update>


</mapper>
