<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.webcammanage.mapper.DeviceAreaHandlerMapper">

  <!-- 批量解绑设备ID和区域ID关联 -->
  <update id="unBindDevice">
    <foreach collection="idList" item="id" index="index" open="" close="" separator=";">
      UPDATE
        linkapp_device
      <set>
        area_id = null,
        area_path = null
      </set>
      <where>
        <if test="id != null">
          and id = #{id}
        </if>
      </where>
    </foreach>
  </update>

  <!-- 批量绑定设备ID和区域ID关联 -->
  <update id="bindDevice">
    <foreach collection="deviceList" item="device" index="index" open="" close="" separator=";">
      UPDATE
      linkapp_device
      <set>
        <if test="device.areaId != null">
          area_id = #{device.areaId},
        </if>
        <if test="device.areaPath != null">
          area_path = #{device.areaPath},
        </if>
      </set>
      <where>
        <if test="device.id != null">
          and id = #{device.id}
        </if>
      </where>
    </foreach>
  </update>

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.device.entity.Device">
    <id column="id" property="id"/>
    <result column="code" property="code"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="delete_state" property="deleteState"/>
    <result column="online_state" property="onlineState"/>
    <result column="battery" property="battery"/>
    <result column="remark" property="remark"/>
    <result column="indoor_location" property="indoorLocation"/>
    <result column="alarm_switch" property="alarmSwitch"/>
    <result column="company_id" property="companyId"/>
    <result column="project_id" property="projectId"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="install_time" property="installTime"/>
    <result column="repair_time" property="repairTime"/>
    <result column="next_repair_time" property="nextRepairTime"/>
    <result column="last_push_time" property="lastPushTime"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="area_id" property="areaId"/>
    <result column="latitude" property="latitude"/>
    <result column="longitude" property="longitude"/>
    <result column="site" property="site"/>
    <result column="area_path" property="areaPath"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="linkthing_delete" property="linkthingDelete"/>
  </resultMap>



  <resultMap id="selectDevicesMap" type="com.easylinkin.linkappapi.device.entity.Device">
    <id column="id" property="id"/>
    <result column="code" property="code"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="delete_state" property="deleteState"/>
    <result column="linkthing_delete" property="linkthingDelete"/>
    <result column="online_state" property="onlineState"/>
    <result column="remark" property="remark"/>
    <result column="indoor_location" property="indoorLocation"/>
    <result column="alarm_switch" property="alarmSwitch"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="install_time" property="installTime"/>
    <result column="repair_time" property="repairTime"/>
    <result column="next_repair_time" property="nextRepairTime"/>
    <result column="last_push_time" property="lastPushTime"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="area_id" property="areaId"/>
    <result column="latitude" property="latitude"/>
    <result column="longitude" property="longitude"/>
    <result column="site" property="site"/>
    <result column="area_path" property="areaPath"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="spaceName" property="spaceName"/>
    <result column="deviceTypeName" property="deviceTypeName"/>
    <result column="areaName" property="areaName"/>
    <result column="deviceUnitName" property="deviceUnitName"/>
    <result column="deviceUnitCode" property="deviceUnitCode"/>
    <result column="deviceUnitVersion" property="deviceUnitVersion"/>
    <result column="spaceId" property="spaceId"/>
    <result column="deviceTypeId" property="deviceTypeId"/>
    <association property="deviceType" javaType="com.easylinkin.linkappapi.devicetype.entity.DeviceType">
      <id column="ldt_id" property="id"/>
      <result property="icoPath" column="ldt_ico_path"/>
    </association>
    <association property="deviceUnit" javaType="com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit">
      <result property="deviceTypeName" column="deviceTypeName"/>
      <result property="name" column="deviceUnitName"/>
      <result property="code" column="deviceUnitCode"/>
      <result property="deviceTypeId" column="deviceTypeId"/>
      <result property="version" column="deviceUnitVersion"/>
    </association>
  </resultMap>

  <select id="selectUnBindDevices" resultMap="selectDevicesMap">
    SELECT ld.id,
           ld.code,
           ld.name,
           ld.status,
           ld.online_state,
           ld.remark,
           ld.indoor_location,
           ld.device_unit_id,
           ld.last_push_time,
           ld.create_time,
           ld.modify_time,
           ld.area_id,
           ld.latitude,
           ld.longitude,
           ld.site,
           ld.linkthing_delete,
           ld.area_path,
           ld.tenant_id,
           ld.linkthing_delete,
           a.area_name        AS areaName,
           s.id               AS spaceId,
           s.space_name       AS spaceName,
           u.device_type_name AS deviceTypeName,
           u.name             AS deviceUnitName,
           u.code             AS deviceUnitCode,
           u.device_type_id   AS deviceTypeId,
           u.version          AS deviceUnitVersion,
           ldt.id             as ldt_id,
           ldt.ico_path       as ldt_ico_path
    FROM linkapp_device ld
           LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
           LEFT JOIN linkapp_device_type ldt ON ldt.id = u.device_type_id
           LEFT JOIN linkapp_area a ON (a.id = ld.area_id)
           LEFT JOIN linkapp_space s ON (s.id = a.space_id)
    <where>
      ld.delete_state = 1
      and (area_id is null or LENGTH(trim(area_id)) &lt;= 0)
      <if test="device.id != null and device.id != ''">
        and ld.id = #{device.id}
      </if>
      <if test="device.spaceId != null and device.spaceId != ''">
        and s.id = #{device.spaceId}
      </if>
      <if test="device.linkappSpace != null and device.linkappSpace.type != null">
        and s.type = #{device.linkappSpace.type}
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
        and (a.area_path = #{device.areaPath} or a.area_path like concat(#{device.areaPath},':%'))
      </if>
      <if test="device.deviceTypeId != null and device.deviceTypeId != ''">
        and u.device_type_id = #{device.deviceTypeId}
      </if>
      <if test="device.areaId != null and device.areaId != ''">
        and ld.area_id = #{device.areaId}
      </if>
      <if test="device.queryTimeStart != null and device.queryTimeStart != ''">
        and ld.modify_time >= #{device.queryTimeStart}
      </if>
      <if test="device.queryTimeEnd != null and device.queryTimeEnd != ''">
        and #{device.queryTimeEnd} >= ld.modify_time
      </if>

      <if test="device.name != null and device.name != ''">
        and ld.name like CONCAT('%', #{device.name}, '%')
      </if>

      <if test="device.code != null and device.code != ''">
        and ld.code like CONCAT('%', #{device.code}, '%')
      </if>

      <if test="device.status != null">
        and ld.status = #{device.status}
      </if>
      <if test="device.onlineState != null">
        and ld.online_state = #{device.onlineState}
      </if>
      <if test="device.projectId != null and device.projectId != ''">
        and ld.project_id = #{device.projectId}
      </if>
      <if test="device.deviceUnitId != null and device.deviceUnitId != ''">
        and ld.device_unit_id = #{device.deviceUnitId}
      </if>
      <if test="device.deviceUnitVersion != null and device.deviceUnitVersion != ''">
        and u.version = #{device.deviceUnitVersion}
      </if>
      <if test="device.deviceUnitCode != null and device.deviceUnitCode != ''">
        and u.code = #{device.deviceUnitCode}
      </if>
      <if test="device.creator != null and device.creator != ''">
        and ld.creator like CONCAT('%', #{device.creator}, '%')
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceUnitCodeList)">
        and u.code in
        <foreach collection="device.deviceUnitCodeList" item="deviceUnitCode" index="index" open="(" close=")" separator=",">
          #{deviceUnitCode}
        </foreach>
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.areaIds)">
        and ld.area_id in
        <foreach collection="device.areaIds" item="areaId" index="index" open="(" close=")" separator=",">
          #{areaId}
        </foreach>
      </if>
    </where>
    <choose>
      <when test="device.sortModels != null and device.sortModels.size() > 0">
        ORDER BY
        <trim suffixOverrides=",">
          <foreach collection="device.sortModels" item="item" index="index">
            ${item.field} ${item.sortRule},
          </foreach>
        </trim>
      </when>
      <otherwise>
        ORDER BY ld.modify_time DESC,ld.code ASC
      </otherwise>
    </choose>
  </select>


  <!-- 不使用 -->
  <select id="getDeviceVideoList" resultType="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
    SELECT
        d.name deviceName,
        d.code deviceCode,
        ldas.prop_code  propCode,
        ldas.prop_name  propName,
        ldas.prop_value propValue
    FROM
        linkapp_device_attribute_status ldas
        LEFT JOIN linkapp_device_attribute lda ON ldas.prop_code = lda.identifier
        LEFT JOIN linkapp_device d ON das.device_code = d.code AND d.delete_state = 1
        LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
    <where>
        das.version = u.version
        <if test="device.name != null and device.name != ''">
            and das.device_name like CONCAT('%', #{device.name}, '%')
        </if>
        <if test="device.onlineState != null">
          and d.online_state =#{device.onlineState}
        </if>


    </where>
  </select>

  <select id="getDeviceVideoCount" resultType="java.lang.Integer">
    select count(*) from (
        select
        ld.id,
        ld.name deviceName,
        ld.code deviceCode,
        ld.device_unit_id deviceUnitId
        from
        linkapp_device ld left join linkapp_device_attribute lda on ld.device_unit_id=lda.device_unit_id and ld.delete_state=1
        <where>
          lda.unit='videoMedia'
          <if test="device.name != null and device.name != ''">
            and ld.name like CONCAT('%', #{device.name}, '%')
          </if>
          <if test="device.onlineState != null">
            and ld.online_state =#{device.onlineState}
          </if>
        </where>
        group by ld.id
    )   total
  </select>

  <!-- 获取所有含有摄像头的设备list -->
  <select id="getVideoTypeDeviceListGlobal" resultType="com.easylinkin.linkappapi.webcammanage.entity.DeviceAttributeStatus">
      select ld.id,
             ld.name                     deviceName,
             ld.code                     deviceCode,
             ld.create_time              createTime,
             ld.online_state             onlineState,
             ld.area_path             as areaPath,
             lt.platform_project_name as projectName,
             ld.device_unit_id           deviceUnitId
      from linkapp_device ld
               left join linkapp_device_attribute lda on ld.device_unit_id = lda.device_unit_id and ld.delete_state = 1
               inner join linkapp_tenant lt on lt.id = ld.tenant_id
      <where>
        lda.unit='videoMedia'
          <if test="device.projectName != null and device.projectName != ''">
              and lt.platform_project_name like CONCAT('%', #{device.projectName}, '%')
          </if>
        <if test="device != null ">
          <if test="device.tenantId != null and device.tenantId != ''">
            and ld.tenant_id = #{device.tenantId}
          </if>
        </if>
        <if test="device.name != null and device.name != ''">
          and ld.name like CONCAT('%', #{device.name}, '%')
        </if>
      <if test="device.onlineState != null">
          and ld.online_state = #{device.onlineState}
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
          and (ld.area_path = #{device.areaPath} or ld.area_path like concat(#{device.areaPath}, ':%'))
      </if>
      <if test="device.code != null and device.code != ''">
          and ld.code = #{device.code}
      </if>
      </where>
      group by ld.id
      order by ld.name asc
  </select>

  <!-- 获取设备下的视频数据list -->
  <select id="getVideoInfoByDeviceGlobal" resultType="com.easylinkin.linkappapi.webcammanage.entity.DeviceAttributeStatus">
    select
      ld.id,
      ld.name deviceName,
      ld.code deviceCode,
      ld.device_unit_id deviceUnitId,
      ldas.version version,
      ldas.prop_name propName,
      ldas.prop_code propCode,
      ldas.prop_value propValue,
      lda.sort_no sortNo
    from
      linkapp_device ld right join linkapp_device_attribute lda on ld.device_unit_id=lda.device_unit_id and ld.delete_state=1
                        right join linkapp_device_unit ldu on ld.device_unit_id=ldu.id and ldu.delete_state=1
                        right join linkapp_device_attribute_status ldas on ld.code=ldas.device_code and ldu.version=ldas.version and lda.identifier=ldas.prop_code
    <where>
        <!-- lda.unit='videoMedia' and -->
      <if test="deviceId != null and deviceId != ''">
          and ld.id = #{deviceId}
      </if>
        <if test="deviceIds != null and deviceIds.size() != 0">
            and ld.id in
            <foreach collection="deviceIds" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </where>
  </select>

</mapper>
