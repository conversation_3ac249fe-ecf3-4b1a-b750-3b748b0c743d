<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.assess.mapper.AssessRuleFileMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.assess.vo.AssessRuleFileVo">
    <id property="id" column="id"/>
    <result property="tenantId" column="tenant_id"/>
    <result property="fileType" column="file_type"/>
    <result property="fileUrl" column="file_url"/>
    <result property="fileName" column="file_name"/>
    <result property="isUsed" column="is_used"/>
    <result property="createUid" column="create_uid"/>
    <result property="createTime" column="create_time"/>
    <result property="createUserName" column="createUserName"/>
    <result property="tenantName" column="tenantName"/>
  </resultMap>
  <sql id="assessRuleFileMap">
    rarf.*,
    lt.platform_project_name as tenantName,
    lu.real_name as createUserName
  </sql>

  <select id="selectList" resultMap="BaseResultMap">
    select <include refid="assessRuleFileMap" />
    from rail_assess_rule_file rarf
    left join rail_linkapp_roster_personnel lu on rarf.create_uid = lu.id
    left join linkapp_tenant lt on rarf.tenant_id = lt.id
    where 1 = 1
   <if test="assessRuleFileDto != null">
     <if test="assessRuleFileDto.id != null">
       and rarf.id = #{assessRuleFileDto.id}
     </if>
     <if test="assessRuleFileDto.fileType != null">
       and rarf.file_type = #{assessRuleFileDto.fileType}
     </if>
     <if test="assessRuleFileDto.tenantId != null">
       and rarf.tenant_id = #{assessRuleFileDto.tenantId}
     </if>
     <if test="assessRuleFileDto.isUsed != null">
       and rarf.is_used = #{assessRuleFileDto.isUsed}
     </if>
   </if>
  </select>
</mapper>
