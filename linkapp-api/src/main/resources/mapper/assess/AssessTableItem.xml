<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.assess.mapper.AssessTableItemMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.assess.vo.AssessTableItemVo">
    <id property="id" column="id"/>
    <result property="tenantId" column="tenant_id"/>
    <result property="tableType" column="table_type"/>
    <result property="tableName" column="table_name"/>
    <result property="indexNo" column="index_no"/>
    <result property="assessTarget" column="assess_target"/>
    <result property="assessEntry" column="assess_entry"/>
    <result property="assessContent" column="assess_content"/>
    <result property="scoreStandard" column="score_standard"/>
    <result property="scoreDeduct" column="score_deduct"/>
    <result property="scoreAdd" column="score_add"/>
    <result property="remark" column="remark"/>
    <result property="calculateRules" column="calculate_rules"/>
  </resultMap>

  <select id="selectList" resultMap="BaseResultMap">
    SELECT
    rati.*
    FROM
    rail_assess_table_item rati
    WHERE 1 = 1
   <if test="assessTableItemDto != null">
     <if test="assessTableItemDto.id != null">
       and rati.id = #{assessTableItemDto.id}
     </if>
     <if test="assessTableItemDto.tenantId != null">
       and rati.tenant_id = #{assessTableItemDto.tenantId}
     </if>
     <if test="assessTableItemDto.tableType != null">
       and rati.table_type = #{assessTableItemDto.tableType}
     </if>
   </if>
    ORDER BY
    rati.table_type asc,
    rati.index_no asc,
    rati.table_type,
    rati.assess_target
  </select>
</mapper>
