<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.assess.mapper.AssessTableMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.assess.vo.AssessTableVo">
    <id property="id" column="id"/>
    <result property="tenantId" column="tenant_id"/>
    <result column="table_name" property="tableName"/>
    <result column="assess_month" property="assessMonth"/>
    <result column="assess_type" property="assessType"/>
    <result column="assess_user_id" property="assessUserId"/>
    <result column="assess_group" property="assessGroup"/>
    <result column="assess_status" property="assessStatus"/>
    <result column="total_score" property="totalScore"/>
    <result column="actual_score" property="actualScore"/>
    <result column="deduct_score" property="deductScore"/>
    <result column="assess_result" property="assessResult"/>
    <result column="assess_group_ids" property="assessGroupIds"/>
    <result column="assessGroupNames" property="assessGroupNames"/>
    <result column="assess_group_sign" property="assessGroupSign"/>
    <result column="assess_time" property="assessTime"/>
    <result column="submit_sign_time" property="submitSignTime"/>
    <result column="finish_time" property="finishTime"/>
    <result column="create_uid" property="createUid"/>
    <result column="create_time" property="createTime"/>
    <result column="createUserName" property="createUserName"/>

  </resultMap>
  <sql id="assessTableMap">
    rat.*,
    rat.assess_group as assessUserName,
    lt.platform_project_name as tenantName,
    lu3.real_name as assessGroupNames
  </sql>

  <select id="selectList" resultMap="BaseResultMap">
    select <include refid="assessTableMap" />
    from rail_assess_table rat
    left join rail_linkapp_roster_personnel lu on rat.assess_user_id = lu.id
    left join rail_linkapp_roster_personnel lu3 on rat.assess_group_ids = lu3.id
    left join linkapp_tenant lt on rat.tenant_id = lt.id
    where 1 = 1
   <if test="assessTableDto != null">
     <if test="assessTableDto.tenantId != null and assessTableDto.tenantId !=''">
       and rat.tenant_id = #{assessTableDto.tenantId}
     </if>
     <if test="assessTableDto.id != null">
       and rat.id = #{assessTableDto.id}
     </if>
     <if test="assessTableDto.assessUserId != null">
       and rat.assess_user_id = #{assessTableDto.assessUserId}
     </if>
     <if test="assessTableDto.assessGroup != null and assessTableDto.assessGroup != ''">
       and rat.assess_group = #{assessTableDto.assessGroup}
     </if>
     <if test="assessTableDto.assessType != null">
       and rat.assess_type = #{assessTableDto.assessType}
     </if>
     <if test="assessTableDto.assessStatus != null">
       and rat.assess_status = #{assessTableDto.assessStatus}
     </if>
     <if test="assessTableDto.assessResult != null">
       and rat.assess_result = #{assessTableDto.assessResult}
     </if>
     <if test="assessTableDto.assessMonth != null and assessTableDto.assessMonth != ''">
       and rat.assess_month = #{assessTableDto.assessMonth}
     </if>
     <if test="assessTableDto.assessUser != null and assessTableDto.assessUser != ''">
       and (rat.assess_group like CONCAT('%',#{assessTableDto.assessUser},'%') or lu.real_name like CONCAT('%',#{assessTableDto.assessUser},'%'))
     </if>

   </if>
    ORDER BY ${assessTableDto.sortParams} ${assessTableDto.sortRule}
  </select>

  <select id="selectActualScoreScreen" resultType="java.util.Map">
    SELECT
        ranges.range_name AS 'scoreRange',
        IFNULL(COUNT(scores.actual_score), 0) AS 'recordCount'
    FROM (
        SELECT <![CDATA['<60' ]]> AS range_name, 0 AS min_score, 59 AS max_score UNION ALL
        SELECT '60-69', 60, 69 UNION ALL
        SELECT '70-79', 70, 79 UNION ALL
        SELECT '80-89', 80, 89 UNION ALL
        SELECT '≥90', 90, 120) AS ranges
        LEFT JOIN ( SELECT actual_score FROM rail_assess_table
       WHERE assess_status = 2 AND  tenant_id = #{tenantId}) AS scores
            ON  (scores.actual_score BETWEEN ranges.min_score AND ranges.max_score) OR(ranges.min_score = 0 AND scores.actual_score &lt; 60 )
    GROUP BY ranges.range_name, ranges.min_score ORDER BY ranges.min_score;
  </select>

  <select id="selectMaxCScoreByMonthAndType" resultType="java.util.Map">
    SELECT
        assess_group as assessGroup,
        actual_score as actualScore,
        case assess_type when '1' then '网格安全员' when '2' then '安监专务' when '3' then '施工队伍' else '未知' end  as assessType
    FROM rail_assess_table
    WHERE actual_score = (
        SELECT MAX( actual_score) FROM rail_assess_table
        WHERE  assess_status = 2 AND assess_month = #{assessMonth} and assess_type in (${assessTypes}) and tenant_id = #{tenantId} )
    AND assess_status = 2 AND assess_month = #{assessMonth} and assess_type in (${assessTypes})  and tenant_id = #{tenantId}
  </select>

  <select id="selectTableTotalByMonthScreen" resultType="java.util.Map">
    SELECT IFNULL(SUM(CASE WHEN assess_type = 1 OR assess_type = 2 THEN 1 ELSE 0 END), 0) as 'personTotal',
           IFNULL(SUM(CASE WHEN assess_type = 3 THEN 1 ELSE 0 END), 0)                    as 'groupTotal'
    FROM rail_assess_table
    WHERE assess_status = 2 AND tenant_id = #{tenantId}
  </select>

  <select id="selectSafetyUserAssessResult" resultType="java.lang.String">
    SELECT DATE_ADD(#{assessMonth}, INTERVAL seq DAY) AS missingDate
    FROM (
    SELECT 0 AS seq UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
    SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
    SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION
    SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
    SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION
    SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION
    SELECT 30
    ) AS numbers
    WHERE DATE_ADD(#{assessMonth}, INTERVAL seq DAY) &lt;= LAST_DAY(#{assessMonth})
    AND NOT EXISTS (
    SELECT * FROM (
    SELECT DATE(create_time_) AS record_date FROM rail_evening_meeting_user WHERE user_id_ = #{userId} and tenant_id_ = #{tenantId}
    UNION
    SELECT DATE(create_time) AS record_date FROM rail_process_monitor WHERE create_user_id = #{userId} and tenant_id = #{tenantId}
    UNION
    SELECT DATE(create_time_) AS record_date FROM rail_grid_security_log WHERE user_id_ = #{userId} and tenant_id_ = #{tenantId}
    UNION
    SELECT DATE(create_time_) AS record_date FROM rail_person_work_record WHERE person_id_ = #{userId} and tenant_id_ = #{tenantId}

    ) AS records
    WHERE records.record_date = DATE_ADD(#{assessMonth}, INTERVAL seq DAY)
    )
    ORDER BY missingDate
  </select>

  <select id="selectFllowWorkNoDays" resultType="java.lang.String">
    SELECT DATE_ADD(#{assessMonth}, INTERVAL seq DAY) AS missingDate
    FROM (
    SELECT 0 AS seq UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
    SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
    SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION
    SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
    SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION
    SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION
    SELECT 30
    ) AS numbers
    WHERE DATE_ADD(#{assessMonth}, INTERVAL seq DAY) &lt;= LAST_DAY(#{assessMonth})
    AND NOT EXISTS (
    SELECT * FROM (
    SELECT DATE(create_time_) AS record_date FROM rail_grid_security_log WHERE user_id_ = #{userId} and tenant_id_ = #{tenantId}
    UNION
    SELECT DATE(create_time) AS record_date FROM rail_process_monitor WHERE create_user_id = #{userId} and tenant_id = #{tenantId}
    ) AS records
    WHERE records.record_date = DATE_ADD(#{assessMonth}, INTERVAL seq DAY)
    )
    ORDER BY missingDate
  </select>

</mapper>
