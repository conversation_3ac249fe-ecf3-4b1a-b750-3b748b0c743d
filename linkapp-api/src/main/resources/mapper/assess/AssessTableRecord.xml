<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.assess.mapper.AssessTableRecordMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.assess.vo.AssessTableRecordVo">
    <id property="id" column="id"/>
    <result column="table_id" property="tableId"/>
    <result column="item_id" property="itemId"/>
    <result column="record_actual_score" property="recordActualScore"/>
    <result column="record_deduct_score" property="recordDeductScore"/>
    <result column="assessTarget" property="assessTarget"/>
    <result column="assessEntry" property="assessEntry"/>
    <result column="assessContent" property="assessContent"/>
    <result column="scoreStandard" property="scoreStandard"/>
    <result column="record_remark" property="recordRemark"/>
    <result column="create_uid" property="createUid"/>
    <result column="create_time" property="createTime"/>
    <result column="createUserName" property="createUserName"/>
  </resultMap>
  <sql id="assessTableRecordMap">
    ratr.*,
    rsti.assess_target as assessTarget,
    rsti.assess_entry as assessEntry,
    rsti.assess_content as assessContent,
    rsti.score_standard as scoreStandard,
    lu2.real_name as createUserName
  </sql>

  <select id="selectList" resultMap="BaseResultMap">
    select <include refid="assessTableRecordMap" />
    from rail_assess_table_record ratr
    left join rail_assess_table_item rsti on ratr.item_id = rsti.id
    left join rail_linkapp_roster_personnel lu2 on ratr.create_uid = lu2.id
    where 1 = 1
   <if test="assessTableRecordDto != null">
     <if test="assessTableRecordDto.id != null">
       and ratr.id = #{assessTableRecordDto.id}
     </if>
     <if test="assessTableRecordDto.tableId != null">
       and ratr.table_id = #{assessTableRecordDto.tableId}
     </if>
   </if>
    GROUP BY ratr.id
  </select>
</mapper>
