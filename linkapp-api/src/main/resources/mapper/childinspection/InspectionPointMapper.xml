<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.childinspection.mapper.InspectionPointMapper">

    <sql id="Base_Column_list">
        a.id,a.inspection_point_code,a.inspection_point_name,a.description,a.need_photograph,a.effect,a.qr_code_id,a.longitude,a.latitude,a.status,
            a.inspection_point_status,a.create_time,a.modify_time,a.point_image
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.childinspection.entity.InspectionPointEntity">
        <result column="id" property="id"/>
        <result column="inspection_point_code" property="inspectionPointCode"/>
        <result column="inspection_point_name" property="inspectionPointName"/>
        <result column="description" property="description"/>
        <result column="need_photograph" property="needPhotograph"/>
        <result column="effect" property="effect"/>
        <result column="qr_code_id" property="qrCodeId"/>
        <result column="qr_code_status" property="qrCodeStatus"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="status" property="status"/>
        <result column="inspection_point_status" property="inspectionPointStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="point_image" property="pointImage"/>
        <result column="inspection_count" property="inspectionCount"/>
        <result column="inspection_status" property="inspectionStatus"/>
    </resultMap>

    <select id="getInspectionPointList" resultMap="BaseResultMap" resultType="com.easylinkin.linkappapi.childinspection.vo.InspectionPointVo">
        select <include refid="Base_Column_list"></include>, b.status as qr_code_status,IFNULL(c.count,0) as inspection_count
            from child_inspection_point a
                left join child_inspection_qr_code b on a.qr_code_id = b.id
                left join (select point_id,count(*) count from child_inspection_record group by point_id) c on a.id = c.point_id
                where 1 = 1
        <if test="model.inspectionPointCode!=null and model.inspectionPointCode!=''">
            and a.inspection_point_code like concat('%',#{model.inspectionPointCode},'%')
        </if>
        <if test="model.inspectionPointName!=null and model.inspectionPointName!=''">
            and a.inspection_point_name like concat('%',#{model.inspectionPointName},'%')
        </if>
        order by a.create_time desc
    </select>

    <select id="selectMaxCode" resultType="java.lang.String">
        select max(inspection_point_code) as code from child_inspection_point
    </select>

    <select id="selectPointList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select a.id,a.inspection_point_code,a.inspection_point_name,a.description,a.need_photograph,a.effect,a.qr_code_id,a.longitude,a.latitude,
               a.inspection_point_status,a.create_time,a.modify_time,a.point_image,
               (case when c.count > 0 then 1 else 0 end) as status from child_inspection_point a
        inner join child_task_point b on a.id = b.point_id
        left join (select point_id,count(1) as count from child_inspection_record where DATE(create_time) = CURDATE() and type = 1 GROUP BY point_id) c on a.id = c.point_id
        where b.task_id = #{id} and a.inspection_point_status = 1
        order by a.create_time desc
    </select>

    <select id="getTodayTaskInfo" resultMap="BaseResultMap">
        select a.id,a.inspection_point_code,a.inspection_point_name,a.description,a.need_photograph,a.effect,a.qr_code_id,a.longitude,a.latitude,
        a.inspection_point_status,a.create_time,a.modify_time,a.point_image,
        (case when c.count > 0 then 1 else 0 end) as status from child_inspection_point a
            inner join child_task_point b on a.id = b.point_id
        left join (select point_id,count(1) as count from child_inspection_record where DATE(create_time) = CURDATE() and type = 1 GROUP BY point_id) c on a.id = c.point_id
        where b.task_id = #{model.id} and a.inspection_point_status = 1
            <if test="model.keywords!=null and model.keywords!=''">
                and a.inspection_point_name like concat('%',#{model.keywords},'%')
            </if>
        order by a.create_time desc
    </select>

    <update id="updateByQrCode" >
        update child_inspection_point set qr_code_id = null where qr_code_id = #{id}
    </update>

    <update id="updatePointByQrCode">
        update child_inspection_point set qr_code_id = #{id} where id = #{inspectionPointId}
    </update>

    <select id="selectCurrentTaskByPointId" resultType="java.lang.Integer">
        select count(1) from child_task_point where point_id = #{pointId} and DATE(create_time) = CURDATE()
    </select>
</mapper>
