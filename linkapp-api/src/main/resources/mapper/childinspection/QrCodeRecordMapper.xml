<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.childinspection.mapper.QrCodeRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.childinspection.entity.QrCodeRecordEntity">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="point_id" property="pointId"/>
        <result column="point_name" property="pointName"/>
        <result column="status" property="status"/>
    </resultMap>

    <select id="selectRecordInfoList" resultMap="BaseResultMap" >
        select * from child_qr_code_record where code = #{model.code}
        <if test="model.keywords!=null and model.keywords!=''">
            and (code like concat('%',#{model.keywords},'%') or point_name like concat('%',#{model.keywords},'%'))
        </if>
    </select>

</mapper>
