<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.childinspection.mapper.InspectionRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.childinspection.entity.InspectionRecordEntity">
        <result column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="point_id" property="pointId"/>
        <result column="status" property="status"/>
        <result column="is_del" property="isDel"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_dept" property="userDept"/>
        <result column="assign_user_id" property="assignUserId"/>
        <result column="assign_user_name" property="assignUserName"/>
        <result column="assign_user_dept" property="assignUserDept"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
    </resultMap>

    <select id="selectPointRecordInfoList" resultMap="BaseResultMap">
        select a.*,b.nickname as user_name,d.name_ as user_dept from child_inspection_record a
                 left join linkapp_user b on a.user_id = b.id
                 left join app_hidden_danger c on a.hidden_danger_id = c.id
                 left join app_labor_company d on c.link_unit_id = d.id
                 where point_id = #{model.id} and a.is_del = 0
        <if test="model.keywords!=null and model.keywords!=''">
            and (b.nickname like concat('%',#{model.keywords},'%') or a.create_time like concat('%',#{model.keywords},'%'))
        </if>
        <if test="model.status!=null and model.status!=''">
            and a.status = #{model.status}
        </if>
        order by a.create_time desc
    </select>

    <select id="selectTodayRecordInfo" resultMap="BaseResultMap">
        select a.*,b.nickname as user_name,d.name_ as user_dept from child_inspection_record a
        left join linkapp_user b on a.user_id = b.id
        left join app_hidden_danger c on a.hidden_danger_id = c.id
        left join app_labor_company d on c.link_unit_id = d.id
        where point_id = #{model.id} and a.status > 0 and a.is_del = 0
        <if test="model.keywords!=null and model.keywords!=''">
            and (b.nickname like concat('%',#{model.keywords},'%') or a.create_time like concat('%',#{model.keywords},'%'))
        </if>
        order by a.create_time desc
    </select>

    <select id="selectListById" resultType="java.util.Map">
        select point_id,count(1) as count from child_inspection_record
        where is_del = 0 and  point_id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY point_id
    </select>

    <select id="selectOneById" resultMap="BaseResultMap">
        select a.*,b.nickname as user_name from child_inspection_record a
                   left join linkapp_user b on a.user_id = b.id
                   where point_id = #{pointId} and DATE(a.create_time) = CURDATE() and a.is_del = 0
                   order by create_time desc limit 1
    </select>

    <delete id="deleteRecordById">
        update child_inspection_record set is_del = 1 where point_id = #{pointId} and type = 1 and DATE(create_time) = CURDATE() and is_del = 0
    </delete>

    <select id="getTaskCount" resultMap="BaseResultMap">
        select c.* from child_inspection_task a
               inner join child_task_point b on a.id = b.task_id
               inner join child_inspection_record c on b.point_id = c.point_id and DATE(c.create_time) = DATE(a.create_time)
               where a.id = #{taskId} and c.is_del = 0
    </select>

    <update id="updateTaskAndPointStatus">
        update child_task_point set status = #{status} where point_id = #{pointId} and DATE(create_time) = CURDATE()
    </update>
</mapper>
