<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.childinspection.mapper.InspectionQrCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.childinspection.entity.InspectionQrCodeEntity">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="image_url" property="imageUrl"/>
        <result column="inspection_point_id" property="inspectionPointId"/>
        <result column="inspection_point_name" property="inspectionPointName"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
    </resultMap>

    <select id="getList" resultMap="BaseResultMap">
        select * from child_inspection_qr_code where 1 = 1
        <if test="model.code!=null and model.code!=''">
            and code like concat('%',#{model.code},'%')
        </if>
        <if test="model.status!=null and model.status!=''">
            and status = #{model.status}
        </if>
        order by create_time desc
    </select>

    <select id="selectMaxCode" resultType="java.lang.String">
        select max(code) from child_inspection_qr_code
    </select>

    <update id="cleanRelation" parameterType="java.lang.String">
        update child_inspection_qr_code set inspection_point_id = null,inspection_point_name = null,status = 0 where inspection_point_id = #{inspectionPointId}
    </update>

    <update id="updateRelation" >
        update child_inspection_qr_code set inspection_point_id = #{inspectionPointId},inspection_point_name = #{inspectionPointName}, status = 1 where id = #{id}
    </update>

    <update id="updateQrCodeRecord" >
        update child_qr_code_record set status = 2,end_time = now() where `code` = #{code} and status = 1
    </update>

    <select id="insertQrCodeRecord" >
        insert into child_qr_code_record(`code`,point_id,point_name,status,start_time) values (#{code},#{inspectionPointId},#{inspectionPointName}, #{status}, now())
    </select>

    <update id="updateQrCodeRecordByPointId">
        update child_qr_code_record set status = 2,end_time = now() where point_id = #{inspectionPointId} and status = 1
    </update>

</mapper>
