<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.childinspection.mapper.InspectionMapper">
    <sql id="Base_Column_List">
        id,inspection_name,inspection_time,start_time,end_time,duration,inspection_point,person_id,person_name,schedule,create_time,modify_time,tenant_id,creator,modifier
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.childinspection.entity.InspectionTaskEntity">
        <result column="id" property="id"/>
        <result column="inspection_name" property="inspectionName"/>
        <result column="inspection_time" property="inspectionTime"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="duration" property="duration"/>
        <result column="inspection_point" property="inspectionPoint"/>
        <result column="person_id" property="personId"/>
        <result column="person_name" property="personName"/>
        <result column="schedule" property="schedule"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
    </resultMap>

    <select id="getTaskList" resultMap="BaseResultMap">
        select
        a.id,a.inspection_name,a.inspection_time,a.inspection_point,a.person_id,a.person_name,ROUND(count/inspection_point * 100,2) AS `schedule`,a.create_time,
        CASE WHEN a.inspection_point > count THEN null ELSE DATE_FORMAT(FROM_UNIXTIME(b.max - b.min), '%Hh %i′%s〃') END  as duration,a.tenant_id,a.creator,
        DATE_FORMAT(IFNULL(b.min,a.start_time),'%Y-%m-%d %H:%i:%s') as start_time,
        CASE WHEN a.inspection_point > count THEN a.end_time ELSE DATE_FORMAT(IFNULL(b.max,a.end_time),'%Y-%m-%d %H:%i:%s') END  as end_time
            from child_inspection_task a
            left join (
                select a.id as task_id,count(c.point_id) as count,MIN(min) as min,MAX(min) as max from child_inspection_task a
                LEFT JOIN child_task_point b on a.id = b.task_id
                LEFT JOIN (
                select point_id,DATE(create_time) as date,count(1),MIN(create_time) min from child_inspection_record
                GROUP BY point_id,cast(create_time as date)
                ) c on b.point_id = c.point_id and c.date = DATE(b.create_time)
                GROUP BY a.id,a.inspection_name
             ) b on a.id = b.task_id
                where 1 = 1
        <if test="model.inspectionName!=null and model.inspectionName!=''">
            and inspection_name like concat('%',#{model.inspectionName},'%')
        </if>
        <if test="model.startTime!=null and model.startTime!=''">
            and start_time like concat('%',#{model.startTime},'%')
        </if>
        <if test="model.endTime!=null and model.endTime!=''">
            and end_time like concat('%',#{model.endTime},'%')
        </if>
        order by create_time desc
    </select>

    <select id="selectPointListNew" resultType="java.util.Map">
        select a.id as task_id,count(c.point_id) as count,MIN(min) as min,MAX(min) as max,d.longitude,d.latitude from child_inspection_task a
            LEFT JOIN child_task_point b on a.id = b.task_id
            LEFT JOIN (
            select point_id,DATE(create_time) as date,count(1),MIN(create_time) min from child_inspection_record
            GROUP BY point_id,cast(create_time as date)
            ) c on b.point_id = c.point_id and c.date = DATE(b.create_time)
            left join child_inspection_point d on b.point_id = d.id
        where b.task_id = #{taskId}
        GROUP BY a.id,a.inspection_name,d.longitude,d.latitude
    </select>

    <insert id="insertTaskAndPoint" parameterType="java.lang.String">
        insert into child_task_point (task_id,point_id,create_time,status) values(#{taskId}, #{pointId},NOW(),0)
    </insert>

    <delete id="deleteTaskAndPointByTaskId" parameterType="java.lang.String" >
        delete from child_task_point where task_id = #{taskId}
    </delete>

    <select id="selectListByTaskId" resultType="java.lang.Integer">
        select count(1) from child_task_point where task_id = #{taskId} and DATE(create_time) = CURDATE()
    </select>

    <select id="getHistoryTaskList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from child_inspection_task
                where DATE(create_time) &lt; CURDATE() ORDER BY create_time DESC,inspection_name DESC
    </select>
</mapper>
