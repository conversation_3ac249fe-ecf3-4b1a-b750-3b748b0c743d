<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.positioning.mapper.AlarmDeviceConfigMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.positioning.entity.AlarmDeviceConfig">
    <!--@mbg.generated-->
    <!--@Table sbdw_alarm_device_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="fence_id" jdbcType="INTEGER" property="fenceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, device_code, fence_id
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sbdw_alarm_device_config
    (device_code, fence_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deviceCode,jdbcType=VARCHAR}, #{item.fenceId,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>