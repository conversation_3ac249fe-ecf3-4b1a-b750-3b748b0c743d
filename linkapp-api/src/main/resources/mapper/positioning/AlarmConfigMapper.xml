<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.positioning.mapper.AlarmConfigMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.positioning.entity.AlarmConfig">
    <!--@mbg.generated-->
    <!--@Table sbdw_alarm_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="tooltip" jdbcType="VARCHAR" property="tooltip" />
    <result column="fence_id" jdbcType="INTEGER" property="fenceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `type`, duration, tooltip, fence_id
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sbdw_alarm_config
    (`type`, duration, tooltip, fence_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.type,jdbcType=BOOLEAN}, #{item.duration,jdbcType=INTEGER}, #{item.tooltip,jdbcType=VARCHAR},
      #{item.fenceId,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>