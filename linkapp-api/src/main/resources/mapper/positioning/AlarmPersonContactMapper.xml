<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.positioning.mapper.SbdwAlarmPersonContactMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.positioning.entity.AlarmPersonContact">
    <!--@mbg.generated-->
    <!--@Table sbdw_alarm_person_contact-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fence_id" jdbcType="INTEGER" property="fenceId" />
    <result column="alarm_person_contact_id" jdbcType="VARCHAR" property="alarmPersonContactId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, fence_id, alarm_person_contact_id
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sbdw_alarm_person_contact
    (fence_id, alarm_person_contact_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fenceId,jdbcType=INTEGER}, #{item.alarmPersonContactId,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>