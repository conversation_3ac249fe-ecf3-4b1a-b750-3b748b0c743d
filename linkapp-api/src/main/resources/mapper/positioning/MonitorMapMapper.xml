<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.positioning.mapper.MonitorMapMapper">

    <select id="getAttributesList" resultMap="getAttributesListMap">
        select temp.* from (
        SELECT ldas.id,
        ldas.prop_code,
        ldas.prop_value       AS vo_prop_value,
        ldas.parent_prop_code AS vo_parent_prop_code,
        lda.device_unit_id,
        lda.name              AS vo_prop_name,
        lda.unit              AS vo_unit,
        lda.specs,
        <choose>
            <when test="pTenantId != null and pTenantId != ''">
                ifnull(ldatc.is_show, lda.is_show) is_show,
                ifnull(ldatc.sort_no, lda.sort_no) sort_no,
            </when>
            <otherwise>
                lda.is_show,
                lda.sort_no,
            </otherwise>
        </choose>
        lda.ico_path
        FROM linkapp_device_attribute_status ldas
        LEFT JOIN linkapp_device_attribute lda ON lda.device_unit_id = #{pDeviceUnitId} AND ldas.prop_code = lda.identifier
        <if test="pTenantId != null and pTenantId != ''">
            left join linkapp_device_attribute_tenant_config ldatc on ldatc.device_attribute_id = lda.id and ldatc.tenant_id = #{pTenantId,jdbcType=VARCHAR}
        </if>
        LEFT JOIN linkapp_device d ON ldas.device_code = d.code AND d.delete_state = 1
        LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
        WHERE ldas.version = u.version
        AND ldas.device_code = #{pCode}
        ) temp
        where temp.is_show = 1
        ORDER BY temp.sort_no ASC
    </select>

    <select id="getDeviceList" resultMap="MonitorDevicesMap">
        SELECT
        d_.*
        FROM
        (
        SELECT
        d.drasAreaId area_id,
        d.drasAreaPath area_path,
        d.id,
        d.name,
        d.code,
        d.remark,
        d.device_unit_id,
        d.create_time,
        d.online_state,
        d.tenant_id,
        d.status,
        u.device_type_name AS d_deviceTypeName,
        u.code             AS d_device_unit_code,
        u.name             AS deviceUnitName,
        u.version          AS deviceUnitVersion,
        ldt.ico_path
        FROM
        (
        select
        dras.area_id drasAreaId,
        dras.area_path  drasAreaPath,
        dChild.*
        from
        device_ref_area_scope dras left join linkapp_device dChild on dras.device_code=dChild.code
        <where>
            <if test="qmm.functionIdentifier != null and qmm.functionIdentifier != ''">
                and dras.function_identifier = #{qmm.functionIdentifier}
            </if>
            <if test="qmm.tenantId != null and qmm.tenantId != ''">
                and dras.tenant_id = #{qmm.tenantId}
            </if>
        </where>
        group by dChild.id
        ) d
        LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
        left join linkapp_device_type ldt on ldt.id = u.device_type_id
        where
        d.delete_state = 1
        ORDER BY d.create_time DESC
        ) d_ left join linkapp_device_attribute_status ldas on d_.code=ldas.device_code
        <where>
            <if test="qmm.commonQuery != null and qmm.commonQuery != ''">
                and
                (
                    (d_.area_path = #{qmm.commonQuery} or d_.area_path like concat(#{qmm.commonQuery},':%'))
                    or d_.name like CONCAT('%', #{qmm.commonQuery}, '%')
                    or d_.code = #{qmm.commonQuery}
                )
            </if>
            <if test="qmm.queryStatus != null">
                and d_.online_state = #{qmm.queryStatus}
            </if>
        </where>
        group by d_.id
    </select>

    <select id="getStatisticsInfo" resultType="com.easylinkin.linkappapi.positioning.entity.MonitorMapStatistics">
        SELECT
            count(distinct dras.device_code) allNum,
            count(distinct case when online_state = 1  then dras.device_code else null end) onlineNum,
            count(distinct case when online_state = 0 then dras.device_code else null end) offlineNum
        FROM  device_ref_area_scope dras
        left  join linkapp_device d on dras.device_code=d.code
        WHERE
            d.delete_state = 1
            <if test="functionIdentifier != null and functionIdentifier != ''">
                and dras.function_identifier=#{functionIdentifier}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and dras.tenant_id=#{tenantId}
            </if>
            <if test="commonQuery != null and commonQuery != ''">
                and (d.name like concat('%', #{commonQuery},'%') or
                    (dras.area_path = #{commonQuery} or dras.area_path like concat(#{commonQuery},':%'))
                    )
            </if>
    </select>


    <!-- 通用查询映射结果 -->
    <resultMap id="MonitorDevicesMap" type="com.easylinkin.linkappapi.device.entity.Device">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="online_state" property="onlineState"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="area_id" property="areaId"/>
        <result column="area_path" property="areaPath"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="device_unit_id" property="deviceUnitId"/>
        <result column="d_device_unit_code" property="deviceUnitCode"/>
        <result column="area_path" property="areaPath"/>
        <result column="last_push_time" property="lastPushTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="d_deviceTypeName" property="deviceTypeName"/>
        <result column="deviceUnitVersion" property="deviceUnitVersion"/>
        <association property="deviceType" javaType="com.easylinkin.linkappapi.devicetype.entity.DeviceType">
            <result column="ico_path" property="icoPath"/>
        </association>
        <collection property="deviceAttributeStatusList" column="{pDeviceUnitId=device_unit_id,pCode=code,pTenantId = tenant_id}" select="getAttributesList" javaType="java.util.ArrayList" ofType="com.easylinkin.linkappapi.airconditioner.entity.AirDeviceAttributeStatus.AirDeviceAttributeStatus">
        </collection>
    </resultMap>

    <resultMap id="getAttributesListMap" type="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
        <id column="id" property="id"/>
        <result column="device_unit_id" property="deviceUnitId"/>
        <result column="prop_code" property="propCode"/>
        <result column="vo_prop_name" property="propName"/>
        <result column="vo_unit" property="unit"/>
        <result column="specs" property="specs"/>
        <result column="sort_no" property="sortNo"/>
        <result column="is_show" property="isShow"/>
        <result column="vo_prop_value" property="propValue"/>
        <result column="vo_parent_prop_code" property="parentPropCode"/>
        <result column="vo_array_Index" property="arrayIndex"/>
    </resultMap>

</mapper>
