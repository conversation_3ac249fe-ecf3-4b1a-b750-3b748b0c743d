<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.positioning.mapper.ElectronicFenceMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.positioning.entity.ElectronicFence">
    <!--@mbg.generated-->
    <!--@Table sbdw_electronicfence-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="gid" jdbcType="VARCHAR" property="gid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, remarks, tenant_id, gid
  </sql>
  
  <resultMap id="electronicFenceMap" type="com.easylinkin.linkappapi.positioning.entity.ElectronicFence">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="gid" jdbcType="VARCHAR" property="gid" />
    <collection property="alarmDeviceConfigList" column="{fenceId=id}" select="getAlarmDeviceConfigList" javaType="java.util.ArrayList" ofType="com.easylinkin.linkappapi.positioning.entity.AlarmDeviceConfig">
    </collection>
    <collection property="alarmConfigList" column="{fenceId=id}" select="getAlarmConfigList" javaType="java.util.ArrayList" ofType="com.easylinkin.linkappapi.positioning.entity.AlarmConfig">
    </collection>
    <collection property="alarmPersonContactList" column="{fenceId=id}" select="getAlarmPersonContactList" javaType="java.util.ArrayList" ofType="com.easylinkin.linkappapi.positioning.entity.AlarmPersonContact">
    </collection>
  </resultMap>
  
  <select id="getAlarmPersonContactList" resultType="com.easylinkin.linkappapi.positioning.entity.AlarmPersonContact">
     select apc.* from sbdw_alarm_person_contact apc
    <where>
      <if test="fenceId != null and fenceId != ''">
        and apc.fence_id = #{fenceId}
      </if>
    </where>
  </select>

  <select id="getAlarmConfigList" resultType="com.easylinkin.linkappapi.positioning.entity.AlarmConfig">
    select ac.* from sbdw_alarm_config ac
    <where>
      <if test="fenceId != null and fenceId != ''">
        and ac.fence_id = #{fenceId}
      </if>
    </where>
  </select>


  <select id="getAlarmDeviceConfigList" resultType="com.easylinkin.linkappapi.positioning.entity.AlarmDeviceConfig">
    SELECT
      adc.*,
      ld.name deviceName
    FROM (
        SELECT adc.*
        FROM sbdw_alarm_device_config adc
        WHERE EXISTS (
            SELECT 1
            FROM device_ref_area_scope ras
            WHERE ras.device_code = adc.device_code
        )
        <if test="fenceId != null and fenceId != ''">
          and adc.fence_id = #{fenceId}
        </if>
    ) adc
        LEFT JOIN linkapp_device ld ON ld.`code` = adc.device_code and ld.delete_state=1
  </select>
  
  <select id="getElectronicFenceList" resultMap="electronicFenceMap" parameterType="com.easylinkin.linkappapi.positioning.entity.ElectronicFence">
    select ef.id, ef.`name`, ef.remarks, ef.tenant_id, ef.gid,ef.update_time,ef.type from sbdw_electronicfence ef
    <where>
      <if test="item.tenantId != null and item.tenantId != '' ">
         and ef.tenant_id=#{item.tenantId}
      </if>
      <if test="item.id != null ">
        and ef.id = #{item.id}
      </if>
      <if test="item.ids != null and item.ids.size()>0 ">
        and ef.id in
        <foreach collection="item.ids" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="item.name != null  and item.name != '' ">
         and ef.name like CONCAT('%', #{item.name}, '%')
      </if>
    </where>
    order by ef.update_time desc
  </select>
</mapper>