<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.positioning.mapper.PositionAlarmInfoMapper">

    <select id="getPage" resultMap="PositionAlarmMaps">
        select
            sai.*,
            se.name fenceName,
            ld.name deviceName
        from
            sbdw_alarm_info sai left join sbdw_electronicfence se on sai.fence_id=se.id
            left join linkapp_device ld on sai.device_code=ld.code
        <where>
            and ld.delete_state = 1
            <if test="queryAlarm.tenantId != null and queryAlarm.tenantId != ''">
                and se.tenant_id = #{queryAlarm.tenantId}
            </if>
            <if test="queryAlarm.deviceName != null and queryAlarm.deviceName != ''">
                and ld.name like concat('%',#{queryAlarm.deviceName},'%')
            </if>
            <if test="queryAlarm.deviceCode != null and queryAlarm.deviceCode != ''">
                and sai.device_code like concat('%',#{queryAlarm.deviceCode},'%')
            </if>
            <if test="queryAlarm.fenceId != null">
                and sai.fence_id = #{queryAlarm.fenceId}
            </if>
            <if test="queryAlarm.queryStartTime != null and queryAlarm.queryStartTime != ''">
                and sai.alarm_time &gt;= #{queryAlarm.queryStartTime}
            </if>
            <if test="queryAlarm.queryEndTime != null and queryAlarm.queryEndTime != ''">
                and sai.alarm_time &lt;= #{queryAlarm.queryEndTime}
            </if>
        </where>
        order by sai.alarm_time desc
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="PositionAlarmMaps" type="com.easylinkin.linkappapi.positioning.entity.PositionAlarmInfo">
        <id column="id" property="id"/>
        <result column="device_code" property="deviceCode"/>
        <result column="deviceName" property="deviceName"/>
        <result column="fenceName" property="fenceName"/>
        <result column="fence_id" property="fenceId"/>
        <result column="tooltip" property="tooltip"/>
        <result column="alarm_time" property="alarmTime"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


</mapper>
