<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappWorkResponsibilityMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkResponsibilityVO">
        <id column="id" property="id" />
        <result column="work_id" property="workId" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="sort_index" property="sortIndex" />
    </resultMap>

    <select id="getList" resultMap="BaseResultMap"  parameterType="com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkResponsibilityDTO">
        select  a.* from rail_linkapp_work_responsibility a
        <where>
            <if test="entity.tenantId != null and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.workId != null and entity.workId != ''">
                and a.work_id = #{entity.workId}
            </if>
            <if test="entity.id != null and entity.id != ''">
                and a.id = #{entity.id}
            </if>
        </where>
        order by a.sort_index asc
    </select>
</mapper>