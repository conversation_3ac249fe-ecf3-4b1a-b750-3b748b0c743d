<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappOrgInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappOrgInfoVO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="modifier" property="modifier" />
        <result column="creator" property="creator" />
        <result column="tenant_id" property="tenantId" />
        <result column="sort_index" property="sortIndex" />
    </resultMap>
    <select id="getList" resultMap="BaseResultMap"  parameterType="com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgInfoDTO">
       select a.* from rail_linkapp_org_info a
        <where>
            <if test="entity.id != null and entity.id != ''">
                and a.id = #{entity.id}
            </if>
            <if test="entity.tenantId != null and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.name != null and entity.name != ''">
                and a.name like concat('%',#{entity.name},'%'))
            </if>
        </where>
        order by a.sort_index asc
    </select>

</mapper>