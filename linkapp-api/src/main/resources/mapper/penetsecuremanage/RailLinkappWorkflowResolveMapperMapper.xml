<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappWorkflowResolveMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkflowResolveVO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="workflow_file_url" property="workflowFileUrl" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="tenant_id" property="tenantId" />
        <result column="sort_index" property="sortIndex" />
    </resultMap>
    <select id="getList" resultMap="BaseResultMap"  parameterType="com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkflowResolveDTO">
        select  a.* from rail_linkapp_workflow_resolve a
        <where>
            <if test="entity.tenantId != null and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.name != null and entity.name != ''">
                and a.name like concat('%',#{entity.name},'%'))
            </if>
            <if test="entity.id != null and entity.id != ''">
                and a.id = #{entity.id}
            </if>
        </where>
        order by a.sort_index asc
    </select>

    <!-- 验证名称是否重复，如果传了id则排除该id -->
    <select id="countByOrgName" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM rail_linkapp_workflow_resolve
        WHERE tenant_id = #{tenantId} and name = #{name}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>
</mapper>