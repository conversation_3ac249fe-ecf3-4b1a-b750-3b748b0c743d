<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappGridManagementInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappGridManagementInfoVO">
        <id column="id" property="id" />
        <result column="enginee_type" property="engineeType" />
        <result column="number" property="number" />
        <result column="grid_type" property="gridType" />
        <result column="grid_name" property="gridName" />
        <result column="mileage_range" property="mileageRange" />
        <result column="safety_sup_id" property="safetySupId" />
        <result column="safety_sup_name" property="safetySupName" />
        <result column="grid_sec_id" property="gridSecId" />
        <result column="grid_sec_name" property="gridSecName" />
        <result column="grid_foreman_id" property="gridForemanId" />
        <result column="grid_foreman_name" property="gridForemanName" />
        <result column="grid_siteman_id" property="gridSitemanId" />
        <result column="grid_siteman_name" property="gridSitemanName" />
        <result column="staff_number" property="staffNumber" />
        <result column="construction_team" property="constructionTeam" />
        <result column="is_sole_duty" property="isSoleDuty" />
        <result column="hold_cert" property="holdCert" />
        <result column="is_formal_employee" property="isFormalEmployee" />
        <result column="notes" property="notes" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="modifier" property="modifier" />
        <result column="creator" property="creator" />
        <result column="tenant_id" property="tenantId" />
        <!-- 联系方式 -->
        <result column="safetySupPhone" property="safetySupPhone" />
        <result column="gridSecPhone" property="gridSecPhone" />
        <result column="gridForemanPhone" property="gridForemanPhone" />
        <result column="gridSitemanPhone" property="gridSitemanPhone" />
        <result column="gridMeetPhone" property="gridMeetPhone" />

        <result column="safetySupPict" property="safetySupPict" />
        <result column="gridSecPict" property="gridSecPict" />
        <result column="gridForemanPict" property="gridForemanPict" />
        <result column="gridSitemanPict" property="gridSitemanPict" />
        <result column="gridMeetPict" property="gridMeetPict" />
        <result column="announcement_file_url" property="announcementFileUrl" />
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap"  parameterType="com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappGridManagementInfoDTO">
        select a.*,ss.link_phone as safetySupPhone,gs.link_phone as gridSecPhone,gf.link_phone as  gridForemanPhone,gsi.link_phone as gridSitemanPhone,
        gm.link_phone as gridMeetPhone,
        ss.profile_pict as safetySupPict,gs.profile_pict as gridSecPict,gf.profile_pict as gridForemanPict,gsi.profile_pict as gridSitemanPict,
        gm.profile_pict as gridMeetPict
        from rail_linkapp_grid_management_info a
        left join rail_linkapp_roster_personnel ss on ss.id = a.safety_sup_id
        left join rail_linkapp_roster_personnel gs on gs.id = a.grid_sec_id
        left join rail_linkapp_roster_personnel gf on gs.id = a.grid_foreman_id
        left join rail_linkapp_roster_personnel gsi on gsi.id = a.grid_siteman_name
        left join rail_linkapp_roster_personnel gm on gm.id = a.grid_meet_id
        <where>
            <if test="entity.tenantId != null and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.gridName != null and entity.gridName != ''">
                and a.grid_name like concat('%',#{entity.gridName},'%')
            </if>
            <if test="entity.safetySupName != null and entity.safetySupName != ''">
                and ss.real_name like concat('%',#{entity.safetySupName},'%')
            </if>
            <if test="entity.safetySupName != null and entity.safetySupName != ''">
                and ss.real_name like concat('%',#{entity.safetySupName},'%')
            </if>
            <if test="entity.gridType != null and entity.gridType != ''">
                and a.grid_type like concat('%',#{entity.gridType},'%')
            </if>
            <if test="entity.gridSitemanId != null and entity.gridSitemanId != ''">
                and a.grid_siteman_id = #{entity.gridSitemanId}
            </if>
            <if test="entity.id != null">
                and a.id = #{entity.id}
            </if>
            <if test="entity.engineeType != null and entity.engineeType != ''">
                and a.enginee_type = #{entity.engineeType}
            </if>
            <if test="entity.number != null">
                and a.number = #{entity.number}
            </if>
        </where>
        GROUP BY a.id
        order by a.number asc
    </select>

    <select id="getList" resultMap="BaseResultMap"  parameterType="com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappGridManagementInfoDTO">
        select a.*,ss.link_phone as safetySupPhone,gs.link_phone as gridSecPhone,gf.link_phone as  gridForemanPhone,gsi.link_phone as gridSitemanPhone,
        gm.link_phone as gridMeetPhone,
        ss.profile_pict as safetySupPict,gs.profile_pict as gridSecPict,gf.profile_pict as gridForemanPict,gsi.profile_pict as gridSitemanPict,
        gm.profile_pict as gridMeetPict
        from rail_linkapp_grid_management_info a
        left join rail_linkapp_roster_personnel ss on ss.id = a.safety_sup_id
        left join rail_linkapp_roster_personnel gs on gs.id = a.grid_sec_id
        left join rail_linkapp_roster_personnel gf on gs.id = a.grid_foreman_id
        left join rail_linkapp_roster_personnel gsi on gsi.id = a.grid_siteman_name
        left join rail_linkapp_roster_personnel gm on gm.id = a.grid_meet_id
        <where>
            <if test="entity.tenantId != null and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.gridName != null and entity.gridName != ''">
                and a.grid_name like concat('%',#{entity.gridName},'%')
            </if>
            <if test="entity.safetySupName != null and entity.safetySupName != ''">
                and ss.real_name like concat('%',#{entity.safetySupName},'%')
            </if>
            <if test="entity.gridType != null and entity.gridType != ''">
                and a.grid_type like concat('%',#{entity.gridType},'%')
            </if>
            <if test="entity.id != null">
                and a.id = #{entity.id}
            </if>
            <if test="entity.engineeType != null and entity.engineeType != ''">
                and a.enginee_type = #{entity.engineeType}
            </if>
            <if test="entity.gridSitemanId != null and entity.gridSitemanId != ''">
                and a.grid_siteman_id = #{entity.gridSitemanId}
            </if>
            <if test="entity.number != null">
                and a.number = #{entity.number}
            </if>
        </where>
        GROUP BY a.id
        order by a.number asc
    </select>

    <update id="delByAnnouncementFileUrl">
      update rail_linkapp_grid_management_info a set a.announcement_file_url='' where a.id= #{id}
    </update>
</mapper>