<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.followshift.mapper.RailFollowPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.followshift.entity.RailFollowPlan">
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="user_id_" property="userId"/>
        <result column="is_deleted_" property="isDeleted"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.easylinkin.linkappapi.followshift.vo.RailFollowPlanVo" extends="BaseResultMap">
    </resultMap>

    <select id="getCurrentPlan" resultMap="BaseResultMap">
        SELECT t.*
        FROM rail_follow_plan t
        WHERE t.tenant_id_ = #{tenantId}
        ORDER BY t.create_time_ ASC
    </select>

    <delete id="deleteByTenantId">
        DELETE FROM rail_follow_plan
        WHERE tenant_id_ = #{tenantId}
    </delete>

</mapper> 