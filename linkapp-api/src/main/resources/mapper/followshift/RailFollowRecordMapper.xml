<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.followshift.mapper.RailFollowRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.followshift.entity.RailFollowRecord">
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="follow_user_id_" property="followUserId"/>
        <result column="plan_follow_date_" property="planFollowDate"/>
        <result column="grid_ids_" property="gridIds"/>
        <result column="duty_situation_summary_" property="dutySituationSummary"/>
        <result column="production_result_" property="productionResult"/>
        <result column="status_" property="status"/>
        <result column="actual_fill_date_" property="actualFillDate"/>
        <result column="fill_user_id_" property="fillUserId"/>
        <result column="fill_user_sign_url_" property="fillUserSignUrl"/>
        <result column="handover_user_id_" property="handoverUserId"/>
        <result column="handover_user_sign_url_" property="handoverUserSignUrl"/>
        <result column="handover_sign_time_" property="handoverSignTime"/>
        <result column="is_deleted_" property="isDeleted"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
        <result column="is_active_" property="isActive"/>
    </resultMap>

    <!-- VO查询映射结果 -->
    <resultMap id="VoResultMap" type="com.easylinkin.linkappapi.followshift.vo.RailFollowRecordVo" extends="BaseResultMap">
    </resultMap>

    <!-- 项目领导跟班记录日历结果映射 -->
    <resultMap id="RecordCalendarResultMap" type="com.easylinkin.linkappapi.followshift.vo.RailFollowRecordCalendarVo">
        <result column="date" property="date"/>
        <result column="date_status" property="dateStatus"/>
    </resultMap>

    <!-- 用户统计结果映射 -->
    <resultMap id="UserStatsResultMap" type="com.easylinkin.linkappapi.followshift.vo.RailFollowCalendarUserStatsVo">
        <result column="user_id" property="userId"/>
        <result column="plan_count" property="planCount"/>
        <result column="completed_count" property="completedCount"/>
        <result column="filled_count" property="filledCount"/>
    </resultMap>

    <!-- 每日状态结果映射 -->
    <resultMap id="DayStatusResultMap" type="com.easylinkin.linkappapi.followshift.vo.RailFollowCalendarDayStatusVo">
        <result column="user_id" property="userId"/>
        <result column="day" property="day"/>
        <result column="has_plan" property="hasPlan"/>
        <result column="is_filled" property="isFilled"/>
        <result column="grid_ids" property="gridIds"/>
    </resultMap>

    <!-- 月份结果映射 -->
    <resultMap id="MonthResultMap" type="com.easylinkin.linkappapi.followshift.vo.RailFollowPlanMonthVo">
        <result column="year_month" property="yearMonth"/>
        <result column="display_name" property="displayName"/>
    </resultMap>

    <!-- 分页查询跟班记录列表 -->
    <select id="queryListByPage" resultMap="VoResultMap">
        SELECT
            r.*
        FROM rail_follow_record r
        WHERE r.is_deleted_ = 0
            AND r.is_active_ = 1
        <if test="queryVo.tenantId != null and queryVo.tenantId != ''">
            AND r.tenant_id_ = #{queryVo.tenantId}
        </if>
        <if test="queryVo.followUserId != null">
            AND r.follow_user_id_ = #{queryVo.followUserId}
        </if>
        <if test="queryVo.status != null">
            AND r.status_ = #{queryVo.status}
        </if>
        <if test="queryVo.startTime != null">
            AND r.plan_follow_date_ &gt;= #{queryVo.startTime}
        </if>
        <if test="queryVo.endTime != null">
            AND r.plan_follow_date_ &lt;= #{queryVo.endTime}
        </if>
        <if test="queryVo.gridId != null and queryVo.gridId != ''">
            AND FIND_IN_SET(#{queryVo.gridId}, r.grid_ids_) > 0
        </if>
        ORDER BY r.plan_follow_date_ DESC, r.create_time_ DESC
    </select>

    <!-- 根据ID查询跟班记录详情 -->
    <select id="findById" resultMap="VoResultMap">
        SELECT
            r.*
        FROM rail_follow_record r
        WHERE r.id_ = #{id}
        AND r.is_deleted_ = 0
    </select>

    <!-- 获取指定月份的用户统计数据 -->
    <select id="getUserStatsForMonth" resultMap="UserStatsResultMap">
        SELECT
            r.follow_user_id_ as user_id,
            COUNT(CASE WHEN r.is_active_ = 1 THEN 1 END) as plan_count,
            COUNT(CASE WHEN r.status_ = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN r.status_ &gt;= 1 THEN 1 END) as filled_count
        FROM rail_follow_record r
        WHERE r.tenant_id_ = #{tenantId}
        AND DATE_FORMAT(r.plan_follow_date_, '%Y-%m') = #{yearMonth}
        AND r.is_deleted_ = 0
        GROUP BY r.follow_user_id_
        ORDER BY r.follow_user_id_
    </select>

    <!-- 获取指定月份的每日状态数据 -->
    <select id="getDayStatusForMonth" resultMap="DayStatusResultMap">
        SELECT
            r.follow_user_id_ as user_id,
            DAY(r.plan_follow_date_) as day,
            CASE WHEN r.is_active_ = 1 THEN 1 ELSE 0 END as has_plan,
            CASE WHEN r.status_ &gt;= 1 THEN 1 ELSE 0 END as is_filled,
            r.grid_ids_ as grid_ids
        FROM rail_follow_record r
        WHERE r.tenant_id_ = #{tenantId}
        AND DATE_FORMAT(r.plan_follow_date_, '%Y-%m') = #{yearMonth}
        AND r.is_deleted_ = 0
        ORDER BY r.follow_user_id_, DAY(r.plan_follow_date_)
    </select>

    <!-- 获取跟班记录的月份列表（按月份降序） -->
    <select id="getRecordMonthList" resultMap="MonthResultMap">
        SELECT DISTINCT
            DATE_FORMAT(r.plan_follow_date_, '%Y-%m') as `year_month`,
            CONCAT(YEAR(r.plan_follow_date_), '年', LPAD(MONTH(r.plan_follow_date_), 2, '0'), '月') as `display_name`
        FROM rail_follow_record r
        WHERE r.tenant_id_ = #{tenantId}
        AND r.is_deleted_ = 0
        ORDER BY DATE_FORMAT(r.plan_follow_date_, '%Y-%m') DESC
    </select>

    <!-- 获取指定月份的网格ID字符串列表 -->
    <select id="getGridIdsByMonth" resultType="java.lang.String">
        SELECT DISTINCT r.grid_ids_
        FROM rail_follow_record r
        WHERE r.tenant_id_ = #{tenantId}
        AND DATE_FORMAT(r.plan_follow_date_, '%Y-%m') = #{yearMonth}
        AND r.is_deleted_ = 0
        AND r.grid_ids_ IS NOT NULL
        AND r.grid_ids_ != ''
    </select>

    <!-- 获取项目领导跟班记录日历数据 -->
    <select id="getRecordCalendarByMonth" resultMap="RecordCalendarResultMap">
        SELECT
            DATE_FORMAT(r.plan_follow_date_, '%Y-%m-%d') as date,
            CASE
                WHEN COUNT(CASE WHEN r.status_ &gt;= 1 THEN 1 END) &gt; 0 THEN 1  -- 有填写的记录
                ELSE 2  -- 未填写或者无记录
            END as date_status
        FROM rail_follow_record r
        WHERE r.tenant_id_ = #{tenantId}
        AND DATE_FORMAT(r.plan_follow_date_, '%Y-%m') = #{yearMonth}
        AND r.is_deleted_ = 0
        GROUP BY DATE_FORMAT(r.plan_follow_date_, '%Y-%m-%d')
        ORDER BY r.plan_follow_date_
    </select>

    <!-- 批量更新当前所在月及以后，未激活记录的网格点ID（在原有基础上追加） -->
    <update id="addGridIdsFromCurrentMonth">
        UPDATE rail_follow_record
        SET grid_ids_ = CASE
            WHEN grid_ids_ IS NULL OR grid_ids_ = '' THEN #{gridId}
            ELSE CONCAT(grid_ids_, ',', #{gridId})
        END,
        modify_time_ = NOW()
        WHERE tenant_id_ = #{tenantId}
        AND is_deleted_ = 0
        AND is_active_ = 0
        AND DATE_FORMAT(plan_follow_date_, '%Y-%m') &gt;= DATE_FORMAT(CURDATE(), '%Y-%m')
    </update>

</mapper>