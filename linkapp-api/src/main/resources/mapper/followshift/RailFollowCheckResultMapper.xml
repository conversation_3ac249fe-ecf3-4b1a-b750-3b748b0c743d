<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.followshift.mapper.RailFollowCheckResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.followshift.entity.RailFollowCheckResult">
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="follow_record_id_" property="followRecordId"/>
        <result column="catalog_id_" property="catalogId"/>
        <result column="catalog_content_" property="catalogContent"/>
        <result column="parent_catalog_id_" property="parentCatalogId"/>
        <result column="parent_catalog_content_" property="parentCatalogContent"/>
        <result column="check_conclusion_" property="checkConclusion"/>
        <result column="is_deleted_" property="isDeleted"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
    </resultMap>

    <!-- VO查询映射结果 -->
    <resultMap id="VoResultMap" type="com.easylinkin.linkappapi.followshift.vo.RailFollowCheckResultVo" extends="BaseResultMap">
    </resultMap>

    <!-- 根据跟班记录ID查询检查结果 -->
    <select id="getByFollowRecordId" resultMap="VoResultMap">
        SELECT r.*
        FROM rail_follow_check_result r
        WHERE r.follow_record_id_ = #{followRecordId}
        AND r.is_deleted_ = 0
        ORDER BY r.parent_catalog_id_ ASC, r.catalog_id_ ASC
    </select>

</mapper>