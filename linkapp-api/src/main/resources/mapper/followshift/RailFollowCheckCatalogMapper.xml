<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.followshift.mapper.RailFollowCheckCatalogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.followshift.entity.RailFollowCheckCatalog">
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="parent_id_" property="parentId"/>
        <result column="content_" property="content"/>
        <result column="catalog_level_" property="catalogLevel"/>
        <result column="sort_order_" property="sortOrder"/>
        <result column="catalog_path_" property="catalogPath"/>
        <result column="is_deleted_" property="isDeleted"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
    </resultMap>

</mapper> 