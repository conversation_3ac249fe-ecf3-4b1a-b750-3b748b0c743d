<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.followshift.mapper.RailFollowRequirementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.followshift.entity.RailFollowRequirement">
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="sort_order_" property="sortOrder"/>
        <result column="content_" property="content"/>
        <result column="is_deleted_" property="isDeleted"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="use_state_" property="useState"/>
        <result column="remark_" property="remark"/>
    </resultMap>

</mapper> 