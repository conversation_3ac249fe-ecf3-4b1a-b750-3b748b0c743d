<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.iaqi.dao.IaqiStatisticMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.iaqi.entity.IaqiStatistic">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="statistic_time" jdbcType="TIMESTAMP" property="statisticTime"/>
        <result column="statistic_value" jdbcType="DOUBLE" property="statisticValue"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_iaqi_statistic
        where tenant_id = #{appIaqiStatistic.tenantId}
        order by modify_time desc,
        create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_iaqi_statistic
        where id = #{id}
    </select>

    <select id="countDaysGroupByLevel" resultType="com.easylinkin.linkappapi.iaqi.entity.vo.CountDaysVo">
        select level, count(1) as total
        from app_iaqi_statistic
        <where>
            <if test="countDaysVo.queryTimeStart != null and countDaysVo.queryTimeStart != ''">
                AND statistic_time <![CDATA[>=]]> #{countDaysVo.queryTimeStart,jdbcType=VARCHAR}
            </if>
            <if test="countDaysVo.queryTimeEnd != null and countDaysVo.queryTimeEnd != ''">
                AND statistic_time <![CDATA[<=]]> #{countDaysVo.queryTimeEnd,jdbcType=VARCHAR}
            </if>
            <if test="countDaysVo.tenantId != null and countDaysVo.tenantId != ''">
                AND app_iaqi_statistic.tenant_id = #{countDaysVo.tenantId,jdbcType=VARCHAR}
            </if>
        </where>
        group by level order by level
    </select>
</mapper>
