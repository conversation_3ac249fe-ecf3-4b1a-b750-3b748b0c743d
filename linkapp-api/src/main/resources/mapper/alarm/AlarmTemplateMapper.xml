<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.alarm.mapper.AlarmTemplateMapper">


  <select id="getAlarmTemplates" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmTemplate">
    SELECT
    lat.id,lat.name,lat.level,lat.content,lat.create_time as createTime,lat.modify_time as modifyTime
    FROM
    linkapp_alarm_template lat
    WHERE
    lat.delete_state = 1
    <if test="currentUserId!=null and currentUserId!=''">
      and lat.user_id = #{currentUserId}
    </if>
    <if test="alarmTemplate.tenantId!=null and alarmTemplate.tenantId!=''">
      and lat.tenant_id = #{alarmTemplate.tenantId}
    </if>
    <if test="alarmTemplate.name!=null and alarmTemplate.name!=''">
      AND lat.name like CONCAT('%',#{alarmTemplate.name},'%')
    </if>
    <if test="alarmTemplate.level!=null">
      AND lat.level = #{alarmTemplate.level}
    </if>
    <if test="alarmTemplate.names != null and alarmTemplate.names.size() > 0">
      AND lat.name in 
      <foreach item="item" collection="alarmTemplate.names" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    ORDER BY lat.modify_time DESC
  </select>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into linkapp_alarm_template
    (id, tenant_id, `name`, `level`, content, create_time, modify_time, user_id, delete_state
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.tenantId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
      #{item.level,jdbcType=INTEGER}, #{item.content,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.modifyTime,jdbcType=TIMESTAMP}, #{item.userId,jdbcType=VARCHAR}, #{item.deleteState,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

</mapper>
