<!--
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.isoftstone.linkappapi.alarm.mapper.AlarmRuleRelateMapper">

  &lt;!&ndash; 通用查询映射结果 &ndash;&gt;
  <resultMap id="BaseResultMap" type="com.isoftstone.linkappapi.alarm.entity.AlarmRuleRelate">
    <id column="id" property="id"/>
    <result column="alarm_rule_id" property="alarmRuleId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="device_code" property="deviceCode"/>
  </resultMap>

  <delete id="deleteForceByRuleIdAndDeviceCodes">
    delete from linkapp_alarm_rule_relate where alarm_rule_id = #{alarmRuleId} and device_code in
    <foreach item="item" index="index" collection="deleteAlarmRuleRelateDeviceCodes" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
-->
