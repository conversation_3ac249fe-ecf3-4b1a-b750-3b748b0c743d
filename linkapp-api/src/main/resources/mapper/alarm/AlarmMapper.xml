<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.alarm.mapper.AlarmMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.alarm.entity.Alarm">
    <id column="id" property="id"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="device_code" property="deviceCode"/>
    <result column="status" property="status"/>
    <result column="level" property="level"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="alarm_time" property="alarmTime"/>
    <result column="deviceName" property="deviceName"/>
    <result column="platformProjectName" property="platformProjectName"/>
    <result column="ru_name" property="ruleEngineName"/>
      <result column="content" property="content"/>
      <result column="alarm_data" property="alarmData"/>
      <result column="video_url" property="videoUrl"/>
  </resultMap>

  <resultMap id="BaseResultMapRelateDevice" type="com.easylinkin.linkappapi.alarm.entity.Alarm">
    <id column="id" property="id"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="device_code" property="deviceCode"/>
    <result column="status" property="status"/>
    <result column="level" property="level"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="alarm_time" property="alarmTime"/>
    <result column="ru_name" property="ruleEngineName"/>
    <result column="content" property="content"/>
    <result column="alarm_data" property="alarmData"/>
    <result column="video_url" property="videoUrl"/>
    <result column="source_json" property="sourceJson"/>
    <association property="device" javaType="com.easylinkin.linkappapi.device.entity.Device">
      <id column="d_id" property="id"/>
      <result column="d_code" property="code"/>
      <result column="d_name" property="name"/>
      <result column="d_device_unit_id" property="deviceUnitId"/>
      <result column="d_status" property="status"/>
      <result column="d_online_state" property="onlineState"/>
      <result column="d_area_id" property="areaId"/>
      <result column="d_site" property="site"/>
      <result column="d_area_path" property="areaPath"/>
      <result column="s_space_name" property="spaceName"/>
      <result column="s_space_id" property="spaceId"/>
      <result column="t_id" property="deviceTypeId"/>
      <result column="t_name" property="deviceTypeName"/>
      <result column="a_area_name" property="areaName"/>
      <result column="u_name" property="deviceUnitName"/>
      <result column="u_code" property="deviceUnitCode"/>
      <result column="u_version" property="deviceUnitVersion"/>
    </association>

  </resultMap>

  <select id="getAlarms" resultMap="BaseResultMapRelateDevice">
      SELECT
          al.`id`,
          al.`rule_engine_id`,
          al.`video_url`,
          al.`device_code`,
          al.`status`,
          al.`level`,
          al.`content`,
          al.`alarm_data`,
          al.`tenant_id`,
          al.`alarm_time`,
          re2.name ru_name,
           d.id               d_id,
           d.code             d_code,
           d.name             d_name,
           d.status           d_status,
           d.online_state     d_online_state,
           d.device_unit_id   d_device_unit_id,
           d.area_id          d_area_id,
           d.site             d_site,
           d.area_path        d_area_path,
           s.id               s_space_id,
           s.space_name       s_space_name,
           u.device_type_name t_name,
           u.device_type_id   t_id,
           u.version          u_version,
           a.area_name        a_area_name,
           u.name             u_name,
      <if test="alarm.requiredParam!=null and alarm.requiredParam.sourceJson!=null">
          al.source_json ,
      </if>
           u.code             u_code
    FROM linkapp_alarm al
           LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id
           LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
           LEFT JOIN linkapp_area a ON a.id = d.area_id
           LEFT JOIN linkapp_space s ON s.id = a.space_id
    <where>
      d.delete_state = 1
        and al.tenant_id = #{alarm.tenantId,jdbcType=VARCHAR}
      <if test="spacesIds != null and spacesIds.size() > 0">
        and a.space_id in
        <foreach collection="spacesIds" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="alarm.ruleEngineName != null  and alarm.ruleEngineName != ''">
        AND re2.name LIKE CONCAT('%', #{alarm.ruleEngineName}, '%')
      </if>
      <if test="alarm.status != null">
        AND al.status = #{alarm.status}
      </if>
      <if test="alarm.level != null">
        AND al.level = #{alarm.level}
      </if>
      <if test="alarm.queryTimeStart != null and alarm.queryTimeStart != ''">
        and al.alarm_time &gt;= #{alarm.queryTimeStart}
      </if>
      <if test="alarm.queryTimeEnd != null and alarm.queryTimeEnd != ''">
        and al.alarm_time &lt;= #{alarm.queryTimeEnd}
      </if>
      <if test="alarm.device.id != null and alarm.device.id != ''">
        and d.id = #{alarm.device.id}
      </if>
      <if test="alarm.device.spaceId != null and alarm.device.spaceId != ''">
        and s.id = #{alarm.device.spaceId}
      </if>
        <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(alarm.device.areaIds)">
            and d.area_id in
            <foreach collection="alarm.device.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
      <if test="alarm.device.areaPath != null and alarm.device.areaPath != ''">
        and (d.area_path = #{alarm.device.areaPath} or d.area_path like concat(#{alarm.device.areaPath},':%'))
      </if>
      <if test="alarm.device.deviceTypeId != null and alarm.device.deviceTypeId != ''">
        and u.device_type_id = #{alarm.device.deviceTypeId}
      </if>
      <if test="alarm.device.areaId != null and alarm.device.areaId != ''">
        and d.area_id = #{alarm.device.areaId}
      </if>
      <if test="alarm.device.queryTimeStart != null and alarm.device.queryTimeStart != ''">
        and d.modify_time >= #{alarm.device.queryTimeStart}
      </if>
      <if test="alarm.device.queryTimeEnd != null and alarm.device.queryTimeEnd != ''">
        and d.modify_time &lt;= #{alarm.device.queryTimeEnd}
      </if>
      <if test="alarm.device.name != null and alarm.device.name != ''">
        and d.name like CONCAT('%', #{alarm.device.name}, '%')
      </if>
      <if test="alarm.device.code != null and alarm.device.code != ''">
        and d.code like CONCAT('%', #{alarm.device.code}, '%')
      </if>
      <if test="alarm.device.status != null">
        and d.status = #{alarm.device.status}
      </if>
      <if test="alarm.device.onlineState != null">
        and d.online_state = #{alarm.device.onlineState}
      </if>
      <if test="alarm.device.projectId != null and alarm.device.projectId != ''">
        and d.project_id = #{alarm.device.projectId}
      </if>
      <if test="alarm.device.deviceUnitId != null and alarm.device.deviceUnitId != ''">
        and d.device_unit_id = #{alarm.device.deviceUnitId}
      </if>
      <if test="alarm.device.creator != null and alarm.device.creator != ''">
        and d.creator like CONCAT('%', #{alarm.device.creator}, '%')
      </if>
--       设备类型名称查询
      <if test="alarm.deviceTypeName != null and alarm.deviceTypeName != ''">
        and u.device_type_name = #{alarm.deviceTypeName}
      </if>
--       设备类型名称集合查询
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(alarm.deviceTypeNames)">
        and u.device_type_name in
        <foreach collection="alarm.deviceTypeNames" item="deviceTypeName" open="(" separator="," close=")">
          #{deviceTypeName}
        </foreach>
      </if>
    </where>
    ORDER BY al.alarm_time DESC
  </select>

  <!-- 获取最近20分钟的告警数据 -->
  <select id="getRecentAlarms" resultMap="BaseResultMapRelateDevice">
      SELECT
          al.`id`,
          al.`rule_engine_id`,
          al.`video_url`,
          al.`device_code`,
          al.`status`,
          al.`level`,
          al.`content`,
          al.`alarm_data`,
          al.`tenant_id`,
          al.`alarm_time`,
          re2.name ru_name,
           d.id               d_id,
           d.code             d_code,
           d.name             d_name,
           d.status           d_status,
           d.online_state     d_online_state,
           d.device_unit_id   d_device_unit_id,
           d.area_id          d_area_id,
           d.site             d_site,
           d.area_path        d_area_path,
           s.id               s_space_id,
           s.space_name       s_space_name,
           u.device_type_name t_name,
           u.device_type_id   t_id,
           u.version          u_version,
           a.area_name        a_area_name,
           u.name             u_name,
           u.code             u_code
    FROM linkapp_alarm al
           LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id
           LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
           LEFT JOIN linkapp_area a ON a.id = d.area_id
           LEFT JOIN linkapp_space s ON s.id = a.space_id
    <where>
      d.delete_state = 1
        and al.tenant_id = #{alarm.tenantId,jdbcType=VARCHAR}
        and al.alarm_time >= #{twentyMinutesAgo,jdbcType=TIMESTAMP}
      <if test="spacesIds != null and spacesIds.size() > 0">
        and a.space_id in
        <foreach collection="spacesIds" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    ORDER BY al.alarm_time DESC
  </select>

    <select id="getAlarmsGlobal" resultMap="BaseResultMap">
        SELECT
        t.platform_project_name as platformProjectName,
        d.name             deviceName,
        re2.name ru_name,
        al.`alarm_time`
        FROM linkapp_alarm al
        left join linkapp_tenant t on al.tenant_id = t.id
        LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id
        LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
        <where>
            d.delete_state = 1
            <if test="alarm.ruleEngineName != null  and alarm.ruleEngineName != ''">
                AND re2.name LIKE CONCAT('%', #{alarm.ruleEngineName}, '%')
            </if>
            <if test="alarm.platformProjectName != null  and alarm.platformProjectName != ''">
                AND t.platform_project_name LIKE CONCAT('%', #{alarm.platformProjectName}, '%')
            </if>
            <if test="alarm.status != null">
                AND al.status = #{alarm.status}
            </if>
            <if test="alarm.level != null">
                AND al.level = #{alarm.level}
            </if>
            <if test="alarm.queryTimeStart != null and alarm.queryTimeStart != ''">
                and al.alarm_time &gt;= #{alarm.queryTimeStart}
            </if>
            <if test="alarm.queryTimeEnd != null and alarm.queryTimeEnd != ''">
                and al.alarm_time &lt;= #{alarm.queryTimeEnd}
            </if>
        </where>
        ORDER BY al.alarm_time DESC
    </select>


  <!--  电力监控页告警日志-->
  <select id="getDistributionAlarmPage" resultMap="BaseResultMapRelateDevice">
    SELECT al.id,
           al.device_code,
           al.status,
           al.alarm_time,
           re2.name as ru_name
    FROM linkapp_alarm al
           inner join distribution_room_ref_device drrd on drrd.device_code = al.device_code
           LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id
           LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
           LEFT JOIN linkapp_area a ON a.id = d.area_id
    <where>
      and al.tenant_id = #{alarm.tenantId,jdbcType=VARCHAR}
      and drrd.distribution_room_id = #{alarm.distributionRoomId}
      <if test="spacesIds != null and spacesIds.size() > 0">
        and a.space_id in
        <foreach collection="spacesIds" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    ORDER BY al.alarm_time DESC
  </select>

  <resultMap id="ResultDetail2Map" type="com.easylinkin.linkappapi.alarm.entity.Alarm">
    <id column="id" property="id"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="device_code" property="deviceCode"/>
    <result column="source_json" property="sourceJson"/>
    <result column="status" property="status"/>
    <result column="level" property="level"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="alarm_time" property="alarmTime"/>

    <result column="ru_name" property="ruleEngineName"/>
    <result column="content" property="content"/>
      <result column="alarm_data" property="alarmData"/>
    <result column="video_url" property="videoUrl"/>


    <association property="alarmProcess" javaType="com.easylinkin.linkappapi.alarm.entity.AlarmProcess">
      <result column="handler_id" property="handlerId"/>
      <result column="mistake_flag" property="mistakeFlag"/>
      <result column="process_result" property="processResult"/>
      <result column="process_time" property="processTime"/>
      <result column="p_username" property="userName"/>
    </association>
    <association property="device" javaType="com.easylinkin.linkappapi.device.entity.Device">
      <id column="d_id" property="id"/>
      <result column="d_code" property="code"/>
      <result column="d_name" property="name"/>
      <result column="d_device_unit_id" property="deviceUnitId"/>
      <result column="d_status" property="status"/>
      <result column="d_online_state" property="onlineState"/>
      <result column="d_area_id" property="areaId"/>
      <result column="d_site" property="site"/>
      <result column="d_longitude" property="longitude"/>
      <result column="d_latitude" property="latitude"/>
      <result column="d_area_path" property="areaPath"/>
      <result column="s_space_name" property="spaceName"/>
      <result column="s_space_id" property="spaceId"/>
      <result column="t_id" property="deviceTypeId"/>
      <result column="t_name" property="deviceTypeName"/>
      <result column="a_area_name" property="areaName"/>
      <result column="u_name" property="deviceUnitName"/>
      <result column="u_code" property="deviceUnitCode"/>
      <result column="u_version" property="deviceUnitVersion"/>
    </association>
  </resultMap>

  <!--  查询详情-->
  <select id="getDetail2" resultMap="ResultDetail2Map">
    SELECT al.*,
           re2.name                         ru_name,
           p.mistake_flag,
           p.process_result,
           p.process_time,
           IFNULL(us.nickname, us.username) p_username,
           d.id                             d_id,
           d.code                           d_code,
           d.name                           d_name,
           d.status                         d_status,
           d.online_state                   d_online_state,
           d.device_unit_id                 d_device_unit_id,
           d.area_id                        d_area_id,
           d.site                           d_site,
           d.area_path                      d_area_path,
           d.longitude as                   d_longitude,
           d.latitude  as                   d_latitude,
           s.id                             s_space_id,
           s.space_name                     s_space_name,
           u.device_type_name               t_name,
           u.device_type_id                 t_id,
           u.version                        u_version,
           a.area_name                      a_area_name,
           u.name                           u_name,
           u.code                           u_code
    FROM linkapp_alarm al

           LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id

           LEFT JOIN linkapp_alarm_process p ON p.alarm_id = al.id
           left join linkapp_user us on us.id = p.handler_id
           LEFT JOIN linkapp_device d ON al.device_code = d.code and d.delete_state = 1
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
           LEFT JOIN linkapp_area a ON a.id = d.area_id
           LEFT JOIN linkapp_space s ON s.id = a.space_id
    <where>
      al.id = #{alarm.id}
    </where>
  </select>


  <resultMap id="BaseResultDetail" type="com.easylinkin.linkappapi.alarm.entity.Alarm">
    <id column="id" property="id"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="device_code" property="deviceCode"/>
    <result column="source_json" property="sourceJson"/>
    <result column="status" property="status"/>
    <result column="level" property="level"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="alarm_time" property="alarmTime"/>
    <result column="ru_name" property="ruleEngineName"/>
    <result column="content" property="content"/>
      <result column="alarm_data" property="alarmData"/>
    <result column="video_url" property="videoUrl"/>

    <association property="device" javaType="com.easylinkin.linkappapi.device.entity.Device">
      <id column="d_id" property="id"/>
      <result column="d_code" property="code"/>
      <result column="d_name" property="name"/>
      <result column="d_device_unit_id" property="deviceUnitId"/>
      <result column="d_status" property="status"/>
      <result column="d_online_state" property="onlineState"/>
      <result column="d_area_id" property="areaId"/>
      <result column="d_site" property="site"/>
      <result column="d_area_path" property="areaPath"/>
      <result column="s_space_name" property="spaceName"/>
      <result column="s_space_id" property="spaceId"/>
      <result column="t_id" property="deviceTypeId"/>
      <result column="t_name" property="deviceTypeName"/>
      <result column="a_area_name" property="areaName"/>
      <result column="u_name" property="deviceUnitName"/>
      <result column="u_code" property="deviceUnitCode"/>
      <result column="u_version" property="deviceUnitVersion"/>
    </association>
    <!--    <association property="alarmRule" javaType="com.easylinkin.linkappapi.alarm.entity.AlarmRule">
          <result column="ru_name" property="name"/>
          <result column="ru_level" property="level"/>
          <result column="ru_alarm_content" property="alarmContent"/>
        </association>-->

    <association property="alarmProcess" javaType="com.easylinkin.linkappapi.alarm.entity.AlarmProcess">
      <result column="handler_id" property="handlerId"/>
      <result column="mistake_flag" property="mistakeFlag"/>
      <result column="process_result" property="processResult"/>
      <result column="process_time" property="processTime"/>
      <result column="p_username" property="userName"/>
    </association>
  </resultMap>

  <!--  查询详情-->
  <select id="getDetail" resultMap="BaseResultDetail">
    SELECT al.*,
           re2.name                         ru_name,
           p.mistake_flag,
           p.process_result,
           p.process_time,
           IFNULL(us.nickname, us.username) p_username,
           d.id                             d_id,
           d.code                           d_code,
           d.name                           d_name,
           d.status                         d_status,
           d.online_state                   d_online_state,
           d.device_unit_id                 d_device_unit_id,
           d.area_id                        d_area_id,
           d.site                           d_site,
           d.area_path                      d_area_path,
           s.id                             s_space_id,
           s.space_name                     s_space_name,
           u.device_type_name               t_name,
           u.device_type_id                 t_id,
           a.area_name                      a_area_name,
           u.name                           u_name,
           u.version                        u_version,
           u.code                           u_code
    FROM linkapp_alarm al

           LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id

           LEFT JOIN linkapp_alarm_process p ON p.alarm_id = al.id
           left join linkapp_user us on us.id = p.handler_id
           LEFT JOIN linkapp_device d ON al.device_code = d.code
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
           LEFT JOIN linkapp_area a ON a.id = d.area_id
           LEFT JOIN linkapp_space s ON s.id = a.space_id
    <where>
      al.id = #{alarm.id}
    </where>
  </select>

  <select id="getAlarmStatisticsByRule" resultType="java.util.Map">
    SELECT re2.name AS ruleName,
           COUNT(a.id) alarmCount
    FROM linkapp_alarm a

           inner JOIN rule_engine re2 ON a.rule_engine_id = re2.id
    WHERE re2.name is not null
      and a.tenant_id = #{alarm.tenantId}
      <if test="alarm.status != null">
          AND a.status = #{alarm.status}
      </if>
      <if test="alarm.queryTimeEnd">
          AND a.alarm_time &lt; #{alarm.queryTimeEnd}
      </if>
    GROUP BY ruleName
    ORDER BY alarmCount DESC
  </select>

  <select id="getAlarmStatisticsByStatus" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmStatistic">
    SELECT DATE_FORMAT(a.alarmTime, #{alarm.statisticsTimeType}) AS alarmTime,
           SUM(a.oneDayAddCount)                                 AS oneDayAddCount,
           a.tenantId                                            AS tenantId,
           a.spaceId                                             AS spaceId,
           a.status                                              AS status
    FROM linkapp_alarm_info a
    <where>
      <if test="alarm.status != null">
        AND a.status = #{alarm.status}
      </if>
      <if test="alarm.queryTimeEnd">
        AND a.alarmTime &lt; #{alarm.queryTimeEnd}
      </if>
      <if test="tenantId">
        AND a.tenantId = #{tenantId}
      </if>
      <if test="spacesIds != null and spacesIds.size() > 0">
        and a.spaceId in
        <foreach collection="spacesIds" separator="," open="(" close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>
    GROUP BY a.tenantId, a.status, a.alarmTime
    ORDER BY a.alarmTime desc
    LIMIT 0,7
  </select>

    <select id="getAlarmStatisticsByStatus2" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmStatistic">
        SELECT
        DATE_FORMAT(a.alarm_time, '%Y-%m-%d') AS alarmTime,
        count(a.id) AS oneDayAddCount,
        a.tenant_Id AS tenantId,
        a. STATUS AS STATUS
        FROM
        linkapp_alarm a

        <where>
            <if test="alarm.status != null">
                AND a.status = #{alarm.status}
            </if>
            <if test="alarm.queryTimeEnd">
                AND a.alarm_time &lt; #{alarm.queryTimeEnd}
            </if>
            <if test="tenantId">
                AND a.tenant_id = #{tenantId}
            </if>
        </where>
        GROUP BY
        alarmTime
        ORDER BY
        a.alarm_Time DESC
        LIMIT 0, 7
    </select>

  <select id="getAlarmStatisticsAudit" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmInfo">
    SELECT
    <if test="queryTimeEnd != null">
      #{queryTimeEnd} AS alarmTime,
    </if>
    <if test="queryTimeEnd == null">
      CURDATE() AS alarmTime,
    </if>
    COUNT(*)      AS oneDayAddCount,
    d.tenant_id   AS tenantId,
    area.space_id AS spaceId,
    a.status      AS STATUS
      FROM `linkapp_alarm` a
             LEFT JOIN `linkapp_device` d
        ON a.device_code = d.code
             LEFT JOIN `linkapp_area` AREA
        ON area.id = d.area_id
    <where>
      <if test="queryTimeEnd != null">
        AND a.alarm_time &lt;= #{queryTimeEnd}
      </if>
      <if test="tenantId != null and tenantId != ''">
        AND a.tenant_id = #{tenantId}
      </if>
      and d.delete_state = 1
    </where>
    GROUP BY a.status, d.tenant_id, area.space_id
    ORDER BY d.tenant_id, area.space_id DESC
  </select>

  <select id="selectAlarmInfoList" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmInfo">
    select *
    from linkapp_alarm_info
    <where>
      <if test="alarmInfo.alarmTime != null">
        and alarmTime = #{alarmInfo.alarmTime}
      </if>
      <if test="spacesIds != null and spacesIds.size() > 0">
        and spaceId in
        <foreach collection="spacesIds" separator="," open="(" close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="tenantId != null">
        and tenantId = #{tenantId}
      </if>
      <if test="alarmInfo.status != null">
        and status = #{alarmInfo.status}
      </if>
    </where>
    ORDER BY alarmTime DESC
    LIMIT 0,
      7
  </select>

  <insert id="insertAlarmInfo">
    insert into linkapp_alarm_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="alarmTime != null">
        alarmTime,
      </if>
      <if test="oneDayAddCount != null">
        oneDayAddCount,
      </if>
      <if test="spaceId != null">
        spaceId,
      </if>
      <if test="tenantId != null">
        tenantId,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="alarmTime != null">
        #{alarmTime},
      </if>
      <if test="oneDayAddCount != null">
        #{oneDayAddCount},
      </if>
      <if test="spaceId != null">
        #{spaceId},
      </if>
      <if test="tenantId != null">
        #{tenantId},
      </if>
      <if test="status != null">
        #{status},
      </if>
    </trim>
  </insert>


  <update id="updateAlarmInfo">
    UPDATE
      linkapp_alarm_info
    <set>
      <if test="oneDayAddCount != null">
        oneDayAddCount = #{oneDayAddCount},
      </if>
      <if test="spaceId != null">
        spaceId = #{spaceId},
      </if>
      <if test="tenantId != null">
        tenantId = #{tenantId},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
    </set>
    <where>
      <if test="alarmTime != null">
        and alarmTime = #{alarmTime}
      </if>
      <if test="spaceId != null">
        and spaceId = #{spaceId}
      </if>
      <if test="tenantId != null">
        and tenantId = #{tenantId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
    </where>
  </update>

  <select id="getAlarmPageByAsset" resultMap="BaseResultMapRelateDevice">
    SELECT al.*,
           re2.name AS        ru_name,
           d.id               d_id,
           d.code             d_code,
           d.name             d_name,
           d.status           d_status,
           d.online_state     d_online_state,
           d.device_unit_id   d_device_unit_id,
           d.area_id          d_area_id,
           d.site             d_site,
           d.area_path        d_area_path,
           u.device_type_name t_name,
           u.device_type_id   t_id,
           u.name             u_name,
           u.version          u_version,
           u.code             u_code
    FROM linkapp_alarm al
           LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id

           LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id

    <where>
      d.delete_state = 1
      <if test="deviceIds != null and deviceIds.size() > 0">
        AND d.id in
        <foreach collection="deviceIds" item="arr" index="no" open="("
          separator="," close=")">
          #{arr}
        </foreach>
      </if>
    </where>
    ORDER BY al.alarm_time DESC
  </select>

  <select id="getLatestAlarmByAlarm" resultMap="BaseResultMap">
    SELECT *
    FROM linkapp_alarm a
      WHERE
      a.device_code = #{alarm.deviceCode}
    <!--    AND a.alarm_rule_id = #{alarm.alarmRuleId}-->
    AND a.rule_engine_id = #{alarm.ruleEngineId}
      ORDER BY alarm_time DESC
      limit 1 ;
  </select>

  <!--  <update id="lockLatestAlarmByAlarm">
      UPDATE linkapp_alarm a set a.id=a.id
      WHERE
      a.device_code = #{alarm.deviceCode}
      AND a.alarm_rule_id = #{alarm.alarmRuleId}
      ORDER BY create_time DESC limit 1 ;
    </update>-->

  <select id="countAlarmByDeviceCodeAndStatus" resultType="int">
    select COUNT(a.id)
    from linkapp_alarm a
    <where>
      a.device_code = #{deviceCode}
        and a.status = #{status}
    </where>
  </select>


    <select id="countAlarmByAreaPathAndStatus" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmCountByArea">
        SELECT
            d.area_id areaId,
            a.area_name areaName,
            d.area_path areaPath,
            count(*) alarmCount
        FROM linkapp_alarm al
                 LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id
                 LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
                 LEFT JOIN linkapp_area a ON a.id = d.area_id
                 LEFT JOIN linkapp_space s ON s.id = a.space_id
        where
            d.delete_state = 1
            and al.tenant_id = #{alarm.tenantId}
            and al.status = #{alarm.status}
            <if test="alarm.queryTimeStart != null and alarm.queryTimeStart != ''">
                and al.alarm_time &gt;= #{alarm.queryTimeStart}
            </if>
            <if test="alarm.queryTimeEnd != null and alarm.queryTimeEnd != ''">
                and al.alarm_time &lt;= #{alarm.queryTimeEnd}
            </if>
            <if test="alarm.device.areaPath != null and alarm.device.areaPath != ''">
                and (d.area_path = #{alarm.device.areaPath} or d.area_path like concat(#{alarm.device.areaPath},':%'))
            </if>
        group by d.area_id;
    </select>

    <select id="countAlarmByStatusAndAreaIds" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmCountByArea">
        SELECT
            DATE_FORMAT(al.alarm_time,'%Y-%m') month,
            count(*) alarmCount
        FROM
            linkapp_alarm al
            LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id
            LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
            LEFT JOIN linkapp_area a ON a.id = d.area_id
            LEFT JOIN linkapp_space s ON s.id = a.space_id
        where
        d.delete_state = 1
        and al.tenant_id = #{alarm.tenantId}
        and al.status = #{alarm.status}
        <if test="alarm.queryTimeStart != null and alarm.queryTimeStart != ''">
            and al.alarm_time &gt;= #{alarm.queryTimeStart}
        </if>
        <if test="alarm.queryTimeEnd != null and alarm.queryTimeEnd != ''">
            and al.alarm_time &lt;= #{alarm.queryTimeEnd}
        </if>
        <if test="alarm.device.areaPath != null and alarm.device.areaPath != ''">
            and (d.area_path = #{alarm.device.areaPath} or d.area_path like concat(#{alarm.device.areaPath},':%'))
        </if>
        <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(alarm.device.areaIds)">
            and d.area_id in
            <foreach collection="alarm.device.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        group by DATE_FORMAT(al.alarm_time,'%Y-%m');
    </select>

  <select id="selectVisibleAlarmCommonUserIds" resultType="java.lang.String">
    select lu.id
    from linkapp_user lu
           inner join linkapp_user_ref_role lurr on lu.id = lurr.user_id
           inner join linkapp_role_space lrs on lurr.role_id = lrs.role_id
    <where>
      lu.id in
      <foreach collection="userIds" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
      and lu.type = '2'
      and lu.tenant_id = #{alarm.tenantId,jdbcType=VARCHAR}
    </where>
    and (select la.space_id
         from linkapp_device ld
                inner join linkapp_area la on ld.area_id = la.id
         where ld.code = #{alarm.deviceCode}
           and ld.delete_state = 1) = lrs.space_id
  </select>

  <select id="getNoticeInfoAlarm" resultType="com.easylinkin.linkappapi.alarm.entity.Alarm">
    select la.*,
           ld.area_path         as deviceAreaPath,
           ldu.device_type_name as deviceTypeName,
           re.name              as ruleEngineName
    from linkapp_alarm la
           left join linkapp_device ld on ld.code = la.device_code and ld.delete_state = 1
           left join linkapp_device_unit ldu on ld.device_unit_id = ldu.id
           left join rule_engine re on la.rule_engine_id = re.id
    <where>
      la.id = #{id}
    </where>
  </select>

  <select id="getAlarmVos" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmVo">
    SELECT d.name                                                                    deviceName,
           d.code                                                                    deviceCode,
           u.device_type_name                                                        deviceTypeName,
           d.area_path                                                               deviceAreaPath,
           re2.name                                                                  ruleEngineName,
           if(al.LEVEL = 3, '低', if(al.level = 2, '中', if(al.level = 1, '高', '未知'))) LEVEL,
           al.content                                                                content,
           al.alarm_data                                                             alarmData,
           al.source_json                                                            sourceJson,
           al.alarm_time                                                             alarmTime,
           if(al.status = 2, '已处理', if(al.status = 1, '未处理', '未知')) as               processStatus,
           lu.nickname                                                               processUserName,
           lap.process_result                                                        processResult,
           if(lap.mistake_flag = 1, '是', if(lap.mistake_flag = 0, '否', '未知'))        mistakeFlag,
           lap.process_time                                                          processTime
    FROM linkapp_alarm al
           LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id
           LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
           LEFT JOIN linkapp_area a ON a.id = d.area_id
           LEFT JOIN linkapp_space s ON s.id = a.space_id
           left join linkapp_alarm_process lap on al.id = lap.alarm_id
           left join linkapp_user lu on lap.handler_id = lu.id
    <where>
      d.delete_state = 1
        and al.tenant_id = #{alarm.tenantId,jdbcType=VARCHAR}
      <if test="spacesIds != null and spacesIds.size() > 0">
        and a.space_id in
        <foreach collection="spacesIds" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="alarm.ruleEngineName != null  and alarm.ruleEngineName != ''">
        AND re2.name LIKE CONCAT('%', #{alarm.ruleEngineName}, '%')
      </if>
      <if test="alarm.status != null">
        AND al.status = #{alarm.status}
      </if>
      <if test="alarm.level != null">
        AND al.level = #{alarm.level}
      </if>
      <if test="alarm.queryTimeStart != null and alarm.queryTimeStart != ''">
        and al.alarm_time &gt;= #{alarm.queryTimeStart}
      </if>
      <if test="alarm.queryTimeEnd != null and alarm.queryTimeEnd != ''">
        and al.alarm_time &lt;= #{alarm.queryTimeEnd}
      </if>

      <if test="alarm.device.id != null and alarm.device.id != ''">
        and d.id = #{alarm.device.id}
      </if>
      <if test="alarm.device.spaceId != null and alarm.device.spaceId != ''">
        and s.id = #{alarm.device.spaceId}
      </if>
      <if test="alarm.device.areaPath != null and alarm.device.areaPath != ''">
        and (d.area_path = #{alarm.device.areaPath} or d.area_path like concat(#{alarm.device.areaPath},':%'))
      </if>
      <if test="alarm.device.deviceTypeId != null and alarm.device.deviceTypeId != ''">
        and u.device_type_id = #{alarm.device.deviceTypeId}
      </if>
      <if test="alarm.device.areaId != null and alarm.device.areaId != ''">
        and d.area_id = #{alarm.device.areaId}
      </if>
      <if test="alarm.device.queryTimeStart != null and alarm.device.queryTimeStart != ''">
        and d.modify_time >= #{alarm.device.queryTimeStart}
      </if>
      <if test="alarm.device.queryTimeEnd != null and alarm.device.queryTimeEnd != ''">
        and d.modify_time &lt;= #{alarm.device.queryTimeEnd}
      </if>

      <if test="alarm.device.name != null and alarm.device.name != ''">
        and d.name like CONCAT('%', #{alarm.device.name}, '%')
      </if>

      <if test="alarm.device.code != null and alarm.device.code != ''">
        and d.code like CONCAT('%', #{alarm.device.code}, '%')
      </if>

      <if test="alarm.device.status != null">
        and d.status = #{alarm.device.status}
      </if>
      <if test="alarm.device.onlineState != null">
        and d.online_state = #{alarm.device.onlineState}
      </if>
      <if test="alarm.device.projectId != null and alarm.device.projectId != ''">
        and d.project_id = #{alarm.device.projectId}
      </if>
      <if test="alarm.device.deviceUnitId != null and alarm.device.deviceUnitId != ''">
        and d.device_unit_id = #{alarm.device.deviceUnitId}
      </if>
      <if test="alarm.device.creator != null and alarm.device.creator != ''">
        and d.creator like CONCAT('%', #{alarm.device.creator}, '%')
      </if>
    </where>
    ORDER BY al.alarm_time DESC
  </select>

  <select id="getAlarmsForBigScreenPageGlobal" resultMap="BaseResultMapRelateDevice">
    SELECT
    al.`id`,
    al.`rule_engine_id`,
    al.`video_url`,
    al.`device_code`,
    al.`status`,
    al.`level`,
    al.`content`,
    al.`alarm_data`,
    al.`tenant_id`,
    al.`alarm_time`,
    re2.name ru_name,
    d.id               d_id,
    d.code             d_code,
    d.name             d_name,
    d.status           d_status,
    d.online_state     d_online_state,
    d.device_unit_id   d_device_unit_id,
    d.area_id          d_area_id,
    d.site             d_site,
    d.area_path        d_area_path,
    s.id               s_space_id,
    s.space_name       s_space_name,
    u.device_type_name t_name,
    u.device_type_id   t_id,
    u.version          u_version,
    a.area_name        a_area_name,
    u.name             u_name,
    u.code             u_code
    FROM linkapp_alarm al
    LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id
    LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
    LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
    LEFT JOIN linkapp_area a ON a.id = d.area_id
    LEFT JOIN linkapp_space s ON s.id = a.space_id
    <where>
      d.delete_state = 1
      and al.tenant_id = #{tenantId}
      <if test="deviceCodes != null and deviceCodes.size() > 0">
        and al.device_code in
        <foreach collection="deviceCodes" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    ORDER BY al.alarm_time DESC
  </select>
  <select id="countAlarmByDeviceCodesGlobal" resultType="java.lang.Integer">
    SELECT
    count(1)
    FROM linkapp_alarm al
    LEFT JOIN rule_engine re2 ON al.rule_engine_id = re2.id
    LEFT JOIN linkapp_device d ON al.device_code = d.code AND d.delete_state = 1
    LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
    LEFT JOIN linkapp_area a ON a.id = d.area_id
    LEFT JOIN linkapp_space s ON s.id = a.space_id
    <where>
      d.delete_state = 1
      and al.tenant_id = #{tenantId}
      <if test="deviceCodes != null and deviceCodes.size() > 0">
        and al.device_code in
        <foreach collection="deviceCodes" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="ruleEngineIds != null and ruleEngineIds.size() > 0">
        and re2.id in
        <foreach collection="ruleEngineIds" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="startTime !=null">
        and al.alarm_time >= #{startTime}
      </if>
      <if test="endTime !=null">
        and al.alarm_time &lt;= #{endTime}
      </if>
    </where>
  </select>

    <select id="countAlarmByRule" parameterType="com.easylinkin.linkappapi.alarm.vo.AlarmDeviceVo"
            resultType="java.util.Map">
        select la.rule_engine_id as ruleId,
               count(*)          as num
        from linkapp_alarm la,
             linkapp_device ld
        where la.device_code = ld.code
        <if test="deviceInfo != null">
            <if test="deviceInfo.id != null and deviceInfo.id != ''">
                and ld.id = #{device.id}
            </if>
            <if test="deviceInfo.code != null and deviceInfo.code != ''">
                and la.device_code = #{deviceInfo.code}
            </if>
        </if>
        <if test="startTime != null">
            <![CDATA[
            and la.alarm_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and la.alarm_time <= #{endTime}
            ]]>
        </if>
        <if test="tenantId != null and tenantId != ''">
            and la.tenant_id = #{tenantId}
        </if>
        <if test="ruleIds != null and ruleIds.size() != 0">
            and la.rule_engine_id in
            <foreach collection="ruleIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by la.rule_engine_id
    </select>

    <select id="countAlarmByDay" parameterType="com.easylinkin.linkappapi.alarm.vo.AlarmDeviceVo"
            resultType="java.util.Map">
        select date_format(la.alarm_time, '%Y-%m-%d') as alarmTime,
               count(*)                               as num
        from linkapp_alarm la,
             linkapp_device ld
        where la.device_code = ld.code
        <if test="deviceInfo != null">
            <if test="deviceInfo.id != null and deviceInfo.id != ''">
                and ld.id = #{device.id}
            </if>
            <if test="deviceInfo.code != null and deviceInfo.code != ''">
                and la.device_code = #{deviceInfo.code}
            </if>
        </if>
        <if test="startTime != null">
            <![CDATA[
            and la.alarm_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and la.alarm_time <= #{endTime}
            ]]>
        </if>
        <if test="tenantId != null and tenantId != ''">
            and la.tenant_id = #{tenantId}
        </if>
        <if test="ruleIds != null and ruleIds.size() != 0">
            and la.rule_engine_id in
            <foreach collection="ruleIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by date_format(la.alarm_time, '%Y-%m-%d')
    </select>

    <select id="countAlarmByRuleAndParamGlobal" parameterType="com.easylinkin.linkappapi.alarm.vo.AlarmDeviceVo"
            resultType="java.util.Map">
        select laa.rule_engine_id,
               (select e.name
                from rule_engine e
                where e.id = laa.rule_engine_id) as alarmName,
               count(*)                          as num
        from
        (
        select distinct la.*
        from linkapp_device ld,
             linkapp_alarm la,
             linkapp_device_unit ldu,
             rule_engine re,
             rule_trigger rt
        where rt.rule_engine_id = la.rule_engine_id
          and ld.device_unit_id = rt.device_unit_id
          and re.id = rt.rule_engine_id
          and ldu.id = rt.device_unit_id
          and rt.`type` = 1
        <if test="tenantId != null and tenantId != ''">
            and la.tenant_id = #{tenantId}
        </if>
        <if test="deviceInfo != null ">
            <if test="deviceInfo.id != null and deviceInfo.id != ''">
                and ld.id = #{deviceInfo.id}
            </if>
            <if test="deviceInfo.code != null and deviceInfo.code != ''">
                and la.device_code = #{deviceInfo.code}
            </if>
        </if>
        <if test="deviceTypeNameList != null and deviceTypeNameList.size() != 0">
            and ldu.device_type_name in
            <foreach collection="deviceTypeNameList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            <![CDATA[
            and la.alarm_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and la.alarm_time <= #{endTime}
            ]]>
        </if>
        ) laa
        group by laa.rule_engine_id
    </select>

    <select id="countAlarmByDayAndParamGlobal" parameterType="com.easylinkin.linkappapi.alarm.vo.AlarmDeviceVo"
            resultType="java.util.Map">
        select date_format(laa.alarm_time, '%Y-%m-%d') as alarmTime,
               count(*)                                as num
        from
        (
        select distinct la.*
        from linkapp_device ld,
             linkapp_alarm la,
             linkapp_device_unit ldu,
             rule_engine re,
             rule_trigger rt
        where rt.rule_engine_id = la.rule_engine_id
          and ld.device_unit_id = rt.device_unit_id
          and re.id = rt.rule_engine_id
          and ldu.id = rt.device_unit_id
          and rt.`type` = 1
        <if test="tenantId != null and tenantId != ''">
            and la.tenant_id = #{tenantId}
        </if>
        <if test="deviceInfo != null">
            <if test="deviceInfo.id != null and deviceInfo.id != ''">
                and ld.id = #{deviceInfo.id}
            </if>
            <if test="deviceInfo.code != null and deviceInfo.code != ''">
                and la.device_code = #{deviceInfo.code}
            </if>
        </if>
        <if test="deviceTypeNameList != null and deviceTypeNameList.size() != 0">
            and ldu.device_type_name in
            <foreach collection="deviceTypeNameList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            <![CDATA[
            and la.alarm_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and la.alarm_time <= #{endTime}
            ]]>
        </if>
        ) laa
        group by date_format(laa.alarm_time, '%Y-%m-%d')
    </select>

    <select id="selectAlarmStatisticGlobal" resultType="com.easylinkin.linkappapi.openapi.dto.BigScreenStatisticDTO">
        select name, countNum, round(countNum * 100 / tmp2.total, 2) as rate from  (select re.name      as name,
                                                                                 count(la.id) as countNum
                                                                          from linkapp_alarm la
                                                                                   inner join rule_engine re on la.rule_engine_id = re.id
            left join linkapp_tenant lt on
                la.tenant_id = lt.id
        <where>
            lt.id is not null
            <if test="start != null">
                and la.alarm_time <![CDATA[>=]]> #{start,jdbcType=TIMESTAMP}
            </if>
            <if test="end != null">
                and la.alarm_time <![CDATA[<]]> #{end,jdbcType=TIMESTAMP}
            </if>
        </where>
        group by re.name) tmp1 inner join
        (select count(*) as total
         from linkapp_alarm la
        inner join rule_engine re on la.rule_engine_id = re.id
        left join linkapp_tenant lt on
            la.tenant_id = lt.id
        <where>
            lt.id is not null
            <if test="start != null">
                and la.alarm_time <![CDATA[>=]]> #{start,jdbcType=TIMESTAMP}
            </if>
            <if test="end != null">
                and la.alarm_time <![CDATA[<]]> #{end,jdbcType=TIMESTAMP}
            </if>
        </where>
        ) tmp2
        order by tmp1.countNum desc, tmp1.name
    </select>

    <select id="countAlarmByWarnTypeGlobal" resultType="com.easylinkin.linkappapi.machinery.vo.MachineryWarnGroupTypeVO">
      SELECT
        re.name AS warnName,
        count( la.id ) AS num
      FROM
        linkapp_alarm la
        INNER JOIN rule_engine re ON la.rule_engine_id = re.id
      <where>
        <if test="warmNameList != null and warmNameList.size() != 0">
          and re.name in
          <foreach collection="warmNameList" open="(" separator="," close=")" item="item">
            #{item}
          </foreach>
        </if>
      </where>
      GROUP BY
        re.name
    </select>

    <select id="countAlarmByProjectGlobal" resultType="com.easylinkin.linkappapi.machinery.vo.ProjectWarnStatsticsVO">
      SELECT
        la.tenant_id AS projectId,
        count( la.id ) AS num
      FROM
        linkapp_alarm la
        INNER JOIN rule_engine re ON la.rule_engine_id = re.id
      <where>
        <if test="warmNameList != null and warmNameList.size() != 0">
          and re.name in
          <foreach collection="warmNameList" open="(" separator="," close=")" item="item">
            #{item}
          </foreach>
        </if>
      </where>
      GROUP BY
        la.tenant_id
    </select>

    <select id="find4ReportGlobal" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmVo">
        SELECT
            a.id,
            b.`name` ruleEngineName,
            d.device_type_name deviceTypeName
        FROM
            linkapp_alarm a
                LEFT JOIN rule_engine b ON a.rule_engine_id = b.id
                LEFT JOIN linkapp_device c ON a.device_code = c.`code`
                LEFT JOIN linkapp_device_unit d ON c.device_unit_id = d.id
        WHERE
            a.tenant_id = #{tenantId}
          AND date_format( a.alarm_time, "%Y-%m" ) = date_format( #{time}, "%Y-%m" )
    </select>

    <select id="find4ReportAIGlobal" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmVo">
        SELECT
            a.id,
            a.device_code deviceCode,
            d.name ruleEngineName,
            a.content
        FROM
            linkapp_alarm a
                LEFT JOIN linkapp_device b ON a.device_code = b.`code`
                LEFT JOIN linkapp_device_unit c ON b.device_unit_id = c.id
                LEFT JOIN rule_engine d ON a.rule_engine_id = d.id
        WHERE
            a.tenant_id = #{tenantId}
          AND date_format( a.alarm_time, "%Y-%m" ) = date_format(#{time}, "%Y-%m" )
          AND c.device_type_name = #{deviceTypeName}
    </select>

    <select id="find4CountReportGlobal" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmVo">
        SELECT
            a.id,
            a.device_code deviceCode,
            b.name ruleEngineName,
            a.content
        FROM
            linkapp_alarm a
            LEFT JOIN rule_engine b ON a.rule_engine_id = b.id
        WHERE
            a.tenant_id = #{tenantId}
          AND date_format( a.alarm_time, "%Y-%m" ) = date_format(#{time}, "%Y-%m" )
        <if test="deviceCodes != null and deviceCodes.size() != 0">
            and a.device_code in
            <foreach collection="deviceCodes" open="(" separator="," close=")" item="code">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="getGzmAlarmTypeStatistics" resultType="com.easylinkin.linkappapi.gzmmonitor.vo.GzmAlarmVO">
      select la.rule_engine_id as ruleEngineId,
             b.name            as ruleEngineName,
             count(*)          as alarmCount
      from linkapp_alarm la
             LEFT JOIN rule_engine b ON la.rule_engine_id = b.id
      <where>
        <if test="tenantId != null and tenantId != ''">
          and la.tenant_id = #{tenantId}
        </if>
        <if test="ruleIds != null and ruleIds.size() != 0">
          and la.rule_engine_id in
          <foreach collection="ruleIds" open="(" separator="," close=")" item="item">
            #{item}
          </foreach>
        </if>
      </where>
      group by la.rule_engine_id
    </select>

  <select id="getAlarmTrendByTime" resultType="com.easylinkin.linkappapi.gzmmonitor.vo.GzmAlarmDayVO">
    select date_format(la.alarm_time, '%Y-%m-%d') as `date`,
           count(*)                               as `count`
    from linkapp_alarm la
    <where>
      <if test="startTime != null">
        <![CDATA[
        and la.alarm_time >= #{startTime}
        ]]>
      </if>
      <if test="endTime != null">
        <![CDATA[
        and la.alarm_time <= #{endTime}
        ]]>
      </if>
      <if test="tenantId != null and tenantId != ''">
        and la.tenant_id = #{tenantId}
      </if>
      <if test="ruleEngineId != null and ruleEngineId != ''">
        and la.rule_engine_id = #{ruleEngineId}
      </if>
    </where>
    group by date_format(la.alarm_time, '%Y-%m-%d')
  </select>
</mapper>
