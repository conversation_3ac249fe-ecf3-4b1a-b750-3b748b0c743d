<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.alarm.mapper.AlarmPersonContactMapper">


  <select id="getAlarmPersonContacts" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmPersonContact">
    SELECT
    lat.id,lat.name,lat.phone,lat.email,lat.create_time as createTime,lat.modify_time as modifyTime
    FROM
    linkapp_alarm_person_contact lat
    WHERE
    lat.delete_state = 1
    <if test="alarmPersonContact.userId!=null and alarmPersonContact.userId!=''">
      and lat.user_id = #{alarmPersonContact.userId}
    </if>
    <if test="alarmPersonContact.name!=null and alarmPersonContact.name!=''">
      AND lat.name like CONCAT('%',#{alarmPersonContact.name},'%')
    </if>
    <if test="alarmPersonContact.phone!=null and alarmPersonContact.phone!=''">
      AND lat.phone like CONCAT('%',#{alarmPersonContact.phone},'%')
    </if>
    <if test="alarmPersonContact.email!=null and alarmPersonContact.email!=''">
      AND lat.email like CONCAT('%',#{alarmPersonContact.email},'%')
    </if>
    <if test="alarmPersonContact.tenantId!=null and alarmPersonContact.tenantId!=''">
      AND lat.tenant_id = #{alarmPersonContact.tenantId}
    </if>
    ORDER BY lat.modify_time DESC
  </select>


</mapper>
