<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.alarm.mapper.AlarmProcessMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.alarm.entity.AlarmProcess">
    <id column="id" property="id"/>
    <result column="alarm_id" property="alarmId"/>
    <result column="device_code" property="deviceCode"/>
    <result column="handler_id" property="handlerId"/>
    <result column="status" property="status"/>
    <result column="mistake_flag" property="mistakeFlag"/>
    <result column="process_result" property="processResult"/>
    <result column="description" property="description"/>
    <result column="creator" property="creator"/>
    <result column="create_time" property="createTime"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>

</mapper>
