<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.alarm.mapper.AlarmNoticeMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.alarm.entity.AlarmNotice">
    <id column="id" property="id"/>
<!--    <result column="alarm_rule_id" property="alarmRuleId"/>-->
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="ne_ruleEngineName" property="ruleEngineName"/>
    <result column="alarm_id" property="alarmId"/>
    <result column="user_id" property="userId"/>
    <result column="content" property="content"/>
    <result column="status" property="status"/>
    <result column="send_time" property="sendTime"/>
    <result column="message_type" property="messageType"/>
    <result column="message_way" property="messageWay"/>
    <result column="phone_number" property="phoneNumber"/>
    <result column="email_address" property="emailAddress"/>
    <result column="templetmessage_content" property="templetMessageContent"/>
    <result column="templetemail_content" property="templeteMailContent"/>
    <result column="area_path" property="areaPath"/>
    <result column="templet_id" property="templetId"/>
    <result column="device_type" property="deviceType"/>
    <result column="device_name" property="deviceName"/>
    <result column="tenant_id" property="tenantId"/>
<!--    <association property="alarmRule" javaType="com.easylinkin.linkappapi.alarm.entity.AlarmRule">
      <id column="ru_id" property="id"/>
      <result column="ru_name" property="name"/>
      <result column="ru_message_type" property="messageType"/>
      <result column="ru_message_switch" property="messageSwitch"/>
    </association>-->
    <association property="linkappUser" javaType="com.easylinkin.linkappapi.security.entity.LinkappUser">
      <id column="u_id" property="id"/>
      <result column="u_nickname" property="nickname"/>
      <result column="u_phone" property="phone"/>
      <result column="u_email" property="email"/>
    </association>
  </resultMap>

  <select id="getAlarmNotices" resultMap="BaseResultMap">
    select
    n.*,
    <!--    ru.id ru_id,-->
    <!--    ru.name ru_name,-->
    <!--    ru.message_type ru_message_type,-->
    <!--    ru.message_switch ru_message_switch,-->
    re2.name as ne_ruleEngineName,
    u.id u_id,
    IFNULL(u.nickname,u.username) u_nickname
    from linkapp_alarm_notice_log n
    left join rule_engine re2 on re2.id =n.rule_engine_id
    left join linkapp_user u on n.user_id = u.id
    <where>
      <!--   拦截空间过滤会根据告警规则过滤 -->
      re2.id is not null
      <if test="alarmNotice.status!=null">
        and n.status = #{alarmNotice.status}
      </if>
      <if test='alarmNotice.alarmId!=null and alarmNotice.alarmId!="" '>
        and n.alarm_id = #{alarmNotice.alarmId}
      </if>
      <!--  <if test="alarmNotice.alarmRule!=null and alarmNotice.alarmRule.name!=null  and alarmNotice.alarmRule.name!=''">
              and ru.name LIKE CONCAT('%', #{alarmNotice.alarmRule.name},'%')
            </if>-->
      <if test="alarmNotice.ruleEngineName!=null and alarmNotice.ruleEngineName!=''">
        and re2.name LIKE CONCAT('%', #{alarmNotice.ruleEngineName},'%')
      </if>
      <!-- 如果不是管理员  type!=1-->
      <if test='alarmNotice.linkappUser!=null and alarmNotice.linkappUser.id!=null  and alarmNotice.linkappUser.id!=""'>
        and u.id = #{alarmNotice.linkappUser.id}
      </if>
    </where>
    order by n.send_time desc
  </select>

  <select id="getNoticeUser" resultType="com.easylinkin.linkappapi.security.entity.LinkappUser">
    select u.id,IFNULL(u.nickname,u.username) nickname
    from (select distinct user_id from linkapp_alarm_notice_log) an
    left join linkapp_user u on u.id = an.user_id
    <where>
      1=1
      <if test="alarmNotice.linkappUser!=null and alarmNotice.linkappUser.tenantId!=null and alarmNotice.linkappUser.tenantId!=''">
        and u.tenant_id = #{alarmNotice.linkappUser.tenantId}
      </if>
      <!-- 如果不是管理员  type!=1-->
      <if test='alarmNotice.linkappUser!=null and alarmNotice.linkappUser.type!=null and alarmNotice.linkappUser.type!="1" '>
        <if test="alarmNotice.linkappUser.id!=null and alarmNotice.linkappUser.id!=''">
          and u.id = #{alarmNotice.linkappUser.id}
        </if>
      </if>
    </where>
  </select>
  <!-- 通过告警查询所有需要发送的通知 -->
<!--  <select id="getNoticeByAlarm" resultMap="BaseResultMap">
    SELECT
    userInfo.id AS user_id,
    alarmInfo.alarm_rule_id,
    alarmInfo.id as alarm_id ,
    rule.message_type AS message_type,
    templetemail.id AS templet_id,
    rule.alarm_content AS content,
    templetmessage.content AS templetmessage_content,
    templetemail.content AS templetemail_content,
    area.area_path AS area_path,
    deviceunit.device_type_name AS device_type,
    userInfo.phone AS phone_number,
    userInfo.email AS email_address
    FROM
    linkapp_alarm_rule_ref_user ruleRuser
    INNER JOIN linkapp_alarm_rule rule ON ruleRuser.alarm_rule_id = rule.id
    INNER JOIN linkapp_intelligent_rule intelRule ON rule.intelligent_rule_id = intelRule.id
    INNER JOIN linkapp_user userInfo ON ruleRuser.user_id = userInfo.id
    INNER JOIN (
    SELECT
    alarm_rule_id,
    id
    FROM
    linkapp_alarm
    WHERE
    id = #{alarm.id}
    ) alarmInfo ON ruleRuser.alarm_rule_id = alarmInfo.alarm_rule_id
    INNER JOIN linkapp_area area ON rule.area_id = area.id
    INNER JOIN linkapp_device_unit deviceunit ON intelRule.device_unit_id = deviceunit.id
    LEFT JOIN linkapp_alarm_notice_templet templetmessage ON rule.message_templet_id = templetmessage.id
    LEFT JOIN linkapp_alarm_notice_templet templetemail ON rule.email_templet_id = templetemail.id
    WHERE rule.message_switch =1
  </select>-->


  <select id="getWaitInsertAlarmNotices" resultMap="BaseResultMap">
    SELECT
    lapc.user_id AS user_id,
    <!--    alarmInfo.alarm_rule_id,-->
    <!--    alarmInfo.id as alarm_id ,-->
    templetemail.id AS templet_id,
    lat.content AS content,
    templetmessage.content AS templetmessage_content,
    templetemail.content AS templetemail_content,
    ld.area_path AS area_path,
    deviceunit.device_type_name AS device_type,
    ld.name AS device_name,
    lapc.phone AS phone_number,
    lapc.email AS email_address
    FROM
    linkapp_alarm_person_contact lapc
    inner join rule_execution_ref_alarm_person_contact rerapc on lapc.id= rerapc.alarm_person_contact_id
    inner join rule_execution re on re.id = rerapc.rule_execution_id
    inner join linkapp_alarm_template lat on lat.id =re.alarm_template_id
    left join `linkapp_device` ld on ld.code= #{device.code} and ld.delete_state = 1
    left JOIN linkapp_device_unit deviceunit ON ld.device_unit_id = deviceunit.id
    LEFT JOIN linkapp_alarm_notice_templet templetmessage ON templetmessage.id= '12'
    LEFT JOIN linkapp_alarm_notice_templet templetemail ON templetemail.id = '13'
    where re.id =#{ruleExecution.id}

  </select>

  <select id="getWaitInsertAlarmNoticesTenant" resultMap="BaseResultMap">
    SELECT lapc.id AS user_id,
    <!--    alarmInfo.alarm_rule_id,-->
    <!--    alarmInfo.id as alarm_id ,-->
    templetemail.id             AS templet_id,
    lat.content                 AS content,
    templetmessage.content      AS templetmessage_content,
    templetemail.content        AS templetemail_content,
    ld.area_path                AS area_path,
    deviceunit.device_type_name AS device_type,
    ld.name                     AS device_name,
    lapc.phone                  AS phone_number,
    lapc.email                  AS email_address,
    ld.code                     AS device_code,
    e.name                      AS ne_ruleEngineName,
    e.tenant_id as tenant_id
    FROM linkapp_user lapc
           inner join rule_execution_ref_alarm_person_contact rerapc on lapc.id = rerapc.alarm_person_contact_id
           inner join rule_execution re on re.id = rerapc.rule_execution_id
           inner join linkapp_alarm_template lat on lat.id = re.alarm_template_id
           left join `linkapp_device` ld on ld.code = #{device.code} and ld.delete_state = 1
           left JOIN linkapp_device_unit deviceunit ON ld.device_unit_id = deviceunit.id
           LEFT JOIN linkapp_alarm_notice_templet templetmessage ON templetmessage.id = '12'
           LEFT JOIN linkapp_alarm_notice_templet templetemail ON templetemail.id = '13'
           left join rule_engine e on re.rule_engine_id = e.id
    where re.id = #{ruleExecution.id}
  </select>
</mapper>
