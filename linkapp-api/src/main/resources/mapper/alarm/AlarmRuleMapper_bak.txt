<!--
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.isoftstone.linkappapi.alarm.mapper.AlarmRuleMapper">

  &lt;!&ndash; 通用查询映射结果 &ndash;&gt;
  <resultMap id="getAlarmRulesMap" type="com.isoftstone.linkappapi.alarm.entity.AlarmRule">
    <id column="id" property="id"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="area_id" property="areaId"/>
    <result column="message_switch" property="messageSwitch"/>
    <result column="message_type" property="messageType"/>
    <result column="name" property="name"/>
    <result column="alarm_content" property="alarmContent"/>
    <result column="level" property="level"/>
    <result column="status" property="status"/>
    <result column="action_scope" property="actionScope"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
    <result column="space_id" property="spaceId"/>
    <result column="spaceName" property="spaceName"/>
    <result column="creatorName" property="creatorName"/>

&lt;!&ndash;    <association property="intelligentRule" javaType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRule">
      <id column="inteRule_id" property="id"/>
      <result column="inteRule_name" property="name"/>
      <result property="timeScopeCron" column="time_scope_cron"/>
      <result property="commonRuleItem" column="common_rule_item"/>
      <result property="type" column="rule_type"/>
    </association>&ndash;&gt;

    <collection property="alarmRuleRefUsers" javaType="ArrayList" ofType="com.isoftstone.linkappapi.alarm.entity.AlarmRuleRefUser">
      <id column="arru_id" property="id"/>
      <result column="arru_user_id" property="userId"/>
      <result column="arru_alarm_rule_id" property="alarmRuleId"/>
    </collection>

  </resultMap>

  &lt;!&ndash; 查询单条 &ndash;&gt;
  <resultMap id="AlarmRuleInfo" type="com.isoftstone.linkappapi.alarm.entity.AlarmRule">
    <id column="id" property="id"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="area_id" property="areaId"/>
    <result column="space_id" property="spaceId"/>
    <result column="message_switch" property="messageSwitch"/>
    <result column="message_type" property="messageType"/>
    <result column="name" property="name"/>
    <result column="alarm_content" property="alarmContent"/>
    <result column="level" property="level"/>
    <result column="status" property="status"/>
    <result column="action_scope" property="actionScope"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
  </resultMap>


  <select id="getAlarmRules" resultMap="getAlarmRulesMap">
    SELECT
    ru.*,
    inteRule.id AS  inteRule_id,
    inteRule.name AS  inteRule_name,
    inteRule.type as rule_type,
    inteRule.common_rule_item,
    inteRule.time_scope_cron,
    IFNULL(u.nickname,u.username) AS creatorName,
    s.space_name as spaceName,
    arru.id arru_id,
    arru.user_id arru_user_id,
    arru.alarm_rule_id arru_alarm_rule_id
    FROM
    linkapp_alarm_rule ru
    left join linkapp_intelligent_rule inteRule on inteRule.id = ru.intelligent_rule_id
    LEFT JOIN linkapp_alarm_rule_ref_user arru on ru.id = arru.alarm_rule_id and arru.user_id = #{currentUserId}
    LEFT join linkapp_user u on u.id = ru.creator
    left join linkapp_area a on a.id = ru.area_id
    left join linkapp_space s on a.space_id = s.id
    WHERE
    ru.delete_state = 1
    <if test="alarmRule.name!=null and alarmRule.name!=''">
      AND ru.name like CONCAT('%',#{alarmRule.name},'%')
    </if>
    <if test="alarmRule.status!=null">
      AND ru.status = #{alarmRule.status}
    </if>
    <if test="alarmRule.level!=null">
      AND ru.level = #{alarmRule.level}
    </if>
    <if test='alarmRule.intelligentRuleId!=null and alarmRule.intelligentRuleId!=""  '>
      AND ru.intelligent_rule_id = #{alarmRule.intelligentRuleId}
    </if>
    ORDER BY ru.modify_time DESC
  </select>

  <select id="getAlarmRule" resultMap="AlarmRuleInfo">
    SELECT
    ru.*,
    a.space_id as ru_sapceId
    FROM
    linkapp_alarm_rule ru
    left join linkapp_area a on a.id = ru.area_id
    WHERE
    ru.delete_state = 1 AND ru.id = #{id}
  </select>


  &lt;!&ndash; 根据设备查询开启的告警规则关联 &ndash;&gt;
  <resultMap id="alarmRulesWithExpression" type="com.isoftstone.linkappapi.alarm.entity.AlarmRule">
    <id column="id" property="id"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="space_id" property="spaceId"/>
    <result column="area_id" property="areaId"/>
    <result column="message_switch" property="messageSwitch"/>
    <result column="message_type" property="messageType"/>
    <result column="name" property="name"/>
    <result column="alarm_content" property="alarmContent"/>
    <result column="level" property="level"/>
    <result column="status" property="status"/>
    <result column="action_scope" property="actionScope"/>
&lt;!&ndash;    <association property="intelligentRule" javaType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRule">
      <id property="id" column="inteRule_id"/>
      <result property="type" column="inteRule_type"/>
      <result property="timeScopeCron" column="time_scope_cron"/>
      <result property="commonRuleItem" column="common_rule_item"/>
      <collection property="intelligentRuleExpressionList" ofType="com.isoftstone.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
        <id column="intelExpre_id" property="id"/>
        <result column="device_attribute_id" property="deviceAttributeId"/>
        <result column="calculate_sign" property="calculateSign"/>
        <result column="value" property="value"/>
        <result column="sort_no" property="sortNo"/>
        <result column="logic_code" property="logicCode"/>
        <association property="deviceAttribute" javaType="com.isoftstone.linkappapi.deviceattribute.entity.DeviceAttribute">
          <id column="attr_id" property="id"/>
          <result column="attr_device_unit_id" property="deviceUnitId"/>
          <result column="attr_identifier" property="identifier"/>
          <result column="attr_unit" property="unit"/>
          <result column="attr_parent_id" property="parentId"/>
          <result column="attr_parent_prop_code" property="parentPropCode"/>
          <result column="attr_parent_unit" property="parentUnit"/>
        </association>
      </collection>
    </association>&ndash;&gt;
  </resultMap>


  &lt;!&ndash;  根据设备查询开启的告警规则关联&ndash;&gt;
  <select id="getAlarmRulesByDevice" resultMap="alarmRulesWithExpression">
    SELECT
    ru.*,
    intelRule.id as inteRule_id,
    intelRule.type as inteRule_type,
    intelRule.time_scope_cron,
    intelRule.common_rule_item,
    intelExpre.id as intelExpre_id,
    intelExpre.device_attribute_id ,
    intelExpre.calculate_sign ,
    intelExpre.value ,
    intelExpre.sort_no ,
    intelExpre.logic_code ,
    attr.id as attr_id,
    attr.device_unit_id as attr_device_unit_id,
    attr.unit as attr_unit,
    attr.parent_id as attr_parent_id,
    attr2.identifier as attr_parent_prop_code,
    attr2.unit as attr_parent_unit,
    attr.identifier as attr_identifier
    FROM
    linkapp_alarm_rule ru
    LEFT join linkapp_alarm_rule_relate re on re.alarm_rule_id = ru.id and re.delete_state = 1
    LEFT JOIN linkapp_intelligent_rule intelRule on intelRule.id = ru.intelligent_rule_id
    LEFT JOIN linkapp_intelligent_rule_expression intelExpre on intelExpre.intelligent_rule_id = intelRule.id
    LEFT JOIN linkapp_device_attribute attr on attr.id = intelExpre.device_attribute_id
    LEFT JOIN linkapp_device_attribute attr2 on attr2.id = attr.parent_id
    <where>
      ru.delete_state = 1  AND ru.status = 2 AND re.device_code = #{device.code}
    </where>

  </select>

  &lt;!&ndash;  查询告警规则以及告警表达式(规则状态：开启/关闭 规则类型：全局/设备) &ndash;&gt;
  <select id="getAlarmRulesAndExpressions" resultMap="alarmRulesWithExpression">
    SELECT
    ru.*,
    intelRule.id as inteRule_id,
    intelRule.type as inteRule_type,
    intelExpre.id as intelExpre_id,
    intelExpre.device_attribute_id ,
    intelExpre.calculate_sign ,
    intelExpre.value ,
    intelExpre.sort_no ,
    intelExpre.logic_code ,
    attr.id as attr_id,
    attr.device_unit_id as attr_device_unit_id,
    attr.unit as attr_unit,
    attr.parent_id as attr_parent_id,
    attr2.identifier as attr_parent_prop_code,
    attr2.unit as attr_parent_unit,
    attr.identifier as attr_identifier
    FROM
    linkapp_alarm_rule ru
    LEFT JOIN linkapp_area a on a.id = ru.area_id
    LEFT JOIN linkapp_intelligent_rule intelRule on intelRule.id = ru.intelligent_rule_id
    LEFT JOIN linkapp_intelligent_rule_expression intelExpre on intelExpre.intelligent_rule_id = intelRule.id
    LEFT JOIN linkapp_device_attribute attr on attr.id = intelExpre.device_attribute_id
    LEFT JOIN linkapp_device_attribute attr2 on attr2.id = attr.parent_id

    <where>
      ru.delete_state = 1
      and #{device.areaPath} REGEXP CONCAT('^', a.area_path,'$|',a.area_path,':')
      <if test="device.deviceUnitId!=null and device.deviceUnitId!=''">
        and intelRule.device_unit_id = #{device.deviceUnitId}
      </if>
      <if test="alarmRule.status!=null">
        and ru.status = #{alarmRule.status}
      </if>
      <if test="alarmRule.actionScope!=null and alarmRule.actionScope!=''">
        and ru.action_scope = #{alarmRule.actionScope}
      </if>
    </where>
  </select>

</mapper>
-->
