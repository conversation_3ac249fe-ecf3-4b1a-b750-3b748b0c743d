<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.analyze.mapper.AnalyzeDateSourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.analyze.entity.AnalyzeDateSource">
        <id column="id" property="id"/>
        <result column="datasource_name" property="datasourceName"/>
        <result column="datasource_desc" property="datasourceDesc"/>
        <result column="datasource_type" property="datasourceType"/>
        <result column="area_id" property="areaId"/>
        <result column="device_id_string" property="deviceIdString"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_state" property="deleteState"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="status" property="status"/>
    </resultMap>


    <select id="getDeviceByDataSourceId" resultType="com.easylinkin.linkappapi.device.entity.Device">
        SELECT id ,name,code ,area_path as areaPath ,onlineState ,deviceUnitCode,deviceTypeName ,area_name as
        areaName,space_name as spaceName from
        (
        SELECT
        a.name,a.online_state AS onlineState,a.id as id,a.code as code ,a.status,b.code as
        deviceUnitCode,c.name as deviceTypeName
        FROM
        linkapp_device a,
        linkapp_device_unit b,
        linkapp_device_type c
        where
        a.device_unit_id = b.id and b.device_type_id = c.id and a.delete_state = 1
        <if test="deviceVo.code != null and deviceVo.code != ''">
            and a.code like concat('%',#{deviceVo.code},'%')
        </if>
        <if test="deviceVo.status != null and deviceVo.status != ''">
            and a.status = #{deviceVo.status}
        </if>
        <if test="deviceVo.deviceTypeId != null and deviceVo.deviceTypeId != ''">
            and b.device_type_id= #{deviceVo.deviceTypeId}
        </if>

        <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(deviceVo.deviceUnitCodeList)">
            and b.code in
            <foreach collection="deviceVo.deviceUnitCodeList" item="deviceUnitCode" index="index" open="(" close=")"
                     separator=",">
                #{deviceUnitCode}
            </foreach>
        </if>
        ) deviceInfo
        INNER JOIN
        (
        SELECT DISTINCT
        a.id AS device_id,c.area_name,d.space_name,c.area_path
        FROM
        linkapp_device a
        <if test="deviceVo.buddingFlag != null and deviceVo.buddingFlag != ''and deviceVo.buddingFlag =='budding' ">
            INNER JOIN linkapp_analyze_datasource b ON
            FIND_IN_SET(a.id,b.device_id_string)
        </if>
        <if test="deviceVo.buddingFlag != null and deviceVo.buddingFlag != ''and deviceVo.buddingFlag =='nobudding' ">
            INNER JOIN linkapp_analyze_datasource b ON
            !FIND_IN_SET(a.id,b.device_id_string)
        </if>
        INNER JOIN linkapp_area c on a.area_id = c.id
        LEFT JOIN linkapp_space d on c.space_id = d.id
        WHERE
        <if test="deviceVo.buddingFlag =='nobudding' or deviceVo.buddingFlag =='budding'">
            b.id = #{deviceVo.dataSourceid} AND
        </if>
        <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(deviceVo.spaceId)">
            d.id = #{deviceVo.spaceId} AND
        </if>
        a.delete_state = 1
        ) deviceIdInfo where deviceInfo.id =deviceIdInfo.device_id
    </select>


    <!-- 数据源列表 -->
    <select id="getDataSourcList" resultType="com.easylinkin.linkappapi.analyze.entity.AnalyzeDateSource">
        SELECT
          a.id  AS id,
          a.datasource_name AS  datasourceName,
          a.datasource_desc AS datasourceDesc,
          a.datasource_type  AS datasourceType,
          a.status  AS status,
          c.id   AS  spaceId,
           IFNULL(u.nickname,u.username) AS creator,
            c.space_name AS spaceName
        FROM
            linkapp_analyze_datasource a
        LEFT JOIN linkapp_area b ON a.area_id = b.id
        LEFT JOIN linkapp_space c ON b.space_id = c.id
        LEFT join linkapp_user u on u.id = a.creator
        WHERE
        a.delete_state = 1
        <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(analyzeDateSource.datasourceName)">
        AND a.datasource_name   LIKE concat('%',#{analyzeDateSource.datasourceName},'%')
        </if>
        <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(analyzeDateSource.status)">
        AND a.status = #{analyzeDateSource.status}
        </if>
        <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(analyzeDateSource.datasourceType)">
        AND  a.datasource_type = #{analyzeDateSource.datasourceType}
        </if>
        <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(analyzeDateSource.datasourceDesc)">
        AND  a.datasource_desc  LIKE concat('%',#{analyzeDateSource.datasourceDesc},'%')
        </if>
        ORDER BY a.modify_time DESC
    </select>


    <resultMap id="getDateSourcesMap" type="com.easylinkin.linkappapi.analyze.entity.AnalyzeDateSource">
        <id column="id" property="id"/>
        <result column="datasource_name" property="datasourceName"/>
        <result column="datasource_desc" property="datasourceDesc"/>
        <result column="datasource_type" property="datasourceType"/>
        <result column="area_id" property="areaId"/>
        <result column="device_id_string" property="deviceIdString"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_state" property="deleteState"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="status" property="status"/>
    </resultMap>

  <select id="getDateSources" resultMap="getDateSourcesMap">
    select * from
    linkapp_analyze_datasource lad
    left join linkapp_area la on la.id = lad.area_id
    where
    lad.delete_state=1
    <if test="analyzeDateSource.status!=null">
      and lad.status = #{analyzeDateSource.status}
    </if>
    <if test='analyzeDateSource.tenantId!=null and analyzeDateSource.tenantId!=""'>
      and lad.tenant_Id = #{analyzeDateSource.tenantId}
    </if>
    <if test='analyzeDateSource.spaceId!=null and analyzeDateSource.spaceId!=""'>
      and la.space_id = #{analyzeDateSource.spaceId}
    </if>
    <if test='analyzeDateSource.datasourceType!=null'>
      and lad.datasource_type = #{analyzeDateSource.datasourceType}
    </if>
    <if test='analyzeDateSource.datasourceName!=null and analyzeDateSource.datasourceName!=""'>
      and lad.datasource_name like concat('%',#{analyzeDateSource.datasourceName},'%')
    </if>

  </select>
</mapper>
