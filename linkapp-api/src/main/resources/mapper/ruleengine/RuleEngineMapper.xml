<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.ruleengine.mapper.RuleEngineMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleEngine">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="space_id" property="spaceId"/>
<!--    <result column="space_name" property="spaceName"/>-->
    <result column="area_name" property="areaName"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="description" property="description"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
  </resultMap>

  <select id="getRuleEngines" resultMap="BaseResultMap">
    select re.*,
    la.area_path as area_name
    from rule_engine re
    left join linkapp_area la on la.id = re.area_id
    <where>
      <if test="spacesIds!=null and spacesIds.size()>0">
        and re.space_id in
        <foreach collection="spacesIds" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="ruleEngine.name!=null and ruleEngine.name!=''">
        and re.name like concat('%',#{ruleEngine.name},'%')
      </if>
      <if test="ruleEngine.id!=null and ruleEngine.id!=''">
        and re.id = #{ruleEngine.id}
      </if>
      <if test="ruleEngine.spaceId!=null and ruleEngine.spaceId!=''">
        and re.space_id = #{ruleEngine.spaceId}
      </if>
      <if test="ruleEngine.status!=null ">
        and re.status = #{ruleEngine.status}
      </if>
      <if test="ruleEngine.tenantId!=null and ruleEngine.tenantId!=''">
        and re.tenant_id = #{ruleEngine.tenantId}
      </if>
    </where>
    order by re.modify_time desc
  </select>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rule_engine
    (id, `name`, `status`, space_id, area_id, tenant_id, auto_recoverable, description,
    create_time, modify_time, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER},
      #{item.spaceId,jdbcType=VARCHAR}, #{item.areaId,jdbcType=VARCHAR}, #{item.tenantId,jdbcType=VARCHAR},
      #{item.autoRecoverable,jdbcType=BOOLEAN}, #{item.description,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.modifyTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR},
      #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <resultMap id="getRuleEnginesExistsDeviceCodeMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleEngine">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="space_id" property="spaceId"/>
    <!--    <result column="space_name" property="spaceName"/>-->
    <result column="area_name" property="areaName"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="description" property="description"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <collection property="ruleExecutions" column="{ruleExecution.ruleEngineId=id}" select="getRuleExecutionDetails"/>
  </resultMap>

  <select id="getRuleEnginesExistsDeviceCode" resultMap="getRuleEnginesExistsDeviceCodeMap">
    SELECT re.*
    FROM rule_engine re
    <where>
      <if test="spacesIds!=null and spacesIds.size()>0">
        and re.space_id in
        <foreach collection="spacesIds" item="item" open="("
                 separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="ruleEngine.name!=null and ruleEngine.name!=''">
        and re.name like concat('%',#{ruleEngine.name},'%')
      </if>
      <if test="ruleEngine.tenantId!=null and ruleEngine.tenantId!=''">
        and re.tenant_id = #{ruleEngine.tenantId}
      </if>
      <if test="ruleEngine.status != null">
        and re.status = #{ruleEngine.status}
      </if>
      <if test="deviceCode != null and deviceCode != ''">
        AND EXISTS (
            SELECT rt.id
            FROM rule_trigger rt
            WHERE rt.rule_engine_id = re.id
                AND EXISTS (
                    SELECT 1
                    FROM rule_trigger_ref_device rtd
                    WHERE rtd.rule_trigger_id = rt.id
                        AND rtd.device_code = #{deviceCode}
                )
        )
      </if>
    </where>
  </select>

  <resultMap id="getRuleExecutionDetailsMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleExecution">
    <id column="id" property="id"/>
    <result column="type" property="type"/>
    <result column="sort_flag" property="sortFlag"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="alarm_template_id" property="alarmTemplateId"/>
    <result column="delay_time" property="delayTime"/>
    <collection property="ruleExecutionRefAlarmPersonContactList" ofType="com.easylinkin.linkappapi.ruleengine.entity.RuleExecutionRefAlarmPersonContact">
      <result property="alarmPersonContactId" column="alarm_person_contact_id"/>
      <association property="alarmPersonContact" javaType="com.easylinkin.linkappapi.alarm.entity.AlarmPersonContact">
        <result column="person_name" property="name"/>
        <result column="person_phone" property="phone"/>
        <result column="person_email" property="email"/>
        <result column="person_user_id" property="userId"/>
      </association>
    </collection>
  </resultMap>

  <select id="getRuleExecutionDetails" resultMap="getRuleExecutionDetailsMap">
    select
    re.id ,
    re.type ,
    re.sort_flag ,
    re.rule_engine_id,
    re.alarm_template_id,
    re.delay_time,
    lcrdd.id as down_id,
    lcrdd.device_code as down_deviceCode,
    lcrdd.sort_no as down_sortNo,
    lcrdd.rule_execution_id as down_ruleExecutionId,
    rerapc.alarm_person_contact_id,
    lapc.name person_name,
    lapc.phone person_phone,
    lapc.email person_email,
    lapc.user_id person_user_id
    from rule_execution re
    left join linkage_config_ref_down_device lcrdd on lcrdd.rule_execution_id = re.id
    left join rule_execution_ref_alarm_person_contact rerapc on rerapc.rule_execution_id = re.id
    left join linkapp_alarm_person_contact lapc on rerapc.alarm_person_contact_id=lapc.id and lapc.delete_state=1
    <where>
      1=1
      <if test="ruleExecution.ruleEngineId!=null and ruleExecution.ruleEngineId!=''">
        and re.rule_engine_id =#{ruleExecution.ruleEngineId}
      </if>
    </where>
  </select>
</mapper>
