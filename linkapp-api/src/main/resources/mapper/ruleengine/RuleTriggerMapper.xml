<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.ruleengine.mapper.RuleTriggerMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleTrigger">
    <id column="id" property="id"/>
    <result column="type" property="type"/>
    <result column="sort_flag" property="sortFlag"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>

  <select id="getRuleTriggers" resultType="com.easylinkin.linkappapi.ruleengine.entity.RuleTrigger">
    select rt.* from rule_trigger rt
    <where>
      1=1
      <if test="ruleTrigger.id!=null and ruleTrigger.id!=''">
        and rt.id = #{ruleTrigger.id}
      </if>
      <if test="ruleTrigger.ruleEngineId!=null and ruleTrigger.ruleEngineId!=''">
        and rt.rule_engine_id = #{ruleTrigger.ruleEngineId}
      </if>
      <if test="ruleTrigger.ruleEngineIds != null and ruleTrigger.ruleEngineIds.size()>0">
        and rt.rule_engine_id in
        <foreach item="item" collection="ruleTrigger.ruleEngineIds" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>


  <select id="getRuleTrigger" resultType="com.easylinkin.linkappapi.ruleengine.entity.RuleTrigger">
    select rt.* from rule_trigger rt
    <where>
      <if test="id!=null and id!=''">
        and rt.id = #{id}
      </if>
    </where>
  </select>

  <!-- 根据设备查询开启的告警规则关联 -->
  <resultMap id="getRuleTriggersAndExpressionsMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleTrigger">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="type" property="type"/>
    <result column="common_rule_item" property="commonRuleItem"/>
    <association property="jobEntity" javaType="com.easylinkin.linkappapi.taskscheduler.entity.JobEntity">
      <id column="lje_id" property="id"/>
      <result column="cron_expression" property="cronExpression"/>
      <result column="job_status" property="jobStatus"/>
      <result column="job_name" property="jobName"/>
      <result column="job_group" property="jobGroup"/>
    </association>
    <!--   column = id id是要带过去的参数-->
    <collection property="intelligentRuleExpressionList" column="id" javaType="ArrayList" select="selectExpressionAndAttrByRuleId" ofType="com.easylinkin.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    </collection>
    <collection property="ruleTriggerRefDeviceList" column="id" javaType="ArrayList" ofType="com.easylinkin.linkappapi.ruleengine.entity.RuleTriggerRefDevice">
      <result property="deviceCode" column="device_code"/>
    </collection>

  </resultMap>

  <select id="getRuleTriggersAndExpressions" resultMap="getRuleTriggersAndExpressionsMap">
    SELECT
    rt.id,
    rt.device_unit_id,
    rt.type,
    rt.common_rule_item,
    lje.id lje_id,
    lje.cron_expression,
    lje.job_status,
    lje.job_name,
    lje.job_group,
    rtrd.device_code
    FROM
    rule_trigger rt
    left join linkapp_job_entity lje on lje.id = rt.job_entity_id
    left join rule_trigger_ref_device rtrd on rtrd.rule_trigger_id = rt.id
    <where>
      1 = 1
      <if test='ruleTrigger.id!=null and ruleTrigger.id!=""'>
        and rt.id = #{ruleTrigger.id}
      </if>
      <if test="ruleTrigger.deviceUnitId!=null and ruleTrigger.deviceUnitId!=''">
        and rt.device_unit_id = #{ruleTrigger.deviceUnitId}
      </if>
      <if test="ruleTrigger.ruleEngineId!=null and ruleTrigger.ruleEngineId!=''">
        and rt.rule_engine_id = #{ruleTrigger.ruleEngineId}
      </if>
    </where>
  </select>


  <resultMap id="selectExpressionAndAttrByRuleTriggerIdMap" type="com.easylinkin.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    <id column="id" property="id"/>
    <result column="device_attribute_id" property="deviceAttributeId"/>
    <result column="calculate_sign" property="calculateSign"/>
    <result column="value" property="value"/>
    <result column="sort_no" property="sortNo"/>
    <result column="logic_code" property="logicCode"/>
    <association property="deviceAttribute" javaType="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute">
      <id column="att_id" property="id"/>
      <id column="att_unit" property="unit"/>
      <result column="att_device_unit_id" property="deviceUnitId"/>
      <result column="att_identifier" property="identifier"/>
      <result column="att_parent_prop_code" property="parentPropCode"/>
      <result column="att_parent_id" property="parentId"/>
    </association>
  </resultMap>

  <select id="selectExpressionAndAttrByRuleTriggerId" resultMap="selectExpressionAndAttrByRuleTriggerIdMap" parameterType="java.lang.String">
    SELECT e.id,
    e.device_attribute_id,
    e.calculate_sign,
    e.value,
    e.logic_code,
    e.sort_no,
    att.id as att_id,
    att.unit att_unit,
    att.device_unit_id att_device_unit_id,
    att.parent_id as att_parent_id,
    att2.identifier as att_parent_prop_code,
    att.identifier att_identifier
    FROM linkapp_intelligent_rule_expression e
    LEFT JOIN linkapp_device_attribute att on att.id = e.device_attribute_id
    LEFT JOIN linkapp_device_attribute att2 on att.parent_id = att2.id
    WHERE e.delete_state = 1
    AND e.rule_trigger_id = #{id}
    order by e.sort_no
  </select>


  <resultMap id="getRuleTriggersByRelatedDeviceCodeMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleTrigger">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="type" property="type"/>
    <result column="common_rule_item" property="commonRuleItem"/>
    <!--   column = id id是要带过去的参数-->
    <collection property="intelligentRuleExpressionList" column="id" javaType="ArrayList" select="selectExpressionAndAttrByRuleId" ofType="com.easylinkin.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    </collection>
  </resultMap>


  <resultMap id="selectExpressionAndAttrByRuleIdMap" type="com.easylinkin.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    <id column="id" property="id"/>
    <result column="rule_trigger_id" property="ruleTriggerId"/>
    <result column="device_attribute_id" property="deviceAttributeId"/>
    <result column="calculate_sign" property="calculateSign"/>
    <result column="value" property="value"/>
    <result column="sort_no" property="sortNo"/>
    <result column="logic_code" property="logicCode"/>
    <association property="deviceAttribute" javaType="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute">
      <id column="att_id" property="id"/>
      <result column="att_unit" property="unit"/>
      <result column="att_specs" property="specs"/>
      <result column="att_device_unit_id" property="deviceUnitId"/>
      <result column="att_identifier" property="identifier"/>
      <result column="att_parent_prop_code" property="parentPropCode"/>
      <result column="att_parent_id" property="parentId"/>
    </association>
  </resultMap>

  <select id="selectExpressionAndAttrByRuleId" resultMap="selectExpressionAndAttrByRuleIdMap" parameterType="java.lang.String">
    SELECT e.id,
      e.rule_trigger_id,
    e.device_attribute_id,
    e.calculate_sign,
    e.value,
    e.logic_code,
    e.sort_no,
    att.id as att_id,
    att.unit att_unit,
    att.specs att_specs,
    att.device_unit_id att_device_unit_id,
    att.parent_id as att_parent_id,
    att2.identifier as att_parent_prop_code,
    att.identifier att_identifier
    FROM linkapp_intelligent_rule_expression e
    LEFT JOIN linkapp_device_attribute att on att.id = e.device_attribute_id
    LEFT JOIN linkapp_device_attribute att2 on att.parent_id = att2.id
    WHERE e.delete_state = 1
    AND e.rule_trigger_id = #{id}
    order by e.sort_no
  </select>


  <select id="getRuleTriggersByRelatedDeviceCode" resultMap="getRuleTriggersByRelatedDeviceCodeMap">
    SELECT
    rt.*
    FROM
    rule_trigger rt
    inner join rule_trigger_ref_device rtrd on rtrd.rule_trigger_id = rt.id
    inner join rule_engine re on re.id = rt.rule_engine_id and re.status = 1
    <where>
      1 = 1
      <if test='deviceCode!=null and deviceCode!=""'>
        and rtrd.device_code = #{deviceCode}
      </if>
      <if test='tenantId!=null and tenantId!=""'>
        and re.tenant_id = #{tenantId}
      </if>
    </where>
  </select>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rule_trigger
    (id, `type`, rule_engine_id, sort_flag, create_time, modify_time, common_rule_item,
    device_unit_id, job_entity_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.ruleEngineId,jdbcType=VARCHAR},
      #{item.sortFlag,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.modifyTime,jdbcType=TIMESTAMP},
      #{item.commonRuleItem,jdbcType=INTEGER}, #{item.deviceUnitId,jdbcType=VARCHAR},
      #{item.jobEntityId,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
