<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.ruleengine.mapper.DataFlushServiceMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleEngine">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="space_id" property="spaceId"/>
    <result column="space_name" property="spaceName"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="description" property="description"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
  </resultMap>

  <select id="getDeviceUnitIdErrorCanCorrectRuleTriggers" resultType="com.easylinkin.linkappapi.ruleengine.entity.RuleTrigger">
    SELECT distinct rt.*,
    ldu2.id as newDeviceUnitId
    FROM rule_trigger rt
    INNER JOIN rule_engine re2 ON rt.rule_engine_id = re2.id
    inner JOIN linkapp_device_unit ldu ON ldu.id = rt.device_unit_id
    inner join linkapp_device_unit ldu2 on ldu.code = ldu2.code and ldu2.id != ldu.id
    inner join linkapp_tenant_ref_device_unit ltrdu on ldu2.id = ltrdu.device_unit_id and ltrdu.tenant_id = re2.tenant_id
    <where>
      1=1
      <if test="actualAddedTenantRefDeviceUnitIds != null and actualAddedTenantRefDeviceUnitIds.size() != 0">
        and ltrdu.id in
        <foreach collection="actualAddedTenantRefDeviceUnitIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getDeviceUnitIdErrorCanCorrectRuleExecutions" resultType="com.easylinkin.linkappapi.ruleengine.entity.RuleExecution">
    SELECT distinct re.*,
    ldu2.id as newDeviceUnitId
    FROM rule_execution re
    INNER JOIN rule_engine re2 ON re.rule_engine_id = re2.id
    inner JOIN linkapp_device_unit ldu ON ldu.id = re.device_unit_id
    inner join linkapp_device_unit ldu2 on ldu.code = ldu2.code and ldu2.id != ldu.id
    inner join linkapp_tenant_ref_device_unit ltrdu on ldu2.id = ltrdu.device_unit_id and ltrdu.tenant_id = re2.tenant_id
    <where>
      1=1
      <if test="actualAddedTenantRefDeviceUnitIds != null and actualAddedTenantRefDeviceUnitIds.size() != 0">
        and ltrdu.id in
        <foreach collection="actualAddedTenantRefDeviceUnitIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>


  <select id="selectDeviceUnitIdErrorCanCorrectConfigRelateServiceParmList" resultType="com.easylinkin.linkappapi.linkage.entity.LinkageConfigRelateServiceParm">
    select DISTINCT lcrsp.id,
    lcrsp.device_unit_id,
    ltrdu.device_unit_id as newDeviceUnitId,
    lcrsp.device_service_id,
    lds2.id as newDeviceServiceId,
    lcrsp.device_parm_id,
    ldp2.id as newDeviceParmId
    from linkage_config_relate_service_parm lcrsp
    inner join rule_execution re on lcrsp.rule_execution_id = re.id
    inner join rule_engine r on re.rule_engine_id = r.id
    inner join linkapp_device_unit ldu on lcrsp.device_unit_id = ldu.id
    inner join linkapp_device_unit ldu2 on ldu.code = ldu2.code and ldu2.id != ldu.id
    inner join linkapp_tenant_ref_device_unit ltrdu on ldu2.id = ltrdu.device_unit_id and ltrdu.tenant_id = r.tenant_id
    left join linkapp_device_service lds on lcrsp.device_service_id = lds.id
    left join linkapp_device_service lds2 on lds2.identifier = lds.identifier and lds2.id != lds.id and lds2.device_unit_id = ltrdu.device_unit_id
    left join linkapp_device_parm ldp on lcrsp.device_parm_id = ldp.id
    left join linkapp_device_parm ldp2 on ldp2.identifier = ldp.identifier and ldp2.id != ldp.id and ldp2.device_service_id = lds2.id
    <where>
      lcrsp.device_unit_id != ltrdu.device_unit_id
      <if test="actualAddedTenantRefDeviceUnitIds != null and actualAddedTenantRefDeviceUnitIds.size() != 0">
        and ltrdu.id in
        <foreach collection="actualAddedTenantRefDeviceUnitIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>

  </select>


  <select id="getDeviceAttrIdErrorCanCorrectRuleExpressions" resultType="com.easylinkin.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    SELECT lire.*,
           lda2.id as newDeviceAttributeId
    FROM linkapp_intelligent_rule_expression lire
           inner join rule_trigger rt on lire.rule_trigger_id = rt.id
           inner JOIN linkapp_device_unit ldu ON ldu.id = rt.device_unit_id
           inner join linkapp_device_attribute lda on lire.device_attribute_id = lda.id
           inner join linkapp_device_attribute lda2 on ldu.id = lda2.device_unit_id and lda2.identifier = lda.identifier
    where lda.id != lda2.id and lire.delete_state = 1
    union
    SELECT lire.*,
           lda2.id as newDeviceAttributeId
    FROM linkapp_intelligent_rule_expression lire
           inner join rule_condition rc on lire.rule_condition_id = rc.id
           inner join `linkapp_device` ld on ld.delete_state = 1 and ld.code = rc.device_code
           inner JOIN linkapp_device_unit ldu ON ldu.id = ld.device_unit_id
           inner join linkapp_device_attribute lda on lire.device_attribute_id = lda.id
           inner join linkapp_device_attribute lda2 on ldu.id = lda2.device_unit_id and lda2.identifier = lda.identifier
    where lda.id != lda2.id and lire.delete_state = 1
  </select>

  <select id="getDeviceParentAttrIdErrorCanCorrectRuleExpressions" resultType="com.easylinkin.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    SELECT lire.*,
           lda2.id as newDeviceAttributeParentId
    FROM linkapp_intelligent_rule_expression lire
           inner join rule_trigger rt on lire.rule_trigger_id = rt.id
           inner JOIN linkapp_device_unit ldu ON ldu.id = rt.device_unit_id
           inner join linkapp_device_attribute lda on lire.device_attribute_parent_id = lda.id
           inner join linkapp_device_attribute lda2 on ldu.id = lda2.device_unit_id and lda2.identifier = lda.identifier
    where lda.id != lda2.id and lire.delete_state = 1
    union
    SELECT lire.*,
           lda2.id as newDeviceAttributeId
    FROM linkapp_intelligent_rule_expression lire
           inner join rule_condition rc on lire.rule_condition_id = rc.id
           inner join `linkapp_device` ld on ld.delete_state = 1 and ld.code = rc.device_code
           inner JOIN linkapp_device_unit ldu ON ldu.id = ld.device_unit_id
           inner join linkapp_device_attribute lda on lire.device_attribute_parent_id = lda.id
           inner join linkapp_device_attribute lda2 on ldu.id = lda2.device_unit_id and lda2.identifier = lda.identifier
    where lda.id != lda2.id and lire.delete_state = 1
  </select>


</mapper>
