<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.ruleengine.mapper.RuleTriggerRefDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleTriggerRefDevice">
        <id column="id" property="id"/>
        <result column="rule_trigger_id" property="ruleTriggerId"/>
        <result column="device_code" property="deviceCode"/>
    </resultMap>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rule_trigger_ref_device
        (id, rule_trigger_id, device_code)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.ruleTriggerId,jdbcType=VARCHAR}, #{item.deviceCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
