<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.ruleengine.mapper.RuleConditionMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleCondition">
    <id column="id" property="id"/>
    <result column="type" property="type"/>
    <result column="sort_flag" property="sortFlag"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="device_code" property="deviceCode"/>
    <result column="time_scope_cron" property="timeScopeCron"/>
  </resultMap>

  <select id="getRuleConditions" resultType="com.easylinkin.linkappapi.ruleengine.entity.RuleCondition">
    select rc.* from rule_condition rc
    <where>
      1=1
      <if test="ruleCondition.deviceCode != null and ruleCondition.deviceCode !=''">
        and rc.device_code = #{ruleCondition.deviceCode}
      </if>
      <if test="ruleCondition.id!=null and ruleCondition.id!=''">
        and rc.id = #{ruleCondition.id}
      </if>
      <if test="ruleCondition.ruleEngineId!=null and ruleCondition.ruleEngineId!=''">
        and rc.rule_engine_id = #{ruleCondition.ruleEngineId}
      </if>
      <if test="ruleCondition.ruleEngineIds != null and ruleCondition.ruleEngineIds.size()>0">
        and rc.rule_engine_id in
        <foreach item="item" index="index" collection="ruleCondition.ruleEngineIds" open="(" separator="," close=")">
          #{item}
         </foreach>
      </if>
    </where>
    order by rc.sort_flag desc
  </select>

  <select id="getRuleCondition" resultType="com.easylinkin.linkappapi.ruleengine.entity.RuleCondition">
    select rc.* from rule_condition rc
    <where>
      <if test="id!=null and id!=''">
        and rc.id = #{id}
      </if>
    </where>
  </select>

  <resultMap id="getRuleConditionWithExpressionsMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleCondition">
    <id column="id" property="id"/>
    <result column="type" property="type"/>
    <result column="sort_flag" property="sortFlag"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="device_code" property="deviceCode"/>
    <result column="time_scope_cron" property="timeScopeCron"/>
    <collection property="intelligentRuleExpressionList" column="id" javaType="ArrayList" select="selectExpressionAndAttrByRuleId" ofType="com.easylinkin.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    </collection>
  </resultMap>


  <resultMap id="selectExpressionAndAttrByRuleIdMap" type="com.easylinkin.linkappapi.intelligentrule.entity.IntelligentRuleExpression">
    <id column="id" property="id"/>
    <result column="device_attribute_id" property="deviceAttributeId"/>
    <result column="calculate_sign" property="calculateSign"/>
    <result column="value" property="value"/>
    <result column="sort_no" property="sortNo"/>
    <result column="logic_code" property="logicCode"/>
    <association property="deviceAttribute" javaType="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute">
      <id column="att_id" property="id"/>
      <id column="att_unit" property="unit"/>
      <result column="att_device_unit_id" property="deviceUnitId"/>
      <result column="att_identifier" property="identifier"/>
      <result column="att_parent_prop_code" property="parentPropCode"/>
      <result column="att_parent_id" property="parentId"/>
    </association>
  </resultMap>

  <select id="selectExpressionAndAttrByRuleId" resultMap="selectExpressionAndAttrByRuleIdMap" parameterType="java.lang.String">
    SELECT
    e.id ,
    e.device_attribute_id ,
    e.calculate_sign ,
    e.value,
    e.logic_code ,
    e.sort_no,
    att.id as att_id,
    att.device_unit_id att_device_unit_id,
    att.unit att_unit,
    att.parent_id as att_parent_id,
    att2.identifier as att_parent_prop_code,
    att.identifier att_identifier
    FROM linkapp_intelligent_rule_expression e
    LEFT JOIN linkapp_device_attribute att on att.id = e.device_attribute_id
    LEFT JOIN linkapp_device_attribute att2 on att.parent_id = att2.id
    WHERE e.delete_state = 1 AND  e.rule_condition_id = #{id}
    order by e.sort_no
  </select>

  <select id="getRuleConditionWithExpressions" resultMap="getRuleConditionWithExpressionsMap">
    select rc.* from rule_condition rc
    <where>
      1=1
      <if test="ruleCondition.deviceCode != null and ruleCondition.deviceCode !=''">
        and rc.device_code = #{ruleCondition.deviceCode}
      </if>
      <if test="ruleCondition.id!=null and ruleCondition.id!=''">
        and rc.id = #{ruleCondition.id}
      </if>
      <if test="ruleCondition.ruleEngineId!=null and ruleCondition.ruleEngineId!=''">
        and rc.rule_engine_id = #{ruleCondition.ruleEngineId}
      </if>
      <if test="ruleCondition.ruleEngineIds != null and ruleCondition.ruleEngineIds.size() > 0">
        and rc.rule_engine_id in
        <foreach collection="ruleCondition.ruleEngineIds" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>
    order by rc.sort_flag desc
  </select>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rule_condition
    (id, `type`, sort_flag, rule_engine_id, create_time, modify_time, device_code, time_scope_cron
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.sortFlag,jdbcType=VARCHAR},
      #{item.ruleEngineId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.modifyTime,jdbcType=TIMESTAMP},
      #{item.deviceCode,jdbcType=VARCHAR}, #{item.timeScopeCron,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
