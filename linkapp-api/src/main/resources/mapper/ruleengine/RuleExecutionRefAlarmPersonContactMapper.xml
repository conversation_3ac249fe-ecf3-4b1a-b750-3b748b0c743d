<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.ruleengine.mapper.RuleExecutionRefAlarmPersonContactMapper">

  <!-- 通用查询映射结果 -->
    <sql id="Base_Column_List">
	id,
	rule_execution_id,
	alarm_person_contact_id
</sql>
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleExecutionRefAlarmPersonContact">
    <id column="id" property="id"/>
    <result column="rule_execution_id" property="ruleExecutionId"/>
    <result column="alarm_person_contact_id" property="alarmPersonContactId"/>
    <collection property="alarmPersonContact" column="{pid=alarm_person_contact_id}" select="getAlarmPersonContacts"/>
  </resultMap>

<!--auto generated by MybatisCodeHelper on 2021-10-26-->
    <select id="findByRuleExecutionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rule_execution_ref_alarm_person_contact
        <where>
            <if test="ruleExecutionIds != null and ruleExecutionIds.size() > 0">
                rule_execution_id in
                <foreach item="item" collection="ruleExecutionIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getAlarmPersonContacts" resultType="com.easylinkin.linkappapi.alarm.entity.AlarmPersonContact">
        SELECT
        lat.id,lat.name,lat.phone,lat.email,lat.create_time as createTime,lat.modify_time as modifyTime
        FROM
        linkapp_alarm_person_contact lat
        WHERE
        lat.delete_state = 1
        <if test="pid!=null and pid!=''">
            and lat.id = #{pid}
        </if>
        ORDER BY lat.modify_time DESC
    </select>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rule_execution_ref_alarm_person_contact
        (id, rule_execution_id, alarm_person_contact_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.ruleExecutionId,jdbcType=VARCHAR}, #{item.alarmPersonContactId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
