<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.ruleengine.mapper.RuleExecutionMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleExecution">
    <id column="id" property="id"/>
    <result column="type" property="type"/>
    <result column="sort_flag" property="sortFlag"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="alarm_template_id" property="alarmTemplateId"/>
    <result column="delay_time" property="delayTime"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>


  <select id="getRuleExecutions" resultType="com.easylinkin.linkappapi.ruleengine.entity.RuleExecution">

    select re.* from rule_execution re
    <where>
      1=1
      <if test="ruleExecution.id!=null and ruleExecution.id!=''">
        and re.id = #{ruleExecution.id}
      </if>
      <if test="ruleExecution.type!=null">
        and re.type = #{ruleExecution.type}
      </if>
      <if test="ruleExecution.ruleEngineId!=null and ruleExecution.ruleEngineId!=''">
        and re.rule_engine_id = #{ruleExecution.ruleEngineId}
      </if>
      <if test="ruleExecution.ruleEngineIds != null and ruleExecution.ruleEngineIds.size()>0">
        and re.rule_engine_id in
        <foreach item="item"  collection="ruleExecution.ruleEngineIds" open="(" separator="," close=")">
          #{item}
         </foreach>
      </if>
    </where>
  </select>


  <select id="getRuleExecution" resultType="com.easylinkin.linkappapi.ruleengine.entity.RuleExecution">
    select re.* from rule_execution re
    <where>
      <if test="id!=null and id!=''">
        and re.id = #{id}
      </if>
    </where>
  </select>


  <resultMap id="getRuleExecutionWithLinkageConfigRefDownDeviceListMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleExecution">
    <id column="id" property="id"/>
    <result column="type" property="type"/>
    <result column="sort_flag" property="sortFlag"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="selfTrigger" property="selfTrigger"/>
    <result column="alarm_template_id" property="alarmTemplateId"/>
    <result column="delay_time" property="delayTime"/>
    <association property="ruleEngine" javaType="com.easylinkin.linkappapi.ruleengine.entity.RuleEngine">
      <result column="re2_tenant_id" property="tenantId"/>
      <result column="re2_space_id" property="spaceId"/>
    </association>
    <association property="alarmTemplate" javaType="com.easylinkin.linkappapi.alarm.entity.AlarmTemplate">
      <result column="lat_level" property="level"/>
      <result column="lat_content" property="content"/>
    </association>
    <collection property="linkageConfigRefDownDeviceList" ofType="com.easylinkin.linkappapi.linkage.entity.LinkageConfigRefDownDevice">
      <id property="id" column="down_id"/>
      <result property="deviceCode" column="down_deviceCode"/>
      <result property="sortNo" column="sort_no"/>
      <result property="ruleExecutionId" column="down_ruleExecutionId"/>
    </collection>
  </resultMap>

  <select id="getRuleExecutionWithLinkageConfigRefDownDeviceList" resultMap="getRuleExecutionWithLinkageConfigRefDownDeviceListMap">
    select
    re.id ,
    re.type ,
    re.sort_flag ,
    re.rule_engine_id,
    re.alarm_template_id,
    re.delay_time,
    <!--  查询规则引擎是不是自触发型的  -->
    (select count(rc.id) from rule_condition rc where rc.rule_engine_id = re.rule_engine_id and rc.type = 31) > 0 as selfTrigger,
    re2.tenant_id                                                                                                 as re2_tenant_id,
    re2.space_id as re2_space_id,
    lat.level as lat_level,
    lat.content as lat_content,
    re.id as down_ruleExecutionId,
    lcrdd.id as down_id,
    lcrdd.sort_no as sort_no,
    lcrdd.device_code as down_deviceCode,
    lcrdd.rule_execution_id as down_linkageConfigId
    from rule_execution re
    left join linkapp_alarm_template lat on lat.id = re.alarm_template_id
    left join rule_engine re2 on re2.id = re.rule_engine_id
    left join linkage_config_ref_down_device lcrdd on lcrdd.rule_execution_id = re.id
    left join linkapp_device ld on lcrdd.device_code = ld.code and ld.delete_state = 1 and linkthing_delete =1
    <where>
      1=1
      <if test="ruleEngineIds!=null and ruleEngineIds.size()>0">
        and re.rule_engine_id in
        <foreach collection="ruleEngineIds" item="item" open="(" separator="," close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      and (ld.tenant_id = re2.tenant_id or lat.tenant_id = re2.tenant_id)
    </where>
    order by re.sort_flag asc
  </select>


  <resultMap id="selectLinkageConfigRelateServiceParmsMap" type="com.easylinkin.linkappapi.linkage.entity.LinkageConfigRelateServiceParm">
    <id column="id" property="id"/>
    <!--    <result column="linkage_config_id" property="linkageConfigId"/>-->
    <result column="rule_execution_id" property="ruleExecutionId"/>
    <result column="group_number" property="groupNumber"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="device_service_id" property="deviceServiceId"/>
    <result column="device_parm_id" property="deviceParmId"/>
    <result column="parm_value" property="parmValue"/>
    <result column="parent_id" property="parentId"/>
    <result column="array_index" property="arrayIndex"/>
    <result column="specs" property="specs"/>
    <result column="unit" property="unit"/>
    <result column="name" property="name"/>
  </resultMap>


  <select id="selectLinkageConfigRelateServiceParms" resultMap="selectLinkageConfigRelateServiceParmsMap">
    SELECT
    p.*,
    ldp.specs,
    ldp.unit,
    ldp.name as name
    FROM linkage_config_relate_service_parm p
    left join linkapp_device_parm ldp on ldp.id = p.device_parm_id
    WHERE rule_execution_id = #{id}
    ORDER BY group_number ASC,device_service_id ASC ,device_parm_id ASC
  </select>

  <resultMap id="getRuleExecutionDetailsMap" type="com.easylinkin.linkappapi.ruleengine.entity.RuleExecution">
    <id column="id" property="id"/>
    <result column="type" property="type"/>
    <result column="sort_flag" property="sortFlag"/>
    <result column="rule_engine_id" property="ruleEngineId"/>
    <result column="alarm_template_id" property="alarmTemplateId"/>
    <result column="delay_time" property="delayTime"/>
    <collection property="linkageConfigRefDownDeviceList" ofType="com.easylinkin.linkappapi.linkage.entity.LinkageConfigRefDownDevice">
      <id property="id" column="down_id"/>
      <result property="deviceCode" column="down_deviceCode"/>
      <result property="sortNo" column="down_sortNo"/>
      <result property="ruleExecutionId" column="down_ruleExecutionId"/>
    </collection>
    <collection property="linkageConfigRelateServiceParms" column="id" ofType="com.easylinkin.linkappapi.linkage.entity.LinkageConfigRelateServiceParm" select="selectLinkageConfigRelateServiceParms">
    </collection>
    <collection property="ruleExecutionRefAlarmPersonContactList" ofType="com.easylinkin.linkappapi.ruleengine.entity.RuleExecutionRefAlarmPersonContact">
      <result property="alarmPersonContactId" column="alarm_person_contact_id"/>
    </collection>
  </resultMap>

  <select id="getRuleExecutionDetails" resultMap="getRuleExecutionDetailsMap">
    select
    re.id ,
    re.type ,
    re.sort_flag ,
    re.rule_engine_id,
    re.alarm_template_id,
    re.delay_time,
    lcrdd.id as down_id,
    lcrdd.device_code as down_deviceCode,
    lcrdd.sort_no as down_sortNo,
    lcrdd.rule_execution_id as down_ruleExecutionId,
    rerapc.alarm_person_contact_id
    from rule_execution re
    left join linkage_config_ref_down_device lcrdd on lcrdd.rule_execution_id = re.id
    left join rule_execution_ref_alarm_person_contact rerapc on rerapc.rule_execution_id = re.id
    <where>
      1=1
      <if test="ruleExecution.ruleEngineId!=null and ruleExecution.ruleEngineId!=''">
        and re.rule_engine_id =#{ruleExecution.ruleEngineId}
      </if>
    </where>
  </select>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rule_execution
    (id, `type`, rule_engine_id, device_unit_id, sort_flag, alarm_template_id, delay_time,
    create_time, modify_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.ruleEngineId,jdbcType=VARCHAR},
      #{item.deviceUnitId,jdbcType=VARCHAR}, #{item.sortFlag,jdbcType=VARCHAR}, #{item.alarmTemplateId,jdbcType=VARCHAR},
      #{item.delayTime,jdbcType=FLOAT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.modifyTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
</mapper>
