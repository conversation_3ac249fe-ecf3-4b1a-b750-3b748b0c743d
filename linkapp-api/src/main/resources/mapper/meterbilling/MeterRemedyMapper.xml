<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.meterbilling.mapper.MeterRemedyMapper">

    <select id="getPage" resultType="com.easylinkin.linkappapi.meterbilling.entity.ext.MeterRemedyExt">
        select
            mr.device_code deviceCode,
            mr.energy_amount energyAmount,
            mr.remedy_time remedyTime,
            mr.remark,
            ri.room_name roomName,
            mdc.meter_type meterType,
            d.name deviceName,
            u.username creatorName
        from
        cbjf_meter_remedy mr left join cbjf_room_info ri on mr.room_id=ri.id
        left join cbjf_meter_device_config mdc on mr.device_code=mdc.device_code
        left join linkapp_device d on mr.device_code=d.code
        left join linkapp_user u on mr.creator=u.id
        <where>
            ri.delete_status = 0 and mdc.delete_status = 0 and d.delete_state = 1
            <if test="meterRemedyExt.tenantId != null and meterRemedyExt.tenantId != ''">
                and mr.tenant_id = #{meterRemedyExt.tenantId}
            </if>
            <if test="meterRemedyExt.meterType != null">
                and mdc.meter_type = #{meterRemedyExt.meterType}
            </if>
            <if test="meterRemedyExt.roomId != null">
                and mr.room_id = #{meterRemedyExt.roomId}
            </if>
            <if test="meterRemedyExt.deviceCode != null and meterRemedyExt.deviceCode != ''">
                and mr.device_code like concat('%',#{meterRemedyExt.deviceCode}, '%')
            </if>
            <if test="meterRemedyExt.deviceName != null and meterRemedyExt.deviceName != ''">
                and d.name like concat('%',#{meterRemedyExt.deviceName}, '%')
            </if>
            <if test="meterRemedyExt.queryStartTime != null">
                and mr.remedy_time &gt;= #{meterRemedyExt.queryStartTime}
            </if>
            <if test="meterRemedyExt.queryEndTime != null">
                and mr.remedy_time &lt;= #{meterRemedyExt.queryEndTime}
            </if>
        </where>
        order by mr.remedy_time desc
    </select>


    <select id="getNewRemedyDataGroup" resultType="com.easylinkin.linkappapi.meterbilling.entity.MeterRemedy">
        select * from (
            select
                cmr.id,
                cmr.room_id roomId,
                cmr.device_code deviceCode,
                cmr.energy_amount energyAmount,
                cmr.remedy_time remedyTime,
                cmr.remark,
                cmr.tenant_id tenantId,
                cmr.create_time createTime,
                cmr.creator,
                cmr.calculate_status calculateStatus
            from
            cbjf_meter_remedy cmr
            <where>
                cmr.calculate_status=0
                <if test="deviceCode != null">
                    and cmr.device_code = #{deviceCode}
                </if>
                order by cmr.create_time desc
            </where>
        ) b group by b.deviceCode
    </select>

    <select id="getNewRemedyData" resultType="com.easylinkin.linkappapi.meterbilling.entity.MeterRemedy">
        select
        cmr.id,
        cmr.room_id roomId,
        cmr.device_code deviceCode,
        cmr.energy_amount energyAmount,
        cmr.remedy_time remedyTime,
        cmr.remark,
        cmr.tenant_id tenantId,
        cmr.create_time createTime,
        cmr.creator,
        cmr.calculate_status calculateStatus
        from
        cbjf_meter_remedy cmr
        <where>
            cmr.calculate_status=0
            <if test="deviceCode != null">
                and cmr.device_code = #{deviceCode}
            </if>
            order by cmr.create_time desc
        </where>
    </select>

</mapper>
