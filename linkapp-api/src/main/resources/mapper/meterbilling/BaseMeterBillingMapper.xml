<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.meterbilling.mapper.BaseMeterBillingMapper">

    <select id="getBaseBillingList" resultMap="BaseMeterBillingMap">
        select
        cbb.*,
        lu.username creatorName
        from
        cbjf_base_billing cbb left join linkapp_user lu on cbb.creator=lu.id
        <where>
            cbb.delete_status = 0 and cbb.is_valid = 0
            <if test="baseBilling.priceName != null and baseBilling.priceName != ''">
                and cbb.price_name like concat('%',#{baseBilling.priceName}, '%')
            </if>
            <if test="baseBilling.meterType != null">
                and cbb.meter_type = #{baseBilling.meterType}
            </if>
            <if test="baseBilling.tenantId != null and baseBilling.tenantId != ''">
                and cbb.tenant_id = #{baseBilling.tenantId}
            </if>
            <if test="baseBilling.id != null">
                and cbb.id = #{baseBilling.id}
            </if>
        </where>
        order by cbb.modify_time desc
    </select>

    <select id="getBillingIdByRoomAndMeterType" resultType="java.lang.Integer">
        select
        cbb.id
        from
        cbjf_room_info ri left join cbjf_room_billing_ref rbi on ri.id=rbi.room_id
        left join cbjf_base_billing cbb on rbi.billing_id=cbb.id
        <where>
            ri.delete_status = 0
            and cbb.delete_status = 0
            and cbb.is_valid = 0
            and ri.id=#{roomId}
            and cbb.meter_type=#{meterType}
        </where>
    </select>


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseMeterBillingMap" type="com.easylinkin.linkappapi.meterbilling.entity.ext.BaseBillingExt">
        <id column="id" property="id"/>
        <result column="price_name" property="priceName"/>
        <result column="meter_type" property="meterType"/>
        <result column="price" property="price"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_status" property="deleteStatus"/>
        <result column="is_valid" property="isValid"/>
        <result column="unit" property="unit"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creatorName" property="creatorName"/>
        <result column="modifierName" property="modifierName"/>
        <result column="meterTypeName" property="meterTypeName"/>
    </resultMap>
</mapper>
