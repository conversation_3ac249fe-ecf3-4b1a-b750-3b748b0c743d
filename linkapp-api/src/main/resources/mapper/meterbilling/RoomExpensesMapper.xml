<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.meterbilling.mapper.RoomExpensesMapper">

    <select id="getNewestRoomExpensesBillRecordByDeviceCode" resultMap="RoomExpensesMap">
        select * from (
            select
                creb.*
            from
            cbjf_room_expenses_bill creb
            <where>
                creb.remedy_status=0
                <if test="deviceCodeList != null">
                    and creb.device_code in
                    <foreach collection="deviceCodeList" item="deviceCode" open="(" separator="," close=")">
                        #{deviceCode}
                    </foreach>
                </if>
                order by creb.create_time desc
            </where>
        ) b group by b.device_code
    </select>

    <resultMap id="RoomExpensesMap" type="com.easylinkin.linkappapi.meterbilling.entity.RoomExpensesBill">
        <id column="id" property="id"/>
        <result column="contract_id" property="contractId"/>
        <result column="settlement_time_start" property="settlementTimeStart"/>
        <result column="settlement_time_end" property="settlementTimeEnd"/>
        <result column="energy_amount" property="energyAmount"/>
        <result column="device_code" property="deviceCode"/>
        <result column="consume_fee" property="consumeFee"/>
        <result column="create_time" property="createTime"/>
        <result column="deduction_status" property="deductionStatus"/>
        <result column="billing_id" property="billingId"/>
        <result column="version" property="version"/>
        <result column="remedy_status" property="remedyStatus"/>
        <result column="end_read_amount" property="endReadAmount"/>
        <result column="operator" property="operator"/>
    </resultMap>

</mapper>
