<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.meterbilling.mapper.RoomBillingRefMapper">

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into cbjf_room_billing_ref
        (room_id, billing_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roomId}, #{item.billingId})
        </foreach>
    </insert>
</mapper>
