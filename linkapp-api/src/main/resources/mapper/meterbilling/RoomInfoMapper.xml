<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.meterbilling.mapper.RoomInfoMapper">

    <select id="getRoomBillingList" resultMap="BillingMap">
        select
            bb.*
        from
            cbjf_room_billing_ref rbr
            left join cbjf_base_billing bb on rbr.billing_id=bb.id
        <where>
            rbr.room_id=#{pRoomId}
            and bb.tenant_id=#{pTenantId}
            and bb.delete_status = 0
        </where>
    </select>

    <select id="getRoomListByResident" resultType="com.easylinkin.linkappapi.meterbilling.entity.ext.WechatRoomList">
        select
            ri.id roomId,
            ri.room_code roomCode,
            ri.room_name roomName,
            a.area_path areaPath
        from
        cbjf_resident_info cri left join cbjf_tenantry_info rti on rti.resident_id=cri.id
        left join cbjf_room_info ri on ri.id=rti.room_id
        left join linkapp_area a on ri.area_id=a.id
        <where>
            ri.delete_status = 0
            and rti.delete_status = 0
            and rti.bind_status = 0
            and cri.delete_status = 0
            <if test="phone != null and phone != ''">
                and cri.phone = #{phone}
            </if>
        </where>
        group by ri.id
        order by ri.create_time desc

    </select>

    <select id="getRoomPage" resultMap="RoomPageMap">
        select
            ri.id,
            ri.room_code,
            ri.room_name,
            ri.water_balance,
            ri.area_id,
            a.area_path,
            ri.electricity_balance,
            ri.tenant_id,
            rti.id contractId,
            cri.name,
            cri.phone,
            rti.contract_start_time,
            rti.contract_end_time
        from
            cbjf_room_info ri left join cbjf_tenantry_info rti on ri.id=rti.room_id
            left join cbjf_resident_info cri on rti.resident_id=cri.id
            left join linkapp_area a on ri.area_id=a.id
        <where>
            ri.delete_status = 0
            and rti.delete_status = 0
            and rti.bind_status = 0
            and cri.delete_status = 0
            <if test="roomList.tenantId != null and roomList.tenantId != ''">
                and ri.tenant_id = #{roomList.tenantId}
            </if>
            <if test="roomList.roomCode != null and roomList.roomCode != ''">
                and ri.room_code like concat('%',#{roomList.roomCode}, '%')
            </if>
            <if test="roomList.areaId != null and roomList.areaId != ''">
                and ri.area_id = #{roomList.areaId}
            </if>
            <if test="roomList.name != null and roomList.name != ''">
                and cri.name like concat('%',#{roomList.name}, '%')
            </if>
            <if test="roomList.phone != null and roomList.phone != ''">
                and cri.phone like concat('%',#{roomList.phone}, '%')
            </if>
            <if test="roomList.areaPath != null and roomList.areaPath != ''">
                and (a.area_path = #{roomList.areaPath} or a.area_path like concat(#{roomList.areaPath},':%'))
            </if>
            <if test="roomList.contractStartTime != null">
                and rti.contract_start_time &gt;= #{roomList.contractStartTime}
            </if>
            <if test="roomList.contractEndTime != null">
                and rti.contract_end_time &lt;= #{roomList.contractEndTime}
            </if>
        </where>
        order by ri.modify_time desc
    </select>

    <select id="getRoomdataByIds" resultMap="RoomPageMap">
        select
        ri.id,
        ri.room_code,
        ri.room_name,
        ri.water_balance,
        ri.electricity_balance,
        ri.tenant_id,
        rti.id contractId,
        cri.name,
        cri.phone,
        rti.contract_start_time,
        rti.contract_end_time
        from
        cbjf_room_info ri left join cbjf_tenantry_info rti on ri.id=rti.room_id
        left join cbjf_resident_info cri on rti.resident_id=cri.id
        <where>
            ri.delete_status = 0
            and rti.delete_status = 0
            and rti.bind_status = 0
            and cri.delete_status = 0
            <if test="roomList.roomIdList != null">
                and ri.id in
                <foreach collection="roomList.roomIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <resultMap id="BillingMap" type="com.easylinkin.linkappapi.meterbilling.entity.BaseBilling">
        <id column="id" property="id"/>
        <result column="price_name" property="priceName"/>
        <result column="meter_type" property="meterType"/>
        <result column="price" property="price"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_status" property="deleteStatus"/>
        <result column="is_valid" property="isValid"/>
        <result column="unit" property="unit"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="RoomPageMap" type="com.easylinkin.linkappapi.meterbilling.entity.ext.RoomList">
        <id column="id" property="roomId"/>
        <result column="room_code" property="roomCode"/>
        <result column="room_name" property="roomName"/>
        <result column="water_balance" property="waterBalance"/>
        <result column="electricity_balance" property="electricityBalance"/>
        <result column="contractId" property="contractId"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="area_id" property="areaId"/>
        <result column="area_path" property="areaPath"/>
        <result column="contract_start_time" property="contractStartTime"/>
        <result column="contract_end_time" property="contractEndTime"/>
        <collection property="meterPriceList" column="{pRoomId = id,pTenantId = tenant_id}" select="getRoomBillingList" javaType="java.util.ArrayList" ofType="com.easylinkin.linkappapi.meterbilling.entity.BaseBilling">
        </collection>
    </resultMap>

</mapper>
