<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.meterbilling.mapper.MeterDeviceConfigMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.meterbilling.entity.MeterDeviceConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="room_id" jdbcType="INTEGER" property="roomId" />
    <result column="room_code" jdbcType="VARCHAR" property="roomCode" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="meter_type" jdbcType="TINYINT" property="meterType" />
    <result column="meter_type_name" jdbcType="VARCHAR" property="meterTypeName" />
    <result column="record_time" jdbcType="TIME" property="recordTime" />
    <result column="day_threshold" jdbcType="DECIMAL" property="dayThreshold" />
    <result column="month_threshold" jdbcType="DECIMAL" property="monthThreshold" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="deviceName" jdbcType="VARCHAR" property="deviceName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, device_code, room_id, meter_type, record_time, day_threshold, month_threshold,
    create_time, modify_time, delete_status, tenant_id
  </sql>

  <select id="getMeterDeviceConfigList" parameterType="com.easylinkin.linkappapi.meterbilling.entity.MeterDeviceConfig" resultMap="BaseResultMap">
    select temp.* from (select
    mdc.id,
    mdc.tenant_id,
    mdc.device_code,
    mdc.meter_type,
    mdc.create_time,
    if(mdc.meter_type=0,'水表','电表') meter_type_name,
    mdc.room_id,
    ri.room_code,
    d.name deviceName,
    CONCAT(replace(la.area_path,':','/'),'/',ri.room_name) room_name
    from cbjf_meter_device_config mdc
    left join linkapp_device d on mdc.device_code=d.code and d.delete_state = 1
    left join cbjf_room_info ri on mdc.room_id=ri.id and ri.delete_status=0
    left join linkapp_area la on ri.area_id=la.id
    <where>
          mdc.delete_status=0
          <if test="item.deviceCode != null and item.deviceCode != ''">
             and mdc.device_code like CONCAT('%',#{item.deviceCode},'%')
          </if>
          <if test="item.roomId != null">
             and mdc.room_id = #{item.roomId}
          </if>
          <if test="item.meterType != null ">
            and mdc.meter_type = #{item.meterType}
          </if>
          <if test="item.roomIds != null and item.roomIds.size()>0">
            and mdc.room_id in
            <foreach collection="item.roomIds" item="roomId" open="(" separator="," close=")">
              #{roomId}
            </foreach>
          </if>
          <if test="item.id != null ">
            and mdc.id=#{item.id}
          </if>
          <if test="item.ids != null and item.ids.size()>0">
            and mdc.id in
            <foreach collection="item.ids" item="id" open="(" separator="," close=")">
              #{id}
            </foreach>
          </if>
          <if test="item.tenantId != null and item.tenantId != ''">
            and mdc.tenant_id = #{item.tenantId}
          </if>
        </where>
      order by mdc.modify_time desc
    ) temp
    <where>
      <if test="item.roomName != null and item.roomName != ''">
        and (temp.room_name=#{item.roomName} or temp.room_name like CONCAT(#{item.roomName},'/%'))
      </if>
    </where>
  </select>

  <select id="getDeviceAll" parameterType="com.easylinkin.linkappapi.meterbilling.entity.MeterDeviceConfig" resultMap="BaseResultMap">
    select temp.* from (select
    mdc.id,
    mdc.tenant_id,
    mdc.device_code,
    mdc.meter_type,
    if(mdc.meter_type=0,'水表','电表') meter_type_name,
    mdc.room_id,
    ri.room_code,
    CONCAT(replace(la.area_path,':','/'),'/',ri.room_name) room_name
    from cbjf_meter_device_config mdc
    left join cbjf_room_info ri on mdc.room_id=ri.id and ri.delete_status=0
    left join cbjf_tenantry_info ti on ri.id=ti.room_id and ti.delete_status=0
    left join cbjf_resident_info cri on ti.resident_id=cri.id and cri.delete_status=0
    left join linkapp_area la on ri.area_id=la.id
    <where>
      mdc.delete_status=0
      <if test="item.deviceCode != null and item.deviceCode != ''">
        and mdc.device_code like CONCAT('%',#{item.deviceCode},'%')
      </if>
      <if test="item.roomId != null">
        and mdc.room_id = #{item.roomId}
      </if>
      <if test="item.meterType != null ">
        and mdc.meter_type = #{item.meterType}
      </if>
      <if test="item.roomIds != null and item.roomIds.size()>0">
        and mdc.room_id in
        <foreach collection="item.roomIds" item="roomId" open="(" separator="," close=")">
          #{roomId}
        </foreach>
      </if>
      <if test="item.id != null ">
        and mdc.id=#{item.id}
      </if>
      <if test="item.ids != null and item.ids.size()>0">
        and mdc.id in
        <foreach collection="item.ids" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="item.tenantId != null and item.tenantId != ''">
        and mdc.tenant_id = #{item.tenantId}
      </if>
      <if test="item.phone != null and item.phone != ''">
        and cri.phone = #{item.phone}
      </if>
      <if test="item.roomCode != null and item.roomCode">
        and ri.room_code = #{item.roomCode}
      </if>
    </where>
    order by mdc.modify_time desc
    ) temp
    <where>
      <if test="item.roomName != null and item.roomName != ''">
        and (temp.room_name=#{item.roomName} or temp.room_name like CONCAT(#{item.roomName},'/%'))
      </if>
    </where>
  </select>

  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update cbjf_meter_device_config
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="device_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.deviceCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="room_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.roomId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="meter_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.meterType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="record_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.recordTime,jdbcType=TIME}
        </foreach>
      </trim>
      <trim prefix="day_threshold = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.dayThreshold,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="month_threshold = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.monthThreshold,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="modify_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.modifyTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="delete_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.deleteStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="tenant_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.tenantId,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update cbjf_meter_device_config
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="device_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deviceCode != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.deviceCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="room_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.roomId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.roomId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="meter_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.meterType != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.meterType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="record_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.recordTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.recordTime,jdbcType=TIME}
          </if>
        </foreach>
      </trim>
      <trim prefix="day_threshold = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dayThreshold != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.dayThreshold,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="month_threshold = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.monthThreshold != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.monthThreshold,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="modify_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifyTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.modifyTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="delete_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deleteStatus != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.deleteStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="tenant_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.tenantId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into cbjf_meter_device_config
    (device_code, room_id, meter_type, record_time, day_threshold, month_threshold,
    create_time, modify_time, delete_status, tenant_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deviceCode,jdbcType=VARCHAR}, #{item.roomId,jdbcType=INTEGER}, #{item.meterType,jdbcType=BOOLEAN},
      #{item.recordTime,jdbcType=TIME}, #{item.dayThreshold,jdbcType=DECIMAL}, #{item.monthThreshold,jdbcType=DECIMAL},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.modifyTime,jdbcType=TIMESTAMP}, #{item.deleteStatus,jdbcType=INTEGER},
      #{item.tenantId,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>