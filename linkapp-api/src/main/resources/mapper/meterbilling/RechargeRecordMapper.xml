<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.meterbilling.mapper.RechargeRecordMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.meterbilling.entity.RechargeRecord">
    <!--@mbg.generated-->
    <!--@Table dev-linkapp-mysql.cbjf_recharge_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="recharge_type" jdbcType="INTEGER" property="rechargeType" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="recharge_platform" jdbcType="INTEGER" property="rechargePlatform" />
    <result column="notify_time" jdbcType="TIMESTAMP" property="notifyTime" />
    <result column="recharge_status" jdbcType="INTEGER" property="rechargeStatus" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="payer" jdbcType="VARCHAR" property="payer" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="third_order_num" jdbcType="VARCHAR" property="thirdOrderNum" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="type" jdbcType="INTEGER" property="type" />

    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="recharge_type_name" jdbcType="VARCHAR" property="rechargeTypeName" />
    <result column="pay_type_name" jdbcType="VARCHAR" property="payTypeName" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="room_id" jdbcType="INTEGER" property="roomId" />
    <result column="room_code" jdbcType="VARCHAR" property="roomCode" />
    <result column="pay_time_name" jdbcType="VARCHAR" property="payTimeName" />
    <result column="create_time_name" jdbcType="VARCHAR" property="createTimeName" />
      <result column="type_name" jdbcType="VARCHAR" property="typeName" />
      <result column="price_name" jdbcType="VARCHAR" property="priceName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_num, contract_id, recharge_type, price, pay_type, recharge_platform, notify_time, 
    recharge_status, pay_time, payer, create_time, `operator`, third_order_num,`tenant_id`,`type`
  </sql>

  <!--<select id="getRechargeRecordList" parameterType="com.easylinkin.linkappapi.meterbilling.entity.RechargeRecord" resultMap="BaseResultMap">
      select temp.* from (select
      rr.id,
      rr.order_num,
      rr.type,
      rr.price,
      CONCAT(rr.price,'元') price_name,
      if(rr.type=0,'充值','退款') type_name,
      rr.tenant_id,
      ti.room_id,
      ri.room_code,
      CONCAT(replace(la.area_path,':','/'),'/',ri.room_name) room_name,
      rti.`name`,
      rti.phone,
      rr.recharge_type,
      if(rr.recharge_type=0,'水表','电表') recharge_type_name,
      rr.pay_type,
      if(rr.pay_type=0,'现金','微信') pay_type_name,
      rr.pay_time,
      DATE_FORMAT(rr.pay_time,'%Y-%m-%d %H:%i:%s') pay_time_name,
      rr.create_time,
      DATE_FORMAT(rr.create_time,'%Y-%m-%d %H:%i:%s') create_time_name,
      rr.operator,
      IFNULL(lu.nickname,lu.username) operator_name
      from cbjf_recharge_record rr
      left join cbjf_tenantry_info ti on rr.contract_id=ti.id
      left join cbjf_resident_info rti on ti.resident_id=rti.id
      left join cbjf_room_info ri on ti.room_id=ri.id
      left join linkapp_area la on ri.area_id=la.id
      left join linkapp_user lu on rr.operator=lu.id
      <where>
          <if test="item.id != null ">
              and rr.id=#{item.id}
          </if>
          <if test="item.ids != null and item.ids.size()>0">
            and rr.id in
            <foreach collection="item.ids" item="id" open="(" separator="," close=")">
              #{id}
            </foreach>
          </if>
          <if test="item.roomCode != null and item.roomCode != ''">
              and ri.room_code like CONCAT('%',#{item.roomCode},'%')
          </if>
          <if test="item.roomId != null ">
              and ti.room_id = #{item.roomId}
          </if>
          <if test="item.name != null and item.name != ''">
              and rti.`name` like CONCAT('%',#{item.name},'%')
          </if>
          <if test="item.rechargeType != null ">
              and rr.recharge_type = #{item.rechargeType}
          </if>
          <if test="item.operator != null and item.operator != ''">
              and rr.operator = #{item.operator}
          </if>
            <if test="item.phone != null and item.phone != ''">
                and rti.phone = #{item.phone}
            </if>
            <if test="item.operatorName != null and item.operatorName != ''">
                and (lu.nickname like CONCAT('%',#{item.operatorName},'%') or
                      lu.username like CONCAT('%',#{item.operatorName},'%'))
            </if>
          <if test="item.queryPayTimeStart != null and item.queryPayTimeEnd != null ">
              and (rr.create_time <![CDATA[ >= ]]> #{item.queryPayTimeStart} and rr.create_time <![CDATA[ <= ]]> #{item.queryPayTimeEnd})
          </if>
          <if test="item.tenantId != null and item.tenantId != ''">
              and rr.tenant_id = #{item.tenantId}
          </if>
        </where>
        order by rr.create_time desc
      ) temp
      <where>
          <if test="item.roomName != null and item.roomName != ''">
              and (temp.room_name=#{item.roomName} or temp.room_name like CONCAT(#{item.roomName},'/%'))
          </if>
      </where>

  </select>-->

    <select id="getRechargeRecordList" parameterType="com.easylinkin.linkappapi.meterbilling.entity.RechargeRecord" resultMap="BaseResultMap">
        select temp.* from (select
        rr.*,
        ti.room_id,
        ri.room_code,
        CONCAT(replace(la.area_path,':','/'),'/',ri.room_name) room_name,
        rti.`name`,
        rti.phone
        from (
            select
                reb.id,
                null order_num,
                2 type,
                reb.consume_fee price,
                CONCAT(reb.consume_fee,'元') price_name,
                '扣费' type_name,
                bb.tenant_id,
                bb.meter_type recharge_type,
                if(bb.meter_type=0,'水表','电表') recharge_type_name,
                null pay_type,
                '--' pay_type_name,
                reb.create_time pay_time,
                DATE_FORMAT(reb.create_time,'%Y-%m-%d %H:%i:%s')  pay_time_name,
                reb.create_time,
                DATE_FORMAT(reb.create_time,'%Y-%m-%d %H:%i:%s') create_time_name,
                reb.operator,
                reb.contract_id,
                case
                    when reb.remedy_status = 0 then '自动'
                    when reb.remedy_status = 1 then IFNULL(lu.nickname,lu.username)
                end operator_name
            from cbjf_room_expenses_bill reb
            left join cbjf_base_billing bb on reb.billing_id=bb.id
            left join linkapp_user lu on reb.operator=lu.id
                <where>
                    reb.deduction_status = 0
                    <if test="item.id != null ">
                        and reb.id=#{item.id}
                    </if>
                    <if test="item.ids != null and item.ids.size()>0">
                        and reb.id in
                        <foreach collection="item.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="item.rechargeType != null ">
                        and bb.meter_type = #{item.rechargeType}
                    </if>
                    <if test="item.operator != null and item.operator != ''">
                        and reb.operator = #{item.operator}
                    </if>
                    <if test="item.queryPayTimeStart != null and item.queryPayTimeEnd != null ">
                        and (reb.create_time <![CDATA[ >= ]]> #{item.queryPayTimeStart} and reb.create_time <![CDATA[ <= ]]> #{item.queryPayTimeEnd})
                    </if>
                    <if test="item.tenantId != null and item.tenantId != ''">
                        and bb.tenant_id = #{item.tenantId}
                    </if>
                    <if test="item.type != null and item.type != 2">
                        and bb.id is  null
                    </if>
                </where>
            union all
            select
                rr.id,
                rr.order_num,
                rr.type,
                rr.price,
                CONCAT(rr.price,'元') price_name,
                if(rr.type=0,'充值','退款') type_name,
                rr.tenant_id,
                rr.recharge_type,
                if(rr.recharge_type=0,'水表','电表') recharge_type_name,
                rr.pay_type,
                if(rr.pay_type=0,'现金','微信') pay_type_name,
                rr.pay_time,
                DATE_FORMAT(rr.pay_time,'%Y-%m-%d %H:%i:%s') pay_time_name,
                rr.create_time,
                DATE_FORMAT(rr.create_time,'%Y-%m-%d %H:%i:%s') create_time_name,
                rr.operator,
                rr.contract_id,
                IFNULL(lu.nickname,lu.username) operator_name
            from cbjf_recharge_record rr
            left join linkapp_user lu on rr.operator=lu.id
                <where>
                    rr.recharge_status = 1
                    <if test="item.id != null ">
                        and rr.id=#{item.id}
                    </if>
                    <if test="item.ids != null and item.ids.size()>0">
                        and rr.id in
                        <foreach collection="item.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="item.rechargeType != null ">
                        and rr.recharge_type = #{item.rechargeType}
                    </if>
                    <if test="item.operator != null and item.operator != ''">
                        and rr.operator = #{item.operator}
                    </if>
                    <if test="item.queryPayTimeStart != null and item.queryPayTimeEnd != null ">
                        and (rr.create_time <![CDATA[ >= ]]> #{item.queryPayTimeStart} and rr.create_time <![CDATA[ <= ]]> #{item.queryPayTimeEnd})
                    </if>
                    <if test="item.tenantId != null and item.tenantId != ''">
                        and rr.tenant_id = #{item.tenantId}
                    </if>
                    <if test="item.type != null ">
                        and rr.type = #{item.type}
                    </if>
                </where>
        ) rr
        left join cbjf_tenantry_info ti on rr.contract_id=ti.id
        left join cbjf_resident_info rti on ti.resident_id=rti.id
        left join cbjf_room_info ri on ti.room_id=ri.id
        left join linkapp_area la on ri.area_id=la.id
        <where>
            <if test="item.roomCode != null and item.roomCode != ''">
                and ri.room_code like CONCAT('%',#{item.roomCode},'%')
            </if>
            <if test="item.roomId != null ">
                and ti.room_id = #{item.roomId}
            </if>
            <if test="item.name != null and item.name != ''">
                and rti.`name` like CONCAT('%',#{item.name},'%')
            </if>
            <if test="item.phone != null and item.phone != ''">
                and rti.phone = #{item.phone}
            </if>
        </where>
        order by rr.create_time desc
        ) temp
        <where>
            <if test="item.roomName != null and item.roomName != ''">
                and (temp.room_name=#{item.roomName} or temp.room_name like CONCAT(#{item.roomName},'/%'))
            </if>
            <if test="item.operatorName != null and item.operatorName != ''">
                and (temp.operator_name like CONCAT('%',#{item.operatorName},'%') or
                temp.operator_name like CONCAT('%',#{item.operatorName},'%'))
            </if>
        </where>

    </select>

    <select id="getMeterDeviceConfigCost" parameterType="com.easylinkin.linkappapi.meterbilling.entity.RechargeRecord" resultType="java.util.Map">
        select
          rr.type,sum(rr.price) price
        from (
            select
            reb.id,
            reb.consume_fee price,
            2 type,
            reb.contract_id
            from cbjf_room_expenses_bill reb
           left join cbjf_base_billing bb on reb.billing_id=bb.id
            <where>
                reb.deduction_status = 0
                <if test="item.id != null ">
                    and reb.id=#{item.id}
                </if>
                <if test="item.ids != null and item.ids.size()>0">
                    and reb.id in
                    <foreach collection="item.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="item.rechargeType != null ">
                    and bb.meter_type = #{item.rechargeType}
                </if>
                <if test="item.operator != null and item.operator != ''">
                    and reb.operator = #{item.operator}
                </if>
                <if test="item.queryPayTimeStart != null and item.queryPayTimeEnd != null ">
                    and (reb.create_time <![CDATA[ >= ]]> #{item.queryPayTimeStart} and reb.create_time <![CDATA[ <= ]]> #{item.queryPayTimeEnd})
                </if>
                <if test="item.tenantId != null and item.tenantId != ''">
                    and bb.tenant_id = #{item.tenantId}
                </if>
                <if test="item.type != null and item.type != 2">
                    and bb.id is  null
                </if>
            </where>
            union all
            select
            rr.id,
            rr.price,
            rr.type,
            rr.contract_id
            from cbjf_recharge_record rr
            <where>
                rr.recharge_status = 1
                <if test="item.id != null ">
                    and rr.id=#{item.id}
                </if>
                <if test="item.ids != null and item.ids.size()>0">
                    and rr.id in
                    <foreach collection="item.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="item.rechargeType != null ">
                    and rr.recharge_type = #{item.rechargeType}
                </if>
                <if test="item.operator != null and item.operator != ''">
                    and rr.operator = #{item.operator}
                </if>
                <if test="item.queryPayTimeStart != null and item.queryPayTimeEnd != null ">
                    and (rr.create_time <![CDATA[ >= ]]> #{item.queryPayTimeStart} and rr.create_time <![CDATA[ <= ]]> #{item.queryPayTimeEnd})
                </if>
                <if test="item.tenantId != null and item.tenantId != ''">
                    and rr.tenant_id = #{item.tenantId}
                </if>
                <if test="item.type != null ">
                    and rr.type = #{item.type}
                </if>
            </where>
        ) rr
        left join cbjf_tenantry_info ti on rr.contract_id=ti.id
        left join cbjf_resident_info rti on ti.resident_id=rti.id
        left join cbjf_room_info ri on ti.room_id=ri.id
        <where>
            <if test="item.roomCode != null and item.roomCode != ''">
                and ri.room_code like CONCAT('%',#{item.roomCode},'%')
            </if>
            <if test="item.roomId != null ">
                and ti.room_id = #{item.roomId}
            </if>
            <if test="item.name != null and item.name != ''">
                and rti.`name` like CONCAT('%',#{item.name},'%')
            </if>
            <if test="item.phone != null and item.phone != ''">
                and rti.phone = #{item.phone}
            </if>
        </where>
        group by rr.type
    </select>
</mapper>