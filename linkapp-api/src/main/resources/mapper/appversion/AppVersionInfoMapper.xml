<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.appversion.mapper.AppVersionInfoMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.appversion.entity.AppVersionInfo">
    <id column="id" property="id"/>
    <result column="device_brand" property="deviceBrand"/>
    <result column="os_name" property="osName"/>
    <result column="os_version" property="osVersion"/>
    <result column="os_language" property="osLanguage"/>
    <result column="app_version" property="appVersion"/>
    <result column="application_version" property="applicationVersion"/>
    <result column="app_url" property="appUrl"/>
    <result column="update_content" property="updateContent"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="deleted" property="deleted"/>
  </resultMap>

  <select id="getAppVerssionInfo"
    parameterType="com.easylinkin.linkappapi.appversion.entity.AppVersionInfo"
    resultType="com.easylinkin.linkappapi.appversion.entity.AppVersionInfo">
    select avi.*
    from app_version_info avi
    <where>
      1=1
      <if test="appVersionInfo != null">
        <if test="appVersionInfo.deleted !=null">
          and avi.deleted = #{appVersionInfo.deleted}
        </if>
        <if test="appVersionInfo.appVersion !=null and appVersionInfo.appVersion != ''"  >
          and avi.app_version like CONCAT('%',#{appVersionInfo.appVersion},'%')
        </if>
        <if test="appVersionInfo.osName != null and appVersionInfo.osName != ''">
          and avi.os_name = #{appVersionInfo.osName}
        </if>
        <if test="appVersionInfo.osVersion != null and appVersionInfo.osVersion != ''">
          and avi.os_version like CONCAT('%',#{appVersionInfo.osVersion},'%')
        </if>
      </if>
    </where>
    order by avi.create_time desc
  </select>
</mapper>
