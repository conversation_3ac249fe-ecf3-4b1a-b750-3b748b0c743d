<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.taskscheduler.mapper.JobEntityMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.taskscheduler.entity.JobEntity">
    <id column="id" property="id"/>
    <result column="job_name" property="jobName"/>
    <result column="job_group" property="jobGroup"/>
    <result column="job_status" property="jobStatus"/>
    <result column="cron_expression" property="cronExpression"/>
    <result column="remark" property="remark"/>
    <result column="job_task_type" property="jobTaskType"/>
    <result column="api_url" property="apiUrl"/>
    <result column="params" property="params"/>
    <result column="job_type" property="jobType"/>
    <result column="trigger_name" property="triggerName"/>
    <result column="trigger_group" property="triggerGroup"/>
    <result column="is_now_run" property="isNowRun"/>
    <result column="start_date" property="startDate"/>
    <result column="end_date" property="endDate"/>
    <result column="job_class_name" property="jobClassName"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
  </resultMap>

</mapper>
