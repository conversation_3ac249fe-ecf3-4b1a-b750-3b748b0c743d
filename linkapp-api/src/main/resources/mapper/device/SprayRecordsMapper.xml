<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.device.dao.SprayRecordsMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.device.entity.SprayRecords">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="trigger_source" jdbcType="VARCHAR" property="triggerSource" />
    <result column="trigger_time" jdbcType="TIMESTAMP" property="triggerTime" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="spray_hours_" jdbcType="DOUBLE" property="sprayHours" />
    <result column="creator_id_" jdbcType="BIGINT" property="creatorId" />
    <result column="create_time_" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_id_" jdbcType="BIGINT" property="modifyId" />
    <result column="modify_time_" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="remark_" jdbcType="LONGVARCHAR" property="remark" />
  </resultMap>

    <select id="findPage" resultMap="BaseResultMap">
        SELECT
          *
        FROM
          app_spray_records
        WHERE tenant_id = #{appSprayRecords.tenantId}
          and device_code = #{appSprayRecords.deviceCode}
        <if test="appSprayRecords.startTime != null">
          AND end_time <![CDATA[>=]]> #{appSprayRecords.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="appSprayRecords.endTime != null">
          AND end_time <![CDATA[<]]>#{appSprayRecords.endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY modify_time_ DESC, start_time DESC
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_spray_records where id = #{id}
    </select>

  <select id="getTrend" resultMap="BaseResultMap">
    select
      sum(spray_hours_) as spray_hours_ ,
      DATE_FORMAT(end_time , '%Y-%m-%d') end_time
    from
      app_spray_records
    <where>
      <if test="startTime != null">
        and end_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and end_time <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="tenantId != null">
        and tenant_id = #{tenantId,jdbcType=VARCHAR}
      </if>
    </where>
    group by
    DATE_FORMAT(end_time , '%Y-%m-%d') order by end_time;
  </select>

    <select id="findNoEndTimeByDeviceCode" resultMap="BaseResultMap">
        select * from app_spray_records where end_time is null and device_code = #{deviceCode} order by create_time_ desc
    </select>

  <select id="getLatestRecord" resultMap="BaseResultMap">
      select * from app_spray_records
      <where>
        end_time is not null
        and tenant_id = #{tenantId}
        and device_code = #{deviceCode}
      </where>
      order by end_time desc
      limit 1
  </select>

  <select id="getSprayHoursByTime" resultType="double">
    select SUM(spray_hours_)
    from app_spray_records
    where end_time is not null
    and tenant_id = #{tenantId}
    and device_code = #{deviceCode}
    <if test="startTime != null">
      and end_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and end_time <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="getAvgDaySprayHours" resultType="double">
    select avg(sr.daySprayHours) from (
      select SUM(spray_hours_) as daySprayHours,DATE_FORMAT(end_time,'%Y%m%d') dayTime
      from app_spray_records
      where end_time is not null
      and tenant_id = #{tenantId}
      and device_code = #{deviceCode}
      group by DATE_FORMAT(end_time,'%Y%m%d')
    ) sr
  </select>

  <select id="find4Report" resultMap="BaseResultMap">
    SELECT
      a.device_code,
      a.end_time,
      a.spray_hours_,
      a.trigger_time
    FROM
      app_spray_records a
    WHERE
      a.tenant_id = #{tenantId}
      AND date_format( a.trigger_time, "%Y-%m" ) = date_format(#{time}, "%Y-%m" )
  </select>
</mapper>
