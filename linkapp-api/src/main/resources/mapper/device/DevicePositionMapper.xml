<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.device.dao.DevicePositionMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.device.entity.vo.DevicePositionVo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="device_code" jdbcType="VARCHAR" property="deviceCode"/>
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <id column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <id column="device_type_name" jdbcType="VARCHAR" property="deviceTypeName"/>
        <id column="deviceUnitId" jdbcType="VARCHAR" property="deviceUnitId"/>
        <id column="status" jdbcType="INTEGER" property="status"/>
        <id column="online_state" jdbcType="INTEGER" property="onlineState"/>
        <result column="position_x" jdbcType="REAL" property="positionX"/>
        <result column="position_y" jdbcType="REAL" property="positionY"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="model_id_" jdbcType="INTEGER" property="modelId" />
        <result column="type_" jdbcType="INTEGER" property="type" />
    </resultMap>

    <select id="selectPageGlobal" resultMap="BaseResultMap">
        select app_device_position.device_code,
               app_device_position.position_x,
               app_device_position.position_y,
               app_device_position.tenant_id,
               app_device_position.create_time,
               app_device_position.creator,
               app_device_position.modifier,
               app_device_position.modify_time,
                app_device_position.model_id_,
               ld.name              as device_name,
               ldu.device_type_name as device_type_name,
               ld.status as status,
               ld.online_state as online_state,
               null as type_,
               ldu.id as deviceUnitId,
               ld.id as id
        from app_device_position
                 INNER JOIN linkapp_device ld on app_device_position.device_code = ld.code
                    and ld.tenant_id = #{appDevicePositionVo.tenantId}
                 inner join linkapp_device_unit ldu on ld.device_unit_id = ldu.id
        <where>
            ld.delete_state = 1 and is_remove_ = 0
              and app_device_position.tenant_id = #{appDevicePositionVo.tenantId}
--         排除掉配电箱表中的数据
            and ld.code not in (select code_ from app_electric_box where is_delete_ = 0 and tenant_id_ = #{appDevicePositionVo.tenantId})
            <if test="appDevicePositionVo != null and appDevicePositionVo.deviceQueryVo != null and appDevicePositionVo.deviceQueryVo.deviceTypeName != null">
                and ldu.device_type_name = #{appDevicePositionVo.deviceQueryVo.deviceTypeName}
            </if>
            <if test="appDevicePositionVo != null and appDevicePositionVo.deviceTypeNameList != null and appDevicePositionVo.deviceTypeNameList.size() > 0">
                and ldu.device_type_name in
                <foreach collection="appDevicePositionVo.deviceTypeNameList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="appDevicePositionVo != null and appDevicePositionVo.modelId != null">
              and app_device_position.model_id_ = #{appDevicePositionVo.modelId}
            </if>
        </where>
        UNION
        SELECT app_device_position.device_code,
               app_device_position.position_x,
               app_device_position.position_y,
               app_device_position.tenant_id,
               app_device_position.create_time,
               app_device_position.creator,
               app_device_position.modifier,
               app_device_position.modify_time,
                app_device_position.model_id_,
               app_electric_box.code_ as device_name,
               '配电箱' as device_type_name,
                ld.status as status,
                ld.online_state as online_state,
                app_electric_box.type_ ,
                null as deviceUnitId,
                app_electric_box.id as id
        FROM app_device_position
                 INNER JOIN app_electric_box ON app_electric_box.code_ = app_device_position.device_code and app_electric_box.is_delete_ = 0
                 left join linkapp_device ld on ld.code = app_electric_box.monitor_device_code_ and ld.tenant_id = #{appDevicePositionVo.tenantId}
        <where>
            app_device_position.tenant_id = #{appDevicePositionVo.tenantId} and is_remove_ = 0
            <if test="appDevicePositionVo != null and appDevicePositionVo.deviceTypeNameList != null and appDevicePositionVo.deviceTypeNameList.size() > 0">
                and '配电箱' in
                <foreach collection="appDevicePositionVo.deviceTypeNameList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="appDevicePositionVo != null and appDevicePositionVo.modelId != null">
              and app_device_position.model_id_ = #{appDevicePositionVo.modelId}
            </if>
        </where>
        <!--        order by app_device_position.modify_time desc, app_device_position.create_time desc-->
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_device_position
        where id = #{id}
    </select>
</mapper>
