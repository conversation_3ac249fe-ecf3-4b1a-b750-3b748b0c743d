<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.device.mapper.DeviceRefAreaScopeMapper">

  <!-- 通用查询映射结果 -->
  <sql id="Base_Column_List">
	device_code,
	tenant_id,
	function_identifier,
	area_id,
	area_path
</sql>
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.device.entity.DeviceRefAreaScope">
    <result column="device_code" property="deviceCode"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="function_identifier" property="functionIdentifier"/>
    <result column="area_id" property="areaId"/>
    <result column="area_path" property="areaPath"/>
  </resultMap>

  <!-- 通用查询映射结果 -->
  <resultMap id="DeviceResultMap" type="com.easylinkin.linkappapi.device.entity.Device">
    <id column="id" property="id"/>
    <result column="code" property="code"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="delete_state" property="deleteState"/>
    <result column="online_state" property="onlineState"/>
    <result column="battery" property="battery"/>
    <result column="remark" property="remark"/>
    <result column="indoor_location" property="indoorLocation"/>
    <result column="deviceTypeName" property="deviceTypeName"/>
    <result column="alarm_switch" property="alarmSwitch"/>
    <result column="company_id" property="companyId"/>
    <result column="project_id" property="projectId"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="install_time" property="installTime"/>
    <result column="repair_time" property="repairTime"/>
    <result column="next_repair_time" property="nextRepairTime"/>
    <result column="last_push_time" property="lastPushTime"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="area_id" property="areaId"/>
    <result column="latitude" property="latitude"/>
    <result column="longitude" property="longitude"/>
    <result column="site" property="site"/>
    <result column="area_path" property="areaPath"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="linkthing_delete" property="linkthingDelete"/>
  </resultMap>

  <select id="getDeviceListByAreaPathAndFunctionIdentifier" resultMap="DeviceResultMap">
    select
      dras.area_id,
      dras.area_path,
      d.id,
      d.name,
      d.code,
      d.remark,
      d.device_unit_id,
      d.create_time,
      ldt.name deviceTypeName
    from
    device_ref_area_scope dras left join linkapp_device d on dras.device_code=d.code
    LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
    LEFT JOIN linkapp_device_type ldt ON ldt.id = u.device_type_id
    <where>
      and d.delete_state = 1
      <if test="dras.functionIdentifier != null and dras.functionIdentifier != ''">
        and dras.function_identifier = #{dras.functionIdentifier}
      </if>
      <if test="dras.tenantId != null and dras.tenantId != ''">
        and dras.tenant_id = #{dras.tenantId}
      </if>
      <if test="dras.areaId != null and dras.areaId != ''">
        and dras.area_id = #{dras.areaId}
      </if>
      <if test="dras.deviceCode != null and dras.deviceCode != ''">
        <bind name="likeCode" value=" '%' +dras.deviceCode+ '%' " />
        and dras.device_code like #{likeCode}
      </if>
      <if test="dras.areaPath != null and dras.areaPath != ''">
        and (dras.area_path = #{dras.areaPath} or dras.area_path like concat(#{dras.areaPath},':%'))
      </if>
    </where>
  </select>

  <select id="getDeviceInfoByFunctionIdentifier" resultMap="DeviceResultMap">
    select
      dras.area_id,
      a.area_path,
      d.id,
      d.name,
      du.device_type_name deviceTypeName,
      d.code,
      d.remark,
      d.device_unit_id,
      d.create_time
    from
        device_ref_area_scope dras left join linkapp_device d on dras.device_code=d.code
        left join linkapp_device_unit du on d.device_unit_id=du.id
        left join linkapp_area a on dras.area_id=a.id
    <where>
      and d.delete_state = 1
      <if test="dras.functionIdentifier != null and dras.functionIdentifier != ''">
        and dras.function_identifier = #{dras.functionIdentifier}
      </if>
      <if test="dras.tenantId != null and dras.tenantId != ''">
        and dras.tenant_id = #{dras.tenantId}
      </if>
      <if test="dras.deviceCode != null and dras.deviceCode != ''">
        and dras.device_code = #{dras.deviceCode}
      </if>
    </where>
  </select>


<!--  <select id="getDeviceCountByFunctionIdentifier" resultType="java.lang.Integer">-->
<!--    select count(*) from-->
<!--    device_ref_area_scope-->
<!--    <where>-->
<!--      function_identifier =#{pFunctionIdentifier}-->
<!--      and tenant_id=#{pTenantId}-->
<!--      and area_id=#{pAreaId}-->
<!--    </where>-->
<!--  </select>-->

  <select id="getBindDeviceCount" resultType="java.lang.Integer">
    select sum(totalNum) from (
      select
        count(*) totalNum
      from
        regular_temperature_control_info ci left join regular_temperature_control_ref_device crf on ci.id=crf.regular_tci_id and ci.delete_status=0
      <where>
        crf.device_code=#{deviceCode}
      </where>
      union
      select
        count(*) totalNum
      from
        ac_aircondition_device aad
      <where>
        aad.device_code=#{deviceCode}
      </where>
    ) d_
  </select>

  <insert id="batchInsert" parameterType="com.easylinkin.linkappapi.device.entity.DeviceRefAreaScope">
    insert into device_ref_area_scope
    (device_code, tenant_id, function_identifier, area_id, area_path)
    values
    <foreach collection="drasList" item="item" separator=",">
      (#{item.deviceCode}, #{item.tenantId}, #{item.functionIdentifier}, #{item.areaId}, #{item.areaPath})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2021-09-07-->
  <select id="findAllByTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from device_ref_area_scope
    where tenant_id=#{tenantId}
  </select>


</mapper>
