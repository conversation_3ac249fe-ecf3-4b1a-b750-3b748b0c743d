<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.device.mapper.EnvironmentalAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.device.entity.EnvironmentalArea">
        <id column="id" property="id" />
        <result column="tenant_id_" property="tenantId" />
        <result column="parent_id_" property="parentId" />
        <result column="full_id_" property="fullId" />
        <result column="full_name_" property="fullName" />
        <result column="name_" property="name" />
        <result column="code_" property="code" />
        <result column="level_" property="level" />
        <result column="order_" property="order" />
        <result column="type_" property="type" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="selectDeviceByArea" resultType="com.easylinkin.linkappapi.device.dto.EnvironmentalAreaDTO">
        SELECT
          model.code_ AS deviceCode,
          area.*
        FROM
          app_environmental_area area
          LEFT JOIN linkapp_device_model model ON area.id = model.environmental_area_id
          AND model.is_remove_ = 0
        WHERE
          area.tenant_id_ = #{tenantId}
        <if test="id != null and id != ''">
            AND area.id = #{id}
        </if>
        <if test="type != null and type != ''">
            AND area.type_ = #{type}
        </if>
        <if test="level != null and level != ''">
            AND area.level_ = #{level}
        </if>
        <choose>
          <when test="isIncludParent != null and isIncludParent == true">
            AND (area.parent_id_ = #{parentId} OR area.id = #{parentId})
          </when>
          <otherwise>
            AND area.parent_id_ = #{parentId}
          </otherwise>
        </choose>
        <if test="areaIds != null and areaIds.size() > 0">
          and area.id in
          <foreach collection="areaIds" item="id" open="(" separator="," close=")">
            #{id}
          </foreach>
        </if>
    </select>

    <select id="selectWaterAreaStatistics" resultType="com.easylinkin.linkappapi.device.dto.EnvironmentalAreaStatisticsDTO">
        SELECT
          model.code_,
          area.*,
          (
            SELECT SUM( water_increment )
            FROM app_water_records
            WHERE tenant_id = #{tenantId}
            AND device_code = model.code_
            <if test="startTime != null">
                AND collect_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND collect_time <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
            </if>
           ) AS totalWater
        FROM
          app_environmental_area area
          LEFT JOIN linkapp_device_model model ON area.id = model.environmental_area_id
          AND model.is_remove_ = 1
        WHERE
          area.tenant_id_ = #{tenantId}
        <if test="level != null and level != ''">
            AND area.level_ = #{level}
        </if>
        <if test="parentId != null and parentId != ''">
            AND area.parent_id_ = #{parentId}
        </if>
        ORDER BY
          totalWater
        DESC
    </select>

    <select id="selectElectricAreaStatistics" resultType="com.easylinkin.linkappapi.device.dto.EnvironmentalAreaStatisticsDTO">
        SELECT
          model.code_,
          area.*,
          (
            SELECT SUM( electricy_increment )
            FROM app_electricy_records
            WHERE tenant_id = #{tenantId}
            AND device_code = model.code_
            <if test="startTime != null">
              AND collect_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
              AND collect_time <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
            </if>
        ) AS totalElectricy
        FROM
          app_environmental_area area
          LEFT JOIN linkapp_device_model model ON area.id = model.environmental_area_id
          AND model.is_remove_ = 1
        WHERE
          area.tenant_id_ = #{tenantId}
        <if test="level != null and level != ''">
            AND area.level_ = #{level}
        </if>
        <if test="parentId != null and parentId != ''">
            AND area.parent_id_ = #{parentId}
        </if>
        ORDER BY
          totalElectricy
        DESC
    </select>

</mapper>
