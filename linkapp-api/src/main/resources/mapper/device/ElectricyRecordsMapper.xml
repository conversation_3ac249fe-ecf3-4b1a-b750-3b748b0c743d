<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.device.mapper.ElectricyRecordsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.device.entity.ElectricyRecords">
        <id column="id" property="id" />
        <result column="device_code" property="deviceCode" />
        <result column="tenant_id" property="tenantId" />
        <result column="collect_time" property="collectTime" />
        <result column="electricy_increment" property="electricyIncrement" />
        <result column="electricy_total" property="electricyTotal" />
        <result column="stop_reading" jdbcType="DOUBLE" property="stopReading" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="a_phase_voltage" property="aPhaseVoltage" />
        <result column="b_phase_voltage" property="bPhaseVoltage" />
        <result column="c_phase_voltage" property="cPhaseVoltage" />

        <result column="a_phase_current" property="aPhaseCurrent" />
        <result column="b_phase_current" property="bPhaseCurrent" />
        <result column="c_phase_current" property="cPhaseCurrent" />
        <result column="active_power" property="activePower" />

    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_electricy_records
        where tenant_id = #{electricyRecords.tenantId}
        <if test="electricyRecords.deviceCode != null and electricyRecords.deviceCode != ''">
            and device_code = #{electricyRecords.deviceCode}
        </if>
        <if test="electricyRecords.startTime != null">
            AND create_time_ <![CDATA[>=]]> #{electricyRecords.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="electricyRecords.endTime != null">
            AND create_time_ <![CDATA[<]]>#{electricyRecords.endTime,jdbcType=TIMESTAMP}
        </if>
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_electricy_records where id = #{id}
    </select>

    <select id="findByDeviceCode" resultMap="BaseResultMap">
        select * from app_electricy_records where tenant_id = #{tenantId} and device_code = #{deviceCode}  order by create_time_ desc
    </select>

    <select id="getSumElectricyByTime" resultType="double">
        select SUM(electricy_increment)
        from app_electricy_records
        where tenant_id = #{tenantId}
        <if test="deviceCode != null and deviceCode != ''">
            and device_code = #{deviceCode}
        </if>
        <if test="areaId != null and areaId != ''">
            and area_id = #{areaId}
        </if>
        <if test="startTime != null">
            and collect_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and collect_time <![CDATA[<]]>#{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="deviceCodes != null and deviceCodes.size() > 0">
            and device_code in
            <foreach collection="deviceCodes" index="index" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
    </select>
    
    <select id="getLatestRecord" resultMap="BaseResultMap">
        select * 
        from app_electricy_records
        where tenant_id = #{tenantId}
        <if test="deviceCode != null and deviceCode != ''">
            and device_code = #{deviceCode}
        </if>
        order by collect_time desc
        limit 1
    </select>

    <select id="getElectricyStatisticsVoByDay" resultType="com.easylinkin.linkappapi.device.entity.vo.ElectricyStatisticsVo">
        SELECT
            <if test="grainSize != null and grainSize == 0">
                DATE_FORMAT( collect_time, '%Y%m%d' ) days,
                IFNULL(SUM(IFNULL(electricy_increment,0)),0) AS todayElectricy
            </if>
            <if test="grainSize != null and grainSize == 1">
                DATE_FORMAT( collect_time, '%Y%u' ) weeks,
                IFNULL(SUM(IFNULL(electricy_increment,0)),0) AS weekElectricy
            </if>
            <if test="grainSize != null and grainSize == 2">
                DATE_FORMAT( collect_time, '%Y%m' ) months,
                IFNULL(SUM(IFNULL(electricy_increment,0)),0) AS monthElectricy
            </if>
        FROM
          app_electricy_records
        WHERE
            <include refid="where_queryCondition"></include>
        GROUP BY
        <if test="grainSize != null and grainSize == 0">
            days
        </if>
        <if test="grainSize != null and grainSize == 1">
            weeks
        </if>
        <if test="grainSize != null and grainSize == 2">
            months
        </if>
    </select>
    <select id="findOneArea" resultType="com.easylinkin.linkappapi.device.entity.vo.ElectricyRecordsVo">
        SELECT
            a.device_code deviceCode,
            a.electricy_increment electricyIncrement,
            a.collect_time collectTime,
            b.name_ areaName
        FROM
            app_electricy_records a
                LEFT JOIN app_environmental_area b ON a.area_id = b.id
        WHERE
            a.tenant_id = #{tenantId}
          AND b.level_ = 1
          AND b.parent_id_ = 0
          AND date_format( a.collect_time, "%Y-%m" ) = date_format(#{time}, "%Y-%m" )
        ORDER BY a.collect_time ASC
    </select>


    <sql id="where_queryCondition">
      tenant_id = #{tenantId}
        <if test="deviceCode != null and deviceCode != ''">
            AND device_code = #{deviceCode}
        </if>
        <if test="areaId != null and areaId != ''">
            AND area_id = #{areaId}
        </if>
      AND DATE_FORMAT(collect_time,'%Y%m%d') &gt;= DATE_FORMAT(#{startTime}, '%Y%m%d' )
      AND DATE_FORMAT(collect_time,'%Y%m%d') &lt;= DATE_FORMAT(#{endTime}, '%Y%m%d' )
  </sql>


    <select id="selectByList" resultMap="BaseResultMap">
        select id,collect_time,electricy_increment from app_electricy_records
        where electricy_increment>0 and tenant_id = #{electricyRecords.tenantId}
        <if test="electricyRecords.deviceCode != null and electricyRecords.deviceCode != ''">
            and device_code = #{electricyRecords.deviceCode}
        </if>
        <if test="electricyRecords.startTime != null">
            AND create_time_ <![CDATA[>=]]> #{electricyRecords.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="electricyRecords.endTime != null">
            AND create_time_ <![CDATA[<=]]>#{electricyRecords.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="electricyRecords.startTimeStr != null and electricyRecords.endTimeStr != null">
            and create_time_ >= #{electricyRecords.startTimeStr}   and create_time_ <![CDATA[<=]]> #{electricyRecords.endTimeStr}
        </if>
        <if test="electricyRecords.startTimeStrH != null and electricyRecords.endTimeStrH != null">
            and   create_time_ >= #{electricyRecords.startTimeStrH} and  create_time_ <![CDATA[<=]]> #{electricyRecords.endTimeStrH}
        </if>
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="selectByYearMoon" resultType="Map">
        SELECT
        DATE_FORMAT(create_time_, '%Y-%m') AS month,
        SUM(electricy_increment) AS totalElectricyIncrement
        FROM
        app_electricy_records
        <where>
            electricy_increment > 0 and  tenant_id = #{electricyRecords.tenantId}
        <if test="electricyRecords.deviceCode != null and electricyRecords.deviceCode != ''">
            and device_code = #{electricyRecords.deviceCode}
        </if>
        </where>
        GROUP BY
        DATE_FORMAT(create_time_, '%Y-%m')
        ORDER BY
        month ASC;
    </select>

    <select id="selectListByYears" resultType="com.easylinkin.linkappapi.device.entity.ElectricyRecords">
        select id,DATE(collect_time) AS collectTime,SUM(electricy_increment) AS electricyIncrement
        from app_electricy_records
          where electricy_increment>0 and tenant_id = #{electricyRecords.tenantId}
        <if test="electricyRecords.deviceCode != null and electricyRecords.deviceCode != ''">
            and device_code = #{electricyRecords.deviceCode}
        </if>
        <if test="electricyRecords.startTime != null">
            AND create_time_ <![CDATA[>=]]> #{electricyRecords.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="electricyRecords.endTime != null">
            AND create_time_ <![CDATA[<=]]>#{electricyRecords.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="electricyRecords.startTimeStr != null and electricyRecords.endTimeStr != null">
            and  DATE_FORMAT( collect_time,'%Y-%m-%d') >= #{electricyRecords.startTimeStr} and DATE_FORMAT( collect_time,'%Y-%m-%d')<![CDATA[<=]]> #{electricyRecords.endTimeStr}
        </if>
        GROUP BY collectTime
    </select>
    <select id="codeByNewRecords" resultType="com.easylinkin.linkappapi.device.entity.ElectricyRecords">
SELECT DISTINCT  dr.*
FROM app_electricy_records dr
INNER JOIN (
    SELECT device_code, MAX(collect_time) AS max_time
    FROM app_electricy_records
    WHERE  tenant_id = #{tenantId} and device_code IN
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
    GROUP BY device_code
) AS latest
ON dr.device_code = latest.device_code
AND dr.collect_time = latest.max_time
    </select>

    <select id="getEarlyRecord" resultMap="BaseResultMap">
        select *
        from app_electricy_records
        where tenant_id = #{tenantId}
        <if test="deviceCode != null and deviceCode != ''">
            and device_code = #{deviceCode}
        </if>
        order by collect_time asc
        limit 1
    </select>
</mapper>
