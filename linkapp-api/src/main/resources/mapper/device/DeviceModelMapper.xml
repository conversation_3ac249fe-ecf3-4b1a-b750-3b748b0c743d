<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.device.mapper.DeviceModelMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.device.entity.DeviceModel">
    <id column="auto_id" property="autoId"/>
    <result column="code_" property="code"/>
    <result column="model_id_" property="modelId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="remark_" property="remark"/>
    <result column="is_remove_" property="removeStatus" />
    <result column="environmental_area_id" property="environmentalAreaId"/>
  </resultMap>

  <delete id="deleteByCodes" parameterType="list">
    delete from linkapp_device_model where code_ in
    <foreach collection="deviceCodes" index="index" item="code" open="(" close=")" separator=",">
      #{code}
    </foreach>
  </delete>

  <delete id="deleteByCondition" >
    delete from linkapp_device_model
    where
    <if test="modelId != null">
      model_id_ = #{modelId}
    </if>
    <if test="tenantId != null">
      and tenant_id = #{tenantId}
    </if>
    <if test="codes != null">
      and code_ in
      <foreach collection="codes" index="index" item="code" open="(" close=")" separator=",">
        #{code}
      </foreach>
    </if>
  </delete>

  <select id="selectDevicesByModelId" resultMap="BaseResultMap">
    select d.* from linkapp_device_model d
    <where>
      <if test="modelId != null">
        d.model_id_ = #{modelId}
      </if>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
    </where>
  </select>
  <select id="selectByTenantIdAndIsRemove" resultMap="BaseResultMap">
    select d.* from linkapp_device_model d
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="isRemove != null">
        and is_remove_ = #{isRemove}
      </if>
    </where>
  </select>

  <update id="deleteAreaByAreaId">
    update linkapp_device_model
    set environmental_area_id = null
    <where>
      <if test="modelId != null">
        and model_id_ = #{modelId}
      </if>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="environmentalAreaId != null">
        and environmental_area_id = #{environmentalAreaId}
      </if>
    </where>
  </update>

  <select id="findOne" resultMap="BaseResultMap">
    select *
    from linkapp_device_model
    <where>
      is_remove_ = 0
      <if test="modelId != null">
        and model_id_ = #{modelId}
      </if>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="code != null">
        and code_  = #{code}
      </if>
    </where>
  </select>

  <select id="selectModelDeviceByArea" resultType="com.easylinkin.linkappapi.device.entity.DeviceModelDto">
    select model.*,ld.name AS deviceName from linkapp_device_model model
    INNER JOIN linkapp_device ld on ld.code = model.code_ and ld.tenant_id = model.tenant_id and model.model_id_ = #{device.modelId}
    LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
    <where>
      model.is_remove_ = 0
      and ld.delete_state = 1
      <if test="device.tenantId != null and device.tenantId != ''">
        and ld.tenant_id = #{device.tenantId}
      </if>
      <if test="device.id != null and device.id != ''">
        and ld.id = #{device.id}
      </if>
      <if test="device.code != null and device.code != '' and device.codeLikeQuery == null">
        and ld.code = #{device.code}
      </if>
      <if
        test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceUnitCodeList)">
        and u.code in
        <foreach collection="device.deviceUnitCodeList" item="deviceUnitCode" index="index" open="("
          close=")" separator=",">
          #{deviceUnitCode}
        </foreach>
      </if>
      <!--<if test="device.isRefEnvironmentalArea != null and device.isRefEnvironmentalArea == true">-->
        <!--and (model.environmental_area_id is null-->
        <!--<if-->
          <!--test="device.currentEnvironmentalAreaId != null and device.currentEnvironmentalAreaId != ''">-->
          <!--or model.environmental_area_id = #{device.currentEnvironmentalAreaId}-->
        <!--</if>)-->
      <!--</if>-->
    </where>
  </select>

</mapper>
