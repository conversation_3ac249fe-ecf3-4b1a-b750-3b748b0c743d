<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.device.mapper.DeviceMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.device.entity.Device">
    <id column="id" property="id"/>
    <result column="code" property="code"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="delete_state" property="deleteState"/>
    <result column="online_state" property="onlineState"/>
    <result column="battery" property="battery"/>
    <result column="remark" property="remark"/>
    <result column="indoor_location" property="indoorLocation"/>
    <result column="alarm_switch" property="alarmSwitch"/>
    <result column="company_id" property="companyId"/>
    <result column="project_id" property="projectId"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="install_time" property="installTime"/>
    <result column="repair_time" property="repairTime"/>
    <result column="next_repair_time" property="nextRepairTime"/>
    <result column="last_push_time" property="lastPushTime"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="area_id" property="areaId"/>
    <result column="latitude" property="latitude"/>
    <result column="longitude" property="longitude"/>
    <result column="site" property="site"/>
    <result column="area_path" property="areaPath"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="linkthing_delete" property="linkthingDelete"/>
    <result column="filing_no" property="filingNo"/>
    <result column="work_status" property="workStatus"/>
    <result column="manufacturer" property="manufacturer"/>
    <result column="manufacturer_tel_" property="manufacturerTel"/>
    <result column="supplier" property="supplier"/>
    <result column="responsible_person" property="responsiblePerson"/>
    <result column="responsible_person_tel_" property="responsiblePersonTel"/>
    <result column="collect_freq" property="collectFreq"/>
    <result column="elect_status" property="electStatus"/>
    <result column="high_device_type" property="highDeviceType"/>
    <result column="high_work_mode" property="highWorkMode"/>
  </resultMap>

  <resultMap id="DeviceMachineryDtoMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.machinery.dto.DeviceMachineryDto">
    <result column="unitTypeName" property="unitTypeName"/>
    <association property="deviceTypeInfo" column="device_type_id" select="com.easylinkin.linkappapi.devicetype.mapper.DeviceTypeMapper.selectById" />
    <association property="machineryDeviceRef" column="{machineryId=machinery_id,deviceId=id}" select="com.easylinkin.linkappapi.machinery.mapper.MachineryDeviceRefMapper.selectMachineryDeviceBy" />
  </resultMap>
  <resultMap id="DeviceMachineryNoRefDtoMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.machinery.dto.DeviceMachineryDto">
    <result column="unitTypeName" property="unitTypeName"/>
    <result column="version" property="deviceUnitVersion"/>
    <association property="deviceTypeInfo" column="device_type_id" select="com.easylinkin.linkappapi.devicetype.mapper.DeviceTypeMapper.selectById" />
  </resultMap>

  <delete id="deleteInfo">
    delete from linkapp_device_info where tenantId = #{tenantId}
  </delete>


  <select id="selectDevicesByAreaPath" resultMap="BaseResultMap"
    parameterType="com.easylinkin.linkappapi.device.entity.Device">
    select d.*
    from linkapp_device d
    <where>
      and d.delete_state = 1
      <if test="areaPath != null and areaPath != ''">
        and (d.area_path = #{areaPath} or d.area_path like concat(#{areaPath}, '/%'))
      </if>
    </where>
  </select>


  <resultMap id="selectDevicesMap" type="com.easylinkin.linkappapi.device.entity.Device">
    <id column="id" property="id"/>
    <result column="code" property="code"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="delete_state" property="deleteState"/>
    <result column="linkthing_delete" property="linkthingDelete"/>
    <result column="online_state" property="onlineState"/>
    <result column="remark" property="remark"/>
    <result column="indoor_location" property="indoorLocation"/>
    <result column="alarm_switch" property="alarmSwitch"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="install_time" property="installTime"/>
    <result column="repair_time" property="repairTime"/>
    <result column="next_repair_time" property="nextRepairTime"/>
    <result column="last_push_time" property="lastPushTime"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="area_id" property="areaId"/>
    <result column="latitude" property="latitude"/>
    <result column="longitude" property="longitude"/>
    <result column="site" property="site"/>
    <result column="area_path" property="areaPath"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="spaceName" property="spaceName"/>
    <result column="deviceTypeName" property="deviceTypeName"/>
    <result column="areaName" property="areaName"/>
    <result column="deviceUnitName" property="deviceUnitName"/>
    <result column="deviceUnitCode" property="deviceUnitCode"/>
    <result column="deviceUnitVersion" property="deviceUnitVersion"/>
    <result column="spaceId" property="spaceId"/>
    <result column="deviceTypeId" property="deviceTypeId"/>
    <result column="work_status" property="workStatus"/>
    <result column="filing_no" property="filingNo"/>
    <result column="deviceModelRemoveStatus" property="deviceModelRemoveStatus"/>
    <result column="manufacturer" property="manufacturer"/>
    <result column="manufacturer_tel_" property="manufacturerTel"/>
    <result column="supplier" property="supplier"/>
    <result column="responsible_person" property="responsiblePerson"/>
    <result column="responsible_person_tel_" property="responsiblePersonTel"/>
    <result column="collect_freq" property="collectFreq"/>
    <result column="elect_status" property="electStatus"/>
    <result column="high_device_type" property="highDeviceType"/>
    <result column="high_work_mode" property="highWorkMode"/>
    <association property="deviceType"
      javaType="com.easylinkin.linkappapi.devicetype.entity.DeviceType">
      <id column="ldt_id" property="id"/>
      <result column="ldt_ico_path" property="icoPath"/>
    </association>
    <association property="deviceUnit"
      javaType="com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit">
      <result property="deviceTypeName" column="deviceTypeName"/>
      <result property="name" column="deviceUnitName"/>
      <result property="code" column="deviceUnitCode"/>
      <result property="deviceTypeId" column="deviceTypeId"/>
      <result property="version" column="deviceUnitVersion"/>
    </association>
    <collection property="deviceAttributeStatusList"
                column="{pDeviceUnitId=device_unit_id,pCode=code,pTenantId = tenant_id}"
                select="getAttributesList" javaType="java.util.ArrayList"
                ofType="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
    </collection>
  </resultMap>


  <select id="selectDevices" resultMap="selectDevicesMap">
    SELECT ld.id,
           ld.code,
           ld.name,
           ld.status,
           ld.online_state,
           ld.remark,
           ld.indoor_location,
           ld.device_unit_id,
           ld.last_push_time,
           ld.create_time,
           ld.modify_time,
           ld.area_id,
           ld.latitude,
           ld.longitude,
           ld.site,
           ld.area_path,
           ld.tenant_id,
           ld.linkthing_delete,
          ld.filing_no,
           a.area_name        AS areaName,
           s.id               AS spaceId,
           s.space_name       AS spaceName,
           u.device_type_name AS deviceTypeName,
           u.name             AS deviceUnitName,
           u.code             AS deviceUnitCode,
           u.device_type_id   AS deviceTypeId,
           u.version          AS deviceUnitVersion,
           ldt.id             as ldt_id,
           ldt.ico_path       as ldt_ico_path,
           ld.work_status,
           ld.manufacturer,
           ld.manufacturer_tel_,
           ld.supplier,
           ld.responsible_person,
           ld.responsible_person_tel_,
           ld.collect_freq,
           ld.elect_status,
           ld.high_device_type,
           ld.high_work_mode
    FROM linkapp_device ld
           LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
           LEFT JOIN linkapp_device_type ldt ON ldt.id = u.device_type_id
           LEFT JOIN linkapp_area a ON (a.id = ld.area_id)
           LEFT JOIN linkapp_space s ON (s.id = a.space_id)
    <where>
      ld.delete_state = 1
      <if test="device.id != null and device.id != ''">
        and ld.id = #{device.id}
      </if>
      <if test="device.spaceId != null and device.spaceId != ''">
        and s.id = #{device.spaceId}
      </if>
      <if test="device.linkappSpace != null and device.linkappSpace.type != null">
        and s.type = #{device.linkappSpace.type}
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
        and (a.area_path = #{device.areaPath} or a.area_path like concat(#{device.areaPath}, ':%'))
      </if>
      <if test="device.deviceTypeId != null and device.deviceTypeId != ''">
        and u.device_type_id = #{device.deviceTypeId}
      </if>
      <if test="device.deviceTypeName != null and device.deviceTypeName != ''">
        and u.device_type_name = #{device.deviceTypeName}
      </if>
      <if test="device.deviceTypeNames != null and device.deviceTypeNames.size() > 0">
        and u.device_type_name in
        <foreach collection="device.deviceTypeNames" item="deviceTypeName" index="index" open="("
          close=")" separator=",">
          #{deviceTypeName}
        </foreach>
      </if>
      <if test="device.areaId != null and device.areaId != ''">
        and ld.area_id = #{device.areaId}
      </if>
      <if test="device.queryTimeStart != null and device.queryTimeStart != ''">
        and ld.modify_time >= #{device.queryTimeStart}
      </if>
      <if test="device.queryTimeEnd != null and device.queryTimeEnd != ''">
        and #{device.queryTimeEnd} >= ld.modify_time
      </if>

      <if test="device.name != null and device.name != ''">
        and ld.name like CONCAT('%', #{device.name}, '%')
      </if>
      <if test="device.code != null and device.code != '' and device.codeLikeQuery == null">
        and ld.code = #{device.code}
      </if>
      <if test="device.codeLikeQuery != null and device.codeLikeQuery != ''">
        and ld.code like CONCAT('%', #{device.codeLikeQuery}, '%')
      </if>
      <if test="device.status != null">
        and ld.status = #{device.status}
      </if>
      <if test="device.onlineState != null">
        and ld.online_state = #{device.onlineState}
      </if>
      <if test="device.projectId != null and device.projectId != ''">
        and ld.project_id = #{device.projectId}
      </if>
      <if test="device.tenantId != null and device.tenantId != ''">
        and ld.tenant_id = #{device.tenantId}
      </if>
      <if test="device.deviceUnitId != null and device.deviceUnitId != ''">
        and ld.device_unit_id = #{device.deviceUnitId}
      </if>
      <if test="device.deviceUnitVersion != null and device.deviceUnitVersion != ''">
        and u.version = #{device.deviceUnitVersion}
      </if>
      <if test="device.deviceUnitCode != null and device.deviceUnitCode != ''">
        and u.code = #{device.deviceUnitCode}
      </if>
      <if test="device.creator != null and device.creator != ''">
        and ld.creator like CONCAT('%', #{device.creator}, '%')
      </if>
      <if
        test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceUnitCodeList)">
        and u.code in
        <foreach collection="device.deviceUnitCodeList" item="deviceUnitCode" index="index" open="("
          close=")" separator=",">
          #{deviceUnitCode}
        </foreach>
      </if>
      <if
        test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceCodeList)">
        and ld.code in
        <foreach collection="device.deviceCodeList" item="deviceCode" index="index" open="("
          close=")" separator=",">
          #{deviceCode}
        </foreach>
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.areaIds)">
        and ld.area_id in
        <foreach collection="device.areaIds" item="areaId" index="index" open="(" close=")"
          separator=",">
          #{areaId}
        </foreach>
      </if>
    </where>
    <choose>
      <when test="device.sortModels != null and device.sortModels.size() > 0">
        ORDER BY
        <trim suffixOverrides=",">
          <foreach collection="device.sortModels" item="item" index="index">
            ${item.field} ${item.sortRule},
          </foreach>
        </trim>
      </when>
      <otherwise>
        ORDER BY ld.modify_time DESC,ld.create_time DESC,ld.code ASC
      </otherwise>
    </choose>

    <!--    ORDER BY ld.modify_time-->
    <!--    <choose>-->
    <!--      <when test="device.sortAsc">-->
    <!--        ASC-->
    <!--      </when>-->
    <!--      <otherwise>-->
    <!--        DESC-->
    <!--      </otherwise>-->
    <!--    </choose>-->
    <!--    ,ld.code ASC-->
  </select>

  <select id="selectModelDevices" resultMap="selectDevicesMap">
    SELECT
      ld.id,
      ld.code,
      ld.name,
      ld.status,
      ld.online_state,
      ld.remark,
      ld.indoor_location,
      ld.device_unit_id,
      ld.last_push_time,
      ld.create_time,
      ld.modify_time,
      ld.area_id,
      ld.latitude,
      ld.longitude,
      ld.site,
      ld.area_path,
      ld.tenant_id,
      ld.linkthing_delete,
      model.is_remove_ as deviceModelRemoveStatus,
      a.area_name        AS areaName,
      s.id               AS spaceId,
      s.space_name       AS spaceName,
      u.device_type_name AS deviceTypeName,
      u.name             AS deviceUnitName,
      u.code             AS deviceUnitCode,
      u.device_type_id   AS deviceTypeId,
      u.version          AS deviceUnitVersion,
      ldt.id             as ldt_id,
      ldt.ico_path       as ldt_ico_path,
      ld.work_status
    FROM
      linkapp_device_model model
    INNER JOIN linkapp_device ld on ld.code = model.code_ and ld.tenant_id = model.tenant_id and model.model_id_ = #{device.modelId}
    LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
    LEFT JOIN linkapp_device_type ldt ON ldt.id = u.device_type_id
    LEFT JOIN linkapp_area a ON (a.id = ld.area_id)
    LEFT JOIN linkapp_space s ON (s.id = a.space_id)
    <where>
      <if test="device.tenantId != null and device.tenantId != ''">
        and ld.tenant_id = #{device.tenantId}
      </if>
      <if test="device.id != null and device.id != ''">
        and ld.id = #{device.id}
      </if>
      <if test="device.code != null and device.code != '' and device.codeLikeQuery == null">
        and ld.code = #{device.code}
      </if>
      <if
        test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceUnitCodeList)">
        and u.code in
        <foreach collection="device.deviceUnitCodeList" item="deviceUnitCode" index="index" open="("
          close=")" separator=",">
          #{deviceUnitCode}
        </foreach>
      </if>
      <if test="device.deviceUnitCode != null and device.deviceUnitCode != ''">
        and u.code = #{device.deviceUnitCode}
      </if>
      <if test="device.codeLikeQuery != null and device.codeLikeQuery != ''">
        and ld.code like CONCAT('%', #{device.codeLikeQuery}, '%')
      </if>
      <if test="device.status != null">
        and ld.status = #{device.status}
      </if>
      <if test="device.linkthingDelete != null">
        and ld.linkthing_delete = #{device.linkthingDelete}
      </if>
      <if test="device.deleteState != null">
        and ld.delete_state = #{device.deleteState}
      </if>
      <if test="device.onlineState != null">
        and ld.online_state = #{device.onlineState}
      </if>
    </where>
    GROUP BY ld.code
    <choose>
      <when test="device.sortModels != null and device.sortModels.size() > 0">
        ORDER BY
        <trim suffixOverrides=",">
          <foreach collection="device.sortModels" item="item" index="index">
            ${item.field} ${item.sortRule},
          </foreach>
        </trim>
      </when>
      <otherwise>
        ORDER BY ld.modify_time DESC,ld.code ASC
      </otherwise>
    </choose>
  </select>


  <select id="selectDevicesFilter" resultMap="selectDevicesMap">
    SELECT ld.id,
           ld.code,
           ld.name,
           ld.status,
           ld.online_state,
           ld.remark,
           ld.indoor_location,
           ld.device_unit_id,
           ld.last_push_time,
           ld.create_time,
           ld.modify_time,
           ld.area_id,
           ld.latitude,
           ld.longitude,
           ld.site,
           ld.linkthing_delete,
           ld.area_path,
           ld.tenant_id,
           a.area_name        AS areaName,
           s.id               AS spaceId,
           s.space_name       AS spaceName,
           u.device_type_name AS deviceTypeName,
           u.name             AS deviceUnitName,
           u.code             AS deviceUnitCode,
           u.device_type_id   AS deviceTypeId,
           u.version          AS deviceUnitVersion,
           ldt.id             as ldt_id,
           ldt.ico_path       as ldt_ico_path
      FROM (
      select *
      from linkapp_device d_
      where not exists(
      select 1
      from device_ref_area_scope ras
      where d_.code = ras.device_code
        and ras.function_identifier = #{device.functionIdentifier}
    <if test="device.tenantId != null and device.tenantId != ''">
      and ras.tenant_id = #{device.tenantId}
    </if>
    )
      and exists(
      select 1
      from tenant_function_ref_area tfa
      where d_.area_id = tfa.area_id
        and tfa.function_identifier = #{device.functionIdentifier}
    <if test="device.tenantId != null and device.tenantId != ''">
      and tfa.tenant_id = #{device.tenantId}
    </if>
    )
      ) ld
      LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
      LEFT JOIN linkapp_device_type ldt ON ldt.id = u.device_type_id
      LEFT JOIN linkapp_area a ON (a.id = ld.area_id)
      LEFT JOIN linkapp_space s ON (s.id = a.space_id)
    <where>
      ld.delete_state = 1
      <if test="device.id != null and device.id != ''">
        and ld.id = #{device.id}
      </if>
      <if test="device.spaceId != null and device.spaceId != ''">
        and s.id = #{device.spaceId}
      </if>
      <if test="device.linkappSpace != null and device.linkappSpace.type != null">
        and s.type = #{device.linkappSpace.type}
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
        and (a.area_path = #{device.areaPath} or a.area_path like concat(#{device.areaPath}, ':%'))
      </if>
      <if test="device.deviceTypeId != null and device.deviceTypeId != ''">
        and u.device_type_id = #{device.deviceTypeId}
      </if>
      <if test="device.areaId != null and device.areaId != ''">
        and ld.area_id = #{device.areaId}
      </if>
      <if test="device.queryTimeStart != null and device.queryTimeStart != ''">
        and ld.modify_time >= #{device.queryTimeStart}
      </if>
      <if test="device.queryTimeEnd != null and device.queryTimeEnd != ''">
        and #{device.queryTimeEnd} >= ld.modify_time
      </if>

      <if test="device.name != null and device.name != ''">
        and ld.name like CONCAT('%', #{device.name}, '%')
      </if>
      <if test="device.code != null and device.code != '' and device.codeLikeQuery == null">
        and ld.code = #{device.code}
      </if>
      <if test="device.codeLikeQuery != null and device.codeLikeQuery != ''">
        and ld.code like CONCAT('%', #{device.codeLikeQuery}, '%')
      </if>
      <if test="device.status != null">
        and ld.status = #{device.status}
      </if>
      <if test="device.onlineState != null">
        and ld.online_state = #{device.onlineState}
      </if>
      <if test="device.projectId != null and device.projectId != ''">
        and ld.project_id = #{device.projectId}
      </if>
      <if test="device.deviceUnitId != null and device.deviceUnitId != ''">
        and ld.device_unit_id = #{device.deviceUnitId}
      </if>
      <if test="device.deviceUnitVersion != null and device.deviceUnitVersion != ''">
        and u.version = #{device.deviceUnitVersion}
      </if>
      <if test="device.deviceUnitCode != null and device.deviceUnitCode != ''">
        and u.code = #{device.deviceUnitCode}
      </if>
      <if test="device.creator != null and device.creator != ''">
        and ld.creator like CONCAT('%', #{device.creator}, '%')
      </if>
      <if
        test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceUnitCodeList)">
        and u.code in
        <foreach collection="device.deviceUnitCodeList" item="deviceUnitCode" index="index" open="("
          close=")" separator=",">
          #{deviceUnitCode}
        </foreach>
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.areaIds)">
        and ld.area_id in
        <foreach collection="device.areaIds" item="areaId" index="index" open="(" close=")"
          separator=",">
          #{areaId}
        </foreach>
      </if>
      <if test="device.deviceTypeIds != null and device.deviceTypeIds.size() > 0">
        and ldt.id in
        <foreach collection="device.deviceTypeIds" item="deviceType" open="(" separator=","
          close=")">
          #{deviceType}
        </foreach>
      </if>
      <if test="device.tenantId != null and device.tenantId != ''">
        and ld.tenant_id = #{device.tenantId}
      </if>
    </where>
    <choose>
      <when test="device.sortModels != null and device.sortModels.size() > 0">
        ORDER BY
        <trim suffixOverrides=",">
          <foreach collection="device.sortModels" item="item" index="index">
            ${item.field} ${item.sortRule},
          </foreach>
        </trim>
      </when>
      <otherwise>
        ORDER BY ld.modify_time DESC,ld.code ASC
      </otherwise>
    </choose>
  </select>

  <select id="selectDevicesFilterDifferBind" resultMap="selectDevicesMap">
    SELECT ld.id,
           ld.code,
           ld.name,
           ld.status,
           ld.online_state,
           ld.indoor_location,
           ld.device_unit_id,
           ld.last_push_time,
           ld.create_time,
           ld.modify_time,
           ld.area_id,
           ld.latitude,
           ld.longitude,
           ld.site,
           ld.linkthing_delete,
           ld.area_path,
           ld.tenant_id,
           a.area_name        AS areaName,
           s.id               AS spaceId,
           s.space_name       AS spaceName,
           u.device_type_name AS deviceTypeName,
           u.name             AS deviceUnitName,
           u.code             AS deviceUnitCode,
           u.device_type_id   AS deviceTypeId,
           u.version          AS deviceUnitVersion,
           ldt.id             as ldt_id,
           ldt.ico_path       as ldt_ico_path,
           ld.bind_device_code   remark
      FROM (
      select d_.*, if(ras.device_code is null, 0, 1) bind_device_code from linkapp_device d_
      left join device_ref_area_scope ras on  d_.code = ras.device_code and d_.delete_state = 1
    <if test="device.functionIdentifier != null and device.functionIdentifier != ''">
      and ras.function_identifier = #{device.functionIdentifier}
    </if>
    <if test="device.tenantId != null and device.tenantId != ''">
      and ras.tenant_id = #{device.tenantId}
    </if>
    <if test="device.areaId != null and device.areaId != ''">
      and ras.area_id = #{device.areaId}
    </if>
    <if test="device.areaPath != null and device.areaPath != ''">
      and
        (ras.area_path = #{device.areaPath} or ras.area_path like concat(#{device.areaPath}, ':%'))
    </if>
    <where>
      d_.delete_state = 1
      <if test="device.tenantId != null and device.tenantId != ''">
        and d_.tenant_id = #{device.tenantId}
      </if>
    </where>
    ) ld
      LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
      LEFT JOIN linkapp_device_type ldt ON ldt.id = u.device_type_id
      LEFT JOIN linkapp_area a ON (a.id = ld.area_id)
      LEFT JOIN linkapp_space s ON (s.id = a.space_id)
    <where>
      ld.delete_state = 1
      <if test="device.id != null and device.id != ''">
        and ld.id = #{device.id}
      </if>
      <if test="device.spaceId != null and device.spaceId != ''">
        and s.id = #{device.spaceId}
      </if>
      <if test="device.linkappSpace != null and device.linkappSpace.type != null">
        and s.type = #{device.linkappSpace.type}
      </if>
      <!--<if test="device.areaPath != null and device.areaPath != ''">-->
      <!--and (a.area_path = #{device.areaPath} or a.area_path like concat(#{device.areaPath},':%'))-->
      <!--</if>-->
      <if test="device.deviceTypeId != null and device.deviceTypeId != ''">
        and u.device_type_id = #{device.deviceTypeId}
      </if>
      <!--<if test="device.areaId != null and device.areaId != ''">-->
      <!--and ld.area_id = #{device.areaId}-->
      <!--</if>-->
      <if test="device.queryTimeStart != null and device.queryTimeStart != ''">
        and ld.modify_time >= #{device.queryTimeStart}
      </if>
      <if test="device.queryTimeEnd != null and device.queryTimeEnd != ''">
        and #{device.queryTimeEnd} >= ld.modify_time
      </if>

      <if test="device.name != null and device.name != ''">
        and ld.name like CONCAT('%', #{device.name}, '%')
      </if>
      <if test="device.code != null and device.code != '' and device.codeLikeQuery == null">
        and ld.code = #{device.code}
      </if>
      <if test="device.codeLikeQuery != null and device.codeLikeQuery != ''">
        and ld.code like CONCAT('%', #{device.codeLikeQuery}, '%')
      </if>
      <if test="device.status != null">
        and ld.status = #{device.status}
      </if>
      <if test="device.onlineState != null">
        and ld.online_state = #{device.onlineState}
      </if>
      <if test="device.projectId != null and device.projectId != ''">
        and ld.project_id = #{device.projectId}
      </if>
      <if test="device.deviceUnitId != null and device.deviceUnitId != ''">
        and ld.device_unit_id = #{device.deviceUnitId}
      </if>
      <if test="device.deviceUnitVersion != null and device.deviceUnitVersion != ''">
        and u.version = #{device.deviceUnitVersion}
      </if>
      <if test="device.deviceUnitCode != null and device.deviceUnitCode != ''">
        and u.code = #{device.deviceUnitCode}
      </if>
      <if test="device.creator != null and device.creator != ''">
        and ld.creator like CONCAT('%', #{device.creator}, '%')
      </if>
      <if
        test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceUnitCodeList)">
        and u.code in
        <foreach collection="device.deviceUnitCodeList" item="deviceUnitCode" index="index" open="("
          close=")" separator=",">
          #{deviceUnitCode}
        </foreach>
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.areaIds)">
        and ld.area_id in
        <foreach collection="device.areaIds" item="areaId" index="index" open="(" close=")"
          separator=",">
          #{areaId}
        </foreach>
      </if>
      <if test="device.deviceTypeIds != null and device.deviceTypeIds.size() > 0">
        and ldt.id in
        <foreach collection="device.deviceTypeIds" item="deviceType" open="(" separator=","
          close=")">
          #{deviceType}
        </foreach>
      </if>
      <if test="device.tenantId != null and device.tenantId != ''">
        and ld.tenant_id = #{device.tenantId}
      </if>
    </where>
    <choose>
      <when test="device.sortModels != null and device.sortModels.size() > 0">
        ORDER BY
        <trim suffixOverrides=",">
          <foreach collection="device.sortModels" item="item" index="index">
            ${item.field} ${item.sortRule},
          </foreach>
        </trim>
      </when>
      <otherwise>
        ORDER BY ld.bind_device_code ASC, ld.modify_time DESC,ld.code ASC
      </otherwise>
    </choose>
  </select>

  <select id="getDeviceSiteInfos" resultType="com.easylinkin.linkappapi.device.entity.Device">
    SELECT d.id,
           d.code,
           d.name,
           d.latitude,
           d.longitude,
           d.status,
           d.online_state,
           d.indoor_location,
           la.space_id  as spaceId,
           ldt.name     as deviceTypeName,
           ldt.id       as deviceTypeId,
           ldt.ico_path as typeIcoPath
    FROM linkapp_device d
           inner JOIN linkapp_device_unit u ON d.device_unit_id = u.id
           left join linkapp_device_type ldt on ldt.id = u.device_type_id
           left join linkapp_area la on d.area_id = la.id
    <where>
      d.delete_state = 1
      <if test="device.id != null and device.id != ''">
        and d.id = #{device.id}
      </if>
      <if test="device.status != null">
        and d.status = #{device.status}
      </if>
      <if test="device.onlineState != null">
        and d.online_state = #{device.onlineState}
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
        and (d.area_path = #{device.areaPath} or d.area_path like concat(#{device.areaPath}, ':%'))
      </if>
      <if test="device.deviceTypeId != null and device.deviceTypeId != ''">
        and u.device_type_id = #{device.deviceTypeId}
      </if>
      <if test="device.deviceType != null and device.deviceType != ''">
        and u.device_type_id = #{device.deviceType}
      </if>
      <if test="device.areaId != null and device.areaId != ''">
        and d.area_id = #{device.areaId}
      </if>
    </where>
    ORDER BY d.modify_time, d.code ASC
  </select>


  <select id="selectDevicesWithAlarmRule"
    resultType="com.easylinkin.linkappapi.device.entity.Device">
    SELECT d.id               AS id,
           d.CODE             AS CODE,
           d.NAME             AS NAME,
           d.STATUS           AS STATUS,
           d.delete_state     AS deleteState,
           d.online_state     AS onlineState,
           d.battery          AS battery,
           d.remark           AS remark,
           d.alarm_switch     AS alarmSwitch,
           d.project_id       AS projectId,
           d.device_unit_id   AS deviceUnitId,
           d.install_time     AS installTime,
           d.repair_time      AS repairTime,
           d.next_repair_time AS nextRepairTime,
           d.last_push_time   AS lastPushTime,
           d.create_time      AS createTime,
           d.creator          AS creator,
           d.modifier         AS modifier,
           d.modify_time      AS modifyTime,
           d.area_id          AS areaId,
           d.latitude         AS latitude,
           d.longitude        AS longitude,
           d.site             AS site,
           d.area_path        AS areaPath,
           d.tenant_id        as tenantId,
           d.linkthing_delete,
           s.space_name       AS spaceName,
           a.area_name        AS areaName,
           u.name             AS deviceUnitName,
           u.code             AS deviceUnitCode,
           u.version          AS deviceUnitVersion,
           s.id               AS spaceId,
           u.device_type_name AS deviceTypeName,
           u.device_type_id   AS deviceTypeId

    FROM linkapp_device d
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
           LEFT JOIN linkapp_area a ON a.id = d.area_id
           LEFT JOIN linkapp_space s ON s.id = a.space_id
           LEFT JOIN linkapp_alarm_rule_relate re
      ON re.delete_state = 1 AND re.device_code = d.code AND
         re.alarm_rule_id = #{device.alarmRuleId}
    <where>
      d.delete_state = 1
      <if test="device.id != null and device.id != ''">
        and d.id = #{device.id}
      </if>
      <if test="device.spaceId != null and device.spaceId != ''">
        and s.id = #{device.spaceId}
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
        and (d.area_path = #{device.areaPath} or d.area_path like concat(#{device.areaPath}, ':%'))
      </if>
      <if test="device.deviceTypeId != null and device.deviceTypeId != ''">
        and u.device_type_id = #{device.deviceTypeId}
      </if>
      <if test="device.areaId != null and device.areaId != ''">
        and d.area_id = #{device.areaId}
      </if>
      <if test="device.queryTimeStart != null and device.queryTimeStart != ''">
        and d.modify_time >= #{device.queryTimeStart}
      </if>
      <if test="device.queryTimeEnd != null and device.queryTimeEnd != ''">
        and #{device.queryTimeEnd} >= d.modify_time
      </if>

      <if test="device.name != null and device.name != ''">
        and d.name like CONCAT('%', #{device.name}, '%')
      </if>

      <if test="device.code != null and device.code != ''">
        and d.code like CONCAT('%', #{device.code}, '%')
      </if>

      <if test="device.status != null">
        and d.status = #{device.status}
      </if>
      <if test="device.onlineState != null">
        and d.online_state = #{device.onlineState}
      </if>
      <if test="device.projectId != null and device.projectId != ''">
        and d.project_id = #{device.projectId}
      </if>
      <if test="device.deviceUnitId != null and device.deviceUnitId != ''">
        and d.device_unit_id = #{device.deviceUnitId}
      </if>
      <if test="device.creator != null and device.creator != ''">
        and d.creator like CONCAT('%', #{device.creator}, '%')
      </if>
      <if test="!device.notBindAlarmRule">
        and re.alarm_rule_id = #{device.alarmRuleId}
      </if>
      <if test="device.notBindAlarmRule">
        and re.id is null
      </if>
    </where>
    ORDER BY d.modify_time DESC, d.code ASC
  </select>


  <select id="selectAssetIdsByIds" resultType="java.lang.String">
    select asset_id
    from linkapp_asset_ref_device where device_id in
    <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="getAttributeStatusList"
    resultType="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
    SELECT das.prop_code  propCode,
           das.prop_name  propName,
           das.prop_value propValue
    FROM linkapp_device_attribute_status das
           LEFT JOIN linkapp_device d
      ON das.device_code = d.code AND d.delete_state = 1
           LEFT JOIN linkapp_device_unit u
      ON d.device_unit_id = u.id AND u.delete_state = 1
    <where>
      das.version = u.version
        AND das.device_code = #{code}
    </where>
  </select>


  <select id="getStatisticsByType" resultType="java.util.Map">
    SELECT u.device_type_name typeName,
           count(1)           deviceCount
    FROM linkapp_device d
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
      WHERE d.delete_state = 1
    <if test="onlineState != null">
      and d.online_state = #{onlineState}
    </if>
    GROUP BY u.device_type_name;
  </select>

  <select id="getStatisticsExistDeviceByType" resultType="com.easylinkin.linkappapi.device.vo.DeviceTypeCountVo">
    SELECT t1.*
      FROM
      (
      SELECT u.device_type_name AS deviceTypeName,
             u.device_type_id   AS deviceTypeId,
             ldt.ico_path       as icoPath,
             count(d.id)        AS deviceCount
      FROM linkapp_device d
             LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
             LEFT JOIN linkapp_device_type ldt ON u.device_type_id = ldt.id
             left join linkapp_area la on la.id = d.area_id
             left join linkapp_space ls on ls.id = la.space_id
    <where>
      d.delete_state = 1
      <if test="device.areaPath != null and device.areaPath != ''">
        and (d.area_path = #{device.areaPath} or d.area_path like concat(#{device.areaPath}, ':%'))
      </if>
      <if test="device.linkappSpace != null and device.linkappSpace.type != null">
        and ls.type = #{device.linkappSpace.type}
      </if>
      <if test="device.onlineState != null">
        and d.online_state = #{device.onlineState}
      </if>
      <if test="device.tenantId != null and device.tenantId != ''">
        and d.tenant_id = #{device.tenantId}
      </if>
      AND u.device_type_name IS NOT NULL
      AND char_length(u.device_type_name) > 0
    </where>
    GROUP BY u.device_type_name
      ) t1
    <where>
      t1.deviceCount > 0
    </where>
    ORDER BY t1.deviceTypeId
  </select>

  <!-- 根据设备状态统计设备数 -->
  <select id="countDeviceByStatus" resultType="java.util.Map">
    SELECT d.`status`  AS status,
           COUNT(d.id) AS deviceCount
    FROM linkapp_device d
      where d.delete_state = 1
    <if test="device.tenantId != null and device.tenantId != ''">
      and d.tenant_id = #{device.tenantId}
    </if>
    <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.areaIds)">
      and d.area_id in
      <foreach collection="device.areaIds" item="areaId" open="(" separator="," close=")">
        #{areaId}
      </foreach>
    </if>
    <if test="device.areaPath != null and device.areaPath != ''">
      and (d.area_path = #{device.areaPath} or d.area_path like concat(#{device.areaPath}, ':%'))
    </if>
    GROUP BY d.`status`
  </select>

  <!-- 根据设备在线状态 统计设备数 -->
  <select id="countDeviceByOnlineState" resultType="java.util.Map">
    SELECT d.online_state AS onlineState,
           COUNT(d.id)    AS deviceCount
    FROM linkapp_device d
      where d.delete_state = 1
    <if test="device.tenantId != null and device.tenantId != ''">
      and d.tenant_id = #{device.tenantId}
    </if>
    GROUP BY d.online_state;
  </select>

  <!--  统计设备数量-->
  <select id="getStatisticsDeviceCount" resultType="java.util.Map">
    SELECT
    <if test="device.onlineState == null">
      SUM(t.oneDayAddCount) AS addUpCount,
    </if>
    t.* FROM (
      SELECT DISTINCT DATE_FORMAT(d.createTime, #{device.statisticsTimeType}) AS createTime,
                      d.oneDayAddCount                                        AS oneDayAddCount,
                      d.tenantId                                              AS tenantId,
                      onlineState                                             AS onlineState
      FROM linkapp_device_info d
    <where>
      <if test="device.onlineState != null">
        AND d.onlineState = #{device.onlineState}
      </if>
      <if test="device.queryTimeEnd">
        AND d.createTime &lt; #{device.queryTimeEnd}
      </if>
      <if test="device.tenantId">
        AND d.tenantId = #{device.tenantId}
      </if>
    </where>
    ORDER BY d.createTime DESC
      ) t
    <if test="device.onlineState == null">
      GROUP BY t.tenantId, t.createTime
    </if>
    ORDER BY t.createTime desc
    LIMIT #{current},#{size}
  </select>


  <select id="getStatisticsDeviceAddUpCount" resultType="java.util.Map">
    SELECT createTime,
           oneDayAddCount,
           addUpCount
      FROM
      (
      SELECT createTime,
             oneDayAddCount,
             @csum := @csum + t1.oneDayAddCount AS addUpCount
      FROM
      (
      (
      SELECT DATE_FORMAT(create_time, #{device.statisticsTimeType}) AS createTime,
             count(*)                                               AS oneDayAddCount
      FROM linkapp_device d
    <where>
      delete_state = 1
      <if test="device.onlineState != null">
        AND online_state = #{device.onlineState}
      </if>
    </where>
    AND create_time &lt; #{device.queryTimeEnd}
      GROUP BY createTime
      ORDER BY create_time ASC
      ) t1, (select @csum := 0) r
      )
      ) t2
      ORDER BY t2.createTime DESC
      LIMIT #{current},#{size}
  </select>


  <select id="getDeviceStatisticsAudit"
    resultType="com.easylinkin.linkappapi.device.entity.DeviceInfo">
    SELECT
    <if test="device.queryTimeEnd != null">
      #{device.queryTimeEnd} AS createTime,
    </if>
    <if test="device.queryTimeEnd == null">
      CURDATE() AS createTime,
    </if>
    COUNT(*)                  AS oneDayAddCount,
    d.tenant_id               AS tenantId,
    IFNULL(d.online_state, 0) AS onlineState
      FROM `linkapp_device` d
    <where>
      <if test="device.queryTimeEnd != null">
        AND d.create_time &lt;= #{device.queryTimeEnd}
      </if>
      and d.delete_state = 1
    </where>
    GROUP BY onlineState, tenantId
    ORDER BY d.tenant_id, d.online_state
  </select>

  <select id="selectDeviceInfoList" resultType="com.easylinkin.linkappapi.device.entity.DeviceInfo">
    select *
    from linkapp_device_info
    <where>
      <if test="createTime != null">
        and createTime = #{createTime}
      </if>
      <if test="tenantId != null">
        and tenantId = #{tenantId}
      </if>
      <if test="onlineState != null">
        and onlineState = #{onlineState}
      </if>
    </where>
  </select>

  <insert id="insertDeviceInfo">
    insert into linkapp_device_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        createTime,
      </if>
      <if test="oneDayAddCount != null">
        oneDayAddCount,
      </if>
      <if test="tenantId != null">
        tenantId,
      </if>
      <if test="onlineState != null">
        onlineState,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="oneDayAddCount != null">
        #{oneDayAddCount},
      </if>
      <if test="tenantId != null">
        #{tenantId},
      </if>
      <if test="onlineState != null">
        #{onlineState},
      </if>
    </trim>
  </insert>


  <update id="updateDeviceInfo">
    UPDATE
      linkapp_device_info
    <set>
      <if test="oneDayAddCount != null">
        oneDayAddCount = #{oneDayAddCount},
      </if>
      <if test="tenantId != null">
        tenantId = #{tenantId},
      </if>
      <if test="onlineState != null">
        onlineState = #{onlineState},
      </if>
    </set>
    <where>
      <if test="createTime != null">
        and createTime = #{createTime}
      </if>
      <if test="tenantId != null">
        and tenantId = #{tenantId}
      </if>
      <if test="onlineState != null">
        and onlineState = #{onlineState}
      </if>
    </where>
  </update>


  <!--  统计设备在线离线-->
  <select id="getStatisticsDeviceOnlineOffLine" resultType="java.util.Map">
    SELECT createTime,
           oneDayAddCount,
           addUpCount
      FROM
      (
      SELECT createTime,
             oneDayAddCount,
             @csum := @csum + t1.oneDayAddCount AS addUpCount
      FROM
      (
      (
      SELECT DATE_FORMAT(d.create_time, #{device.statisticsTimeType}) AS createTime,
             count(d.id)                                              AS oneDayAddCount
      FROM linkapp_device d
    <where>
      d.delete_state = 1
      <if test="device.onlineState != null">
        AND d.online_state = #{device.onlineState}
      </if>
    </where>
    AND d.create_time
      &lt;
        #{device.queryTimeEnd}
      GROUP BY d.create_time
      ORDER BY d.create_time ASC
      ) t1, (select @csum := 0) v
      )
      ) t2
      ORDER BY t2.createTime DESC
  </select>
  <select id="selectDeviceByCodeGlobal" resultMap="BaseResultMap">
    select *
    from linkapp_device
    <where>
      delete_state = 1
        and code = #{code}
    </where>
  </select>


  <select id="getDeviceCountAndStrengthenByUnitCode" resultType="java.util.Map">
    SELECT u.device_type_name                                                   typeName,
           u.code                                                               unitCode,
           count(distinct d.code)                                               deviceCount,
           count(distinct case when online_state = 1 then d.code else null end) normalCount,
           count(distinct case when online_state = 0 then d.code else null end) offlineCount,
           count(distinct case when status = 1 then d.code else null end)       faultCount
    FROM linkapp_device d
           LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
      WHERE d.delete_state = 1
    <if test="onlineState != null">
      and d.online_state = #{onlineState}
    </if>
    <if test="tenantId != null and tenantId != ''">
      and d.tenant_id = #{tenantId}
    </if>
    <if test="areaId != null and areaId != ''">
      and d.area_id = #{areaId}
    </if>
    <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(areaIds)">
      and d.area_id in
      <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
        #{areaId}
      </foreach>
    </if>
    <if test="areaPath != null and areaPath != ''">
      and (d.area_path = #{areaPath} or d.area_path like concat(#{areaPath}, ':%'))
    </if>
    GROUP BY u.code
  </select>

  <select id="statisticDeviceStatusCountBySpace" resultType="java.util.Map">
    SELECT area_path        as areaPath,
           STATUS           as status,
           count(tmp1.CODE) AS statusCount
    FROM (
           SELECT ld.CODE,
                  ld.STATUS                             AS STATUS,
                  SUBSTRING_INDEX(ld.area_path, ':', 1) AS area_path
           FROM linkapp_device ld
                  INNER JOIN linkapp_alarm la ON ld.CODE = la.device_code
           WHERE ld.tenant_id = #{device.tenantId}
             AND ld.delete_state = 1
             AND ld.`status` = 1
             AND la.alarm_time > #{device.queryTimeStart}
             AND la.STATUS = 1
           UNION
           SELECT ld.CODE,
                  ld.STATUS                             AS STATUS,
                  SUBSTRING_INDEX(ld.area_path, ':', 1) AS area_path
           FROM linkapp_device ld
           WHERE ld.tenant_id = #{device.tenantId}
             AND ld.delete_state = 1
             AND ld.`status` = 0
           ) tmp1
    GROUP BY tmp1.area_path,
             tmp1.STATUS
  </select>

  <!-- 设备监控页 设备查询 -->
  <resultMap id="MonitorDevicesMap" type="com.easylinkin.linkappapi.device.entity.Device">
    <id column="id" property="id"/>
    <result column="code" property="code"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="d_device_unit_code" property="deviceUnitCode"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="online_state" property="onlineState"/>
    <result column="last_push_time" property="lastPushTime"/>
    <result column="area_id" property="areaId"/>
    <result column="area_path" property="areaPath"/>
    <result column="d_deviceTypeName" property="deviceTypeName"/>
    <result column="deviceUnitVersion" property="deviceUnitVersion"/>
    <result column="work_status" property="workStatus"/>
    <association property="deviceType"
      javaType="com.easylinkin.linkappapi.devicetype.entity.DeviceType">
      <result column="ico_path" property="icoPath"/>
    </association>
    <collection property="deviceAttributeStatusList"
      column="{pDeviceUnitId=device_unit_id,pCode=code,pTenantId = tenant_id}"
      select="getAttributesList" javaType="java.util.ArrayList"
      ofType="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
    </collection>
  </resultMap>

  <resultMap id="MonitorDevicesMapWithProperty"
    type="com.easylinkin.linkappapi.device.entity.Device">
    <id column="id" property="id"/>
    <result column="code" property="code"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="d_device_unit_code" property="deviceUnitCode"/>
    <result column="name" property="name"/>
    <result column="status" property="status"/>
    <result column="online_state" property="onlineState"/>
    <result column="last_push_time" property="lastPushTime"/>
    <result column="area_path" property="areaPath"/>
    <result column="d_deviceTypeName" property="deviceTypeName"/>
    <result column="deviceUnitVersion" property="deviceUnitVersion"/>
    <result column="work_status" property="workStatus"/>
    <association property="deviceType"
      javaType="com.easylinkin.linkappapi.devicetype.entity.DeviceType">
      <result column="ico_path" property="icoPath"/>
    </association>
  </resultMap>

  <resultMap id="getAttributesListMap"
    type="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="prop_code" property="propCode"/>
    <result column="ico_path" property="icoPath"/>
    <result column="vo_prop_name" property="propName"/>
    <result column="vo_unit" property="unit"/>
    <result column="specs" property="specs"/>
    <result column="sort_no" property="sortNo"/>
    <result column="is_show" property="isShow"/>
    <result column="vo_prop_value" property="propValue"/>
    <result column="vo_parent_prop_code" property="parentPropCode"/>
    <result column="vo_array_Index" property="arrayIndex"/>
  </resultMap>

  <select id="getAttributesList" resultMap="getAttributesListMap">
    select temp.* from (
      SELECT ldas.id,
             ldas.prop_code,
             ldas.prop_value       AS vo_prop_value,
             ldas.parent_prop_code AS vo_parent_prop_code,
             ldas.array_Index      AS vo_array_Index,
             lda.device_unit_id,
             lda.name              AS vo_prop_name,
             lda.unit              AS vo_unit,
             lda.specs,
    <choose>
      <when test="pTenantId != null and pTenantId != ''">
        ifnull(ldatc.is_show, lda.is_show) is_show,
        ifnull(ldatc.sort_no, lda.sort_no) sort_no,
      </when>
      <otherwise>
        lda.is_show,
        lda.sort_no,
      </otherwise>
    </choose>
    lda.ico_path
      FROM linkapp_device_attribute_status ldas
      LEFT JOIN linkapp_device_attribute lda ON lda.device_unit_id = #{pDeviceUnitId} AND
                                                ldas.prop_code = lda.identifier
    <if test="pTenantId != null and pTenantId != ''">
      left join linkapp_device_attribute_tenant_config ldatc on ldatc.device_attribute_id = lda.id
        and ldatc.tenant_id = #{pTenantId,jdbcType=VARCHAR}
    </if>
    LEFT JOIN linkapp_device d ON ldas.device_code = d.code AND d.delete_state = 1
    LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
      WHERE ldas.version = u.version
        AND ldas.device_code = #{pCode}
      ) temp
      where temp.is_show = 1
      ORDER BY temp.sort_no ASC
  </select>

  <!-- 批量查询设备属性-->
  <select id="getBatchAttributesList" resultMap="getAttributesListMap">
    select temp.* from (
      SELECT ldas.id,
             ldas.device_code,
             ldas.prop_code,
             ldas.prop_value       AS vo_prop_value,
             ldas.parent_prop_code AS vo_parent_prop_code,
             ldas.array_Index      AS vo_array_Index,
             lda.device_unit_id,
             lda.name              AS vo_prop_name,
             lda.unit              AS vo_unit,
             lda.specs,
    <choose>
      <when test="pTenantId != null and pTenantId != ''">
        ifnull(ldatc.is_show, lda.is_show) is_show,
        ifnull(ldatc.sort_no, lda.sort_no) sort_no,
      </when>
      <otherwise>
        lda.is_show,
        lda.sort_no,
      </otherwise>
    </choose>
    lda.ico_path
      FROM linkapp_device_attribute_status ldas
      LEFT JOIN linkapp_device_attribute lda ON lda.device_unit_id = #{pDeviceUnitId} AND
                                                ldas.prop_code = lda.identifier
    <if test="pTenantId != null and pTenantId != ''">
      left join linkapp_device_attribute_tenant_config ldatc on ldatc.device_attribute_id = lda.id
        and ldatc.tenant_id = #{pTenantId,jdbcType=VARCHAR}
    </if>
    LEFT JOIN linkapp_device d ON ldas.device_code = d.code AND d.delete_state = 1
    LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id AND u.delete_state = 1
      WHERE ldas.version = u.version
        AND ldas.device_code in
    <foreach item="item" index="index" collection="pCode" open="(" separator="," close=")">
      #{item}
    </foreach>
    ) temp
      where temp.is_show = 1
      ORDER BY temp.sort_no ASC
  </select>
  <!-- 设备监控页 设备查询 -->
  <select id="getMonitorDevices" resultMap="MonitorDevicesMap">
    SELECT d.*,
    u.device_type_name AS d_deviceTypeName,
    u.code AS d_device_unit_code,
    u.name AS deviceUnitName,
    u.version AS deviceUnitVersion,
    ldt.ico_path,
    d.work_status
    FROM linkapp_device d
    LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
    left join linkapp_device_type ldt on ldt.id = u.device_type_id
    <where>
      d.delete_state = 1
      <if test="device.deviceUnitId != null and device.deviceUnitId != ''">
        and d.device_unit_id = #{device.deviceUnitId}
      </if>
      <if test="device.moniterCommonQuery != null and device.moniterCommonQuery != ''">
        and (d.code like CONCAT('%', #{device.moniterCommonQuery}, '%')
        or d.name like CONCAT('%', #{device.moniterCommonQuery}, '%')
        or u.device_type_name like CONCAT('%', #{device.moniterCommonQuery}, '%')
        or u.code like CONCAT('%', #{device.moniterCommonQuery}, '%')
        )
      </if>
      <if test="device.codes != null and device.codes.size() > 0">
        and d.code in
        <foreach collection="device.codes" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if
        test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceUnitCodeList)">
        and u.code in
        <foreach collection="device.deviceUnitCodeList" item="deviceUnitCode" index="index" open="("
          close=")" separator=",">
          #{deviceUnitCode}
        </foreach>
      </if>
      <if test="device.id != null and device.id != ''">
        and d.id = #{device.id}
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
        and (d.area_path = #{device.areaPath} or d.area_path like concat(#{device.areaPath},':%'))
      </if>
      <if test="device.deviceTypeId != null and device.deviceTypeId != ''">
        and u.device_type_id = #{device.deviceTypeId}
      </if>
      <if test="device.name != null and device.name != ''">
        and d.name like CONCAT('%', #{device.name}, '%')
      </if>
      <if test="device.code != null and device.code != ''">
        and d.code like CONCAT('%', #{device.code}, '%')
      </if>
      <if test="device.status != null">
        and d.status = #{device.status}
      </if>
      <if test="device.onlineState != null">
        and d.online_state = #{device.onlineState}
      </if>
      <if test="device.tenantId != null and device.tenantId != ''">
        and d.tenant_id= #{device.tenantId}
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.areaIds)">
        and d.area_id in
        <foreach collection="device.areaIds" item="areaId" open="(" separator="," close=")">
          #{areaId}
        </foreach>
      </if>
    </where>
    ORDER BY d.modify_time
    <choose>
      <when test="device.sortAsc">
        ASC
      </when>
      <otherwise>
        DESC
      </otherwise>
    </choose>
  </select>

  <!-- 设备监控页 设备查询 高级查询 -->
  <select id="getMonitorDevicesWithProperties" resultMap="MonitorDevicesMapWithProperty">
    SELECT d.*,
    u.device_type_name AS d_deviceTypeName,
    u.code AS d_device_unit_code,
    u.name AS deviceUnitName,
    u.version AS deviceUnitVersion,
    ldt.ico_path,
    d.work_status
    FROM linkapp_device d
    LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
    left join linkapp_device_type ldt on ldt.id = u.device_type_id

    <where>
      d.delete_state = 1
      <if test="device.deviceUnitId != null and device.deviceUnitId != ''">
        and d.device_unit_id = #{device.deviceUnitId}
      </if>
      <if test="device.moniterCommonQuery != null and device.moniterCommonQuery != ''">
        and (d.code like CONCAT('%', #{device.moniterCommonQuery}, '%')
        or d.name like CONCAT('%', #{device.moniterCommonQuery}, '%')
        or u.device_type_name like CONCAT('%', #{device.moniterCommonQuery}, '%')
        or u.code like CONCAT('%', #{device.moniterCommonQuery}, '%')
        )
      </if>
      <if test="device.codes != null and device.codes.size() > 0">
        and d.code in
        <foreach collection="device.codes" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="device.id != null and device.id != ''">
        and d.id = #{device.id}
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
        and (d.area_path = #{device.areaPath} or d.area_path like concat(#{device.areaPath},':%'))
      </if>
      <if test="device.deviceTypeId != null and device.deviceTypeId != ''">
        and u.device_type_id = #{device.deviceTypeId}
      </if>
      <if test="device.name != null and device.name != ''">
        and d.name like CONCAT('%', #{device.name}, '%')
      </if>
      <if test="device.code != null and device.code != ''">
        and d.code like CONCAT('%', #{device.code}, '%')
      </if>
      <if test="device.status != null">
        and d.status = #{device.status}
      </if>
      <if test="device.onlineState != null">
        and d.online_state = #{device.onlineState}
      </if>
      <if test="device.tenantId != null and device.tenantId != ''">
        and d.tenant_id= #{device.tenantId}
      </if>
    </where>

    ORDER BY d.modify_time
    <choose>
      <when test="device.sortAsc">
        ASC
      </when>
      <otherwise>
        DESC
      </otherwise>
    </choose>
  </select>

  <select id="getDistributionMonitorDevices" resultMap="MonitorDevicesMap">
    SELECT d.*,
    u.device_type_name AS d_deviceTypeName,
    u.code AS d_device_unit_code,
    u.name AS deviceUnitName,
    u.version AS deviceUnitVersion,
    ldt.ico_path
    FROM linkapp_device d
    inner join distribution_room_ref_device drrd on drrd.device_code = d.code
    LEFT JOIN linkapp_device_unit u ON d.device_unit_id = u.id
    left join linkapp_device_type ldt on ldt.id = u.device_type_id
    <where>
      d.delete_state = 1
      and drrd.distribution_room_id = #{distributionRoomId}
    </where>
    ORDER BY drrd.sort_no ASC, d.modify_time DESC
  </select>

  <select id="selectDevicesByCodes" resultType="com.easylinkin.linkappapi.device.entity.Device">
    select ld.id ,ld.code, ld.name, ld.area_path, ldu.code as deviceUnitCode, ldu.device_type_name
    deviceTypeName, ldu.name as deviceUnitName,ld.latitude,ld.longitude,ld.indoor_location,ld.site
    from linkapp_device ld
    left join linkapp_device_unit ldu on ld.device_unit_id = ldu.id
    <where>
      ld.delete_state = 1
      and
      ld.code in
      <foreach collection="deviceCodes" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
    </where>
    <if test="device.code != null and device.code != ''">
      and ld.code like CONCAT('%', #{device.code}, '%')
    </if>
    <if test="device.areaPath != null and device.areaPath != ''">
      and (ld.area_path = #{device.areaPath} or ld.area_path like concat(#{device.areaPath}, ':%'))
    </if>
  </select>

  <select id="selectCameraDevices" resultType="com.easylinkin.linkappapi.device.entity.Device">
    select ld.code, ld.name from linkapp_device ld
    left join linkapp_device_unit ldu on ld.device_unit_id = ldu.id
    where ld.tenant_id = #{tenantId} and
    ldu.physics_model like '%imageUrl%';
  </select>

  <update id="updateDeviceAreaById">
    update linkapp_device
    <set>
      modify_time = #{device.modifyTime},
      <if test="device.areaPath != null and device.areaPath != ''">
        area_id = #{device.areaId},
      </if>
      <if test="device.latitude != null and device.latitude != ''">
        latitude = #{device.latitude},
      </if>
      <if test="device.longitude != null and device.longitude != ''">
        longitude = #{device.longitude},
      </if>
      <if test="device.site != null and device.site != ''">
        site = #{device.site},
      </if>
      <if test="device.indoorLocation != null and device.indoorLocation != ''">
        indoor_location = #{device.indoorLocation},
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
        area_path = #{device.areaPath},
      </if>
    </set>
    <where>
      id = #{device.id}
    </where>
  </update>

  <select id="countByDevice" resultType="java.lang.Long">
    SELECT distinct count(ld.id)
    FROM linkapp_device ld
    <if test="deviceQueryVo.deviceAttributeStatusList==null and (deviceQueryVo.deviceUnitCode != null or deviceQueryVo.deviceTypeId != null or deviceQueryVo.deviceUnitVersion != null or deviceQueryVo.deviceUnitCodeList!=null)">
      inner JOIN linkapp_device_unit ldu ON ld.device_unit_id = ldu.id
    </if>
    <if test="deviceQueryVo.deviceAttributeStatusList!=null and deviceQueryVo.deviceAttributeStatusList.size()>0" >
      inner join linkapp_device_unit ldu on ldu.id = ld.device_unit_id
      inner join linkapp_device_attribute_status ldas  on ldas.device_code=ld.code and ldas.version=ldu.version
    </if>
    <where>
      ld.delete_state = 1
      <if test="deviceQueryVo.id != null and deviceQueryVo.id != ''">
        and ld.id = #{deviceQueryVo.id}
      </if>
      <if test="deviceQueryVo.areaPath != null and deviceQueryVo.areaPath != ''">
        and (ld.area_path = #{deviceQueryVo.areaPath} or ld.area_path like concat(#{deviceQueryVo.areaPath}, ':%'))
      </if>
      <if test="deviceQueryVo.deviceTypeId != null and deviceQueryVo.deviceTypeId != ''">
        and ldu.device_type_id = #{deviceQueryVo.deviceTypeId}
      </if>
      <if test="deviceQueryVo.areaId != null and deviceQueryVo.areaId != ''">
        and ld.area_id = #{deviceQueryVo.areaId}
      </if>
      <if test="deviceQueryVo.queryTimeStart != null and deviceQueryVo.queryTimeStart != ''">
        and ld.modify_time >= #{deviceQueryVo.queryTimeStart}
      </if>
      <if test="deviceQueryVo.queryTimeEnd != null and deviceQueryVo.queryTimeEnd != ''">
        and #{deviceQueryVo.queryTimeEnd} >= ld.modify_time
      </if>

      <if test="deviceQueryVo.name != null and deviceQueryVo.name != ''">
        and ld.name like CONCAT('%', #{deviceQueryVo.name}, '%')
      </if>
      <if test="deviceQueryVo.code != null and deviceQueryVo.code != '' and deviceQueryVo.codeLikeQuery == null">
        and ld.code = #{deviceQueryVo.code}
      </if>
      <if test="deviceQueryVo.codeLikeQuery != null and deviceQueryVo.codeLikeQuery != ''">
        and ld.code like CONCAT('%', #{deviceQueryVo.codeLikeQuery}, '%')
      </if>
      <if test="deviceQueryVo.status != null">
        and ld.status = #{deviceQueryVo.status}
      </if>
      <if test="deviceQueryVo.onlineState != null">
        and ld.online_state = #{deviceQueryVo.onlineState}
      </if>
      <if test="deviceQueryVo.projectId != null and deviceQueryVo.projectId != ''">
        and ld.project_id = #{deviceQueryVo.projectId}
      </if>
      <if test="deviceQueryVo.deviceUnitId != null and deviceQueryVo.deviceUnitId != ''">
        and ld.device_unit_id = #{deviceQueryVo.deviceUnitId}
      </if>
      <if test="deviceQueryVo.deviceUnitVersion != null and deviceQueryVo.deviceUnitVersion != ''">
        and ldu.version = #{deviceQueryVo.deviceUnitVersion}
      </if>
      <if test="deviceQueryVo.deviceUnitCode != null and deviceQueryVo.deviceUnitCode != ''">
        and ldu.code = #{deviceQueryVo.deviceUnitCode}
      </if>
      <if test="deviceQueryVo.creator != null and deviceQueryVo.creator != ''">
        and ld.creator like CONCAT('%', #{deviceQueryVo.creator}, '%')
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(deviceQueryVo.deviceUnitCodeList)">
        and ldu.code in
        <foreach collection="deviceQueryVo.deviceUnitCodeList" item="deviceUnitCode" index="index" open="("
                 close=")" separator=",">
          #{deviceUnitCode}
        </foreach>
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(deviceQueryVo.deviceCodeList)">
        and ld.code in
        <foreach collection="deviceQueryVo.deviceCodeList" item="deviceCode" index="index" open="("
                 close=")" separator=",">
          #{deviceCode}
        </foreach>
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(deviceQueryVo.areaIds)">
        and ld.area_id in
        <foreach collection="deviceQueryVo.areaIds" item="areaId" index="index" open="(" close=")"
                 separator=",">
          #{areaId}
        </foreach>
      </if>
      <if test="deviceQueryVo.deviceAttributeStatusList!=null and deviceQueryVo.deviceAttributeStatusList.size()>0" >
         and
        <foreach collection="deviceQueryVo.deviceAttributeStatusList" item="item" separator=" " open="(" close=")">
           ldas.prop_code = #{item.propCode} and ldas.prop_value = #{item.propValue}
        </foreach>

      </if>
    </where>
  </select>

  <select id="selectDeviceCodes" resultType="java.lang.String">
    select distinct (ld.code) from linkapp_device ld
    <if test="deviceQueryVo.inDeviceUnitCodes!=null  and deviceQueryVo.hasAttrs==null" >
      inner join linkapp_device_unit ldu on ldu.id = ld.device_unit_id
    </if>
    <if test="deviceQueryVo.hasAttrs!=null" >
      inner join linkapp_device_unit ldu on ldu.id = ld.device_unit_id
      inner join linkapp_device_attribute lda  on ldu.id = lda.device_unit_id
    </if>
    <where>
      ld.delete_state =1
    and ld.tenant_id = #{deviceQueryVo.tenantId}
      <if test="deviceQueryVo.hasAttrs!=null" >
        and lda.identifier in
        <foreach collection="deviceQueryVo.hasAttrs" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="deviceQueryVo.inDeviceUnitCodes!=null">
        and ldu.code in
        <foreach collection="deviceQueryVo.inDeviceUnitCodes" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getDeleteRepeatedDeviceAttrStatusIds" resultType="java.lang.String">
    SELECT
<!--            rn,-->
<!--           dcvpc,-->
           t2.id
    FROM (
                 SELECT (
                                IF(
                                        @dcvpc = dcvpc,
                                        @rownum := @rownum + 1,
                                        @rownum := 1
                                        )
                                ) rn,
                        @dcvpc := dcvpc,
                        t1.*
                 FROM (
                              SELECT concat(
                                             device_code,
                                             prop_code,
                                             version,
                                             IF(
                                                     ! ISNULL(parent_prop_code),
                                                     parent_prop_code,
                                                     ''
                                                     ),
                                             IF(
                                                     ! ISNULL(array_index),
                                                     array_index,
                                                     ''
                                                     )
                                             ) dcvpc,
                                     ldas.id,
                                     ldas.modify_time
                              FROM linkapp_device_attribute_status ldas
                              ORDER BY device_code,
                                       prop_code,
                                       version,
                                       parent_prop_code,
                                       array_index,
                                       modify_time,
                                       create_time DESC
                              ) t1,
                      (SELECT @rownum := 0) var1,
                      (SELECT @dcvpc := NULL) var2
                 ) t2
    WHERE rn > 1
    </select>
  <select id="getMonitorDevice" resultMap="BaseResultMap">
    select *
    from linkapp_device
    <where>
      delete_state = 1
      <if test="tenantId != null and tenantId != ''">
        and tenant_id = #{tenantId}
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(deviceUnitIds)">
        and device_unit_id in
        <foreach collection="deviceUnitIds" item="deviceUnitId" index="index" open="(" close=")" separator=",">
          #{deviceUnitId}
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectDeviceMachineryDtoList" parameterType="com.easylinkin.linkappapi.machinery.vo.MachineryDeviceVo"
          resultMap="DeviceMachineryDtoMap">
    select ld.*,
           ldu.device_type_id,
           #{id}    as machinery_id,
           ldu.device_type_name as unitTypeName
    from linkapp_device ld,
         linkapp_device_unit ldu
    where ld.device_unit_id = ldu.id and ld.delete_state = 1
    <if test="unitCodeList != null and unitCodeList.size() != 0">
      and ldu.code in
      <foreach collection="unitCodeList" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
    </if>
    <if test="binded != null">
      <if test="binded == 0">
        and ld.id not in (select amdr.device_id from app_machinery_device_ref amdr where amdr.delete_state = 1)
      </if>
      <if test="binded == 1">
        and ld.id in (select amdr.device_id
                      from app_machinery_device_ref amdr
                      where amdr.delete_state = 1
                        and amdr.machinery_id = #{id})
      </if>
    </if>
  </select>

    <select id="selectDeviceMachineryDtoNoRefList"
            parameterType="com.easylinkin.linkappapi.machinery.vo.MachineryDeviceVo"
            resultMap="DeviceMachineryNoRefDtoMap">
        select ld.*,
               ldu.device_type_id,
               #{id}    as machinery_id,
               ldu.device_type_name as unitTypeName,
               ldu.version
        from linkapp_device ld,
             linkapp_device_unit ldu
        where ld.device_unit_id = ldu.id
        <if test="unitCodeList != null and unitCodeList.size() != 0">
            and ldu.code in
            <foreach collection="unitCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="binded != null">
            <if test="binded == 0">
                and ld.id not in (select amdr.device_id from app_machinery_device_ref amdr where amdr.delete_state = 1)
            </if>
            <if test="binded == 1">
                and ld.id in (select amdr.device_id
                              from app_machinery_device_ref amdr
                              where amdr.delete_state = 1
                                and amdr.machinery_id = #{id})
            </if>
        </if>
    </select>

  <!-- 根据设备在线状态 统计设备数 （全局） -->
  <select id="countDeviceByOnlineStateGlobal" resultType="java.util.Map">
      SELECT d.online_state AS onlineState,
      COUNT(d.id)    AS deviceCount
      FROM linkapp_device d
      where d.delete_state = 1
      <if test="device.tenantId != null and device.tenantId != ''">
        and d.tenant_id = #{device.tenantId}
      </if>
      GROUP BY d.online_state;
  </select>

  <select id="findAiDeviceByCode"
    resultType="com.easylinkin.linkappapi.device.entity.Device">
        SELECT
          a.tenant_id
        FROM
          linkapp_device a
          LEFT JOIN linkapp_device_unit b ON a.device_unit_id = b.id
        WHERE
          a.CODE = #{deviceCode}
          AND a.delete_state = 1
          AND b.device_type_name = 'AI识别主机'
  </select>

  <select id="findDeviceByCodesGlobal" resultMap="BaseResultMap">
    SELECT
          a.*
        FROM
          linkapp_device a
        WHERE
           a.delete_state = 1
        <if test="tenantId != null and tenantId != ''">
          and a.tenant_id = #{tenantId}
        </if>
        <if test="codes != null and codes.size() != 0">
          and a.code in
          <foreach collection="codes" open="(" separator="," close=")" item="item">
            #{item}
          </foreach>
        </if>
  </select>

  <select id="getCameraCountByProjectIds" resultType="com.easylinkin.linkappapi.openapi.dto.CameraCountByProjectDTO">
    select tmp1.projectId, tmp1.totalCount, tmp2.onlineCount from (select lt.project_id as projectId,
    count(distinct ld.code) as totalCount
    from linkapp_device ld
    left join linkapp_device_attribute lda on
    ld.device_unit_id = lda.device_unit_id
    and ld.delete_state = 1
    inner join linkapp_tenant lt on
    lt.id = ld.tenant_id
    where lda.unit = 'videoMedia'
    <if test="projectIds!=null and projectIds.size()>0">
    and lt.project_id in
    <foreach collection="projectIds" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
    </if>
    group by lt.project_id
    ) tmp1
    left join(
    select lt.project_id as projectId,
    count(distinct ld.code) as onlineCount
    from linkapp_device ld
    left join linkapp_device_attribute lda on
    ld.device_unit_id = lda.device_unit_id
    and ld.delete_state = 1
    inner join linkapp_tenant lt on
    lt.id = ld.tenant_id
    where lda.unit = 'videoMedia'
    and ld.online_state = 1
    <if test="projectIds!=null and projectIds.size()>0">
    and lt.project_id in
    <foreach collection="projectIds" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
    </if>
    group by lt.project_id) tmp2
    on tmp1.projectId = tmp2.projectId
  </select>

  <select id="getDeviceListForGate" resultMap="BaseResultMap">
    SELECT ld.id,
    ld.code,
    ld.name,
    ld.delete_state
    FROM linkapp_device ld
    LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
    <where>
      <if test="code != null and code != ''">
        ld.code = #{code} or
      </if>
      (ld.delete_state = 1
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(deviceUnitNames)">
        and u.device_type_name in
        <foreach collection="deviceUnitNames" item="deviceUnitName" index="index" open="("
                 close=")" separator=",">
          #{deviceUnitName}
        </foreach>
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(codes)">
        and ld.code not in
        <foreach collection="codes" item="code" index="index" open="("
                 close=")" separator=",">
          #{code}
        </foreach>
      </if>
      )
    </where>
    ORDER BY ld.delete_state DESC
  </select>

  <select id="countDeviceGlobal" resultType="com.easylinkin.linkappapi.reportCenter.entity.ReportDevice">
    SELECT
      b.device_type_id deviceTypeId,
      count( a.id ) num
    FROM
      linkapp_device a
        JOIN linkapp_device_unit b ON a.device_unit_id = b.id
    WHERE
      a.tenant_id = #{tenantId}
      AND a.delete_state = 1
      AND a.area_id IS NOT NULL
      AND a.area_id != ''
    GROUP BY
      b.device_type_id
  </select>


  <select id="countAIDeviceGlobal" resultType="com.easylinkin.linkappapi.device.entity.Device">
    SELECT
      DISTINCT a.`code`
    FROM
      linkapp_device a
        LEFT JOIN linkapp_device_unit b ON a.device_unit_id = b.id
    WHERE
      a.delete_state = 1
      AND a.area_id IS NOT NULL
      AND b.device_type_name = #{deviceTypeName}
      AND a.tenant_id = #{tenantId}
  </select>

  <select id="selectByDeviceTypeName" resultType="com.easylinkin.linkappapi.device.entity.Device">
    SELECT
       a.`code`,a.name,a.id
    FROM
      linkapp_device a
        LEFT JOIN linkapp_device_unit b ON a.device_unit_id = b.id
    WHERE
      a.delete_state = 1
      AND a.area_id IS NOT NULL
      AND b.device_type_name = #{deviceTypeName}
      AND a.tenant_id = #{tenantId}
  </select>

  <update id="updateStatusById">
    UPDATE linkapp_device
    SET status = #{status}
    WHERE id = #{id}
  </update>
  <update id="upDateHighDeviceType">
    UPDATE linkapp_device
    SET high_device_type = #{highDeviceType}
    WHERE id = #{id}
  </update>


  <!-- 按租户统计设备总数、在线数、离线数 -->
  <select id="getDeviceStatisticsByTenantId" resultType="com.easylinkin.linkappapi.device.vo.DeviceStatisticsVo">
    SELECT
      #{tenantId} as tenantId,
      COUNT(1) as totalCount,
      SUM(CASE WHEN a.online_state = 1 THEN 1 ELSE 0 END) as onlineCount,
      SUM(CASE WHEN a.online_state = 0 THEN 1 ELSE 0 END) as offlineCount
    FROM linkapp_device a
    LEFT JOIN linkapp_device_unit b ON a.device_unit_id = b.id
    WHERE a.delete_state = 1
    AND a.tenant_id = #{tenantId}
    AND b.device_type_name in
    <foreach collection="deviceTypes" item="deviceTypeName" index="index" open="("
             close=")" separator=",">
          #{deviceTypeName}
    </foreach>
  </select>

  <update id="updateHighTypeByCode">
    UPDATE linkapp_device
    SET high_device_type = #{highDeviceType}
    WHERE code = #{code} and tenant_id = #{tenantId}
  </update>


  <select id="selectDevicesByHigh" resultMap="selectDevicesMap">
    SELECT ld.id,
    ld.code,
    ld.name,
    ld.status,
    ld.online_state,
    ld.remark,
    ld.indoor_location,
    ld.device_unit_id,
    ld.last_push_time,
    ld.create_time,
    ld.modify_time,
    ld.area_id,
    ld.latitude,
    ld.longitude,
    ld.site,
    ld.area_path,
    ld.tenant_id,
    ld.linkthing_delete,
    ld.filing_no,
    a.area_name        AS areaName,
    s.id               AS spaceId,
    s.space_name       AS spaceName,
    u.device_type_name AS deviceTypeName,
    u.name             AS deviceUnitName,
    u.code             AS deviceUnitCode,
    u.device_type_id   AS deviceTypeId,
    u.version          AS deviceUnitVersion,
    ldt.id             as ldt_id,
    ldt.ico_path       as ldt_ico_path,
    ld.work_status,
    ld.manufacturer,
    ld.manufacturer_tel_,
    ld.supplier,
    ld.responsible_person,
    ld.responsible_person_tel_,
    ld.collect_freq,
    ld.elect_status,
    ld.high_device_type,
    ld.high_work_mode
    FROM linkapp_device ld
    LEFT JOIN linkapp_device_unit u ON ld.device_unit_id = u.id
    LEFT JOIN linkapp_device_type ldt ON ldt.id = u.device_type_id
    LEFT JOIN linkapp_area a ON (a.id = ld.area_id)
    LEFT JOIN linkapp_space s ON (s.id = a.space_id)
    left join rail_linkapp_high_formwork_item_ref  fi on ld.code = fi.device_code and fi.status=0
    <where>
      ld.delete_state = 1
      <if test="device.id != null and device.id != ''">
        and ld.id = #{device.id}
      </if>
      <if test="device.spaceId != null and device.spaceId != ''">
        and s.id = #{device.spaceId}
      </if>
      <if test="device.uniKey != null and device.uniKey != ''">
        and  (ld.code like concat('%',#{device.uniKey},'%') or ld.name like concat('%',#{device.uniKey},'%')
        or fi.item_name like concat('%',#{device.uniKey},'%') or ld.high_device_type like concat('%',#{device.uniKey},'%'))
      </if>
      <if test="device.linkappSpace != null and device.linkappSpace.type != null">
        and s.type = #{device.linkappSpace.type}
      </if>
      <if test="device.areaPath != null and device.areaPath != ''">
        and (a.area_path = #{device.areaPath} or a.area_path like concat(#{device.areaPath}, ':%'))
      </if>
      <if test="device.deviceTypeId != null and device.deviceTypeId != ''">
        and u.device_type_id = #{device.deviceTypeId}
      </if>
      <if test="device.deviceTypeName != null and device.deviceTypeName != ''">
        and u.device_type_name = #{device.deviceTypeName}
      </if>
      <if test="device.deviceTypeNames != null and device.deviceTypeNames.size() > 0">
        and u.device_type_name in
        <foreach collection="device.deviceTypeNames" item="deviceTypeName" index="index" open="("
                 close=")" separator=",">
          #{deviceTypeName}
        </foreach>
      </if>
      <if test="device.areaId != null and device.areaId != ''">
        and ld.area_id = #{device.areaId}
      </if>
      <if test="device.queryTimeStart != null and device.queryTimeStart != ''">
        and ld.modify_time >= #{device.queryTimeStart}
      </if>
      <if test="device.queryTimeEnd != null and device.queryTimeEnd != ''">
        and #{device.queryTimeEnd} >= ld.modify_time
      </if>

      <if test="device.name != null and device.name != ''">
        and ld.name like CONCAT('%', #{device.name}, '%')
      </if>
      <if test="device.code != null and device.code != '' and device.codeLikeQuery == null">
        and ld.code = #{device.code}
      </if>
      <if test="device.codeLikeQuery != null and device.codeLikeQuery != ''">
        and ld.code like CONCAT('%', #{device.codeLikeQuery}, '%')
      </if>
      <if test="device.status != null">
        and ld.status = #{device.status}
      </if>
      <if test="device.onlineState != null">
        and ld.online_state = #{device.onlineState}
      </if>
      <if test="device.projectId != null and device.projectId != ''">
        and ld.project_id = #{device.projectId}
      </if>
      <if test="device.tenantId != null and device.tenantId != ''">
        and ld.tenant_id = #{device.tenantId}
      </if>
      <if test="device.deviceUnitId != null and device.deviceUnitId != ''">
        and ld.device_unit_id = #{device.deviceUnitId}
      </if>
      <if test="device.deviceUnitVersion != null and device.deviceUnitVersion != ''">
        and u.version = #{device.deviceUnitVersion}
      </if>
      <if test="device.deviceUnitCode != null and device.deviceUnitCode != ''">
        and u.code = #{device.deviceUnitCode}
      </if>
      <if test="device.creator != null and device.creator != ''">
        and ld.creator like CONCAT('%', #{device.creator}, '%')
      </if>
      <if
              test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceUnitCodeList)">
        and u.code in
        <foreach collection="device.deviceUnitCodeList" item="deviceUnitCode" index="index" open="("
                 close=")" separator=",">
          #{deviceUnitCode}
        </foreach>
      </if>
      <if
              test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.deviceCodeList)">
        and ld.code in
        <foreach collection="device.deviceCodeList" item="deviceCode" index="index" open="("
                 close=")" separator=",">
          #{deviceCode}
        </foreach>
      </if>
      <if test="@com.easylinkin.linkappapi.common.utils.MybatisUtil@isNotEmpty(device.areaIds)">
        and ld.area_id in
        <foreach collection="device.areaIds" item="areaId" index="index" open="(" close=")"
                 separator=",">
          #{areaId}
        </foreach>
      </if>
    </where>
    <choose>
      <when test="device.sortModels != null and device.sortModels.size() > 0">
        ORDER BY
        <trim suffixOverrides=",">
          <foreach collection="device.sortModels" item="item" index="index">
            ${item.field} ${item.sortRule},
          </foreach>
        </trim>
      </when>
      <otherwise>
        ORDER BY ld.modify_time DESC,ld.create_time DESC,ld.code ASC
      </otherwise>
    </choose>

    <!--    ORDER BY ld.modify_time-->
    <!--    <choose>-->
    <!--      <when test="device.sortAsc">-->
    <!--        ASC-->
    <!--      </when>-->
    <!--      <otherwise>-->
    <!--        DESC-->
    <!--      </otherwise>-->
    <!--    </choose>-->
    <!--    ,ld.code ASC-->
  </select>


  <update id="updateDeviceByHighMode">
    update linkapp_device
    <set>
      modify_time = #{device.modifyTime},
      <if test="device.highWorkMode != null and device.highWorkMode != ''">
        high_work_mode = #{device.highWorkMode},
      </if>
      <if test="device.collectFreq != null ">
        collect_freq = #{device.collectFreq},
      </if>
    </set>
    <where>
      id = #{device.id}
    </where>
  </update>


</mapper>
