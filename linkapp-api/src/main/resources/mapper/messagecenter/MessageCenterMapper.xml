<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.messagecenter.dao.MessageCenterMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.messagecenter.entity.MessageCenter">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="owner_id" jdbcType="VARCHAR" property="ownerId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
     <result column="link_id" jdbcType="VARCHAR" property="linkId" />
  </resultMap>

  <resultMap id="vo" type="com.easylinkin.linkappapi.messagecenter.entity.vo.MessageCenterVo" extends="BaseResultMap">
    <result column="count_" jdbcType="INTEGER" property="count"></result>
  </resultMap>
  <resultMap id="MessageCenterDtoMap" extends="BaseResultMap" type="com.easylinkin.linkappapi.messagecenter.dto.MessageCenterDto">
    <collection property="detailList" column="id" select="com.easylinkin.linkappapi.messagecenter.dao.MessageCenterDetailMapper.selectByMsgId"/>
  </resultMap>

<!--  由于要求 管理员用户查询是只聚合的一条，所以采用特殊查询，为防止不同数据生成时时间有差异，故时间采用特殊处理-->
<!--    select id,tenant_id,title,content,str_to_date(date_format(create_time,'%Y-%m-%d %H:%i:00'),'%Y-%m-%d %H:%i:%s') as create_time-->
  <select id="selectPage" resultMap="BaseResultMap">
    select id,owner_id,self,tenant_id,title,content,create_time,status,link_id,type
    from app_message_center
    <where>
      tenant_id = #{appMessageCenter.tenantId}
      <if test="appMessageCenter.ownerId != null and appMessageCenter.ownerId != ''">
        and owner_id = #{appMessageCenter.ownerId,jdbcType=VARCHAR}
      </if>
      <if test="appMessageCenter.type != null and appMessageCenter.type != ''">
        and type = #{appMessageCenter.type,jdbcType=INTEGER}
      </if>
      <if test="appMessageCenter.status != null">
        and status = #{appMessageCenter.status,jdbcType=INTEGER}
      </if>
    </where>
    order by status asc,create_time desc
  </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_message_center where id = #{id}
    </select>

  <select id="count" resultType="int">
    select count(1)
    from app_message_center
    <where>
      tenant_id = #{appMessageCenter.tenantId}
      <if test="appMessageCenter.ownerId != null and appMessageCenter.ownerId != ''">
        and owner_id = #{appMessageCenter.ownerId,jdbcType=VARCHAR}
      </if>
      <if test="appMessageCenter.type != null and appMessageCenter.type != ''">
        and type = #{appMessageCenter.type,jdbcType=INTEGER}
      </if>
      <if test="appMessageCenter.status != null">
        and status = #{appMessageCenter.status,jdbcType=INTEGER}
      </if>
    </where>
    order by status asc,create_time desc
  </select>

  <select id="countByType" resultMap="vo">
    select count(1) count_,type
    from app_message_center
    <where>
      tenant_id = #{appMessageCenter.tenantId}
      <if test="appMessageCenter.ownerId != null and appMessageCenter.ownerId != ''">
        and owner_id = #{appMessageCenter.ownerId,jdbcType=VARCHAR}
      </if>
      <if test="appMessageCenter.type != null and appMessageCenter.type != ''">
        and type = #{appMessageCenter.type,jdbcType=INTEGER}
      </if>
      <if test="appMessageCenter.status != null">
        and status = #{appMessageCenter.status,jdbcType=INTEGER}
      </if>
    </where>
    group by type
    order by status asc,create_time desc
  </select>

  <select id="selectMsgDtoById" parameterType="long" resultMap="MessageCenterDtoMap">
    select
      amc.*
    from
      app_message_center amc
    where
      amc.id = #{id}
  </select>
</mapper>
