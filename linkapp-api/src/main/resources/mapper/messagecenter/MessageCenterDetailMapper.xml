<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.messagecenter.dao.MessageCenterDetailMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.messagecenter.entity.MessageCenterDetail">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="device_code" jdbcType="VARCHAR" property="deviceCode"/>
        <result column="device_type_name" jdbcType="VARCHAR" property="deviceTypeName"/>
        <result column="position" jdbcType="VARCHAR" property="position"/>
        <result column="lead_name" jdbcType="VARCHAR" property="leadName"/>
        <result column="lead_id" jdbcType="VARCHAR" property="leadId"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="telephone" jdbcType="VARCHAR" property="telephone"/>
        <result column="message_center_id" jdbcType="INTEGER" property="messageCenterId"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from app_message_center_detail
        where tenant_id = #{appMessageCenterDetail.tenantId}
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_message_center_detail
        where id = #{id}
    </select>

    <select id="listUnCheckedDetails" resultMap="BaseResultMap">
        select *
        from app_message_center_detail
        where message_center_id = #{messageCenter.id,jdbcType=INTEGER}
        order by device_code asc
    </select>

    <select id="selectByMsgId" parameterType="long" resultMap="BaseResultMap">
        select amcd.* from app_message_center_detail amcd where amcd.message_center_id = #{msgId}
    </select>
</mapper>
