<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.danger.dao.DangerMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.danger.entity.Danger">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="the_id" jdbcType="INTEGER" property="theId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="danger_type_id" jdbcType="BIGINT" property="dangerTypeId"/>
        <result column="full_id" jdbcType="VARCHAR" property="fullId"/>
        <result column="full_name" jdbcType="VARCHAR" property="fullName"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="order_" jdbcType="INTEGER" property="order"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="change_limit" jdbcType="INTEGER" property="changeLimit"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
        <result column="record_status" jdbcType="INTEGER" property="recordStatus"/>
        <result column="points" jdbcType="INTEGER" property="points"/>
        <result column="fine" jdbcType="INTEGER" property="fine"/>
        <result column="push_period" jdbcType="INTEGER" property="pushPeriod"/>
        <result column="related" jdbcType="INTEGER" property="related"/>
        <result column="identify" jdbcType="VARCHAR" property="identify"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select * from app_danger
        <where>
            tenant_id = #{appDanger.tenantId}
            <if test="appDanger.content!=null">
                and content like CONCAT('%', #{appDanger.content}, '%')
            </if>
            <if test="appDanger.fullName!=null">
                and full_name like CONCAT(#{appDanger.fullName}, '%')
            </if>
            <if test="appDanger.recordStatus!=null">
                and record_status = #{appDanger.recordStatus}
            </if>
        </where>
        order by code asc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from app_danger where id = #{id}
    </select>
</mapper>
