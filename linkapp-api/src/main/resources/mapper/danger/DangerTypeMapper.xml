<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.danger.dao.DangerTypeMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.danger.entity.DangerType">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="danger_type_id" jdbcType="BIGINT" property="dangerTypeId"/>
        <result column="full_id" jdbcType="VARCHAR" property="fullId"/>
        <result column="full_name" jdbcType="VARCHAR" property="fullName"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
    </resultMap>


    <select id="selectPage" resultType="com.easylinkin.linkappapi.danger.entity.vo.DangerTypeNode">
        select adt.*
        from app_danger_type adt
        <if test="appDangerType != null and appDangerType.hasDangerContent == true">
            inner join
            (select distinct adt1.full_id as full_id
             from app_danger ad
                      inner join app_danger_type adt1 on
                 ad.full_id like concat(adt1.full_id, '/%')
            <where>
                ad.tenant_id = #{appDangerType.tenantId}
                <if test="appDangerType.danger != null and appDangerType.danger.recordStatus != null">
                    and ad.record_status = #{appDangerType.danger.recordStatus}
                </if>
            </where>
            ) tmp1 on tmp1.full_id = adt.full_id
        </if>
        <where>
            adt.tenant_id = #{appDangerType.tenantId}
        </where>
        order by adt.id desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_danger_type
        where id = #{id}
    </select>
    
     <select id="getListByParentId" resultMap="BaseResultMap">
        select *
        from app_danger_type
        where parent_id=#{parentId}
    </select>
    
</mapper>
