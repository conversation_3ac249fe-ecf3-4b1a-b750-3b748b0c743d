<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.mechanical.mapper.MechanicalMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.mechanical.entity.Mechanical">
            <id property="id" column="id"/>
            <result property="tenantId" column="tenant_id_"/>
            <result property="workPointId" column="work_point_id_"/>
            <result property="workPointName" column="work_point_name_"/>
            <result property="type" column="type_"/>
            <result property="name" column="name_"/>
            <result property="code" column="code_"/>
            <result property="status" column="status_"/>
            <result property="ownerShipType" column="owner_ship_type_"/>
            <result property="ownerShipName" column="owner_ship_name_"/>
            <result property="specification" column="specification_"/>
            <result property="enterTime" column="enter_time_"/>
            <result property="outTime" column="out_time_"/>
            <result property="driverIds" column="driver_ids_" typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler"/>
            <result property="managerId" column="manager_id_"/>
            <result property="managerName" column="manager_name_"/>
            <result property="isSpecialEquipment" column="is_special_equipment_"/>
            <result property="machineryAcceptanceStatus" column="machinery_acceptance_status_"/>
            <result property="plannedRemovalTime" column="planned_removal_time_"/>
            <result property="isPower" column="is_power_"/>
            <result property="manufacturer" column="manufacturer_"/>
            <result property="manufacturerTel" column="manufacturer_tel_"/>
            <result property="maintainer" column="maintainer_"/>
            <result property="maintainerTel" column="maintainer_tel_"/>
            <result property="maintainerDate" column="maintainer_date_"/>
            <result property="inspectionFileList" column="inspection_file_list_" typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler"/>
            <result property="certificateFileList" column="certificate_file_list_" typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler"/>
            <result property="appraisalCertificateFileList" column="appraisal_certificate_file_list_" typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler"/>
            <result property="registrationCertificateFileList" column="registration_certificate_file_list_" typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler"/>
            <result property="equipmentPhotoFileList" column="equipment_photo_file_list_" typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler"/>
            <result property="specificalInfo" column="specifical_info_"/>
            <result property="deleteState" column="delete_state"/>
            <result property="createId" column="create_id_"/>
            <result property="createTime" column="create_time_"/>
            <result property="modifyId" column="modify_id_"/>
            <result property="modifyTime" column="modify_time_"/>
            <result property="remark" column="remark_"/>
            <result property="xOffset" column="x_offset_"/>
            <result property="yOffset" column="y_offset_"/>
            <result property="onlineState" column="online_state"/>
            <result property="alarmState" column="alarm_state"/>
            <result property="fiveChartTime" column="five_chart_time_"/>
            <result property="fiveChartContent" column="five_chart_content_"/>
            <result property="fiveChartImg" column="five_chart_img_" typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler"/>
        <collection property="refDeviceList" column="id"
                    select="com.easylinkin.linkappapi.mechanical.mapper.MechanicalRefDeviceMapper.selectByRailMechanicalId">
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        a.id,
        a.tenant_id_,
        a.work_point_id_,
        p.point_name_ AS work_point_name_,
        a.type_,
        a.name_,
        a.code_,
        a.status_,
        a.owner_ship_type_,
        a.owner_ship_name_,
        a.specification_,
        a.enter_time_,
        a.out_time_,
        a.driver_ids_,
        a.manager_id_,
        a.manager_name_,
        a.is_special_equipment_,
        a.machinery_acceptance_status_,
        a.planned_removal_time_,
        a.is_power_,
        a.manufacturer_,
        a.manufacturer_tel_,
        a.maintainer_,
        a.maintainer_tel_,
        a.maintainer_date_,
        a.inspection_file_list_,
        a.certificate_file_list_,
        a.appraisal_certificate_file_list_,
        a.registration_certificate_file_list_,
        a.equipment_photo_file_list_,
        a.specifical_info_,
        a.delete_state,
        a.create_id_,
        a.create_time_,
        a.modify_id_,
        a.modify_time_,
        a.remark_,
        a.x_offset_,
        a.y_offset_,
        c.online_state,
        c.status as alarm_state,
        a.five_chart_time_,
        a.five_chart_content_,
        a.five_chart_img_
    </sql>

    <select id="listPage" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from rail_mechanical a
        left join rail_project_point p on a.work_point_id_ = p.id
        left join rail_mechanical_ref_device rmrd on a.id = rmrd.rail_mechanical_id_ and rmrd.delete_state = 0 and rmrd.device_type_ = 1
        left join linkapp_device c on rmrd.device_id_ = c.id
        <where>
            a.delete_state = 0
            and a.tenant_id_ = #{customQueryParams.tenantId}
            <if test="customQueryParams.isRefMainDevice != null and customQueryParams.isRefMainDevice == true">
                and c.code is not null
            </if>
            <if test="customQueryParams.onlineState != null">
                <if test="customQueryParams.onlineState == 0">
                    and c.online_state is null or c.online_state = 0
                </if>
                <if test="customQueryParams.onlineState == 1">
                    and c.online_state = 1
                    </if>
            </if>
            <if test="customQueryParams.alarmState != null">
                <if test="customQueryParams.alarmState == 0">
                    and c.status is null or c.status = 0
                </if>
                <if test="customQueryParams.alarmState == 1">
                    and c.status = 1
                </if>
            </if>
            <if test="customQueryParams.codeOrName!= null and customQueryParams.codeOrName != ''">
                and (a.name_ like concat('%',#{customQueryParams.codeOrName},'%')
                or a.code_ like concat('%',#{customQueryParams.codeOrName},'%'))
            </if>
            <if test="customQueryParams.tenantId!= null and customQueryParams.tenantId != ''">
                and a.tenant_id_ = #{customQueryParams.tenantId}
            </if>
            <if test="customQueryParams.status!= null">
                and a.status_ = #{customQueryParams.status}
            </if>
            <if test="customQueryParams.code!= null and customQueryParams.code != ''">
                and a.code_ like concat('%',#{customQueryParams.code},'%')
            </if>
            <if test="customQueryParams.name!= null and customQueryParams.name != ''">
                and name_ like concat('%',#{customQueryParams.name},'%')
            </if>
            <if test="customQueryParams.workPointId!= null and customQueryParams.workPointId != ''">
                and a.work_point_id_ = #{customQueryParams.workPointId}
            </if>
            <if test="customQueryParams.type != null and customQueryParams.type != ''">
                and a.type_ = #{customQueryParams.type}
            </if>
            <if test="customQueryParams.types!= null and customQueryParams.types.size() > 0">
                and a.type_ in
                <foreach collection="customQueryParams.types" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="customQueryParams.enterStartTime!= null">
                and a.enter_time_ &gt;= #{customQueryParams.enterStartTime}
            </if>
            <if test="customQueryParams.enterEndTime!= null">
                and a.enter_time_ &lt;= #{customQueryParams.enterEndTime}
            </if>
            <if test="customQueryParams.outStartTime!= null">
                and a.out_time_ &gt;= #{customQueryParams.outStartTime}
            </if>
            <if test="customQueryParams.outEndTime!= null">
                and a.out_time_ &lt;= #{customQueryParams.outEndTime}
            </if>
        </where>
        group by a.id
        order by a.create_time_ desc
    </select>

    <select id="queryById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rail_mechanical a
        left join rail_project_point p on a.work_point_id_ = p.id
        left join rail_mechanical_ref_device rmrd on a.id = rmrd.rail_mechanical_id_ and rmrd.delete_state = 0 and rmrd.device_type_ = 1
        left join linkapp_device c on rmrd.device_id_ = c.id
        where a.id = #{id} and a.delete_state = 0
    </select>

    <select id="selectMechanicalByDeviceCode" resultMap="BaseResultMap">
        select *
        from rail_mechanical rm
                 left join rail_mechanical_ref_device rmrd
                           on rm.id = rmrd.rail_mechanical_id_
        where rmrd.device_id_ = #{id} and rm.delete_state = 0
        and rm.tenant_id_ = #{tenantId} and rmrd.delete_state = 0
    </select>

    <select id="selectMechanicalByOperationAreaId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from rail_mechanical a
                 left join rail_crane_operation_area_ref_mechanical rcoram
                           on a.id = rcoram.mechanical_id_
        left join rail_project_point p on a.work_point_id_ = p.id
        left join rail_mechanical_ref_device rmrd on a.id = rmrd.rail_mechanical_id_ and rmrd.delete_state = 0 and rmrd.device_type_ = 1
        left join linkapp_device c on rmrd.device_id_ = c.id
        where rcoram.operation_area_id_ = #{id} and a.delete_state = 0
        and rcoram.delete_state = 0
    </select>

    <select id="selectMechanicalByWorkPointId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from rail_mechanical a
        left join rail_project_point p on a.work_point_id_ = p.id
        left join rail_mechanical_ref_device rmrd on a.id = rmrd.rail_mechanical_id_ and rmrd.delete_state = 0 and rmrd.device_type_ = 1
        left join linkapp_device c on rmrd.device_id_ = c.id
        where a.work_point_id_ = #{workPointId} and a.delete_state = 0
        <if test="type != null and type != ''">
            and a.type_ = #{type}
        </if>
    </select>

    <select id="selectDeviceByMechanicalIdAndType" resultType="java.lang.String">
        select device_id_
        from rail_mechanical_ref_device rmrd
        left join rail_mechanical rm on rmrd.rail_mechanical_id_ = rm.id
        where rmrd.rail_mechanical_id_ = #{mechanicalId}
        and rmrd.device_type_ = #{type} and rm.delete_state = 0 and rmrd.delete_state = 0
    </select>
</mapper>