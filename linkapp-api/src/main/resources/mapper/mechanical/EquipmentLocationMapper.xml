<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.mechanical.mapper.EquipmentLocationMapper">

    <select id="listPage" resultType="com.easylinkin.linkappapi.mechanical.entity.EquipmentLocation">
        select a.*,
        el.location_type_ as locationType,
        el.equipment_lng_ as equipmentLng,
        el.equipment_lat_ as equipmentLat
        from (
        select
        distinct
        ld.id AS equipmentId,
        ld.name AS equipmentName,
        ld.code AS equipmentCode,
        ldu.device_type_name AS equipmentTypeName,
        ld.online_state onlineState,
        ld.status AS isAlarm
        from linkapp_device ld
        left join linkapp_device_unit ldu on ld.device_unit_id = ldu.id
        where ld.delete_state = 1
        union
        select
        distinct
        rm.id AS equipmentId,
        rm.name_ AS equipmentName,
        rm.code_ AS equipmentCode,
        '机械' AS equipmentTypeName,
        null AS onlineState,
        null AS isAlarm
        from rail_mechanical rm
        where rm.delete_state = 0 and rm.status_ = 0
        and rm.tenant_id_ = #{customQueryParams.tenantId}
        ) a left join rail_equipment_location el on a.equipmentId = el.equipment_id_ and el.tenant_id_ = #{customQueryParams.tenantId}
        <where>
            <if test="customQueryParams.deviceNameOrCode!= null and customQueryParams.deviceNameOrCode!= ''">
                and (a.equipmentName like concat('%', #{customQueryParams.deviceNameOrCode}, '%')
                or a.equipmentCode like concat('%', #{customQueryParams.deviceNameOrCode}, '%'))
            </if>
            <if test="customQueryParams.typeNames!= null and customQueryParams.typeNames.size() > 0">
                and a.equipmentTypeName in
                <foreach collection="customQueryParams.typeNames" item="typeId" open="(" separator="," close=")">
                    #{typeId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>