<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.mechanical.mapper.MechanicalWorkRecordMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.mechanical.entity.MechanicalWorkRecord">
        <id property="id" column="id_" />
        <result property="tenantId" column="tenant_id_" />
        <result property="mechanicalId" column="mechanical_id_" />
        <result property="startTime" column="start_time_" />
        <result property="endTime" column="end_time_" />
        <result property="workTime" column="work_time_" />
        <result property="createId" column="create_id_" />
        <result property="createTime" column="create_time_" />
        <result property="modifyId" column="modify_id_" />
        <result property="modifyTime" column="modify_time_" />
        <result property="remark" column="remark_" />
    </resultMap>

    <sql id="MechanicalWorkRecordColumns">
        id_, tenant_id_, mechanical_id_, start_time_, end_time_, work_time_, create_id_, create_time_, modify_id_, modify_time_, remark_
    </sql>

    <select id="queryPageList" resultMap="BaseResultMap">
        SELECT <include refid="MechanicalWorkRecordColumns" />
        FROM rail_mechanical_work_record
        WHERE tenant_id_ = #{customQueryParams.tenantId}
        <if test="customQueryParams.mechanicalId!= null and customQueryParams.mechanicalId!= ''">
            AND mechanical_id_ = #{customQueryParams.mechanicalId}
        </if>
        <if test="customQueryParams.queryStartTime != null and customQueryParams.queryStartTime != ''">
            AND create_time_ >= #{customQueryParams.queryStartTime}
        </if>
        <if test="customQueryParams.queryEndTime != null and customQueryParams.queryEndTime != ''">
            AND create_time_ &lt;= #{customQueryParams.queryEndTime}
        </if>
        ORDER BY create_time_ DESC
    </select>
</mapper>