<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.mechanical.mapper.MechanicalRefDeviceMapper">
    <resultMap id="MechanicalRefDeviceResultMap" type="com.easylinkin.linkappapi.mechanical.entity.MechanicalRefDevice">
        <id property="id" column="id_" />
        <result property="railMechanicalId" column="rail_mechanical_id_" />
        <result property="deviceType" column="device_type_" />
        <result property="deviceId" column="device_id_" />
        <result property="deviceCode" column="device_code_" />
        <result property="deviceName" column="device_name_" />
        <result property="deleteState" column="delete_state" />
        <result property="createId" column="create_id_" />
        <result property="createTime" column="create_time_" />
        <result property="modifyId" column="modify_id_" />
        <result property="modifyTime" column="modify_time_" />
        <result property="remark" column="remark_" />
    </resultMap>

    <sql id="MechanicalRefDeviceColumns">
        id_, rail_mechanical_id_, device_type_, device_id_, device_code_, device_name_, delete_state, create_id_, create_time_, modify_id_, modify_time_, remark_
    </sql>

    <select id="selectByRailMechanicalId" parameterType="java.lang.String"  resultMap="MechanicalRefDeviceResultMap">
        SELECT
            <include refid="MechanicalRefDeviceColumns" />
        FROM rail_mechanical_ref_device WHERE rail_mechanical_id_ = #{railMechanicalId} AND delete_state = 0
    </select>

    <select id="selectRecentBindRecord" resultMap="MechanicalRefDeviceResultMap">
        SELECT
            <include refid="MechanicalRefDeviceColumns" />
        FROM rail_mechanical_ref_device WHERE device_id_ = #{deviceId} ORDER BY modify_time_ DESC LIMIT 1
    </select>

</mapper>