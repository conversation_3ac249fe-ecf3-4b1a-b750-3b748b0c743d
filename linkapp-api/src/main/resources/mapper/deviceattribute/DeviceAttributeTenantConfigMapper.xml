<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.deviceattribute.mapper.DeviceAttributeTenantConfigMapper">
  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttributeTenantConfig">
    <id column="id" property="id"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="device_attribute_id" property="deviceAttributeId"/>
    <result column="sort_no" property="sortNo"/>
    <result column="is_show" property="isShow"/>
  </resultMap>

  <select id="getDeviceAttributeTenantConfigs" resultMap="BaseResultMap">
    select ldatc.id, ldatc.tenant_id, ldatc.device_attribute_id, ldatc.sort_no, ldatc.is_show, lda.name as deviceAttributeName
    from linkapp_device_attribute_tenant_config ldatc
           left join linkapp_device_attribute lda on ldatc.device_attribute_id = lda.id
      where 1 = 1
    <if test="deviceAttributeTenantConfig.id != null and deviceAttributeTenantConfig.id != ''">
      and ldatc.id = #{deviceAttributeTenantConfig.id}
    </if>
    <if test="deviceAttributeTenantConfig.deviceUnitId != null and deviceAttributeTenantConfig.deviceUnitId != ''">
      and lda.device_unit_id = #{deviceAttributeTenantConfig.deviceUnitId}
    </if>
    <if test="deviceAttributeTenantConfig.deviceAttributeId != null and deviceAttributeTenantConfig.deviceAttributeId != ''">
      and ldatc.device_attribute_id = #{deviceAttributeTenantConfig.deviceAttributeId}
    </if>
    <if test="deviceAttributeTenantConfig.tenantId != null and deviceAttributeTenantConfig.tenantId != ''">
      and ldatc.tenant_id = #{deviceAttributeTenantConfig.tenantId}
    </if>
    order by ldatc.sort_no asc
  </select>

  <select id="getLatestToBeUpdatedPersonalizedConfig2" resultType="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttributeTenantConfig">
    SELECT s2.ldatc_id,
           s2.lda2_id,
           ldatc_tenant_id,
           ldatc_device_attribute_id
    FROM (
           SELECT IF(
                    s1.code_identi = @code_identi,
                    @rownum := @rownum + 1,
                    @rownum := 1
                    ),
                  @code_identi := code_identi,
                  @rownum rownum,
                  s1.*
           FROM (
                  SELECT lda2.identifier,
                         lda2.id                                             AS lda2_id,
                         lda2.version,
                         tmp1.ldu_code,
                         lda_id,
                         lda_identifier,
                         ldatc_id,
                         ldatc_tenant_id,
                         ldatc_device_attribute_id,
                         concat(ldatc_tenant_id, ldu2.CODE, lda2.identifier) AS code_identi
                  FROM linkapp_device_attribute lda2
                         INNER JOIN linkapp_device_unit ldu2 ON ldu2.id = lda2.device_unit_id
                         LEFT JOIN (
                    SELECT ldu.CODE                  ldu_code,
                           lda.identifier,
                           lda.id                    lda_id,
                           lda.identifier            lda_identifier,
                           ldatc.id                  ldatc_id,
                           ldatc.device_attribute_id ldatc_device_attribute_id,
                           ldatc.tenant_id           ldatc_tenant_id
                    FROM linkapp_device_attribute_tenant_config ldatc
                           INNER JOIN linkapp_device_attribute lda ON lda.id = ldatc.device_attribute_id
                           INNER JOIN linkapp_device_unit ldu ON lda.device_unit_id = ldu.id
                    ) tmp1 ON ldu2.CODE = tmp1.ldu_code
                    AND lda2.identifier = tmp1.lda_identifier
                  ORDER BY ldatc_tenant_id,
                           ldu2.CODE,
                           lda2.identifier,
                           lda2.create_time DESC
                  ) s1,
                (SELECT @code_identi := NULL) var1,
                (SELECT @rownum := 0) var2
           ) s2
    WHERE s2.rownum = 1
      AND ldatc_tenant_id IS NOT NULL
      AND s2.lda2_id != ldatc_device_attribute_id
      AND (
            SELECT count(1)
            FROM linkapp_device_attribute_tenant_config
            WHERE ldatc_tenant_id = tenant_id
              AND lda2_id = device_attribute_id
            ) = 0
  </select>

  <select id="getLatestToBeUpdatedPersonalizedConfig" resultType="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttributeTenantConfig">

    SELECT id,
           tenant_id,
           lda2_device_attribute_id as device_attribute_id,
           ldatc_sort_no            as sort_no,
           ldatc_is_show               is_show
    <!--      ,-->
    <!--           ldatc_device_attribute_id,-->
    <!--           lda2_identifier,-->
    <!--           lda_identifier,-->
    <!--           lda2_version,-->
    <!--           lda_version,-->
    <!--           device_unit_id,-->
    <!--           lda2_create_time,-->
    <!--           lda_create_time,-->
    <!--           rownum-->
    from (SELECT IF(
                   t1.code_identi = @code_identi,
                   @rownum := @rownum + 1,
                   @rownum := 1
                   ),

                 @code_identi := code_identi,
                 @rownum rownum,
                 id,
                 tenant_id,
                 lda2_device_attribute_id,
                 ldatc_device_attribute_id,
                 ldatc_is_show,
                 ldatc_sort_no
    <!--                 lda2_identifier,-->
    <!--                 lda_identifier,-->
    <!--                 lda2_version,-->
    <!--                 lda_version,-->
    <!--                 device_unit_id,-->
    <!--                 lda2_create_time,-->
    <!--                 lda_create_time-->

    from (select concat(ldatc.tenant_id, ldu2.code, lda2.identifier) AS code_identi,
                 ldatc.id,
                 ldatc.tenant_id,
                 ldatc.is_show                                          ldatc_is_show,
                 ldatc.sort_no                                          ldatc_sort_no,
                 ldatc.device_attribute_id                           as ldatc_device_attribute_id,
                 lda2.id                                             as lda2_device_attribute_id,
                 ldu2.code                                              ldu2_code,
                 lda2.device_unit_id,
                 lda2.identifier                                        lda2_identifier,
                 lda2.version                                           lda2_version,
                 lda2.create_time                                       lda2_create_time,
                 lda.identifier                                         lda_identifier,
                 lda.version                                            lda_version,
                 lda.create_time                                        lda_create_time

          from linkapp_device_attribute_tenant_config ldatc
                 inner join linkapp_device_attribute lda on ldatc.device_attribute_id = lda.id
                 inner join linkapp_device_unit ldu on lda.device_unit_id = ldu.id
                 inner join linkapp_device_unit ldu2 on ldu2.code = ldu.code
                 inner JOIN linkapp_device_attribute lda2 ON lda2.device_unit_id = ldu2.id and lda.identifier = lda2.identifier
          order by ldatc.tenant_id, ldu2.code, lda2.identifier, lda2.create_time desc, lda2.version desc) t1,
         (SELECT @code_identi := NULL) var1,
         (SELECT @rownum := 0) var2) t2
      where t2.rownum = 1
        and ldatc_device_attribute_id != t2.lda2_device_attribute_id
        and (
              select count(1)
              from linkapp_device_attribute_tenant_config
              where t2.tenant_id = tenant_id
                and lda2_device_attribute_id = device_attribute_id
              ) = 0
        and t2.lda2_device_attribute_id in
    <foreach collection="addDeviceAttrIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <delete id="deleteAttrIdentifierRepeatData">
    delete ldatc
    from linkapp_device_attribute_tenant_config ldatc
           inner join linkapp_device_attribute lda on ldatc.device_attribute_id = lda.id
           inner join (
      SELECT s2.tenant_id, s2.ldu_id
      FROM (
             SELECT IF(
                      s1.code_identi = @code_identi,
                      @rownum := @rownum + 1,
                      @rownum := 1
                      ),
                    @code_identi := code_identi,
                    @rownum rownum,
                    s1.ldu_code,
                    s1.ldu_id,
                    tenant_id,
                    version
             FROM (
                    SELECT DISTINCT ldu.CODE                             ldu_code,
                                    ldu.id                               ldu_id,
                                    ldu.version,
                                    ldatc.tenant_id,
                                    concat(ldatc.tenant_id, ldu.CODE) AS code_identi
                    FROM linkapp_device_attribute_tenant_config ldatc
                           INNER JOIN linkapp_device_attribute lda ON ldatc.device_attribute_id = lda.id
                           INNER JOIN linkapp_device_unit ldu ON lda.device_unit_id = ldu.id
                    ORDER BY ldatc.tenant_id,
                             ldu.CODE ASC,
                             ldu.create_time DESC
                    ) s1,
                  (SELECT @code_identi := NULL) var1,
                  (SELECT @rownum := 0) var2
             ) s2
      where s2.rownum > 1) tmp on ldatc.tenant_id = tmp.tenant_id and lda.device_unit_id = tmp.ldu_id
  </delete>

<!--  刷数据 属性id 本身不在 linkapp_device_attribute_tenant_config 表但是其同型号下的兄弟属性在这个表中 -->
  <select id="getNeedAddConfigAfterInheritPersonalizedConfig" resultType="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttributeTenantConfig">
    SELECT DISTINCT t1.tenant_id,
                    lda2.id as device_attribute_id
    FROM (
           SELECT device_unit_id,
                  ldatc.tenant_id
           FROM linkapp_device_attribute lda
                  INNER JOIN linkapp_device_attribute_tenant_config ldatc ON lda.id = ldatc.device_attribute_id
           ) t1
           INNER JOIN linkapp_device_attribute lda2 ON lda2.device_unit_id = t1.device_unit_id
           inner join linkapp_device_unit t3 on t3.id = lda2.device_unit_id
    WHERE (
            SELECT count(1)
            FROM linkapp_device_attribute_tenant_config ldatc2
            WHERE ldatc2.device_attribute_id = lda2.id
              AND ldatc2.tenant_id = t1.tenant_id
            ) = 0
      and lda2.parent_id is null
    ORDER BY t1.tenant_id, t3.code, t3.version, lda2.identifier
    
</select>

<!--  <delete id="deleteAttrIdentifierRepeatData2">-->
<!--    delete-->
<!--    from linkapp_device_attribute_tenant_config-->
<!--    where id in (-->
<!--      SELECT s2.ldatc_id-->
<!--      FROM (-->
<!--             SELECT IF(-->
<!--                      s1.code_identi = @code_identi,-->
<!--                      @rownum := @rownum + 1,-->
<!--                      @rownum := 1-->
<!--                      ),-->
<!--                    @code_identi := code_identi,-->
<!--                    @rownum rownum,-->
<!--                    s1.ldatc_id,-->
<!--                    s1.tenant_id,-->
<!--                    s1.device_attribute_id-->

<!--             FROM (-->
<!--                    SELECT ldatc.id                                           AS ldatc_id,-->
<!--                           concat(ldatc.tenant_id,ldatc.device_attribute_id ) AS code_identi,-->
<!--                           ldatc.tenant_id,-->
<!--                           ldatc.device_attribute_id-->
<!--                    FROM linkapp_device_attribute_tenant_config ldatc-->
<!--                    order by ldatc.tenant_id, ldatc.device_attribute_id-->
<!--                    ) s1,-->
<!--                  (SELECT @code_identi := NULL) var1,-->
<!--                  (SELECT @rownum := 0) var2-->
<!--             ) s2-->
<!--      where s2.rownum > 1-->
<!--      )-->
<!--  </delete>-->

</mapper>
