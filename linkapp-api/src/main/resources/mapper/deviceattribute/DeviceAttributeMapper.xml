<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.deviceattribute.mapper.DeviceAttributeMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="identifier" property="identifier"/>
    <result column="name" property="name"/>
    <result column="unit" property="unit"/>
    <result column="ico_path" property="icoPath"/>
    <result column="type" property="type"/>
    <result column="specs" property="specs"/>
    <result column="sort_no" property="sortNo"/>
    <result column="description" property="description"/>
    <result column="is_show" property="isShow"/>
    <result column="expression" property="expression"/>
    <result column="dict_code" property="dictCode"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>


  <select id="getAll" resultMap="BaseResultMap">
    select temp.* from(
      select lda.id,
             lda.device_unit_id,
             lda.identifier,
             lda.name,
             lda.value,
             lda.unit,
             lda.ico_path,
             lda.type,
             lda.specs,
             lda.dict_code,
             lda.create_time,
             lda.modify_time,
             lda.parent_id,
    <choose>
      <when test="deviceAttribute.tenantId != null and deviceAttribute.tenantId != ''">
        ifnull(ldatc.is_show, lda.is_show) is_show,
        ifnull(ldatc.sort_no, lda.sort_no) sort_no,
      </when>
      <otherwise>
        lda.is_show,
        lda.sort_no,
      </otherwise>
    </choose>
    lda.version
      from linkapp_device_attribute lda
    <if test="deviceAttribute.tenantId != null and deviceAttribute.tenantId != ''">
      left join linkapp_device_attribute_tenant_config ldatc on ldatc.device_attribute_id = lda.id and ldatc.tenant_id = #{deviceAttribute.tenantId,jdbcType=VARCHAR}
    </if>
    <if test="deviceAttribute.deviceUnitCode != null and deviceAttribute.deviceUnitCode != ''">
      left join linkapp_device_unit ldu on ldu.id = lda.device_unit_id
    </if>
    <if test="deviceAttribute.parentIdentifier != null and deviceAttribute.parentIdentifier != ''">
      left join linkapp_device_attribute lda2 on lda.parent_id = lda2.id
    </if>
    <where>
      <if test="deviceAttribute.id != null and deviceAttribute.id != ''">
        and lda.id = #{deviceAttribute.id}
      </if>
      <if test="deviceAttribute.parentIdentifier != null and deviceAttribute.parentIdentifier != ''">
        and lda2.identifier = #{deviceAttribute.parentIdentifier}
      </if>
      <if test="deviceAttribute.deviceUnitId != null and deviceAttribute.deviceUnitId != ''">
        and lda.device_unit_id = #{deviceAttribute.deviceUnitId}
      </if>
      <if test="deviceAttribute.identifier != null and deviceAttribute.identifier != ''">
        and lda.identifier = #{deviceAttribute.identifier}
      </if>
      <if test="deviceAttribute.name != null and deviceAttribute.name != ''">
        and lda.name = #{deviceAttribute.name}
      </if>
      <if test="deviceAttribute.unit != null and deviceAttribute.unit != ''">
        and lda.unit = #{deviceAttribute.unit}
      </if>
      <if test="deviceAttribute.parentId != null and deviceAttribute.parentId != ''">
        and lda.parent_id = #{deviceAttribute.parentId}
      </if>
      <if test="deviceAttribute.dictCode != null and deviceAttribute.dictCode != ''">
        and lda.dict_code = #{deviceAttribute.dictCode}
      </if>
      <if test="deviceAttribute.type != null and deviceAttribute.type != ''">
        and lda.type = #{deviceAttribute.type}
      </if>
      <if test="deviceAttribute.typeCodes != null and deviceAttribute.typeCodes.size() > 0">
        and lda.unit in
        <foreach collection="deviceAttribute.typeCodes" separator="," open="(" close=")" item="ele">
          #{ele}
        </foreach>
      </if>
      <if test="deviceAttribute.deviceUnitVersion != null and deviceAttribute.deviceUnitVersion != ''">
        and lda.version = #{deviceAttribute.deviceUnitVersion}
      </if>
      <if test="deviceAttribute.version != null and deviceAttribute.version != ''">
        and lda.version = #{deviceAttribute.version}
      </if>
      <if test="deviceAttribute.deviceUnitCode != null and deviceAttribute.deviceUnitCode != ''">
        and ldu.code = #{deviceAttribute.deviceUnitCode}
      </if>
    </where>
    ) temp
    <if test="deviceAttribute.isShow != null">
      where temp.is_show = #{deviceAttribute.isShow,jdbcType=BOOLEAN}
    </if>
    order by temp.sort_no asc
  </select>


</mapper>
