<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.deviceattributestatus.mapper.DeviceAttributeStatusMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
    <id column="id" property="id"/>
    <result column="device_code" property="deviceCode"/>
    <result column="device_name" property="deviceName"/>
    <result column="prop_name" property="propName"/>
    <result column="prop_code" property="propCode"/>
    <result column="prop_unit" property="propUnit"/>
    <result column="prop_value" property="propValue"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>


  <select id="getPageGlobal" resultMap="BaseResultMap">
    SELECT
    ldas.*
    FROM
    linkapp_device_attribute_status ldas
    INNER JOIN (
    SELECT
    device_code,
    prop_code,
    MAX(modify_time) modify_time_2
    FROM
    linkapp_device_attribute_status
    GROUP BY
    CONCAT(device_code, prop_code)
    ) ldas2 ON ldas.device_code = ldas2.device_code
    AND ldas.prop_code = ldas2.prop_code
    AND ldas.modify_time = ldas2.modify_time_2
    <where>
      1=1
      <if test="deviceAttributeStatus.deviceCode!=null and deviceAttributeStatus.deviceCode!=''">
        and ldas.device_code = #{deviceAttributeStatus.deviceCode}
      </if>
      <if test="deviceAttributeStatus.propCode!=null and deviceAttributeStatus.propCode!=''">
        and ldas.prop_code = #{deviceAttributeStatus.propCode}
      </if>
      <if test="deviceAttributeStatus.version!=null and deviceAttributeStatus.version!=''">
        and ldas.version = #{deviceAttributeStatus.version}
      </if>
      <if test="deviceAttributeStatus.propName!=null and deviceAttributeStatus.propName!=''">
        and ldas.prop_name = #{deviceAttributeStatus.propName}
      </if>
      <if test="deviceAttributeStatus.queryTimeStart!=null and deviceAttributeStatus.queryTimeStart!='' ">
        and ldas.modify_time &gt;= #{deviceAttributeStatus.queryTimeStart}
      </if>
      <if test="deviceAttributeStatus.queryTimeEnd!=null and deviceAttributeStatus.queryTimeEnd!='' ">
        and ldas.modify_time &lt;= #{deviceAttributeStatus.queryTimeEnd}
      </if>
    </where>
    order by ldas.modify_time desc
  </select>

  <resultMap id="getDeviceRealtimeDataMap" type="com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus">
    <id column="id" property="id"/>
    <result column="device_code" property="deviceCode"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="version" property="version"/>
    <result column="prop_code" property="propCode"/>
    <result column="vo_prop_name" property="propName"/>
    <result column="ico_path" property="icoPath"/>
    <result column="vo_unit" property="unit"/>
    <result column="specs" property="specs"/>
    <result column="sort_no" property="sortNo"/>
    <result column="is_show" property="isShow"/>
    <result column="vo_prop_value" property="propValue"/>
    <result column="vo_parent_prop_code" property="parentPropCode"/>
    <result column="vo_array_Index" property="arrayIndex"/>
    <result column="modify_time" property="modifyTime"/>
  </resultMap>

  <select id="getDeviceRealtimeDataGlobal" resultMap="getDeviceRealtimeDataMap">
    select temp.id,
           device_code,
           prop_code,
           array_index,
           version,
           vo_prop_value,
           vo_parent_prop_code,
           vo_array_Index,
           device_unit_id,
           vo_prop_name,
           vo_unit,
           specs,
           is_show,
           sort_no,
           modify_time,
           ico_path from(
      SELECT
      ldas3.id,
      ldas3.device_code,
      ldas3.prop_code,
      ldas3.array_index,
      ldas3.version,
      ldas3.prop_value       AS vo_prop_value,
      ldas3.parent_prop_code AS vo_parent_prop_code,
      ldas3.array_Index      AS vo_array_Index,
      ldas3.modify_time,
      lda.device_unit_id,
      lda.name               as vo_prop_name,
      lda.unit               as vo_unit,
      lda.specs,
    <choose>
      <when test="tenantId != null and tenantId != ''">
        ifnull(ldatc.is_show, lda.is_show) is_show,
        ifnull(ldatc.sort_no, lda.sort_no) sort_no,
      </when>
      <otherwise>
        lda.is_show,
        lda.sort_no,
      </otherwise>
    </choose>
    lda.ico_path
      FROM
      linkapp_device_attribute_status ldas3
      INNER JOIN linkapp_device ld ON ld.code = ldas3.device_code and ld.delete_state = 1
      INNER JOIN linkapp_device_attribute lda ON lda.device_unit_id = ld.device_unit_id AND lda.identifier = ldas3.prop_code
    <if test="tenantId != null and tenantId != ''">
      left join linkapp_device_attribute_tenant_config ldatc on ldatc.device_attribute_id = lda.id and ldatc.tenant_id = #{tenantId,jdbcType=VARCHAR}
    </if>
    <where>
      ldas3.device_code = #{deviceCode}
<!--        and ldas3.version is not null-->
      <if test='parentPropCode != null and parentPropCode != ""'>
        and ldas3.parent_prop_code = #{parentPropCode}
      </if>
      <if test='propCode != null and propCode != ""'>
        and ldas3.prop_code = #{propCode}
      </if>
      <if test='version != null and version != ""'>
        and ldas3.version = #{version}
      </if>
      <if test='tenantId != null and tenantId != ""'>
        and ld.tenant_id = #{tenantId}
      </if>
    </where>
    ) temp
<!--    <if test="isShow != null">-->
<!--   因为涉及到复杂类型 和子属性 这里放到代码里面去对 isShow 过滤   -->
<!--      where temp.is_show = #{isShow,jdbcType=BOOLEAN}-->
<!--    </if>-->
    order by
    <if test='parentPropCode != null and parentPropCode != ""'>
      temp.array_index asc,
    </if>
    temp.sort_no asc
  </select>

  <select id="getDeviceRealtimeDataByDevice" resultMap="getDeviceRealtimeDataMap">
    select temp.id,
    device_code,
    prop_code,
    array_index,
    version,
    vo_prop_value,
    vo_parent_prop_code,
    vo_array_Index,
    device_unit_id,
    vo_prop_name,
    vo_unit,
    specs,
    ico_path from(
    SELECT
    ldas3.id,
    ldas3.device_code,
    ldas3.prop_code,
    ldas3.array_index,
    ldas3.version,
    ldas3.prop_value       AS vo_prop_value,
    ldas3.parent_prop_code AS vo_parent_prop_code,
    ldas3.array_Index      AS vo_array_Index,
    lda.device_unit_id,
    lda.name               as vo_prop_name,
    lda.unit               as vo_unit,
    lda.specs,
    lda.ico_path
    FROM
    linkapp_device_attribute_status ldas3
    INNER JOIN linkapp_device ld ON ld.code = ldas3.device_code and ld.delete_state = 1
    INNER JOIN linkapp_device_attribute lda ON lda.device_unit_id = ld.device_unit_id AND lda.identifier = ldas3.prop_code
    <where>
      ldas3.device_code = #{code}
      <if test='deviceUnitVersion != null and deviceUnitVersion != ""'>
        and ldas3.version = #{deviceUnitVersion}
      </if>
      <if test="tenantId != null and tenantId != ''">
        and ld.tenant_id = #{tenantId}
      </if>
    </where>
    ) temp
  </select>

    <select id="getDeviceRealtimeDataByDeviceGlobal" resultMap="getDeviceRealtimeDataMap">
        select temp.id,
               device_code,
               prop_code,
               array_index,
               version,
               vo_prop_value,
               vo_parent_prop_code,
               vo_array_Index,
               device_unit_id,
               vo_prop_name,
               vo_unit,
               specs,
               ico_path from(
        SELECT ldas3.id,
               ldas3.device_code,
               ldas3.prop_code,
               ldas3.array_index,
               ldas3.version,
               ldas3.prop_value       AS vo_prop_value,
               ldas3.parent_prop_code AS vo_parent_prop_code,
               ldas3.array_Index      AS vo_array_Index,
               lda.device_unit_id,
               lda.name               as vo_prop_name,
               lda.unit               as vo_unit,
               lda.specs,
               lda.ico_path
        FROM linkapp_device_attribute_status ldas3
                 INNER JOIN linkapp_device ld ON ld.code = ldas3.device_code and ld.delete_state = 1
                 INNER JOIN linkapp_device_attribute lda
                            ON lda.device_unit_id = ld.device_unit_id AND lda.identifier = ldas3.prop_code
        <where>
            ldas3.device_code = #{code}
            <if test='deviceUnitVersion != null and deviceUnitVersion != ""'>
                and ldas3.version = #{deviceUnitVersion}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and ld.tenant_id = #{tenantId}
            </if>
        </where>
        ) temp
    </select>

    <select id="queryDeviceVersion" resultType="java.lang.String">
        select ldu.version
        from linkapp_device ld
                 left join linkapp_device_unit ldu on ld.device_unit_id = ldu.id
        where ld.delete_state = 1
            and ld.code = #{deviceCode}
    </select>
</mapper>
