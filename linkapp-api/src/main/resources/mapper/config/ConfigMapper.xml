<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.config.dao.ConfigMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.config.entity.Config">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="key_" jdbcType="VARCHAR" property="key"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="module_level" jdbcType="VARCHAR" property="moduleLevel"/>
        <result column="describe_" jdbcType="VARCHAR" property="describe"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="value_" jdbcType="LONGVARCHAR" property="value"/>
        <result column="example" jdbcType="LONGVARCHAR" property="example"/>
    </resultMap>

    <select id="selectList2" resultMap="BaseResultMap">
        select *
        from app_config
        <where>
            <!--            如果，传字符串null 则查询所有租户下的-->
            <if test="appConfig != null and appConfig.tenantId != null and appConfig.tenantId!='null'">
                and tenant_id = #{appConfig.tenantId}
            </if>
            <if test="appConfig.tenantId == null">
                and tenant_id is null
            </if>
            <if test="appConfig != null and appConfig.key != null">
                and key_ = #{appConfig.key}
            </if>
        </where>
        order by modify_time desc, create_time desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from app_config
        where id = #{id}
    </select>
</mapper>
