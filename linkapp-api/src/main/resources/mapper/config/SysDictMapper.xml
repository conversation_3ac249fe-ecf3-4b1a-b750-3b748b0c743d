<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.config.dao.SysDictMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.config.entity.SysDict">
        <id column="id_" jdbcType="BIGINT" property="id"/>
        <result column="dict_name_" jdbcType="VARCHAR" property="dictName"/>
        <result column="dict_code_" jdbcType="VARCHAR" property="dictCode"/>
        <result column="description_" jdbcType="VARCHAR" property="description"/>
        <result column="create_id_" jdbcType="BIGINT" property="createId"/>
        <result column="create_time_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_id_" jdbcType="BIGINT" property="updateId"/>
        <result column="update_time_" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    	id_,dict_name_,dict_code_,description_,create_id_,create_time_,update_id_,update_time_
    </sql>

    <select id="selectByPage" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List"></include>
        from sys_dict d
    </select>

</mapper>