<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.config.dao.SysDictItemMapper">
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.config.entity.SysDictItem">
        <id column="id_" jdbcType="BIGINT" property="id"/>
        <result column="dict_id_" jdbcType="BIGINT" property="dictId"/>
        <result column="item_text_" jdbcType="VARCHAR" property="itemText"/>
        <result column="item_value_" jdbcType="VARCHAR" property="itemValue"/>
        <result column="description_" jdbcType="VARCHAR" property="description"/>
        <result column="sort_order_" jdbcType="BIGINT" property="sortOrder"/>
        <result column="status_" jdbcType="BIGINT" property="status"/>
        <result column="create_id_" jdbcType="BIGINT" property="createId"/>
        <result column="create_time_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_id_" jdbcType="BIGINT" property="updateId"/>
        <result column="update_time_" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    	id_,dict_id_,item_text_,item_value_,description_,sort_order_,status_,create_id_,create_time_,update_id_,update_time_
    </sql>

    <select id="selectByPage" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List"></include>
        from sys_dict_item d
    </select>

</mapper>