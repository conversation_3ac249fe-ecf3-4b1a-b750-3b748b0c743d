<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.trainingpenetrate.mapper.RailLinkappSafetyEduDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduDetailVO">
    <id column="id" property="id" />
    <result column="person_id" property="personId" />
    <result column="kh_number" property="khNumber" />
    <result column="is_qual" property="isQual" />
    <result column="modify_time" property="modifyTime" />
    <result column="edu_id" property="eduId" />
    <result column="tenant_id`" property="tenantId`" />
    <result column="person_name" property="personName" />
    <result column="person_phone" property="personPhone" />
    <result column="person_status" property="personStatus" />
    <result column="job_type" property="jobType" />
    </resultMap>
    <select id="selectPage" resultMap="BaseResultMap"  parameterType="com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduDetailDTO">
        select a.* from rail_linkapp_safety_edu_detail a
        <where>
            <if test="entity.id != null ">
                and a.id = #{entity.id}
            </if>
            <if test="entity.tenantId != null and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.personId != null and entity.personId != ''">
                and a.person_id = #{entity.personId}
            </if>
            <if test="entity.eduId != null and entity.eduId != ''">
                and a.edu_id = #{entity.eduId}
            </if>
        </where>
        order by a.modify_time desc
    </select>

    <select id="selectPersonnelStatList" resultType="com.easylinkin.linkappapi.trainingpenetrate.vo.EduDetailStatVO">
        SELECT
        p.id  as personId,
        p.real_name as realName,
        IFNULL(e.exam_count, 0) AS examCount,
        IFNULL(j.join_train_count, 0) AS joinRainCount,
        IFNULL(pass.pass_train_count, 0) AS passTrainCount,
        CASE
        WHEN IFNULL(e.exam_count, 0) = 0 THEN '0.00%'
        ELSE CONCAT(ROUND(IFNULL(pass_qual.pass_qual_count, 0) / IFNULL(e.exam_count, 0) * 100, 2), '%')
        END AS passRate
        FROM
        (
        SELECT DISTINCT p.id, p.real_name, p.roster_type
        FROM rail_linkapp_roster_personnel p
        WHERE p.id IN (
        SELECT person_id FROM rail_linkapp_safety_edu_detail WHERE tenant_id = #{entity.tenantId}
        UNION
        SELECT curator_id FROM rail_linkapp_safety_edu_plan WHERE tenant_id =  #{entity.tenantId}
        )
        <if test="entity.rosterType != null and entity.rosterType!=''">
            and   FIND_IN_SET(#{entity.rosterType}, p.roster_type)
        </if>
        ) p
        LEFT JOIN (

        SELECT person_id, COUNT(DISTINCT edu_id) AS exam_count
        FROM (
        SELECT person_id, edu_id
        FROM rail_linkapp_safety_edu_detail
        WHERE tenant_id = #{entity.tenantId}
        <if test="entity.personName != null and entity.personName != ''">
            AND person_name LIKE CONCAT('%', #{entity.personName}, '%')
        </if>
        UNION
        SELECT curator_id AS person_id, id AS edu_id
        FROM rail_linkapp_safety_edu_plan
        WHERE tenant_id = #{entity.tenantId}
        ) t
        GROUP BY person_id
        ) e ON p.id = e.person_id
        LEFT JOIN (

        SELECT t.person_id, COUNT(DISTINCT t.edu_id) AS join_train_count
        FROM (
        SELECT person_id, edu_id
        FROM rail_linkapp_safety_edu_detail
        WHERE tenant_id = #{entity.tenantId}
        <if test="entity.personName != null and entity.personName != ''">
            AND person_name LIKE CONCAT('%', #{entity.personName}, '%')
        </if>
        UNION
        SELECT curator_id AS person_id, id AS edu_id
        FROM rail_linkapp_safety_edu_plan
        WHERE tenant_id = #{entity.tenantId}
        ) t
        GROUP BY t.person_id
        ) j ON p.id = j.person_id
        LEFT JOIN (

        SELECT person_id, COUNT(*) AS pass_train_count
        FROM rail_linkapp_safety_edu_detail
        WHERE tenant_id = #{entity.tenantId}
        <if test="entity.personName != null and entity.personName != ''">
            AND person_name LIKE CONCAT('%', #{entity.personName}, '%')
        </if>
        GROUP BY person_id
        ) pass ON p.id = pass.person_id
        LEFT JOIN (

        SELECT person_id, COUNT(*) AS pass_qual_count
        FROM rail_linkapp_safety_edu_detail
        WHERE is_qual = 0
        AND tenant_id = #{entity.tenantId}
        <if test="entity.personName != null and entity.personName != ''">
            AND person_name LIKE CONCAT('%', #{entity.personName}, '%')
        </if>
        GROUP BY person_id
        ) pass_qual ON p.id = pass_qual.person_id
        WHERE 1=1
        <if test="entity.personName != null and entity.personName != ''">
            AND p.real_name LIKE CONCAT('%', #{entity.personName}, '%')
        </if>
        ORDER BY
        <choose>
            <when test="entity.orderField != null and entity.orderField != ''">
                ${entity.orderField}
                <choose>
                    <when test="entity.orderType != null and entity.orderType != ''">
                        ${entity.orderType}
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                p.id ASC
            </otherwise>
        </choose>
    </select>

</mapper>