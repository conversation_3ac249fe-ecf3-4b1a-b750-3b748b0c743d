<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.trainingpenetrate.mapper.RailLinkappSafetyEduPlanMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduPlanVO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="edu_date" property="eduDate" />
        <result column="training_type" property="trainingType" />
        <result column="training_site" property="trainingSite" />
        <result column="content" property="content" />
        <result column="curator_id`" property="curatorId`" />
        <result column="curator_name" property="curatorName" />
        <result column="notes" property="notes" />
        <result column="imgs" property="imgs" />
        <result column="files" property="files" />
        <result column="pass_number" property="passNumber" />
        <result column="grid_id" property="gridId" />
        <result column="grid_name" property="gridName" />
        <result column="complete_number" property="completeNumber" />
        <result column="pass_through_number" property="passThroughNumber" />
        <result column="modify_time" property="modifyTime" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="modifier" property="modifier" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>
    <select id="selectPage" resultMap="BaseResultMap"  parameterType="com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduPlanDTO">
     select a.* from rail_linkapp_safety_edu_plan a
     <where>
         <if test="entity.tenantId != null and entity.tenantId != ''">
             and a.tenant_id = #{entity.tenantId}
         </if>
         <if test="entity.curatorName != null and entity.curatorName != ''">
             and a.curator_name like concat('%',#{entity.curatorName},'%')
         </if>
         <if test="entity.trainingType != null and entity.trainingType != '' ">
             and a.training_type = #{entity.trainingType}
         </if>
         <if test="entity.startDate != null and entity.startDate != '' and entity.endDate != null and entity.endDate != ''">
             and DATE_FORMAT( a.edu_date,'%Y-%m-%d') >= #{entity.startDate} and DATE_FORMAT( a.edu_date,'%Y-%m-%d') <![CDATA[<=]]> #{entity.endDate}
         </if>
         <if test="entity.ids != null and entity.ids.size() > 0">
             and a.id in
             <foreach collection="entity.ids" item="id" open="(" separator="," close=")">
                 #{id}
             </foreach>
         </if>
     </where>
        order by a.create_time desc
    </select>

    <select id="selectPersonnelStatList" resultType="com.easylinkin.linkappapi.trainingpenetrate.vo.EduDetailStatVO"  parameterType="com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduDetailDTO">

        SELECT a.person_id as personId, a.person_name as realName
        FROM rail_linkapp_safety_edu_detail a
        LEFT JOIN rail_linkapp_roster_personnel c ON a.person_id = c.id
        <where>
            a.tenant_id = #{entity.tenantId} and  c.tenant_id = #{entity.tenantId}
            <if test="entity.personName != null and entity.personName != ''">
                AND person_name LIKE CONCAT('%', #{entity.personName}, '%')
            </if>
            <if test="entity.rosterType != null and entity.rosterType!=''">
                and   FIND_IN_SET(#{entity.rosterType}, c.roster_type)
            </if>
        </where>
        UNION
        SELECT b.curator_id as personId, b.curator_name as realName
        FROM rail_linkapp_safety_edu_plan b
        LEFT JOIN rail_linkapp_roster_personnel c ON b.curator_id = c.id
        <where>
            b.tenant_id = #{entity.tenantId} and  c.tenant_id = #{entity.tenantId}
            <if test="entity.personName != null and entity.personName != ''">
               AND person_name LIKE CONCAT('%', #{entity.personName}, '%')
           </if>
            <if test="entity.rosterType != null and entity.rosterType!=''">
                and  FIND_IN_SET(#{entity.rosterType}, c.roster_type)
            </if>
        </where>


    </select>
</mapper>