<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.afterloan.mapper.AfterloanRiskNumberMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskNumber">
    <!--@mbg.generated-->
    <!--@Table dev-linkapp-mysql.afterloan_risk_number-->
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="risk_type_id" jdbcType="BIGINT" property="riskTypeId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>


  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    enterprise_id, risk_type_id, tenant_id, create_time, modify_time, creator, modifier
  </sql>

  <insert id="insertSaveOrUpdateList" parameterType="com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskNumber">
    insert into afterloan_risk_number
        (enterprise_id,risk_type_id,tenant_id,number,create_time,modify_time,creator,modifier)
        values 
        <foreach collection="list" item="item" separator=",">
          (#{item.enterpriseId},#{item.riskTypeId},#{item.tenantId},#{item.number},#{item.createTime},#{item.modifyTime},#{item.creator},#{item.modifier})
        </foreach>
        ON DUPLICATE KEY UPDATE
          enterprise_id=values(enterprise_id),
          risk_type_id=values(risk_type_id),
          tenant_id=values(tenant_id)
          ,number=values(number)
          ,modify_time=values(modify_time)
          ,modifier=values(modifier);
  </insert>
</mapper>