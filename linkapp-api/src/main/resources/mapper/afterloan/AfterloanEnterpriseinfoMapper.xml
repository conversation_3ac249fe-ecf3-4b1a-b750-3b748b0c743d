<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.afterloan.mapper.AfterloanEnterpriseinfoMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.afterloan.entity.AfterloanEnterpriseinfo">
    <!--@mbg.generated-->
    <!--@Table dev-linkapp-mysql.afterloan_enterpriseinfo-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
    <result column="lar_name" jdbcType="VARCHAR" property="larName" />
    <result column="registe_capital" jdbcType="DECIMAL" property="registeCapital" />
    <result column="enterprise_code" jdbcType="VARCHAR" property="enterpriseCode" />
    <result column="establish_time" jdbcType="TIMESTAMP" property="establishTime" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="data_source_id" jdbcType="VARCHAR" property="dataSourceId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>

  <resultMap id="getAfterloanEnterpriseinfoMap" type="com.easylinkin.linkappapi.afterloan.entity.AfterloanEnterpriseinfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
    <result column="lar_name" jdbcType="VARCHAR" property="larName" />
    <result column="registe_capital" jdbcType="VARCHAR" property="registeCapital" />
    <result column="enterprise_code" jdbcType="VARCHAR" property="enterpriseCode" />
    <result column="establish_time" jdbcType="TIMESTAMP" property="establishTime" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="data_source_id" jdbcType="VARCHAR" property="dataSourceId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <collection property="afterloanRiskTypeList" select="getAfterloanRiskType" column="{penterprise_id=id,ptenant_id = tenant_id,pisrisktypeall=isrisktypeall}"
                javaType="java.util.ArrayList"  ofType="com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskType">
    </collection>

  </resultMap>

  <resultMap id="getAfterloanRiskTypeMap" type="com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="risk_name" jdbcType="VARCHAR" property="riskName" />
    <result column="risk_type" jdbcType="TINYINT" property="riskType" />
    <result column="risk_code" jdbcType="TINYINT" property="riskCode" />
    <result column="api_url" jdbcType="VARCHAR" property="apiUrl" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, enterprise_name, lar_name, registe_capital, enterprise_code, establish_time, 
    address,data_source_id, tenant_id, create_time, modify_time, creator, modifier
  </sql>


  <select id="getAfterloanEnterpriseinfo" resultMap="getAfterloanEnterpriseinfoMap">
    select
     ae.id
    ,ae.tenant_id
    ,ae.`enterprise_name`
    ,ae.`lar_name`
    ,ae.`registe_capital`
    ,ae.`enterprise_code`
    ,ae.`establish_time`
    ,ae.`address`
    ,ae.image_url
    ,ae.data_source_id
    ,ae.create_time
    ,ae.modify_time
    <if test="risk.isQueryRiskTypeAll != null">
      ,1 isrisktypeall
    </if>
    <if test="risk.isQueryRiskTypeAll == null">
      ,null isrisktypeall
    </if>
    from afterloan_enterpriseinfo ae
    <where>
      <if test="risk.id != null and risk.id != ''">
        and ae.id=#{risk.id,jdbcType=BIGINT}
      </if>
      <if test="risk.tenantId != null and risk.tenantId != ''">
        and ae.tenant_id=#{risk.tenantId,jdbcType=VARCHAR}
      </if>
      <if test="risk.enterpriseName != null and risk.enterpriseName != ''">
        and ae.enterprise_name like CONCAT('%',#{risk.enterpriseName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="risk.ids != null and risk.ids.size() != 0">
        and id in
        <foreach collection="risk.ids" item="id" open="(" separator="," close=")">
           #{id,jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
    order by ae.modify_time desc
  </select>

  <select id="getAfterloanRiskType" resultMap="getAfterloanRiskTypeMap">
     select
     art.risk_name
    ,art.risk_type
    ,art.risk_code
    ,art.api_url
    ,art.id
    ,arn.enterprise_id
    ,IFNULL(arn.number,0) number
    from afterloan_risk_type art
    left join afterloan_risk_number arn  on  art.id=arn.risk_type_id
    <!--<where>-->
      <if test="penterprise_id != null and penterprise_id != ''">
        <if test="pisrisktypeall != null and pisrisktypeall != ''">
          and arn.enterprise_id=#{penterprise_id,jdbcType=BIGINT}
        </if>
        <if test="pisrisktypeall == null or pisrisktypeall == ''">
          and (arn.enterprise_id=#{penterprise_id,jdbcType=BIGINT} or arn.enterprise_id is null)
        </if>

      </if>
      <if test="ptenant_id != null and ptenant_id != ''">
        and arn.tenant_id=#{ptenant_id,jdbcType=VARCHAR}
      </if>
    <!--</where>-->
     ORDER BY art.risk_type,art.risk_code asc
  </select>
</mapper>