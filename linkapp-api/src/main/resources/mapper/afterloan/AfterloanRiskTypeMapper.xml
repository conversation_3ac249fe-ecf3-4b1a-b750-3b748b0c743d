<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.afterloan.mapper.AfterloanRiskTypeMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskType">
    <!--@mbg.generated-->
    <!--@Table dev-linkapp-mysql.afterloan_risk_type-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="risk_type" jdbcType="TINYINT" property="riskType" />
    <result column="risk_name" jdbcType="VARCHAR" property="riskName" />
    <result column="risk_code" jdbcType="VARCHAR" property="riskCode" />
    <result column="api_url" jdbcType="VARCHAR" property="apiUrl" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>

  <resultMap id="getTypeAndCountMap" type="com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="risk_name" jdbcType="VARCHAR" property="riskName" />
    <result column="risk_type" jdbcType="TINYINT" property="riskType" />
    <result column="risk_code" jdbcType="VARCHAR" property="riskCode" />
    <result column="api_url" jdbcType="VARCHAR" property="apiUrl" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <!--<association property="afterloanEnterpriseinfo" javaType="com.easylinkin.linkappapi.afterloan.entity.AfterloanEnterpriseinfo">-->
      <!--<result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />-->
      <!--<result column="lar_name" jdbcType="VARCHAR" property="larName" />-->
      <!--<result column="registe_capital" jdbcType="DECIMAL" property="registeCapital" />-->
      <!--<result column="enterprise_code" jdbcType="VARCHAR" property="enterpriseCode" />-->
      <!--<result column="establish_time" jdbcType="TIMESTAMP" property="establishTime" />-->
      <!--<result column="address" jdbcType="VARCHAR" property="address" />-->
      <!--<result column="data_source_id" jdbcType="VARCHAR" property="dataSourceId" />-->
    <!--</association>-->
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, risk_name, tenant_id,risk_code,api_url, create_time, modify_time, creator, modifier
  </sql>

  <select id="getTypeAndCount" resultMap="getTypeAndCountMap">
    select
     art.risk_name
    ,art.risk_type
    ,art.risk_code
    ,art.api_url
    ,art.id
    ,IFNULL(arn.number,0) number
    ,arn.enterprise_id
     from afterloan_risk_type art
    left join afterloan_risk_number arn  on  art.id=arn.risk_type_id
    <if test="risk.tenantId != null and risk.tenantId != ''">
      and arn.tenant_id=#{risk.tenantId,jdbcType=VARCHAR}
    </if>
    <if test="risk.enterpriseId != null and risk.enterpriseId != ''">
      and arn.enterprise_id=#{risk.enterpriseId,jdbcType=BIGINT}
    </if>
    <where>
      <if test="risk.riskType != null">
        and art.risk_type=#{risk.riskType,jdbcType=TINYINT}
      </if>
      <if test="risk.riskCode != null">
        and art.risk_code=#{risk.riskCode,jdbcType=TINYINT}
      </if>
      <if test="risk.queryRiskCodeList != null and risk.queryRiskCodeList.size() > 0">
        and art.risk_code in
        <foreach collection="risk.queryRiskCodeList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
      </if>
    </where>
    order by art.modify_time desc
  </select>
</mapper>