<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.easylinkin.linkappapi.security.mapper.LinkappMenuMapper">
  <resultMap id="linkappMenuMap" type="com.easylinkin.linkappapi.security.entity.LinkappMenu">
    <id column="id_" property="id"/>
    <result column="name_" property="name"/>
    <result column="path_" property="path"/>
    <result column="icon_" property="icon"/>
    <result column="privilege_code_" property="privilegeCode"/>
    <result column="search_code_" property="searchCode"/>
    <result column="level_" property="level"/>
    <result column="parent_id_" property="parentId"/>
    <result column="system_id_" property="systemId"/>
    <result column="parent_id_" property="parentId"/>
    <result column="order_no_" property="orderNo"/>
    <result column="state_" property="state"/>
    <result column="display_" property="display"/>
    <result column="remark_" property="remark"/>
  </resultMap>

  <select id="getMenuAll" resultMap="linkappMenuMap">
    SELECT
    sm.id_,
    (CASE WHEN (i.value_ IS NULL OR i.value_ = '') THEN sm.name_ ELSE i.value_ END) name_,
    sm.path_,
    sm.icon_,
    sm.search_code_,
    sm.level_,
    sm.privilege_code_,
    sp.name_ privilegeName,
    sm.parent_id_,
    smm.name_ parentName,
    sm.system_id_,
    ssi.name_ systemName,
    sm.order_no_,
    sm.state_,
    sm.remark_,
    sm.display_
    FROM
    Linkapp_menu sm
    LEFT JOIN Linkapp_menu smm ON sm.parent_id_ = smm.id_
    LEFT JOIN Linkapp_privilege sp ON sp.id_ = sm.privilege_code_
    LEFT JOIN sm_system_info ssi ON ssi.id_ = sm.system_id_
    LEFT JOIN sm_i18n i ON sm.id_ = i.key_ AND i.language_ = #{language} AND i.module_ = #{module}
    WHERE
    1 = 1
    <if test="null != queryParams.id">
      AND sm.id_ = #{queryParams.id}
    </if>
    <if test="null != queryParams.parentId">
      AND sm.parent_id_ = #{queryParams.parentId}
    </if>
    <if test="null != queryParams.searchCode and queryParams.searchCode.length != 0">
      AND (
      sm.search_code_ LIKE CONCAT(#{queryParams.searchCode},'%')
      AND sm.search_code_ <![CDATA[ <> ]]> #{queryParams.searchCode}
      )
    </if>
    <if test="queryParams.privilegeCodes!= null and queryParams.privilegeCodes.size>0">
      AND sm.privilege_code_ IN
      <foreach item="item" index="index" collection="queryParams.privilegeCodes"
        open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="null != queryParams.display">
      AND sm.display_ = #{queryParams.display}
    </if>
    ORDER BY sm.order_no_
  </select>

  <select id="getMenuPage" resultMap="linkappMenuMap">
    SELECT
    sm.id_,
    sm.name_,
    sm.path_,
    sm.icon_,
    sm.search_code_,
    sm.level_,
    sm.privilege_code_,
    sp.name_ privilegeName,
    sm.parent_id_,
    smm.name_ parentName,
    sm.system_id_,
    ssi.name_ systemName,
    sm.order_no_,
    sm.state_,
    sm.remark_,
    sm.display_
    FROM
    Linkapp_menu sm
    LEFT JOIN Linkapp_menu smm ON sm.parent_id_ = smm.id_
    LEFT JOIN Linkapp_privilege sp ON sp.id_ = sm.privilege_code_
    LEFT JOIN sm_system_info ssi ON ssi.id_ = sm.system_id_
    WHERE
    1 = 1
    <if test="null != queryParams.id">
      AND sm.id_ = #{queryParams.id}
    </if>
    <if test="null != queryParams.parentId">
      AND sm.parent_id_ = #{queryParams.parentId}
    </if>
    <if test="null != queryParams.searchCode and queryParams.searchCode.length != 0">
      AND (
      sm.search_code_ LIKE CONCAT(#{queryParams.searchCode},'%')
      AND sm.search_code_ <![CDATA[ <> ]]> #{queryParams.searchCode}
      )
    </if>
    <if test="null != queryParams.display">
      AND sm.display_ = #{queryParams.display}
    </if>
    ORDER BY sm.order_no_
  </select>

</mapper>