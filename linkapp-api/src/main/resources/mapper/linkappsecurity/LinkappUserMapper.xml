<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.easylinkin.linkappapi.security.mapper.LinkappUserMapper">
  <select id="selectUsersSorted" resultType="com.easylinkin.linkappapi.security.entity.LinkappUser">
    SELECT a.id,
           a.create_time,
           a.creator,
           a.modifier,
           a.modify_time,
           a.address,
           a.birthday,
           a.code,
           a.description,
           a.email,
           a.icon,
           a.icon_sm,
           a.id_number,
           a.nickname,
           a.password,
           a.phone,
           a.sex,
           a.username,
           a.display,
           a.type,
           a.tenant_id as tenantId,
           a.locked
    FROM linkapp_user a
    <where>
    		and a.delete_state = 1
    	<if test="user.username != null and user.username != ''">
    		and a.username LIKE CONCAT('%',#{user.username},'%')
    	</if>
    	<if test="user.nickname != null and user.nickname != ''">
    		and a.nickname LIKE CONCAT('%',#{user.nickname},'%')
    	</if>
    	<if test="user.type != null and user.type != ''">
    		and a.type = #{user.type}
    	</if>
    	<if test="user.locked != null and user.locked != ''">
    		and a.locked = #{user.locked}
    	</if>
    	<if test="user.tenantId != null and user.tenantId != ''">
    		and a.tenant_id = #{user.tenantId}
    	</if>
    </where>
    ORDER BY a.type ASC, a.modify_time DESC
  </select>

  
  <select id="selectUsers" resultType="com.easylinkin.linkappapi.security.entity.LinkappUser">
    SELECT a.id,
           a.create_time,
           a.creator,
           a.modifier,
           a.modify_time,
           a.address,
           a.birthday,
           a.code,
           a.description,
           a.email,
           a.icon,
           a.icon_sm,
           a.id_number,
           a.nickname,
           a.password,
           a.phone,
           a.sex,
           a.username,
           a.display,
           a.type,
           a.tenant_id as tenantId,
           a.locked
    FROM linkapp_user a
    <where>
    		and a.delete_state = 1
   		<if test="id != null and id != ''">
    		and a.id = #{id}
    	</if>
    	<if test="usernameLikeQuery != null">
    		and a.username LIKE CONCAT('%',#{usernameLikeQuery},'%')
    	</if>
      <if test="username != null and username != ''">
    		and a.username = #{username}
    	</if>
    	<if test="type != null and type != ''">
    		and a.type = #{type}
    	</if>
      <if test="locked != null">
        and a.locked = #{locked}
      </if>
    	<if test="tenantId != null and tenantId != ''">
    		and a.tenant_id = #{tenantId}
    	</if>
    	<if test="phone != null and phone != ''">
    		and a.phone = #{phone}
    	</if>
    </where>
    <!--勿动，否则用户登录可能存在问题，如果有业务需求，需要将排序规则传入，再修改接口-->
    order by a.create_time asc
  </select>
  
  
  <!-- 首页统计租户对应数据 -->
  <select id="auditUser" resultType="Integer" parameterType="String">
    SELECT count(1)
    FROM linkapp_user a 
    <where>
 	<if test="date != null and date != ''">
         and DATE_FORMAT(a.create_time,#{dateExp}) = DATE_FORMAT(#{date},#{dateExp})
    </if>
    	 and a.tenant_id = #{tenantId}
    </where>
  </select>
  
  <select id="selectCurrentUserSpace" resultType="com.easylinkin.linkappapi.space.entity.LinkappSpace"
		parameterType="java.lang.Long">
		SELECT 
			a.id as id,
            a.space_name as spaceName,
            a.space_no as spaceNo,
            a.short_name as shortName,
            a.sort_no as sortNo,
            a.status as status,
            a.longitude as longitude,
            a.latitude as latitude,
            a.province as province,
            a.city as city,
            a.city_code as cityCode,
            a.district as district,
            a.site as site,
            a.type as type,
            a.parent_id as parentId,
            a.remark as remark,
            a.tenant_id as tenantId,
            a.create_time as createTime,
            a.creator as creator,
            a.modifier as modifier,
            a.modify_time as modifyTime 
            FROM linkapp_space a
			LEFT JOIN linkapp_role_space b ON a.id = b.space_id
			LEFT JOIN linkapp_role c ON b.role_id = c.role_id_
			LEFT JOIN linkapp_user_ref_role d ON c.role_id_ = d.role_id
			LEFT JOIN linkapp_user e ON d.user_id = e.id
			WHERE e.id = #{id}
	</select>
	
	<select id="selectUserByRolePage" resultType="com.easylinkin.linkappapi.security.entity.LinkappUser">
	    SELECT a.id,
	           a.create_time,
	           a.creator,
	           a.modifier,
	           a.modify_time,
	           a.address,
	           a.birthday,
	           a.code,
	           a.description,
	           a.email,
	           a.icon,
	           a.icon_sm,
	           a.id_number,
	           a.nickname,
	           a.password,
	           a.phone,
	           a.sex,
	           a.username,
	           a.display,
	           a.type,
	           a.tenant_id AS tenantId,
	           a.locked
	    FROM linkapp_user a
	  LEFT JOIN linkapp_user_ref_role b 
	    ON a.id = b.user_id
	  LEFT JOIN linkapp_role c 
	    ON c.role_id_ = b.role_id
    <where>
    		and a.delete_state = 1
    		and a.type = 2
   		<if test="role.id != null and role.id != ''">
    		and c.role_id_ = #{role.id}
    	</if>
    	<if test="role.username != null and role.username != ''">
    		and a.username LIKE CONCAT('%',#{role.username},'%')
    	</if>
        <if test="role.tenantId != null and role.tenantId != ''">
            and a.tenant_id = #{role.tenantId}
        </if>
    </where>
    ORDER BY a.type ASC, a.modify_time DESC
  </select>
  
  	<delete id="deleteUser2Roles">
  		DELETE FROM linkapp_user_ref_role WHERE user_id = #{userId}
  	</delete>
  	
  	<insert id="insertUser2Roles">
  		insert into linkapp_user_ref_role 
  		<trim prefix="(" suffix=")" suffixOverrides=",">
        	<if test="id != null">
            	id,
            </if>
            <if test="userId != null">
            	user_id,
            </if>
            <if test="roleId != null">
            	role_id,
            </if>
            <if test="tenantId != null">
            	tenant_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
      		<if test="id != null">
        		#{id},
        	</if>
            <if test="userId != null">
        		#{userId},
        	</if>
            <if test="roleId != null">
            	#{roleId},
            </if>
            <if test="tenantId != null">
            	#{tenantId}
            </if>
         </trim>
  	</insert>
  	
  	<update id="tenantLock">
  		UPDATE linkapp_user SET locked = 1
  		<where>
  		  tenant_id = #{tenantId}
  		</where>  
  	</update>

    <update id="tenantLockBatch">
        UPDATE linkapp_user SET locked = 1
        <where>
            tenant_id in
            <foreach collection="tenantIdList" item="tenantId" open="(" separator="," close=")">
                #{tenantId}
            </foreach>
        </where>
    </update>

    <update id="tenantUnLock">
        UPDATE linkapp_user SET locked = 0
        <where>
            tenant_id = #{tenantId}
        </where>
    </update>

  <select id="selectOneUserByTenantPhone"
    resultType="com.easylinkin.linkappapi.security.entity.LinkappUser">
    select lu.*
    from linkapp_user lu
    where lu.delete_state = 1
    <if test="tenantId != null and tenantId != ''">
      and lu.tenant_id = #{tenantId}
    </if>
    <if test="phone != null and phone != ''">
      and lu.phone = #{phone}
    </if>
    <if test="username != null and username != ''">
      and lu.username = #{username}
    </if>
    order by lu.create_time desc limit 1
  </select>

  <select id="findByCode" resultType="com.easylinkin.linkappapi.security.entity.LinkappUser">
    SELECT
      u.*
    FROM
      linkapp_user u
      JOIN linkapp_user_ref_role ur ON u.id = ur.user_id
      JOIN linkapp_role_ref_privilege rp ON rp.role_id_ = ur.role_id
      AND u.tenant_id = rp.tenant_id
      JOIN linkapp_privilege a ON a.id_ = rp.privilege_id_
    WHERE
      u.tenant_id = #{tenantId}
      <if test="code != null and code != ''">
        and a.privilege_code_ = #{code}
      </if>
      <if test="type != null and type != ''">
        and u.type = #{type}
      </if>
    AND u.locked = 0
    UNION
    SELECT
    a.*
    FROM
    linkapp_user a
    LEFT JOIN linkapp_tenant b ON b.id = a.tenant_id
    LEFT JOIN linkapp_tenant_ref_privilege c ON c.tenant_id = b.id
    LEFT JOIN linkapp_privilege d ON d.id_ = c.privilege_id
    WHERE
    a.type = 1
    AND a.tenant_id = #{tenantId}
    <if test="code != null and code != ''">
      and d.privilege_code_ = #{code}
    </if>
    <if test="type != null and type != ''">
      and a.type = #{type}
    </if>
    AND a.locked = 0
  </select>

    <select id="selectUserByPrivilegeCode" resultType="com.easylinkin.linkappapi.security.entity.LinkappUser">
        SELECT u.id,u.tenant_id
        FROM linkapp_user u
                 JOIN linkapp_user_ref_role ur ON u.id = ur.user_id
                 JOIN linkapp_role_ref_privilege rp ON rp.role_id_ = ur.role_id
                 JOIN linkapp_privilege lp ON lp.id_ = rp.privilege_id_
        <where>
            <if test="tenantIds != null and tenantIds.size() != 0">
                u.tenant_id in
                <foreach collection="tenantIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="privilegeCode != null and privilegeCode != ''">
                and lp.privilege_code_ = #{privilegeCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectUsersByIdsStr" parameterType="String" resultType="com.easylinkin.linkappapi.security.entity.LinkappUser">
        select lu.*
        from linkapp_user lu
        where lu.id in (${_parameter})
    </select>

  <select id="selectNewestId" resultType="string">
    select id from linkapp_user order by id+0 desc limit 1
  </select>
</mapper>
