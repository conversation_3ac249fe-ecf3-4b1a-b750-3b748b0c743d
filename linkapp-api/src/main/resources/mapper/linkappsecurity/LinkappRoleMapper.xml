<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.easylinkin.linkappapi.security.mapper.LinkappRoleMapper">
  <select id="findRoles" resultType="com.easylinkin.linkappapi.security.entity.LinkappRole">
    SELECT 
      a.role_id_ AS id,
	  a.name_,
	  a.code_,
	  a.description_,
	  a.department_id_,
	  a.display_,
	  a.create_time,
	  a.creator,
	  a.modifier,
	  a.modify_time,
	  a.tenant_id 
    FROM linkapp_role a
    <where>
    	<if test="role.name != null and role.name != ''">
    		and a.name_ LIKE CONCAT('%',#{role.name},'%')
    	</if>
    	<if test="role.code != null and role.code != ''">
    		and a.code_ = #{role.code}
    	</if>
    	<if test="role.description != null and role.description != ''">
    		and a.description_ = #{role.description}
    	</if>
    	<if test="role.tenantId != null and role.tenantId != ''">
    		and a.tenant_id = #{role.tenantId}
    	</if>
    </where>
    order by a.create_time desc
  </select>

	<select id="selectSpaceByRole" resultType="com.easylinkin.linkappapi.space.entity.LinkappSpace"
		parameterType="java.lang.Long">
		SELECT
		a.id as id,
		a.space_name as spaceName,
		a.space_no as spaceNo,
		a.short_name as shortName,
		a.sort_no as sortNo,
		a.status as status,
		a.longitude as longitude,
		a.latitude as latitude,
		a.province as province,
		a.city as city,
		a.city_code as cityCode,
		a.district as district,
		a.site as site,
		a.type as type,
		a.parent_id as parentId,
		a.remark as remark,
		a.tenant_id as tenantId,
		a.create_time as createTime,
		a.creator as creator,
		a.modifier as modifier,
		a.modify_time as modifyTime
		FROM linkapp_space a
		LEFT JOIN linkapp_role_space b ON a.id = b.space_id
		LEFT JOIN linkapp_role c ON b.role_id = c.role_id_
		WHERE c.role_id_ = #{id}
	</select>

	<delete id="deleteRole2Spaces">
		DELETE FROM linkapp_role_space WHERE role_id =
		#{roleId}
	</delete>

	<insert id="insertRole2Spaces">
		insert into linkapp_role_space
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="spaceId != null">
				space_id,
			</if>
			<if test="roleId != null">
				role_id,
			</if>
			<if test="tenantId != null">
				tenant_id
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id},
			</if>
			<if test="spaceId != null">
				#{spaceId},
			</if>
			<if test="roleId != null">
				#{roleId},
			</if>
			<if test="tenantId != null">
				#{tenantId}
			</if>
		</trim>
	</insert>


	<select id="selectUserByRole"
		resultType="com.easylinkin.linkappapi.security.entity.LinkappUser"
		parameterType="java.lang.Long">
		SELECT a.id,
		a.create_time,
		a.creator,
		a.modifier,
		a.modify_time,
		a.address,
		a.birthday,
		a.code,
		a.description,
		a.email,
		a.icon,
		a.icon_sm,
		a.id_number,
		a.nickname,
		a.password,
		a.phone,
		a.sex,
		a.username,
		a.display,
		a.type,
		a.tenant_id AS tenantId,
		a.locked
		FROM linkapp_user a
		LEFT JOIN linkapp_user_ref_role b ON a.id = b.user_id
		LEFT JOIN linkapp_role c ON b.role_id = c.role_id_
		WHERE c.role_id_ = #{id}
	</select>

	<select id="findByRoleId" resultType="com.easylinkin.linkappapi.security.entity.LinkappRole">
		SELECT
			a.role_id_ AS id,
			a.name_,
			a.code_,
			a.description_,
			a.department_id_,
			a.display_,
			a.create_time,
			a.creator,
			a.modifier,
			a.modify_time,
			a.tenant_id
    FROM
    	linkapp_role a
    WHERE
    	a.role_id_ = #{roleId}
	</select>

	<delete id="deleteRole2Users">
		DELETE FROM linkapp_user_ref_role WHERE role_id = #{roleId}
	</delete>

	<insert id="insertRole2Users">
		insert into linkapp_user_ref_role
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="userId != null">
				user_id,
			</if>
			<if test="roleId != null">
				role_id,
			</if>
			<if test="tenantId != null">
				tenant_id
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id},
			</if>
			<if test="userId != null">
				#{userId},
			</if>
			<if test="roleId != null">
				#{roleId},
			</if>
			<if test="tenantId != null">
				#{tenantId}
			</if>
		</trim>
	</insert>
  
</mapper>