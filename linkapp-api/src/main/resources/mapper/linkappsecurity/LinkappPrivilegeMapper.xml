<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.easylinkin.linkappapi.security.mapper.LinkappPrivilegeMapper">
  <resultMap id="linkappPrivilegeMap" type="com.easylinkin.linkappapi.security.entity.LinkappPrivilege">
    <id column="id_" property="id"/>
    <result column="parent_id_" property="parentId"/>
    <result column="name_" property="name"/>
    <result column="privilege_code_" property="code"/>
    <result column="description_" property="description"/>
    <result column="level_" property="level"/>
    <result column="target" property="target"/>
    <result column="search_code_" property="searchCode"/>
    <result column="seq_" property="sort"/>
    <result column="type_" property="type"/>
    <result column="url_" property="url"/>
    <result column="is_log_" property="isLog"/>
    <result column="tenant_id_" property="tenantId"/>
    <result column="icon_name" property="iconName"/>
    <result column="create_time_" property="createTime"/>
    <result column="creator_" property="creator"/>
    <result column="modifier_" property="modifier"/>
    <result column="modify_time_" property="modifyTime"/>
  </resultMap>

  <select id="selectPrivilegeByRole" resultMap="linkappPrivilegeMap">
	SELECT
		a.*
	FROM
		linkapp_privilege a
		LEFT JOIN linkapp_role_ref_privilege b ON a.id_ = b.privilege_id_
		LEFT JOIN linkapp_role c ON b.role_id_ = c.role_id_
	<where>
		a.type_ != 0
		<if test="roleId != null and roleId != ''">
			and c.role_id_ = #{roleId}
		</if>
		<if test="type == null or type == 0">
			and a.flag_=0
		</if>
		<if test="type == 1">
			and a.flag_=1
		</if>
		<if test="type == 2">
			and a.flag_=2
		</if>
	</where>
	order by a.seq_
  </select>

  <select id="selectPrivilegeAll" resultMap="linkappPrivilegeMap">
	SELECT
		a.*
	FROM
		linkapp_privilege a
	<where>
		a.type_ != 0
		<if test="type != null and type != ''">
			and a.type_ = #{type}
		</if>
		<if test="parentId != null and parentId != ''">
			and a.parent_id_ = #{parentId}
		</if>
		<if test="flag != null and flag != ''">
			and a.flag_ = #{flag}
		</if>
	</where>
	order by a.seq_
  </select>

	<select id="checkPrivilegeUrlIsRepeat" resultMap="linkappPrivilegeMap">
		SELECT
		a.*
		FROM
		linkapp_privilege a
		<where>
			a.type_ != 0
			<if test="flag != null and flag != ''">
				and a.flag_ = #{flag}
			</if>
			<if test="id != null and id != ''">
				and a.id_ <![CDATA[<>]]> #{id}
			</if>
			<if test="url != null and url != ''">
				and a.url_ = #{url}
			</if>
		</where>
		order by a.seq_
	</select>

	<select id="checkPrivilegeSeqIsRepeat" resultType="java.lang.Integer">
		SELECT
			count(*)
		FROM
		linkapp_privilege a
		<where>
			a.type_ != 0
			<if test="id != null and id != ''">
				and a.id_ <![CDATA[<>]]> #{id}
			</if>
			<if test="sort != null and sort != ''">
				and a.seq_ = #{sort}
			</if>
		</where>
	</select>


	<select id="selectPrivilegeByUser" resultMap="linkappPrivilegeMap">
	SELECT
		DISTINCT a.*
	FROM
		linkapp_privilege a
		LEFT JOIN linkapp_role_ref_privilege b ON a.id_ = b.privilege_id_
		LEFT JOIN linkapp_role c ON b.role_id_ = c.role_id_
		LEFT JOIN linkapp_user_ref_role d ON c.role_id_ = d.role_id
		LEFT JOIN linkapp_user e ON d.user_id = e.id
	<where>
		a.type_ != 0
		<if test="userId != null and userId != ''">
			and e.id = #{userId}
		</if>
		<if test="type == null or type == 0">
			and a.flag_=0
		</if>
		<if test="type == 1">
			and a.flag_=1
		</if>
		<if test="type == 2">
			and a.flag_=2
		</if>
	</where>
	order by a.seq_
  </select>

	<select id="selectPrivilegeCustomByUser" resultMap="linkappPrivilegeMap">
		select
			CASE
			WHEN t.c_name_ IS NOT NULL OR t.c_name_ != '' THEN t.name_
			ELSE t.name_
			END AS name_,
			CASE
			WHEN t.c_seq_ IS NOT NULL THEN t.c_seq_
			ELSE t.seq_
			END AS seq_,
			t.*
			from (
		SELECT
			a.*,
			a_.name_ as c_name_,
			a_.seq_ as c_seq_
		FROM
		linkapp_privilege a
		left join linkapp_privilege_custom a_ on a.id_ = a_.privilege_id_ and a_.tenant_id_ = #{tenantId}
		LEFT JOIN linkapp_role_ref_privilege b ON a.id_ = b.privilege_id_
		LEFT JOIN linkapp_role c ON b.role_id_ = c.role_id_
		LEFT JOIN linkapp_user_ref_role d ON c.role_id_ = d.role_id
		LEFT JOIN linkapp_user e ON d.user_id = e.id
		<where>
			a.type_ != 0
			<if test="userId != null and userId != ''">
				and e.id = #{userId}
			</if>
			<if test="type == null or type == 0">
				and a.flag_=0
			</if>
			<if test="type == 1">
				and a.flag_=1
			</if>
			<if test="type == 2">
				and a.flag_=2
			</if>
		</where>

		union all

		<!-- 菜单分隔符属于租户	-->
		SELECT
			a.*,
			a_.name_ as c_name_,
			a_.seq_ as c_seq_
		FROM
		linkapp_privilege a
		left join linkapp_privilege_custom a_ on a.id_ = a_.privilege_id_ and a_.tenant_id_ = #{tenantId}
		LEFT JOIN linkapp_tenant_ref_privilege b ON a.id_ = b.privilege_id
		<where>
			a.type_ = 0
			and b.tenant_id = #{tenantId}
			<if test="type == null or type == 0">
				and a.flag_=0
			</if>
			<if test="type == 1">
				and a.flag_=1
			</if>
			<if test="type == 2">
				and a.flag_=2
			</if>
		</where>

		) t
		group by t.id_
		order by
			case
			WHEN t.c_seq_ IS NOT NULL THEN t.c_seq_
			ELSE t.seq_
			END
	</select>

    <delete id="deletePrivilege2Role">
		DELETE FROM
			linkapp_role_ref_privilege
		WHERE
			role_id_ = #{roleId}
		<if test="type == null or type == 0">
			and exists ( select 1 from linkapp_privilege p where privilege_id_=p.id_ and p.flag_=0)
		</if>
		<if test="type == 1">
			and exists ( select 1 from linkapp_privilege p where privilege_id_=p.id_ and p.flag_=1)
		</if>
		<if test="type == 2">
			and exists ( select 1 from linkapp_privilege p where privilege_id_=p.id_ and p.flag_=2)
		</if>
	</delete>

	<insert id="insertPrivilege2Role">
		insert into linkapp_role_ref_privilege
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="privilegeId != null">
				privilege_id_,
			</if>
			<if test="roleId != null">
				role_id_,
			</if>
			<if test="tenantId != null">
				tenant_id
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id},
			</if>
			<if test="privilegeId != null">
				#{privilegeId},
			</if>
			<if test="roleId != null">
				#{roleId},
			</if>
			<if test="tenantId != null">
				#{tenantId}
			</if>
		</trim>
	</insert>

	<insert id="insertPrivilegeExt">
		insert into linkapp_privilege
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id_,
			</if>
			<if test="parentId != null">
				parent_id_,
			</if>
			<if test="name != null">
				name_,
			</if>
			<if test="code != null">
				privilege_code_,
			</if>
			<if test="description != null">
				description_,
			</if>
			<if test="level != null">
				level_,
			</if>
			<if test="target != null">
				target,
			</if>
			<if test="searchCode != null">
				search_code_,
			</if>
			<if test="sort != null">
				seq_,
			</if>
			<if test="type != null">
				type_,
			</if>
			<if test="url != null">
				url_,
			</if>
			<if test="isLog != null">
				is_log_,
			</if>
			<if test="tenantId != null">
				tenant_id_,
			</if>
			<if test="createTime != null">
				create_time_,
			</if>
			<if test="creator != null">
				creator_,
			</if>
			<if test="modifier != null">
				modifier_,
			</if>
			<if test="modifyTime != null">
				modify_time_,
			</if>
			<if test="iconName != null">
				icon_name,
			</if>
			<if test="flag != null">
				flag_
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id},
			</if>
			<if test="parentId != null">
				#{parentId},
			</if>
			<if test="name != null">
				#{name},
			</if>
			<if test="code != null">
				#{code},
			</if>
			<if test="description != null">
				#{description},
			</if>
			<if test="level != null">
				#{level},
			</if>
			<if test="target != null">
				#{target},
			</if>
			<if test="searchCode != null">
				#{searchCode},
			</if>
			<if test="sort != null">
				#{sort},
			</if>
			<if test="type != null">
				#{type},
			</if>
			<if test="url != null">
				#{url},
			</if>
			<if test="isLog != null">
				#{isLog},
			</if>
			<if test="tenantId != null">
				#{tenantId},
			</if>
			<if test="createTime != null">
				#{createTime},
			</if>
			<if test="creator != null">
				#{creator},
			</if>
			<if test="modifier != null">
				#{modifier},
			</if>
			<if test="modifyTime != null">
				#{modifyTime},
			</if>
			<if test="iconName != null">
				#{iconName},
			</if>
			<if test="flag != null">
				#{flag}
			</if>
		</trim>
	</insert>



	<update id="updatePrivilegeExt">
		update  linkapp_privilege
		<set>
			<if test="parentId != null">
				parent_id_ = #{parentId},
			</if>
			<if test="name != null">
				name_ = #{name},
			</if>
			<if test="code != null">
				privilege_code_ = #{code},
			</if>
			<if test="description != null">
				description_ = #{description},
			</if>
			<if test="level != null">
				level_ = #{level},
			</if>
			<if test="target != null">
				target = #{target},
			</if>
			<if test="searchCode != null">
				search_code_ = #{searchCode},
			</if>
			<if test="sort != null">
				seq_ = #{sort},
			</if>
			<if test="type != null">
				type_ = #{type},
			</if>
			<if test="url != null">
				url_ = #{url},
			</if>
			<if test="isLog != null">
				is_log_ = #{isLog},
			</if>
			<if test="tenantId != null">
				tenant_id_ = #{tenantId},
			</if>
			<if test="createTime != null">
				create_time_ = #{createTime},
			</if>
			<if test="creator != null">
				creator_ = #{creator},
			</if>
			<if test="modifier != null">
				modifier_ = #{modifier},
			</if>
			<if test="modifyTime != null">
				modify_time_ = #{modifyTime},
			</if>
			<if test="iconName != null">
				icon_name = #{iconName},
			</if>
			<if test="flag != null">
				flag_ = #{flag}
			</if>
		</set>
		<where>
			id_ = #{id}
		</where>
	</update>

	<select id="getPrivilegeMaxId" resultMap="linkappPrivilegeMap">
		SELECT
			*
		FROM
			linkapp_privilege
		WHERE
				length(id_) = (
				SELECT
					max(LENGTH(id_))
				FROM
					linkapp_privilege
			)
		ORDER BY
			id_ DESC;
	</select>

	<select id="selectByCode" resultMap="linkappPrivilegeMap">
		select *
		from linkapp_privilege
		where privilege_code_ = #{code}
		limit 1
	</select>

</mapper>
