<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.easylinkin.linkappapi.operatelog.mapper.CommonOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.operatelog.entity.CommonOperateLog">
    <id property="id" column="id"/>
    <id property="content" column="content"/>
    <id property="createTime" column="create_time"/>
    <id property="userAccount" column="user_account"/>
    <id property="nickname" column="nickname"/>
    <id property="tenantId" column="tenant_id"/>
    <id property="phone" column="phone_"/>
    <id property="platform" column="platform_"/>
    <id property="moduleName" column="module_name"/>
    <id property="resultContent" column="result_content"/>
    <id property="params" column="params"/>
    <id property="result" column="result"/>
  </resultMap>


  <select id="selectLogs" resultMap="BaseResultMap">
    SELECT *
    FROM common_operate_log
    <where>
      1 = 1
      <if test="log.id != null">
        AND id = #{log.id}
      </if>
      <if test="log.moduleName != null and log.moduleName != ''">
        AND module_name like concat('%', #{log.moduleName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="log.queryTimeStart != null and log.queryTimeStart != ''">
        AND create_time &gt;= #{log.queryTimeStart,jdbcType=VARCHAR}
      </if>
      <if test="log.queryTimeEnd != null and log.queryTimeEnd != ''">
        AND create_time &lt;= #{log.queryTimeEnd,jdbcType=VARCHAR}
      </if>
      <if test="log.tenantId != null and log.tenantId != ''">
        AND tenant_id = #{log.tenantId,jdbcType=VARCHAR}
      </if>
      <if test="log.content != null and log.content != ''">
        AND content like concat(#{log.content,jdbcType=VARCHAR}, '%')
      </if>
      <if test="log.userAccount != null and log.userAccount != ''">
        AND user_account like CONCAT('%', #{log.userAccount}, '%')
      </if>
      <if test="log.phone != null and log.phone != ''">
        AND phone_ like CONCAT('%', #{log.phone}, '%')
      </if>
      <if test="log.nickname != null and log.nickname != ''">
        AND nickname like CONCAT('%', #{log.nickname}, '%')
      </if>
      <if test="log.result != null">
        AND result = #{log.result,jdbcType=BOOLEAN}
      </if>
    </where>
    order by create_time desc
  </select>

  <delete id="deleteLogs">
    delete
    from common_operate_log
    <where>
      tenant_id = #{log.tenantId,jdbcType=VARCHAR}
      <if test="log.queryTimeStart != null and log.queryTimeStart != ''">
        AND create_time &gt;= #{log.queryTimeStart,jdbcType=VARCHAR}
      </if>
      <if test="log.queryTimeEnd != null and log.queryTimeEnd != ''">
        AND create_time &lt;= #{log.queryTimeEnd,jdbcType=VARCHAR}
      </if>
    </where>
    
  </delete>
  
</mapper>
