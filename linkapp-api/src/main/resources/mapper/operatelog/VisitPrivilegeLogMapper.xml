<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.operatelog.mapper.VisitPrivilegeLogMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap"
    type="com.easylinkin.linkappapi.operatelog.entity.VisitPrivilegeLog">
    <id column="id_" property="id"/>
    <result column="name_" property="name"/>
    <result column="privilege_code_" property="privilegeCode"/>
    <result column="url_" property="url"/>
    <result column="tenant_id_" property="tenantId"/>
    <result column="create_time_" property="createTime"/>
    <result column="creator_id_" property="creatorId"/>
  </resultMap>

  <select id="getPage" resultMap="BaseResultMap">
    select *
    from
    (
        select *
        from app_visit_privilege_log
        where tenant_id_ = #{visitPrivilegeLog.tenantId}
        and creator_id_ = #{visitPrivilegeLog.creatorId}
    <if test="codes !=null and codes.size>0">
      and privilege_code_ in
      <foreach collection="codes" index="index" item="code" open="(" separator="," close=")">
        #{code}
      </foreach>
    </if>
    order by create_time_ desc
    ) a
    group by privilege_code_
    order by create_time_ desc
    </select>
</mapper>
