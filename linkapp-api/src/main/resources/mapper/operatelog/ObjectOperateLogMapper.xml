<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.operatelog.mapper.ObjectOperateLogMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.operatelog.entity.ObjectOperateLog">
    <id column="log_id" property="logId"/>
    <result column="object_id" property="objectId"/>
    <result column="object_key" property="objectKey"/>
    <result column="operate_desc" property="operateDesc"/>
    <result column="content" property="content"/>
    <result column="fail_information" property="failInformation"/>
    <result column="ip" property="ip"/>
    <result column="url" property="url"/>
    <result column="module_name" property="moduleName"/>
    <result column="status" property="status"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="creator_name" property="creatorName"/>
    <result column="tenant_id" property="tenantId"/>
  </resultMap>

  <select id="selectLogs" resultMap="BaseResultMap">
    SELECT *
    FROM linkapp_obj_operate_log
    <where>
      1=1
      <if test="log.logId!=null and log.logId!=''">
        AND log_id = #{log.logId}
      </if>
      <if test="log.objectId!=null and log.objectId!=''">
        AND object_id = #{log.objectId}
      </if>
      <if test="log.objectKey!=null and log.objectKey!=''">
        AND object_key = #{log.objectKey,jdbcType=VARCHAR}
      </if>
      <if test="log.moduleName!=null and log.moduleName!=''">
        AND module_name = #{log.moduleName,jdbcType=VARCHAR}
      </if>
      <if test="log.queryTimeStart!=null and log.queryTimeStart!=''">
        AND create_time &gt;= #{log.queryTimeStart,jdbcType=VARCHAR}
      </if>
      <if test="log.queryTimeEnd!=null and log.queryTimeEnd!=''">
        AND create_time &lt;= #{log.queryTimeEnd,jdbcType=VARCHAR}
      </if>
      <if test="log.operateDesc!=null and log.operateDesc!=''">
        AND operate_desc = #{log.operateDesc,jdbcType=VARCHAR}
      </if>
      <if test="log.tenantId!=null and log.tenantId!=''">
        AND tenant_id = #{log.tenantId,jdbcType=VARCHAR}
      </if>
    </where>
    order by create_time desc

  </select>

</mapper>
