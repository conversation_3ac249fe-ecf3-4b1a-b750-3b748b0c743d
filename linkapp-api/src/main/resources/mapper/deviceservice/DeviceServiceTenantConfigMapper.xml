<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.deviceservice.mapper.DeviceServiceTenantConfigMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.deviceservice.entity.DeviceServiceTenantConfig">
    <id column="id" property="id"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="device_service_id" property="deviceServiceId"/>
    <result column="sort_no" property="sortNo"/>
    <result column="is_show" property="isShow"/>
  </resultMap>

  <select id="getDeviceServiceTenantConfigs" resultMap="BaseResultMap">
    select ldstc.id, ldstc.tenant_id, ldstc.device_service_id, ldstc.sort_no, ldstc.is_show, lds.name as deviceServiceName
    from linkapp_device_service_tenant_config ldstc
           left join linkapp_device_service lds on ldstc.device_service_id = lds.id
      where 1 = 1
    <if test="deviceServiceTenantConfig.id != null and deviceServiceTenantConfig.id != ''">
      and ldstc.id = #{deviceServiceTenantConfig.id}
    </if>
    <if test="deviceServiceTenantConfig.deviceUnitId != null and deviceServiceTenantConfig.deviceUnitId != ''">
      and lds.device_unit_id = #{deviceServiceTenantConfig.deviceUnitId}
    </if>
    <if test="deviceServiceTenantConfig.deviceServiceId != null and deviceServiceTenantConfig.deviceServiceId != ''">
      and ldstc.device_service_id = #{deviceServiceTenantConfig.deviceServiceId}
    </if>
    <if test="deviceServiceTenantConfig.tenantId != null and deviceServiceTenantConfig.tenantId != ''">
      and ldstc.tenant_id = #{deviceServiceTenantConfig.tenantId}
    </if>
    order by ldstc.sort_no asc
  </select>

  <update id="inheritPersonalizedConfig">
    update     linkapp_device_service_tenant_config ldatc
    <!--    lds 为老版本的 设备服务-->
    inner join linkapp_device_service lds on ldatc.device_service_id = lds.id
      inner join linkapp_device_unit ldu on lds.device_unit_id = ldu.id
    <!--    lds2 是 新插入的 设备服务-->
    inner join linkapp_device_unit ldu2 on ldu2.code = ldu.code and ldu2.id != ldu.id
      inner JOIN linkapp_device_service lds2 ON lds2.device_unit_id = ldu2.id and lds.identifier = lds2.identifier and lds2.version != lds.version
      set ldatc.device_service_id = lds2.id
      where lds2.id in

    <foreach collection="addDeviceServicesIds" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
  </update>
  
  <delete id="deleteServiceIdentifierRepeatData">
    delete ldatc
    from linkapp_device_service_tenant_config ldatc
           inner join linkapp_device_service lda on ldatc.device_service_id = lda.id
           inner join (
      SELECT s2.tenant_id, s2.ldu_id
      FROM (
             SELECT IF(
                      s1.code_identi = @code_identi,
                      @rownum := @rownum + 1,
                      @rownum := 1
                      ),
                    @code_identi := code_identi,
                    @rownum rownum,
                    s1.ldu_code,
                    s1.ldu_id,
                    tenant_id,
                    version
             FROM (
                    SELECT DISTINCT ldu.CODE                             ldu_code,
                                    ldu.id                               ldu_id,
                                    ldu.version,
                                    ldatc.tenant_id,
                                    concat(ldatc.tenant_id, ldu.CODE) AS code_identi
                    FROM linkapp_device_service_tenant_config ldatc
                           INNER JOIN linkapp_device_service lda ON ldatc.device_service_id = lda.id
                           INNER JOIN linkapp_device_unit ldu ON lda.device_unit_id = ldu.id
                    ORDER BY ldatc.tenant_id,
                             ldu.CODE ASC,
                             ldu.create_time DESC
                    ) s1,
                  (SELECT @code_identi := NULL) var1,
                  (SELECT @rownum := 0) var2
             ) s2
      where s2.rownum > 1) tmp on ldatc.tenant_id = tmp.tenant_id and lda.device_unit_id = tmp.ldu_id
  </delete>
  
  <!--  刷数据 服务id 本身不在 linkapp_device_service_tenant_config表，但是其同型号下的兄弟服务在这个表中 -->
  <select id="getNeedAddConfigAfterInheritPersonalizedConfig" resultType="com.easylinkin.linkappapi.deviceservice.entity.DeviceServiceTenantConfig">
    SELECT DISTINCT t1.tenant_id,
                    lda2.id as device_service_id
    FROM (
           SELECT device_unit_id,
                  ldatc.tenant_id
           FROM linkapp_device_service lda
                  INNER JOIN linkapp_device_service_tenant_config ldatc ON lda.id = ldatc.device_service_id
           ) t1
           INNER JOIN linkapp_device_service lda2 ON lda2.device_unit_id = t1.device_unit_id
           inner join linkapp_device_unit t3 on t3.id = lda2.device_unit_id
    WHERE (
            SELECT count(1)
            FROM linkapp_device_service_tenant_config ldatc2
            WHERE ldatc2.device_service_id = lda2.id
              AND ldatc2.tenant_id = t1.tenant_id
            ) = 0
    ORDER BY t1.tenant_id, t3.code, t3.version, lda2.identifier
    
  </select>
  
  
  <select id="getLatestToBeUpdatedPersonalizedConfig" resultType="com.easylinkin.linkappapi.deviceservice.entity.DeviceServiceTenantConfig">

    SELECT id,
    tenant_id,
    lda2_device_service_id as device_service_id,
    ldatc_sort_no            as sort_no,
    ldatc_is_show               is_show
    <!--      ,-->
    <!--           ldatc_device_service_id,-->
    <!--           lda2_identifier,-->
    <!--           lda_identifier,-->
    <!--           lda2_version,-->
    <!--           lda_version,-->
    <!--           device_unit_id,-->
    <!--           lda2_create_time,-->
    <!--           lda_create_time,-->
    <!--           rownum-->
    from (SELECT IF(
    t1.code_identi = @code_identi,
    @rownum := @rownum + 1,
    @rownum := 1
    ),

    @code_identi := code_identi,
    @rownum rownum,
    id,
    tenant_id,
    lda2_device_service_id,
    ldatc_device_service_id,
    ldatc_is_show,
    ldatc_sort_no
    <!--                 lda2_identifier,-->
    <!--                 lda_identifier,-->
    <!--                 lda2_version,-->
    <!--                 lda_version,-->
    <!--                 device_unit_id,-->
    <!--                 lda2_create_time,-->
    <!--                 lda_create_time-->

    from (select concat(ldatc.tenant_id, ldu2.code, lda2.identifier) AS code_identi,
    ldatc.id,
    ldatc.tenant_id,
    ldatc.is_show                                          ldatc_is_show,
    ldatc.sort_no                                          ldatc_sort_no,
    ldatc.device_service_id                           as ldatc_device_service_id,
    lda2.id                                             as lda2_device_service_id,
    ldu2.code                                              ldu2_code,
    lda2.device_unit_id,
    lda2.identifier                                        lda2_identifier,
    lda2.version                                           lda2_version,
    lda2.create_time                                       lda2_create_time,
    lda.identifier                                         lda_identifier,
    lda.version                                            lda_version,
    lda.create_time                                        lda_create_time

    from linkapp_device_service_tenant_config ldatc
    inner join linkapp_device_service lda on ldatc.device_service_id = lda.id
    inner join linkapp_device_unit ldu on lda.device_unit_id = ldu.id
    inner join linkapp_device_unit ldu2 on ldu2.code = ldu.code
    inner JOIN linkapp_device_service lda2 ON lda2.device_unit_id = ldu2.id and lda.identifier = lda2.identifier
    order by ldatc.tenant_id, ldu2.code, lda2.identifier, lda2.create_time desc, lda2.version desc) t1,
    (SELECT @code_identi := NULL) var1,
    (SELECT @rownum := 0) var2) t2
    where t2.rownum = 1
    and ldatc_device_service_id != t2.lda2_device_service_id
    and (
    select count(1)
    from linkapp_device_service_tenant_config
    where t2.tenant_id = tenant_id
    and lda2_device_service_id = device_service_id
    ) = 0
    and t2.lda2_device_service_id in
    <foreach collection="addDeviceServicesIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

</mapper>
