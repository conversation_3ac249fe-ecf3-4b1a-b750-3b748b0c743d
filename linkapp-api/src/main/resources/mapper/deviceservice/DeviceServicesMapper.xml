<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.easylinkin.linkappapi.deviceservice.mapper.DeviceServicesMapper">
  <resultMap id="BaseResultMap"
    type="com.easylinkin.linkappapi.deviceservice.entity.DeviceServices">
    <id column="id" property="id"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="identifier" property="identifier"/>
    <result column="name" property="name"/>
    <result column="type" property="type"/>
    <result column="sort_no" property="sortNo"/>
    <result column="is_show" property="isShow"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <collection property="paramList" column="{deviceServiceId = id}" select="com.easylinkin.linkappapi.deviceparm.mapper.DeviceParmMapper.selectDeviceParmList" ofType="com.easylinkin.linkappapi.deviceparm.entity.DeviceParm">
    </collection>
  </resultMap>


  <select id="getDeviceServicesList" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.deviceservice.entity.DeviceServices">
    select temp.* from(
      select lds.id,
             lds.device_unit_id,
             lds.identifier,
             lds.name,
             lds.type,
             lds.create_time,
             lds.modify_time,
    <choose>
      <when test="tenantId != null and tenantId != ''">
        ifnull(ldstc.is_show, lds.is_show) is_show,
        ifnull(ldstc.sort_no, lds.sort_no) sort_no,
      </when>
      <otherwise>
        lds.is_show,
        lds.sort_no,
      </otherwise>
    </choose>
    lds.version
      from linkapp_device_service lds
    <if test="tenantId != null and tenantId != ''">
      left join linkapp_device_service_tenant_config ldstc on ldstc.device_service_id = lds.id and ldstc.tenant_id = #{tenantId,jdbcType=VARCHAR}
    </if>
    <where>
      <if test="id != null and id != ''">
        and lds.id = #{id}
      </if>
      <if test="deviceUnitId != null and deviceUnitId != ''">
        and lds.device_unit_id = #{deviceUnitId}
      </if>
      <if test="identifier != null and identifier != ''">
        and lds.identifier = #{identifier}
      </if>
      <if test="version != null and version != ''">
        AND lds.version = #{version}
      </if>
      <if test="name != null and name != ''">
        and lds.name = #{name}
      </if>
      <if test="type != null and type != ''">
        and lds.type = #{type}
      </if>
    </where>
    ) temp
    <where>
      <if test="isShow != null">
        and temp.is_show = #{isShow}
      </if>
    </where>
      order by temp.sort_no asc
  </select>
</mapper>
