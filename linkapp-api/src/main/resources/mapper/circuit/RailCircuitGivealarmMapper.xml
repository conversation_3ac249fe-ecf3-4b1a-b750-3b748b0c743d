<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.circuit.mapper.RailCircuitGivealarmMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.circuit.vo.RailCircuitGivealarmVO">
        <id column="id" property="id" />
        <result column="device_code" property="deviceCode" />
        <result column="give_type" property="giveType" />
        <result column="fault_type" property="faultType" />
        <result column="create_time" property="createTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="content" property="content" />
        <result column="code" property="code" />
        <result column="fault_description" property="faultDescription" />
        <result column="fault_value" property="faultValue" />

        <result column="a_phase_current" property="aPhaseCurrent" />
        <result column="a_phase_voltage" property="aPhaseVoltage" />
        <result column="a_phase_power" property="aPhasePower" />
        <result column="b_phase_current" property="bPhaseCurrent" />
        <result column="b_phase_voltage" property="bPhaseVoltage" />
        <result column="b_phase_power" property="bPhasePower" />
        <result column="c_phase_current" property="cPhaseCurrent" />
        <result column="c_phase_voltage" property="cPhaseVoltage" />
        <result column="c_phase_power" property="cPhasePower" />
        <result column="active_power" property="activePower" />
        <result column="residual_current" property="residualCurrent" />
        <result column="line_online_state" property="lineOnlineState" />
        <result column="switch_state" property="switchState" />
    </resultMap>

    <select id="getList" resultMap="BaseResultMap"  parameterType="com.easylinkin.linkappapi.circuit.dto.RailCircuitGivealarmDTO">
         select a.* from rail_circuit_givealarm a
        <where>
            <if test="entity.id != null ">
                and a.id = #{entity.id}
            </if>
            <if test="entity.faultType != null and entity.faultType != '' ">
                and a.fault_type = #{entity.faultType}
            </if>
            <if test="entity.giveType != null and entity.giveType != '' ">
                and a.give_type = #{entity.giveType}
            </if>
            <if test="entity.deviceCode != null and entity.deviceCode != '' ">
                and a.device_code = #{entity.deviceCode}
            </if>
            <if test="entity.deviceCode != null and entity.deviceCode != '' ">
                and a.device_code = #{entity.deviceCode}
            </if>
            <if test="entity.tenantId != null and entity.tenantId != '' ">
               and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.startDate != null and entity.startDate != '' and entity.endDate != null and entity.endDate != ''">
               and DATE_FORMAT( a.create_time,'%Y-%m-%d') >= #{entity.startDate} and DATE_FORMAT( a.create_time,'%Y-%m-%d') <![CDATA[<=]]> #{entity.endDate}
            </if>
            <if test="entity.deviceCodes != null and !entity.deviceCodes.isEmpty()">
              and  a.device_code IN
                <foreach item="item" collection="entity.deviceCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="codeByNewRecords" resultType="com.easylinkin.linkappapi.circuit.vo.RailCircuitGivealarmVO">
    SELECT DISTINCT  dr.*
    FROM rail_circuit_givealarm dr
    INNER JOIN (
    SELECT device_code, MAX(create_time) AS max_time
    FROM rail_circuit_givealarm
    WHERE  tenant_id = #{tenantId} and device_code IN
    <foreach item="code" collection="codes" open="(" separator="," close=")">
        #{code}
    </foreach>
    GROUP BY device_code
    ) AS latest
    ON dr.device_code = latest.device_code
    AND dr.create_time = latest.max_time
</select>


</mapper>