<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.circuit.mapper.RailLinkappHighFormworkRecordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.circuit.vo.RailLinkappHighFormworkRecordVO">
        <id column="id" property="id"/>
        <result column="device_code" property="deviceCode"/>
        <result column="up_times" property="upTimes"/>
        <result column="word_mode" property="wordMode"/>
        <result column="alarm_status" property="alarmStatus"/>
        <result column="erg" property="erg"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="sedimentation" property="sedimentation"/>
        <result column="pressure" property="pressure"/>
        <result column="angle_y" property="angleY"/>
        <result column="angle_x" property="angleX"/>
        <result column="data_time" property="dataTime"/>
        <result column="horizontal_displacement" property="horizontalDisplacement"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="device_sub_type" property="deviceSubType"/>
        <result column="item_id" property="itemId"/>
        <result column="high_code" property="highCode"/>
        <result column="deviceName" property="deviceName"/>
        <result column="highName" property="highName"/>
        <result column="itemName" property="itemName"/>
        <result column="onlineState" property="onlineState"/>
        <result column="highDeviceType" property="highDeviceType"/>
    </resultMap>
    <select id="getList" resultMap="BaseResultMap">
    select a.*,d.name as  deviceName,fi.item_name as itemName from rail_linkapp_high_formwork_record a
        left JOIN linkapp_device d on a.device_code=d.code
        left JOIN rail_linkapp_high_formwork_item fi on a.item_id=fi.id
    <where>
        1=1
        <if test="entity.id != null ">
            and a.id = #{entity.id}
        </if>
        <if test="entity.deviceSubType != null and entity.deviceSubType != '' ">
            and a.device_sub_type = #{entity.deviceSubType}
        </if>
        <if test="entity.tenantId != null and entity.tenantId != ''">
            and a.tenant_id = #{entity.tenantId}
        </if>
        <if test="entity.startCreateH != null and entity.startCreateH != '' and entity.endCreateH != null and entity.endCreateH != ''">
            and DATE_FORMAT(a.data_time,'%Y-%m-%d %H') >= #{entity.startCreateH} and DATE_FORMAT( a.data_time,'%Y-%m-%d %H') <![CDATA[<=]]> #{entity.endCreateH}
        </if>
        <if test="entity.startCreate != null and entity.startCreate != '' and entity.endCreate != null and entity.endCreate != ''">
            and  a.data_time >= #{entity.startCreate} and  a.data_time  <![CDATA[<=]]>  #{entity.endCreate}
        </if>
        <if test="entity.deviceCode != null and entity.deviceCode != ''">
            and a.device_code= #{entity.deviceCode}
        </if>
        <if test="entity.alarmStatus != null and entity.alarmStatus != ''">
            and a.alarm_status= #{entity.alarmStatus}
        </if>
        <if test="entity.code != null and entity.code != ''">
            and a.high_code= #{entity.code}
        </if>
        <if test="entity.wordMode != null and entity.wordMode != ''">
            and a.word_mode= #{entity.wordMode}
        </if>
        <if test="entity.deviceSubType != null and entity.deviceSubType != ''">
            and a.device_sub_type= #{entity.deviceSubType}
        </if>
        <if test="entity.uniKey != null and entity.uniKey != ''">
            and (d.name like  CONCAT('%', #{entity.uniKey},'%')
            or fi.item_name like  CONCAT('%', #{entity.uniKey},'%')
            or  a.device_sub_type like  CONCAT('%', #{entity.uniKey},'%'))
        </if>
    </where>
        <if test="entity.sortField != null and entity.sortField != '' and entity.sortOrder != null and entity.sortOrder != ''">
            order by ${entity.sortField}  ${entity.sortOrder}
        </if>
    </select>

    <select id="newRecordByList" resultMap="BaseResultMap">
SELECT t.*,d.name as deviceName,hf.`name` as highName,fi.item_name as itemName,d.online_state as onlineState,d.high_device_type as highDeviceType
FROM rail_linkapp_high_formwork_record t
JOIN (
    SELECT device_code, MAX(create_time) AS max_create_time
    FROM rail_linkapp_high_formwork_record
    GROUP BY device_code
) m ON t.device_code = m.device_code AND t.create_time = m.max_create_time
left JOIN linkapp_device d on t.device_code=d.code
left JOIN rail_linkapp_high_formwork hf on t.high_code=hf.code
left JOIN rail_linkapp_high_formwork_item fi on t.item_id=fi.id
<where>
   t.tenant_id = #{entity.tenantId}
    <if test="entity.code != null and entity.code != ''">
        and t.high_code= #{entity.code}
    </if>
    <if test="entity.uniKey != null and entity.uniKey != ''">
        and (d.name like  CONCAT('%', #{entity.uniKey},'%')
        or fi.item_name like  CONCAT('%', #{entity.uniKey},'%')
        or hf.`name` like  CONCAT('%', #{entity.uniKey},'%')
        or  d.high_device_type like  CONCAT('%', #{entity.uniKey},'%'))
    </if>
</where>
        <if test="entity.sortField != null and entity.sortField != '' and entity.sortOrder != null and entity.sortOrder != ''">
            order by ${entity.sortField}  ${entity.sortOrder}
        </if>
    </select>

    <select id="selectByTestItemLine" resultMap="BaseResultMap">
SELECT a.device_sub_type,a.create_time,a.pressure,a.sedimentation,a.angle_y,d.name as deviceName,a.device_code,
a.angle_x,a.horizontal_displacement from rail_linkapp_high_formwork_record a
        left JOIN linkapp_device d on a.device_code=d.code

        <where>
        a.tenant_id = #{entity.tenantId} and a.item_id =  #{entity.itemId} and a.device_sub_type = #{entity.deviceSubType}
            <if test="entity.startCreate != null and entity.startCreate != '' and entity.endCreate != null and entity.endCreate != ''">
                and  a.data_time >= #{entity.startCreate} and  a.data_time  <![CDATA[<=]]>  #{entity.endCreate}
            </if>
        </where>
        order by a.data_time asc
    </select>


    <select id="selectBySubTypeOneDeviceName" resultType="java.util.Map">
    SELECT
    d.name AS deviceName,
    a.device_code as deviceCode
    FROM
    rail_linkapp_high_formwork_record a
    LEFT JOIN
    linkapp_device d ON a.device_code = d.code
    WHERE
    a.tenant_id = #{entity.tenantId} and a.item_id =  #{entity.itemId} and
    a.device_sub_type = #{entity.deviceSubType}
        <if test="entity.startCreate != null and entity.startCreate != '' and entity.endCreate != null and entity.endCreate != ''">
            and  a.create_time >= #{entity.startCreate} and  a.create_time  <![CDATA[<=]]>  #{entity.endCreate}
        </if>
    GROUP BY a.device_code, d.name
    </select>
    <update id="setGroupConcatMaxLen">
    SET SESSION group_concat_max_len = 10000000
  </update>
    <select id="selectByTimes" resultType="String">
        SELECT
        GROUP_CONCAT(a.data_time ORDER BY a.create_time) AS createTimes
        FROM
        rail_linkapp_high_formwork_record a
        WHERE
        a.tenant_id = #{entity.tenantId}
        AND a.item_id = #{entity.itemId}
        <if test="entity.deviceSubType != null and entity.deviceSubType != '' ">
            AND a.device_sub_type = #{entity.deviceSubType}
        </if>
        <if test="entity.startCreate != null and entity.startCreate != '' and entity.endCreate != null and entity.endCreate != ''">
            AND a.data_time >= #{entity.startCreate}   AND a.data_time <![CDATA[<=]]> #{entity.endCreate}
        </if>
        order by a.data_time asc 
    </select>

    <select id="getNowRecord" resultMap="BaseResultMap">
       select a.* from  rail_linkapp_high_formwork_record a where a.tenant_id=#{tenantId} and  a.device_code=#{code}   order by a.create_time desc LIMIT 1
    </select>

    <select id="getDaysDiffByItemId" resultType="int">
        SELECT
        CASE
        WHEN COUNT(*) <![CDATA[<=]]> 1 THEN 0
        ELSE DATEDIFF(MAX(data_time), MIN(data_time))
        END AS days_diff
        FROM rail_linkapp_high_formwork_record
        WHERE item_id = #{itemId} and tenant_id=#{tenantId}
    </select>
</mapper>