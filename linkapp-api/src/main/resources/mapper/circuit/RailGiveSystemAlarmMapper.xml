<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.circuit.mapper.RailGiveSystemAlarmMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.circuit.vo.RailGiveSystemAlarmVO">
        <id column="id" property="id"/>
        <result column="testing_item" property="testingItem"/>
        <result column="device_code" property="deviceCode"/>
        <result column="level" property="level"/>
        <result column="create_time" property="createTime"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="content" property="content"/>
        <result column="handle_id" property="handleId"/>
        <result column="handle_name" property="handleName"/>
        <result column="status" property="status"/>
        <result column="push_content" property="pushContent"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="modifier" property="modifier"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="handle_file_urls" property="handleFileUrls"/>
        <result column="handle_resp" property="handleResp"/>
        <result column="handle_rest" property="handleRest"/>
        <result column="category" property="category"/>
        <result column="device_name" property="deviceName"/>
        <result column="ref_id_" property="refId"/>
        <result column="area_ids_" property="areaIds"/>
        <result column="high_code" property="highCode"/>
        <result column="setting_handle_id" property="settingHandleId"/>
        <result column="setting_handle_name" property="settingHandleName"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id,
        case 
            when a.category = 1 then b.name_
            when a.category = 7 then c.real_name
            else a.testing_item
        end as testing_item,
        a.device_code,
        a.level,
        a.create_time,
        a.alarm_type,
        a.content,
        a.handle_id,
        a.handle_name,
        a.status,
        a.push_content,
        a.modify_time,
        a.modifier,
        a.tenant_id,
        a.handle_file_urls,
        a.handle_resp,
        a.handle_rest,
        a.category,
        a.ref_id_,
        a.area_ids_,
        a.high_code,
        d.name as device_name,
        case a.level when '1' then 'I级' when '2' then 'II级' when '3' then 'III级' else '未知' end as level_text
    </sql>
    <select id="findPage" resultMap="BaseResultMap"
            parameterType="com.easylinkin.linkappapi.circuit.dto.RailGiveSystemAlarmDTO">
        select result.*
        from (select
        <include refid="Base_Column_List"/>
        from rail_give_system_alarm a
            left join
            linkapp_device d
        on d.code = a.device_code
            left join
            rail_mechanical b
        on a.category = 1 and a.ref_id_ = b.id
            left join
            rail_linkapp_roster_personnel c
        on a.category = 7 and a.ref_id_ = c.id
        <where>
            <if test="entity.id != null">
                and a.id = #{entity.id}
            </if>
            <if test="entity.refId != null and entity.refId != ''">
                and a.ref_id_ = #{entity.refId}
            </if>
            <if test="entity.tenantId != null and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.id != null and entity.id != ''">
                and a.id = #{entity.id}
            </if>
            <if test="entity.level != null and entity.level != ''">
                and a.level = #{entity.level}
            </if>
            <if test="entity.alarmType != null and entity.alarmType != ''">
                and a.alarm_type = #{entity.alarmType}
            </if>
            <if test="entity.alarmTypes!= null and entity.alarmTypes.size() > 0">
                and a.alarm_type  in
                <foreach collection="entity.alarmTypes" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="entity.categoryList!= null and entity.categoryList.size() > 0">
                and a.category  in
                <foreach collection="entity.categoryList" item="category" open="(" separator="," close=")">
                    #{category}
                </foreach>
            </if>
            <if test="entity.status != null">
                and a.status = #{entity.status}
            </if>
            <if test="entity.startDate != null and entity.startDate != '' and entity.endDate != null and entity.endDate != ''">
                and (a.create_time <![CDATA[>=]]> #{entity.startDate}
                AND a.create_time <![CDATA[<=]]> #{entity.endDate})
            </if>
            <if test="entity.category != null  and entity.category != ''">
                and a.category = #{entity.category}
            </if>
        </where>
        group by a.id
        order by a.create_time desc
        ) as `result`
        where 1=1
        <if test="entity.uniKey != null and entity.uniKey != ''">
            and (`result`.level_text like concat('%', #{entity.uniKey}, '%') or
                `result`.device_name like concat('%', #{entity.uniKey}, '%') or
                `result`.testing_item like concat('%', #{entity.uniKey}, '%') or
                `result`.content like concat('%', #{entity.uniKey}, '%'))
        </if>
    </select>

    <select id="alarmTypeCount" resultType="com.easylinkin.linkappapi.circuit.dto.AlarmTypeCount">
        select alarm_type as alarmType,
               count(*)   as `count`
        from rail_give_system_alarm a
        where a.tenant_id = #{tenantId}
        <if test="category != null and category != ''">
            and a.category = #{category}
        </if>
        <if test="startTime != null">
            and a.create_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and a.create_time <![CDATA[<]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="refId != null and refId != ''">
            and a.ref_id_ = #{refId}
        </if>
        <if test="typeList != null and typeList.size() > 0">
            and a.alarm_type in
            <foreach collection="typeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by alarm_type
        order by `count` desc
    </select>

    <select id="isLeftAreaAlarm" resultType="boolean">
        select if(count(*) > 0, true, false) as isLeftAreaAlarm
        from rail_give_system_alarm a
        where a.status = 0
        and FIND_IN_SET(#{areaId}, a.area_ids_) > 0
        <if test="typeList != null and typeList.size() > 0">
            and a.alarm_type in
            <foreach collection="typeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="topTestingItemAlarm" resultType="com.easylinkin.linkappapi.circuit.dto.TestingItemAlarmCount">
        <!-- 机械 -->
        <if test="category != null and category == 1">
            select ref_id_ as id,
            name_ as `name`,
            count(*) as `count`
            from rail_give_system_alarm a
            left join rail_mechanical b on a.ref_id_ = b.id
        </if>
        <!-- 人员 -->
        <if test="category != null and category == 7">
            select ref_id_ as id,
            real_name as `name`,
            count(*) as `count`
            from rail_give_system_alarm a
            left join rail_linkapp_roster_personnel b on a.ref_id_ = b.id
        </if>
        where a.tenant_id = #{tenantId}
        and a.category = #{category}
        and ref_id_ is not null
        group by ref_id_
        order by `count` desc
    </select>

    <select id="selectStartHighNumber" resultType="Map">
        SELECT a.high_code,b.name,
        SUM(CASE WHEN a.status = 0 THEN 1 ELSE 0 END) AS notHandleSum,
        SUM(CASE WHEN a.status IN (1, 2) THEN 1 ELSE 0 END) AS HandleSum
        FROM rail_give_system_alarm a
        left join rail_linkapp_high_formwork b  on a.high_code = b.code
        where  a.high_code!='' and a.high_code is not null and a.category = #{category}
         and a.tenant_id = #{tenantId}
        <if test="highCodes != null and highCodes.size() > 0">
            and a.high_code in
            <foreach collection="highCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY a.high_code  ORDER BY a.high_code
    </select>

    <select id="selectStartHighList" resultMap="BaseResultMap">
         select a.*,d.name as device_name from rail_give_system_alarm a
            left join  linkapp_device d  on d.code = a.device_code
         where a.high_code!='' and a.high_code is not null and a.category = #{category}
            and a.tenant_id = #{tenantId}
           and a.high_code = #{highCode}
         order by a.create_time desc  LIMIT 30
    </select>

    <select id="getLastAlarmByRefIdListAndAreaIdAndTypeList" resultMap="BaseResultMap">
        select *
        from rail_give_system_alarm a
        left join linkapp_device b on b.code = a.device_code
        <where>
            a.status = 0
            <if test="refIdList != null and refIdList.size() > 0">
                and a.ref_id_ in
                <foreach collection="refIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="areaIdList != null and areaIdList.size() > 0">
                and
                <foreach collection="areaIdList" item="item" open="(" separator=" or " close=")">
                    FIND_IN_SET(#{item}, a.area_ids_) > 0
                </foreach>
            </if>
            <if test="typeList != null and typeList.size() > 0">
                and a.alarm_type in
                <foreach collection="typeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>