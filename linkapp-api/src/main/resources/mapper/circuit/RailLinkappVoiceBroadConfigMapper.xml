<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.circuit.mapper.RailLinkappVoiceBroadConfigMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.circuit.vo.RailLinkappVoiceBroadConfigVO">
        <id column="id" property="id"/>
        <result column="device_codes" property="deviceCodes"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="category" property="category"/>
        <result column="prefix_type" property="prefixType"/>
        <result column="broadcast_content" property="broadcastContent"/>
        <result column="number_times" property="numberTimes"/>
        <result column="enable" property="enable"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="alarmTypeText" property="alarmTypeText"/>
        <result column="deviceNames" property="deviceNames"/>
    </resultMap>
    <select id="findPage"  resultMap="BaseResultMap">
        select a.*,b.text as  alarmTypeText, GROUP_CONCAT(c.name) AS  deviceNames from rail_linkapp_voice_broad_config a
        left join rail_give_alarm_type_config b on (b.type = a.alarm_type and a.tenant_id=b.tenant_id)
        LEFT JOIN linkapp_device c  ON FIND_IN_SET(c.code, a.device_codes)
        <where>
            <if test="entity.tenantId != null  and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.id != null  and entity.id != ''">
                and a.id = #{entity.id}
            </if>
            <if test="entity.category != null   and entity.category != '' ">
                and a.category=#{entity.category}
            </if>
            <if test="entity.alarmType != null   and entity.alarmType != '' ">
                and a.alarm_type=#{entity.alarmType}
            </if>
            <if test="entity.enable != null   ">
                and a.enable=#{entity.enable}
            </if>
        </where>
        GROUP BY a.id
        order by a.create_time desc
    </select>


    <select id="findList"  resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.circuit.dto.RailLinkappVoiceBroadConfigDTO">
        select a.*,b.text as  alarmTypeText,if(c.name!=null,GROUP_CONCAT(c.name),null)  deviceNames from rail_linkapp_voice_broad_config a
        left join rail_give_alarm_type_config b on (b.type = a.alarm_type and a.tenant_id=b.tenant_id)
        LEFT JOIN linkapp_device c  ON FIND_IN_SET(c.code, a.device_codes)
        <where>
            <if test="entity.tenantId != null  and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.id != null  and entity.id != ''">
                and a.id = #{entity.id}
            </if>
            <if test="entity.category != null   and entity.category != '' ">
                and a.category=#{entity.category}
            </if>
            <if test="entity.alarmType != null   and entity.alarmType != '' ">
                and a.alarm_type=#{entity.alarmType}
            </if>
            <if test="entity.enable != null   ">
                and a.enable=#{entity.enable}
            </if>
        </where>
        GROUP BY a.id
        order by a.create_time desc
    </select>

</mapper>