<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.circuit.mapper.RailGiveAlarmTypeConfigMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.circuit.vo.RailGiveAlarmTypeConfigVO">
        <id column="id" property="id" />
        <result column="text" property="text" />
        <result column="level" property="level" />
        <result column="thresh_value" property="threshValue" />
        <result column="enabled" property="enabled" />
        <result column="tenant_id" property="tenantId" />
        <result column="push_meth" property="pushMeth" />
        <result column="push_user_ids" property="pushUserIds" />
        <result column="push_user_names" property="pushUserNames" />
        <result column="handle_user_ids" property="handleUserIds" />
        <result column="handle_user_names" property="handleUserNames" />
        <result column="push_user_phones" property="pushUserPhones" />
        <result column="fieids" property="fieids" />
        <result column="type" property="type" />
        <result column="category" property="category" />
        <result column="deduplication_time_" property="deduplicationTime" />
        <result column="create_id_" property="createId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <sql id="Base_Column_List">
        a.id,
        a.text,
        a.level,
        a.thresh_value,
        a.enabled,
        a.tenant_id,
        a.push_meth,
        a.push_user_ids,
        a.push_user_names,
        a.handle_user_ids,
        a.handle_user_names,
        a.push_user_phones,
        a.fieids,
        a.type,
        a.category,
        a.deduplication_time_,
        a.create_id_,
        a.create_time_,
        a.modify_id_,
        a.modify_time_,
        a.remark_
    </sql>
    <select id="findPage" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.circuit.dto.RailGiveAlarmTypeConfigDTO">
     select <include refid="Base_Column_List" />
     from rail_give_alarm_type_config a
        <where>
            <if test="entity.tenantId != null  and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.id != null  and entity.id != ''">
                and a.id = #{entity.id}
            </if>
            <if test="entity.text != null   and entity.text != ''">
                and a.text like concat('%', #{entity.text},'%')
            </if>
            <if test="entity.level != null   and entity.level != '' ">
                and a.level=#{entity.level}
            </if>
            <if test="entity.category != null   and entity.category != '' ">
                and a.category=#{entity.category}
            </if>
        </where>
        order by a.type, a.level
    </select>

    <select id="findListByCondition" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.circuit.dto.RailGiveAlarmTypeConfigDTO">
        select <include refid="Base_Column_List" />
        from rail_give_alarm_type_config a
        <where>
            <if test="entity.tenantId != null  and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.id != null  and entity.id != ''">
                and a.id = #{entity.id}
            </if>
            <if test="entity.text != null   and entity.text != ''">
                and a.text like concat('%', #{entity.text},'%')
            </if>
            <if test="entity.level != null and entity.level != '' ">
                and a.level like concat('%', #{entity.level},'%')
            </if>
            <if test="entity.category != null and entity.category != '' ">
                and a.category=#{entity.category}
            </if>
        </where>
        order by a.type, a.level
    </select>

</mapper>
