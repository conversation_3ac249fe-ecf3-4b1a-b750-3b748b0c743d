<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.circuit.mapper.RailLinkappAiHostRecordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.circuit.vo.RailLinkappAiHostRecordVO">
        <id column="id" property="id"/>
        <result column="device_code" property="deviceCode"/>
        <result column="ai_rule_type" property="aiRuleType"/>
        <result column="image" property="image"/>
        <result column="ipc_sn" property="ipcSn"/>
        <result column="ipc_address" property="ipcAddress"/>
        <result column="channel_status" property="channelStatus"/>
        <result column="ai_event_type" property="aiEventType"/>
        <result column="has_result" property="hasResult"/>
        <result column="behaviour_json" property="behaviourJson"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="channel_name" property="channelName"/>
    </resultMap>
    <select id="getList" resultMap="BaseResultMap">
        select a.* from rail_linkapp_ai_host_record a
        <where>
         1=1
            <if test="entity.tenantId != null and entity.tenantId != ''">
                and a.tenant_id = #{entity.tenantId}
            </if>
            <if test="entity.startCreateH != null and entity.startCreateH != '' and entity.endCreateH != null and entity.endCreateH != ''">
                and DATE_FORMAT(a.create_time,'%Y-%m-%d %H') >= #{entity.startCreateH} and DATE_FORMAT( a.create_time,'%Y-%m-%d %H') <![CDATA[<=]]> #{entity.endCreateH}
            </if>
            <if test="entity.startCreate != null and entity.startCreate != '' and entity.endCreate != null and entity.endCreate != ''">
                and DATE_FORMAT(a.create_time,'%Y-%m-%d') >= #{entity.startCreate} and DATE_FORMAT( a.create_time,'%Y-%m-%d') <![CDATA[<=]]> #{entity.endCreate}
            </if>
            <if test="entity.aiRuleType != null and entity.aiRuleType != ''">
                and a.ai_rule_type= #{entity.aiRuleType}
            </if>
            <if test="entity.aiRuleTypes != null and entity.aiRuleTypes != ''">
                and find_in_set(a.ai_rule_type,#{entity.aiRuleTypes} )
            </if>
            <if test="entity.deviceCode != null and entity.deviceCode != ''">
                and a.device_code= #{entity.deviceCode}
            </if>
            <if test="entity.hasResult != null and entity.hasResult != ''">
                and a.has_result= #{entity.hasResult}
            </if>
        </where>
        order by a.create_time desc
    </select>

    <!-- 统计当日数量 -->
    <select id="countTodayByTenantId" resultType="java.lang.Long">
        SELECT COUNT(1) FROM rail_linkapp_ai_host_record a
        WHERE a.tenant_id = #{tenantId}
        AND DATE(a.create_time) = CURDATE()
    </select>

    <!-- 统计近7日数量 -->
    <select id="countLast7DaysByTenantId" resultType="java.lang.Long">
        SELECT COUNT(1) FROM rail_linkapp_ai_host_record a
        WHERE a.tenant_id = #{tenantId}
        AND a.create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    </select>


    <!-- 统计近7日数量 -->
    <select id="countLastNumber7DaysByTenantId" resultType="com.easylinkin.linkappapi.circuit.vo.RailLinkappAiHostRecordStatisticsVO">
        SELECT   dd.device_code as devCode,
        COUNT(dd.id) AS dataCount  FROM rail_linkapp_ai_host_record dd
        WHERE dd.tenant_id = #{tenantId}  and  dd.device_code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND dd.create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) GROUP BY
        dd.device_code;
    </select>
    <!-- 统计近30日数量 -->
    <select id="countLastNumber30DaysByTenantId" resultType="com.easylinkin.linkappapi.circuit.vo.RailLinkappAiHostRecordStatisticsVO">
        SELECT   dd.device_code as devCode,
        COUNT(dd.id) AS dataCount  FROM rail_linkapp_ai_host_record dd
        WHERE dd.tenant_id = #{tenantId}  and  dd.device_code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
          AND dd.create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) GROUP BY
        dd.device_code;
    </select>


    <select id="countName7DaysByTenantId" resultType="com.easylinkin.linkappapi.circuit.vo.RailLinkappAiHostRecordStatisticsVO">
        SELECT   dd.channel_name as devCode,
        COUNT(dd.id) AS dataCount  FROM rail_linkapp_ai_host_record dd
        WHERE dd.tenant_id = #{tenantId}  and  dd.channel_name in
        <foreach item="name" collection="names" open="(" separator="," close=")">
            #{name}
        </foreach>
        AND dd.create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) GROUP BY
        dd.channel_name;
    </select>

    <!-- 统计近30日数量 -->
    <select id="countName30DaysByTenantId" resultType="com.easylinkin.linkappapi.circuit.vo.RailLinkappAiHostRecordStatisticsVO">
        SELECT   dd.channel_name as devCode,
        COUNT(dd.id) AS dataCount  FROM rail_linkapp_ai_host_record dd
        WHERE dd.tenant_id = #{tenantId}  and  dd.channel_name in
        <foreach item="name" collection="names" open="(" separator="," close=")">
            #{name}
        </foreach>
        AND dd.create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) GROUP BY
        dd.channel_name;
    </select>




    <select id="codeByNewRecords" resultType="com.easylinkin.linkappapi.circuit.vo.RailLinkappAiHostRecordVO">
        SELECT DISTINCT  dr.*
        FROM rail_linkapp_ai_host_record dr
        INNER JOIN (
        SELECT device_code, MAX(create_time) AS max_time
        FROM rail_linkapp_ai_host_record
        WHERE  tenant_id = #{tenantId} and device_code IN
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        GROUP BY device_code
        ) AS latest
        ON dr.device_code = latest.device_code
        AND dr.create_time = latest.max_time
    </select>

    <select id="getNowByOne" resultMap="BaseResultMap">
        SELECT a.* FROM rail_linkapp_ai_host_record a
        WHERE a.tenant_id=#{tenantId}
        <if test="types != null and types.size() > 0">
            and a.ai_rule_type IN
            <foreach item="type" collection="types" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        ORDER BY a.create_time DESC LIMIT 1
    </select>

    <select id="getNowHourByOne" resultMap="BaseResultMap">
        SELECT a.* FROM rail_linkapp_ai_host_record a
        WHERE a.create_time >= NOW() - INTERVAL 1 HOUR and a.tenant_id=#{tenantId}
        <if test="types != null and types.size() > 0">
            and a.ai_rule_type IN
            <foreach item="type" collection="types" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        ORDER BY a.create_time DESC LIMIT 1
    </select>

    <select id="getNowDayByOne" resultType="Integer">
        SELECT COUNT(1)   FROM rail_linkapp_ai_host_record a
        WHERE  a.create_time >= CURDATE()
        AND a.create_time <![CDATA[<]]> CURDATE() + INTERVAL 1 DAY and  a.tenant_id=#{tenantId}
        <if test="types != null and types.size() > 0">
            and a.ai_rule_type IN
            <foreach item="type" collection="types" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        ORDER BY a.create_time DESC
    </select>

    <select id="getNowDayByAll" resultType="com.easylinkin.linkappapi.circuit.vo.RailLinkappAiHostRecordStatisticsVO" >
        select device_code as devCode,channel_name as chaName,
        count(*)   as `dataCount`
        from rail_linkapp_ai_host_record a
        where  a.create_time >= CURDATE()
        AND a.create_time  <![CDATA[<]]>  CURDATE() + INTERVAL 1 DAY
        and a.tenant_id = #{tenantId}
        group by device_code
    </select>
</mapper>