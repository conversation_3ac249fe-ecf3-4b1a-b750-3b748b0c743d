<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.classroom.mapper.GfdRoleRefAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.classroom.entity.GfdRoleRefArea">
        <result column="role_id" property="roleId"/>
        <result column="roleName" property="roleName"/>
        <result column="area_id" property="areaId"/>
        <result column="areaPath" property="areaPath"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="getAreasByRoles" resultType="com.easylinkin.linkappapi.space.entity.LinkappArea">
        select la.*
        from linkapp_area la
        inner join gfd_role_ref_area grra on la.id = grra.area_id
        where
        <if test="roleIds != null">
            grra.role_id in
            <foreach collection="roleIds" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getRoleRefAreaPage" resultType="com.easylinkin.linkappapi.classroom.entity.GfdRoleRefArea">
        select
            grra.*,
            replace(la.area_path, ':', '/') areaPath,
            lr.name_ roleName
        from
            gfd_role_ref_area grra
            inner join linkapp_area la on la.id = grra.area_id
            inner join linkapp_role lr on grra.role_id=lr.role_id_
        where
            grra.tenant_id=#{item.tenantId}
            <if test="item.roleId != null and item.roleId != '' ">
                and grra.role_id=#{item.roleId}
            </if>
            <if test="item.areaId != null and item.areaId != '' ">
                and grra.area_id=#{item.areaId}
            </if>
            <if test="item.areaPath != null and item.areaPath != '' ">
                AND (la.area_path = #{item.areaPath} or la.area_path like concat(#{item.areaPath},':%'))
            </if>

        order by grra.modify_time desc
    </select>

</mapper>
