<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.bim.mapper.BimServerConfigMapper">


  <insert id="add">
    insert into bim_server_config (id, app_key, app_secret, file_id, tenant_id, create_time, creator, creator_id, modify_id, modify_time, modifier)
    values (#{id}, #{appKey}, #{appSecret}, #{fileId}, #{tenantId}, #{createTime}, #{creator}, #{creatorId}, #{modifyId}, #{modifyTime}, #{modifier})
  </insert>
</mapper>
