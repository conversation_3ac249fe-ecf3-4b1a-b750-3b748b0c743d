<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.eveningmeeting.mapper.RailEveningMeetingUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.eveningmeeting.entity.RailEveningMeetingUser">
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="meeting_id_" property="meetingId"/>
        <result column="user_id_" property="userId"/>
        <result column="user_type_" property="userType"/>
        <result column="signature_image_url_" property="signatureImageUrl"/>
        <result column="signature_time_" property="signatureTime"/>
        <result column="status_" property="status"/>
        <result column="is_deleted_" property="isDeleted"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.easylinkin.linkappapi.eveningmeeting.vo.RailEveningMeetingUserVo" extends="BaseResultMap">
    </resultMap>

    <select id="findByMeetingId" resultMap="BaseResultMapVo">
        SELECT t.*
        FROM rail_evening_meeting_user t
        WHERE t.meeting_id_ = #{meetingId}
          AND t.is_deleted_ = 0
        ORDER BY t.user_type_, t.create_time_
    </select>

    <select id="findByMeetingIdAndUserType" resultMap="BaseResultMapVo">
        SELECT t.*,
               u.phone user_phone_
        FROM rail_evening_meeting_user t
                 LEFT JOIN linkapp_user u ON t.user_id_ = u.id
        WHERE t.meeting_id_ = #{meetingId}
          AND t.user_type_ = #{userType}
          AND t.is_deleted_ = 0
        ORDER BY t.create_time_
    </select>
</mapper>
