<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.eveningmeeting.mapper.RailEveningMeetingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.eveningmeeting.entity.RailEveningMeeting">
        <id column="id_" property="id"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="grid_ids_" property="gridIds"/>
        <result column="meeting_time_" property="meetingTime"/>
        <result column="speaker_" property="speaker"/>
        <result column="attendee_count_" property="attendeeCount"/>
        <result column="location_" property="location"/>
        <result column="work_completion_" property="workCompletion"/>
        <result column="issues_" property="issues"/>
        <result column="risk_judgment_" property="riskJudgment"/>
        <result column="next_day_plan_" property="nextDayPlan"/>
        <result column="media_files_url_" property="mediaFilesUrl"/>
        <result column="status_" property="status"/>
        <result column="is_deleted_" property="isDeleted"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.easylinkin.linkappapi.eveningmeeting.vo.RailEveningMeetingVo" extends="BaseResultMap">
        <result column="collaborator_id_" property="collaboratorId"/>
    </resultMap>

    <!-- 日期网格点映射结果 -->
    <resultMap id="DateGridResultMap" type="com.easylinkin.linkappapi.eveningmeeting.vo.RailEveningMeetingDateGridVo">
        <result column="date" property="date"/>
        <result column="grid_id" property="gridId"/>
    </resultMap>

    <!-- 网格点切换映射结果 -->
    <resultMap id="GridSwitchResultMap" type="com.easylinkin.linkappapi.eveningmeeting.vo.RailEveningMeetingGridSwitchVo">
        <id column="id" property="id"/>
        <result column="enginee_type" property="engineeType"/>
        <result column="number" property="number"/>
        <result column="grid_type" property="gridType"/>
        <result column="grid_name" property="gridName"/>
        <result column="mileage_range" property="mileageRange"/>
        <result column="safety_sup_id" property="safetySupId"/>
        <result column="safety_sup_name" property="safetySupName"/>
        <result column="grid_sec_id" property="gridSecId"/>
        <result column="grid_sec_name" property="gridSecName"/>
        <result column="grid_foreman_id" property="gridForemanId"/>
        <result column="grid_foreman_name" property="gridForemanName"/>
        <result column="grid_siteman_id" property="gridSitemanId"/>
        <result column="grid_siteman_name" property="gridSitemanName"/>
        <result column="staff_number" property="staffNumber"/>
        <result column="construction_team" property="constructionTeam"/>
        <result column="is_sole_duty" property="isSoleDuty"/>
        <result column="hold_cert" property="holdCert"/>
        <result column="is_formal_employee" property="isFormalEmployee"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modify_id" property="modifyId"/>
        <result column="has_data" property="hasData"/>
    </resultMap>

    <select id="queryListByPage" resultMap="BaseResultMapVo">
        SELECT t.*,
               collaborator.user_id_ collaborator_id_
        FROM rail_evening_meeting t
                 LEFT JOIN rail_evening_meeting_user collaborator ON t.id_ = collaborator.meeting_id_ AND collaborator.user_type_ = 1
        WHERE t.tenant_id_ = #{queryVo.tenantId}
        <if test="queryVo.gridId != null">
            AND FIND_IN_SET(#{queryVo.gridId}, t.grid_ids_) > 0
        </if>
        <if test="queryVo.webGridId != null">
            AND CAST(SUBSTRING_INDEX(t.grid_ids_, ',', 1) AS UNSIGNED) = #{queryVo.webGridId}
        </if>
        <if test="queryVo.status != null">
            AND t.status_ = #{queryVo.status}
        </if>
        <if test="queryVo.startTime != null and queryVo.startTime != ''">
            AND t.meeting_time_ &gt;= #{queryVo.startTime}
        </if>
        <if test="queryVo.endTime != null and queryVo.endTime != ''">
            AND t.meeting_time_ &lt;= #{queryVo.endTime}
        </if>
        ORDER BY t.meeting_time_ DESC, t.modify_time_ DESC
    </select>

    <select id="findById" resultMap="BaseResultMapVo">
        SELECT t.*,
               collaborator.user_id_ collaborator_id_
        FROM rail_evening_meeting t
                 LEFT JOIN rail_evening_meeting_user collaborator ON t.id_ = collaborator.meeting_id_ AND collaborator.user_type_ = 1
        WHERE t.id_ = #{id}
    </select>

    <!-- 获取晚交班会月度数据：按天、按网格统计（如果有多个网格点，取第一个） -->
    <select id="getCalendarData" resultMap="DateGridResultMap">
        SELECT
            DATE_FORMAT(t.meeting_time_, '%Y-%m-%d') as date,
            CAST(SUBSTRING_INDEX(t.grid_ids_, ',', 1) AS UNSIGNED) as grid_id
        FROM rail_evening_meeting t
        WHERE 1=1
        <if test="queryVo.tenantId != null and queryVo.tenantId != ''">
            AND t.tenant_id_ = #{queryVo.tenantId}
        </if>
        <if test="queryVo.yearMonth != null and queryVo.yearMonth != ''">
            AND t.meeting_time_ LIKE CONCAT(#{queryVo.yearMonth}, '%')
        </if>
        AND t.is_deleted_ = 0
        GROUP BY DATE_FORMAT(t.meeting_time_, '%Y-%m-%d'), CAST(SUBSTRING_INDEX(t.grid_ids_, ',', 1) AS UNSIGNED)
        ORDER BY date, grid_id
    </select>

    <!-- 获取网格点切换数据：显示所有网格点及其数据状态（如果有多个网格点，算第一个网格点的数据） -->
    <select id="getGridSwitchData" resultMap="GridSwitchResultMap">
        SELECT
            g.*,
            CASE
                WHEN COUNT(em.id_) > 0 THEN 1
                ELSE 0
            END as has_data
        FROM rail_linkapp_grid_management_info g
        LEFT JOIN rail_evening_meeting em ON g.id = CAST(SUBSTRING_INDEX(em.grid_ids_, ',', 1) AS UNSIGNED)
            <if test="tenantId != null and tenantId != ''">
                AND em.tenant_id_ = #{tenantId}
            </if>
            <if test="startTime != null">
                AND em.meeting_time_ &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND em.meeting_time_ &lt;= #{endTime}
            </if>
            AND em.is_deleted_ = 0
        WHERE 1=1
        <if test="tenantId != null and tenantId != ''">
            AND g.tenant_id = #{tenantId}
        </if>
        GROUP BY g.id
        ORDER BY g.number ASC
    </select>
</mapper>
