<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.knowledgebase.mapper.KnowledgeBaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.knowledgebase.entity.KnowledgeBase">
        <id column="id_" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="name_" property="name" />
        <result column="types_" property="types" />
        <result column="content_" property="content" />
        <result column="attachments_" property="attachments" />
        <result column="attachments_zip_url" property="attachmentsZipUrl" />
        <result column="permission_scope_" property="permissionScope" />
        <result column="permission_users_" property="permissionUsers" />
        <result column="creator_" property="creator" />
        <result column="create_time_" property="createTime" />
        <result column="modifier_" property="modifier" />
        <result column="modify_time_" property="modifyTime" />
    </resultMap>

  <select id="getKnowledgeBasePage" resultMap="BaseResultMap">
    SELECT
    b.*
    FROM
    linkapp_knowledge_base b
    LEFT JOIN linkapp_knowledge_base_ref_type ref ON b.id_ = ref.knowledge_id_
    INNER JOIN linkapp_knowledge_type t ON t.id_ = ref.type_id_
    <where>
      <if test="base.name != null  and base.name != ''">
        AND b.name_ LIKE CONCAT('%', #{base.name}, '%')
      </if>
      <if test="base.type != null">
        AND ref.type_id_ = #{base.type}
      </if>
      <if test="base.tenantId != null">
        AND b.tenant_id = #{base.tenantId}
        AND t.tenant_id = #{base.tenantId}
      </if>
      AND (
      -- 访问权限：0全体 1指定用户 2仅创建人
      -- 0全体
      b.permission_scope_ = 0
      <if test="base.userId != null">
        -- 1指定用户或者自己创建
        OR b.id_ IN ( SELECT knowledge_id_ FROM linkapp_knowledge_base_ref_user WHERE user_id_ =
        #{base.userId} )
        OR (b.permission_scope_ = 1 AND b.creator_id_ = #{base.userId})
        -- 2仅创建人
        OR (b.permission_scope_ = 2 AND b.creator_id_ = #{base.userId})
      </if>
      )
    </where>
    GROUP BY b.id_
    ORDER BY
    b.modify_time_ DESC,
    b.create_time_ DESC
  </select>

  <select id="findByTypeIdAndTenantId" resultMap="BaseResultMap">
    SELECT
    b.*
    FROM
    linkapp_knowledge_base b
    LEFT JOIN linkapp_knowledge_base_ref_type ref ON b.id_ = ref.knowledge_id_
    WHERE ref.type_id_ = #{typeId}
    AND b.tenant_id = #{tenantId}
  </select>

  <select id="findByTypeIdAndNameAndTenantId" resultMap="BaseResultMap">
    SELECT
    b.*
    FROM
    linkapp_knowledge_base b
    LEFT JOIN linkapp_knowledge_base_ref_type ref ON b.id_ = ref.knowledge_id_
    WHERE ref.type_id_ = #{typeId}
    AND b.name_ = #{name}
    AND b.tenant_id = #{tenantId}
  </select>

  <select id="countKnowledgeByTypeIdAndUserIdAndTenantId" resultType="java.lang.Integer">
    SELECT
    IFNULL( COUNT(*),0)
    FROM(
      SELECT
      b.*
      FROM
      linkapp_knowledge_base b
      LEFT JOIN linkapp_knowledge_base_ref_type ref ON b.id_ = ref.knowledge_id_
      INNER JOIN linkapp_knowledge_type t ON t.id_ = ref.type_id_
      WHERE ref.type_id_ = #{typeId}
      <if test="tenantId != null">
        AND b.tenant_id = #{tenantId}
        AND t.tenant_id = #{tenantId}
      </if>
      AND (
      b.permission_scope_ = 0
      <if test="userId != null">
        OR b.id_ IN ( SELECT knowledge_id_ FROM linkapp_knowledge_base_ref_user WHERE user_id_ =
        #{userId} )
        OR (b.permission_scope_ = 2 AND b.creator_id_ = #{userId})
      </if>
      )
      GROUP BY b.id_
    ) a
  </select>
</mapper>
