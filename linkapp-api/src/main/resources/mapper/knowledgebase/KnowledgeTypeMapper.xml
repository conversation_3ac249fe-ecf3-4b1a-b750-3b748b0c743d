<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.knowledgebase.mapper.KnowledgeTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.knowledgebase.entity.KnowledgeType">
        <id column="id_" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="parent_id_" property="parentId" />
        <result column="name_" property="name" />
        <result column="order_" property="order" />
        <result column="create_time_" property="createTime" />
        <result column="creator_" property="creator" />
        <result column="modifier_" property="modifier" />
        <result column="modify_time_" property="modifyTime" />
    </resultMap>

  <select id="listAll" resultType="com.easylinkin.linkappapi.knowledgebase.vo.KnowledgeTypeVO">
    select id_,name_,parent_id_ from linkapp_knowledge_type
    where
    tenant_id = #{type.tenantId}
    order by create_time_
  </select>

  <select id="findTypeByKnowledgeId" resultMap="BaseResultMap">
    select * from linkapp_knowledge_type where id_ in (select type_id_ from linkapp_knowledge_base_ref_type where knowledge_id_ = #{knowledgeId})
  </select>

  <select id="countNameByCondition" resultType="int">
    select count(*) from linkapp_knowledge_type
    where
    tenant_id = #{tenantId}
    and name_ = #{name}
    <if test="parentId != null">
      and parent_id_ = #{parentId}
    </if>
    <if test="parentId == null">
      and parent_id_ is null
    </if>
    <if test="id != null">
      and id_ != #{id}
    </if>
  </select>

  <select id="findAllNodeByTenantId" resultType="com.easylinkin.linkappapi.knowledgebase.util.Node">
    SELECT
      id_ AS id,
      name_ AS value,
      parent_id_ AS pId
    FROM
      linkapp_knowledge_type
    WHERE
      tenant_id = #{tenantId}
  </select>

</mapper>
