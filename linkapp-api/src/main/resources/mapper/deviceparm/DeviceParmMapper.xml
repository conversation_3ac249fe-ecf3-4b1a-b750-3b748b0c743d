<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.deviceparm.mapper.DeviceParmMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.deviceparm.entity.DeviceParm">
    <id column="id" property="id"/>
    <result column="device_service_id" property="deviceServiceId"/>
    <result column="identifier" property="identifier"/>
    <result column="name" property="name"/>
    <result column="value" property="value"/>
    <result column="unit" property="unit"/>
    <result column="specs" property="specs"/>
    <result column="sort_no" property="sortNo"/>
    <result column="description" property="description"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="parent_id" property="parentId"/>
  </resultMap>

  <resultMap id="selectDeviceParmsMap" type="com.easylinkin.linkappapi.deviceparm.entity.DeviceParm">
    <id column="id" property="id"/>
    <result column="device_service_id" property="deviceServiceId"/>
    <result column="identifier" property="identifier"/>
    <result column="value" property="value"/>
    <result column="unit" property="unit"/>
    <result column="specs" property="specs"/>
    <result column="parent_id" property="parentId"/>
    <result column="sort_no" property="sortNo"/>
    <result column="name" property="name"/>
  </resultMap>


  <select id="selectDeviceParmList" resultMap="selectDeviceParmsMap">
    select
    dp.*
    from linkapp_device_parm dp
    <where>
      <if test="id != null and id != ''">
        and dp.id = #{id}
      </if>
      <if test="deviceServiceId != null and deviceServiceId != ''">
        and dp.device_service_id = #{deviceServiceId}
      </if>
      <if test="identifier != null and identifier != ''">
        and dp.identifier = #{identifier}
      </if>
      <if test="parentId != null and parentId != ''">
        and dp.parent_id = #{parentId}
      </if>
      <if test="name != null and name != ''">
        and dp.name = #{name}
      </if>
      <if test="unit != null and unit != ''">
        and dp.unit = #{unit}
      </if>
    </where>
    order by sort_no asc
  </select>

</mapper>
