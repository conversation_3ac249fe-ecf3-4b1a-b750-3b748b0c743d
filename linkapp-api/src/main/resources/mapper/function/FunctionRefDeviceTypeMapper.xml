<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.function.mapper.LinkappFunctionRefDeviceTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.function.entity.LinkappFunctionRefDeviceType">
        <id column="id_" property="id"/>
        <result column="function_id" property="functionId"/>
        <result column="ref_device_type" property="refDeviceTypeList" typeHandler="com.easylinkin.linkappapi.function.typehandler.RefDeviceTypeHandler"/>
    </resultMap>
    <select id="findByfunctionId" resultMap="BaseResultMap">
        select * from linkapp_function_ref_device_type where function_id = #{functionId}
    </select>


</mapper>
