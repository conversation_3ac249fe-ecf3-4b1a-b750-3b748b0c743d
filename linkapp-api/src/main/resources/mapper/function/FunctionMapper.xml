<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.function.mapper.LinkappFunctionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.function.entity.LinkappFunction">
        <id column="id" property="id"/>
        <result column="identifier" property="identifier"/>
        <result column="function_name" property="functionName"/>
        <result column="description" property="description"/>
        <result column="is_must" property="isMust"/>
        <result column="type" property="type"/>
        <result column="sort_no" property="sortNo"/>
        <result column="img_path" property="imgPath"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier" property="modifier"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <select id="getLinkappFunction" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.function.entity.LinkappFunction">
		SELECT
       		*
        FROM
            linkapp_function a
        <where>
        	<if test="id != null and id != ''">
	        	and a.id = #{id}
	        </if>
	        <if test="functionName != null and functionName != ''">
	        	and a.function_name like  CONCAT('%',#{functionName},'%')
	        </if>
	        <if test="isMust != null and isMust != ''">
	        	and a.is_must = #{isMust}
	        </if>
        </where>
        ORDER BY a.modify_time DESC,a.create_time desc
    </select>


    <select id="getLinkappFunctions" resultMap="BaseResultMap" parameterType="com.easylinkin.linkappapi.function.entity.LinkappFunction">
		SELECT
       		a.*
        FROM
            linkapp_function a
        <if test="function.tenantId != null and function.tenantId != ''">
	        left join linkapp_tenant_ref_function b on  a.id = b.function_id        
	    </if>    
        <where>
	        <if test="function.functionName != null and function.functionName != ''">
	        	and a.function_name like  CONCAT('%',#{function.functionName},'%')
	        </if>
	        <if test="function.isMust != null and function.isMust != ''">
	        	and a.is_must = #{function.isMust}
	        </if>
	        <if test="function.tenantId != null and function.tenantId != ''">
	        	and b.tenant_id = #{function.tenantId}
	        </if>
        </where>
        ORDER BY a.modify_time DESC,a.create_time desc
    </select>

    <resultMap id="linkappPrivilegeMap" type="com.easylinkin.linkappapi.security.entity.LinkappPrivilege">
        <id column="id_" property="id"/>
        <result column="parent_id_" property="parentId"/>
        <result column="name_" property="name"/>
        <result column="privilege_code_" property="code"/>
        <result column="description_" property="description"/>
        <result column="level_" property="level"/>
        <result column="target" property="target"/>
        <result column="search_code_" property="searchCode"/>
        <result column="seq_" property="sort"/>
        <result column="type_" property="type"/>
        <result column="url_" property="url"/>
        <result column="is_log_" property="isLog"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time_" property="createTime"/>
        <result column="creator_" property="creator"/>
        <result column="modifier_" property="modifier"/>
        <result column="modify_time_" property="modifyTime"/>
    </resultMap>

	<select id="selectPrivilegeByFunction" resultMap="linkappPrivilegeMap"
		parameterType="java.lang.String">
		SELECT a.* FROM linkapp_privilege a
		LEFT JOIN linkapp_function_ref_privilege b ON a.id_ = b.privilege_id
		LEFT JOIN linkapp_function c ON b.function_id = c.id
		<where>
			<if test="id != null">
				and c.id = #{id}
			</if>
		</where>
		order by a.seq_
	</select>

	
	<delete id="deleteFunctions2Tenant">
		DELETE FROM linkapp_tenant_ref_function WHERE tenant_id = #{id}
	</delete>

    <insert id="insertFunctions2Tenant">
        insert into linkapp_tenant_ref_function
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="functionId != null">
                function_id,
            </if>
            <if test="tenantId != null">
                tenant_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="functionId != null">
                #{functionId},
            </if>
            <if test="tenantId != null">
                #{tenantId}
            </if>
        </trim>
    </insert>
	
	
	
	

<!--     <select id="selectApplicationPrivilegeByUser" resultMap="linkappPrivilegeMap" parameterType="java.lang.String"> -->
<!--         SELECT a.* FROM linkapp_privilege a -->
<!--         LEFT JOIN linkapp_application_ref_privilege b ON a.id_ = b.privilege_id -->
<!--         LEFT JOIN linkapp_application c ON b.application_id = c.id -->
<!--         LEFT JOIN linkapp_tenant d ON c.app_key = d.app_key -->
<!--         LEFT JOIN linkapp_user e ON d.id = e.tenant_id -->
<!--         <where> -->
<!--             <if test="id != null"> -->
<!--                 and e.id = #{id} -->
<!--             </if> -->
<!--         </where> -->
<!--         order by a.seq_ -->
<!--     </select> -->


    <delete id="deleteFunction2Privileges">
		DELETE FROM linkapp_function_ref_privilege WHERE function_id = #{id}
	</delete>

    <insert id="insertFunction2Privileges">
        insert into linkapp_function_ref_privilege
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="functionId != null">
                function_id,
            </if>
            <if test="privilegeId != null">
                privilege_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="functionId != null">
                #{functionId},
            </if>
            <if test="privilegeId != null">
                #{privilegeId}
            </if>
        </trim>
    </insert>
	
<!-- 	<delete id="deleteTenant2Privileges"> -->
<!-- 		DELETE FROM linkapp_tenant_ref_privilege WHERE privilege_id = #{privilegeId} and tenant_id = #{tenantId} -->
<!-- 	</delete> -->
	
<!-- 	<delete id="deleteRole2Privileges"> -->
<!-- 		DELETE FROM linkapp_role_ref_privilege WHERE privilege_id_ = #{privilegeId} and tenant_id = #{tenantId} -->
<!-- 	</delete> -->
	
	<!-- 通用查询映射结果 -->
    <resultMap id="TenantResultMap" type="com.easylinkin.linkappapi.tenant.entity.LinkappTenant">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_key" property="appKey" />
        <result column="app_type" property="appType" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
        <result column="modifier" property="modifier" />
        <result column="modify_time" property="modifyTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="personality_id" property="personalityId" />
    </resultMap>

	<select id="selectTenantByFunction" resultMap="TenantResultMap" parameterType="java.lang.String">
        SELECT a.* FROM linkapp_tenant a 
           LEFT JOIN `linkapp_tenant_ref_function` b ON a.`id` = b.`tenant_id`
        <where>
            <if test="functionId != null">
                b.`function_id` = #{functionId}
            </if>
        </where>
    </select>

    <select id="getFunacionDeviceTypeByIdentifier" resultType="java.util.Map">
        select
         lfdt.ref_device_type deviceType from (
         select lf.id from linkapp_function lf where lf.id in (
            select ltf.function_id from linkapp_tenant_ref_function ltf
            <where>
                <if test="tenantId != null">
                    and ltf.tenant_id = #{tenantId}
                </if>
            </where>
         )
        <if test="identifier != null">
            and lf.identifier = #{identifier}
        </if>
         ) lf
        left join linkapp_function_ref_device_type lfdt on lf.id=lfdt.function_id
    </select>
</mapper>
