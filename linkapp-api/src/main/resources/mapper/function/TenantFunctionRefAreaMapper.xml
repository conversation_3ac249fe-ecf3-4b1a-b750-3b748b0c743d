<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.function.mapper.TenantFunctionRefAreaMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.function.entity.TenantFunctionRefArea">
    <result column="tenant_id" property="tenantId"/>
    <result column="function_identifier" property="functionIdentifier"/>
    <result column="area_id" property="areaId"/>
    <result column="area_path" property="areaPath"/>
  </resultMap>


  <insert id="batchInsert" parameterType="com.easylinkin.linkappapi.function.entity.TenantFunctionRefArea">
    insert into tenant_function_ref_area
    (tenant_id, function_identifier, area_id, area_path)
    values
    <foreach collection="tfraList" item="item" separator=",">
      (#{item.tenantId}, #{item.functionIdentifier}, #{item.areaId}, #{item.areaPath})
    </foreach>
  </insert>

</mapper>
