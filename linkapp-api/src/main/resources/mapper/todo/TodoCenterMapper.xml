<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.todo.mapper.TodoCenterMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.todo.entity.TodoCenter">
        <id property="id" column="id_" />
        <result property="tenantId" column="tenant_id_" />
        <result property="ownerId" column="owner_id_" />
        <result property="type" column="type_" />
        <result property="buttonType" column="button_type_" />
        <result property="status" column="status_" />
        <result property="title" column="title_" />
        <result property="content" column="content_" />
        <result property="linkId" column="link_id" />
        <result property="linkSeq" column="link_seq_" />
        <result property="createTime" column="create_time" />
        <result property="creator" column="creator" />
        <result property="modifier" column="modifier" />
        <result property="modifyTime" column="modify_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id_, tenant_id_, owner_id_, type_, button_type_, status_, title_, content_, link_id, link_seq_, create_time, creator, modifier, modify_time
    </sql>

    <select id="queryPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rail_todo_center
        WHERE tenant_id_ = #{customQueryParams.tenantId}
        AND owner_id_ = #{customQueryParams.ownerId}
        <if test="customQueryParams.type!= null">
            AND type_ = #{customQueryParams.type}
        </if>
        <if test="customQueryParams.status != null">
            AND status_ = #{customQueryParams.status}
        </if>
        ORDER BY create_time DESC, type_ ASC
    </select>

    <select id="getMaxBatch" resultType="java.lang.Integer">
        SELECT MAX(link_seq_)
        FROM rail_todo_center
        WHERE link_id = #{linkId}
        AND type_ = #{type}
    </select>

    <select id="getMinBatch" resultType="java.lang.Integer">
        SELECT MIN(link_seq_)
        FROM rail_todo_center
        WHERE link_id = #{linkId}
        AND status = 0
        AND type_ = #{type}
    </select>

    <select id="statisticsByType" resultType="com.easylinkin.linkappapi.todo.dto.TodoTypeStatisticsDTO">
        SELECT 
            type_ as type,
            COUNT(*) as totalCount,
            SUM(CASE WHEN status_ = 0 THEN 1 ELSE 0 END) as pendingCount,
            SUM(CASE WHEN status_ = 1 THEN 1 ELSE 0 END) as completedCount,
            ROUND(SUM(CASE WHEN status_ = 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as pendingPercentage,
            ROUND(SUM(CASE WHEN status_ = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as completedPercentage
        FROM rail_todo_center
        WHERE tenant_id_ = #{tenantId}
        AND owner_id_ = #{ownerId}
        GROUP BY type_
        ORDER BY type_ ASC
    </select>
</mapper>