<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.calculaterule.mapper.CalculateConfigMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.calculaterule.entity.CalculateConfig">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="code" property="code"/>
    <result column="space_id" property="spaceId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="type" property="type"/>
    <result column="status" property="status"/>
    <result column="calculate_rule_id" property="calculateRuleId"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
  </resultMap>

  <resultMap id="getCalculateConfigMap" type="com.easylinkin.linkappapi.calculaterule.entity.CalculateConfig">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="code" property="code"/>
    <result column="space_id" property="spaceId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="type" property="type"/>
    <result column="status" property="status"/>
    <result column="calculate_rule_id" property="calculateRuleId"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
    <association property="linkappSpace" javaType="com.easylinkin.linkappapi.space.entity.LinkappSpace">
      <result column="space_name" property="spaceName"/>
    </association>
    <collection property="calculateConfigAttributes" javaType="ArrayList" ofType="com.easylinkin.linkappapi.calculaterule.entity.CalculateConfigAttribute">
      <id property="id" column="lcca_id"/>
      <result property="deviceUnitCode" column="lcca_deviceUnitCode"/>
      <result property="deviceUnitId" column="lcca_deviceUnitId"/>
      <result property="groupNumber" column="lcca_groupNumber"/>
      <result property="spaceId" column="lcca_spaceId"/>
      <result property="analyzeDataSourceId" column="lcca_analyzeDataSourceId"/>
      <result property="calculateConfigId" column="lcca_calculateConfigId"/>
      <result property="coefficient" column="lcca_coefficient"/>
      <result property="deviceAttributeId" column="lcca_deviceAttributeId"/>
      <result property="attributeIdentifier" column="lcca_attributeIdentifier"/>
    </collection>

  </resultMap>

  <select id="getCalculateConfig" resultMap="getCalculateConfigMap">
    select
    lcc.* ,
    lcca.id as lcca_id,
    lcca.device_unit_code as lcca_deviceUnitCode,
    lcca.device_unit_id as lcca_deviceUnitId,
    lcca.group_number as lcca_groupNumber,
    lcca.space_id as lcca_spaceId,
    lcca.analyze_data_source_id as lcca_analyzeDataSourceId,
    lcca.calculate_config_id as lcca_calculateConfigId,
    lcca.coefficient as lcca_coefficient,
    lcca.device_attribute_id as lcca_deviceAttributeId,
    lcca.attribute_identifier as lcca_attributeIdentifier,
    ls.space_name

    from linkapp_calculate_config lcc
    left join linkapp_calculate_config_attribute lcca
    on lcca.calculate_config_id = lcc.id
    left join linkapp_space ls on ls.id = lcc.space_id
    <where>
      lcc.delete_state = 1
      and
      lcc.id = #{id}
    </where>
  </select>


  <resultMap id="CalculateConfigsMap" type="com.easylinkin.linkappapi.calculaterule.entity.CalculateConfig">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="code" property="code"/>
    <result column="space_id" property="spaceId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="type" property="type"/>
    <result column="status" property="status"/>
    <result column="calculate_rule_id" property="calculateRuleId"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <association property="linkappSpace" javaType="com.easylinkin.linkappapi.space.entity.LinkappSpace">
      <result property="spaceName" column="space_name"/>
    </association>
    <association property="calculateRule" javaType="com.easylinkin.linkappapi.calculaterule.entity.CalculateRule">
      <result property="name" column="lcr_name"/>
    </association>

  </resultMap>

  <select id="getCalculateConfigs" resultMap="CalculateConfigsMap">
    select
    lcc.* ,
    lcr.name as lcr_name,
    ls.space_name
    from linkapp_calculate_config lcc
    left join linkapp_space ls on ls.id = lcc.space_id
    left join linkapp_calculate_rule lcr on lcr.id = lcc.calculate_rule_id
    <where>
      lcc.delete_state = 1
      <if test='calculateConfig.id!=null and calculateConfig.id!="" '>
        and lcc.id = #{calculateConfig.id}
      </if>
      <if test='calculateConfig.tenantId!=null and calculateConfig.tenantId!="" '>
        and lcc.tenant_id = #{calculateConfig.tenantId}
      </if>
      <if test='calculateConfig.calculateRuleId!=null and calculateConfig.calculateRuleId!="" '>
        and lcc.calculate_rule_id = #{calculateConfig.calculateRuleId}
      </if>
      <if test='calculateConfig.name!=null and calculateConfig.name!="" '>
        and lcc.name like CONCAT('%', #{calculateConfig.name},'%')
      </if>
      <if test='calculateConfig.status!=null '>
        and lcc.status = #{calculateConfig.status}
      </if>
    </where>
    order by lcc.modify_time desc
  </select>

  <select id="getCalculateConfigs2" resultMap="CalculateConfigsMap">
    select lcc.id, lcc.name
    from linkapp_calculate_config lcc
           inner join linkapp_calculate_config_attribute lcca on lcc.id = lcca.calculate_config_id
    <where>
      lcc.delete_state = 1
        and lcca.analyze_data_source_id = #{dataSourceId,jdbcType=VARCHAR}
        and lcc.type = #{type,jdbcType=VARCHAR}
    </where>
  </select>

</mapper>
