<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.calculaterule.mapper.CalculateRuleMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.calculaterule.entity.CalculateRule">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="code" property="code"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
  </resultMap>


  <resultMap id="getCalculateRuleMap" type="com.easylinkin.linkappapi.calculaterule.entity.CalculateRule">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="code" property="code"/>
    <result column="create_time" property="createTime"/>
    <result column="creator" property="creator"/>
    <result column="modifier" property="modifier"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="delete_state" property="deleteState"/>
    <collection property="calculateRuleAttributes" javaType="ArrayList" ofType="com.easylinkin.linkappapi.calculaterule.entity.CalculateRuleAttribute">
      <id property="id" column="lcra_id"/>
      <result property="name" column="lcra_name"/>
      <result property="specs" column="lcra_specs"/>
      <result property="identifier" column="lcra_identifier"/>
      <result property="unit" column="lcra_unit"/>
      <result property="calculateRuleId" column="lcra_calculateRuleId"/>
    </collection>
  </resultMap>

  <select id="getCalculateRule" resultMap="getCalculateRuleMap">
    select
    lcr.* ,
    lcra.id as lcra_id,
    lcra.name as lcra_name,
    lcra.unit as lcra_unit,
    lcra.identifier as lcra_identifier,
    lcra.specs as lcra_specs,
    lcra.calculate_rule_id as lcra_calculateRuleId

    from linkapp_calculate_rule lcr
    left join linkapp_calculate_rule_attribute lcra
    on lcra.calculate_rule_id = lcr.id
    <where>
      lcr.id = #{id}
    </where>
  </select>

  <select id="getRuleInfoByDataSourceId" resultType="com.easylinkin.linkappapi.elasticsearch.entity.CalculateRuleOut">
    SELECT distinct rule.id as ruleId, cfg.tenant_id as tenantId, tenant.project_id as projectId, cfg.id as calculateConfigId
    FROM linkapp_calculate_config cfg
    left join linkapp_calculate_config_attribute conat on (conat.calculate_config_id=cfg.id)
    left join linkapp_calculate_rule rule on (cfg.calculate_rule_id = rule.id)
    left join linkapp_tenant tenant on (tenant.id = cfg.tenant_id)
    WHERE conat.analyze_data_source_id = #{id}
  </select>

</mapper>
