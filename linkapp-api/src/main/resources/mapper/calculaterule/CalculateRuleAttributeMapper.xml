<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.calculaterule.mapper.CalculateRuleAttributeMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.calculaterule.entity.CalculateRuleAttribute">
    <id column="id" property="id"/>
    <result column="calculate_rule_id" property="calculateRuleId"/>
    <result column="name" property="name"/>
    <result column="identifier" property="identifier"/>
    <result column="unit" property="unit"/>
    <result column="specs" property="specs"/>
  </resultMap>

</mapper>
