<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.calculaterule.mapper.CalculateConfigAttributeMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.calculaterule.entity.CalculateConfigAttribute">
    <id column="id" property="id"/>
    <result column="calculate_config_id" property="calculateConfigId"/>
    <result column="device_unit_id" property="deviceUnitId"/>
    <result column="device_unit_code" property="deviceUnitCode"/>
    <result column="device_attribute_id" property="deviceAttributeId"/>
    <result column="attribute_identifier" property="attributeIdentifier"/>
    <result column="coefficient" property="coefficient"/>
    <result column="analyze_data_source_id" property="analyzeDataSourceId"/>
    <result column="space_id" property="spaceId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="group_number" property="groupNumber"/>
  </resultMap>

</mapper>
