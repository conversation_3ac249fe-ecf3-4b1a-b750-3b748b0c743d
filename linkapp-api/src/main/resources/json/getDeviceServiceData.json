[{"identifier": "set_threshold", "name": "设置温湿度阈值", "inputs": [{"identifier": "temperature", "name": "温度值", "data_type": {"type": "double", "specs": {"min": "0.0", "max": "60.0", "unit": "℃", "unit_name": "摄氏度", "step": "0.1"}}}, {"identifier": "humidity", "name": "湿度值", "data_type": {"type": "double", "specs": {"min": "0.0", "max": "100.0", "unit": "%RH", "unit_name": "百分比", "step": "0.1"}}}]}, {"identifier": "set_switch", "name": "设置插座开关", "inputs": [{"identifier": "flag", "name": "标志位", "data_type": {"type": "bool", "specs": {"true_value": "1", "false_value": "0", "true_text": "开启", "false_text": "关闭"}}}]}, {"identifier": "set_period", "name": "设置采集周期", "inputs": [{"identifier": "period", "name": "采集周期", "data_type": {"type": "int", "specs": {"min": "30", "max": "120", "unit": "min", "unit_name": "分钟", "step": "15"}}}]}]