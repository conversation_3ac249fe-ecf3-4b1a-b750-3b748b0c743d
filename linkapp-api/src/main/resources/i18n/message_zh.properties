#Morn\u6D88\u606F\u56FD\u9645\u5316
rest.success.message=\u64CD\u4F5C\u6210\u529F
rest.failure.message=\u64CD\u4F5C\u5931\u8D25
#\u767B\u5F55
AbstractUserDetailsAuthenticationProvider.badCredentials=\u8D26\u53F7\u6216\u5BC6\u7801\u4E0D\u6B63\u786E
model.username=\u7528\u6237\u540D
model.password=\u5BC6\u7801
rest.login.success.message=\u767B\u5F55\u6210\u529F
error.login.no-department.message=\u7528\u6237\u672A\u5173\u8054\u6240\u5C5E\u673A\u6784
error.login.department-no-found.message=\u7528\u6237\u6240\u5C5E\u673A\u6784\u4E0D\u5B58\u5728
error.login.no-customer.message=\u7528\u6237\u672A\u5173\u8054\u6240\u5C5E\u5BA2\u6237
error.login.customer-no-found.message=\u7528\u6237\u6240\u5C5E\u5BA2\u6237\u4E0D\u5B58\u5728
error.login.customer-locked.message=\u7528\u6237\u6240\u5C5E\u5BA2\u6237\u5DF2\u9501\u5B9A
error.login.customer-login-failed-locked.message=\u7528\u6237\u6240\u5C5E\u5BA2\u6237\u767B\u5F55\u5931\u8D255\u6B21\u88AB\u9501\u5B9A
#\u7528\u6237\u7BA1\u7406
error.user.user-no-found.message=\u7528\u6237\u4E0D\u5B58\u5728[id={0}]
error.user.forget-user-no-found.message=\u7528\u6237\u540D\u4E0D\u5B58\u5728
error.user.manager-password-is-wrong.message=\u7BA1\u7406\u5458\u5BC6\u7801\u9A8C\u8BC1\u5931\u8D25
error.user.password-is-wrong.message=\u5BC6\u7801\u9519\u8BEF
error.user.originalPassword-is-wrong.message=\u539F\u59CB\u5BC6\u7801\u9519\u8BEF
error.user.password-confirm-failure.message=\u4E24\u6B21\u5BC6\u7801\u4E0D\u4E00\u81F4
error.user.password-repeat.message=\u65B0\u5BC6\u7801\u4E0E\u65E7\u5BC6\u7801\u76F8\u540C
error.user.account-repeat.message=\u8D26\u53F7\u5DF2\u5B58\u5728
error.user.user-phone-mismatch.message=\u7528\u6237\u540D\u4E0E\u624B\u673A\u53F7\u4E0D\u5339\u914D
error.user.verification-get-fail.message=\u9A8C\u8BC1\u7801\u83B7\u53D6\u5931\u8D25
error.user.img-verification-mismatch.message=\u56FE\u7247\u9A8C\u8BC1\u7801\u4E0D\u5339\u914D
error.user.information-not-found.message=\u8BF7\u8F93\u5165\u7528\u6237\u4FE1\u606F
error.user.number-not-null.message=\u7528\u6237\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A
error.user.sms-verification-mismatch.message=\u77ED\u4FE1\u9A8C\u8BC1\u7801\u4E0D\u5339\u914D
error.user.img-not-found=\u56FE\u7247\u9A8C\u8BC1\u7801\u751F\u6210\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u83B7\u53D6\u000d\u000a
#\u673A\u6784\u7BA1\u7406
error.dept.no-found.message=\u7EC4\u7EC7\u673A\u6784\u4E0D\u5B58\u5728[id={0}]
error.dept.code.not-null.message=\u673A\u6784\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A
error.dept.code.repeat.message=\u64CD\u4F5C\u5931\u8D25\uFF0C\u673A\u6784\u7F16\u7801\u91CD\u590D
error.dept.name.repeat.message=\u64CD\u4F5C\u5931\u8D25\uFF0C\u673A\u6784\u540D\u79F0\u91CD\u590D
#\u5B57\u5178\u7C7B\u578B\u7BA1\u7406
error.dict.code.not-null.message=\u5B57\u5178\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A
error.dict.delete-fail.has-child.message=\u5220\u9664\u5931\u8D25\uFF0C\u5B58\u5728\u5B50\u8282\u70B9
error.dict.deletefail.has-dictItem.message=\u5220\u9664\u5931\u8D25\uFF0C\u5B58\u5728\u6570\u636E\u5B57\u5178
error.dict.code.repeat.message=\u64CD\u4F5C\u5931\u8D25\uFF0C\u7F16\u7801\u91CD\u590D
error.dict.name.repeat.message=\u64CD\u4F5C\u5931\u8D25\uFF0C\u540D\u79F0\u91CD\u590D
#\u6570\u636E\u5B57\u5178\u7BA1\u7406
error.dictItem.code.not-null.message=\u5B57\u5178\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A
error.dictItem.delete_fail_has-child.message=\u5220\u9664\u5931\u8D25\uFF0C\u5B58\u5728\u5B50\u8282\u70B9
error.dictItem.code.repeat.message=\u64CD\u4F5C\u5931\u8D25\uFF0C\u7F16\u7801\u91CD\u590D
error.dictItem.name.repeat.message=\u64CD\u4F5C\u5931\u8D25\uFF0C\u540D\u79F0\u91CD\u590D
#\u83DC\u5355\u7BA1\u7406
error.menu.searchCode.not-null.message=searchCode\u4E0D\u80FD\u4E3A\u7A7A
error.menu.parentId.not-null.message=\u7236\u7C7BId\u4E0D\u80FD\u4E3A\u7A7A
error.menu.not-exist.message=\u83DC\u5355\u5BF9\u8C61\u4E0D\u5B58\u5728
error.menu.parentMenu.not-null.message=\u7236\u7C7B\u83DC\u5355\u4E0D\u5B58\u5728
error.menu.not-allow-set-parentNode.message=\u4E0D\u80FD\u5C06\u5F53\u524D\u8282\u70B9\u6216\u5B50\u8282\u70B9\u8BBE\u7F6E\u4E3A\u7236\u7EA7\u83DC\u5355
#\u89D2\u8272\u7BA1\u7406
error.role.name.repeat.message=\u64CD\u4F5C\u5931\u8D25\uFF0C\u89D2\u8272\u540D\u79F0\u91CD\u590D
#licence\u7BA1\u7406
error.licence.unkonwn.message=\u672A\u77E5\u7684licence
error.licence.failure.message=\u5931\u6548\u7684licence
error.licence.mac.message=licence\u7684mac\u5730\u5740\u4E0D\u5339\u914D
error.licence.less.message=licence\u7684\u5141\u8BB8\u8BBE\u5907\u5B89\u88C5\u6570\u5C11\u4E8E\u5DF2\u5B89\u88C5\u8BBE\u5907\u6570
error.licence.not-found.message=licence\u6587\u4EF6\u672A\u4E0A\u4F20
error.licence.import.fail.message=licence\u5BFC\u5165\u5931\u8D25
error.customer.not.limit.message=\u5F53\u524D\u5BA2\u6237\u672A\u914D\u7F6E\u9650\u989D\u4FE1\u606F
error.customer.more.limit.message=\u5B89\u88C5\u6570\u8D85\u8FC7\u4E86\u5F53\u524D\u5BA2\u6237\u7684\u5269\u4F59\u53EF\u5B89\u88C5\u8BBE\u5907\u6570
error.licence.less.limit.message=licence\u7684\u5141\u8BB8\u8BBE\u5907\u5B89\u88C5\u6570\u5C11\u4E8E\u5DF2\u5206\u914D\u7684\u5BA2\u6237\u9650\u989D\u6570\u4E4B\u548C
#\u9519\u8BEF\u4FE1\u606F
error.system.service.exception=\u7CFB\u7EDF\u670D\u52A1\u5F02\u5E38
#\u8BBE\u5907\u7BA1\u7406
deviceManagement=\u8BBE\u5907\u7BA1\u7406
log.deviceManagement.add=\u65B0\u589E\u8BBE\u5907
log.deviceManagement.update=\u4FEE\u6539\u8BBE\u5907
log.deviceManagement.deleteBatch=\u6279\u91CF\u5220\u9664\u8BBE\u5907
log.deviceManagement.importData=\u5BFC\u5165\u8BBE\u5907
#\u52B3\u52A1\u7BA1\u7406
labol.user.up.to.gate=\u95F8\u673A\u4E0B\u53D1\uFF0C\u8EAB\u4EFD\u8BC1\u53F7\uFF1A{0}
labol.user.update=\u4EBA\u5458\u7F16\u8F91\uFF0C\u8EAB\u4EFD\u8BC1\u53F7\uFF1A{0}
labol.user.delete=\u4EBA\u5458\u5220\u9664\uFF0C\u8EAB\u4EFD\u8BC1\u53F7\uFF1A{0}
labol.user.exit.project=\u4EBA\u5458\u9000\u573A\uFF0C\u8EAB\u4EFD\u8BC1\u53F7\uFF1A{0}
labol.group.delete=\u5220\u9664\u73ED\u7EC4\uFF0C\u73ED\u7EC4\u540D\uFF1A{0}
labol.group.exit.project=\u73ED\u7EC4\u9000\u573A\uFF0C\u73ED\u7EC4\u540D\uFF1A{0}
labol.group.in.project=\u73ED\u7EC4\u8FDB\u573A\uFF0C\u73ED\u7EC4\u540D\uFF1A{0}
labol.company.delete=\u5220\u9664\u5355\u4F4D\uFF0C\u5355\u4F4D\u540D\uFF1A{0}
labol.company.exit.project=\u5355\u4F4D\u9000\u573A\uFF0C\u5355\u4F4D\u540D\uFF1A{0}
labol.company.in.project=\u5355\u4F4D\u8FDB\u573A\uFF0C\u5355\u4F4D\u540D\uFF1A{0}
labol.clock.config.update=\u8003\u52E4\u89C4\u5219\u7F16\u8F91\uFF0C\u539F\u8003\u52E4\u89C4\u5219 \u8003\u52E4\u8BA1\u7B97\u65B9\u5F0F\uFF1A{0}\uFF0C\u6253\u5361\u65B9\u5F0F\uFF1A{1} -> \u65B0\u8003\u52E4\u89C4\u5219 \u8003\u52E4\u8BA1\u7B97\u65B9\u5F0F\uFF1A{2}\uFF0C\u6253\u5361\u65B9\u5F0F\uFF1A{3}
labol.realName.Platform.importReal=\u5B9E\u540D\u5236\u5E73\u53F0\u5BFC\u5165\uFF0C\u6587\u4EF6\u540D\u79F0\uFF1A{0}
labol.user.batch.import=\u6279\u91CF\u5BFC\u5165\u4EBA\u5458\u4FE1\u606F\uFF0C\u6587\u4EF6\u540D\u79F0\uFF1A{0}
labol.user.batch.importPhoto=\u6279\u91CF\u5BFC\u5165\u4EBA\u5458\u8FD1\u7167\uFF0C\u6587\u4EF6\u540D\u79F0\uFF1A{0}
labol.upUserToGate=\u540C\u6B65\u95F8\u673A\u6388\u6743\u4EBA\u5458\uFF0C\u95F8\u673A\u540D\u79F0\uFF1A{0}
labol.gate.edit=\u7F16\u8F91\u95F8\u673A\uFF0C\u95F8\u673A\u540D\u79F0\uFF1A{0}
labol.gate.delete=\u5220\u9664\u95F8\u673A,\u95F8\u673A\u540D\u79F0\uFF1A{0}
labol.roster.add.black=\u7533\u8bf7\u6dfb\u52a0\u4eba\u5458\u9ed1\u540d\u5355\uff0c\u59d3\u540d:{0}\uff0c\u8eab\u4efd\u8bc1\u53f7:{1}
labol.user.roster.add.black=
#\u5B89\u5168\u7BA1\u7406
safe.hidden.danger.delete=\u5220\u9664\u9690\u60A3\uFF0C\u9690\u60A3\u4FE1\u606F\uFF1A{0}
safe.check.box.update=\u7F16\u914D\u7535\u7BB1\uFF0C\u914D\u7535\u7BB1\u4FE1\u606F\uFF1A{0}
safe.check.box.delete=\u5220\u9664\u914D\u7535\u7BB1\uFF0C\u914D\u7535\u7BB1\u4FE1\u606F\uFF1A{0}
safe.quality.position.delete=\u5220\u9664\u68C0\u67E5\u90E8\u4F4D\uFF0C\u533A\u57DF\u540D\u79F0\uFF1A{0}
safe.export.inspection.records=\u5BFC\u51FA\u68C0\u67E5\u8BB0\u5F55\u8868
safe.export.rectification.notice=\u5BFC\u51FA\u6574\u6539\u901A\u77E5\u4E66
safe.quality.actual.info.delete=\u5220\u9664\u5B9E\u6D4B\u5B9E\u91CF\u8BB0\u5F55,\u6D4B\u91CF\u90E8\u4F4D\uFF1A{0}
safe.concrete.strength.info.delete=\u5220\u9664\u6DF7\u51DD\u571F\u5F3A\u5EA6\u68C0\u6D4B\u8BB0\u5F55\uFF0C\u6784\u4EF6\u540D\u79F0:{0}\uFF0C\u68C0\u6D4B\u65F6\u95F4:{1}
#\u673A\u68B0\u7BA1\u7406
machinery.record.delete=\u5220\u9664\u8BBE\u5907\uFF0C\u8BBE\u5907\u540D\u79F0\uFF1A{0}
machinery.record.update=\u7F16\u8F91\u8BBE\u5907\uFF0C\u8BBE\u5907\u540D\u79F0\uFF1A{0}
#\u89C4\u5219\u5F15\u64CE
rule.engine.delete=\u5220\u9664\u89C4\u5219\u5F15\u64CE\uFF0C\u89C4\u5219\u540D\u79F0\uFF1A{0}
rule.engine.update=\u914D\u7F6E\u89C4\u5219\u5F15\u64CE\uFF0C\u89C4\u5219\u540D\u79F0\uFF1A{0}
#\u7528\u6237\u7BA1\u7406
user.delete=\u5220\u9664\u7528\u6237\uFF0C\u59D3\u540D\uFF1A{0}\uFF0C\u624B\u673A\u53F7\uFF1A{1}
user.update=\u4FEE\u6539\u7528\u6237\uFF0C\u59D3\u540D\uFF1A{0}\uFF0C\u624B\u673A\u53F7\uFF1A{1}
#\u89D2\u8272\u7BA1\u7406
role.add=\u65B0\u589E\u89D2\u8272\uFF0C\u89D2\u8272\u540D\uFF1A{0}
role.delete=\u5220\u9664\u89D2\u8272\uFF0C\u89D2\u8272\u540D\uFF1A{0}
role.update=\u4FEE\u6539\u89D2\u8272\uFF0C\u89D2\u8272\u540D\uFF1A{0}
#\u767B\u5F55\u9875
login.in.project=\u8FDB\u5165\u9879\u76EE\uFF0C\u9879\u76EE\u540D\u79F0\uFF1A{0}
#\u9879\u76EE\u5927\u5C4F\u8BBE\u7F6E
dashboard.partBuild.important.party.lesson.add=\u65B0\u589E\u91CD\u8981\u515A\u8BFE\uFF0C\u515A\u8BFE\u540D\u79F0:{0}
dashboard.partBuild.important.party.lesson.edit=\u7F16\u8F91\u91CD\u8981\u515A\u8BFE\uFF0C\u515A\u8BFE\u540D\u79F0:{0}
dashboard.partBuild.important.party.lesson.del=\u5220\u9664\u91CD\u8981\u515A\u8BFE\uFF0C\u515A\u8BFE\u540D\u79F0:{0}
dashboard.partBuild.important.speech.add=\u6DFB\u52A0\u91CD\u8981\u8BB2\u8BDD\uFF0C\u8BB2\u8BDD\u540D\u79F0:{0}
dashboard.partBuild.important.speech.edit=\u7F16\u8F91\u91CD\u8981\u8BB2\u8BDD\uFF0C\u8BB2\u8BDD\u540D\u79F0:{0}
dashboard.partBuild.important.speech.del=\u5220\u9664\u91CD\u8981\u8BB2\u8BDD\uFF0C\u8BB2\u8BDD\u540D\u79F0:{0}
dashboard.partBuild.video.add = \u6DFB\u52A0\u667A\u6167\u515A\u5EFA\u89C6\u9891\uFF0C\u89C6\u9891\u540D\u79F0\uFF1A{0}
dashboard.partBuild.video.edit = \u7F16\u8F91\u667A\u6167\u515A\u5EFA\u89C6\u9891\uFF0C\u89C6\u9891\u540D\u79F0\uFF1A{0}
dashboard.partBuild.video.del = \u5220\u9664\u667A\u6167\u515A\u5EFA\u89C6\u9891\uFF0C\u89C6\u9891\u540D\u79F0\uFF1A{0}
dashboard.partBuild.activities.add=\u6DFB\u52A0\u515A\u5EFA\u6D3B\u52A8\uFF0C\u6D3B\u52A8\u540D\u79F0:{0}
dashboard.partBuild.activities.edit=\u7F16\u8F91\u515A\u5EFA\u6D3B\u52A8\uFF0C\u6D3B\u52A8\u540D\u79F0:{0}
dashboard.partBuild.activities.del=\u5220\u9664\u515A\u5EFA\u6D3B\u52A8\uFF0C\u6D3B\u52A8\u540D\u79F0:{0}
dashboard.partBuild.elegant.demeanor.add=\u6DFB\u52A0\u515A\u5458\u98CE\u91C7\uFF0C\u59D3\u540D:{0}
dashboard.partBuild.elegant.demeanor.edit=\u7F16\u8F91\u515A\u5458\u98CE\u91C7\uFF0C\u59D3\u540D:{0}
dashboard.partBuild.elegant.demeanor.del=\u5220\u9664\u515A\u5458\u98CE\u91C7\uFF0C\u59D3\u540D:{0}
dashboard.partBuild.political.status.add=\u6DFB\u52A0\u653F\u6CBB\u9762\u8C8C\uFF0C\u9762\u8C8C\u540D\u79F0:{0}
dashboard.partBuild.political.status.edit=\u7F16\u8F91\u653F\u6CBB\u9762\u8C8C\uFF0C\u9762\u8C8C\u540D\u79F0:{0}
dashboard.partBuild.political.status.del=\u5220\u9664\u653F\u6CBB\u9762\u8C8C\uFF0C\u9762\u8C8C\u540D\u79F0:{0}
dashboard.overview.setting=\u9879\u76EE\u6982\u89C8\u8BBE\u7F6E
dashboard.warning.setting=\u7F16\u8F91\u9879\u76EE\u5927\u5C4F\u9884\u8B66\u8BBE\u7F6E
