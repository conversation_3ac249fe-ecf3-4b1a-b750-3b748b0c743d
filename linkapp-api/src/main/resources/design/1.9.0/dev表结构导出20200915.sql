/*
Navicat MySQL Data Transfer

Source Server         : dev-linkapp-mysql
Source Server Version : 50616
Source Host           : e8n-dev-linkapp.mysql.rds.aliyuncs.com:3306
Source Database       : dev-linkapp-mysql

Target Server Type    : MYSQL
Target Server Version : 50616
File Encoding         : 65001

Date: 2020-09-15 10:37:41
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for `base_role_ref_privilege`
-- ----------------------------
CREATE TABLE `base_role_ref_privilege` (
`role_id_`  bigint(20) NOT NULL ,
`privilege_id_`  bigint(20) NOT NULL ,
PRIMARY KEY (`role_id_`, `privilege_id_`),
FOREIGN KEY (`role_id_`) REFERENCES `sm_role` (`role_id_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
<PERSON>OR<PERSON><PERSON><PERSON> KEY (`privilege_id_`) REFERENCES `sm_privilege` (`id_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
INDEX `FK9l1i31pre02g54tx9vhlir922` (`privilege_id_`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `distribution_cabinet`
-- ----------------------------
CREATE TABLE `distribution_cabinet` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配电柜类型名称' ,
`distribution_cabinet_type_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配电柜类型id' ,
`distribution_room_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配电站id' ,
`sort_no`  int(8) NULL DEFAULT NULL COMMENT '排序编号' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='配电柜'

;

-- ----------------------------
-- Table structure for `distribution_cabinet_configuration`
-- ----------------------------
CREATE TABLE `distribution_cabinet_configuration` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称' ,
`description`  varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述' ,
`status_picture`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态图' ,
`distribution_cabinet_type_site_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配电柜类型的位置信息id' ,
`group_number`  int(11) NULL DEFAULT 0 COMMENT '组编号' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='配电柜类型组态'

;

-- ----------------------------
-- Table structure for `distribution_cabinet_configuration_expression`
-- ----------------------------
CREATE TABLE `distribution_cabinet_configuration_expression` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`distribution_cabinet_configuration_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组态id' ,
`device_unit_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号code' ,
`device_unit_version`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号版本号' ,
`device_attribute_identifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备属性标志符' ,
`device_attribute_parent_identifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父属性属性标志符' ,
`calculate_sign`  varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '算术符' ,
`value`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数值' ,
`sort_no`  int(8) NULL DEFAULT NULL COMMENT '排序' ,
`logic_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '逻辑运算符（与、或）' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='组态表达式'

;

-- ----------------------------
-- Table structure for `distribution_cabinet_ref_device`
-- ----------------------------
CREATE TABLE `distribution_cabinet_ref_device` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`distribution_cabinet_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配电柜id' ,
`device_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备code' ,
`distribution_cabinet_type_site_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配电柜类型位置id' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='配电柜关联设备'

;

-- ----------------------------
-- Table structure for `distribution_cabinet_status`
-- ----------------------------
CREATE TABLE `distribution_cabinet_status` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`distribution_cabinet_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配电柜id ' ,
`distribution_cabinet_configuration_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组态id' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='配电柜状态(冗余部分组态字段)'

;

-- ----------------------------
-- Table structure for `distribution_cabinet_type`
-- ----------------------------
CREATE TABLE `distribution_cabinet_type` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则联动配置名称' ,
`description`  varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='配电柜类型'

;

-- ----------------------------
-- Table structure for `distribution_cabinet_type_site`
-- ----------------------------
CREATE TABLE `distribution_cabinet_type_site` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`site_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '位置名称' ,
`device_unit_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '型号code' ,
`device_unit_version`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '型号版本号' ,
`distribution_cabinet_type_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配电柜类型id' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='配电柜类型关联位置信息'

;

-- ----------------------------
-- Table structure for `distribution_room`
-- ----------------------------
CREATE TABLE `distribution_room` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称' ,
`area_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域id' ,
`description`  varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述' ,
`delete_state`  int(2) NOT NULL DEFAULT 1 COMMENT '逻辑删除，1存在，0已删除' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`latitude`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度' ,
`longitude`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
`video_path`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '视频地址' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='配电房'

;

-- ----------------------------
-- Table structure for `distribution_room_ref_device`
-- ----------------------------
CREATE TABLE `distribution_room_ref_device` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`distribution_room_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配电房id' ,
`device_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编号' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='配电房关联设备'

;

-- ----------------------------
-- Table structure for `hibernate_sequence`
-- ----------------------------
CREATE TABLE `hibernate_sequence` (
`next_val`  bigint(20) NULL DEFAULT NULL 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
AUTO_INCREMENT=2

;

-- ----------------------------
-- Table structure for `linkage_config_ref_down_device`
-- ----------------------------
CREATE TABLE `linkage_config_ref_down_device` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`rule_execution_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器id' ,
`bak_linkage_config_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则联动配置id' ,
`device_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编号' ,
`bak_group_number`  int(11) NULL DEFAULT 0 COMMENT '组编号' ,
PRIMARY KEY (`id`),
UNIQUE INDEX `config_id_device_code_index` (`bak_linkage_config_id`, `device_code`) USING BTREE ,
INDEX `linakge_config_id_index` (`bak_linkage_config_id`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='联动配置关联下行设备'

;

-- ----------------------------
-- Table structure for `linkage_config_relate_service_parm`
-- ----------------------------
CREATE TABLE `linkage_config_relate_service_parm` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`rule_execution_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器id' ,
`bak_linkage_config_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则id' ,
`group_number`  int(11) NOT NULL COMMENT '组序号' ,
`parent_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父级id' ,
`array_index`  int(11) NULL DEFAULT NULL COMMENT '数组下标' ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备型号id' ,
`device_service_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备服务id' ,
`device_parm_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备参数id' ,
`parm_value`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数值' ,
PRIMARY KEY (`id`),
INDEX `rule_area_id_index` (`bak_linkage_config_id`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='智能规则联动配置关联下行设备表'

;

-- ----------------------------
-- Table structure for `linkapp_alarm`
-- ----------------------------
CREATE TABLE `linkapp_alarm` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`rule_engine_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则引擎id' ,
`bak_alarm_rule_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警规则id(关联linkapp_alarm_rule)' ,
`device_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编号' ,
`status`  int(2) NULL DEFAULT NULL COMMENT '处理状态；1未处理，2已处理，其他值-其他' ,
`level`  int(2) NULL DEFAULT NULL COMMENT '告警等级；1高，2中，3低' ,
`content`  varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警内容' ,
`source_json`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`alarm_time`  datetime NULL DEFAULT NULL COMMENT '告警时间' ,
PRIMARY KEY (`id`),
INDEX `ad` (`bak_alarm_rule_id`, `device_code`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_alarm_banknotice_log`
-- ----------------------------
CREATE TABLE `linkapp_alarm_banknotice_log` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`alarm_type`  tinyint(1) NULL DEFAULT NULL COMMENT '告警类型 1周 2 月' ,
`data_source_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源id' ,
`alarm_way`  varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警方式 电话或者邮箱' ,
`content`  varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警内容' ,
`current_statistic`  float(15,2) NULL DEFAULT NULL COMMENT '当周当月' ,
`mom_statistic`  float(15,2) NULL DEFAULT NULL COMMENT '环比上周或者上月统计' ,
`mom_rate`  float(4,2) NULL DEFAULT NULL COMMENT '环比比率' ,
`status`  tinyint(1) NULL DEFAULT NULL COMMENT '通知状态 发送状态；0发送失败，1发送成功' ,
`alarm_date`  datetime NULL DEFAULT NULL COMMENT '告警时间' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='告警通知日志'

;

-- ----------------------------
-- Table structure for `linkapp_alarm_info`
-- ----------------------------
CREATE TABLE `linkapp_alarm_info` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`alarmTime`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`oneDayAddCount`  int(11) NULL DEFAULT NULL ,
`tenantId`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`spaceId`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`status`  int(11) NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_alarm_notice_log`
-- ----------------------------
CREATE TABLE `linkapp_alarm_notice_log` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`rule_engine_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则引擎id' ,
`alarm_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警id' ,
`bak_alarm_rule_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警id(关联linkapp_alarm)' ,
`user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订阅人id (关联linkapp_user)' ,
`content`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '告警内容' ,
`status`  int(1) NULL DEFAULT NULL COMMENT '发送状态；0发送失败，1发送成功' ,
`send_time`  datetime NULL DEFAULT NULL COMMENT '发送时间' ,
`message_type`  int(11) NOT NULL ,
`message_way`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`push_api_url`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='告警通知日志'

;

-- ----------------------------
-- Table structure for `linkapp_alarm_notice_templet`
-- ----------------------------
CREATE TABLE `linkapp_alarm_notice_templet` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`type`  int(1) NULL DEFAULT NULL COMMENT '消息推送类型，1-短信，2-邮件' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板的名称' ,
`description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板的描述' ,
`content`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板内容（1 短信是阿里云预留id 2 邮件是自己编辑的模板 ）' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`create_time`  datetime NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
`modify_time`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_alarm_person_contact`
-- ----------------------------
CREATE TABLE `linkapp_alarm_person_contact` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名' ,
`phone`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号' ,
`email`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '更新时间' ,
`user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id' ,
`delete_state`  int(2) NOT NULL DEFAULT 1 COMMENT '删除状态，1存在 0已删除' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_alarm_process`
-- ----------------------------
CREATE TABLE `linkapp_alarm_process` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`alarm_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '告警信息id,关联linkapp_alarm' ,
`device_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编号' ,
`handler_id`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理人id' ,
`status`  int(2) NULL DEFAULT NULL COMMENT '处理状态；1未处理，2已处理，其他值-其他' ,
`mistake_flag`  int(2) NULL DEFAULT NULL COMMENT '误报标志，101 非误报，201误报' ,
`process_result`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理结果' ,
`process_time`  datetime NULL DEFAULT NULL COMMENT '处理时间' ,
`description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`create_time`  datetime NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
`modify_time`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_alarm_template`
-- ----------------------------
CREATE TABLE `linkapp_alarm_template` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则名称' ,
`level`  int(10) NOT NULL COMMENT '告警等级；1高，2中，3低' ,
`content`  varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警内容' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '更新时间' ,
`user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id' ,
`delete_state`  int(2) NOT NULL DEFAULT 1 COMMENT '删除状态，1存在 0已删除' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_analyze_datasource`
-- ----------------------------
CREATE TABLE `linkapp_analyze_datasource` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据源id' ,
`datasource_name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据源名称' ,
`datasource_desc`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资产的描述' ,
`datasource_type`  int(1) NOT NULL COMMENT '数据源的类型 0:已删; 1:存在' ,
`area_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域id' ,
`device_id_string`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '资产的创建时间' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '资产的修改时间' ,
`delete_state`  int(1) NULL DEFAULT 1 COMMENT '是否删除字段 0:已删; 1:存在' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '租户ID' ,
`status`  int(1) NULL DEFAULT NULL COMMENT '是否删除字段 0:失效; 1:有效' ,
PRIMARY KEY (`id`),
FULLTEXT INDEX `datasource_area_id_index` (`area_id`) 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='设备资产表，关联两张表\r\n关联1  资产表-资产设备表\r\n关联2  资产表-资产用户表\r\n'

;

-- ----------------------------
-- Table structure for `linkapp_application`
-- ----------------------------
CREATE TABLE `linkapp_application` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id' ,
`name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用名称' ,
`description`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述' ,
`app_key`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'appKey' ,
`app_secret`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'app_secret' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`delete_state`  int(1) NULL DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在' ,
`personality_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='应用管理表'

;

-- ----------------------------
-- Table structure for `linkapp_application_ref_device_unit`
-- ----------------------------
CREATE TABLE `linkapp_application_ref_device_unit` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`application_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用模板ID' ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号ID' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_application_ref_privilege`
-- ----------------------------
CREATE TABLE `linkapp_application_ref_privilege` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`application_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用模板ID' ,
`privilege_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限ID' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_application_rule`
-- ----------------------------
CREATE TABLE `linkapp_application_rule` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`module`  decimal(2,0) NULL DEFAULT NULL ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`status`  decimal(2,0) NULL DEFAULT NULL ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`rule`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`value`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_application_template`
-- ----------------------------
CREATE TABLE `linkapp_application_template` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编号' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称' ,
`code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码' ,
`status`  decimal(2,0) NULL DEFAULT NULL COMMENT '状态' ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_area`
-- ----------------------------
CREATE TABLE `linkapp_area` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`parent_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`space_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`area_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`area_no`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`sort_no`  decimal(8,0) NULL DEFAULT 0 ,
`type`  decimal(2,0) NULL DEFAULT NULL COMMENT '1.室内 2.室外' ,
`level`  decimal(2,0) NULL DEFAULT 1 ,
`status`  decimal(2,0) NULL DEFAULT 1 COMMENT '0.删除，1.有效' ,
`img`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`latitude`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`longitude`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`site`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '安装详情' ,
`area_path`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域位置' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`map_polygon_path`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地图范围' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_asset`
-- ----------------------------
CREATE TABLE `linkapp_asset` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资产编号' ,
`asset_no`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '资产编号' ,
`asset_name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资产名称' ,
`asset_area_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`asset_desc`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备描述' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资产创建者' ,
`create_time`  datetime NOT NULL COMMENT '资产的创建时间' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '资产的修改时间' ,
`delete_state`  int(1) NOT NULL COMMENT '是否删除字段 0:已删; 1:存在' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID' ,
`latitude`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`longitude`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`indoor_location`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '室内位置' ,
PRIMARY KEY (`id`),
FULLTEXT INDEX `asset_area_id_index` (`asset_area_id`) 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='设备资产表，关联两张表\r\n关联1  资产表-资产设备表\r\n关联2  资产表-资产用户表\r\n'

;

-- ----------------------------
-- Table structure for `linkapp_asset_ref_device`
-- ----------------------------
CREATE TABLE `linkapp_asset_ref_device` (
`asset_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资产id' ,
`device_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备id' ,
PRIMARY KEY (`asset_id`, `device_id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='资产和设备是多对多的关联关系\r\n资产设备关联表'

;

-- ----------------------------
-- Table structure for `linkapp_asset_ref_user`
-- ----------------------------
CREATE TABLE `linkapp_asset_ref_user` (
`asset_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资产id ' ,
`user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id' ,
PRIMARY KEY (`asset_id`, `user_id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='资产和用户是多对多关系\r\n资产用户关联表'

;

-- ----------------------------
-- Table structure for `linkapp_bank_alarm_rule`
-- ----------------------------
CREATE TABLE `linkapp_bank_alarm_rule` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`data_source_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联的数据源id 一对一' ,
`status`  tinyint(1) NULL DEFAULT NULL COMMENT '规则状态，1未启用（默认）2启用' ,
`alarm_thr`  float(5,0) NULL DEFAULT NULL COMMENT '告警阀值' ,
`is_notice`  tinyint(1) NULL DEFAULT NULL COMMENT '是否告警通知 0否 1是' ,
`phone`  varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警等级；1高，2中，3低' ,
`energy_kind`  varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '能源类型 water:水表设备 electric：电表设备： air：气表设备' ,
`alarm_type`  tinyint(1) NULL DEFAULT NULL ,
`mail`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`delete_state`  tinyint(1) NULL DEFAULT NULL COMMENT '修改人id' ,
`cron_expression`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`job_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '定时jobId' ,
PRIMARY KEY (`id`),
INDEX `ad` (`data_source_id`, `status`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='银行贷后管理，告警规则表'

;

-- ----------------------------
-- Table structure for `linkapp_calculate_config`
-- ----------------------------
CREATE TABLE `linkapp_calculate_config` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计算名称' ,
`code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计算编号' ,
`space_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '空间id' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`status`  int(1) NULL DEFAULT NULL COMMENT '启用状态，0未启用 1启用' ,
`calculate_rule_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计算规则id' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`delete_state`  int(1) NULL DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='计算配置'

;

-- ----------------------------
-- Table structure for `linkapp_calculate_config_attribute`
-- ----------------------------
CREATE TABLE `linkapp_calculate_config_attribute` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id' ,
`calculate_config_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计算配置id' ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号id' ,
`device_unit_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号code' ,
`device_attribute_id`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性id' ,
`attribute_identifier`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性标志符' ,
`coefficient`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系数' ,
`analyze_data_source_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源id' ,
`space_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '空间id' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`group_number`  int(11) NULL DEFAULT NULL COMMENT '组编号' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='计算配置属性'

;

-- ----------------------------
-- Table structure for `linkapp_calculate_rule`
-- ----------------------------
CREATE TABLE `linkapp_calculate_rule` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则名称' ,
`code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则编号' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`delete_state`  int(1) NULL DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='计算规则'

;

-- ----------------------------
-- Table structure for `linkapp_calculate_rule_attribute`
-- ----------------------------
CREATE TABLE `linkapp_calculate_rule_attribute` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id' ,
`calculate_rule_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计算规则id' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性名称' ,
`identifier`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性标识符' ,
`unit`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据类型 int double float long string enum' ,
`specs`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据格式描述' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='计算规则属性'

;

-- ----------------------------
-- Table structure for `linkapp_company`
-- ----------------------------
CREATE TABLE `linkapp_company` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`company_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`company_code`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`sort_no`  decimal(2,0) NULL DEFAULT NULL ,
`status`  decimal(2,0) NULL DEFAULT NULL ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`domain_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_dashboard`
-- ----------------------------
CREATE TABLE `linkapp_dashboard` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id' ,
`name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大屏名称' ,
`dashboard_url`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大屏链接' ,
`description`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大屏描述' ,
`sketch`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大屏缩略图' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID' ,
`delete_state`  int(1) NULL DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
PRIMARY KEY (`id`),
INDEX `dashboard_name` (`name`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='可视化大屏表'

;

-- ----------------------------
-- Table structure for `linkapp_device`
-- ----------------------------
CREATE TABLE `linkapp_device` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编码' ,
`name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称' ,
`status`  int(10) NULL DEFAULT 0 COMMENT '设备状态0:正常; 1:告警' ,
`delete_state`  int(10) NULL DEFAULT 1 COMMENT '是否删除字段 1:存在; 0:删除' ,
`online_state`  int(10) NULL DEFAULT 0 COMMENT '0:离线; 1:正常' ,
`battery`  float NULL DEFAULT NULL COMMENT '电量' ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注' ,
`alarm_switch`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报警开关' ,
`company_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司编号' ,
`project_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目编号' ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备型号' ,
`install_time`  datetime NULL DEFAULT NULL COMMENT '安装时间' ,
`repair_time`  datetime NULL DEFAULT NULL COMMENT '检修时间（最后一次）' ,
`next_repair_time`  datetime NULL DEFAULT NULL COMMENT '检修时间（下一次）' ,
`last_push_time`  datetime NULL DEFAULT NULL COMMENT '报文最后推送时间' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`area_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`latitude`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度' ,
`longitude`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度' ,
`site`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '安装位置' ,
`indoor_location`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '室内位置' ,
`area_path`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域层级路径' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID' ,
PRIMARY KEY (`id`),
FULLTEXT INDEX `device_area_id_index` (`area_id`) 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_device_attribute`
-- ----------------------------
CREATE TABLE `linkapp_device_attribute` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`identifier`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性标识符' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性名称' ,
`value`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性值' ,
`unit`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位' ,
`ico_path`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性显示图标' ,
`type`  int(8) NULL DEFAULT NULL COMMENT '属性类型' ,
`specs`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规范' ,
`sort_no`  int(8) NULL DEFAULT NULL COMMENT '排序' ,
`description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述' ,
`is_show`  bit(1) NULL DEFAULT b'0' COMMENT '是否默认显示,默认不显示' ,
`expression`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性表达式' ,
`dict_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据字典code' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`company_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`parent_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`version`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`array_index`  int(2) NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_device_attribute_status`
-- ----------------------------
CREATE TABLE `linkapp_device_attribute_status` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`device_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编码' ,
`device_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备名称' ,
`prop_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性名称' ,
`array_index`  int(11) NULL DEFAULT NULL COMMENT '数组下标' ,
`parent_prop_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父级属性编码' ,
`prop_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性编码' ,
`prop_unit`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性单位' ,
`prop_value`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '属性值' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`version`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`parent_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`),
INDEX `idx_devicecode_propcode_version` (`device_code`, `prop_code`, `version`) USING BTREE ,
INDEX `idx_devicecode_propcode_parentid_version_arrayindex` (`device_code`, `prop_code`, `parent_id`, `version`, `array_index`) USING BTREE ,
INDEX `idx_devicecode_modifytime_createtime` (`device_code`, `modify_time`, `create_time`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_device_flow`
-- ----------------------------
CREATE TABLE `linkapp_device_flow` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`device_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编码' ,
`device_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备名称' ,
`prop_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性名称' ,
`prop_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性编码' ,
`prop_unit`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性单位' ,
`prop_value`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性值' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_device_info`
-- ----------------------------
CREATE TABLE `linkapp_device_info` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`createTime`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`oneDayAddCount`  int(11) NULL DEFAULT NULL ,
`tenantId`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`onlineState`  int(11) NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_device_parm`
-- ----------------------------
CREATE TABLE `linkapp_device_parm` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`device_service_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`identifier`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数标识符' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数名称' ,
`value`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数值' ,
`unit`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位' ,
`specs`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规范' ,
`sort_no`  int(8) NULL DEFAULT NULL COMMENT '排序' ,
`description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`parent_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`version`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`array_index`  int(10) NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_device_service`
-- ----------------------------
CREATE TABLE `linkapp_device_service` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号' ,
`identifier`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识' ,
`name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称' ,
`type`  int(2) NULL DEFAULT 1 COMMENT '类型' ,
`sort_no`  int(8) NULL DEFAULT NULL COMMENT '排序' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`version`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`is_show`  bit(1) NULL DEFAULT b'0' COMMENT '是否默认显示,默认不显示' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_device_type`
-- ----------------------------
CREATE TABLE `linkapp_device_type` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`parent_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父类型id' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型名称' ,
`description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`level`  decimal(2,0) NULL DEFAULT NULL ,
`search_code`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`company_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`delete_state`  int(10) NULL DEFAULT 1 COMMENT '是否删除字段 1:存在; 0:删除' ,
`ico_path`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性显示图标' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_device_unit`
-- ----------------------------
CREATE TABLE `linkapp_device_unit` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`device_type_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备类型' ,
`device_type_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备类型名称' ,
`company_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称' ,
`code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码' ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注' ,
`icon`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标' ,
`identification`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`device_life`  float NULL DEFAULT NULL COMMENT '设备寿命（月）' ,
`repair_cycle`  float NULL DEFAULT NULL COMMENT '检修周期（天）' ,
`offline_time`  float NULL DEFAULT NULL COMMENT '设备离线时间（小时）' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`delete_state`  int(10) NULL DEFAULT 1 COMMENT '是否删除字段 1:存在; 0:删除' ,
`visualization_config`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '可视化配置' ,
`version`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`physics_model`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '物模型' ,
`linkapp_flag`  int(11) NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_elstic_devenergyreal`
-- ----------------------------
CREATE TABLE `linkapp_elstic_devenergyreal` (
`id`  bigint(50) NOT NULL AUTO_INCREMENT COMMENT '记录序号' ,
`batchId`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '查询的批次id' ,
`dayEnergy`  double(20,2) NOT NULL ,
`deviceCode`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编码' ,
`deviceName`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备名称' ,
`deviceUnitName`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备的编号' ,
`uniqueMark`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号ID' ,
`createTime`  datetime NOT NULL COMMENT '用电数据某天最新的电表数据yyyy-MM-dd' ,
`sumEnergy`  double(20,2) NOT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='设备资产表，关联两张表\r\n关联1  资产表-资产设备表\r\n关联2  资产表-资产用户表\r\n'
AUTO_INCREMENT=739

;

-- ----------------------------
-- Table structure for `linkapp_function`
-- ----------------------------
CREATE TABLE `linkapp_function` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`function_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能名称' ,
`description`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能描述' ,
`type`  int(11) NULL DEFAULT NULL ,
`sort_no`  int(8) NULL DEFAULT NULL ,
`img_path`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片路径' ,
`is_must`  bit(1) NULL DEFAULT b'1' COMMENT '是否必须' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`before_img_path`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上一次图片路径' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_function_ref_privilege`
-- ----------------------------
CREATE TABLE `linkapp_function_ref_privilege` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`function_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`privilege_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_industry`
-- ----------------------------
CREATE TABLE `linkapp_industry` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`code`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`sort_no`  decimal(2,0) NULL DEFAULT NULL ,
`status`  decimal(2,0) NULL DEFAULT NULL ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`domain_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_intelligent_rule_expression`
-- ----------------------------
CREATE TABLE `linkapp_intelligent_rule_expression` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`rule_trigger_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '触发器id' ,
`delete_state`  int(2) NOT NULL DEFAULT 1 COMMENT '删除状态，1存在 0已删除' ,
`bak_intelligent_rule_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则id' ,
`device_attribute_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备属性id' ,
`device_attribute_parent_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父属性id' ,
`calculate_sign`  varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '算术符' ,
`expression`  varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表达式' ,
`value`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数值' ,
`sort_no`  int(8) NULL DEFAULT NULL COMMENT '排序' ,
`logic_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '逻辑运算符（与、或）' ,
`rule_condition_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '条件器id' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='智能规则对应的表达式'

;

-- ----------------------------
-- Table structure for `linkapp_intelligent_rule_relate`
-- ----------------------------
CREATE TABLE `linkapp_intelligent_rule_relate` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`intelligent_rule_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '智能规则id' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`device_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编号' ,
`delete_state`  int(2) NOT NULL DEFAULT 1 COMMENT '删除状态，1存在，0已删除' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_job_entity`
-- ----------------------------
CREATE TABLE `linkapp_job_entity` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`job_name`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称' ,
`job_group`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务分组' ,
`job_status`  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务状态， 0禁用 1启用' ,
`cron_expression`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务cron表达式' ,
`remark`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务描述' ,
`job_task_type`  varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务调度类型，0：接口，1：存储过程' ,
`api_url`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口地址' ,
`params`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数' ,
`job_type`  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务类型，0：周期性，1：一次性' ,
`trigger_name`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '触发器名字' ,
`trigger_group`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '触发器分组' ,
`is_now_run`  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否立即运行，0：否，1：是' ,
`start_date`  datetime NULL DEFAULT NULL COMMENT '生效日期' ,
`end_date`  datetime NULL DEFAULT NULL COMMENT '失效日期' ,
`job_class_name`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行类名' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '任务创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '任务更新时间' ,
`delete_state`  int(1) NULL DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_linkage_log`
-- ----------------------------
CREATE TABLE `linkapp_linkage_log` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id' ,
`rule_engine_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则引擎id' ,
`bak_linkage_config_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置id' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`space_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '空间id' ,
`type`  int(1) NULL DEFAULT NULL COMMENT '类别：1：联动规则触发类，2：定时任务触发类' ,
`state`  tinyint(1) NULL DEFAULT 0 COMMENT '0-失败，1-成功' ,
`device_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编号' ,
`result_code`  varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果码 200 成功 500：失败' ,
`result_msg`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果信息' ,
`request_id`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求id' ,
`send_time`  datetime NULL DEFAULT NULL COMMENT '发送时间' ,
`save_time`  datetime NULL DEFAULT NULL COMMENT '保存时间' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='联动执行日志'

;

-- ----------------------------
-- Table structure for `linkapp_menu`
-- ----------------------------
CREATE TABLE `linkapp_menu` (
`id_`  bigint(20) NOT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`level_`  int(11) NULL DEFAULT NULL ,
`order_no_`  decimal(8,2) NULL DEFAULT NULL ,
`icon_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`path_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`parent_id_`  bigint(20) NULL DEFAULT NULL ,
`privilege_code_`  bigint(20) NULL DEFAULT NULL ,
`remark_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT b'1' COMMENT '1.显示 0.不显示' ,
`search_code_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`state_`  bit(1) NULL DEFAULT NULL ,
`system_id_`  bigint(20) NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_obj_operate_log`
-- ----------------------------
CREATE TABLE `linkapp_obj_operate_log` (
`log_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`object_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对象id' ,
`object_key`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对象关键标识（如code,name）' ,
`operate_desc`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作描述' ,
`content`  varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志内容' ,
`fail_information`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
`ip`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`module_name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`url`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`status`  bit(1) NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`creator_name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称' ,
PRIMARY KEY (`log_id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_personality`
-- ----------------------------
CREATE TABLE `linkapp_personality` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id' ,
`name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '个性化名称' ,
`platform`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`domin`  varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '默认域名' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`delete_state`  int(1) NULL DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在' ,
`deep_color_logo`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`light_color_logo`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`login_back`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`),
INDEX `aa` (`domin`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='个性化表'

;

-- ----------------------------
-- Table structure for `linkapp_privilege`
-- ----------------------------
CREATE TABLE `linkapp_privilege` (
`id_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`parent_id_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`privilege_code_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`description_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`level_`  int(11) NULL DEFAULT NULL ,
`target`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`search_code_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`seq_`  decimal(8,2) NULL DEFAULT NULL ,
`type_`  int(11) NULL DEFAULT NULL COMMENT '类型:1-菜单，2-按钮类' ,
`url_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`is_log_`  int(11) NULL DEFAULT NULL ,
`tenant_id_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`icon_name`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_project`
-- ----------------------------
CREATE TABLE `linkapp_project` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`project_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称' ,
`project_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目编号' ,
`status`  int(10) NULL DEFAULT NULL COMMENT '状态' ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`id`),
UNIQUE INDEX `index_project_name` (`project_name`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_propertyassets`
-- ----------------------------
CREATE TABLE `linkapp_propertyassets` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`status`  decimal(2,0) NULL DEFAULT NULL ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_role`
-- ----------------------------
CREATE TABLE `linkapp_role` (
`role_id_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`code_`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`description_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`department_id_`  bigint(20) NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT b'1' COMMENT '1.显示 0.不显示' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`role_id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_role_ref_privilege`
-- ----------------------------
CREATE TABLE `linkapp_role_ref_privilege` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`role_id_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`privilege_id_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_role_space`
-- ----------------------------
CREATE TABLE `linkapp_role_space` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`space_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`role_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_rule_component`
-- ----------------------------
CREATE TABLE `linkapp_rule_component` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件名称' ,
`type`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件类型' ,
`description`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件描述' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`cron`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时间表达式' ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号' 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
AUTO_INCREMENT=48

;

-- ----------------------------
-- Table structure for `linkapp_rule_component_attribute`
-- ----------------------------
CREATE TABLE `linkapp_rule_component_attribute` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`device_attribute_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备属性id' ,
`device_attribute_parent_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父属性id' ,
`calculate_sign`  varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '算术符' ,
`expression`  varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表达式' ,
`value`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数值' ,
`sort_no`  int(8) NULL DEFAULT NULL COMMENT '排序' ,
`logic_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '逻辑运算符（与、或）' ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`rule_component_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`specs`  varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`unit`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_rule_component_service`
-- ----------------------------
CREATE TABLE `linkapp_rule_component_service` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`device_service_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`device_parm_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`device_parm_parent_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`value`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`sort_no`  int(8) NULL DEFAULT NULL ,
`logic_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`rule_component_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`unit`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`specs`  varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_space`
-- ----------------------------
CREATE TABLE `linkapp_space` (
`id`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL ,
`space_name`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`short_name`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`space_no`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`sort_no`  decimal(8,0) NULL DEFAULT 0 ,
`status`  decimal(2,0) NULL DEFAULT 1 COMMENT '0.删除，1.有效' ,
`longitude`  varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`latitude`  varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`province`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`city`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`city_code`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`district`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`site`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`parent_id`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`type`  decimal(2,0) NULL DEFAULT NULL ,
`remark`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`map_polygon_path`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地图范围' ,
`map_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '室内地图' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_tenant`
-- ----------------------------
CREATE TABLE `linkapp_tenant` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`tenant_id`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`app_id`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`app_key`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`project_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`app_type`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`status`  decimal(2,0) NULL DEFAULT 1 ,
`remark`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`personality_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '个性化配置' ,
`platform_project_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`platform_account`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`platform_account_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`app_industry_type`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`telephone`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`email`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_tenant_ref_device_unit`
-- ----------------------------
CREATE TABLE `linkapp_tenant_ref_device_unit` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_tenant_ref_function`
-- ----------------------------
CREATE TABLE `linkapp_tenant_ref_function` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`function_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
AUTO_INCREMENT=1283

;

-- ----------------------------
-- Table structure for `linkapp_tenant_ref_privilege`
-- ----------------------------
CREATE TABLE `linkapp_tenant_ref_privilege` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`privilege_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_user`
-- ----------------------------
CREATE TABLE `linkapp_user` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`username`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`nickname`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`password`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`address`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`birthday`  date NULL DEFAULT NULL ,
`code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`email`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`id_number`  varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`phone`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`sex`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`icon`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
`description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`icon_sm`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`display`  bit(1) NULL DEFAULT NULL ,
`locked`  bit(1) NULL DEFAULT b'0' COMMENT '1.冻结 0.正常' ,
`department_id`  bigint(20) NULL DEFAULT NULL ,
`type`  varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '1.管理员 2.普通用户' ,
`delete_state`  int(2) NULL DEFAULT 1 ,
`index_config_url`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '首页配置' ,
`index_config_code`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_user_history_p`
-- ----------------------------
CREATE TABLE `linkapp_user_history_p` (
`id_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`password_`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`user_id_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_user_login_info`
-- ----------------------------
CREATE TABLE `linkapp_user_login_info` (
`user_id_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`error_count_`  int(11) NULL DEFAULT NULL ,
`error_time_`  datetime NULL DEFAULT NULL ,
`lock_time_`  datetime NULL DEFAULT NULL ,
`unlock_time_`  datetime NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`user_id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `linkapp_user_ref_role`
-- ----------------------------
CREATE TABLE `linkapp_user_ref_role` (
`user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`role_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `operate_log`
-- ----------------------------
CREATE TABLE `operate_log` (
`log_id`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`content`  varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`fail_information`  longtext CHARACTER SET utf8 COLLATE utf8_bin NULL ,
`ip`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`module_name`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`url`  varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`status`  bit(1) NULL DEFAULT NULL ,
PRIMARY KEY (`log_id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8 COLLATE=utf8_bin

;

-- ----------------------------
-- Table structure for `qrtz_blob_triggers`
-- ----------------------------
CREATE TABLE `qrtz_blob_triggers` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`BLOB_DATA`  blob NULL ,
PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`),
FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT,
INDEX `SCHED_NAME` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_calendars`
-- ----------------------------
CREATE TABLE `qrtz_calendars` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`CALENDAR_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`CALENDAR`  blob NOT NULL ,
PRIMARY KEY (`SCHED_NAME`, `CALENDAR_NAME`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_cron_triggers`
-- ----------------------------
CREATE TABLE `qrtz_cron_triggers` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`CRON_EXPRESSION`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TIME_ZONE_ID`  varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`),
FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_fired_triggers`
-- ----------------------------
CREATE TABLE `qrtz_fired_triggers` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`ENTRY_ID`  varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`INSTANCE_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`FIRED_TIME`  bigint(13) NOT NULL ,
`SCHED_TIME`  bigint(13) NOT NULL ,
`PRIORITY`  int(11) NOT NULL ,
`STATE`  varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`JOB_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`JOB_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`IS_NONCONCURRENT`  varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`REQUESTS_RECOVERY`  varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`SCHED_NAME`, `ENTRY_ID`),
INDEX `IDX_QRTZ_FT_TRIG_INST_NAME` (`SCHED_NAME`, `INSTANCE_NAME`) USING BTREE ,
INDEX `IDX_QRTZ_FT_INST_JOB_REQ_RCVRY` (`SCHED_NAME`, `INSTANCE_NAME`, `REQUESTS_RECOVERY`) USING BTREE ,
INDEX `IDX_QRTZ_FT_J_G` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE ,
INDEX `IDX_QRTZ_FT_JG` (`SCHED_NAME`, `JOB_GROUP`) USING BTREE ,
INDEX `IDX_QRTZ_FT_T_G` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE ,
INDEX `IDX_QRTZ_FT_TG` (`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_job_details`
-- ----------------------------
CREATE TABLE `qrtz_job_details` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`JOB_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`JOB_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`DESCRIPTION`  varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`JOB_CLASS_NAME`  varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`IS_DURABLE`  varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`IS_NONCONCURRENT`  varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`IS_UPDATE_DATA`  varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`REQUESTS_RECOVERY`  varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`JOB_DATA`  blob NULL ,
PRIMARY KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`),
INDEX `IDX_QRTZ_J_REQ_RECOVERY` (`SCHED_NAME`, `REQUESTS_RECOVERY`) USING BTREE ,
INDEX `IDX_QRTZ_J_GRP` (`SCHED_NAME`, `JOB_GROUP`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_locks`
-- ----------------------------
CREATE TABLE `qrtz_locks` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`LOCK_NAME`  varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
PRIMARY KEY (`SCHED_NAME`, `LOCK_NAME`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_paused_trigger_grps`
-- ----------------------------
CREATE TABLE `qrtz_paused_trigger_grps` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
PRIMARY KEY (`SCHED_NAME`, `TRIGGER_GROUP`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_scheduler_state`
-- ----------------------------
CREATE TABLE `qrtz_scheduler_state` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`INSTANCE_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`LAST_CHECKIN_TIME`  bigint(13) NOT NULL ,
`CHECKIN_INTERVAL`  bigint(13) NOT NULL ,
PRIMARY KEY (`SCHED_NAME`, `INSTANCE_NAME`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_simple_triggers`
-- ----------------------------
CREATE TABLE `qrtz_simple_triggers` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`REPEAT_COUNT`  bigint(7) NOT NULL ,
`REPEAT_INTERVAL`  bigint(12) NOT NULL ,
`TIMES_TRIGGERED`  bigint(10) NOT NULL ,
PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`),
FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_simprop_triggers`
-- ----------------------------
CREATE TABLE `qrtz_simprop_triggers` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`STR_PROP_1`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`STR_PROP_2`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`STR_PROP_3`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`INT_PROP_1`  int(11) NULL DEFAULT NULL ,
`INT_PROP_2`  int(11) NULL DEFAULT NULL ,
`LONG_PROP_1`  bigint(20) NULL DEFAULT NULL ,
`LONG_PROP_2`  bigint(20) NULL DEFAULT NULL ,
`DEC_PROP_1`  decimal(13,4) NULL DEFAULT NULL ,
`DEC_PROP_2`  decimal(13,4) NULL DEFAULT NULL ,
`BOOL_PROP_1`  varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`BOOL_PROP_2`  varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`),
FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `qrtz_triggers`
-- ----------------------------
CREATE TABLE `qrtz_triggers` (
`SCHED_NAME`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`JOB_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`JOB_GROUP`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`DESCRIPTION`  varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`NEXT_FIRE_TIME`  bigint(13) NULL DEFAULT NULL ,
`PREV_FIRE_TIME`  bigint(13) NULL DEFAULT NULL ,
`PRIORITY`  int(11) NULL DEFAULT NULL ,
`TRIGGER_STATE`  varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`TRIGGER_TYPE`  varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`START_TIME`  bigint(13) NOT NULL ,
`END_TIME`  bigint(13) NULL DEFAULT NULL ,
`CALENDAR_NAME`  varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`MISFIRE_INSTR`  smallint(2) NULL DEFAULT NULL ,
`JOB_DATA`  blob NULL ,
PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`),
FOREIGN KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) REFERENCES `qrtz_job_details` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT,
INDEX `IDX_QRTZ_T_J` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE ,
INDEX `IDX_QRTZ_T_JG` (`SCHED_NAME`, `JOB_GROUP`) USING BTREE ,
INDEX `IDX_QRTZ_T_C` (`SCHED_NAME`, `CALENDAR_NAME`) USING BTREE ,
INDEX `IDX_QRTZ_T_G` (`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE ,
INDEX `IDX_QRTZ_T_STATE` (`SCHED_NAME`, `TRIGGER_STATE`) USING BTREE ,
INDEX `IDX_QRTZ_T_N_STATE` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE ,
INDEX `IDX_QRTZ_T_N_G_STATE` (`SCHED_NAME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE ,
INDEX `IDX_QRTZ_T_NEXT_FIRE_TIME` (`SCHED_NAME`, `NEXT_FIRE_TIME`) USING BTREE ,
INDEX `IDX_QRTZ_T_NFT_ST` (`SCHED_NAME`, `TRIGGER_STATE`, `NEXT_FIRE_TIME`) USING BTREE ,
INDEX `IDX_QRTZ_T_NFT_MISFIRE` (`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`) USING BTREE ,
INDEX `IDX_QRTZ_T_NFT_ST_MISFIRE` (`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`, `TRIGGER_STATE`) USING BTREE ,
INDEX `IDX_QRTZ_T_NFT_ST_MISFIRE_GRP` (`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `rule_condition`
-- ----------------------------
CREATE TABLE `rule_condition` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`type`  int(2) NULL DEFAULT NULL COMMENT '条件类型：1-时间范围，2设备状态' ,
`sort_flag`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '排序标记' ,
`rule_engine_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则引擎id' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`device_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编号' ,
`time_scope_cron`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时间范围cron表达式' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='条件器'

;

-- ----------------------------
-- Table structure for `rule_engine`
-- ----------------------------
CREATE TABLE `rule_engine` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则名称' ,
`status`  int(2) NULL DEFAULT 0 COMMENT '状态 0-停用 1-启用' ,
`space_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '空间id' ,
`tenant_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' ,
`auto_recoverable`  tinyint(1) NULL DEFAULT 0 COMMENT '可自动恢复的' ,
`description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则描述说明' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人id' ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人id' ,
PRIMARY KEY (`id`),
INDEX `rule_area_id_index` (`space_id`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='智能规则引擎'

;

-- ----------------------------
-- Table structure for `rule_execution`
-- ----------------------------
CREATE TABLE `rule_execution` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`type`  int(2) NULL DEFAULT NULL COMMENT '类型：1-下行服务，2-告警工单' ,
`rule_engine_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则引擎id' ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号id' ,
`sort_flag`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '排序标记' ,
`alarm_template_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警通知模板id(只有type为2-告警工单时，才有值)' ,
`delay_time`  float(15,2) NULL DEFAULT 0.00 COMMENT '延迟时间' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='执行器'

;

-- ----------------------------
-- Table structure for `rule_execution_ref_alarm_person_contact`
-- ----------------------------
CREATE TABLE `rule_execution_ref_alarm_person_contact` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`rule_execution_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器 id' ,
`alarm_person_contact_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警通知人 id' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='执行器关联告警通知人'

;

-- ----------------------------
-- Table structure for `rule_trigger`
-- ----------------------------
CREATE TABLE `rule_trigger` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`type`  int(2) NULL DEFAULT NULL COMMENT '类型：3-定时触发，1设备触发属性触发，2设备触发上下线触发' ,
`rule_engine_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则引擎id' ,
`sort_flag`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '排序标记' ,
`create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`modify_time`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`common_rule_item`  int(1) NULL DEFAULT NULL COMMENT '通用规则的类型列举 0-离线 1-设备上线' ,
`device_unit_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号id' ,
`job_entity_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '定时任务id' ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='规则触发器'

;

-- ----------------------------
-- Table structure for `rule_trigger_ref_device`
-- ----------------------------
CREATE TABLE `rule_trigger_ref_device` (
`id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键' ,
`rule_trigger_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '触发器 id' ,
`device_code`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编号' ,
PRIMARY KEY (`id`),
UNIQUE INDEX `rule_trigger_id_device_code_index` (`rule_trigger_id`, `device_code`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='智能规则联动配置关联设备表'

;

-- ----------------------------
-- Table structure for `sm_area`
-- ----------------------------
CREATE TABLE `sm_area` (
`id_`  bigint(20) NOT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`latitude_`  double NULL DEFAULT NULL ,
`level_`  int(11) NULL DEFAULT NULL ,
`longitude_`  double NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`parent_id_`  bigint(20) NULL DEFAULT NULL ,
`search_code_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_customer`
-- ----------------------------
CREATE TABLE `sm_customer` (
`id_`  bigint(20) NOT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`address_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`customer_code_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT NULL ,
`email_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`locked_`  bit(1) NULL DEFAULT NULL ,
`logo_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`phone_number_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`remark_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_department`
-- ----------------------------
CREATE TABLE `sm_department` (
`id_`  bigint(20) NOT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`address_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`code_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`contact_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`email_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`level_`  int(11) NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`parent_id_`  bigint(20) NULL DEFAULT NULL ,
`remark_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`search_code_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`customer_id_`  bigint(20) NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT NULL ,
`order_no_`  int(11) NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_dict`
-- ----------------------------
CREATE TABLE `sm_dict` (
`id_`  bigint(20) NOT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`code_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT NULL ,
`ext_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`level_`  int(11) NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`order_no_`  int(11) NULL DEFAULT NULL ,
`parent_id_`  bigint(20) NULL DEFAULT NULL ,
`remark_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_dict_item`
-- ----------------------------
CREATE TABLE `sm_dict_item` (
`id_`  bigint(20) NOT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`code_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`dict_id_`  bigint(20) NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT NULL ,
`ext_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`level_`  int(11) NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`order_no_`  int(11) NULL DEFAULT NULL ,
`parent_id_`  bigint(20) NULL DEFAULT NULL ,
`remark_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_feedback`
-- ----------------------------
CREATE TABLE `sm_feedback` (
`feedback_id_`  bigint(20) NOT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`customer_id_`  bigint(20) NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT NULL ,
`feedback_information_`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
PRIMARY KEY (`feedback_id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_i18n`
-- ----------------------------
CREATE TABLE `sm_i18n` (
`id_`  bigint(20) NOT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`key_`  bigint(20) NULL DEFAULT NULL ,
`language_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`module_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`value_`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_language`
-- ----------------------------
CREATE TABLE `sm_language` (
`language_id_`  bigint(20) NOT NULL ,
`code_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`description_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`language_id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_log`
-- ----------------------------
CREATE TABLE `sm_log` (
`log_id_`  bigint(20) NOT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier_`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`content_`  varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`customer_id_`  bigint(20) NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT NULL ,
`fail_information_`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
`ip_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`module_id_`  bigint(20) NULL DEFAULT NULL ,
`request_information_`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
`response_information_`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
`status_`  bit(1) NULL DEFAULT NULL ,
PRIMARY KEY (`log_id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_material`
-- ----------------------------
CREATE TABLE `sm_material` (
`id_`  bigint(20) NOT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`department_id`  bigint(20) NULL DEFAULT NULL ,
`audition_code`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`file_length`  bigint(20) NULL DEFAULT NULL ,
`file_name_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`file_size`  bigint(20) NULL DEFAULT NULL ,
`file_suffixes`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`file_url_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`height`  int(11) NULL DEFAULT NULL ,
`thumbnail`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`type_code`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`width`  int(11) NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_menu`
-- ----------------------------
CREATE TABLE `sm_menu` (
`id_`  bigint(20) NOT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT NULL ,
`icon_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`level_`  int(11) NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`order_no_`  int(11) NULL DEFAULT NULL ,
`parent_id_`  bigint(20) NULL DEFAULT NULL ,
`path_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`privilege_code_`  bigint(20) NULL DEFAULT NULL ,
`remark_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`search_code_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`state_`  bit(1) NULL DEFAULT NULL ,
`system_id_`  bigint(20) NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_privilege`
-- ----------------------------
CREATE TABLE `sm_privilege` (
`id_`  bigint(20) NOT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`privilege_code_`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`description_`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`level_`  int(11) NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`parent_id_`  bigint(20) NULL DEFAULT NULL ,
`search_code_`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`seq_`  int(11) NULL DEFAULT NULL ,
`type_`  int(11) NULL DEFAULT NULL ,
`url_`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`is_log_`  int(11) NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_role`
-- ----------------------------
CREATE TABLE `sm_role` (
`role_id_`  bigint(20) NOT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`description_`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`department_id_`  bigint(20) NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT NULL ,
PRIMARY KEY (`role_id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_system_config`
-- ----------------------------
CREATE TABLE `sm_system_config` (
`sys_key_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL ,
`description_`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`type_`  int(11) NULL DEFAULT NULL ,
`sys_value_`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`sys_key_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_system_info`
-- ----------------------------
CREATE TABLE `sm_system_info` (
`id_`  bigint(20) NOT NULL ,
`create_time_`  datetime NULL DEFAULT NULL ,
`creator_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modifier_`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`code_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`display_`  bit(1) NULL DEFAULT NULL ,
`name_`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`order_no_`  int(11) NULL DEFAULT NULL ,
`remark_`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`url_`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_user`
-- ----------------------------
CREATE TABLE `sm_user` (
`id`  bigint(20) NOT NULL ,
`create_time`  datetime NULL DEFAULT NULL ,
`creator`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`modifier`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`modify_time`  datetime NULL DEFAULT NULL ,
`department_id`  bigint(20) NULL DEFAULT NULL ,
`address`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`birthday`  date NULL DEFAULT NULL ,
`code`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`description`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`email`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`icon`  longtext CHARACTER SET utf8 COLLATE utf8_bin NULL ,
`icon_sm`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`id_number`  varchar(18) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`nickname`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`password`  varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL ,
`phone`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`sex`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`username`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL ,
`display`  bit(1) NULL DEFAULT NULL ,
`locked`  bit(1) NULL DEFAULT NULL ,
PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_user_history_p`
-- ----------------------------
CREATE TABLE `sm_user_history_p` (
`id_`  bigint(20) NOT NULL ,
`modify_time_`  datetime NULL DEFAULT NULL ,
`password_`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`user_id_`  bigint(20) NULL DEFAULT NULL ,
PRIMARY KEY (`id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_user_login_info`
-- ----------------------------
CREATE TABLE `sm_user_login_info` (
`user_id_`  bigint(20) NOT NULL ,
`error_count_`  int(11) NULL DEFAULT NULL ,
`error_time_`  datetime NULL DEFAULT NULL ,
`lock_time_`  datetime NULL DEFAULT NULL ,
`unlock_time_`  datetime NULL DEFAULT NULL ,
PRIMARY KEY (`user_id_`)
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci

;

-- ----------------------------
-- Table structure for `sm_user_ref_role`
-- ----------------------------
CREATE TABLE `sm_user_ref_role` (
`user_id`  bigint(20) NOT NULL ,
`role_id`  bigint(20) NOT NULL ,
FOREIGN KEY (`user_id`) REFERENCES `sm_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
FOREIGN KEY (`role_id`) REFERENCES `sm_role` (`role_id_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
INDEX `FK9nj8c045mhox6t1g6llwfsbln` (`role_id`) USING BTREE ,
INDEX `FK5nyuxma3plgkh5eebjel2yncp` (`user_id`) USING BTREE 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
AUTO_INCREMENT=3

;

-- ----------------------------
-- Table structure for `test_table`
-- ----------------------------
CREATE TABLE `test_table` (
`test_set`  set('') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`test_binary`  varbinary(255) NULL DEFAULT NULL ,
`test_enum`  enum('') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
`test_tiny_int`  tinyint(4) NULL DEFAULT NULL ,
`test_long_text`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
`test_text`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL ,
`test_blob`  blob NULL ,
`test__long_blob`  longblob NULL 
)
ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci
AUTO_INCREMENT=2

;

-- ----------------------------
-- Auto increment value for `hibernate_sequence`
-- ----------------------------
ALTER TABLE `hibernate_sequence` AUTO_INCREMENT=2;

-- ----------------------------
-- Auto increment value for `linkapp_elstic_devenergyreal`
-- ----------------------------
ALTER TABLE `linkapp_elstic_devenergyreal` AUTO_INCREMENT=739;

-- ----------------------------
-- Auto increment value for `linkapp_rule_component`
-- ----------------------------
ALTER TABLE `linkapp_rule_component` AUTO_INCREMENT=48;

-- ----------------------------
-- Auto increment value for `linkapp_tenant_ref_function`
-- ----------------------------
ALTER TABLE `linkapp_tenant_ref_function` AUTO_INCREMENT=1283;

-- ----------------------------
-- Auto increment value for `sm_user_ref_role`
-- ----------------------------
ALTER TABLE `sm_user_ref_role` AUTO_INCREMENT=3;

-- ----------------------------
-- Auto increment value for `test_table`
-- ----------------------------
ALTER TABLE `test_table` AUTO_INCREMENT=2;
