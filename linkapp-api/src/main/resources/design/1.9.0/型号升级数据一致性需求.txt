1.1 涉及 device_unit_id 的表有：
linkage_config_relate_service_parm
linkapp_application_ref_device_unit
linkapp_calculate_config_attribute
linkapp_device
linkapp_device_attribute
linkapp_device_service
linkapp_rule_component
linkapp_tenant_ref_device_unit
rule_execution
rule_trigger

1.2 涉及到device_service_id 的表有：
linkage_config_relate_service_parm
linkapp_device_parm
linkapp_rule_component_service

1.3 涉及到device_parm_id 的表有：
linkage_config_relate_service_parm
linkapp_rule_component_service


2.1目前是型号code+version,需将version 移动到配置表
device_unit_code + device_unit_version：
distribution_cabinet_configuration_expression
distribution_cabinet_type_site

2.2冗余了型号code：
linkapp_calculate_config_attribute



3.vo实体类有型号id,但表中没有device_unit_id 的实体类vo有：

DeviceAttributeStatus
ApplicationRefDeviceUnit

4.规则引擎部分的型号id替换涉及到的表有：
rule_trigger
rule_execution
linkage_config_relate_service_parm
linkapp_rule_component
linkapp_device
linkapp_device_attribute
linkapp_device_service
linkapp_tenant_ref_device_unit
