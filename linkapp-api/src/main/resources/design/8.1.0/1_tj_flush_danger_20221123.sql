# select *
# from app_hidden_danger hd
#          inner join app_danger d on d.id = hd.hidden_danger_id;
# select *
# from app_dangerous_info;
# app_hidden_danger

# select 141 * 715, count(ahd.id)
# from app_hidden_danger ahd outer join app_danger ad
# on ahd.;

alter table app_danger_type
    add column `project_id` varchar(32) DEFAULT NULL COMMENT '项目id';
alter table app_danger_type
    add column `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id';
alter table app_danger_type
    add column `last_push_time` datetime DEFAULT NULL COMMENT '最后同步时间';

INSERT INTO app_danger_type (tenant_id, project_id, parent_id, danger_type_id, full_id, full_name, name, code, `level`)
select temp1.tenant_id,
       temp1.project_id,
       d.parent_id,
       cast(concat(temp1.project_id, '', d.danger_type_id) as signed integer),
       d.full_id,
       d.full_name,
       d.name,
       d.code,
       d.`level`
from (select distinct hd.tenant_id, t.project_id
      from app_hidden_danger hd
               left join linkapp_tenant t on t.id = hd.tenant_id
      where hd.tenant_id is not null) temp1,
     app_danger_type d;

update app_danger_type set parent_id =  cast(concat(project_id, '', parent_id) as signed integer) where parent_id !=0 and project_id is not null;


alter table app_danger
    add column `project_id` varchar(32) DEFAULT NULL COMMENT '项目id';

alter table app_danger
    add column `last_push_time` datetime DEFAULT NULL COMMENT '最后同步时间';

# delete from app_danger where id>715;

INSERT INTO app_danger (id, tenant_id, project_id, the_id, create_time, remark, danger_type_id, full_id,
                        full_name, content, code, order_, `level`, change_limit, delete_status, record_status, points,
                        fine, push_period, related, identify)
select CAST(concat(temp1.project_id, '000', id) AS signed integer),
       temp1.tenant_id,
       temp1.project_id,
       d.the_id,
       d.create_time,
       d.remark,
       d.danger_type_id,
       d.full_id,
       d.full_name,
       d.content,
       d.code,
       d.order_,
       d.`level`,
       d.change_limit,
       d.delete_status,
       d.record_status,
       d.points,
       d.fine,
       d.push_period,
       d.related,
       d.identify
from (select distinct hd.tenant_id, t.project_id
      from app_hidden_danger hd
               left join linkapp_tenant t on t.id = hd.tenant_id
      where hd.tenant_id is not null) temp1,
     app_danger d;

# select concat(project_id,'00000',id) from app_danger where project_id is not null;
# select CAST(concat(project_id,'00000',id) AS signed integer) from app_danger where project_id is not null;

# SELECT CAST('12' AS signed integer);
# select cast ('123' AS int) from app_danger where project_id is not null;


update app_danger
set danger_type_id = CAST(concat(project_id, '', danger_type_id) AS signed integer)
where project_id is not null;


alter table app_hidden_danger
    add column hidden_danger_id_bak varchar(32) DEFAULT NULL COMMENT '隐患库隐患id bak';
update app_hidden_danger
set hidden_danger_id_bak = hidden_danger_id;

update app_hidden_danger ahd inner join linkapp_tenant lt on ahd.tenant_id = lt.id
set ahd.hidden_danger_id = CAST(concat(lt.project_id, '000', ahd.hidden_danger_id_bak) AS signed integer);

alter table app_danger modify column `code` varchar(16) default NULL;
alter table app_danger modify column `order_` int(2) default NULL COMMENT '排序';
alter table app_danger modify column `the_id` int(11) default NULL COMMENT '原id有重复';

-- 检查刷完数据后 数据是不是一致的
select d1.full_name,d2.full_name from app_hidden_danger hd
    left join app_danger d1 on d1.id = hd.hidden_danger_id_bak
    left join app_danger d2 on d2.id = hd.hidden_danger_id where d1.full_name!=d2.full_name;





