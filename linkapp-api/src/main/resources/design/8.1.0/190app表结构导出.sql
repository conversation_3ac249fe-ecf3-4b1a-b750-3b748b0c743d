-- linkappdb.ac_aircondition_device definition

CREATE TABLE `ac_aircondition_device` (
                                          `device_id` varchar(32) NOT NULL DEFAULT '' COMMENT '设备编号',
                                          `device_code` varchar(32) DEFAULT NULL COMMENT '设备编码',
                                          `device_name` varchar(32) DEFAULT NULL COMMENT '设备名称',
                                          `host_device_id` varchar(32) NOT NULL COMMENT '主机设备编号',
                                          `low_fan` decimal(6,1) DEFAULT NULL COMMENT '低风P',
                                          `mid_fan` decimal(6,1) DEFAULT NULL COMMENT '中风P',
                                          `high_fan` decimal(6,1) DEFAULT NULL COMMENT '高风P',
                                          `tenant_id` varchar(32) NOT NULL DEFAULT '' COMMENT '租户id',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          PRIMARY KEY (`device_id`,`tenant_id`,`host_device_id`),
                                          KEY `host_device_id_index` (`host_device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='空调子设备表';


-- linkappdb.ac_aircondition_device_record definition

CREATE TABLE `ac_aircondition_device_record` (
                                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '设备编号',
                                                 `aircondition_device_id` varchar(32) NOT NULL COMMENT '空调子设备表id',
                                                 `type` tinyint(1) DEFAULT NULL COMMENT '当前模式 1--制冷 2--制热',
                                                 `fan_speed` tinyint(1) DEFAULT NULL COMMENT '1--低风 2--中风 3--高风',
                                                 `duration` decimal(6,2) DEFAULT NULL COMMENT '时长（分钟）',
                                                 `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                 `complete_status` tinyint(1) DEFAULT NULL COMMENT '完成状态 0 未完成，1已完成',
                                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                 PRIMARY KEY (`id`),
                                                 KEY `aircondition_device_id_index` (`aircondition_device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='空调子设备出风模式分段记录表';


-- linkappdb.ac_host_device definition

CREATE TABLE `ac_host_device` (
                                  `device_id` varchar(32) NOT NULL DEFAULT '' COMMENT '设备编号',
                                  `device_code` varchar(32) DEFAULT NULL COMMENT '设备编码',
                                  `device_name` varchar(32) DEFAULT NULL COMMENT '设备名称',
                                  `wattmeter_id` varchar(32) DEFAULT NULL COMMENT '电表设备编号',
                                  `switch_state` tinyint(1) DEFAULT NULL COMMENT '当前开机状态 1:关机,2:开机',
                                  `tenant_id` varchar(32) NOT NULL DEFAULT '' COMMENT '租户id',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`device_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主机表';


-- linkappdb.afterloan_enterpriseinfo definition

CREATE TABLE `afterloan_enterpriseinfo` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                            `enterprise_name` varchar(50) NOT NULL COMMENT '公司名称',
                                            `lar_name` varchar(50) DEFAULT NULL COMMENT '法人代表名称',
                                            `registe_capital` varchar(32) DEFAULT NULL COMMENT '注册资金',
                                            `enterprise_code` varchar(20) NOT NULL COMMENT '统一社会信用代码',
                                            `establish_time` datetime DEFAULT NULL COMMENT '成立时间',
                                            `address` varchar(255) NOT NULL COMMENT '注册地址',
                                            `image_url` varchar(255) DEFAULT NULL COMMENT 'logo地址',
                                            `data_source_id` varchar(32) DEFAULT NULL COMMENT '绑定数据源ID',
                                            `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                            `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                            PRIMARY KEY (`id`),
                                            KEY `tenant_id_index` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业信息表';


-- linkappdb.afterloan_risk_number definition

CREATE TABLE `afterloan_risk_number` (
                                         `enterprise_id` bigint(20) NOT NULL COMMENT '企业编号',
                                         `risk_type_id` bigint(20) NOT NULL COMMENT '风险类型编号',
                                         `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                         `number` int(4) DEFAULT '0' COMMENT '数量',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                         `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                         PRIMARY KEY (`enterprise_id`,`risk_type_id`,`tenant_id`),
                                         KEY `tenant_id_index` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风险详情数量表';


-- linkappdb.afterloan_risk_type definition

CREATE TABLE `afterloan_risk_type` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                       `risk_type` tinyint(1) DEFAULT NULL COMMENT '0：物联网风险，1：自身风险，2：关联风险',
                                       `risk_code` tinyint(2) DEFAULT NULL COMMENT 'code',
                                       `api_url` varchar(32) DEFAULT NULL COMMENT 'api地址',
                                       `risk_name` varchar(50) NOT NULL COMMENT '名称',
                                       `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                       `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                       PRIMARY KEY (`id`),
                                       KEY `tenant_id_index` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风险分类表';


-- linkappdb.app_age_warning definition

CREATE TABLE `app_age_warning` (
                                   `id` varchar(32) NOT NULL COMMENT 'ID',
                                   `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                   `user_id_` varchar(32) DEFAULT NULL COMMENT '人员id',
                                   `warning_time_` datetime DEFAULT NULL COMMENT '预警时间',
                                   `warning_rule_` varchar(500) DEFAULT NULL COMMENT '预警详情',
                                   `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                   `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                   `remark_` text COMMENT '备注',
                                   `status_` int(10) DEFAULT '0' COMMENT '处理状态(1已处理 0未处理)',
                                   `handle_time_` datetime DEFAULT NULL COMMENT '处理时间',
                                   `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注',
                                   `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人',
                                   `operate_type` int(1) DEFAULT NULL COMMENT '操作类型，1退场，2忽略',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='年龄预警表';


-- linkappdb.app_ai_alarm_count definition

CREATE TABLE `app_ai_alarm_count` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                      `coordinate_` text COMMENT '区域坐标json',
                                      `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                      `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                      `remark_` text COMMENT '备注',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='ai相机预警统计';


-- linkappdb.app_build_check_info definition

CREATE TABLE `app_build_check_info` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `check_time` datetime DEFAULT NULL COMMENT '检查时间',
                                        `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                        `tenant_project_id` bigint(20) DEFAULT NULL COMMENT '租户项目id',
                                        `check_title` varchar(200) DEFAULT NULL COMMENT '检查标题',
                                        `check_content` varchar(1000) DEFAULT NULL COMMENT '检查内容',
                                        `check_level` int(11) DEFAULT NULL COMMENT '检查级别',
                                        `check_org_code` varchar(100) DEFAULT NULL COMMENT '检查组织编码',
                                        `check_org_name` varchar(200) DEFAULT NULL COMMENT '检查组织名称',
                                        `check_type` int(11) DEFAULT NULL COMMENT '检查类别，1质量 2安全',
                                        `check_model` int(11) DEFAULT NULL COMMENT '检查类型',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                        `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                        `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0:已删; 1:存在',
                                        `phid` bigint(20) DEFAULT NULL COMMENT '新中大现场检查主表id',
                                        PRIMARY KEY (`id`),
                                        KEY `app_build_check_info_tenant_project_id_IDX` (`tenant_project_id`) USING BTREE,
                                        KEY `app_build_check_info_phid_IDX` (`phid`) USING BTREE,
                                        KEY `app_build_check_info_tenant_id_IDX` (`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=663 DEFAULT CHARSET=utf8 COMMENT='施工检查信息';


-- linkappdb.app_building definition

CREATE TABLE `app_building` (
                                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                `name` varchar(30) NOT NULL COMMENT '单体名称',
                                `total_building_height` float(11,2) DEFAULT NULL COMMENT '总建筑高度',
                                `total_floor_height` float(11,2) DEFAULT NULL COMMENT '总楼层高度',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                PRIMARY KEY (`id`) USING BTREE,
                                UNIQUE KEY `app_building_name_IDX` (`name`,`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8 COMMENT='单体';


-- linkappdb.app_certificate_warning definition

CREATE TABLE `app_certificate_warning` (
                                           `id` varchar(32) NOT NULL COMMENT 'ID',
                                           `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                           `user_id_` varchar(32) DEFAULT NULL COMMENT '用户userID ',
                                           `certificate_id_` varchar(32) DEFAULT NULL COMMENT '证书ID ',
                                           `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                                           `no_` varchar(64) DEFAULT NULL COMMENT '编号',
                                           `type_` varchar(32) DEFAULT NULL COMMENT '证书类型 字典管理',
                                           `level_` varchar(32) DEFAULT NULL COMMENT '证书等级 字典管理',
                                           `residue_day_` int(2) DEFAULT NULL COMMENT '剩余天数',
                                           `warning_time_` datetime DEFAULT NULL COMMENT '预警时间',
                                           `warning_rule_` varchar(500) DEFAULT NULL COMMENT '预警详情',
                                           `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                           `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                           `remark_` text COMMENT '备注',
                                           `status_` int(10) DEFAULT '0' COMMENT '处理状态(1已处理 0未处理)',
                                           `handle_time_` datetime DEFAULT NULL COMMENT '处理时间',
                                           `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注',
                                           `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人',
                                           `operate_type` int(1) DEFAULT NULL COMMENT '操作类型，1上传、2忽略',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='证书预警表';


-- linkappdb.app_check_standard definition

CREATE TABLE `app_check_standard` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id，冗余',
                                      `parent_id` int(11) DEFAULT NULL COMMENT '父级id',
                                      `parent_ids` varchar(4000) DEFAULT NULL COMMENT '父级ids，以，隔开',
                                      `standard_name` varchar(100) DEFAULT NULL COMMENT '标准名称',
                                      `standard_val` varchar(100) DEFAULT NULL COMMENT '标准值',
                                      `standard_code` varchar(100) DEFAULT NULL COMMENT '标准编码',
                                      `standard_unit` varchar(100) DEFAULT NULL COMMENT '标准单位',
                                      `type` int(2) DEFAULT NULL COMMENT '分类，1混凝土强度等级',
                                      `use_status` int(1) DEFAULT NULL COMMENT '使用状态，0停用，1使用',
                                      `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                      PRIMARY KEY (`id`),
                                      KEY `app_check_standard_type_IDX` (`type`) USING BTREE,
                                      KEY `app_check_standard_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8 COMMENT='检测标准表';


-- linkappdb.app_clock_config definition

CREATE TABLE `app_clock_config` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id(为null时为默认数据)',
                                    `name_` varchar(128) DEFAULT NULL COMMENT '名称',
                                    `hour_` double DEFAULT NULL COMMENT '工日工时比例',
                                    `count_type_` int(2) DEFAULT NULL COMMENT '考勤计算方式(1未减初 2时间累加)',
                                    `clock_type_` int(2) DEFAULT NULL COMMENT '打卡方法(1一天一卡 2一天两卡)',
                                    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                    `remark_` text COMMENT '备注',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='考勤规则';


-- linkappdb.app_concrete_strength_detail definition

CREATE TABLE `app_concrete_strength_detail` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                `info_id` bigint(20) DEFAULT NULL COMMENT '混凝土强度检测主表id',
                                                `check_face` varchar(100) DEFAULT NULL COMMENT '测面',
                                                `check_angle` varchar(100) DEFAULT NULL COMMENT '测试角度',
                                                `standard_diff` varchar(100) DEFAULT NULL COMMENT '强度标准差',
                                                `data_type` int(1) DEFAULT NULL COMMENT '数据类型，1回弹值，2碳化深度',
                                                `check_area` varchar(100) DEFAULT NULL COMMENT '测区',
                                                `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                PRIMARY KEY (`id`),
                                                KEY `app_concrete_strength_detail_info_id_IDX` (`info_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8 COMMENT='混凝土强度检测详情';


-- linkappdb.app_concrete_strength_info definition

CREATE TABLE `app_concrete_strength_info` (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                              `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
                                              `status` int(1) DEFAULT NULL COMMENT '状态，1合格，2待复查',
                                              `position_id` int(11) DEFAULT NULL COMMENT '部位id',
                                              `check_device_code` varchar(100) DEFAULT NULL COMMENT '检测设备编码',
                                              `member_code` varchar(100) DEFAULT NULL COMMENT '构建编码',
                                              `member_type` varchar(100) DEFAULT NULL COMMENT '构建类型',
                                              `member_name` varchar(100) DEFAULT NULL COMMENT '构建类型名称',
                                              `age` varchar(100) DEFAULT NULL COMMENT '期龄',
                                              `check_area_num` int(11) DEFAULT NULL COMMENT '测区数量',
                                              `water_time` datetime DEFAULT NULL COMMENT '浇筑日期',
                                              `check_time` datetime DEFAULT NULL COMMENT '检测日期',
                                              `design_strength_id` int(11) DEFAULT NULL COMMENT '设计强度等级主键，标准表',
                                              `design_strength_code` varchar(20) DEFAULT NULL COMMENT '设计强度编码',
                                              `strength_presumption_val` varchar(100) DEFAULT NULL COMMENT '强度推定值',
                                              `strength_presumption_unit` varchar(100) DEFAULT NULL COMMENT '强度推定单位',
                                              `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                              `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                              PRIMARY KEY (`id`),
                                              KEY `app_concrete_strength_info_tenant_id_IDX` (`tenant_id`) USING BTREE,
                                              KEY `app_concrete_strength_info_position_id_IDX` (`position_id`) USING BTREE,
                                              KEY `app_concrete_strength_info_design_strength_id_IDX` (`design_strength_id`) USING BTREE,
                                              KEY `app_concrete_strength_info_status_IDX` (`status`) USING BTREE,
                                              KEY `app_concrete_strength_info_check_time_IDX` (`check_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COMMENT='混凝土强度检测';


-- linkappdb.app_concrete_strength_record definition

CREATE TABLE `app_concrete_strength_record` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                `info_id` bigint(20) DEFAULT NULL COMMENT '主记录主键，冗余',
                                                `detail_id` bigint(20) DEFAULT NULL COMMENT '详情主键',
                                                `check_no` varchar(100) DEFAULT NULL COMMENT '测试号',
                                                `check_val` varchar(100) DEFAULT NULL COMMENT '测试值',
                                                `check_unit` varchar(100) DEFAULT NULL COMMENT '测试单位',
                                                `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                PRIMARY KEY (`id`),
                                                KEY `app_concrete_strength_record_detail_id_IDX` (`detail_id`) USING BTREE,
                                                KEY `app_concrete_strength_record_info_id_IDX` (`info_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8 COMMENT='混凝土强度检测值记录';


-- linkappdb.app_config definition

CREATE TABLE `app_config` (
                              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                              `key_` varchar(100) NOT NULL COMMENT '配置关键字',
                              `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                              `value_` text NOT NULL COMMENT '配置的值',
                              `example` text NOT NULL COMMENT '样例',
                              `module_level` varchar(200) NOT NULL COMMENT '模块层级',
                              `describe_` varchar(500) NOT NULL COMMENT '描述',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `uni_key_tenant_id` (`key_`,`tenant_id`) COMMENT '关键字在租户下唯一'
) ENGINE=InnoDB AUTO_INCREMENT=513 DEFAULT CHARSET=utf8 COMMENT='配置中心';


-- linkappdb.app_construction_album definition

CREATE TABLE `app_construction_album` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
                                          `data_type` int(1) DEFAULT NULL COMMENT '类型，1相册，2事件、3照片',
                                          `name` varchar(50) DEFAULT NULL COMMENT '名称',
                                          `code` varchar(50) DEFAULT NULL COMMENT '编码',
                                          `parent_id` bigint(20) DEFAULT NULL COMMENT '父级id',
                                          `parent_ids` varchar(4000) DEFAULT NULL COMMENT '父级ids，以，隔开',
                                          `source_url` varchar(400) DEFAULT NULL COMMENT '资源url',
                                          `remark` varchar(300) DEFAULT NULL COMMENT '描述',
                                          `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                          `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                          PRIMARY KEY (`id`),
                                          KEY `app_construction_album_tenant_id_IDX` (`tenant_id`) USING BTREE,
                                          KEY `app_construction_album_data_type_IDX` (`data_type`) USING BTREE,
                                          KEY `app_construction_album_parent_id_IDX` (`parent_id`) USING BTREE,
                                          KEY `app_construction_album_name_IDX` (`name`) USING BTREE,
                                          KEY `app_construction_album_create_time_IDX` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8 COMMENT='施工相册表';


-- linkappdb.app_danger definition

CREATE TABLE `app_danger` (
                              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                              `the_id` int(11) NOT NULL COMMENT '原id有重复',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                              `remark` varchar(200) DEFAULT NULL COMMENT '备注',
                              `danger_type_id` int(11) NOT NULL COMMENT '隐患类型id',
                              `full_id` varchar(64) NOT NULL COMMENT 'full',
                              `full_name` varchar(128) NOT NULL COMMENT '全名',
                              `content` varchar(128) NOT NULL COMMENT '内容',
                              `code` varchar(16) NOT NULL,
                              `order_` int(2) NOT NULL COMMENT '排序',
                              `level` int(2) NOT NULL COMMENT '等级',
                              `change_limit` int(2) NOT NULL,
                              `delete_status` int(2) NOT NULL COMMENT '删除状态 0-已删除，1-存在',
                              `record_status` int(2) NOT NULL COMMENT '状态 0-正常',
                              `points` int(2) DEFAULT NULL,
                              `fine` int(2) DEFAULT NULL,
                              `push_period` int(2) DEFAULT NULL,
                              `related` int(2) DEFAULT NULL COMMENT '是否关联规范 0-未关联',
                              `identify` varchar(32) DEFAULT NULL,
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=716 DEFAULT CHARSET=utf8 COMMENT='隐患库';


-- linkappdb.app_danger_type definition

CREATE TABLE `app_danger_type` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `parent_id` int(11) NOT NULL COMMENT '父id',
                                   `danger_type_id` int(11) NOT NULL COMMENT '隐患类型id',
                                   `full_id` varchar(64) NOT NULL COMMENT '原全称id',
                                   `full_name` varchar(128) NOT NULL COMMENT '全名',
                                   `name` varchar(128) NOT NULL COMMENT '名称',
                                   `code` varchar(16) NOT NULL COMMENT '编码',
                                   `level` int(2) NOT NULL COMMENT '层级',
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `app_danger_type_un` (`danger_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=387 DEFAULT CHARSET=utf8 COMMENT='隐患类型';


-- linkappdb.app_dangerous_danger_ref definition

CREATE TABLE `app_dangerous_danger_ref` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `dangerous_id` bigint(20) DEFAULT NULL COMMENT '危大工程id',
                                            `danger_id` varchar(32) DEFAULT NULL COMMENT '隐患id',
                                            `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                            PRIMARY KEY (`id`),
                                            KEY `app_dangerous_danger_ref_dangerous_id_IDX` (`dangerous_id`) USING BTREE,
                                            KEY `app_dangerous_danger_ref_danger_id_IDX` (`danger_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8 COMMENT='危大工程过程检查与隐患关系';


-- linkappdb.app_dangerous_dict definition

CREATE TABLE `app_dangerous_dict` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `memo` varchar(300) DEFAULT NULL COMMENT '名称/描述',
                                      `parent_id` bigint(20) DEFAULT NULL COMMENT '父级id',
                                      `dangerous_type` int(1) DEFAULT NULL COMMENT '类型，1分类，2类别描述',
                                      `level` int(1) DEFAULT NULL COMMENT '层级',
                                      `super_dangerous` int(1) DEFAULT NULL COMMENT '是否超危，0否，1是',
                                      `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
                                      `use_state` int(1) DEFAULT NULL COMMENT '使用状态，1启用，0停用',
                                      `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                      `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                      PRIMARY KEY (`id`),
                                      KEY `app_dangerous_project_dict_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8 COMMENT='危大工程字典';


-- linkappdb.app_dangerous_file definition

CREATE TABLE `app_dangerous_file` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `dangerous_id` bigint(20) DEFAULT NULL COMMENT '危大id',
                                      `process_id` bigint(20) DEFAULT NULL COMMENT '过程id',
                                      `step_no` int(11) DEFAULT NULL COMMENT '步骤号，冗余',
                                      `file_name` varchar(200) DEFAULT NULL COMMENT '文件名称',
                                      `file_url` varchar(300) DEFAULT NULL COMMENT '文件url',
                                      `file_size` int(11) DEFAULT NULL COMMENT '文件大小',
                                      `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                      `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                      PRIMARY KEY (`id`),
                                      KEY `app_dangerous_file_dangerous_id_IDX` (`dangerous_id`) USING BTREE,
                                      KEY `app_dangerous_file_process_id_IDX` (`process_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=108 DEFAULT CHARSET=utf8 COMMENT='危大工程过程文件信息';


-- linkappdb.app_dangerous_info definition

CREATE TABLE `app_dangerous_info` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `tenant_id` varchar(100) DEFAULT NULL COMMENT 'tenant_id',
                                      `name` varchar(300) DEFAULT NULL COMMENT '危大工程名称',
                                      `describe_str` varchar(500) DEFAULT NULL COMMENT '危大工程概述',
                                      `type_id` bigint(20) DEFAULT NULL COMMENT '危大工程类别id',
                                      `type_describe_id` bigint(20) DEFAULT NULL COMMENT '危大工程类别描述id',
                                      `super_dangerous` int(1) DEFAULT NULL COMMENT '是否超危，0否，1是',
                                      `work_area_responsible` varchar(500) DEFAULT NULL COMMENT '工区责任人,多个以，隔开',
                                      `sub_area_responsible` varchar(500) DEFAULT NULL COMMENT '分包责任人,多个以，隔开',
                                      `sub_responsible_org` varchar(500) DEFAULT NULL COMMENT '责任分包单位,多个以，隔开',
                                      `plan_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
                                      `plan_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
                                      `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
                                      `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
                                      `program_state` int(2) DEFAULT NULL COMMENT '方案状态，1未完成，2已完成',
                                      `construction_state` int(2) DEFAULT NULL COMMENT '施工状态，1未开工，2在施，3完工',
                                      `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                      `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8 COMMENT='危大工程信息';


-- linkappdb.app_dangerous_process definition

CREATE TABLE `app_dangerous_process` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         `dangerous_id` bigint(20) DEFAULT NULL COMMENT '危大工程id',
                                         `process_name` varchar(300) DEFAULT NULL COMMENT '过程名称',
                                         `step_no` int(2) DEFAULT NULL COMMENT '步骤号',
                                         `process_state` int(1) DEFAULT NULL COMMENT '状态',
                                         `change_end_time` datetime DEFAULT NULL COMMENT '改成完成状态的时间',
                                         `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                         `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `delete_state` int(2) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                         PRIMARY KEY (`id`),
                                         KEY `app_dangerous_project_material_dangerous_project_id_IDX` (`dangerous_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8 COMMENT='危大工程过程';


-- linkappdb.app_device_position definition

CREATE TABLE `app_device_position` (
                                       `device_code` varchar(32) NOT NULL COMMENT '设备编码',
                                       `position_x` float(6,2) NOT NULL COMMENT '电量',
                                       `position_y` float(6,2) NOT NULL COMMENT '电量',
                                       `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                       `model_id_` tinyint(4) NOT NULL COMMENT '模块id，0为环境模块,-1为配电箱',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                       `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                       `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `is_remove_` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否从模块中移除 0未移除 1已移除',
                                       PRIMARY KEY (`device_code`),
                                       KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备在图纸上的位置';


-- linkappdb.app_device_position_image definition

CREATE TABLE `app_device_position_image` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                             `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                             `url_` text COMMENT '图纸url',
                                             `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                             `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                             `remark_` text COMMENT '备注',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='大屏图纸';


-- linkappdb.app_electric_box definition

CREATE TABLE `app_electric_box` (
                                    `id` varchar(32) NOT NULL COMMENT 'ID',
                                    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                    `code_` varchar(32) DEFAULT NULL COMMENT '设备编号',
                                    `type_` int(10) DEFAULT NULL COMMENT '设备类型(0主配电箱,1一级配电箱,2二级配电箱)',
                                    `position_` varchar(50) DEFAULT NULL COMMENT '设置位置',
                                    `lead_id_` varchar(32) DEFAULT NULL COMMENT '负责人id',
                                    `circuitry_` varchar(255) DEFAULT NULL COMMENT '电路图url',
                                    `electrician_` varchar(255) DEFAULT NULL COMMENT '电工证url',
                                    `qr_code` varchar(255) DEFAULT NULL COMMENT '二维码url',
                                    `monitor_device_code_` varchar(100) DEFAULT NULL COMMENT '监控电气火灾设备',
                                    `is_delete_` int(10) DEFAULT NULL COMMENT '1已删除 0未删除',
                                    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                    `remark_` text COMMENT '备注',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配电箱表';


-- linkappdb.app_electric_box_record definition

CREATE TABLE `app_electric_box_record` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                           `electric_box_id_` varchar(32) DEFAULT NULL COMMENT '配电箱ID ',
                                           `device_code` varchar(32) DEFAULT NULL COMMENT '设备id',
                                           `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                           `temperature_1` double(20,2) DEFAULT NULL COMMENT '通道1温度 单位℃',
                                           `temperature_2` double(20,2) DEFAULT NULL COMMENT '通道2温度 单位℃',
                                           `temperature_3` double(20,2) DEFAULT NULL COMMENT '通道3温度 单位℃',
                                           `temperature_4` double(20,2) DEFAULT NULL COMMENT '通道4温度 单位℃',
                                           `active_power` double(20,2) DEFAULT NULL COMMENT '有功总功率 单位W',
                                           `residual_current` double(20,2) DEFAULT NULL COMMENT '剩余电流 单位mA',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           PRIMARY KEY (`id`),
                                           KEY `app_electric_box_record_electric_box_id_IDX` (`electric_box_id_`),
                                           KEY `app_electric_box_record_device_code_IDX` (`device_code`),
                                           KEY `app_electric_box_record_tenant_id_IDX` (`tenant_id_`)
) ENGINE=InnoDB AUTO_INCREMENT=164 DEFAULT CHARSET=utf8 COMMENT='配电箱数据记录';


-- linkappdb.app_electric_check definition

CREATE TABLE `app_electric_check` (
                                      `id` varchar(32) NOT NULL COMMENT 'ID',
                                      `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)，为null时为共有的默认数据',
                                      `electric_box_id_` varchar(32) DEFAULT NULL COMMENT '配电箱ID ',
                                      `check_time_` datetime DEFAULT NULL COMMENT '巡检时间',
                                      `check_user_id_` bigint(20) DEFAULT NULL COMMENT '提交人id',
                                      `position_` varchar(50) DEFAULT NULL COMMENT '地理位置',
                                      `photo_` varchar(255) DEFAULT NULL COMMENT '相片url,多张逗号分隔',
                                      `content_` text COMMENT '巡检提交内容json',
                                      `status_` int(2) DEFAULT NULL COMMENT '巡检状态 0正常 1异常',
                                      `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                      `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                      `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                      `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                      `remark_` text COMMENT '备注',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配电箱巡检表';


-- linkappdb.app_electric_option definition

CREATE TABLE `app_electric_option` (
                                       `id` varchar(32) NOT NULL COMMENT 'ID',
                                       `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)，为null时为共有的默认数据',
                                       `content_` varchar(32) NOT NULL COMMENT '巡检项内容',
                                       `remark_` text COMMENT '备注',
                                       `status_` int(10) DEFAULT NULL COMMENT '状态(1启用0停用)',
                                       `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                       `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                       `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                       `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配电箱巡检项表';


-- linkappdb.app_electric_statistics definition

CREATE TABLE `app_electric_statistics` (
                                           `id` varchar(32) NOT NULL COMMENT 'ID',
                                           `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                           `record_time_` datetime DEFAULT NULL COMMENT '考勤日期',
                                           `sum_` int(10) DEFAULT NULL COMMENT '电箱总数',
                                           `done_` int(10) DEFAULT NULL COMMENT '已巡检数',
                                           `sum_id_` text COMMENT '所有配电箱的id集合，逗号隔开',
                                           `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                           `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                           `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                           `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                           `remark_` text COMMENT '备注',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配电箱巡检统计表';


-- linkappdb.app_electricy_records definition

CREATE TABLE `app_electricy_records` (
                                         `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                         `device_code` varchar(32) NOT NULL COMMENT '设备编码',
                                         `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                         `collect_time` datetime DEFAULT NULL COMMENT '采集时间',
                                         `electricy_increment` double(20,2) DEFAULT NULL COMMENT '用电增量',
                                         `electricy_total` double(20,2) DEFAULT NULL COMMENT '累计用电量',
                                         `stop_reading` double(20,2) DEFAULT NULL COMMENT '止码读数',
                                         `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                         `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                         `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                         `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                         `remark_` text COMMENT '备注',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用电记录';


-- linkappdb.app_elevator_floor_record definition

CREATE TABLE `app_elevator_floor_record` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `machinery_id` int(11) DEFAULT NULL COMMENT '电子档案id',
                                             `device_code` varchar(32) DEFAULT NULL COMMENT '设备code',
                                             `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                             `open_time` datetime DEFAULT NULL COMMENT '开门时间',
                                             `close_time` datetime DEFAULT NULL COMMENT '关门时间',
                                             `door_status` int(2) DEFAULT NULL COMMENT '开关状态 0-关，1-开',
                                             `height_` double(20,2) DEFAULT NULL COMMENT '高度 单位m',
                                             `building_id` int(11) DEFAULT NULL COMMENT '单体id',
                                             `building_name` varchar(32) DEFAULT NULL COMMENT '单体名称',
                                             `floor_id` int(11) DEFAULT NULL COMMENT '楼层id',
                                             `floor_name` varchar(32) DEFAULT NULL COMMENT '楼层名称',
                                             `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                             `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                             `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                             `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                             `remark_` text COMMENT '备注',
                                             PRIMARY KEY (`id`),
                                             KEY `app_elevator_floor_record_machinery_id_IDX` (`machinery_id`) USING BTREE,
                                             KEY `app_elevator_floor_record_device_code_IDX` (`device_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='升降机楼层驻留记录';


-- linkappdb.app_elevator_work_record definition

CREATE TABLE `app_elevator_work_record` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `machinery_id` int(11) DEFAULT NULL COMMENT '电子档案id',
                                            `device_code` varchar(32) DEFAULT NULL COMMENT '设备code',
                                            `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                            `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                            `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                            `driver_name` varchar(32) DEFAULT NULL COMMENT '驾驶员姓名',
                                            `max_weight` double(20,2) DEFAULT NULL COMMENT '最大载重 单位kg',
                                            `weight_percentage` double(20,2) DEFAULT NULL COMMENT '载重百分比 单位%',
                                            `start_height` double(20,2) DEFAULT NULL COMMENT '起点高度 单位m',
                                            `end_height` double(20,2) DEFAULT NULL COMMENT '终点高度 单位m',
                                            `stroke_height` double(20,2) DEFAULT NULL COMMENT '行程高度 单位m',
                                            `rise_direction` int(2) DEFAULT NULL COMMENT '起升方向 0-停止，1-上，2-下，不可能取值为停止',
                                            `average_speed` double(20,2) DEFAULT NULL COMMENT '平均速度 单位m/s',
                                            `max_tilt_x` double(20,2) DEFAULT NULL COMMENT '最大倾斜X 单位°',
                                            `max_tilt_y` double(20,2) DEFAULT NULL COMMENT '最大倾斜Y 单位°',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            PRIMARY KEY (`id`),
                                            KEY `app_elevator_work_record_machinery_id_IDX` (`machinery_id`) USING BTREE,
                                            KEY `app_elevator_work_record_device_code_IDX` (`device_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COMMENT='升降机工作循环记录';


-- linkappdb.app_environmental_area definition

CREATE TABLE `app_environmental_area` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                          `parent_id_` int(11) DEFAULT NULL COMMENT '父id(一级父类别为0)',
                                          `full_id_` varchar(64) DEFAULT NULL COMMENT '父路径id',
                                          `full_name_` varchar(500) DEFAULT NULL COMMENT '全名',
                                          `name_` varchar(128) DEFAULT NULL COMMENT '名称',
                                          `code_` varchar(16) DEFAULT NULL COMMENT '编码',
                                          `level_` int(2) DEFAULT NULL COMMENT '层级',
                                          `order_` int(2) DEFAULT NULL COMMENT '排序/编码使用(冗余)',
                                          `type_` int(2) DEFAULT '1' COMMENT '类别(1.用水 2.用电)',
                                          `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                          `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                          `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                          `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                          `remark_` text COMMENT '备注/说明',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8 COMMENT='环境管理区域表';


-- linkappdb.app_epidemic_warning definition

CREATE TABLE `app_epidemic_warning` (
                                        `id` varchar(32) NOT NULL COMMENT 'ID',
                                        `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                        `user_id_` varchar(32) DEFAULT NULL COMMENT '人员id',
                                        `warning_time_` datetime DEFAULT NULL COMMENT '预警时间',
                                        `warning_rule_` varchar(500) DEFAULT NULL COMMENT '预警详情',
                                        `temperature_` double DEFAULT NULL COMMENT '体温',
                                        `health_code_` int(2) DEFAULT NULL COMMENT '健康码(1绿码，2黄码，3灰码，4红码)',
                                        `type_` int(2) DEFAULT NULL COMMENT '预警类型(1体温预警, 2健康码预警, 3体温和健康码预警)',
                                        `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                        `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                        `remark_` text COMMENT '备注',
                                        `status_` int(10) DEFAULT '0' COMMENT '处理状态(1已处理 0未处理)',
                                        `handle_time_` datetime DEFAULT NULL COMMENT '处理时间',
                                        `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注',
                                        `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人',
                                        `operate_type` int(1) DEFAULT NULL COMMENT '操作类型，1已读，2保留',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='疫情监测预警表';


-- linkappdb.app_floor definition

CREATE TABLE `app_floor` (
                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                             `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                             `name` varchar(30) NOT NULL COMMENT '楼层名称',
                             `building_id` int(11) NOT NULL COMMENT '单体id',
                             `type` smallint(2) DEFAULT NULL COMMENT '楼层类型，0-基础层，1-首层,2-普通层',
                             `building_floor_height` float(11,2) NOT NULL COMMENT '建筑层高',
                             `structure_floor_height` float(11,2) NOT NULL COMMENT '结构层高',
                             `building_standard_height` float(11,2) DEFAULT NULL COMMENT '建筑底标高',
                             `structure_standard_height` float(11,2) DEFAULT NULL COMMENT '结构底标高',
                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                             `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                             `creator` varchar(30) DEFAULT NULL COMMENT '创建人',
                             `modifier` varchar(30) DEFAULT NULL COMMENT '修改人',
                             `sort_no` int(3) NOT NULL COMMENT '排序号',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE KEY `app_floor_name_IDX` (`name`,`building_id`) USING BTREE,
                             KEY `app_floor_building_id_IDX` (`building_id`) USING BTREE,
                             KEY `app_floor_tenant_id_IDX` (`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=240 DEFAULT CHARSET=utf8 COMMENT='楼层信息';


-- linkappdb.app_gate definition

CREATE TABLE `app_gate` (
                            `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                            `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                            `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                            `brand_` varchar(32) DEFAULT NULL COMMENT '品牌',
                            `code_` varchar(32) DEFAULT NULL COMMENT '序列号',
                            `position_` varchar(32) DEFAULT NULL COMMENT '位置',
                            `status_` int(1) DEFAULT NULL COMMENT '状态 1在线0离线',
                            `direction_` int(10) DEFAULT NULL COMMENT '方向 1进门2出门',
                            `health_temperature_` int(10) DEFAULT '1' COMMENT '是否支付健康码测温1支持 0不支持',
                            `device_id_` varchar(32) DEFAULT NULL COMMENT '设备id',
                            `blade_guard_bind_status_` int(1) DEFAULT '0' COMMENT '是否绑定工地卫士 1是0否',
                            `blade_guard_project_id_` varchar(32) DEFAULT NULL COMMENT '工地卫士 projectID',
                            `blade_guard_user_id` varchar(32) DEFAULT NULL COMMENT '工地卫士 userId',
                            `blade_guard_jing_du` varchar(32) DEFAULT NULL COMMENT '闸机 经度',
                            `blade_guard_wei_du` varchar(32) DEFAULT NULL COMMENT '闸机 纬度',
                            `blade_guard_qrcode` varchar(512) DEFAULT NULL COMMENT '工地卫士二维码',
                            `blade_guard_project_name` varchar(256) DEFAULT NULL COMMENT '工地卫士 项目名称',
                            `blade_guard_project_address` varchar(256) DEFAULT NULL COMMENT '工地卫士 项目地址',
                            `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                            `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                            `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                            `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                            `remark_` text COMMENT '备注',
                            `service_area` int(1) DEFAULT NULL COMMENT '服务区域：1施工区，2办公区，3生活区',
                            `access_` varchar(32) DEFAULT NULL COMMENT '通行管理(多选)：1施工云，2武汉实名制平台',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='闸机表';


-- linkappdb.app_group definition

CREATE TABLE `app_group` (
                             `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                             `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                             `company_project_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司与项目中间表id',
                             `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                             `main_people_` varchar(32) DEFAULT NULL COMMENT '责任人名称',
                             `telephone_` varchar(32) DEFAULT NULL COMMENT '联系方式',
                             `card_type_` varchar(32) DEFAULT NULL COMMENT '证件类型 字典管理',
                             `card_no_` varchar(64) DEFAULT NULL COMMENT '证件号码',
                             `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                             `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                             `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                             `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                             `remark_` text COMMENT '备注',
                             `join_time_` datetime DEFAULT NULL COMMENT '加入项目时间',
                             `leave_time_` datetime DEFAULT NULL COMMENT '离开项目时间',
                             `status_` int(2) DEFAULT NULL COMMENT '是否还在项目 1还在项目0离开项目(冗余字段)',
                             `lead_user_id_` varchar(32) DEFAULT NULL COMMENT '班组长id',
                             `delete_state_` int(1) DEFAULT '1' COMMENT '是否删除，0删除，1存在',
                             `service_area_` varchar(32) DEFAULT NULL COMMENT '服务区域(多选)：1施工区，2办公区，3生活区',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='班组表';


-- linkappdb.app_group_warning definition

CREATE TABLE `app_group_warning` (
                                     `id` varchar(32) NOT NULL COMMENT 'ID',
                                     `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                     `group_id_` varchar(32) DEFAULT NULL COMMENT '班组id',
                                     `warning_time_` datetime DEFAULT NULL COMMENT '预警时间',
                                     `warning_rule_` varchar(500) DEFAULT NULL COMMENT '预警详情',
                                     `detail_` varchar(32) DEFAULT NULL COMMENT '出勤情况',
                                     `attendance_` int(10) DEFAULT NULL COMMENT '预警出勤率',
                                     `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                     `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                     `remark_` text COMMENT '备注',
                                     `status_` int(10) DEFAULT '0' COMMENT '处理状态(1已处理 0未处理)',
                                     `handle_time_` datetime DEFAULT NULL COMMENT '处理时间',
                                     `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注',
                                     `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人',
                                     `operate_type` int(1) DEFAULT NULL COMMENT '操作类型，1已读，2保留',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='班组出勤预警表';


-- linkappdb.app_hidden_check definition

CREATE TABLE `app_hidden_check` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                    `task_id_` int(11) DEFAULT NULL COMMENT '任务id',
                                    `content_` text COMMENT '检查项/检查内容',
                                    `check_part_id_` int(11) DEFAULT NULL COMMENT '检查部位id，来源检查部位',
                                    `explain_` varchar(500) DEFAULT NULL COMMENT '说明',
                                    `scene_photo_` text COMMENT '现场图片，多个图片名称以逗号拼接',
                                    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                    `remark_` text COMMENT '备注',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='排查记录';


-- linkappdb.app_hidden_danger definition

CREATE TABLE `app_hidden_danger` (
                                     `id` varchar(32) NOT NULL COMMENT 'id',
                                     `hidden_danger_id` varchar(32) DEFAULT NULL COMMENT '隐患库隐患id',
                                     `is_scene_rectify` int(2) DEFAULT NULL COMMENT '是否已现场整改 0：否，1是',
                                     `supplement_remarks` varchar(500) DEFAULT NULL COMMENT '补充说明/备注说明',
                                     `rectify_requirements` varchar(255) DEFAULT NULL COMMENT '整改要求/检查项',
                                     `link_unit_id` varchar(32) DEFAULT NULL COMMENT '参建单位id',
                                     `rectify_uid` bigint(11) DEFAULT NULL COMMENT '整改人id',
                                     `rectify_end_time` datetime DEFAULT NULL COMMENT '整改时限',
                                     `check_uid` bigint(32) DEFAULT NULL COMMENT '复查人id',
                                     `scene_photo` varchar(1024) DEFAULT NULL COMMENT '现场图片，多个图片名称以逗号拼接',
                                     `is_overdue` int(2) DEFAULT NULL COMMENT '是否超期。0：否，1：是 （弃用）',
                                     `status` int(2) DEFAULT NULL COMMENT '状态：0：待整改，1：待复查，2：合格，3：无需整改',
                                     `close_time` datetime DEFAULT NULL COMMENT '闭合时间(复查通过时间)',
                                     `create_uid` bigint(32) DEFAULT NULL COMMENT '创建人id',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                     `check_part_id` int(11) DEFAULT NULL COMMENT '检查部位id，来源检查部位',
                                     `noticer_ids` varchar(1000) DEFAULT NULL COMMENT '通知人ids',
                                     `noticer_names` varchar(1000) DEFAULT NULL COMMENT '通知人名称,隔开',
                                     `task_id_` int(11) DEFAULT NULL COMMENT '任务id',
                                     `type_` int(11) DEFAULT '1' COMMENT '类别(1隐患2巡检记录)',
                                     `enterprise_check_type` int(1) DEFAULT NULL COMMENT '检查类型，来源企业级检查单类型，1日常检查，2专项检查',
                                     `enterprise_source_type` int(1) DEFAULT NULL COMMENT '来源，空项目级，2企业级',
                                     `enterprise_create_user_name` varchar(50) DEFAULT NULL COMMENT '创建人名称，冗余企业级创建用户名',
                                     `enterprise_organization_id` bigint(20) DEFAULT NULL COMMENT '企业级的组织id',
                                     `enterprise_create_user_id` bigint(20) DEFAULT NULL COMMENT '企业级创建人id',
                                     `enterprise_safety_check_id` bigint(20) DEFAULT NULL COMMENT '企业级安全检查单id',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='隐患记录表';


-- linkappdb.app_hidden_danger_info definition

CREATE TABLE `app_hidden_danger_info` (
                                          `id` varchar(32) NOT NULL COMMENT '记录id',
                                          `check_account_id` varchar(32) NOT NULL COMMENT '隐患记录id',
                                          `rectify_result` int(2) DEFAULT NULL COMMENT '整改结果，0：未整改，1：已整改',
                                          `rectify_photo` varchar(1024) DEFAULT NULL COMMENT '整改现场图片',
                                          `rectify_remark` varchar(140) DEFAULT NULL COMMENT '整改说明',
                                          `rectify_time` datetime DEFAULT NULL COMMENT '整改时间',
                                          `check_result` int(2) DEFAULT NULL COMMENT '整改结果，0：不合格，1：合格',
                                          `check_remark` varchar(140) DEFAULT NULL COMMENT '复查说明',
                                          `check_photo` varchar(1024) DEFAULT NULL COMMENT '复查现场图片',
                                          `check_time` datetime DEFAULT NULL COMMENT '复查时间',
                                          `is_end` int(2) DEFAULT NULL COMMENT '是否历史 0：否，1：是',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='隐患记录整改复查记录表';


-- linkappdb.app_iaqi_statistic definition

CREATE TABLE `app_iaqi_statistic` (
                                      `statistic_time` datetime NOT NULL COMMENT '统计时间',
                                      `level` int(8) NOT NULL COMMENT '空气质量等级',
                                      `statistic_value` float(6,2) NOT NULL COMMENT '统计值',
                                      `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      UNIQUE KEY `uni_stati_time_tenant_id` (`statistic_time`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='空气质量统计';


-- linkappdb.app_inspection_plan definition

CREATE TABLE `app_inspection_plan` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                       `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                                       `type_` int(2) DEFAULT NULL COMMENT '类别(1定期检查2月度检查)',
                                       `basis_` int(2) DEFAULT NULL COMMENT '检查依据(1自由检查)',
                                       `user_ids_` varchar(500) DEFAULT NULL COMMENT '检查人员id,逗号分隔',
                                       `start_time_` datetime DEFAULT NULL COMMENT '计划开始时间',
                                       `end_time_` datetime DEFAULT NULL COMMENT '计划结束时间',
                                       `status_` int(2) DEFAULT NULL COMMENT '状态(1未开始2检查中3已完成)',
                                       `publishing_` int(2) DEFAULT NULL COMMENT '发布状态(1未发布2已发布3中止)',
                                       `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                       `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                       `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                       `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                       `remark_` text COMMENT '备注/说明',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COMMENT='自检计划';


-- linkappdb.app_inspection_task definition

CREATE TABLE `app_inspection_task` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                       `plan_id_` int(11) DEFAULT NULL COMMENT '计划id',
                                       `start_time_` datetime DEFAULT NULL COMMENT '计划开始时间',
                                       `end_time_` datetime DEFAULT NULL COMMENT '计划结束时间',
                                       `status_` int(2) DEFAULT NULL COMMENT '状态(1未开始2已执行)',
                                       `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                       `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                       `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                       `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                       `remark_` text COMMENT '备注/说明',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='自检任务';


-- linkappdb.app_jk_monitor_config definition

CREATE TABLE `app_jk_monitor_config` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                         `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
                                         `project_id` varchar(100) DEFAULT NULL COMMENT '对接方项目id',
                                         `source_provider_name` varchar(100) DEFAULT NULL COMMENT '对接方名称',
                                         `item_monitor_url` varchar(200) DEFAULT NULL COMMENT '监测项接口',
                                         `data_monitor_url` varchar(200) DEFAULT NULL COMMENT '监测数据接口',
                                         `remark` varchar(300) DEFAULT NULL COMMENT '基坑概况',
                                         `craft_video_url` varchar(200) DEFAULT NULL COMMENT '工艺技法介绍视频url',
                                         `craft_video_name` varchar(100) DEFAULT NULL COMMENT '工艺技法介绍视频名称',
                                         `site_video_device_id` varchar(100) DEFAULT NULL COMMENT '现场视频设备id',
                                         `jk_img` varchar(200) DEFAULT NULL COMMENT '基坑平面布置图url',
                                         `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                         `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                         PRIMARY KEY (`id`),
                                         KEY `app_jk_monitor_config_tenant_id_IDX` (`tenant_id`) USING BTREE,
                                         KEY `app_jk_monitor_config_project_id_IDX` (`project_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='基坑监测配置表';


-- linkappdb.app_jk_monitor_item_config definition

CREATE TABLE `app_jk_monitor_item_config` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                              `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
                                              `item_type` int(1) DEFAULT NULL COMMENT '类型，1测项，2测点',
                                              `item_code` varchar(100) DEFAULT NULL COMMENT '测项点编码',
                                              `item_name` varchar(200) DEFAULT NULL COMMENT '测项点名称',
                                              `parent_id` int(11) DEFAULT NULL COMMENT '父级id',
                                              `parent_ids` varchar(4000) DEFAULT NULL COMMENT '父级ids',
                                              `x_axis` double DEFAULT NULL COMMENT 'x坐标值',
                                              `y_axis` double DEFAULT NULL COMMENT 'y轴坐标值',
                                              `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                              `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                              PRIMARY KEY (`id`),
                                              KEY `app_jk_monitor_item_config_tenant_id_IDX` (`tenant_id`) USING BTREE,
                                              KEY `app_jk_monitor_item_config_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4871 DEFAULT CHARSET=utf8 COMMENT='基坑监测测项测点配置';


-- linkappdb.app_labor_company definition

CREATE TABLE `app_labor_company` (
                                     `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                     `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                                     `type_` varchar(32) DEFAULT NULL COMMENT '公司类型 字典管理',
                                     `abbreviation_` varchar(10) DEFAULT NULL COMMENT '单位简称',
                                     `social_type_` varchar(32) DEFAULT NULL COMMENT '社会信用代码类型 字典管理',
                                     `code_` varchar(32) DEFAULT NULL COMMENT '公司代码',
                                     `register_time_` datetime DEFAULT NULL COMMENT '注册时间',
                                     `nature_business` varchar(255) DEFAULT NULL COMMENT '经营范围',
                                     `legal_` varchar(32) DEFAULT NULL COMMENT '法人',
                                     `legal_telephone_` varchar(32) DEFAULT NULL COMMENT '法人电话',
                                     `address` varchar(50) DEFAULT NULL COMMENT '地址 省市区',
                                     `detailed_address_` varchar(255) DEFAULT NULL COMMENT '详细地址',
                                     `create_tenant_id_` varchar(50) DEFAULT NULL COMMENT '创建项目id 预留字段，表示在哪个项目下新增的。null为运营管理平台创建',
                                     `build_type_` varchar(32) DEFAULT NULL COMMENT '承建类型',
                                     `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                     `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                     `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                     `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                     `remark_` text COMMENT '备注',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='劳务公司表';


-- linkappdb.app_labor_company_project definition

CREATE TABLE `app_labor_company_project` (
                                             `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                             `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                             `company_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司id',
                                             `company_name_` varchar(32) DEFAULT NULL COMMENT '劳务公司名称 冗余字段',
                                             `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                             `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                             `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                             `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                             `remark_` text COMMENT '备注',
                                             `join_time_` datetime DEFAULT NULL COMMENT '加入项目时间',
                                             `leave_time_` datetime DEFAULT NULL COMMENT '离开项目时间',
                                             `status_` int(2) DEFAULT NULL COMMENT '是否还在项目 1还在项目0离开项目(冗余字段)',
                                             `delete_state_` int(1) DEFAULT '1' COMMENT '是否删除，0删除，1存在',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='劳务公司项目表';


-- linkappdb.app_labor_region definition

CREATE TABLE `app_labor_region` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                    `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                                    `type_` int(2) DEFAULT NULL COMMENT '类别(1施工区,2生活区)',
                                    `coordinate_type_` int(2) DEFAULT NULL COMMENT '坐标类型(1高德地图)',
                                    `coordinate_` text COMMENT '区域坐标json',
                                    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                    `remark_` text COMMENT '备注',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='区域坐标';


-- linkappdb.app_machinery_device_ref definition

CREATE TABLE `app_machinery_device_ref` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `machinery_id` int(11) DEFAULT NULL COMMENT '电子档案id',
                                            `machinery_code` varchar(32) DEFAULT NULL COMMENT '电子档案设备编码（冗余）',
                                            `device_id` varchar(32) DEFAULT NULL COMMENT '设备id',
                                            `device_code` varchar(32) DEFAULT NULL COMMENT '设备编码（冗余）',
                                            `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                            `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                            `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`),
                                            KEY `app_machinery_device_ref_machinery_id_IDX` (`machinery_id`) USING BTREE,
                                            KEY `app_machinery_device_ref_device_id_IDX` (`device_id`) USING BTREE,
                                            KEY `app_machinery_device_ref_device_code_IDX` (`device_code`) USING BTREE,
                                            KEY `app_machinery_device_ref_machinery_code_IDX` (`machinery_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8 COMMENT='电子档案与设备关联表';


-- linkappdb.app_machinery_parameter definition

CREATE TABLE `app_machinery_parameter` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id(预留)',
                                           `type_code_` varchar(32) DEFAULT NULL COMMENT '类型code',
                                           `label_` varchar(32) DEFAULT NULL COMMENT '参数显示名',
                                           `key_` varchar(32) DEFAULT NULL COMMENT '参数key',
                                           `value_` varchar(32) DEFAULT NULL COMMENT '参数值',
                                           `unit_` varchar(32) DEFAULT NULL COMMENT '单位',
                                           `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                           `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                           `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                           `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                           `remark_` text COMMENT '备注',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COMMENT='app_machinery_parameter';


-- linkappdb.app_machinery_record definition

CREATE TABLE `app_machinery_record` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                        `type_code_` varchar(32) DEFAULT NULL COMMENT '类型code',
                                        `code_` varchar(64) DEFAULT NULL COMMENT '设备编号',
                                        `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                                        `is_had_` int(2) DEFAULT NULL COMMENT '是否自有设备(1是0否)',
                                        `supplier_` varchar(64) DEFAULT NULL COMMENT '供应商',
                                        `join_time_` datetime DEFAULT NULL COMMENT '进场时间',
                                        `model_` varchar(64) DEFAULT NULL COMMENT '规格型号',
                                        `manufactor_` varchar(64) DEFAULT NULL COMMENT '生产厂家',
                                        `manufactor_code_` varchar(32) DEFAULT NULL COMMENT '出厂编号',
                                        `init_code_` varchar(32) DEFAULT NULL COMMENT '登记号',
                                        `manufactor_time_` datetime DEFAULT NULL COMMENT '出厂日期',
                                        `is_special_` int(2) DEFAULT NULL COMMENT '是否特种设备(1是0否)',
                                        `parameter_` text COMMENT '设备特性参数json',
                                        `install_offset` double(20,2) DEFAULT NULL COMMENT '安装偏差值',
                                        `building_id` int(11) DEFAULT NULL COMMENT '单体id',
                                        `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                        `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                        `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                        `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                        `remark_` text COMMENT '备注',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8 COMMENT='设备电子档案';


-- linkappdb.app_machinery_type definition

CREATE TABLE `app_machinery_type` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id(预留)',
                                      `code_` varchar(32) DEFAULT NULL COMMENT '编码',
                                      `name_` varchar(64) DEFAULT NULL COMMENT '定义(名称)',
                                      `value_` varchar(64) DEFAULT NULL COMMENT '数值',
                                      `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                      `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                      `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                      `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                      `remark_` text COMMENT '备注',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COMMENT='设备类型表';


-- linkappdb.app_machinery_user_link definition

CREATE TABLE `app_machinery_user_link` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                           `type_id_` int(11) DEFAULT NULL COMMENT '人员类别id',
                                           `user_id_` varchar(32) DEFAULT NULL COMMENT '用户id',
                                           `certificate_id_` varchar(1000) DEFAULT NULL COMMENT '证书id(多个逗号分隔)',
                                           `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                           `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                           `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                           `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                           `remark_` text COMMENT '备注',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8 COMMENT='人员类别关联人员';


-- linkappdb.app_machinery_user_type definition

CREATE TABLE `app_machinery_user_type` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                           `type_` int(2) DEFAULT NULL COMMENT '类别 1工种 2自定义',
                                           `name_` varchar(128) DEFAULT NULL COMMENT '名称',
                                           `devices_` varchar(500) DEFAULT NULL COMMENT '设备类型(多个逗号分隔,保存的是编码)',
                                           `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                           `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                           `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                           `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                           `remark_` text COMMENT '备注',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8 COMMENT='人员类别';


-- linkappdb.app_manage_info definition

CREATE TABLE `app_manage_info` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `mange_name` varchar(100) DEFAULT NULL COMMENT '管理区域名称',
                                   `mange_code` varchar(40) DEFAULT NULL COMMENT '管理区域编码',
                                   `type` int(11) DEFAULT NULL COMMENT '类型，1国家、2省、3市、4区',
                                   `level` int(11) DEFAULT NULL COMMENT '等级，树形层级',
                                   `parent_id` bigint(20) DEFAULT NULL COMMENT '父级id',
                                   `parent_ids` varchar(2000) DEFAULT NULL COMMENT '父级ids',
                                   `direst_manage` int(11) NOT NULL COMMENT '是否直辖市，0否，1是',
                                   `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
                                   PRIMARY KEY (`id`),
                                   KEY `un_manage_info_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8 COMMENT='行政管理区域信息表';


-- linkappdb.app_message_center definition

CREATE TABLE `app_message_center` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                      `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
                                      `owner_id` varchar(32) NOT NULL COMMENT '所属人id,对应用户表id,-999代表属于所有用户',
                                      `self` tinyint(1) NOT NULL COMMENT '是否是自己本人的，因为是负责人且有查看角色的，会产生两条消息',
                                      `type` int(3) NOT NULL COMMENT '消息类别0-未归类，1-配电箱巡检  （package com.easylinkin.linkappapi.messagecenter.entity.enumType中查看其余消息类型）',
                                      `status` int(2) NOT NULL COMMENT '阅读状态，0-未读，1-已读',
                                      `title` varchar(50) NOT NULL COMMENT '标题',
                                      `content` varchar(500) NOT NULL COMMENT '内容',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                      `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `link_id` varchar(32) DEFAULT NULL COMMENT '关联记录id',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_tenant_id` (`tenant_id`),
                                      KEY `idx_owner_id_type_id` (`owner_id`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=37835 DEFAULT CHARSET=utf8 COMMENT='消息中心';


-- linkappdb.app_message_center_detail definition

CREATE TABLE `app_message_center_detail` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                             `device_code` varchar(32) NOT NULL COMMENT '设备编号',
                                             `device_type_name` varchar(32) DEFAULT NULL COMMENT '设备类型名称',
                                             `position` varchar(100) DEFAULT NULL COMMENT '所在位置',
                                             `lead_id` varchar(32) NOT NULL COMMENT '负责人id',
                                             `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                             `lead_name` varchar(20) DEFAULT NULL COMMENT '负责人名称',
                                             `telephone` varchar(20) DEFAULT NULL COMMENT '联系方式',
                                             `message_center_id` int(11) NOT NULL COMMENT '消息中心id',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=124746 DEFAULT CHARSET=utf8 COMMENT='消息中心详情';


-- linkappdb.app_output_info definition

CREATE TABLE `app_output_info` (
                                   `id` bigint(20) NOT NULL DEFAULT '0' COMMENT '主键',
                                   `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                   `tenant_project_id` bigint(20) DEFAULT NULL COMMENT '租户项目id',
                                   `fee_code` varchar(50) DEFAULT NULL COMMENT '费用编码',
                                   `fee_name` varchar(100) DEFAULT NULL COMMENT '费用名称',
                                   `fee_tax_total` decimal(20,4) DEFAULT NULL COMMENT '含税合价',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                   `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                   `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0:已删; 1:存在',
                                   PRIMARY KEY (`id`),
                                   KEY `linkapp_output_info_tenant_id_IDX` (`tenant_id`) USING BTREE,
                                   KEY `linkapp_output_info_tenant_project_id_IDX` (`tenant_project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='产值信息表';


-- linkappdb.app_party_build_info definition

CREATE TABLE `app_party_build_info` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
                                        `title_name` varchar(300) DEFAULT NULL COMMENT '标题',
                                        `resume` varchar(200) DEFAULT NULL COMMENT '简述',
                                        `index_no` int(11) DEFAULT NULL COMMENT '排序号',
                                        `content` longtext COMMENT '内容',
                                        `data_type` int(1) DEFAULT NULL COMMENT '数据类型，1重要党课，2重要讲话，3党建视频，4党建活动，5党员风采，6政治面貌构成情况',
                                        `page_img` varchar(500) DEFAULT NULL COMMENT '封面照片',
                                        `activity_time` datetime DEFAULT NULL COMMENT '活动时间',
                                        `imgs` varchar(4000) DEFAULT NULL COMMENT '图片集，多张图片以，隔开',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                        `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                        `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0是，1否',
                                        PRIMARY KEY (`id`),
                                        KEY `app_party_build_info_data_type_IDX` (`data_type`) USING BTREE,
                                        KEY `app_party_build_info_create_time_IDX` (`create_time`) USING BTREE,
                                        KEY `app_party_build_info_tenant_id_IDX` (`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8 COMMENT='党建信息';


-- linkappdb.app_people_num_at_fillin definition

CREATE TABLE `app_people_num_at_fillin` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
                                            `project_manage_at_num` int(11) DEFAULT NULL COMMENT '项目部管理人员-在场',
                                            `project_manage_out_num` int(11) DEFAULT NULL COMMENT '项目部管理人员-出勤',
                                            `other_manage_at_num` int(11) DEFAULT NULL COMMENT '其他管理人员-在场',
                                            `other_manage_out_num` int(11) DEFAULT NULL COMMENT '其他管理人员-出勤',
                                            `construction_at_num` int(11) DEFAULT NULL COMMENT '施工作业人员-在场',
                                            `construction_out_num` int(11) DEFAULT NULL COMMENT '施工作业人员-出勤',
                                            `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                            `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                            PRIMARY KEY (`id`),
                                            KEY `app_people_num_at_fillin_tenant_id_IDX` (`tenant_id`) USING BTREE,
                                            KEY `app_people_num_at_fillin_create_time_IDX` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8 COMMENT='在场人员数量填报';


-- linkappdb.app_problem definition

CREATE TABLE `app_problem` (
                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                               `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                               `problem_type_id_` int(11) DEFAULT NULL COMMENT '问题类别id',
                               `full_id_` varchar(64) DEFAULT NULL COMMENT '父路径id',
                               `full_name_` varchar(1000) DEFAULT NULL COMMENT '全名',
                               `level_` int(2) DEFAULT NULL COMMENT '等级',
                               `content_` varchar(300) DEFAULT NULL COMMENT '描述',
                               `change_requirement_` varchar(500) DEFAULT NULL COMMENT '整改要求',
                               `change_Limit_` int(2) DEFAULT NULL COMMENT '整改时限(天)',
                               `disable_` int(2) DEFAULT '1' COMMENT '启用状态(1启用0禁用)',
                               `code_` varchar(16) DEFAULT NULL COMMENT '编码',
                               `order_` int(2) DEFAULT NULL COMMENT '排序',
                               `is_default_` int(2) DEFAULT '0' COMMENT '是否默认数据(1是0否)',
                               `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                               `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                               `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                               `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                               `remark_` text COMMENT '备注',
                               `src_id_` int(11) DEFAULT NULL COMMENT '源数据主键(复制默认数据时有)',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=58187 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='问题库';


-- linkappdb.app_problem_type definition

CREATE TABLE `app_problem_type` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                    `parent_id_` int(11) DEFAULT NULL COMMENT '父id(一级父类别为0)',
                                    `full_id_` varchar(64) DEFAULT NULL COMMENT '父路径id',
                                    `full_name_` varchar(500) DEFAULT NULL COMMENT '全名',
                                    `name_` varchar(128) DEFAULT NULL COMMENT '名称',
                                    `code_` varchar(16) DEFAULT NULL COMMENT '编码',
                                    `level_` int(2) DEFAULT NULL COMMENT '层级',
                                    `is_default_` int(2) DEFAULT '0' COMMENT '是否默认数据(1是0否)',
                                    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                    `remark_` text COMMENT '备注',
                                    `src_id_` int(11) DEFAULT NULL COMMENT '源数据主键(复制默认数据时有)',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4649 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='问题类别';


-- linkappdb.app_quality_actual_measure_details definition

CREATE TABLE `app_quality_actual_measure_details` (
                                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                      `info_id` bigint(20) DEFAULT NULL COMMENT '实测实量主记录id',
                                                      `type_id` bigint(20) DEFAULT NULL COMMENT '实测实量分类id',
                                                      `imgs` varchar(2000) DEFAULT NULL COMMENT '实测实量照片，多张以,隔开',
                                                      `pass_rate` decimal(10,4) DEFAULT NULL COMMENT '合格率',
                                                      `actual_num` int(11) DEFAULT NULL COMMENT '实测点数',
                                                      `qualified_num` int(11) DEFAULT NULL COMMENT '合格点数',
                                                      `breaking_num` int(11) DEFAULT NULL COMMENT '爆点数',
                                                      `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                      `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                      `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                      PRIMARY KEY (`id`),
                                                      KEY `app_quality_actual_measure_details_info_id_IDX` (`info_id`) USING BTREE,
                                                      KEY `app_quality_actual_measure_details_type_id_IDX` (`type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8 COMMENT='实测实量详情';


-- linkappdb.app_quality_actual_measure_info definition

CREATE TABLE `app_quality_actual_measure_info` (
                                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                   `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
                                                   `position_id` int(11) DEFAULT NULL COMMENT '部位id',
                                                   `measure_time` datetime DEFAULT NULL COMMENT '测量时间',
                                                   `sub_org_id` varchar(100) DEFAULT NULL COMMENT '分包单位id',
                                                   `sub_group_id` varchar(100) DEFAULT NULL COMMENT '分包单位分组id',
                                                   `sub_org_type` int(1) DEFAULT NULL COMMENT '分包单位类型，1劳务、2专业',
                                                   `actual_org_type` int(2) DEFAULT NULL COMMENT '实测单位类型，1总包、2总包公司、3监理、4业主',
                                                   `detail_memo` varchar(2000) DEFAULT NULL COMMENT '详情说明',
                                                   `storage_type` int(1) DEFAULT NULL COMMENT '存储类型，1暂存，2已存储',
                                                   `sub_org_name` varchar(300) DEFAULT NULL COMMENT '分包单位名称',
                                                   `sub_group_name` varchar(300) DEFAULT NULL COMMENT '分包单位分组名称',
                                                   `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                   `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                   PRIMARY KEY (`id`),
                                                   KEY `app_quality_actual_measure_info_type_id_IDX` (`tenant_id`) USING BTREE,
                                                   KEY `app_quality_actual_measure_info_position_id_IDX` (`position_id`) USING BTREE,
                                                   KEY `app_quality_actual_measure_info_sub_org_id_IDX` (`sub_org_id`) USING BTREE,
                                                   KEY `app_quality_actual_measure_info_sub_group_id_IDX` (`sub_group_id`) USING BTREE,
                                                   KEY `app_quality_actual_measure_info_measure_time_IDX` (`measure_time`) USING BTREE,
                                                   KEY `app_quality_actual_measure_info_tenant_id_IDX` (`tenant_id`) USING BTREE,
                                                   KEY `app_quality_actual_measure_info_storage_type_IDX` (`storage_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8 COMMENT='实测实量信息';


-- linkappdb.app_quality_actual_measure_item definition

CREATE TABLE `app_quality_actual_measure_item` (
                                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                   `measure_type_id` bigint(20) DEFAULT NULL COMMENT '实测实量分类id',
                                                   `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id，冗余',
                                                   `item_name` varchar(200) DEFAULT NULL COMMENT '测量项名称',
                                                   `item_code` varchar(100) DEFAULT NULL COMMENT '测量项编码',
                                                   `qualified_standard` varchar(100) DEFAULT NULL COMMENT '合格标准',
                                                   `unit` varchar(100) DEFAULT NULL COMMENT '单位',
                                                   `standard_type` int(1) DEFAULT NULL COMMENT '标准类型，1国标，2行标，3自定义',
                                                   `cal_point_num` int(11) DEFAULT NULL COMMENT '计算点数量',
                                                   `group_cal_point_num` int(11) DEFAULT NULL COMMENT '每组计算点',
                                                   `breaking_point_standard` varchar(100) DEFAULT NULL COMMENT '爆点标准',
                                                   `measure_memo` varchar(500) DEFAULT NULL COMMENT '测量说明',
                                                   `algorithm` int(11) DEFAULT NULL COMMENT '算法，1、2、3、4',
                                                   `design_value` varchar(100) DEFAULT NULL COMMENT '设计值，Y、N',
                                                   `sort_no` int(11) DEFAULT NULL COMMENT '排序号，1自增',
                                                   `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                   `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                   PRIMARY KEY (`id`),
                                                   KEY `app_quality_actual_measure_item_measure_type_id_IDX` (`measure_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2946 DEFAULT CHARSET=utf8 COMMENT='实测实量项';


-- linkappdb.app_quality_actual_measure_type definition

CREATE TABLE `app_quality_actual_measure_type` (
                                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                   `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
                                                   `name` varchar(200) DEFAULT NULL COMMENT '分类名称',
                                                   `full_name` varchar(1000) DEFAULT NULL COMMENT '全分类名，以/隔开结尾',
                                                   `full_id` varchar(200) DEFAULT NULL COMMENT '全分类id，以/隔开结尾',
                                                   `parent_id` bigint(20) DEFAULT NULL COMMENT '父级id',
                                                   `code` varchar(100) DEFAULT NULL COMMENT '分类编码',
                                                   `sort_no` int(11) DEFAULT NULL COMMENT '排序号，1自增',
                                                   `level` int(11) DEFAULT NULL COMMENT '层级',
                                                   `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                   `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                   PRIMARY KEY (`id`),
                                                   KEY `app_quality_actual_measure_type_tenant_id_IDX` (`tenant_id`) USING BTREE,
                                                   KEY `app_quality_actual_measure_type_parent_id_IDX` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3982 DEFAULT CHARSET=utf8 COMMENT='实测实量分类';


-- linkappdb.app_quality_actual_measure_value definition

CREATE TABLE `app_quality_actual_measure_value` (
                                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                    `info_id` bigint(20) DEFAULT NULL COMMENT '实测实量id，冗余',
                                                    `details_id` bigint(20) DEFAULT NULL COMMENT '详情id',
                                                    `item_id` bigint(20) DEFAULT NULL COMMENT '实测实量项id',
                                                    `benchmark_value` varchar(20) DEFAULT NULL COMMENT '基准值',
                                                    `group_no` varchar(100) DEFAULT NULL COMMENT '分组编号、名称',
                                                    `actual_value` varchar(20) DEFAULT NULL COMMENT '实测值',
                                                    `actual_state` int(11) DEFAULT NULL COMMENT '实测状态，1合格，2不合格，3爆点',
                                                    `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
                                                    `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                    `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                    PRIMARY KEY (`id`),
                                                    KEY `app_quality_actual_measure_value_details_id_IDX` (`details_id`) USING BTREE,
                                                    KEY `app_quality_actual_measure_value_info_id_IDX` (`info_id`) USING BTREE,
                                                    KEY `app_quality_actual_measure_value_item_id_IDX` (`item_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=516 DEFAULT CHARSET=utf8 COMMENT='实测实量测试记录';


-- linkappdb.app_quality_appraising_excellent definition

CREATE TABLE `app_quality_appraising_excellent` (
                                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                    `tenant_id` varchar(100) DEFAULT NULL COMMENT 'tenantId',
                                                    `imgs` varchar(2000) DEFAULT NULL COMMENT '问题照片，多张以，隔开',
                                                    `check_part_id` int(11) DEFAULT NULL COMMENT '施工部位id，来源检查部位',
                                                    `problem_type_id` int(11) DEFAULT NULL COMMENT '问题类型id，来源问题类型库',
                                                    `content_` varchar(1000) DEFAULT NULL COMMENT '优秀做法',
                                                    `sub_org_type` int(11) DEFAULT NULL COMMENT '分包单位类型，1劳务、2专业',
                                                    `sub_org_id` varchar(32) DEFAULT NULL COMMENT '分包单位id',
                                                    `sub_org_name` varchar(200) DEFAULT NULL COMMENT '分包单位名称',
                                                    `sub_group_id` varchar(100) DEFAULT NULL COMMENT '分包班组id',
                                                    `sub_group_name` varchar(200) DEFAULT NULL COMMENT '分包班组名称',
                                                    `noticer_ids` varchar(1000) DEFAULT NULL COMMENT '通知人ids',
                                                    `noticer_names` varchar(1000) DEFAULT NULL COMMENT '通知人名称,隔开',
                                                    `appraising_time` datetime DEFAULT NULL COMMENT '评优时间',
                                                    `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                    `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COMMENT='质量评优信息';


-- linkappdb.app_quality_drawing definition

CREATE TABLE `app_quality_drawing` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                       `position_id_` int(11) DEFAULT NULL COMMENT '部位id',
                                       `name_` varchar(128) DEFAULT NULL COMMENT '名称',
                                       `url_` varchar(255) DEFAULT NULL COMMENT '图纸url',
                                       `size_` varchar(32) DEFAULT NULL COMMENT '图纸大小',
                                       `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                       `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                       `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                       `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                       `remark_` text COMMENT '备注/说明',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8 COMMENT='部位图纸';


-- linkappdb.app_quality_inspection definition

CREATE TABLE `app_quality_inspection` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                          `url_` text COMMENT '相片url',
                                          `position_id_` int(11) DEFAULT NULL COMMENT '部位id',
                                          `problem_id_` int(11) DEFAULT NULL COMMENT '问题id',
                                          `explain_` varchar(500) DEFAULT NULL COMMENT '巡检说明',
                                          `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                          `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                          `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                          `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                          `remark_` text COMMENT '备注',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='巡检记录';


-- linkappdb.app_quality_position definition

CREATE TABLE `app_quality_position` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                        `parent_id_` int(11) DEFAULT NULL COMMENT '父id(一级父类别为0)',
                                        `full_id_` varchar(64) DEFAULT NULL COMMENT '父路径id',
                                        `full_name_` varchar(500) DEFAULT NULL COMMENT '全名',
                                        `name_` varchar(128) DEFAULT NULL COMMENT '名称',
                                        `code_` varchar(16) DEFAULT NULL COMMENT '编码',
                                        `level_` int(2) DEFAULT NULL COMMENT '层级',
                                        `rectify_id_` bigint(20) DEFAULT NULL COMMENT '整改id',
                                        `notice_id_` varchar(500) DEFAULT NULL COMMENT '通知人id(多选,逗号分隔)',
                                        `subcontractor_` varchar(32) DEFAULT NULL COMMENT '分包单位',
                                        `subcontractor_type_` int(2) DEFAULT NULL COMMENT '分包单位类型(1参建单位 2班组)',
                                        `order_` int(2) DEFAULT NULL COMMENT '排序/编码使用(冗余)',
                                        `type_` int(2) DEFAULT '1' COMMENT '类别(1质量部位2安全部位)',
                                        `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                        `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                        `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                        `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                        `remark_` text COMMENT '备注/说明',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `app_quality_position_tenant_id__IDX` (`tenant_id_`) USING BTREE,
                                        KEY `app_quality_position_type__IDX` (`type_`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=331 DEFAULT CHARSET=utf8 COMMENT='部位';


-- linkappdb.app_quality_question_comment definition

CREATE TABLE `app_quality_question_comment` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                `question_id` bigint(20) DEFAULT NULL COMMENT '质量问题id',
                                                `step` int(11) DEFAULT NULL COMMENT '步骤，连续自增',
                                                `title` varchar(200) DEFAULT NULL COMMENT '标题',
                                                `content` varchar(500) DEFAULT NULL COMMENT '内容',
                                                `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                PRIMARY KEY (`id`),
                                                KEY `quality_question_comment_question_id_IDX` (`question_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COMMENT='质量问题评论表';


-- linkappdb.app_quality_question_deal_record definition

CREATE TABLE `app_quality_question_deal_record` (
                                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                    `question_id` bigint(20) DEFAULT NULL COMMENT '质量问题id',
                                                    `deal_type` int(11) DEFAULT NULL COMMENT '处理类型，1整改、2复核',
                                                    `step` int(11) DEFAULT NULL COMMENT '步骤好，连贯自增',
                                                    `deal_time` datetime DEFAULT NULL COMMENT '处理时间',
                                                    `deal_memo` varchar(2000) DEFAULT NULL COMMENT '处理说明',
                                                    `before_state` int(11) DEFAULT NULL COMMENT '处理前状态',
                                                    `after_stste` int(11) DEFAULT NULL COMMENT '处理后状态',
                                                    `pass_type` int(1) DEFAULT NULL COMMENT '处理通过状态，0不通过，1通过',
                                                    `deal_imgs` varchar(4000) DEFAULT NULL COMMENT '处理图片，多张以逗号隔开',
                                                    `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                                    `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                                    PRIMARY KEY (`id`),
                                                    KEY `quality_question_deal_record_question_id_IDX` (`question_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 COMMENT='质量问题处理记录（整改、复核）';


-- linkappdb.app_quality_question_info definition

CREATE TABLE `app_quality_question_info` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                             `tenant_id` varchar(100) DEFAULT NULL COMMENT 'tenantId',
                                             `check_part_id` int(11) DEFAULT NULL COMMENT '检查部位id，来源检查部位',
                                             `question_describe_id` int(11) DEFAULT NULL COMMENT '问题描述id，来源问题库',
                                             `question_level` int(11) DEFAULT NULL COMMENT '问题等级，先来源问题描述的等级，可修改（1一级、2二级、3三级、4四级）',
                                             `urgent_level` int(11) DEFAULT NULL COMMENT '紧急程度，1一般、2严重、3紧要',
                                             `memo` varchar(1000) DEFAULT NULL COMMENT '补充说明，对应排查说明',
                                             `sub_org_type` int(11) DEFAULT NULL COMMENT '分包单位类型，1劳务、2专业',
                                             `sub_org_id` varchar(32) DEFAULT NULL COMMENT '分包单位id',
                                             `sub_org_name` varchar(200) DEFAULT NULL COMMENT '分包单位名称',
                                             `sub_group_id` varchar(100) DEFAULT NULL COMMENT '分包班组id',
                                             `sub_group_name` varchar(200) DEFAULT NULL COMMENT '分包班组名称',
                                             `rectifier_id` int(11) DEFAULT NULL COMMENT '整改人id',
                                             `rectifier_name` varchar(100) DEFAULT NULL COMMENT '整改人名称',
                                             `rectifi_site` int(1) DEFAULT NULL COMMENT '是否现场整改，0否，1是',
                                             `rectifi_limit_time` datetime DEFAULT NULL COMMENT '整改时限',
                                             `rectifi_target` varchar(1000) DEFAULT NULL COMMENT '整改需求',
                                             `review_limit_time` datetime DEFAULT NULL COMMENT '复核时限',
                                             `reviewer_id` int(11) DEFAULT NULL COMMENT '复核人id',
                                             `reviewer_name` varchar(100) DEFAULT NULL COMMENT '复核人名称',
                                             `noticer_ids` varchar(1000) DEFAULT NULL COMMENT '通知人ids',
                                             `imgs` varchar(2000) DEFAULT NULL COMMENT '问题照片，多张以，隔开',
                                             `noticer_names` varchar(1000) DEFAULT NULL COMMENT '通知人名称,隔开',
                                             `question_state` int(2) DEFAULT NULL COMMENT '问题状态，1待整改、2待复核、3合格',
                                             `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                             `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0删除，1存在',
                                             PRIMARY KEY (`id`),
                                             KEY `quality_question_info_check_part_id_IDX` (`check_part_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8 COMMENT='质量问题信息';


-- linkappdb.app_spray_records definition

CREATE TABLE `app_spray_records` (
                                     `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                     `device_code` varchar(32) NOT NULL COMMENT '设备编码',
                                     `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                     `trigger_source` varchar(32) DEFAULT NULL COMMENT '触发源',
                                     `trigger_time` datetime DEFAULT NULL COMMENT '触发时间',
                                     `start_time` datetime DEFAULT NULL COMMENT '喷淋开始时间',
                                     `end_time` datetime DEFAULT NULL COMMENT '喷淋结束时间',
                                     `spray_hours_` double(20,2) DEFAULT NULL COMMENT '喷淋时长',
                                     `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                     `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                     `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                     `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                     `remark_` text COMMENT '备注',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='喷淋记录';


-- linkappdb.app_tenant_project definition

CREATE TABLE `app_tenant_project` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                      `start_date` datetime DEFAULT NULL COMMENT '开工时间',
                                      `end_date` datetime DEFAULT NULL COMMENT '竣工时间',
                                      `plan_end_date` datetime DEFAULT NULL COMMENT '预计竣工时间',
                                      `progress` varchar(10) DEFAULT NULL COMMENT '项目进度，采集进展信息计算更新',
                                      `build_vol` varchar(30) DEFAULT NULL COMMENT '建筑体量',
                                      `contract_amount` decimal(20,4) DEFAULT NULL COMMENT '合同金额',
                                      `platform_project_name` varchar(300) DEFAULT NULL COMMENT '项目名称（冗余）',
                                      `bidding_unit` varchar(300) DEFAULT NULL COMMENT '建设单位（冗余）',
                                      `construction_unit` varchar(300) DEFAULT NULL COMMENT '承建单位（冗余）',
                                      `location` varchar(300) DEFAULT NULL COMMENT '项目地址（冗余）',
                                      `manage_id` bigint(20) DEFAULT NULL COMMENT '区域id',
                                      `project_type_name` varchar(100) DEFAULT NULL COMMENT '项目类型名称',
                                      `is_class` varchar(1) DEFAULT NULL COMMENT '是否创优',
                                      `pc_class` varchar(20) DEFAULT NULL COMMENT '创优等级,1 国家 ,2 省 3 市',
                                      `phid_type` bigint(20) DEFAULT NULL COMMENT '项目类型编码',
                                      `fill_dt` datetime DEFAULT NULL COMMENT '录入日期',
                                      `project_statue` int(11) DEFAULT NULL COMMENT '项目状态，1在建，2中标，3施工准备，4停工缓建，5完工待结算，6完工已结算',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                      `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
                                      `delete_state` int(1) DEFAULT NULL COMMENT '是否删除，0:已删; 1:存在',
                                      `phid` bigint(20) DEFAULT NULL COMMENT '新中大主键',
                                      PRIMARY KEY (`id`),
                                      KEY `linkapp_tenant_project_tenant_id_IDX` (`tenant_id`) USING BTREE,
                                      KEY `linkapp_tenant_project_manage_id_IDX` (`manage_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=256 DEFAULT CHARSET=utf8 COMMENT='租户项目信息';


-- linkappdb.app_tower_crane_position definition

CREATE TABLE `app_tower_crane_position` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `machinery_id` int(11) DEFAULT NULL COMMENT '电子档案id',
                                            `device_code` varchar(32) DEFAULT NULL COMMENT '设备id',
                                            `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                            `position_x` float(6,2) NOT NULL COMMENT 'x',
                                            `position_y` float(6,2) NOT NULL COMMENT 'y',
                                            `circle_radius_` float(6,2) NOT NULL COMMENT '圆半径',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                            `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                            `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COMMENT='塔吊在图纸上的位置';


-- linkappdb.app_tower_crane_position_image definition

CREATE TABLE `app_tower_crane_position_image` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                  `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                  `url_` text COMMENT '图纸url',
                                                  `config_` varchar(512) DEFAULT NULL COMMENT '图纸配置',
                                                  `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                                  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                                  `remark_` text COMMENT '备注',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='塔吊位置打点配置';


-- linkappdb.app_tower_crane_work_record definition

CREATE TABLE `app_tower_crane_work_record` (
                                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                               `machinery_id` int(11) DEFAULT NULL COMMENT '电子档案id',
                                               `device_code` varchar(32) DEFAULT NULL COMMENT '设备id',
                                               `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                               `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                               `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                               `max_weight` double(20,2) DEFAULT NULL COMMENT '最大吊重 单位t',
                                               `weight_percentage` double(20,2) DEFAULT NULL COMMENT '载重百分比 单位%',
                                               `max_torque` double(20,2) DEFAULT NULL COMMENT '最大力矩 单位t·m',
                                               `torque_percentage` double(20,2) DEFAULT NULL COMMENT '力矩百分比 单位%',
                                               `max_height` double(20,2) DEFAULT NULL COMMENT '最大高度 单位m',
                                               `min_height` double(20,2) DEFAULT NULL COMMENT '最小高度 单位m',
                                               `max_range` double(20,2) DEFAULT NULL COMMENT '最大幅度 单位m',
                                               `min_range` double(20,2) DEFAULT NULL COMMENT '最小幅度 单位m',
                                               `start_rotation` double(20,2) DEFAULT NULL COMMENT '起吊点角度 单位°',
                                               `start_range` double(20,2) DEFAULT NULL COMMENT '起吊点幅度 单位m',
                                               `start_height` double(20,2) DEFAULT NULL COMMENT '起吊点高度 单位m',
                                               `end_rotation` double(20,2) DEFAULT NULL COMMENT '卸吊点角度 单位°',
                                               `end_range` double(20,2) DEFAULT NULL COMMENT '卸吊点幅度 单位m',
                                               `end_height` double(20,2) DEFAULT NULL COMMENT '卸吊点高度 单位m',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               PRIMARY KEY (`id`),
                                               KEY `app_tower_crane_work_record_machinery_id_IDX` (`machinery_id`) USING BTREE,
                                               KEY `app_tower_crane_work_record_device_code_IDX` (`device_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COMMENT='塔吊工作循环记录';


-- linkappdb.app_user_blacklist definition

CREATE TABLE `app_user_blacklist` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                      `user_id_` varchar(32) DEFAULT NULL COMMENT 'userId',
                                      `type_` int(2) DEFAULT NULL COMMENT '人员类别(1工人2班组长)',
                                      `reason_` varchar(500) DEFAULT NULL COMMENT '黑名单原因',
                                      `url_` text COMMENT '附件',
                                      `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                      `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                      `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                      `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                      `remark_` text COMMENT '备注/说明',
                                      `enterprise_source_type_` int(1) DEFAULT '1' COMMENT '来源，空或者1项目级，2企业级',
                                      `enterprise_create_user_name_` varchar(50) DEFAULT NULL COMMENT '创建人名称，冗余企业级创建用户名',
                                      `enterprise_create_user_id_` bigint(20) DEFAULT NULL COMMENT '企业级创建人id',
                                      `enterprise_organization_id_` bigint(20) DEFAULT NULL COMMENT '企业级的组织id',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      KEY `app_user_blacklist_tenant_id__IDX` (`tenant_id_`) USING BTREE,
                                      KEY `app_user_blacklist_user_id__IDX` (`user_id_`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8 COMMENT='人员黑名单';


-- linkappdb.app_user_blacklist_record definition

CREATE TABLE `app_user_blacklist_record` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                             `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                             `user_id_` varchar(32) DEFAULT NULL COMMENT 'userId',
                                             `type_` int(2) DEFAULT NULL COMMENT '类别(1加入黑名单,2移出黑名单)',
                                             `reason_` varchar(500) DEFAULT NULL COMMENT '说明',
                                             `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                             `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                             `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                             `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                             `remark_` text COMMENT '备注/说明',
                                             `enterprise_source_type_` int(1) DEFAULT '1' COMMENT '来源，空或者1项目级，2企业级',
                                             `enterprise_create_user_name_` varchar(50) DEFAULT NULL COMMENT '创建人名称，冗余企业级创建用户名',
                                             `enterprise_create_user_id_` bigint(20) DEFAULT NULL COMMENT '企业级创建人id',
                                             `enterprise_organization_id_` bigint(20) DEFAULT NULL COMMENT '企业级的组织id',
                                             `src_tenant_id_` varchar(32) DEFAULT NULL COMMENT '添加时来源组织(来源项目级:tenant_id_)',
                                             `src_organization_id_` bigint(20) DEFAULT NULL COMMENT '添加时来源组织(来源企业级:组织id)',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8 COMMENT='黑名单记录';


-- linkappdb.app_user_certificate definition

CREATE TABLE `app_user_certificate` (
                                        `id` varchar(32) NOT NULL COMMENT 'ID 主键自增',
                                        `user_id_` varchar(32) DEFAULT NULL COMMENT '用户userID ',
                                        `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                                        `no_` varchar(64) DEFAULT NULL COMMENT '编号',
                                        `type_` varchar(32) DEFAULT NULL COMMENT '证书类型 字典管理',
                                        `level_` varchar(32) DEFAULT NULL COMMENT '证书等级 字典管理',
                                        `valid_time_` datetime DEFAULT NULL COMMENT '有效日期',
                                        `stop_time_` datetime DEFAULT NULL COMMENT '截止日期',
                                        `url_` varchar(500) DEFAULT NULL COMMENT '附件url',
                                        `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                        `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                        `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                        `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                        `remark_` text COMMENT '备注',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `app_user_certificate_user_id__IDX` (`user_id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户资质证书';


-- linkappdb.app_user_clock definition

CREATE TABLE `app_user_clock` (
                                  `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                  `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                  `clock_time` datetime DEFAULT NULL COMMENT '打卡时间',
                                  `direction_` int(10) DEFAULT NULL COMMENT '方向 1进门2出门',
                                  `type_` int(2) DEFAULT NULL COMMENT '人员类型 1内部人员 2聘用人员',
                                  `user_id_` varchar(32) DEFAULT NULL COMMENT '用户/访客id type为1userID 2访客申请表id',
                                  `user_name_` varchar(32) DEFAULT NULL COMMENT '用户名称',
                                  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                  `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                  `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                  `remark_` text COMMENT '备注',
                                  `photo_` varchar(500) DEFAULT NULL COMMENT '照片url',
                                  `group_id_` varchar(32) DEFAULT NULL COMMENT '班组',
                                  `company_project_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司与项目中间表id',
                                  `gate_code` varchar(100) DEFAULT NULL COMMENT '闸机编码',
                                  `service_area` int(1) DEFAULT NULL COMMENT '服务区域：1施工区，2办公区，3生活区',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户考勤表';


-- linkappdb.app_user_gate definition

CREATE TABLE `app_user_gate` (
                                 `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                 `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                 `gate_id_` varchar(32) DEFAULT NULL COMMENT '闸机id',
                                 `time_` datetime DEFAULT NULL COMMENT '记录时间',
                                 `photo_` varchar(500) DEFAULT NULL COMMENT '照片url',
                                 `temperature_` double DEFAULT NULL COMMENT '体温',
                                 `health_code_` int(2) DEFAULT NULL COMMENT '健康码(1绿码，2黄码，3灰码，4红码)',
                                 `travel_code_` int(2) DEFAULT NULL COMMENT '行程码(1正常，0异常)',
                                 `type_` int(2) DEFAULT NULL COMMENT '类型 1内部用户 2聘用人员 3访客',
                                 `user_id_` varchar(32) DEFAULT NULL COMMENT '用户/访客id type为1userID 2访客申请表id',
                                 `user_name_` varchar(32) DEFAULT NULL COMMENT '用户名称',
                                 `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                 `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                 `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                 `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                 `remark_` text COMMENT '备注',
                                 `group_id_` varchar(32) DEFAULT NULL COMMENT '班组',
                                 `company_project_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司与项目中间表id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户闸机刷脸表';


-- linkappdb.app_user_gate_link definition

CREATE TABLE `app_user_gate_link` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                      `gate_id_` varchar(32) DEFAULT NULL COMMENT '闸机id',
                                      `user_id_` varchar(32) DEFAULT NULL COMMENT '用户/访客id type为1userID 2访客申请表id',
                                      `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                      `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                      `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                      `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                      `remark_` text COMMENT '备注',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COMMENT='用户闸机绑定表';


-- linkappdb.app_user_outin_warning definition

CREATE TABLE `app_user_outin_warning` (
                                          `id` varchar(32) NOT NULL COMMENT '主键',
                                          `tenant_id_` varchar(100) DEFAULT NULL COMMENT '租户id',
                                          `user_id_` varchar(100) DEFAULT NULL COMMENT '用户id',
                                          `warning_time_` datetime DEFAULT NULL COMMENT '预警时间',
                                          `warning_rule_` varchar(500) DEFAULT NULL COMMENT '预警详情',
                                          `type_` int(1) DEFAULT NULL COMMENT '预警类型(1出场异常，2进场异常，3停留异常)',
                                          `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
                                          `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                          `remark_` text COMMENT '备注',
                                          `status_` int(1) DEFAULT NULL COMMENT '处理状态(1已处理 0未处理)',
                                          `group_id` varchar(100) DEFAULT NULL COMMENT '班组id',
                                          `handle_time_` datetime DEFAULT NULL COMMENT '处理时间',
                                          `handle_remark_` varchar(500) DEFAULT NULL COMMENT '处理内容',
                                          `handle_user_` varchar(100) DEFAULT NULL COMMENT '处理人',
                                          `operate_type` int(1) DEFAULT NULL COMMENT '操作类型，1已读，2保留',
                                          `in_clock_id` varchar(32) DEFAULT NULL COMMENT '闸机进场id',
                                          `out_clock_id` varchar(32) DEFAULT NULL COMMENT '闸机出场id',
                                          PRIMARY KEY (`id`),
                                          KEY `app_early_warn_user_id_IDX` (`user_id_`) USING BTREE,
                                          KEY `app_early_warn_tenant_id_IDX` (`tenant_id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人员出入场预警信息';


-- linkappdb.app_user_project definition

CREATE TABLE `app_user_project` (
                                    `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                    `user_id_` varchar(32) DEFAULT NULL COMMENT '用户id',
                                    `photo_` varchar(255) DEFAULT NULL COMMENT '相片url',
                                    `user_name_` varchar(32) DEFAULT NULL COMMENT '用户名称',
                                    `gender_` int(2) DEFAULT NULL COMMENT '性别 1男2女',
                                    `birthday_` datetime DEFAULT NULL COMMENT '出生日期',
                                    `no_` varchar(32) DEFAULT NULL COMMENT '工号 工号',
                                    `group_id_` varchar(32) DEFAULT NULL COMMENT '班组',
                                    `type_` varchar(10) DEFAULT NULL COMMENT '工人类型 (1管理人员，2建筑工人)',
                                    `work_type_` varchar(32) DEFAULT NULL COMMENT '工种 字典管理',
                                    `is_group_leader_` int(1) DEFAULT NULL COMMENT '是否班组长',
                                    `join_time_` datetime DEFAULT NULL COMMENT '加入项目时间',
                                    `leave_time_` datetime DEFAULT NULL COMMENT '离开项目时间',
                                    `status_` int(2) DEFAULT NULL COMMENT '是否还在项目 1还在项目0离开项目(冗余字段)',
                                    `gate_id_` varchar(32) DEFAULT NULL COMMENT '闸机id 安全帽设备id',
                                    `gate_status_` int(2) DEFAULT '0' COMMENT '下发闸机状态(1下发，0未下发)',
                                    `hat_sn_` varchar(32) DEFAULT NULL COMMENT '安全帽编号',
                                    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                    `remark_` text COMMENT '备注',
                                    `delete_state_` int(1) DEFAULT '1' COMMENT '是否删除，0删除，1存在',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    KEY `app_user_project_tenant_id__IDX` (`tenant_id_`) USING BTREE,
                                    KEY `app_user_project_type__IDX` (`type_`) USING BTREE,
                                    KEY `app_user_project_user_id__IDX` (`user_id_`) USING BTREE,
                                    KEY `app_user_project_group_id__IDX` (`group_id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户项目表';


-- linkappdb.`app_user_project-new` definition

CREATE TABLE `app_user_project-new` (
                                        `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                        `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                        `user_id_` varchar(32) DEFAULT NULL COMMENT '用户id',
                                        `photo_` varchar(255) DEFAULT NULL COMMENT '相片url',
                                        `user_name_` varchar(32) DEFAULT NULL COMMENT '用户名称',
                                        `gender_` int(2) DEFAULT NULL COMMENT '性别 1男2女',
                                        `birthday_` datetime DEFAULT NULL COMMENT '出生日期',
                                        `no_` varchar(32) DEFAULT NULL COMMENT '工号 工号',
                                        `group_id_` varchar(32) DEFAULT NULL COMMENT '班组',
                                        `type_` varchar(10) DEFAULT NULL COMMENT '工人类型 (1管理人员，2建筑工人)',
                                        `work_type_` varchar(32) DEFAULT NULL COMMENT '工种 字典管理',
                                        `is_group_leader_` int(1) DEFAULT NULL COMMENT '是否班组长',
                                        `join_time_` datetime DEFAULT NULL COMMENT '加入项目时间',
                                        `leave_time_` datetime DEFAULT NULL COMMENT '离开项目时间',
                                        `status_` int(2) DEFAULT NULL COMMENT '是否还在项目 1还在项目0离开项目(冗余字段)',
                                        `gate_id_` varchar(32) DEFAULT NULL COMMENT '闸机id 安全帽设备id',
                                        `gate_status_` int(2) DEFAULT '0' COMMENT '下发闸机状态(1下发，0未下发)',
                                        `hat_sn_` varchar(32) DEFAULT NULL COMMENT '安全帽编号',
                                        `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                        `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                        `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                        `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                        `remark_` text COMMENT '备注',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `app_user_project_tenant_id__IDX` (`tenant_id_`) USING BTREE,
                                        KEY `app_user_project_type__IDX` (`type_`) USING BTREE,
                                        KEY `app_user_project_user_id__IDX` (`user_id_`) USING BTREE,
                                        KEY `app_user_project_group_id__IDX` (`group_id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户项目表';


-- linkappdb.app_user_record definition

CREATE TABLE `app_user_record` (
                                   `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                   `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                   `record_time_` datetime DEFAULT NULL COMMENT '考勤日期',
                                   `hour_` double DEFAULT NULL COMMENT '考勤时长',
                                   `type_` int(2) DEFAULT NULL COMMENT '人员类型 1内部人员 2聘用人员',
                                   `user_id_` varchar(32) DEFAULT NULL COMMENT '用户/访客id type为1userID 2访客申请表id',
                                   `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                   `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                   `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                   `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                   `remark_` text COMMENT '备注',
                                   `record_type_` int(10) DEFAULT '1' COMMENT '1全勤 2半勤 0缺勤',
                                   `group_id_` varchar(32) DEFAULT NULL COMMENT '班组',
                                   `company_project_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司与项目中间表id',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   KEY `app_user_record_tenant_id__IDX` (`tenant_id_`) USING BTREE,
                                   KEY `app_user_record_user_id__IDX` (`user_id_`) USING BTREE,
                                   KEY `app_user_record_record_time__IDX` (`record_time_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户考勤记录表';


-- linkappdb.app_user_statistics definition

CREATE TABLE `app_user_statistics` (
                                       `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                       `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                       `company_project_id_` varchar(32) DEFAULT NULL COMMENT '劳务公司与项目中间表id',
                                       `record_time_` datetime DEFAULT NULL COMMENT '考勤日期',
                                       `sum_` int(10) DEFAULT NULL COMMENT '人员总数',
                                       `manage_` int(10) DEFAULT NULL COMMENT '管理人员总数',
                                       `work_` int(10) DEFAULT NULL COMMENT '劳务人员总数',
                                       `on_` int(10) DEFAULT NULL COMMENT '出勤人数',
                                       `off_` int(10) DEFAULT NULL COMMENT '缺勤人数',
                                       `hours_` double DEFAULT NULL COMMENT '考勤时长(合计)',
                                       `type_` int(2) DEFAULT NULL COMMENT '人员类型 1内部人员 2聘用人员',
                                       `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                       `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                       `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                       `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                       `remark_` text COMMENT '备注',
                                       `group_id_` varchar(32) DEFAULT NULL COMMENT '班组',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户考勤统计表';


-- linkappdb.app_user_temperature definition

CREATE TABLE `app_user_temperature` (
                                        `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                        `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                        `user_id_` varchar(32) DEFAULT NULL COMMENT '人员id',
                                        `user_name_` varchar(32) DEFAULT NULL COMMENT '用户名称',
                                        `time_` datetime DEFAULT NULL COMMENT '记录时间',
                                        `temperature_` double DEFAULT NULL COMMENT '体温',
                                        `type_` int(2) DEFAULT NULL COMMENT '人员类型 1内部用户 2聘用人员 3访客',
                                        `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                        `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                        `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                        `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                        `remark_` text COMMENT '备注',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户体温表';


-- linkappdb.app_user_warning definition

CREATE TABLE `app_user_warning` (
                                    `id` varchar(32) NOT NULL COMMENT 'ID',
                                    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
                                    `user_id_` varchar(32) DEFAULT NULL COMMENT '人员id',
                                    `warning_time_` datetime DEFAULT NULL COMMENT '预警时间',
                                    `warning_rule_` varchar(500) DEFAULT NULL COMMENT '预警详情',
                                    `type_` int(2) DEFAULT NULL COMMENT '预警类型(1连续未出勤, 2自动退场)',
                                    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                    `remark_` text COMMENT '备注',
                                    `status_` int(10) DEFAULT '0' COMMENT '处理状态(1已处理 0未处理)',
                                    `handle_time_` datetime DEFAULT NULL COMMENT '处理时间',
                                    `handle_remark_` varchar(512) DEFAULT NULL COMMENT '处理备注',
                                    `handle_user_` varchar(32) DEFAULT NULL COMMENT '预警处理人',
                                    `operate_type` int(1) DEFAULT NULL COMMENT '操作类型，1退场，2忽略',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    KEY `app_user_warning_tenant_id__IDX` (`tenant_id_`) USING BTREE,
                                    KEY `app_user_warning_user_id__IDX` (`user_id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人员出勤预警表';


-- linkappdb.app_version_info definition

CREATE TABLE `app_version_info` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `device_brand` varchar(100) DEFAULT NULL COMMENT '设备品牌，apple、huawei',
                                    `os_name` varchar(100) DEFAULT NULL COMMENT '系统名称，ios，android',
                                    `os_version` varchar(30) DEFAULT NULL COMMENT '系统版本',
                                    `os_language` varchar(100) DEFAULT NULL COMMENT '系统语言，zh-CN',
                                    `app_version` varchar(20) DEFAULT NULL COMMENT 'app版本号',
                                    `app_url` varchar(300) DEFAULT NULL COMMENT 'app下载地址',
                                    `update_content` varchar(2000) DEFAULT NULL COMMENT '更新内容',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `deleted` int(1) DEFAULT NULL COMMENT '是否删除，1否，0是',
                                    PRIMARY KEY (`id`),
                                    KEY `app_version_info_create_time_IDX` (`create_time`) USING BTREE,
                                    KEY `app_version_info_os_name_IDX` (`os_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8 COMMENT='App版本信息';


-- linkappdb.app_visit_privilege_log definition

CREATE TABLE `app_visit_privilege_log` (
                                           `id_` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `name_` varchar(32) DEFAULT NULL COMMENT '菜单名称',
                                           `privilege_code_` varchar(255) DEFAULT NULL COMMENT '菜单code',
                                           `url_` varchar(255) DEFAULT NULL COMMENT '菜单前端地址',
                                           `tenant_id_` varchar(32) DEFAULT NULL COMMENT '租户id',
                                           `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
                                           `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                           PRIMARY KEY (`id_`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='访问菜单日志';


-- linkappdb.app_warning_config definition

CREATE TABLE `app_warning_config` (
                                      `id` varchar(32) NOT NULL COMMENT 'ID',
                                      `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)，为null时为共有的默认数据',
                                      `absent_` int(10) DEFAULT NULL COMMENT '连续未出勤天数',
                                      `automatic_exit_` int(10) DEFAULT NULL COMMENT '连续未出勤自动退场天数',
                                      `group_` int(10) DEFAULT NULL COMMENT '班组出勤率低预警(百分比)',
                                      `group_check_` varchar(10) DEFAULT NULL COMMENT '每日班组出勤检测时间：10:00',
                                      `man_age_` int(10) DEFAULT NULL COMMENT '男性超龄',
                                      `woman_age_` int(10) DEFAULT NULL COMMENT '女性超龄',
                                      `certificate_` int(10) DEFAULT NULL COMMENT '证书剩余有效期',
                                      `temperature_` double DEFAULT NULL COMMENT '体温预警值',
                                      `health_code_` varchar(32) DEFAULT NULL COMMENT '健康码预警值(1绿码，2黄码，3灰码，4红码) 多选',
                                      `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                      `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                      `remark_` text COMMENT '备注',
                                      `absent_switch` int(1) DEFAULT NULL COMMENT '连续未出勤天数开关，0停用，1启用',
                                      `automatic_exit_switch` int(1) DEFAULT NULL COMMENT '连续未出勤自动退场天数开关，0停用，1启用',
                                      `group_switch` int(1) DEFAULT NULL COMMENT '班组出勤率低预警(百分比)开关，0停用，1启用',
                                      `man_age_switch` int(1) DEFAULT NULL COMMENT '男性超龄开关，0停用，1启用',
                                      `woman_age_switch` int(1) DEFAULT NULL COMMENT '女性超龄开关，0停用，1启用',
                                      `certificate_switch` int(1) DEFAULT NULL COMMENT '证书剩余有效期开关，0停用，1启用',
                                      `temperature_switch` int(1) DEFAULT NULL COMMENT '体温预警值开关，0停用，1启用',
                                      `health_code_switch` int(1) DEFAULT NULL COMMENT '健康码预警开关，0停用，1启用',
                                      `user_out_check_` varchar(10) DEFAULT NULL COMMENT '人员出场检测时间，10:00',
                                      `user_out_switch` int(1) DEFAULT NULL COMMENT '人员出场预警开关，0停用，1启用',
                                      `user_stay_duration` int(2) DEFAULT NULL COMMENT '停留时长设置，整数',
                                      `user_stay_switch` int(1) DEFAULT NULL COMMENT '停留时长开关，0停用，1启用',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警配置表';


-- linkappdb.app_warning_notifier definition

CREATE TABLE `app_warning_notifier` (
                                        `id` varchar(32) NOT NULL COMMENT 'ID',
                                        `tenant_id_` varchar(50) NOT NULL COMMENT '项目ID(linkapp_tenant)',
                                        `warning_type` int(1) NOT NULL COMMENT '预警类型 1出勤预警 2班组预警 3年龄预警 4证书到期预警 5人员出场异常预警 6疫情预警',
                                        `warning_notifiers` text NOT NULL COMMENT '预警通知人，userIds',
                                        `notice_switch` int(1) NOT NULL COMMENT '推送开关，0停用，1启用',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警通知人表';


-- linkappdb.app_water_records definition

CREATE TABLE `app_water_records` (
                                     `id` varchar(32) NOT NULL COMMENT 'ID 主键',
                                     `device_code` varchar(32) NOT NULL COMMENT '设备编码',
                                     `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                     `collect_time` datetime DEFAULT NULL COMMENT '采集时间',
                                     `water_increment` double(20,2) DEFAULT NULL COMMENT '用水增量',
                                     `water_total` double(20,2) DEFAULT NULL COMMENT '累计用水',
                                     `stop_reading` double(20,2) DEFAULT NULL COMMENT '止码读数',
                                     `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                     `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                     `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                     `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                     `remark_` text COMMENT '备注',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用水记录';


-- linkappdb.base_role_ref_privilege definition

CREATE TABLE `base_role_ref_privilege` (
                                           `role_id_` bigint(20) NOT NULL,
                                           `privilege_id_` bigint(20) NOT NULL,
                                           PRIMARY KEY (`role_id_`,`privilege_id_`),
                                           KEY `FK9l1i31pre02g54tx9vhlir922` (`privilege_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.cbjf_base_billing definition

CREATE TABLE `cbjf_base_billing` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `price_name` varchar(225) DEFAULT NULL COMMENT '计费标准名称',
                                     `meter_type` int(5) DEFAULT NULL COMMENT '仪表类型标识（0:水表，1:电表）',
                                     `price` decimal(5,2) DEFAULT NULL COMMENT '价格',
                                     `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                     `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `delete_status` int(5) DEFAULT '0' COMMENT '删除状态(0：存在，1：删除)',
                                     `is_valid` int(5) DEFAULT '0' COMMENT '是否有效(0：有效，1：无效)',
                                     `unit` varchar(225) DEFAULT NULL COMMENT '计量单位',
                                     `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计费标准（各项费用基础最新计费标准）';


-- linkappdb.cbjf_base_billing_history definition

CREATE TABLE `cbjf_base_billing_history` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                             `billing_id` int(11) DEFAULT NULL COMMENT '计费标准ID',
                                             `price_name` varchar(225) DEFAULT NULL COMMENT '计费标准名称',
                                             `meter_type` int(5) DEFAULT NULL COMMENT '仪表类型标识（0:水表，1:电表）',
                                             `price` decimal(5,2) DEFAULT NULL COMMENT '价格',
                                             `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                             `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `delete_status` int(5) DEFAULT '0' COMMENT '删除状态(0：存在，1：删除)',
                                             `is_valid` int(5) DEFAULT '0' COMMENT '是否有效(0：有效，1：无效)',
                                             `unit` varchar(225) DEFAULT NULL COMMENT '计量单位',
                                             `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                             `version` int(5) DEFAULT NULL COMMENT '版本号',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='存储计费标准历史计费，该表只新增不做修改';


-- linkappdb.cbjf_expenses_timer_task definition

CREATE TABLE `cbjf_expenses_timer_task` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                            `query_time_start` datetime DEFAULT NULL COMMENT '结算开始时间',
                                            `query_time_end` datetime DEFAULT NULL COMMENT '结算结束时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣费定时任务运行表';


-- linkappdb.cbjf_meter_device_config definition

CREATE TABLE `cbjf_meter_device_config` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                            `device_code` varchar(225) DEFAULT NULL COMMENT '设备编码',
                                            `room_id` int(11) DEFAULT NULL COMMENT '房间ID',
                                            `meter_type` tinyint(1) NOT NULL COMMENT '水电表类型 0：水表:1：电表',
                                            `record_time` time DEFAULT NULL COMMENT '抄表时间',
                                            `day_threshold` decimal(10,2) DEFAULT NULL COMMENT '日用阀值',
                                            `month_threshold` decimal(10,2) DEFAULT NULL COMMENT '月用阀值',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `delete_status` int(5) DEFAULT '0' COMMENT '删除状态（0：正常，1：删除）',
                                            `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='水电表设备扩展表, 设置抄表时间，预警阀值';


-- linkappdb.cbjf_meter_remedy definition

CREATE TABLE `cbjf_meter_remedy` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `room_id` int(11) DEFAULT NULL COMMENT '房间ID',
                                     `device_code` varchar(32) DEFAULT NULL COMMENT '设备编码',
                                     `energy_amount` decimal(10,2) DEFAULT NULL COMMENT '补录码',
                                     `remedy_time` datetime DEFAULT NULL COMMENT '补录时间',
                                     `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                     `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                     `calculate_status` int(5) DEFAULT '0' COMMENT '下次统计计算以此补录数据为起始止码状态（0：下次统计未计算，1：已计算）',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备抄表补录数据表';


-- linkappdb.cbjf_recharge_record definition

CREATE TABLE `cbjf_recharge_record` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `order_num` varchar(32) DEFAULT NULL COMMENT '订单号',
                                        `contract_id` int(11) DEFAULT NULL COMMENT '合同ID',
                                        `recharge_type` int(5) DEFAULT NULL COMMENT '充值类型（0：水表:1：电表）',
                                        `type` int(5) DEFAULT '0' COMMENT '类型（0：充值，1：退款）',
                                        `price` decimal(10,2) DEFAULT NULL COMMENT '充值金额',
                                        `pay_type` int(5) DEFAULT NULL COMMENT '支付方式（0：现金，1：微信支付）',
                                        `recharge_platform` int(5) DEFAULT NULL COMMENT '充值平台（0：pc端 :1：小程序）',
                                        `notify_time` datetime DEFAULT NULL COMMENT '到账时间',
                                        `recharge_status` int(5) DEFAULT NULL COMMENT '充值状态（0：创建:1：成功:2：失败）',
                                        `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
                                        `payer` varchar(32) DEFAULT NULL COMMENT '支付人',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `operator` varchar(32) DEFAULT NULL COMMENT '操作人',
                                        `third_order_num` varchar(32) DEFAULT NULL COMMENT '第三方订单号',
                                        `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值订单';


-- linkappdb.cbjf_recharge_record_copy_1025 definition

CREATE TABLE `cbjf_recharge_record_copy_1025` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                  `order_num` varchar(32) DEFAULT NULL COMMENT '订单号',
                                                  `contract_id` int(11) DEFAULT NULL COMMENT '合同ID',
                                                  `recharge_type` int(5) DEFAULT NULL COMMENT '充值类型（0：水表:1：电表）',
                                                  `type` int(5) DEFAULT '0' COMMENT '类型（0：充值，1：退款）',
                                                  `price` decimal(10,2) DEFAULT NULL COMMENT '充值金额',
                                                  `pay_type` int(5) DEFAULT NULL COMMENT '支付方式（0：现金，1：微信支付）',
                                                  `recharge_platform` int(5) DEFAULT NULL COMMENT '充值平台（0：pc端 :1：小程序）',
                                                  `notify_time` datetime DEFAULT NULL COMMENT '到账时间',
                                                  `recharge_status` int(5) DEFAULT NULL COMMENT '充值状态（0：创建:1：成功:2：失败）',
                                                  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
                                                  `payer` varchar(32) DEFAULT NULL COMMENT '支付人',
                                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                  `operator` varchar(32) DEFAULT NULL COMMENT '操作人',
                                                  `third_order_num` varchar(32) DEFAULT NULL COMMENT '第三方订单号',
                                                  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值订单';


-- linkappdb.cbjf_resident_info definition

CREATE TABLE `cbjf_resident_info` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `name` varchar(225) DEFAULT NULL COMMENT '姓名',
                                      `phone` varchar(225) DEFAULT NULL COMMENT '手机号',
                                      `password` varchar(225) DEFAULT NULL COMMENT '密码',
                                      `delete_status` int(5) DEFAULT NULL COMMENT '删除状态（0：正常，1：删除）',
                                      `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                      `create_time` date DEFAULT NULL COMMENT '创建时间',
                                      `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='住户信息';


-- linkappdb.cbjf_room_billing_ref definition

CREATE TABLE `cbjf_room_billing_ref` (
                                         `room_id` int(11) DEFAULT NULL COMMENT '房间ID',
                                         `billing_id` int(11) DEFAULT NULL COMMENT '计费标准ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房屋计费标准关联表';


-- linkappdb.cbjf_room_expenses_bill definition

CREATE TABLE `cbjf_room_expenses_bill` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `contract_id` varchar(32) DEFAULT NULL COMMENT '合同ID',
                                           `settlement_time_start` datetime DEFAULT NULL COMMENT '开始结算时间',
                                           `settlement_time_end` datetime DEFAULT NULL COMMENT '结束结算时间',
                                           `energy_amount` decimal(10,2) DEFAULT NULL COMMENT '结算时间段内的能耗',
                                           `device_code` varchar(32) DEFAULT NULL COMMENT '设备编码',
                                           `consume_fee` decimal(10,2) DEFAULT NULL COMMENT '消费金额',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `deduction_status` int(5) DEFAULT NULL COMMENT '扣费状态（0：成功，1：失败）',
                                           `exception_status` int(5) DEFAULT NULL COMMENT '能耗异常状态（0：正常，1：异常）',
                                           `billing_id` int(11) DEFAULT NULL COMMENT '计费标准ID',
                                           `version` int(11) DEFAULT NULL COMMENT '版本号',
                                           `operator` varchar(32) DEFAULT NULL COMMENT '操作人(null:自动扣费，有值则是补录扣费)',
                                           `remedy_status` int(5) DEFAULT '0' COMMENT '0：自动扣费，1：补录扣费',
                                           `end_read_amount` decimal(10,2) DEFAULT NULL COMMENT '计算止码',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间消费账单（记录每天水电表消费扣款数据）';


-- linkappdb.cbjf_room_info definition

CREATE TABLE `cbjf_room_info` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `room_name` varchar(255) DEFAULT NULL COMMENT '房间名称',
                                  `room_code` varchar(225) DEFAULT NULL COMMENT '房间编码',
                                  `area_id` varchar(32) DEFAULT NULL COMMENT '所属空间ID',
                                  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                  `delete_status` int(5) DEFAULT '0' COMMENT '删除状态（0：正常，1：删除）',
                                  `water_balance` decimal(10,2) DEFAULT NULL COMMENT '水费余额',
                                  `electricity_balance` decimal(10,2) DEFAULT NULL COMMENT '电费余额',
                                  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间信息管理表';


-- linkappdb.cbjf_tenantry_info definition

CREATE TABLE `cbjf_tenantry_info` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `room_id` int(11) DEFAULT NULL COMMENT '房间ID',
                                      `resident_id` int(11) DEFAULT NULL COMMENT '住户ID',
                                      `contract_start_time` date DEFAULT NULL COMMENT '合同开始时间',
                                      `contract_end_time` date DEFAULT NULL COMMENT '合同结束时间',
                                      `delete_status` int(5) DEFAULT NULL COMMENT '删除状态（0：正常，1：删除）',
                                      `lock_status` int(5) DEFAULT NULL COMMENT '锁定状态（0：正常，1：锁定）',
                                      `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                      `create_time` date DEFAULT NULL COMMENT '创建时间',
                                      `bind_status` int(5) DEFAULT NULL COMMENT '绑定房间状态（0：绑定，1：解绑）',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同信息';


-- linkappdb.common_operate_log definition

CREATE TABLE `common_operate_log` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `user_account` varchar(32) DEFAULT NULL COMMENT '用户账号',
                                      `nickname` varchar(32) DEFAULT NULL COMMENT '姓名',
                                      `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                      `phone_` varchar(16) DEFAULT NULL COMMENT '操作人手机号',
                                      `platform_` varchar(16) DEFAULT NULL COMMENT '操作平台',
                                      `module_name` varchar(50) DEFAULT NULL COMMENT '模块名称',
                                      `content` varchar(200) DEFAULT NULL COMMENT '操作内容',
                                      `result_content` text COMMENT '结果内容，或异常信息',
                                      `params` longtext COMMENT '参数内容',
                                      `result` bit(1) DEFAULT NULL COMMENT '操作结果 false-失败 true-成功',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6981 DEFAULT CHARSET=utf8mb4;


-- linkappdb.device_ref_area_scope definition

CREATE TABLE `device_ref_area_scope` (
                                         `device_code` varchar(32) NOT NULL COMMENT '设备编号',
                                         `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                         `function_identifier` varchar(50) NOT NULL COMMENT '功能标识',
                                         `area_id` varchar(32) NOT NULL COMMENT '区域id',
                                         `area_path` varchar(512) DEFAULT NULL COMMENT '区域位置',
                                         KEY `union_device_index` (`device_code`,`tenant_id`,`function_identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备关联功能关联作用区域';


-- linkappdb.device_video_monitor definition

CREATE TABLE `device_video_monitor` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `device_code` varchar(225) DEFAULT NULL COMMENT '设备编码',
                                        `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_devicecode` (`device_code`(191))
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='视频监控';


-- linkappdb.distribution_cabinet definition

CREATE TABLE `distribution_cabinet` (
                                        `id` varchar(32) NOT NULL COMMENT '主键',
                                        `NAME` varchar(50) DEFAULT NULL COMMENT '配电柜类型名称',
                                        `distribution_cabinet_type_id` varchar(32) DEFAULT NULL COMMENT '配电柜类型id',
                                        `distribution_room_id` varchar(32) DEFAULT NULL COMMENT '配电站id',
                                        `sort_no` int(8) DEFAULT NULL COMMENT '排序编号',
                                        `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                        `MODIFIER` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                        `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜';


-- linkappdb.distribution_cabinet_alarm_config definition

CREATE TABLE `distribution_cabinet_alarm_config` (
                                                     `cabinet_id` varchar(32) NOT NULL COMMENT '配电柜ID',
                                                     `device_code` varchar(32) NOT NULL COMMENT '设备code',
                                                     `type` tinyint(1) NOT NULL COMMENT '指标类型 1：负荷指标 2:不平衡度指标',
                                                     `value_max` decimal(10,2) DEFAULT NULL COMMENT '负荷上限,严重不平衡度',
                                                     `value_min` decimal(10,2) DEFAULT NULL COMMENT '负荷下限,不平衡度',
                                                     PRIMARY KEY (`cabinet_id`,`device_code`,`type`),
                                                     KEY `device_code_index` (`device_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜-指标设置';


-- linkappdb.distribution_cabinet_alarm_info definition

CREATE TABLE `distribution_cabinet_alarm_info` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                   `cabinet_id` varchar(32) NOT NULL COMMENT '配电柜ID',
                                                   `device_code` varchar(32) NOT NULL COMMENT '设备code',
                                                   `name` varchar(32) NOT NULL COMMENT '告警规则名称',
                                                   `content` varchar(32) NOT NULL COMMENT '告警规则名称',
                                                   `source_json` text COMMENT '告警数据日志',
                                                   `level` tinyint(1) DEFAULT NULL COMMENT '告警等级；1高，2中，3低',
                                                   `status` tinyint(1) DEFAULT NULL COMMENT '处理状态；1未处理，2已处理',
                                                   `alarm_status` tinyint(1) DEFAULT NULL COMMENT '误报标志，0 非误报，1误报',
                                                   `alarm_time` datetime DEFAULT NULL COMMENT '告警时间',
                                                   `user_id` varchar(32) DEFAULT NULL COMMENT '处理人',
                                                   `deal_time` datetime DEFAULT NULL COMMENT '处理时间',
                                                   `description` varchar(255) DEFAULT NULL COMMENT '处理结果',
                                                   PRIMARY KEY (`id`),
                                                   KEY `device_code_index` (`cabinet_id`,`device_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜-告警信息';


-- linkappdb.distribution_cabinet_archives definition

CREATE TABLE `distribution_cabinet_archives` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                 `manufacturer` varchar(32) DEFAULT NULL COMMENT '生产厂商',
                                                 `dom` datetime DEFAULT NULL COMMENT '出厂日期',
                                                 `rated_capacity` varchar(32) DEFAULT NULL COMMENT '额定容量',
                                                 `rated_voltage` varchar(32) DEFAULT NULL COMMENT '额定电压',
                                                 `address` varchar(100) DEFAULT NULL COMMENT '安装位置',
                                                 `remarks` varchar(100) DEFAULT NULL COMMENT '备注',
                                                 `cabinet_id` varchar(32) NOT NULL COMMENT '配电柜ID',
                                                 PRIMARY KEY (`id`),
                                                 KEY `cabinet_id_index` (`cabinet_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜-档案信息';


-- linkappdb.distribution_cabinet_configuration definition

CREATE TABLE `distribution_cabinet_configuration` (
                                                      `id` varchar(32) NOT NULL COMMENT '主键',
                                                      `NAME` varchar(50) DEFAULT NULL COMMENT '名称',
                                                      `description` varchar(225) DEFAULT NULL COMMENT '描述',
                                                      `status_picture` varchar(500) DEFAULT NULL COMMENT '状态图',
                                                      `distribution_cabinet_type_site_id` varchar(32) DEFAULT NULL COMMENT '配电柜类型的位置信息id',
                                                      `group_number` int(11) DEFAULT '0' COMMENT '组编号',
                                                      `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                      `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                      `MODIFIER` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜类型组态';


-- linkappdb.distribution_cabinet_configuration_expression definition

CREATE TABLE `distribution_cabinet_configuration_expression` (
                                                                 `id` varchar(32) NOT NULL,
                                                                 `distribution_cabinet_configuration_id` varchar(32) NOT NULL COMMENT '组态id',
                                                                 `device_unit_code` varchar(32) DEFAULT NULL COMMENT '设备型号code',
                                                                 `device_unit_version` varchar(32) DEFAULT NULL COMMENT '设备型号版本号',
                                                                 `device_attribute_identifier` varchar(32) DEFAULT NULL COMMENT '设备属性标志符',
                                                                 `device_attribute_parent_identifier` varchar(32) DEFAULT NULL COMMENT '父属性属性标志符',
                                                                 `calculate_sign` varchar(10) DEFAULT NULL COMMENT '算术符',
                                                                 `VALUE` varchar(50) DEFAULT NULL COMMENT '数值',
                                                                 `sort_no` int(8) DEFAULT NULL COMMENT '排序',
                                                                 `logic_code` varchar(50) DEFAULT NULL COMMENT '逻辑运算符（与、或）',
                                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组态表达式';


-- linkappdb.distribution_cabinet_file definition

CREATE TABLE `distribution_cabinet_file` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                             `cabinet_id` varchar(32) NOT NULL COMMENT '配电柜ID',
                                             `file_url` varchar(255) DEFAULT NULL COMMENT '文件url',
                                             `file_name` varchar(225) DEFAULT NULL COMMENT '文件名称',
                                             PRIMARY KEY (`id`),
                                             KEY `cabinet_id_index` (`cabinet_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜-文档信息';


-- linkappdb.distribution_cabinet_ref_device definition

CREATE TABLE `distribution_cabinet_ref_device` (
                                                   `id` varchar(32) NOT NULL COMMENT '主键',
                                                   `distribution_cabinet_id` varchar(32) DEFAULT NULL COMMENT '配电柜id',
                                                   `device_code` varchar(32) DEFAULT NULL COMMENT '设备code',
                                                   `distribution_cabinet_type_site_id` varchar(32) DEFAULT NULL COMMENT '配电柜类型位置id',
                                                   PRIMARY KEY (`id`),
                                                   KEY `idx_distribution_cabinet_id` (`distribution_cabinet_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜关联设备';


-- linkappdb.distribution_cabinet_repair_file definition

CREATE TABLE `distribution_cabinet_repair_file` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                    `cabinet_repair_id` int(11) NOT NULL COMMENT '检修记录ID',
                                                    `file_url` varchar(255) DEFAULT NULL COMMENT '文件url',
                                                    `file_name` varchar(225) DEFAULT NULL COMMENT '文件名称',
                                                    PRIMARY KEY (`id`),
                                                    KEY `cabinet_repair_id_index` (`cabinet_repair_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜-检修记录';


-- linkappdb.distribution_cabinet_repair_record definition

CREATE TABLE `distribution_cabinet_repair_record` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                      `name` varchar(32) NOT NULL COMMENT '姓名',
                                                      `content` varchar(100) DEFAULT NULL COMMENT '内容',
                                                      `repair_time` datetime DEFAULT NULL COMMENT '检修时间',
                                                      `status` tinyint(1) DEFAULT NULL COMMENT '处理状态；0 异常，1正常',
                                                      `cabinet_id` varchar(32) NOT NULL COMMENT '配电柜ID',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      PRIMARY KEY (`id`),
                                                      KEY `cabinet_id_index` (`cabinet_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜-检修记录';


-- linkappdb.distribution_cabinet_status definition

CREATE TABLE `distribution_cabinet_status` (
                                               `id` varchar(32) NOT NULL COMMENT '主键',
                                               `distribution_cabinet_id` varchar(32) DEFAULT NULL COMMENT '配电柜id ',
                                               `distribution_cabinet_configuration_id` varchar(32) DEFAULT NULL COMMENT '组态id',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜状态(冗余部分组态字段)';


-- linkappdb.distribution_cabinet_type definition

CREATE TABLE `distribution_cabinet_type` (
                                             `id` varchar(32) NOT NULL COMMENT '主键',
                                             `NAME` varchar(50) DEFAULT NULL COMMENT '规则联动配置名称',
                                             `description` varchar(225) DEFAULT NULL COMMENT '描述',
                                             `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                             `MODIFIER` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜类型';


-- linkappdb.distribution_cabinet_type_site definition

CREATE TABLE `distribution_cabinet_type_site` (
                                                  `id` varchar(32) NOT NULL COMMENT '主键',
                                                  `site_name` varchar(50) DEFAULT NULL COMMENT '位置名称',
                                                  `electric_equipment` tinyint(1) DEFAULT '0' COMMENT '是否是电力设备,0-否，1-是',
                                                  `sort_no` tinyint(1) DEFAULT '0' COMMENT '排序',
                                                  `required` tinyint(1) DEFAULT '1' COMMENT '是否必填,0-否，1-是',
                                                  `device_unit_code` varchar(32) DEFAULT NULL COMMENT '型号code',
                                                  `device_unit_version` varchar(32) DEFAULT NULL COMMENT '型号版本号',
                                                  `distribution_cabinet_type_id` varchar(32) DEFAULT NULL COMMENT '配电柜类型id',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜类型关联位置信息';


-- linkappdb.distribution_company_alarm_contact_mid definition

CREATE TABLE `distribution_company_alarm_contact_mid` (
                                                          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                          `company_id` int(11) NOT NULL COMMENT '企业ID',
                                                          `alarm_contact_id` varchar(32) DEFAULT NULL COMMENT '告警联系人ID',
                                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电管理-企业告警联系人关联表';


-- linkappdb.distribution_company_info definition

CREATE TABLE `distribution_company_info` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                             `company_name` varchar(225) NOT NULL COMMENT '企业名称',
                                             `contacts` varchar(225) DEFAULT NULL COMMENT '联系人',
                                             `phone` varchar(300) DEFAULT NULL COMMENT '电话',
                                             `remark` varchar(300) DEFAULT NULL COMMENT '备注',
                                             `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                             `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电管理-企业管理表';


-- linkappdb.distribution_electric_price definition

CREATE TABLE `distribution_electric_price` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                               `name` varchar(32) NOT NULL COMMENT '名称',
                                               `description` varchar(250) DEFAULT NULL COMMENT '描述',
                                               `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                               `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜-电价方案';


-- linkappdb.distribution_electric_price_config definition

CREATE TABLE `distribution_electric_price_config` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                      `month` varchar(32) NOT NULL COMMENT '月份集合',
                                                      `type` tinyint(1) NOT NULL COMMENT '是否分时：0 分时用电,1 不分时',
                                                      `electric_price_id` int(11) NOT NULL COMMENT '电价方案ID',
                                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜-电价方案-分时用电设置';


-- linkappdb.distribution_electric_price_config_time definition

CREATE TABLE `distribution_electric_price_config_time` (
                                                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                           `type` tinyint(1) NOT NULL COMMENT '峰谷周期 1 尖，2 峰，3 平，4 谷',
                                                           `start_time` time NOT NULL COMMENT '开始时间',
                                                           `end_time` time NOT NULL COMMENT '结束时间',
                                                           `price` decimal(10,2) NOT NULL COMMENT '单价',
                                                           `electric_price_config_id` int(11) NOT NULL COMMENT '分时用电设置id',
                                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电柜-电价方案-分时用电设置-时间设置';


-- linkappdb.distribution_room definition

CREATE TABLE `distribution_room` (
                                     `id` varchar(32) NOT NULL COMMENT '主键',
                                     `NAME` varchar(50) DEFAULT NULL COMMENT '名称',
                                     `area_id` varchar(32) DEFAULT NULL COMMENT '区域id',
                                     `description` varchar(225) DEFAULT NULL COMMENT '描述',
                                     `delete_state` int(2) NOT NULL DEFAULT '1' COMMENT '逻辑删除，1存在，0已删除',
                                     `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                     `latitude` varchar(20) DEFAULT NULL COMMENT '纬度',
                                     `longitude` varchar(20) DEFAULT NULL COMMENT '经度',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                     `MODIFIER` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                     `video_path` text COMMENT '视频地址',
                                     `company_info_id` int(11) DEFAULT NULL COMMENT '企业管理ID',
                                     `electric_price_id` int(11) DEFAULT NULL COMMENT '电价方案ID',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电房';


-- linkappdb.distribution_room_ref_device definition

CREATE TABLE `distribution_room_ref_device` (
                                                `id` varchar(32) NOT NULL COMMENT '主键',
                                                `distribution_room_id` varchar(32) DEFAULT NULL COMMENT '配电房id',
                                                `device_code` varchar(32) DEFAULT NULL COMMENT '设备编号',
                                                `sort_no` int(2) DEFAULT '0' COMMENT '排序编号',
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配电房关联设备';


-- linkappdb.emp_user_base definition

CREATE TABLE `emp_user_base` (
                                 `id` varchar(32) NOT NULL COMMENT 'ID',
                                 `emp_uid_` bigint(20) DEFAULT NULL COMMENT 'emp user表id',
                                 `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                                 `gender_` int(2) DEFAULT NULL COMMENT '性别 1男2女',
                                 `telephone_` varchar(32) DEFAULT NULL COMMENT '电话',
                                 `education_` varchar(32) DEFAULT NULL COMMENT '学历 字典管理',
                                 `nation_` varchar(32) DEFAULT NULL COMMENT '民族 字典管理',
                                 `degree_` varchar(32) DEFAULT NULL COMMENT '学位',
                                 `birthday_` datetime DEFAULT NULL COMMENT '出生日期',
                                 `card_type_` varchar(32) DEFAULT NULL COMMENT '证件类型',
                                 `card_` varchar(32) DEFAULT NULL COMMENT '身份证号',
                                 `authority_` varchar(50) DEFAULT NULL COMMENT '发证机关 省市区',
                                 `address_` varchar(255) DEFAULT NULL COMMENT '住址',
                                 `card_start_` datetime DEFAULT NULL COMMENT '身份证有效期开始时间',
                                 `card_end_` datetime DEFAULT NULL COMMENT '身份证有效期结束时间',
                                 `photo_` varchar(255) DEFAULT NULL COMMENT '相片url',
                                 `card_a_` varchar(255) DEFAULT NULL COMMENT '身份证正面url',
                                 `card_b_` varchar(255) DEFAULT NULL COMMENT '身份证反面url',
                                 `employ_time_` datetime DEFAULT NULL COMMENT '入职时间',
                                 `fire_time_` datetime DEFAULT NULL COMMENT '离职时间',
                                 `employ_status_` int(2) DEFAULT NULL COMMENT '在职状态 1在职0离职(冗余字段)',
                                 `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                 `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                 `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                 `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                 `remark_` text COMMENT '备注',
                                 `gate_id_` varchar(32) DEFAULT NULL COMMENT '下发到闸机时保存的人员信息数据id',
                                 `real_sys_` int(2) DEFAULT '0' COMMENT '是否来源于实名制平台(1是0否)',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户基础表';


-- linkappdb.`emp_user_base-new` definition

CREATE TABLE `emp_user_base-new` (
                                     `id` varchar(32) NOT NULL COMMENT 'ID',
                                     `emp_uid_` bigint(20) DEFAULT NULL COMMENT 'emp user表id',
                                     `name_` varchar(32) DEFAULT NULL COMMENT '名称',
                                     `gender_` int(2) DEFAULT NULL COMMENT '性别 1男2女',
                                     `telephone_` varchar(32) DEFAULT NULL COMMENT '电话',
                                     `education_` varchar(32) DEFAULT NULL COMMENT '学历 字典管理',
                                     `nation_` varchar(32) DEFAULT NULL COMMENT '民族 字典管理',
                                     `degree_` varchar(32) DEFAULT NULL COMMENT '学位',
                                     `birthday_` datetime DEFAULT NULL COMMENT '出生日期',
                                     `card_type_` varchar(32) DEFAULT NULL COMMENT '证件类型',
                                     `card_` varchar(32) DEFAULT NULL COMMENT '身份证号',
                                     `authority_` varchar(50) DEFAULT NULL COMMENT '发证机关 省市区',
                                     `address_` varchar(255) DEFAULT NULL COMMENT '住址',
                                     `card_start_` datetime DEFAULT NULL COMMENT '身份证有效期开始时间',
                                     `card_end_` datetime DEFAULT NULL COMMENT '身份证有效期结束时间',
                                     `photo_` varchar(255) DEFAULT NULL COMMENT '相片url',
                                     `card_a_` varchar(255) DEFAULT NULL COMMENT '身份证正面url',
                                     `card_b_` varchar(255) DEFAULT NULL COMMENT '身份证反面url',
                                     `employ_time_` datetime DEFAULT NULL COMMENT '入职时间',
                                     `fire_time_` datetime DEFAULT NULL COMMENT '离职时间',
                                     `employ_status_` int(2) DEFAULT NULL COMMENT '在职状态 1在职0离职(冗余字段)',
                                     `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                     `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
                                     `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
                                     `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
                                     `remark_` text COMMENT '备注',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户基础表';


-- linkappdb.gfd_role_ref_area definition

CREATE TABLE `gfd_role_ref_area` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `role_id` bigint(20) NOT NULL COMMENT '角色id',
                                     `area_id` varchar(512) DEFAULT NULL COMMENT '区域路径',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `modify_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `tenant_id` varchar(32) DEFAULT NULL,
                                     PRIMARY KEY (`id`),
                                     KEY `index_role_id` (`role_id`) USING BTREE,
                                     KEY `index_area_id` (`area_id`(191)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国防大角色关联区域';


-- linkappdb.hcmy_aquaculture_area definition

CREATE TABLE `hcmy_aquaculture_area` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                         `as_id` int(11) NOT NULL COMMENT '水产养殖(示范区)ID',
                                         `area_id` varchar(32) NOT NULL COMMENT '示范村ID',
                                         `aquaculture_area` decimal(11,3) DEFAULT NULL COMMENT '水产养殖面积',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='汉川面源生态-水产养殖(示范村)';


-- linkappdb.hcmy_aquaculture_space definition

CREATE TABLE `hcmy_aquaculture_space` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                          `statistic_year` int(5) NOT NULL COMMENT '统计年度',
                                          `space_id` varchar(32) NOT NULL COMMENT '示范区ID',
                                          `aquaculture_total_area` decimal(11,3) DEFAULT NULL COMMENT '水产养殖总面积',
                                          `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                          `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                          `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='汉川面源生态-水产养殖(示范区)';


-- linkappdb.hcmy_areasourceecology_dictionary definition

CREATE TABLE `hcmy_areasourceecology_dictionary` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                     `name` varchar(32) NOT NULL COMMENT '类型名称',
                                                     `seq` decimal(8,2) DEFAULT NULL,
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='面源生态-字典';


-- linkappdb.hcmy_areasourceecology_dictionary_function_data definition

CREATE TABLE `hcmy_areasourceecology_dictionary_function_data` (
                                                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                                   `menu_id` int(11) NOT NULL COMMENT '字典类型id',
                                                                   `number` decimal(11,3) DEFAULT NULL COMMENT '列表数值',
                                                                   `function_id` int(11) NOT NULL COMMENT '功能数据表id',
                                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='面源生态-菜单表';


-- linkappdb.hcmy_areasourceecology_function definition

CREATE TABLE `hcmy_areasourceecology_function` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                   `year` int(4) NOT NULL COMMENT '年度',
                                                   `area_ids` text NOT NULL COMMENT '区域ids',
                                                   `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                   `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                                   `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `type` int(11) NOT NULL COMMENT '菜单类型 0:位置信息,1:土地利用,2:作物播种,3:肥料使用,4:施肥流失量',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='面源生态-功能数据表';


-- linkappdb.hcmy_areasourceecology_menu definition

CREATE TABLE `hcmy_areasourceecology_menu` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                               `dictionary_id` int(11) NOT NULL COMMENT '字典类型id',
                                               `type` int(11) NOT NULL COMMENT '菜单类型',
                                               PRIMARY KEY (`id`),
                                               KEY `dictionary_id_index` (`dictionary_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='面源生态-菜单表';


-- linkappdb.hcmy_livestock_farmers definition

CREATE TABLE `hcmy_livestock_farmers` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT ' 主键ID ',
                                          `statistic_year` int(5) NOT NULL COMMENT ' 统计年度 ',
                                          `space_id` varchar(32) NOT NULL COMMENT ' 示范区ID ',
                                          `area_id` varchar(32) NOT NULL COMMENT ' 示范村ID ',
                                          `shengzhu_farmers_num` int(11) DEFAULT '0' COMMENT ' 牲猪规模化养殖户数(户)',
                                          `jiaqin_farmers_num` int(11) DEFAULT '0' COMMENT ' 家禽规模化养殖户数(户)',
                                          `tenant_id` varchar(32) DEFAULT NULL COMMENT ' 租户id ',
                                          `create_time` datetime DEFAULT NULL COMMENT ' 创建时间 ',
                                          `creator` varchar(32) DEFAULT NULL COMMENT ' 创建人id ',
                                          `modifier` varchar(32) DEFAULT NULL COMMENT ' 修改人id ',
                                          `modify_time` datetime DEFAULT NULL COMMENT ' 修改时间 ',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='汉川面源生态-畜禽养殖户表';


-- linkappdb.hcmy_livestock_farming definition

CREATE TABLE `hcmy_livestock_farming` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                          `statistic_year` int(5) NOT NULL COMMENT '统计年度',
                                          `space_id` varchar(32) NOT NULL COMMENT '示范区ID',
                                          `area_id` varchar(32) NOT NULL COMMENT '示范村ID',
                                          `pig_num` int(11) DEFAULT '0' COMMENT ' 养殖猪(头)',
                                          `meat_poultry_num` int(11) DEFAULT '0' COMMENT ' 养殖肉禽(只)',
                                          `egg_num` int(11) DEFAULT '0' COMMENT ' 养殖蛋禽(只)',
                                          `sheep_num` int(11) DEFAULT '0' COMMENT ' 养殖羊(只)',
                                          `cattle_num` int(11) DEFAULT '0' COMMENT ' 养殖牛(只)',
                                          `tenant_id` varchar(32) DEFAULT NULL COMMENT ' 租户id ',
                                          `create_time` datetime DEFAULT NULL COMMENT ' 创建时间 ',
                                          `creator` varchar(32) DEFAULT NULL COMMENT ' 创建人id ',
                                          `modifier` varchar(32) DEFAULT NULL COMMENT ' 修改人id ',
                                          `modify_time` datetime DEFAULT NULL COMMENT ' 修改时间 ',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='汉川面源生态-畜禽养殖表';


-- linkappdb.hibernate_sequence definition

CREATE TABLE `hibernate_sequence` (
    `next_val` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.investigation_task definition

CREATE TABLE `investigation_task` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                      `name_` varchar(50) NOT NULL COMMENT '名称',
                                      `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                      `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                      `status_` tinyint(1) DEFAULT NULL COMMENT '状态,0-待处理，1-处理中，2-已完成',
                                      `investigation_task_model_id` bigint(20) DEFAULT NULL COMMENT '排查任务模板id',
                                      `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                      `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`id`),
                                      KEY `tenant_id_index` (`tenant_id`) USING BTREE,
                                      KEY `investigation_task_model_id_index` (`investigation_task_model_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排查任务';


-- linkappdb.investigation_task_detail definition

CREATE TABLE `investigation_task_detail` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                             `investigation_task_id` bigint(20) NOT NULL COMMENT '排查任务id',
                                             `investigation_type_name` varchar(50) NOT NULL COMMENT '类型名称',
                                             `sort_no` int(2) NOT NULL COMMENT '序号',
                                             `user_id` varchar(32) NOT NULL COMMENT '处理人',
                                             `user_name` varchar(50) DEFAULT NULL COMMENT '处理人名称',
                                             `area_id` varchar(32) DEFAULT NULL COMMENT '区域id',
                                             `area_path` varchar(512) DEFAULT NULL COMMENT '区域路径',
                                             `view_status` tinyint(1) DEFAULT NULL COMMENT '检视状态,0-异常，1-正常',
                                             `deal_status` tinyint(1) DEFAULT NULL COMMENT '处理状态,0-未处理，1-已处理',
                                             `description` varchar(255) DEFAULT NULL COMMENT '说明',
                                             `deal_time` datetime DEFAULT NULL COMMENT '处理时间',
                                             `facilities_picture` varchar(1000) DEFAULT NULL COMMENT '设施图片',
                                             `face_picture` varchar(255) DEFAULT NULL COMMENT '人脸图片',
                                             `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                             `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                             `face_recognition_status` tinyint(1) DEFAULT NULL COMMENT '人脸识别匹配状态 0-未识别匹配正确 1-已识别匹配正确',
                                             PRIMARY KEY (`id`),
                                             KEY `tenant_id_index` (`tenant_id`) USING BTREE,
                                             KEY `investigation_task_id_index` (`investigation_task_id`) USING BTREE,
                                             KEY `user_id_index` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排查任务详情';


-- linkappdb.investigation_task_model definition

CREATE TABLE `investigation_task_model` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                            `name` varchar(50) NOT NULL COMMENT '名称',
                                            `description` varchar(225) DEFAULT NULL COMMENT '说明',
                                            `status` int(2) NOT NULL COMMENT '状态,0-禁用，1-启用',
                                            `cycle` float(5,2) DEFAULT NULL COMMENT '周期（天）',
                                            `execute_time` datetime DEFAULT NULL COMMENT '执行时间',
                                            `period` float(5,2) DEFAULT NULL COMMENT '时长-期限（h）',
                                            `cycle_cron` varchar(50) DEFAULT NULL COMMENT '周期cron表达式',
                                            `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                            `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                            `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                            `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                            PRIMARY KEY (`id`),
                                            KEY `tenant_id_index` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排查任务模板';


-- linkappdb.investigation_task_model_content definition

CREATE TABLE `investigation_task_model_content` (
                                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                    `investigation_task_model_id` bigint(20) NOT NULL COMMENT '排查任务模板id',
                                                    `investigation_type_id` bigint(20) NOT NULL COMMENT '类型id',
                                                    `sort_no` int(2) DEFAULT NULL COMMENT '序号',
                                                    `area_id` varchar(32) DEFAULT NULL COMMENT '区域id',
                                                    `description` varchar(225) DEFAULT NULL COMMENT '说明',
                                                    `user_id` varchar(32) DEFAULT NULL COMMENT '处理人',
                                                    `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                    `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                                    PRIMARY KEY (`id`),
                                                    KEY `tenant_id_index` (`tenant_id`) USING BTREE,
                                                    KEY `investigation_type_id_index` (`investigation_type_id`) USING BTREE,
                                                    KEY `investigation_task_model_id_index` (`investigation_task_model_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排查任务模板内容';


-- linkappdb.investigation_type definition

CREATE TABLE `investigation_type` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                      `name` varchar(50) NOT NULL COMMENT '名称',
                                      `description` varchar(225) DEFAULT NULL COMMENT '描述',
                                      `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                      `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排查类型';


-- linkappdb.jgl_category definition

CREATE TABLE `jgl_category` (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                `name` varchar(100) DEFAULT NULL COMMENT '中文名',
                                `type` int(1) NOT NULL COMMENT '类别，1-纲，2-科，3-属，4-种',
                                `english_name` varchar(200) DEFAULT NULL COMMENT '英文名',
                                PRIMARY KEY (`id`),
                                KEY `i_type` (`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='鸡公岭植物分类';


-- linkappdb.jgl_expert definition

CREATE TABLE `jgl_expert` (
                              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                              `name` varchar(50) NOT NULL COMMENT '名称',
                              `gender` tinyint(1) DEFAULT NULL COMMENT '0-女，1-男',
                              `company` varchar(50) DEFAULT NULL COMMENT '单位',
                              `job` varchar(50) DEFAULT NULL COMMENT '职称',
                              `avatar` varchar(512) DEFAULT NULL COMMENT '头像地址',
                              `study_plant` varchar(50) DEFAULT NULL COMMENT '研究植物',
                              `remark` varchar(100) DEFAULT NULL COMMENT '备注',
                              `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                              `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                              `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `unique_name_gender_company` (`name`,`gender`,`company`,`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='鸡公岭-专家库';


-- linkappdb.jgl_expert_research_area definition

CREATE TABLE `jgl_expert_research_area` (
                                            `expert_id` int(11) NOT NULL COMMENT '专家id',
                                            `type1` varchar(32) DEFAULT NULL COMMENT '纲',
                                            `type2` varchar(32) DEFAULT NULL COMMENT '科',
                                            `type3` varchar(32) DEFAULT NULL COMMENT '属',
                                            `type4` varchar(32) DEFAULT NULL COMMENT '种'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='鸡公岭-专家研究领域';


-- linkappdb.jgl_plant definition

CREATE TABLE `jgl_plant` (
                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                             `name` varchar(50) NOT NULL COMMENT '名称',
                             `type1` varchar(32) DEFAULT NULL COMMENT '纲',
                             `type2` varchar(32) DEFAULT NULL COMMENT '科',
                             `type3` varchar(32) DEFAULT NULL COMMENT '属',
                             `type4` varchar(32) DEFAULT NULL COMMENT '种',
                             `image` varchar(1024) DEFAULT NULL COMMENT '图片地址',
                             `feature` varchar(2048) DEFAULT NULL COMMENT '形态特征',
                             `area` varchar(1024) DEFAULT NULL COMMENT '分布范围',
                             `attachment` varchar(1024) DEFAULT NULL COMMENT '上传附件地址',
                             `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                             `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                             `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                             `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `unique_name` (`name`,`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='鸡公岭-植物库';


-- linkappdb.jgl_plant_ref_expert definition

CREATE TABLE `jgl_plant_ref_expert` (
                                        `expert_id` int(11) NOT NULL COMMENT '专家id',
                                        `plant_id` int(11) NOT NULL COMMENT '植物id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='鸡公岭-植物与专家关联表';


-- linkappdb.jk_monitor_info definition

CREATE TABLE `jk_monitor_info` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `collect_time` varchar(30) DEFAULT NULL COMMENT '采集时间',
                                   `project_id` varchar(30) DEFAULT NULL COMMENT '项目id，第三方提供',
                                   `data_type` varchar(20) DEFAULT NULL COMMENT '采集数据类型，1基坑监测数据',
                                   `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `jk_monitor_info_collect_time_IDX` (`collect_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10877 DEFAULT CHARSET=utf8 COMMENT='基坑监测记录';


-- linkappdb.jk_monitor_record definition

CREATE TABLE `jk_monitor_record` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `info_id` bigint(20) DEFAULT NULL COMMENT '主记录主键，来源jk_monitor_info',
                                     `warning_level` int(11) DEFAULT NULL COMMENT '状态，0,1,2,3对应绿色，黄色，橙色，红色',
                                     `measurement_project_name` varchar(100) DEFAULT NULL COMMENT '测项名称',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `alarm_value` varchar(6) DEFAULT NULL COMMENT '预警值',
                                     `project_id` varchar(30) DEFAULT NULL COMMENT '项目id，第三方提供',
                                     `total_change_result` varchar(20) DEFAULT NULL COMMENT '累计变化量',
                                     `single_change_result` varchar(20) DEFAULT NULL COMMENT '单次变化量',
                                     `change_rate` varchar(30) DEFAULT NULL COMMENT '变化速率',
                                     `point_name` varchar(30) DEFAULT NULL COMMENT '测点名称',
                                     `is_maximum` tinyint(4) DEFAULT NULL COMMENT '是否为最大值点',
                                     `percent` varchar(40) DEFAULT NULL COMMENT '累计值占预警值百分比',
                                     `source_id` varchar(30) DEFAULT NULL COMMENT '原始id，第三方返回数据id',
                                     `parent_id` bigint(20) DEFAULT NULL COMMENT '父级id，本表id',
                                     `points` varchar(300) DEFAULT NULL COMMENT '定位地理位置，经纬度，存json串',
                                     `storage_time` datetime DEFAULT NULL COMMENT '存储时间',
                                     PRIMARY KEY (`id`),
                                     KEY `jk_monitor_record_info_id_IDX` (`info_id`) USING BTREE,
                                     KEY `jk_monitor_record_parent_id_IDX` (`parent_id`) USING BTREE,
                                     KEY `jk_monitor_record_measurement_project_name_IDX` (`measurement_project_name`) USING BTREE,
                                     KEY `jk_monitor_record_point_name_IDX` (`point_name`) USING BTREE,
                                     KEY `jk_monitor_record_storage_time_IDX` (`storage_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=587566 DEFAULT CHARSET=utf8 COMMENT='基坑监测数据记录';


-- linkappdb.laibin_emergency_drills definition

CREATE TABLE `laibin_emergency_drills` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `plan_name` varchar(225) NOT NULL COMMENT '计划名称',
                                           `address` varchar(225) NOT NULL COMMENT '地点',
                                           `leader` varchar(225) NOT NULL COMMENT '负责人',
                                           `start_time` datetime NOT NULL COMMENT '开始时间',
                                           `end_time` datetime NOT NULL COMMENT '结束时间',
                                           `direction` varchar(300) DEFAULT NULL COMMENT '说明',
                                           `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                           `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来宾-应急演练';


-- linkappdb.laibin_emergency_drills_file definition

CREATE TABLE `laibin_emergency_drills_file` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                `emergency_drills_id` int(11) NOT NULL COMMENT '应急演练ID',
                                                `file_url` varchar(255) DEFAULT NULL COMMENT '文件url',
                                                `file_name` varchar(225) DEFAULT NULL COMMENT '文件名称',
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来宾-应急演练上传文件';


-- linkappdb.laibin_emergency_drills_person definition

CREATE TABLE `laibin_emergency_drills_person` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                  `emergency_drills_id` int(11) NOT NULL COMMENT '应急演练ID',
                                                  `name` varchar(225) NOT NULL COMMENT '参与人员名称',
                                                  `phone` varchar(225) NOT NULL COMMENT '电话',
                                                  `gender` tinyint(3) DEFAULT NULL COMMENT '性别(0:男, 1:女)',
                                                  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
                                                  `score` varchar(225) DEFAULT NULL COMMENT '评分',
                                                  `direction` varchar(300) DEFAULT NULL COMMENT '说明',
                                                  `file_url` varchar(255) DEFAULT NULL COMMENT '文件url',
                                                  `file_name` varchar(225) DEFAULT NULL COMMENT '文件名称',
                                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来宾-应急演练参与人员';


-- linkappdb.laibin_emergency_drills_record definition

CREATE TABLE `laibin_emergency_drills_record` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                  `emergency_drills_id` int(11) NOT NULL COMMENT '应急演练ID',
                                                  `record_name` varchar(225) NOT NULL COMMENT '记录名称',
                                                  `address` varchar(225) NOT NULL COMMENT '地点',
                                                  `record_time` datetime NOT NULL COMMENT '记录时间',
                                                  `record_leader` varchar(225) DEFAULT NULL COMMENT '记录负责人',
                                                  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
                                                  `file_url` varchar(255) DEFAULT NULL COMMENT '文件url',
                                                  `file_name` varchar(225) DEFAULT NULL COMMENT '文件名称',
                                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来宾-应急演练记录';


-- linkappdb.laibin_emergency_training definition

CREATE TABLE `laibin_emergency_training` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                             `train_name` varchar(32) DEFAULT NULL COMMENT '培训名称',
                                             `address` varchar(64) DEFAULT NULL COMMENT '地点',
                                             `leader` varchar(32) DEFAULT NULL COMMENT '负责人',
                                             `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                             `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                             `direction` varchar(256) DEFAULT NULL COMMENT '说明',
                                             `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                             `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应急指挥-应急培训';


-- linkappdb.laibin_emergency_training_file definition

CREATE TABLE `laibin_emergency_training_file` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                  `emergency_training_id` int(11) NOT NULL COMMENT '应急培训ID',
                                                  `file_url` varchar(256) DEFAULT NULL COMMENT '文件url',
                                                  `file_name` varchar(225) DEFAULT NULL COMMENT '文件名称',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来宾-应急培训上传文件';


-- linkappdb.laibin_emergency_training_person definition

CREATE TABLE `laibin_emergency_training_person` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                    `emergency_training_id` int(11) NOT NULL COMMENT '应急培训ID',
                                                    `name` varchar(32) NOT NULL COMMENT '参与人员名称',
                                                    `phone` varchar(16) NOT NULL COMMENT '电话',
                                                    `gender` tinyint(1) DEFAULT NULL COMMENT '性别(0:男, 1:女)',
                                                    `remark` varchar(256) DEFAULT NULL COMMENT '备注',
                                                    `score` varchar(32) DEFAULT NULL COMMENT '评分',
                                                    `direction` varchar(256) DEFAULT NULL COMMENT '说明',
                                                    `file_url` varchar(256) DEFAULT NULL COMMENT '文件url',
                                                    `file_name` varchar(225) DEFAULT NULL COMMENT '文件名称',
                                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来宾-应急演练参与人员';


-- linkappdb.laibin_material_manager definition

CREATE TABLE `laibin_material_manager` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `name` varchar(32) NOT NULL COMMENT '名称',
                                           `number` int(11) NOT NULL COMMENT '数量',
                                           `unit` varchar(32) DEFAULT NULL COMMENT '单位',
                                           `spec` varchar(32) DEFAULT NULL COMMENT '规格',
                                           `state` tinyint(1) NOT NULL COMMENT '状态 0空闲 1使用',
                                           `remarks` varchar(256) DEFAULT NULL COMMENT '描述',
                                           `person` varchar(32) DEFAULT NULL COMMENT '负责人',
                                           `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                           `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
                                           `modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应急指挥-物资管理';


-- linkappdb.linkage_config_ref_down_device definition

CREATE TABLE `linkage_config_ref_down_device` (
                                                  `id` varchar(32) NOT NULL COMMENT '主键',
                                                  `rule_execution_id` varchar(32) DEFAULT NULL COMMENT '执行器id',
                                                  `bak_linkage_config_id` varchar(32) DEFAULT NULL COMMENT '规则联动配置id',
                                                  `device_code` varchar(32) NOT NULL COMMENT '设备编号',
                                                  `sort_no` int(8) DEFAULT '0' COMMENT '排序序号',
                                                  `bak_group_number` int(11) DEFAULT '0' COMMENT '组编号',
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE KEY `config_id_device_code_index` (`bak_linkage_config_id`,`device_code`) USING BTREE,
                                                  KEY `linakge_config_id_index` (`bak_linkage_config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联动配置关联下行设备';


-- linkappdb.linkage_config_relate_service_parm definition

CREATE TABLE `linkage_config_relate_service_parm` (
                                                      `id` varchar(32) NOT NULL COMMENT '主键',
                                                      `rule_execution_id` varchar(32) DEFAULT NULL COMMENT '执行器id',
                                                      `bak_linkage_config_id` varchar(32) DEFAULT NULL COMMENT '规则id',
                                                      `group_number` int(11) NOT NULL COMMENT '组序号',
                                                      `parent_id` varchar(32) DEFAULT NULL COMMENT '父级id',
                                                      `array_index` int(11) DEFAULT NULL COMMENT '数组下标',
                                                      `device_unit_id` varchar(32) NOT NULL COMMENT '设备型号id',
                                                      `device_service_id` varchar(32) NOT NULL COMMENT '设备服务id',
                                                      `device_parm_id` varchar(32) NOT NULL COMMENT '设备参数id',
                                                      `parm_value` varchar(50) DEFAULT NULL COMMENT '参数值',
                                                      PRIMARY KEY (`id`),
                                                      KEY `rule_area_id_index` (`bak_linkage_config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能规则联动配置关联下行设备表';


-- linkappdb.linkapp_alarm definition

CREATE TABLE `linkapp_alarm` (
                                 `id` varchar(32) NOT NULL COMMENT '主键',
                                 `rule_engine_id` varchar(32) DEFAULT NULL COMMENT '规则引擎id',
                                 `video_url` varchar(512) DEFAULT NULL COMMENT '回放视频链接地址',
                                 `device_code` varchar(32) NOT NULL COMMENT '设备编号',
                                 `status` int(2) DEFAULT NULL COMMENT '处理状态；1未处理，2已处理，其他值-其他',
                                 `level` int(2) DEFAULT NULL COMMENT '告警等级；1高，2中，3低',
                                 `content` varchar(4000) DEFAULT NULL COMMENT '告警内容',
                                 `source_json` text,
                                 `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                 `alarm_time` datetime DEFAULT NULL COMMENT '告警时间',
                                 `alarm_data` varchar(1000) DEFAULT NULL COMMENT '告警相关数据',
                                 PRIMARY KEY (`id`),
                                 KEY `ad` (`device_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_alarm_banknotice_log definition

CREATE TABLE `linkapp_alarm_banknotice_log` (
                                                `id` varchar(32) NOT NULL COMMENT '主键',
                                                `alarm_type` tinyint(1) DEFAULT NULL COMMENT '告警类型 1周 2 月',
                                                `data_source_id` varchar(32) DEFAULT NULL COMMENT '数据源id',
                                                `alarm_way` varchar(80) DEFAULT NULL COMMENT '告警方式 电话或者邮箱',
                                                `content` varchar(300) DEFAULT NULL COMMENT '告警内容',
                                                `current_statistic` float(15,2) DEFAULT NULL COMMENT '当周当月',
                                                `mom_statistic` float(15,2) DEFAULT NULL COMMENT '环比上周或者上月统计',
                                                `mom_rate` float(4,2) DEFAULT NULL COMMENT '环比比率',
                                                `status` tinyint(1) DEFAULT NULL COMMENT '通知状态 发送状态；0发送失败，1发送成功',
                                                `alarm_date` datetime DEFAULT NULL COMMENT '告警时间',
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警通知日志';


-- linkappdb.linkapp_alarm_info definition

CREATE TABLE `linkapp_alarm_info` (
                                      `id` varchar(32) NOT NULL,
                                      `alarmTime` varchar(50) DEFAULT NULL,
                                      `oneDayAddCount` int(11) DEFAULT NULL,
                                      `tenantId` varchar(32) DEFAULT NULL,
                                      `spaceId` varchar(32) DEFAULT NULL,
                                      `status` int(11) DEFAULT NULL,
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_alarm_notice_log definition

CREATE TABLE `linkapp_alarm_notice_log` (
                                            `id` varchar(32) NOT NULL COMMENT '主键',
                                            `rule_engine_id` varchar(32) DEFAULT NULL COMMENT '规则引擎id',
                                            `alarm_id` varchar(32) DEFAULT NULL COMMENT '告警id',
                                            `bak_alarm_rule_id` varchar(32) DEFAULT NULL COMMENT '告警id(关联linkapp_alarm)',
                                            `user_id` varchar(32) DEFAULT NULL COMMENT '订阅人id (关联linkapp_user)',
                                            `content` text COMMENT '告警内容',
                                            `status` int(1) DEFAULT NULL COMMENT '发送状态；0发送失败，1发送成功',
                                            `send_time` datetime DEFAULT NULL COMMENT '发送时间',
                                            `message_type` int(1) NOT NULL,
                                            `message_way` varchar(50) NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `alarm_rule_id_index` (`bak_alarm_rule_id`) USING BTREE,
                                            KEY `user_id_index` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警通知日志';


-- linkappdb.linkapp_alarm_notice_templet definition

CREATE TABLE `linkapp_alarm_notice_templet` (
                                                `id` varchar(32) NOT NULL COMMENT '主键',
                                                `type` int(1) DEFAULT NULL COMMENT '消息推送类型，1-短信，2-邮件',
                                                `name` varchar(50) DEFAULT NULL COMMENT '模板的名称',
                                                `description` varchar(255) DEFAULT NULL COMMENT '模板的描述',
                                                `content` text COMMENT '模板内容（1 短信是阿里云预留id 2 邮件是自己编辑的模板 ）',
                                                `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                `create_time` datetime DEFAULT NULL,
                                                `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                                `modify_time` datetime DEFAULT NULL,
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_alarm_person_contact definition

CREATE TABLE `linkapp_alarm_person_contact` (
                                                `id` varchar(32) NOT NULL COMMENT '主键',
                                                `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                `name` varchar(50) DEFAULT NULL COMMENT '姓名',
                                                `phone` varchar(255) DEFAULT NULL COMMENT '手机号',
                                                `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `modify_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                `user_id` varchar(32) DEFAULT NULL COMMENT '用户id',
                                                `delete_state` int(2) NOT NULL DEFAULT '1' COMMENT '删除状态，1存在 0已删除',
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_alarm_process definition

CREATE TABLE `linkapp_alarm_process` (
                                         `id` varchar(32) NOT NULL COMMENT '主键',
                                         `alarm_id` varchar(32) NOT NULL COMMENT '告警信息id,关联linkapp_alarm',
                                         `device_code` varchar(32) NOT NULL COMMENT '设备编号',
                                         `handler_id` varchar(255) DEFAULT NULL COMMENT '处理人id',
                                         `status` int(2) DEFAULT NULL COMMENT '处理状态；1未处理，2已处理，其他值-其他',
                                         `mistake_flag` int(2) DEFAULT NULL COMMENT '误报标志，0 非误报，1误报',
                                         `process_result` varchar(512) DEFAULT NULL COMMENT '处理结果',
                                         `process_time` datetime DEFAULT NULL COMMENT '处理时间',
                                         `description` varchar(255) DEFAULT NULL COMMENT '描述',
                                         `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                         `create_time` datetime DEFAULT NULL,
                                         `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                         `modify_time` datetime DEFAULT NULL,
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_alarm_rule_20200907 definition

CREATE TABLE `linkapp_alarm_rule_20200907` (
                                               `id` varchar(32) NOT NULL COMMENT '主键',
                                               `space_id` varchar(32) DEFAULT NULL COMMENT '空间id',
                                               `intelligent_rule_id` varchar(32) DEFAULT NULL COMMENT '规则id',
                                               `area_id` varchar(32) DEFAULT NULL COMMENT '区域id',
                                               `delete_state` int(2) NOT NULL DEFAULT '1' COMMENT '逻辑删除，1存在，0已删除',
                                               `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                               `name` varchar(50) DEFAULT NULL COMMENT '规则名称',
                                               `alarm_content` varchar(255) DEFAULT NULL COMMENT '告警内容',
                                               `level` int(10) NOT NULL COMMENT '告警等级；1高，2中，3低',
                                               `status` int(10) NOT NULL COMMENT '规则状态，1未启用（默认）2启用',
                                               `action_scope` int(10) DEFAULT NULL COMMENT '规则作用范围；1全局，2作用于设备',
                                               `message_type` int(11) DEFAULT NULL COMMENT '消息推送类型，1-短信，2-邮件 ,3-短信+邮件',
                                               `message_switch` tinyint(1) NOT NULL DEFAULT '0' COMMENT '消息推送开关,0-关，1-开',
                                               `create_time` datetime DEFAULT NULL,
                                               `creator` varchar(32) DEFAULT NULL,
                                               `modifier` varchar(32) DEFAULT NULL,
                                               `modify_time` datetime DEFAULT NULL,
                                               `message_templet_id` varchar(32) DEFAULT NULL,
                                               `email_templet_id` varchar(32) DEFAULT NULL,
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_alarm_template definition

CREATE TABLE `linkapp_alarm_template` (
                                          `id` varchar(32) NOT NULL COMMENT '主键',
                                          `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                          `name` varchar(50) DEFAULT NULL COMMENT '规则名称',
                                          `level` int(10) NOT NULL COMMENT '告警等级；1高，2中，3低',
                                          `content` varchar(4000) DEFAULT NULL COMMENT '告警内容',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `modify_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          `user_id` varchar(32) DEFAULT NULL COMMENT '用户id',
                                          `delete_state` int(2) NOT NULL DEFAULT '1' COMMENT '删除状态，1存在 0已删除',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_analyze_datasource definition

CREATE TABLE `linkapp_analyze_datasource` (
                                              `id` varchar(32) NOT NULL COMMENT '数据源id',
                                              `datasource_name` varchar(32) NOT NULL COMMENT '数据源名称',
                                              `datasource_desc` varchar(255) NOT NULL COMMENT '资产的描述',
                                              `datasource_type` int(1) NOT NULL COMMENT '数据源的类型 0:已删; 1:存在',
                                              `area_id` varchar(32) DEFAULT NULL COMMENT '区域id',
                                              `device_id_string` text,
                                              `creator` varchar(32) DEFAULT NULL,
                                              `create_time` datetime DEFAULT NULL COMMENT '资产的创建时间',
                                              `modifier` varchar(32) DEFAULT NULL,
                                              `modify_time` datetime DEFAULT NULL COMMENT '资产的修改时间',
                                              `delete_state` int(1) DEFAULT '1' COMMENT '是否删除字段 0:已删; 1:存在',
                                              `tenant_id` varchar(32) DEFAULT '' COMMENT '租户ID',
                                              `status` int(1) DEFAULT NULL COMMENT '是否删除字段 0:失效; 1:有效',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备资产表，关联两张表\r\n关联1  资产表-资产设备表\r\n关联2  资产表-资产用户表\r\n';


-- linkappdb.linkapp_application definition

CREATE TABLE `linkapp_application` (
                                       `id` varchar(32) NOT NULL COMMENT '主键id',
                                       `name` varchar(32) DEFAULT NULL COMMENT '应用名称',
                                       `description` varchar(200) DEFAULT NULL COMMENT '描述',
                                       `app_key` varchar(32) DEFAULT NULL COMMENT 'appKey',
                                       `app_secret` varchar(255) DEFAULT NULL COMMENT 'app_secret',
                                       `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                       `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `delete_state` int(1) DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在',
                                       `personality_id` varchar(32) DEFAULT NULL,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用管理表';


-- linkappdb.linkapp_application_ref_device_unit definition

CREATE TABLE `linkapp_application_ref_device_unit` (
                                                       `id` varchar(32) NOT NULL,
                                                       `application_id` varchar(32) DEFAULT NULL COMMENT '应用模板ID',
                                                       `device_unit_id` varchar(32) DEFAULT NULL COMMENT '设备型号ID',
                                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_application_ref_privilege definition

CREATE TABLE `linkapp_application_ref_privilege` (
                                                     `id` varchar(32) NOT NULL,
                                                     `application_id` varchar(32) DEFAULT NULL COMMENT '应用模板ID',
                                                     `privilege_id` varchar(32) DEFAULT NULL COMMENT '权限ID',
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_application_rule definition

CREATE TABLE `linkapp_application_rule` (
                                            `id` varchar(32) NOT NULL,
                                            `module` decimal(2,0) DEFAULT NULL,
                                            `name` varchar(50) DEFAULT NULL,
                                            `code` varchar(50) DEFAULT NULL,
                                            `status` decimal(2,0) DEFAULT NULL,
                                            `remark` varchar(255) DEFAULT NULL,
                                            `rule` varchar(255) DEFAULT NULL,
                                            `value` varchar(255) DEFAULT NULL,
                                            `create_time` datetime DEFAULT NULL,
                                            `creator` varchar(32) DEFAULT NULL,
                                            `modifier` varchar(32) DEFAULT NULL,
                                            `modify_time` datetime DEFAULT NULL,
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_application_template definition

CREATE TABLE `linkapp_application_template` (
                                                `id` varchar(32) NOT NULL,
                                                `name` varchar(50) DEFAULT NULL,
                                                `code` varchar(50) DEFAULT NULL,
                                                `status` decimal(2,0) DEFAULT NULL,
                                                `remark` varchar(255) DEFAULT NULL,
                                                `create_time` datetime DEFAULT NULL,
                                                `creator` varchar(32) DEFAULT NULL,
                                                `modifier` varchar(32) DEFAULT NULL,
                                                `modify_time` datetime DEFAULT NULL,
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_area definition

CREATE TABLE `linkapp_area` (
                                `id` varchar(32) NOT NULL,
                                `parent_id` varchar(32) DEFAULT NULL,
                                `space_id` varchar(32) DEFAULT NULL,
                                `area_name` varchar(50) DEFAULT NULL,
                                `area_no` varchar(50) DEFAULT NULL,
                                `sort_no` decimal(8,0) DEFAULT '0',
                                `type` decimal(2,0) DEFAULT NULL COMMENT '1.室内 2.室外',
                                `level` decimal(2,0) DEFAULT '1',
                                `status` decimal(2,0) DEFAULT '1' COMMENT '0.删除，1.有效',
                                `img` varchar(255) DEFAULT NULL,
                                `latitude` varchar(20) DEFAULT NULL,
                                `longitude` varchar(20) DEFAULT NULL,
                                `remark` varchar(255) DEFAULT NULL,
                                `create_time` datetime DEFAULT NULL,
                                `creator` varchar(32) DEFAULT NULL,
                                `modifier` varchar(32) DEFAULT NULL,
                                `modify_time` datetime DEFAULT NULL,
                                `site` varchar(512) DEFAULT NULL COMMENT '安装详情',
                                `area_path` varchar(512) DEFAULT NULL COMMENT '区域位置',
                                `tenant_id` varchar(32) DEFAULT NULL,
                                `map_polygon_path` varchar(512) DEFAULT NULL,
                                PRIMARY KEY (`id`),
                                KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_asset definition

CREATE TABLE `linkapp_asset` (
                                 `id` varchar(32) NOT NULL COMMENT '资产编号',
                                 `asset_no` varchar(32) DEFAULT '' COMMENT '资产编号',
                                 `asset_name` varchar(32) NOT NULL COMMENT '资产名称',
                                 `asset_area_id` varchar(32) DEFAULT NULL,
                                 `asset_desc` varchar(100) DEFAULT '' COMMENT '设备描述',
                                 `creator` varchar(32) NOT NULL COMMENT '资产创建者',
                                 `create_time` datetime NOT NULL COMMENT '资产的创建时间',
                                 `modifier` varchar(32) DEFAULT NULL,
                                 `modify_time` datetime DEFAULT NULL COMMENT '资产的修改时间',
                                 `delete_state` int(1) NOT NULL COMMENT '是否删除字段 0:已删; 1:存在',
                                 `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                 `latitude` varchar(32) DEFAULT NULL,
                                 `longitude` varchar(32) DEFAULT NULL,
                                 `indoor_location` varchar(128) DEFAULT NULL COMMENT '室内位置',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备资产表，关联两张表\r\n关联1  资产表-资产设备表\r\n关联2  资产表-资产用户表\r\n';


-- linkappdb.linkapp_asset_ref_device definition

CREATE TABLE `linkapp_asset_ref_device` (
                                            `asset_id` varchar(32) NOT NULL COMMENT '资产id',
                                            `device_id` varchar(32) NOT NULL COMMENT '设备id',
                                            PRIMARY KEY (`asset_id`,`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产和设备是多对多的关联关系\r\n资产设备关联表';


-- linkappdb.linkapp_asset_ref_user definition

CREATE TABLE `linkapp_asset_ref_user` (
                                          `asset_id` varchar(32) NOT NULL COMMENT '资产id ',
                                          `user_id` varchar(32) NOT NULL COMMENT '用户id',
                                          PRIMARY KEY (`asset_id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产和用户是多对多关系\r\n资产用户关联表';


-- linkappdb.linkapp_bank_alarm_rule definition

CREATE TABLE `linkapp_bank_alarm_rule` (
                                           `id` varchar(32) NOT NULL COMMENT '主键',
                                           `data_source_id` varchar(32) DEFAULT NULL COMMENT '关联的数据源id 一对一',
                                           `status` tinyint(1) DEFAULT NULL COMMENT '规则状态，1未启用（默认）2启用',
                                           `alarm_thr` float(5,0) DEFAULT NULL COMMENT '告警阀值',
                                           `is_notice` tinyint(1) DEFAULT NULL COMMENT '是否告警通知 0否 1是',
                                           `phone` varchar(15) DEFAULT NULL COMMENT '告警等级；1高，2中，3低',
                                           `energy_kind` varchar(10) DEFAULT NULL COMMENT '能源类型 water:水表设备 electric：电表设备： air：气表设备',
                                           `alarm_type` tinyint(1) DEFAULT NULL,
                                           `mail` varchar(50) DEFAULT NULL,
                                           `delete_state` tinyint(1) DEFAULT NULL COMMENT '修改人id',
                                           `cron_expression` varchar(100) DEFAULT NULL,
                                           `job_id` varchar(32) DEFAULT NULL COMMENT '定时jobId',
                                           PRIMARY KEY (`id`),
                                           KEY `ad` (`data_source_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行贷后管理，告警规则表';


-- linkappdb.linkapp_calculate_config definition

CREATE TABLE `linkapp_calculate_config` (
                                            `id` varchar(32) NOT NULL COMMENT '主键id',
                                            `name` varchar(50) DEFAULT NULL COMMENT '计算名称',
                                            `code` varchar(50) DEFAULT NULL COMMENT '计算编号',
                                            `space_id` varchar(32) DEFAULT NULL COMMENT '空间id',
                                            `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                            `type` varchar(20) DEFAULT NULL COMMENT '类别：电-electric,水-water，气-air,人流量-peopleVolume',
                                            `status` int(1) DEFAULT NULL COMMENT '启用状态，0未启用 1启用',
                                            `calculate_rule_id` varchar(32) DEFAULT NULL COMMENT '计算规则id',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                            `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                            `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `delete_state` int(1) DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计算配置';


-- linkappdb.linkapp_calculate_config_attribute definition

CREATE TABLE `linkapp_calculate_config_attribute` (
                                                      `id` varchar(32) NOT NULL COMMENT '主键id',
                                                      `calculate_config_id` varchar(32) DEFAULT NULL COMMENT '计算配置id',
                                                      `device_unit_id` varchar(32) DEFAULT NULL COMMENT '设备型号id',
                                                      `device_unit_code` varchar(50) DEFAULT NULL COMMENT '设备型号code',
                                                      `device_attribute_id` varchar(50) DEFAULT NULL COMMENT '属性id',
                                                      `attribute_identifier` varchar(50) DEFAULT NULL COMMENT '属性标志符',
                                                      `coefficient` varchar(50) DEFAULT NULL COMMENT '系数',
                                                      `analyze_data_source_id` varchar(32) DEFAULT NULL COMMENT '数据源id',
                                                      `space_id` varchar(32) DEFAULT NULL COMMENT '空间id',
                                                      `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                      `group_number` int(11) DEFAULT NULL COMMENT '组编号',
                                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计算配置属性';


-- linkappdb.linkapp_calculate_rule definition

CREATE TABLE `linkapp_calculate_rule` (
                                          `id` varchar(32) NOT NULL COMMENT '主键id',
                                          `name` varchar(50) DEFAULT NULL COMMENT '规则名称',
                                          `code` varchar(50) DEFAULT NULL COMMENT '规则编号',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                          `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                          `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          `delete_state` int(1) DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计算规则';


-- linkappdb.linkapp_calculate_rule_attribute definition

CREATE TABLE `linkapp_calculate_rule_attribute` (
                                                    `id` varchar(32) NOT NULL COMMENT '主键id',
                                                    `calculate_rule_id` varchar(32) DEFAULT NULL COMMENT '计算规则id',
                                                    `name` varchar(50) DEFAULT NULL COMMENT '属性名称',
                                                    `identifier` varchar(50) DEFAULT NULL COMMENT '属性标识符',
                                                    `unit` varchar(50) DEFAULT NULL COMMENT '数据类型 int double float long string enum',
                                                    `specs` varchar(500) DEFAULT NULL COMMENT '数据格式描述',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计算规则属性';


-- linkappdb.linkapp_car_manage definition

CREATE TABLE `linkapp_car_manage` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `name` varchar(50) DEFAULT NULL COMMENT '车辆名称',
                                      `code` varchar(50) DEFAULT NULL COMMENT '车牌号',
                                      `car_owners` varchar(32) DEFAULT NULL COMMENT '车主',
                                      `gps_id` varchar(32) DEFAULT NULL COMMENT 'GPS设备id',
                                      `capacity` decimal(11,3) DEFAULT NULL COMMENT '载重量(吨)',
                                      `purpose` varchar(32) DEFAULT NULL COMMENT '用途',
                                      `image` varchar(1000) DEFAULT NULL COMMENT '图片',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                      `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                      `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆列表';


-- linkappdb.linkapp_company definition

CREATE TABLE `linkapp_company` (
                                   `id` varchar(32) NOT NULL,
                                   `company_name` varchar(255) DEFAULT NULL,
                                   `company_code` varchar(255) DEFAULT NULL,
                                   `sort_no` decimal(2,0) DEFAULT NULL,
                                   `status` decimal(2,0) DEFAULT NULL,
                                   `remark` varchar(255) DEFAULT NULL,
                                   `domain_id` varchar(32) DEFAULT NULL,
                                   `create_time` datetime DEFAULT NULL,
                                   `creator` varchar(32) DEFAULT NULL,
                                   `modifier` varchar(32) DEFAULT NULL,
                                   `modify_time` datetime DEFAULT NULL,
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_dashboard definition

CREATE TABLE `linkapp_dashboard` (
                                     `id` varchar(32) NOT NULL COMMENT '主键id',
                                     `name` varchar(32) DEFAULT NULL COMMENT '大屏名称',
                                     `dashboard_url` varchar(200) DEFAULT NULL COMMENT '大屏链接',
                                     `description` varchar(200) DEFAULT NULL COMMENT '大屏描述',
                                     `sketch` varchar(200) DEFAULT NULL COMMENT '大屏缩略图',
                                     `milestone_setting` text COMMENT '里程碑设置',
                                     `task_setting` text COMMENT '任务设置',
                                     `background_url` varchar(255) DEFAULT NULL COMMENT '背景图',
                                     `warning_setting` text COMMENT '预警设置',
                                     `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                     `delete_state` int(1) DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在',
                                     `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                     `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `show_bim` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否展示bim;0-否，1-是',
                                     `bim_id` varchar(40) DEFAULT NULL COMMENT 'bimId,建筑信息模型id',
                                     PRIMARY KEY (`id`),
                                     KEY `dashboard_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='可视化大屏表';


-- linkappdb.linkapp_device definition

CREATE TABLE `linkapp_device` (
                                  `id` varchar(32) NOT NULL,
                                  `code` varchar(32) NOT NULL COMMENT '设备编码',
                                  `name` varchar(32) NOT NULL COMMENT '设备名称',
                                  `status` int(10) DEFAULT '0' COMMENT '设备状态0:正常; 1:告警',
                                  `delete_state` int(10) DEFAULT '1' COMMENT '是否删除字段 1:存在; 0:删除',
                                  `online_state` int(10) DEFAULT '0' COMMENT '0:离线; 1:正常',
                                  `battery` float DEFAULT NULL COMMENT '电量',
                                  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                  `alarm_switch` varchar(32) DEFAULT NULL COMMENT '报警开关',
                                  `company_id` varchar(32) DEFAULT NULL COMMENT '公司编号',
                                  `project_id` varchar(32) DEFAULT NULL COMMENT '项目编号',
                                  `device_unit_id` varchar(32) NOT NULL COMMENT '设备型号',
                                  `install_time` datetime DEFAULT NULL COMMENT '安装时间',
                                  `repair_time` datetime DEFAULT NULL COMMENT '检修时间（最后一次）',
                                  `next_repair_time` datetime DEFAULT NULL COMMENT '检修时间（下一次）',
                                  `last_push_time` datetime DEFAULT NULL COMMENT '报文最后推送时间',
                                  `create_time` datetime DEFAULT NULL,
                                  `creator` varchar(32) DEFAULT NULL,
                                  `modifier` varchar(32) DEFAULT NULL,
                                  `modify_time` datetime DEFAULT NULL,
                                  `area_id` varchar(32) DEFAULT NULL COMMENT '区域ID',
                                  `latitude` varchar(20) DEFAULT NULL COMMENT '纬度',
                                  `longitude` varchar(20) DEFAULT NULL COMMENT '经度',
                                  `site` varchar(255) DEFAULT NULL COMMENT '安装位置',
                                  `indoor_location` varchar(128) DEFAULT NULL COMMENT '室内位置',
                                  `area_path` varchar(255) DEFAULT NULL COMMENT '区域层级路径',
                                  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                  `linkthing_delete` int(8) DEFAULT '1' COMMENT '在linkthing是否删除1-存在',
                                  `work_status` int(2) DEFAULT NULL COMMENT '工作状态：1初始化，2工作，3维保',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_device_code` (`code`),
                                  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_device_attribute definition

CREATE TABLE `linkapp_device_attribute` (
                                            `id` varchar(32) NOT NULL,
                                            `device_unit_id` varchar(32) DEFAULT NULL,
                                            `identifier` varchar(255) DEFAULT NULL COMMENT '属性标识符',
                                            `name` varchar(50) DEFAULT NULL COMMENT '属性名称',
                                            `value` varchar(50) DEFAULT NULL COMMENT '属性值',
                                            `unit` varchar(50) DEFAULT NULL COMMENT '单位',
                                            `ico_path` varchar(255) DEFAULT NULL COMMENT '属性显示图标',
                                            `type` int(8) DEFAULT NULL COMMENT '属性类型',
                                            `specs` varchar(3000) DEFAULT NULL COMMENT '规范',
                                            `sort_no` int(8) DEFAULT NULL COMMENT '排序',
                                            `description` varchar(255) DEFAULT NULL COMMENT '描述',
                                            `is_show` bit(1) DEFAULT b'0' COMMENT '是否默认显示,默认不显示',
                                            `expression` varchar(1000) DEFAULT NULL COMMENT '属性表达式',
                                            `dict_code` varchar(32) DEFAULT NULL COMMENT '数据字典code',
                                            `create_time` datetime DEFAULT NULL,
                                            `creator` varchar(32) DEFAULT NULL,
                                            `modifier` varchar(32) DEFAULT NULL,
                                            `modify_time` datetime DEFAULT NULL,
                                            `company_id` varchar(32) DEFAULT NULL,
                                            `version` varchar(50) DEFAULT NULL COMMENT '版本号',
                                            `parent_id` varchar(32) DEFAULT NULL COMMENT '父参数',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_device_unit_id_identifier` (`device_unit_id`,`identifier`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_device_attribute_status definition

CREATE TABLE `linkapp_device_attribute_status` (
                                                   `id` varchar(32) NOT NULL,
                                                   `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
                                                   `device_name` varchar(50) DEFAULT NULL COMMENT '设备名称',
                                                   `prop_name` varchar(50) DEFAULT NULL COMMENT '属性名称',
                                                   `array_index` int(11) DEFAULT NULL COMMENT '数组下标',
                                                   `parent_prop_code` varchar(50) DEFAULT NULL COMMENT '父级属性编码',
                                                   `prop_code` varchar(50) DEFAULT NULL COMMENT '属性编码',
                                                   `prop_unit` varchar(50) DEFAULT NULL COMMENT '属性单位',
                                                   `prop_value` text COMMENT '属性值',
                                                   `create_time` datetime DEFAULT NULL,
                                                   `creator` varchar(32) DEFAULT NULL,
                                                   `modifier` varchar(32) DEFAULT NULL,
                                                   `modify_time` datetime DEFAULT NULL,
                                                   `version` varchar(50) DEFAULT NULL COMMENT '版本号',
                                                   `parent_id` varchar(32) DEFAULT NULL COMMENT '父参数',
                                                   `timestamp` bigint(13) DEFAULT NULL,
                                                   `unique_tag` varchar(128) DEFAULT NULL COMMENT '唯一标识',
                                                   PRIMARY KEY (`id`),
                                                   UNIQUE KEY `index_unique_tag` (`unique_tag`),
                                                   KEY `idx_devicecode_propcode_version` (`device_code`,`prop_code`,`version`),
                                                   KEY `idx_devicecode_propcode_parentid_version_arrayindex` (`device_code`,`prop_code`,`parent_id`,`version`,`array_index`),
                                                   KEY `idx_devicecode_modifytime_createtime` (`device_code`,`modify_time`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_device_attribute_tenant_config definition

CREATE TABLE `linkapp_device_attribute_tenant_config` (
                                                          `id` varchar(32) NOT NULL COMMENT '主键',
                                                          `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                          `device_attribute_id` varchar(32) DEFAULT NULL COMMENT '设备属性id',
                                                          `sort_no` int(8) DEFAULT NULL COMMENT '排序',
                                                          `is_show` bit(1) DEFAULT b'0' COMMENT '是否默认显示,默认不显示，0-不显示，1-显示',
                                                          PRIMARY KEY (`id`),
                                                          UNIQUE KEY `tenant_id_unique_device_attribute_id` (`tenant_id`,`device_attribute_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备属性租户个性化配置';


-- linkappdb.linkapp_device_flow definition

CREATE TABLE `linkapp_device_flow` (
                                       `id` varchar(32) NOT NULL,
                                       `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
                                       `device_name` varchar(50) DEFAULT NULL COMMENT '设备名称',
                                       `prop_name` varchar(50) DEFAULT NULL COMMENT '属性名称',
                                       `prop_code` varchar(32) DEFAULT NULL COMMENT '属性编码',
                                       `prop_unit` varchar(50) DEFAULT NULL COMMENT '属性单位',
                                       `prop_value` varchar(50) DEFAULT NULL COMMENT '属性值',
                                       `create_time` datetime DEFAULT NULL,
                                       `creator` varchar(32) DEFAULT NULL,
                                       `modifier` varchar(32) DEFAULT NULL,
                                       `modify_time` datetime DEFAULT NULL,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_device_info definition

CREATE TABLE `linkapp_device_info` (
                                       `id` varchar(32) NOT NULL,
                                       `createTime` varchar(50) DEFAULT NULL,
                                       `oneDayAddCount` int(11) DEFAULT NULL,
                                       `tenantId` varchar(32) DEFAULT NULL,
                                       `onlineState` int(11) DEFAULT NULL,
                                       PRIMARY KEY (`id`),
                                       KEY `index_tenant` (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_device_model definition

CREATE TABLE `linkapp_device_model` (
                                        `auto_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `code_` varchar(32) NOT NULL COMMENT '设备号',
                                        `model_id_` tinyint(4) NOT NULL COMMENT '模块id',
                                        `remark_` varchar(100) DEFAULT NULL COMMENT '预留字段备注',
                                        `tenant_id` varchar(32) NOT NULL,
                                        `is_remove_` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否从设备模块已移除 0未移除 1已移除',
                                        `environmental_area_id` int(11) DEFAULT NULL COMMENT '环境管理区域id',
                                        PRIMARY KEY (`auto_id`),
                                        UNIQUE KEY `linkapp_device_model_un` (`code_`,`model_id_`,`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=87 DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_device_parm definition

CREATE TABLE `linkapp_device_parm` (
                                       `id` varchar(32) NOT NULL,
                                       `device_service_id` varchar(32) DEFAULT NULL,
                                       `identifier` varchar(255) DEFAULT NULL COMMENT '参数标识符',
                                       `name` varchar(50) DEFAULT NULL COMMENT '参数名称',
                                       `value` varchar(50) DEFAULT NULL COMMENT '参数值',
                                       `unit` varchar(50) DEFAULT NULL COMMENT '单位',
                                       `specs` varchar(1000) DEFAULT NULL COMMENT '规范',
                                       `sort_no` int(8) DEFAULT NULL COMMENT '排序',
                                       `description` varchar(255) DEFAULT NULL COMMENT '描述',
                                       `create_time` datetime DEFAULT NULL,
                                       `creator` varchar(32) DEFAULT NULL,
                                       `modifier` varchar(32) DEFAULT NULL,
                                       `modify_time` datetime DEFAULT NULL,
                                       `version` varchar(50) DEFAULT NULL COMMENT '版本号',
                                       `parent_id` varchar(32) DEFAULT NULL COMMENT '父参数',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_device_parm_dictionary definition

CREATE TABLE `linkapp_device_parm_dictionary` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                  `command_name` varchar(225) DEFAULT NULL COMMENT '命令名称',
                                                  `command_value` varchar(225) DEFAULT NULL COMMENT '命令值',
                                                  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='设备服务参数字典';


-- linkappdb.linkapp_device_service definition

CREATE TABLE `linkapp_device_service` (
                                          `id` varchar(32) NOT NULL,
                                          `device_unit_id` varchar(32) DEFAULT NULL COMMENT '设备型号',
                                          `identifier` varchar(512) DEFAULT NULL COMMENT '标识',
                                          `name` varchar(255) DEFAULT NULL COMMENT '名称',
                                          `ack_time_out` int(10) DEFAULT NULL COMMENT '确认信号超时时间，秒',
                                          `ack_type` int(2) DEFAULT NULL COMMENT '确认信号类型，0-无应答 1-异步应答 2-同步应答',
                                          `type` int(2) DEFAULT '1' COMMENT '类型',
                                          `sort_no` int(8) DEFAULT NULL COMMENT '排序',
                                          `creator` varchar(32) DEFAULT NULL,
                                          `create_time` datetime DEFAULT NULL,
                                          `modifier` varchar(32) DEFAULT NULL,
                                          `modify_time` datetime DEFAULT NULL,
                                          `version` varchar(50) DEFAULT NULL COMMENT '版本号',
                                          `is_show` bit(1) DEFAULT b'0' COMMENT '是否默认显示,默认不显示',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_device_service_tenant_config definition

CREATE TABLE `linkapp_device_service_tenant_config` (
                                                        `id` varchar(32) NOT NULL,
                                                        `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                        `device_service_id` varchar(32) DEFAULT NULL COMMENT '设备服务id',
                                                        `sort_no` int(8) DEFAULT NULL COMMENT '排序',
                                                        `is_show` bit(1) DEFAULT b'0' COMMENT '是否默认显示,默认不显示，0-不显示，1-显示',
                                                        PRIMARY KEY (`id`),
                                                        UNIQUE KEY `tenant_id_unique_device_service_id` (`tenant_id`,`device_service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备服务租户个性化配置';


-- linkappdb.linkapp_device_type definition

CREATE TABLE `linkapp_device_type` (
                                       `id` varchar(32) NOT NULL COMMENT '主键',
                                       `parent_id` varchar(32) DEFAULT NULL COMMENT '父类型id',
                                       `name` varchar(50) DEFAULT NULL COMMENT '类型名称',
                                       `ico_path` varchar(255) DEFAULT NULL COMMENT '属性显示图标',
                                       `description` varchar(255) DEFAULT NULL,
                                       `level` decimal(2,0) DEFAULT NULL,
                                       `search_code` varchar(255) DEFAULT NULL,
                                       `remark` varchar(255) DEFAULT NULL,
                                       `company_id` varchar(32) DEFAULT NULL,
                                       `create_time` datetime DEFAULT NULL,
                                       `creator` varchar(32) DEFAULT NULL,
                                       `modifier` varchar(32) DEFAULT NULL,
                                       `modify_time` datetime DEFAULT NULL,
                                       `delete_state` int(10) DEFAULT '1' COMMENT '是否删除字段 1:存在; 0:删除',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_device_unit definition

CREATE TABLE `linkapp_device_unit` (
                                       `id` varchar(32) NOT NULL,
                                       `device_type_id` varchar(32) DEFAULT NULL COMMENT '设备类型',
                                       `device_type_name` varchar(50) DEFAULT NULL COMMENT '设备类型名称',
                                       `company_id` varchar(32) DEFAULT NULL,
                                       `name` varchar(50) DEFAULT NULL COMMENT '名称',
                                       `code` varchar(50) DEFAULT NULL COMMENT '编码',
                                       `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                       `icon` varchar(255) DEFAULT NULL COMMENT '图标',
                                       `identification` varchar(255) DEFAULT NULL,
                                       `device_life` float DEFAULT NULL COMMENT '设备寿命（月）',
                                       `repair_cycle` float DEFAULT NULL COMMENT '检修周期（天）',
                                       `offline_time` float DEFAULT NULL COMMENT '设备离线时间（小时）',
                                       `create_time` datetime DEFAULT NULL,
                                       `creator` varchar(32) DEFAULT NULL,
                                       `modifier` varchar(32) DEFAULT NULL,
                                       `modify_time` datetime DEFAULT NULL,
                                       `delete_state` int(10) DEFAULT '1' COMMENT '是否删除字段 1:存在; 0:删除',
                                       `visualization_config` varchar(1000) DEFAULT NULL COMMENT '可视化配置',
                                       `version` varchar(50) DEFAULT NULL COMMENT '版本号',
                                       `physics_model` longtext COMMENT '物模型',
                                       `linkapp_flag` int(11) DEFAULT NULL,
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `index_unique_code_version` (`code`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_elstic_devenergyreal definition

CREATE TABLE `linkapp_elstic_devenergyreal` (
                                                `id` bigint(50) NOT NULL AUTO_INCREMENT COMMENT '记录序号',
                                                `batchId` varchar(32) NOT NULL COMMENT '查询的批次id',
                                                `dayEnergy` double(20,2) NOT NULL,
                                                `deviceCode` varchar(32) NOT NULL COMMENT '设备编码',
                                                `deviceName` varchar(32) DEFAULT NULL COMMENT '设备名称',
                                                `deviceUnitName` varchar(32) DEFAULT '' COMMENT '设备的编号',
                                                `uniqueMark` varchar(32) DEFAULT NULL COMMENT '设备型号ID',
                                                `createTime` datetime NOT NULL COMMENT '用电数据某天最新的电表数据yyyy-MM-dd',
                                                `sumEnergy` double(20,2) NOT NULL,
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备资产表，关联两张表\r\n关联1  资产表-资产设备表\r\n关联2  资产表-资产用户表\r\n';


-- linkappdb.linkapp_face_camera definition

CREATE TABLE `linkapp_face_camera` (
                                       `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                       `device_code` varchar(64) NOT NULL DEFAULT '' COMMENT '设备ID',
                                       `position` varchar(128) NOT NULL DEFAULT '' COMMENT '摄像头安装位置',
                                       `tenant_id` varchar(32) DEFAULT NULL COMMENT '所属租户ID',
                                       `remarks` varchar(300) DEFAULT NULL COMMENT '备注',
                                       `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- linkappdb.linkapp_face_photo definition

CREATE TABLE `linkapp_face_photo` (
                                      `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                      `photo_id` varchar(32) NOT NULL DEFAULT '' COMMENT '第三方平台返回的照片ID',
                                      `photo_url` varchar(1024) DEFAULT NULL COMMENT '照片URL',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- linkappdb.linkapp_face_recognition_record definition

CREATE TABLE `linkapp_face_recognition_record` (
                                                   `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                                   `capture_photo` varchar(1024) NOT NULL DEFAULT '' COMMENT '抓拍照片的OSS存储路径',
                                                   `recognition_type` int(11) NOT NULL COMMENT '识别类型。0为人脸识别，1为越界抓拍',
                                                   `recognition_photo` varchar(1024) DEFAULT NULL COMMENT '识别照片',
                                                   `recognition_personnel` int(11) NOT NULL COMMENT '识别的人员ID。未识别为-1',
                                                   `recognition_name` varchar(64) DEFAULT '' COMMENT '使别人姓名',
                                                   `identifier_name` varchar(64) DEFAULT NULL COMMENT '识别人员的身份名',
                                                   `device_code` varchar(32) NOT NULL DEFAULT '' COMMENT '抓拍设备的编号',
                                                   `tenant_id` varchar(32) NOT NULL DEFAULT '' COMMENT '所属租户ID',
                                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '抓拍时间',
                                                   `device_id` varchar(32) NOT NULL DEFAULT '' COMMENT '抓拍设备的编号',
                                                   `color` varchar(32) NOT NULL DEFAULT '' COMMENT '前端显示颜色',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- linkappdb.linkapp_function definition

CREATE TABLE `linkapp_function` (
                                    `id` varchar(32) NOT NULL,
                                    `identifier` varchar(50) DEFAULT NULL COMMENT '功能标识符',
                                    `function_name` varchar(255) DEFAULT NULL COMMENT '功能名称',
                                    `description` varchar(1000) DEFAULT NULL COMMENT '功能描述',
                                    `is_must` bit(1) DEFAULT b'1' COMMENT '是否必须',
                                    `create_time` datetime DEFAULT NULL,
                                    `creator` varchar(32) DEFAULT NULL,
                                    `modifier` varchar(32) DEFAULT NULL,
                                    `modify_time` datetime DEFAULT NULL,
                                    `type` int(11) DEFAULT NULL,
                                    `sort_no` int(8) DEFAULT NULL,
                                    `img_path` varchar(512) DEFAULT NULL,
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `identifier` (`identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_function_ref_device_type definition

CREATE TABLE `linkapp_function_ref_device_type` (
                                                    `id_` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                    `function_id` varchar(32) DEFAULT NULL COMMENT '功能id',
                                                    `ref_device_type` text COMMENT '关联产品类型',
                                                    PRIMARY KEY (`id_`),
                                                    UNIQUE KEY `uniq_function_id` (`function_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='功能关联产品类型表';


-- linkappdb.linkapp_function_ref_privilege definition

CREATE TABLE `linkapp_function_ref_privilege` (
                                                  `id` varchar(32) NOT NULL,
                                                  `function_id` varchar(32) DEFAULT NULL,
                                                  `privilege_id` varchar(32) DEFAULT NULL,
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_industry definition

CREATE TABLE `linkapp_industry` (
                                    `id` varchar(32) NOT NULL,
                                    `name` varchar(255) DEFAULT NULL,
                                    `code` varchar(255) DEFAULT NULL,
                                    `sort_no` decimal(2,0) DEFAULT NULL,
                                    `status` decimal(2,0) DEFAULT NULL,
                                    `remark` varchar(255) DEFAULT NULL,
                                    `domain_id` varchar(32) DEFAULT NULL,
                                    `create_time` datetime DEFAULT NULL,
                                    `creator` varchar(32) DEFAULT NULL,
                                    `modifier` varchar(32) DEFAULT NULL,
                                    `modify_time` datetime DEFAULT NULL,
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_intelligent_rule_expression definition

CREATE TABLE `linkapp_intelligent_rule_expression` (
                                                       `id` varchar(32) NOT NULL,
                                                       `rule_trigger_id` varchar(32) DEFAULT NULL COMMENT '触发器id',
                                                       `delete_state` int(2) NOT NULL DEFAULT '1' COMMENT '删除状态，1存在 0已删除',
                                                       `bak_intelligent_rule_id` varchar(32) DEFAULT NULL COMMENT '规则id',
                                                       `device_attribute_id` varchar(32) DEFAULT NULL COMMENT '设备属性id',
                                                       `device_attribute_parent_id` varchar(32) DEFAULT NULL COMMENT '父属性id',
                                                       `calculate_sign` varchar(10) DEFAULT NULL COMMENT '算术符',
                                                       `expression` varchar(1024) DEFAULT NULL COMMENT '表达式',
                                                       `value` varchar(50) DEFAULT NULL COMMENT '数值',
                                                       `sort_no` int(8) DEFAULT NULL COMMENT '排序',
                                                       `logic_code` varchar(50) DEFAULT NULL COMMENT '逻辑运算符（与、或）',
                                                       `rule_condition_id` varchar(32) DEFAULT NULL COMMENT '条件器id',
                                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能规则对应的表达式';


-- linkappdb.linkapp_job_entity definition

CREATE TABLE `linkapp_job_entity` (
                                      `id` varchar(32) NOT NULL COMMENT '主键',
                                      `job_name` varchar(200) NOT NULL COMMENT '任务名称',
                                      `job_group` varchar(200) NOT NULL COMMENT '任务分组',
                                      `job_status` char(1) DEFAULT NULL COMMENT '任务状态， 0禁用 1启用',
                                      `cron_expression` varchar(120) DEFAULT NULL COMMENT '任务cron表达式',
                                      `remark` varchar(200) DEFAULT NULL COMMENT '任务描述',
                                      `job_task_type` varchar(2) DEFAULT NULL COMMENT '任务调度类型，0：接口，1：存储过程',
                                      `api_url` varchar(100) DEFAULT NULL COMMENT '接口地址',
                                      `params` varchar(500) DEFAULT NULL COMMENT '参数',
                                      `job_type` char(1) DEFAULT NULL COMMENT '任务类型，0：周期性，1：一次性',
                                      `trigger_name` varchar(200) DEFAULT NULL COMMENT '触发器名字',
                                      `trigger_group` varchar(200) DEFAULT NULL COMMENT '触发器分组',
                                      `is_now_run` char(1) DEFAULT NULL COMMENT '是否立即运行，0：否，1：是',
                                      `start_date` datetime DEFAULT NULL COMMENT '生效日期',
                                      `end_date` datetime DEFAULT NULL COMMENT '失效日期',
                                      `job_class_name` varchar(200) DEFAULT NULL COMMENT '执行类名',
                                      `create_time` datetime DEFAULT NULL COMMENT '任务创建时间',
                                      `modify_time` datetime DEFAULT NULL COMMENT '任务更新时间',
                                      `delete_state` int(1) DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_knowledge_base definition

CREATE TABLE `linkapp_knowledge_base` (
                                          `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键(自增)',
                                          `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                          `name_` varchar(128) NOT NULL COMMENT '名称',
                                          `types_` varchar(100) DEFAULT NULL COMMENT '知识类别 多选',
                                          `content_` mediumtext COMMENT '内容',
                                          `attachments_` text COMMENT '附件列表',
                                          `attachments_zip_url` varchar(512) DEFAULT NULL COMMENT '附件压缩包地址',
                                          `permission_scope_` tinyint(2) DEFAULT '0' COMMENT '访问权限：0全体 1指定用户 2仅创建人',
                                          `permission_users_` text COMMENT '可见用户(用户ID，逗号分隔) 访问权限为1 ',
                                          `creator_id_` varchar(32) NOT NULL COMMENT '创建人id',
                                          `creator_` varchar(32) DEFAULT NULL,
                                          `create_time_` datetime DEFAULT NULL,
                                          `modifier_` varchar(32) DEFAULT NULL,
                                          `modify_time_` datetime DEFAULT NULL,
                                          PRIMARY KEY (`id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库表';


-- linkappdb.linkapp_knowledge_base_ref_type definition

CREATE TABLE `linkapp_knowledge_base_ref_type` (
                                                   `knowledge_id_` bigint(20) NOT NULL COMMENT '知识id',
                                                   `type_id_` bigint(20) NOT NULL COMMENT '类别id',
                                                   PRIMARY KEY (`type_id_`,`knowledge_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库关联类别表';


-- linkappdb.linkapp_knowledge_base_ref_user definition

CREATE TABLE `linkapp_knowledge_base_ref_user` (
                                                   `knowledge_id_` bigint(20) NOT NULL COMMENT '知识id',
                                                   `user_id_` varchar(32) NOT NULL COMMENT '用户id',
                                                   PRIMARY KEY (`user_id_`,`knowledge_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库关联用户表';


-- linkappdb.linkapp_knowledge_type definition

CREATE TABLE `linkapp_knowledge_type` (
                                          `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键(自增)',
                                          `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                          `parent_id_` varchar(32) DEFAULT NULL COMMENT '父节点',
                                          `name_` varchar(128) NOT NULL COMMENT '类型名称',
                                          `order_` int(11) DEFAULT '0' COMMENT '排序',
                                          `create_time_` datetime DEFAULT NULL,
                                          `creator_` varchar(32) DEFAULT NULL,
                                          `modifier_` varchar(32) DEFAULT NULL,
                                          `modify_time_` datetime DEFAULT NULL,
                                          PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识类别表';


-- linkappdb.linkapp_linkage_log definition

CREATE TABLE `linkapp_linkage_log` (
                                       `id` varchar(32) NOT NULL COMMENT '主键id',
                                       `rule_engine_id` varchar(32) DEFAULT NULL COMMENT '规则引擎id',
                                       `bak_linkage_config_id` varchar(32) DEFAULT NULL COMMENT '配置id',
                                       `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                       `space_id` varchar(32) DEFAULT NULL COMMENT '空间id',
                                       `type` int(1) DEFAULT NULL COMMENT '类别：1：联动规则触发类，2：定时任务触发类',
                                       `state` tinyint(1) DEFAULT '0' COMMENT '0-失败，1-成功',
                                       `device_code` varchar(32) DEFAULT NULL COMMENT '设备编号',
                                       `result_code` varchar(10) DEFAULT NULL COMMENT '结果码 200 成功 500：失败',
                                       `result_msg` varchar(500) DEFAULT NULL COMMENT '结果信息',
                                       `request_id` varchar(100) DEFAULT NULL COMMENT '请求id',
                                       `send_time` datetime DEFAULT NULL COMMENT '发送时间',
                                       `save_time` datetime DEFAULT NULL COMMENT '保存时间',
                                       PRIMARY KEY (`id`),
                                       KEY `index_rule_engine_id` (`rule_engine_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联动执行日志';


-- linkappdb.linkapp_menu definition

CREATE TABLE `linkapp_menu` (
                                `id_` bigint(20) NOT NULL,
                                `create_time_` datetime DEFAULT NULL,
                                `creator_` varchar(32) DEFAULT NULL,
                                `modifier_` varchar(32) DEFAULT NULL,
                                `modify_time_` datetime DEFAULT NULL,
                                `display_` bit(1) DEFAULT NULL,
                                `icon_` varchar(128) DEFAULT NULL,
                                `level_` int(11) DEFAULT NULL,
                                `name_` varchar(32) DEFAULT NULL,
                                `order_no_` int(11) DEFAULT NULL,
                                `parent_id_` bigint(20) DEFAULT NULL,
                                `path_` varchar(32) DEFAULT NULL,
                                `privilege_code_` bigint(20) DEFAULT NULL,
                                `remark_` varchar(128) DEFAULT NULL,
                                `search_code_` varchar(128) DEFAULT NULL,
                                `state_` bit(1) DEFAULT NULL,
                                `system_id_` bigint(20) DEFAULT NULL,
                                PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_obj_operate_log definition

CREATE TABLE `linkapp_obj_operate_log` (
                                           `log_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                           `object_id` varchar(32) DEFAULT NULL COMMENT '对象id',
                                           `object_key` varchar(50) DEFAULT NULL COMMENT '对象关键标识（如code,name）',
                                           `operate_desc` varchar(255) DEFAULT NULL COMMENT '操作描述',
                                           `content` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '日志内容',
                                           `fail_information` longtext CHARACTER SET utf8 COLLATE utf8_bin,
                                           `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           `module_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           `url` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           `status` bit(1) DEFAULT NULL,
                                           `create_time` datetime DEFAULT NULL,
                                           `creator` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
                                           `tenant_id` varchar(32) DEFAULT NULL,
                                           PRIMARY KEY (`log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_personality definition

CREATE TABLE `linkapp_personality` (
                                       `id` varchar(32) NOT NULL COMMENT '主键id',
                                       `name` varchar(32) DEFAULT NULL COMMENT '个性化名称',
                                       `platform` varchar(50) DEFAULT NULL,
                                       `domin` varchar(60) DEFAULT NULL COMMENT '默认域名',
                                       `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                       `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `delete_state` int(1) DEFAULT NULL COMMENT '是否删除字段 0:已删; 1:存在',
                                       `deep_color_logo` varchar(255) DEFAULT NULL,
                                       `light_color_logo` varchar(255) DEFAULT NULL,
                                       `login_back` varchar(255) DEFAULT NULL,
                                       PRIMARY KEY (`id`),
                                       KEY `aa` (`domin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个性化表';


-- linkappdb.linkapp_personnel definition

CREATE TABLE `linkapp_personnel` (
                                     `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `name` varchar(31) NOT NULL DEFAULT '' COMMENT '人员名',
                                     `identifier_id` int(11) NOT NULL COMMENT '人员身份ID',
                                     `subject_id` varchar(255) DEFAULT NULL,
                                     `tenant_id` varchar(32) NOT NULL DEFAULT '' COMMENT '租户ID',
                                     `photo_url` varchar(2048) NOT NULL DEFAULT '' COMMENT '图片的OSS存储地址，多张图片使用,分割',
                                     `username` varchar(32) DEFAULT NULL COMMENT '关联账户',
                                     `user_id` varchar(32) DEFAULT NULL COMMENT '关联账户',
                                     `gender` int(11) DEFAULT NULL COMMENT '性别。0为女，1为男',
                                     `phone` varchar(32) DEFAULT NULL COMMENT '手机号',
                                     `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
                                     `department` varchar(32) DEFAULT NULL COMMENT '部门',
                                     `title` varchar(32) DEFAULT NULL COMMENT '职位',
                                     `remarks` varchar(300) DEFAULT NULL COMMENT '备注',
                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- linkappdb.linkapp_personnel_identifier definition

CREATE TABLE `linkapp_personnel_identifier` (
                                                `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                `identifier` varchar(255) NOT NULL DEFAULT '' COMMENT '人员身份，租户内唯一',
                                                `tenant_id` varchar(32) NOT NULL DEFAULT '' COMMENT '所属租户ID',
                                                `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被删除。默认为false',
                                                `remarks` varchar(300) DEFAULT NULL COMMENT '备注',
                                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                `color` varchar(32) NOT NULL DEFAULT '' COMMENT '前端显示颜色',
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- linkappdb.linkapp_privilege definition

CREATE TABLE `linkapp_privilege` (
                                     `id_` varchar(32) NOT NULL,
                                     `parent_id_` varchar(32) DEFAULT NULL,
                                     `name_` varchar(32) DEFAULT NULL,
                                     `privilege_code_` varchar(255) DEFAULT NULL,
                                     `description_` varchar(255) DEFAULT NULL,
                                     `level_` int(11) DEFAULT NULL,
                                     `target` varchar(32) DEFAULT NULL,
                                     `search_code_` varchar(255) DEFAULT NULL,
                                     `seq_` decimal(8,2) DEFAULT NULL,
                                     `type_` int(11) DEFAULT NULL COMMENT '类型:1-菜单，2-按钮类',
                                     `url_` varchar(255) DEFAULT NULL,
                                     `is_log_` int(11) DEFAULT NULL,
                                     `tenant_id_` varchar(32) DEFAULT NULL,
                                     `create_time_` datetime DEFAULT NULL,
                                     `creator_` varchar(32) DEFAULT NULL,
                                     `modifier_` varchar(32) DEFAULT NULL,
                                     `modify_time_` datetime DEFAULT NULL,
                                     `icon_name` varchar(512) DEFAULT NULL,
                                     `flag_` tinyint(3) DEFAULT '0' COMMENT '0:PC， 1:小程序',
                                     PRIMARY KEY (`id_`),
                                     UNIQUE KEY `linkapp_privilege_un` (`seq_`),
                                     UNIQUE KEY `unq_seq_idx` (`seq_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_privilege_bak definition

CREATE TABLE `linkapp_privilege_bak` (
                                         `id_` varchar(32) NOT NULL,
                                         `parent_id_` varchar(32) DEFAULT NULL,
                                         `name_` varchar(32) DEFAULT NULL,
                                         `privilege_code_` varchar(255) DEFAULT NULL,
                                         `description_` varchar(255) DEFAULT NULL,
                                         `level_` int(11) DEFAULT NULL,
                                         `target` varchar(32) DEFAULT NULL,
                                         `search_code_` varchar(255) DEFAULT NULL,
                                         `seq_` decimal(8,2) DEFAULT NULL,
                                         `type_` int(11) DEFAULT NULL COMMENT '类型:1-菜单，2-按钮类',
                                         `url_` varchar(255) DEFAULT NULL,
                                         `is_log_` int(11) DEFAULT NULL,
                                         `tenant_id_` varchar(32) DEFAULT NULL,
                                         `create_time_` datetime DEFAULT NULL,
                                         `creator_` varchar(32) DEFAULT NULL,
                                         `modifier_` varchar(32) DEFAULT NULL,
                                         `modify_time_` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_privilege_prd_20211103111250_bak definition

CREATE TABLE `linkapp_privilege_prd_20211103111250_bak` (
                                                            `id_` varchar(32) NOT NULL,
                                                            `parent_id_` varchar(32) DEFAULT NULL,
                                                            `name_` varchar(32) DEFAULT NULL,
                                                            `privilege_code_` varchar(255) DEFAULT NULL,
                                                            `description_` varchar(255) DEFAULT NULL,
                                                            `level_` int(11) DEFAULT NULL,
                                                            `target` varchar(32) DEFAULT NULL,
                                                            `search_code_` varchar(255) DEFAULT NULL,
                                                            `seq_` decimal(8,2) DEFAULT NULL,
                                                            `type_` int(11) DEFAULT NULL COMMENT '类型:1-菜单，2-按钮类',
                                                            `url_` varchar(255) DEFAULT NULL,
                                                            `is_log_` int(11) DEFAULT NULL,
                                                            `tenant_id_` varchar(32) DEFAULT NULL,
                                                            `create_time_` datetime DEFAULT NULL,
                                                            `creator_` varchar(32) DEFAULT NULL,
                                                            `modifier_` varchar(32) DEFAULT NULL,
                                                            `modify_time_` datetime DEFAULT NULL,
                                                            `icon_name` varchar(512) DEFAULT NULL,
                                                            `flag_` tinyint(3) DEFAULT '0' COMMENT '0:PC， 1:小程序',
                                                            PRIMARY KEY (`id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;


-- linkappdb.linkapp_privilege_test_20211103111527_bak definition

CREATE TABLE `linkapp_privilege_test_20211103111527_bak` (
                                                             `id_` varchar(32) NOT NULL,
                                                             `parent_id_` varchar(32) DEFAULT NULL,
                                                             `name_` varchar(32) DEFAULT NULL,
                                                             `privilege_code_` varchar(255) DEFAULT NULL,
                                                             `description_` varchar(255) DEFAULT NULL,
                                                             `level_` int(11) DEFAULT NULL,
                                                             `target` varchar(32) DEFAULT NULL,
                                                             `search_code_` varchar(255) DEFAULT NULL,
                                                             `seq_` decimal(8,2) DEFAULT NULL,
                                                             `type_` int(11) DEFAULT NULL COMMENT '类型:1-菜单，2-按钮类',
                                                             `url_` varchar(255) DEFAULT NULL,
                                                             `is_log_` int(11) DEFAULT NULL,
                                                             `tenant_id_` varchar(32) DEFAULT NULL,
                                                             `create_time_` datetime DEFAULT NULL,
                                                             `creator_` varchar(32) DEFAULT NULL,
                                                             `modifier_` varchar(32) DEFAULT NULL,
                                                             `modify_time_` datetime DEFAULT NULL,
                                                             `icon_name` varchar(512) DEFAULT NULL,
                                                             `flag_` tinyint(3) DEFAULT '0' COMMENT '0:PC， 1:小程序',
                                                             PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_project definition

CREATE TABLE `linkapp_project` (
                                   `id` varchar(32) NOT NULL,
                                   `project_name` varchar(50) DEFAULT NULL COMMENT '项目名称',
                                   `project_code` varchar(50) DEFAULT NULL COMMENT '项目编号',
                                   `status` int(10) DEFAULT NULL COMMENT '状态',
                                   `remark` varchar(255) DEFAULT NULL,
                                   `create_time` datetime DEFAULT NULL,
                                   `creator` varchar(32) DEFAULT NULL,
                                   `modifier` varchar(32) DEFAULT NULL,
                                   `modify_time` datetime DEFAULT NULL,
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `index_project_name` (`project_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_propertyassets definition

CREATE TABLE `linkapp_propertyassets` (
                                          `id` varchar(32) NOT NULL,
                                          `name` varchar(50) DEFAULT NULL,
                                          `code` varchar(50) DEFAULT NULL,
                                          `status` decimal(2,0) DEFAULT NULL,
                                          `remark` varchar(255) DEFAULT NULL,
                                          `create_time` datetime DEFAULT NULL,
                                          `creator` varchar(32) DEFAULT NULL,
                                          `modifier` varchar(32) DEFAULT NULL,
                                          `modify_time` datetime DEFAULT NULL,
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_real_time_weather definition

CREATE TABLE `linkapp_real_time_weather` (
                                             `id` varchar(32) NOT NULL COMMENT '主键',
                                             `city` varchar(255) DEFAULT NULL COMMENT '城市名称',
                                             `city_code` varchar(32) DEFAULT NULL COMMENT '城市码',
                                             `shidu` varchar(8) DEFAULT NULL COMMENT '湿度',
                                             `fengli` varchar(8) DEFAULT NULL COMMENT '风力',
                                             `fengxiang` varchar(16) DEFAULT NULL COMMENT '风向',
                                             `wendu` varchar(4) DEFAULT NULL COMMENT '温度',
                                             `high` varchar(32) DEFAULT NULL COMMENT '高温',
                                             `low` varchar(32) DEFAULT NULL COMMENT '低温',
                                             `night_type` varchar(32) DEFAULT NULL COMMENT '夜晚天气状态',
                                             `day_type` varchar(32) DEFAULT NULL COMMENT '白天天气状态',
                                             `updatetime` varchar(8) DEFAULT NULL COMMENT '采集时间',
                                             `createtime` datetime DEFAULT NULL COMMENT '创建时间',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时天气';


-- linkappdb.linkapp_role definition

CREATE TABLE `linkapp_role` (
                                `role_id_` varchar(32) NOT NULL,
                                `name_` varchar(32) DEFAULT NULL,
                                `code_` varchar(50) DEFAULT NULL,
                                `description_` varchar(255) DEFAULT NULL,
                                `department_id_` bigint(20) DEFAULT NULL,
                                `display_` bit(1) DEFAULT b'1' COMMENT '1.显示 0.不显示',
                                `create_time` datetime DEFAULT NULL,
                                `creator` varchar(32) DEFAULT NULL,
                                `modifier` varchar(32) DEFAULT NULL,
                                `modify_time` datetime DEFAULT NULL,
                                `tenant_id` varchar(32) DEFAULT NULL,
                                PRIMARY KEY (`role_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_role_ref_privilege definition

CREATE TABLE `linkapp_role_ref_privilege` (
                                              `id` varchar(32) NOT NULL,
                                              `role_id_` varchar(32) DEFAULT NULL,
                                              `privilege_id_` varchar(32) DEFAULT NULL,
                                              `tenant_id` varchar(32) DEFAULT NULL,
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_role_space definition

CREATE TABLE `linkapp_role_space` (
                                      `id` varchar(32) NOT NULL,
                                      `space_id` varchar(32) DEFAULT NULL,
                                      `role_id` varchar(32) DEFAULT NULL,
                                      `tenant_id` varchar(32) DEFAULT NULL,
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_rule_component definition

CREATE TABLE `linkapp_rule_component` (
                                          `id` varchar(32) DEFAULT NULL,
                                          `name` varchar(255) DEFAULT NULL COMMENT '组件名称',
                                          `type` varchar(255) DEFAULT NULL COMMENT '组件类型',
                                          `description` varchar(512) DEFAULT NULL COMMENT '组件描述',
                                          `create_time` datetime DEFAULT NULL,
                                          `creator` varchar(32) DEFAULT NULL,
                                          `modifier` varchar(32) DEFAULT NULL,
                                          `modify_time` datetime DEFAULT NULL,
                                          `tenant_id` varchar(32) DEFAULT NULL,
                                          `cron` varchar(255) DEFAULT NULL COMMENT '时间表达式',
                                          `device_unit_id` varchar(32) DEFAULT NULL COMMENT '设备型号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_rule_component_attribute definition

CREATE TABLE `linkapp_rule_component_attribute` (
                                                    `id` varchar(32) NOT NULL,
                                                    `device_attribute_id` varchar(32) DEFAULT NULL COMMENT '设备属性id',
                                                    `device_attribute_parent_id` varchar(32) DEFAULT NULL COMMENT '父属性id',
                                                    `calculate_sign` varchar(10) DEFAULT NULL COMMENT '算术符',
                                                    `expression` varchar(1024) DEFAULT NULL COMMENT '表达式',
                                                    `value` varchar(255) DEFAULT NULL COMMENT '数值',
                                                    `sort_no` int(8) DEFAULT NULL COMMENT '排序',
                                                    `logic_code` varchar(32) DEFAULT NULL COMMENT '逻辑运算符（与、或）',
                                                    `create_time` datetime DEFAULT NULL,
                                                    `creator` varchar(32) DEFAULT NULL,
                                                    `modifier` varchar(32) DEFAULT NULL,
                                                    `modify_time` datetime DEFAULT NULL,
                                                    `tenant_id` varchar(32) DEFAULT NULL,
                                                    `rule_component_id` varchar(32) DEFAULT NULL,
                                                    `specs` varchar(1024) DEFAULT NULL,
                                                    `unit` varchar(50) DEFAULT NULL,
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_rule_component_service definition

CREATE TABLE `linkapp_rule_component_service` (
                                                  `id` varchar(32) NOT NULL,
                                                  `device_service_id` varchar(32) DEFAULT NULL,
                                                  `device_parm_id` varchar(32) DEFAULT NULL,
                                                  `device_parm_parent_id` varchar(32) DEFAULT NULL,
                                                  `value` varchar(255) DEFAULT NULL,
                                                  `sort_no` int(8) DEFAULT NULL,
                                                  `logic_code` varchar(32) DEFAULT NULL,
                                                  `create_time` datetime DEFAULT NULL,
                                                  `creator` varchar(32) DEFAULT NULL,
                                                  `modifier` varchar(32) DEFAULT NULL,
                                                  `modify_time` datetime DEFAULT NULL,
                                                  `tenant_id` varchar(32) DEFAULT NULL,
                                                  `rule_component_id` varchar(32) DEFAULT NULL,
                                                  `unit` varchar(32) DEFAULT NULL,
                                                  `specs` varchar(1024) DEFAULT NULL,
                                                  `name` varchar(50) DEFAULT NULL,
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_space definition

CREATE TABLE `linkapp_space` (
                                 `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                 `space_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `short_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `space_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `sort_no` decimal(8,0) DEFAULT '0',
                                 `status` decimal(2,0) DEFAULT '1' COMMENT '0.删除，1.有效',
                                 `longitude` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `latitude` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `province` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `city` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `city_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `district` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `site` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `parent_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `type` decimal(2,0) DEFAULT NULL,
                                 `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `create_time` datetime DEFAULT NULL,
                                 `creator` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `modifier` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `modify_time` datetime DEFAULT NULL,
                                 `tenant_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                 `map_polygon_path` varchar(512) DEFAULT NULL,
                                 `map_id` varchar(32) DEFAULT NULL,
                                 PRIMARY KEY (`id`),
                                 KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_tenant definition

CREATE TABLE `linkapp_tenant` (
                                  `id` varchar(32) NOT NULL,
                                  `tenant_id` varchar(50) DEFAULT NULL,
                                  `tenant_id_bak` varchar(50) DEFAULT NULL,
                                  `app_id` varchar(50) DEFAULT NULL,
                                  `app_key` varchar(50) DEFAULT NULL,
                                  `project_id` varchar(32) DEFAULT NULL,
                                  `app_type` varchar(50) DEFAULT NULL,
                                  `name` varchar(50) DEFAULT NULL,
                                  `code` varchar(50) DEFAULT NULL,
                                  `status` decimal(2,0) DEFAULT '1',
                                  `remark` varchar(255) DEFAULT NULL,
                                  `create_time` datetime DEFAULT NULL,
                                  `creator` varchar(32) DEFAULT NULL,
                                  `modifier` varchar(32) DEFAULT NULL,
                                  `modify_time` datetime DEFAULT NULL,
                                  `personality_id` varchar(32) DEFAULT NULL COMMENT '个性化配置',
                                  `platform_project_name` varchar(255) DEFAULT NULL,
                                  `platform_account` varchar(255) DEFAULT NULL,
                                  `platform_account_name` varchar(255) DEFAULT NULL,
                                  `app_industry_type` varchar(255) DEFAULT NULL,
                                  `telephone` varchar(50) DEFAULT NULL,
                                  `email` varchar(255) DEFAULT NULL,
                                  `estimate_time` datetime DEFAULT NULL COMMENT '预计开工时间',
                                  `completion_time` datetime DEFAULT NULL COMMENT '预计完工时间',
                                  `construction_unit` varchar(255) DEFAULT NULL COMMENT '建设单位',
                                  `bidding_unit` varchar(255) DEFAULT NULL COMMENT '招标单位',
                                  `location` varchar(255) DEFAULT NULL COMMENT '项目地点',
                                  `project_desc` text COMMENT '项目说明',
                                  `area` double DEFAULT NULL COMMENT '建筑面积',
                                  `project_type` tinyint(2) NOT NULL COMMENT '项目类型：0房屋建筑，1市政工程，2建筑智能化，3其他',
                                  `project_status` tinyint(2) NOT NULL COMMENT '项目状态：0中标项目，1在建项目，2施工准备，3停工缓建项目，4完工待结算，5完工已结算',
                                  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开工时间',
                                  `actual_completion_time` datetime DEFAULT NULL COMMENT '实际完工时间',
                                  `project_amount` double DEFAULT NULL COMMENT '项目金额',
                                  `shigong_unit` varchar(100) DEFAULT NULL COMMENT '施工单位',
                                  `design_unit` varchar(100) DEFAULT NULL COMMENT '设计单位',
                                  `construction_control_unit` varchar(100) DEFAULT NULL COMMENT '监理单位',
                                  `exploration_unit` varchar(100) DEFAULT NULL COMMENT '勘查单位',
                                  `project_area` varchar(100) DEFAULT NULL COMMENT '项目区域',
                                  `quality_excellence_level` tinyint(2) DEFAULT NULL COMMENT '质量创优等级，0国家级，1省级，2市级',
                                  `safety_excellence_level` tinyint(2) DEFAULT NULL COMMENT '安全创优等级，0国家级，1省级，2市级',
                                  `longitude` varchar(32) DEFAULT NULL COMMENT '经度',
                                  `latitude` varchar(32) DEFAULT NULL COMMENT '纬度',
                                  PRIMARY KEY (`id`),
                                  KEY `linkapp_tenant_platform_project_name_IDX` (`platform_project_name`(191)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_tenant_copy definition

CREATE TABLE `linkapp_tenant_copy` (
                                       `id` varchar(32) NOT NULL,
                                       `tenant_id` varchar(50) DEFAULT NULL,
                                       `tenant_id_bak` varchar(50) DEFAULT NULL,
                                       `app_id` varchar(50) DEFAULT NULL,
                                       `app_key` varchar(50) DEFAULT NULL,
                                       `project_id` varchar(32) DEFAULT NULL,
                                       `app_type` varchar(50) DEFAULT NULL,
                                       `name` varchar(50) DEFAULT NULL,
                                       `code` varchar(50) DEFAULT NULL,
                                       `status` decimal(2,0) DEFAULT '1',
                                       `remark` varchar(255) DEFAULT NULL,
                                       `create_time` datetime DEFAULT NULL,
                                       `creator` varchar(32) DEFAULT NULL,
                                       `modifier` varchar(32) DEFAULT NULL,
                                       `modify_time` datetime DEFAULT NULL,
                                       `personality_id` varchar(32) DEFAULT NULL COMMENT '个性化配置',
                                       `platform_project_name` varchar(255) DEFAULT NULL,
                                       `platform_account` varchar(255) DEFAULT NULL,
                                       `platform_account_name` varchar(255) DEFAULT NULL,
                                       `app_industry_type` varchar(255) DEFAULT NULL,
                                       `telephone` varchar(50) DEFAULT NULL,
                                       `email` varchar(255) DEFAULT NULL,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_tenant_ref_device_unit definition

CREATE TABLE `linkapp_tenant_ref_device_unit` (
                                                  `id` varchar(32) NOT NULL,
                                                  `device_unit_id` varchar(32) DEFAULT NULL,
                                                  `tenant_id` varchar(32) DEFAULT NULL,
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_tenant_ref_function definition

CREATE TABLE `linkapp_tenant_ref_function` (
                                               `id` varchar(32) DEFAULT NULL,
                                               `tenant_id` varchar(32) DEFAULT NULL,
                                               `function_id` varchar(32) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_tenant_ref_privilege definition

CREATE TABLE `linkapp_tenant_ref_privilege` (
                                                `id` varchar(32) NOT NULL,
                                                `tenant_id` varchar(32) DEFAULT NULL,
                                                `privilege_id` varchar(32) DEFAULT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `index_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_tenant_session_config definition

CREATE TABLE `linkapp_tenant_session_config` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                 `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                                 `session_timeout` int(11) NOT NULL COMMENT '会话保存时长(秒)',
                                                 `description` varchar(225) DEFAULT NULL COMMENT '说明',
                                                 `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `delete_status` tinyint(3) DEFAULT '0' COMMENT '0:正常，1:删除',
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户会话时长配置表';


-- linkappdb.linkapp_user definition

CREATE TABLE `linkapp_user` (
                                `id` varchar(32) NOT NULL,
                                `create_time` datetime DEFAULT NULL,
                                `creator` varchar(32) DEFAULT NULL,
                                `modifier` varchar(32) DEFAULT NULL,
                                `modify_time` datetime DEFAULT NULL,
                                `address` varchar(255) DEFAULT NULL,
                                `birthday` date DEFAULT NULL,
                                `code` varchar(32) DEFAULT NULL,
                                `description` varchar(255) DEFAULT NULL,
                                `email` varchar(255) DEFAULT NULL,
                                `icon` longtext,
                                `icon_sm` varchar(255) DEFAULT NULL,
                                `id_number` varchar(18) DEFAULT NULL,
                                `nickname` varchar(32) DEFAULT NULL,
                                `password` varchar(128) NOT NULL,
                                `phone` varchar(16) DEFAULT NULL,
                                `sex` varchar(255) DEFAULT NULL,
                                `username` varchar(50) DEFAULT NULL,
                                `display` bit(1) DEFAULT NULL,
                                `locked` bit(1) DEFAULT b'0' COMMENT '1.冻结 0.正常',
                                `tenant_id` varchar(32) DEFAULT NULL,
                                `department_id` bigint(20) DEFAULT NULL,
                                `type` varchar(10) DEFAULT '1' COMMENT '1.管理员 2.普通用户',
                                `delete_state` int(2) DEFAULT '1',
                                `index_config_url` varchar(255) DEFAULT NULL,
                                `index_config_code` varchar(255) DEFAULT NULL,
                                `is_show_screen` tinyint(1) DEFAULT NULL COMMENT '是否显示大屏图标 0 不显示，1 显示',
                                `registration_id` varchar(32) DEFAULT NULL COMMENT '极光推送系统对应的id',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `username` (`username`),
                                UNIQUE KEY `index_unique_ten_ph` (`tenant_id`,`phone`),
                                KEY `index_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_user_copy definition

CREATE TABLE `linkapp_user_copy` (
                                     `id` varchar(32) NOT NULL,
                                     `create_time` datetime DEFAULT NULL,
                                     `creator` varchar(32) DEFAULT NULL,
                                     `modifier` varchar(32) DEFAULT NULL,
                                     `modify_time` datetime DEFAULT NULL,
                                     `address` varchar(255) DEFAULT NULL,
                                     `birthday` date DEFAULT NULL,
                                     `code` varchar(32) DEFAULT NULL,
                                     `description` varchar(255) DEFAULT NULL,
                                     `email` varchar(255) DEFAULT NULL,
                                     `icon` longtext,
                                     `icon_sm` varchar(255) DEFAULT NULL,
                                     `id_number` varchar(18) DEFAULT NULL,
                                     `nickname` varchar(32) DEFAULT NULL,
                                     `password` varchar(128) NOT NULL,
                                     `phone` varchar(255) DEFAULT NULL,
                                     `sex` varchar(255) DEFAULT NULL,
                                     `username` varchar(50) DEFAULT NULL,
                                     `display` bit(1) DEFAULT NULL,
                                     `locked` bit(1) DEFAULT b'0' COMMENT '1.冻结 0.正常',
                                     `tenant_id` varchar(32) DEFAULT NULL,
                                     `department_id` bigint(20) DEFAULT NULL,
                                     `type` varchar(10) DEFAULT '1' COMMENT '1.管理员 2.普通用户',
                                     `delete_state` int(2) DEFAULT '1',
                                     `index_config_url` varchar(255) DEFAULT NULL,
                                     `index_config_code` varchar(255) DEFAULT NULL,
                                     `is_show_screen` tinyint(1) DEFAULT NULL COMMENT '是否显示大屏图标 0 不显示，1 显示',
                                     PRIMARY KEY (`id`),
                                     KEY `index_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_user_history_p definition

CREATE TABLE `linkapp_user_history_p` (
                                          `id_` varchar(32) NOT NULL,
                                          `modify_time_` datetime DEFAULT NULL,
                                          `password_` varchar(256) DEFAULT NULL,
                                          `user_id_` varchar(32) DEFAULT NULL,
                                          `tenant_id` varchar(32) DEFAULT NULL,
                                          PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_user_login_info definition

CREATE TABLE `linkapp_user_login_info` (
                                           `user_id_` varchar(32) NOT NULL,
                                           `error_count_` int(11) DEFAULT NULL,
                                           `error_time_` datetime DEFAULT NULL,
                                           `lock_time_` datetime DEFAULT NULL,
                                           `unlock_time_` datetime DEFAULT NULL,
                                           `tenant_id` varchar(32) DEFAULT NULL,
                                           `create_time` datetime DEFAULT NULL,
                                           `creator` varchar(32) DEFAULT NULL,
                                           `modifier` varchar(32) DEFAULT NULL,
                                           `modify_time` datetime DEFAULT NULL,
                                           PRIMARY KEY (`user_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.linkapp_user_ref_role definition

CREATE TABLE `linkapp_user_ref_role` (
                                         `user_id` varchar(32) DEFAULT NULL,
                                         `role_id` varchar(32) DEFAULT NULL,
                                         `tenant_id` varchar(32) DEFAULT NULL,
                                         `id` varchar(32) NOT NULL,
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.operate_log definition

CREATE TABLE `operate_log` (
                               `log_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                               `create_time` datetime DEFAULT NULL,
                               `creator` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                               `content` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                               `fail_information` longtext CHARACTER SET utf8 COLLATE utf8_bin,
                               `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                               `module_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                               `url` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                               `status` bit(1) DEFAULT NULL,
                               PRIMARY KEY (`log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_calendars definition

CREATE TABLE `qrtz_calendars` (
                                  `SCHED_NAME` varchar(120) NOT NULL,
                                  `CALENDAR_NAME` varchar(190) NOT NULL,
                                  `CALENDAR` blob NOT NULL,
                                  PRIMARY KEY (`SCHED_NAME`,`CALENDAR_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_fired_triggers definition

CREATE TABLE `qrtz_fired_triggers` (
                                       `SCHED_NAME` varchar(120) NOT NULL,
                                       `ENTRY_ID` varchar(95) NOT NULL,
                                       `TRIGGER_NAME` varchar(190) NOT NULL,
                                       `TRIGGER_GROUP` varchar(190) NOT NULL,
                                       `INSTANCE_NAME` varchar(190) NOT NULL,
                                       `FIRED_TIME` bigint(13) NOT NULL,
                                       `SCHED_TIME` bigint(13) NOT NULL,
                                       `PRIORITY` int(11) NOT NULL,
                                       `STATE` varchar(16) NOT NULL,
                                       `JOB_NAME` varchar(190) DEFAULT NULL,
                                       `JOB_GROUP` varchar(190) DEFAULT NULL,
                                       `IS_NONCONCURRENT` varchar(1) DEFAULT NULL,
                                       `REQUESTS_RECOVERY` varchar(1) DEFAULT NULL,
                                       PRIMARY KEY (`SCHED_NAME`,`ENTRY_ID`),
                                       KEY `IDX_QRTZ_FT_TRIG_INST_NAME` (`SCHED_NAME`,`INSTANCE_NAME`),
                                       KEY `IDX_QRTZ_FT_INST_JOB_REQ_RCVRY` (`SCHED_NAME`,`INSTANCE_NAME`,`REQUESTS_RECOVERY`),
                                       KEY `IDX_QRTZ_FT_J_G` (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`),
                                       KEY `IDX_QRTZ_FT_JG` (`SCHED_NAME`,`JOB_GROUP`),
                                       KEY `IDX_QRTZ_FT_T_G` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
                                       KEY `IDX_QRTZ_FT_TG` (`SCHED_NAME`,`TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_job_details definition

CREATE TABLE `qrtz_job_details` (
                                    `SCHED_NAME` varchar(120) NOT NULL,
                                    `JOB_NAME` varchar(190) NOT NULL,
                                    `JOB_GROUP` varchar(190) NOT NULL,
                                    `DESCRIPTION` varchar(250) DEFAULT NULL,
                                    `JOB_CLASS_NAME` varchar(250) NOT NULL,
                                    `IS_DURABLE` varchar(1) NOT NULL,
                                    `IS_NONCONCURRENT` varchar(1) NOT NULL,
                                    `IS_UPDATE_DATA` varchar(1) NOT NULL,
                                    `REQUESTS_RECOVERY` varchar(1) NOT NULL,
                                    `JOB_DATA` blob,
                                    PRIMARY KEY (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`),
                                    KEY `IDX_QRTZ_J_REQ_RECOVERY` (`SCHED_NAME`,`REQUESTS_RECOVERY`),
                                    KEY `IDX_QRTZ_J_GRP` (`SCHED_NAME`,`JOB_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_locks definition

CREATE TABLE `qrtz_locks` (
                              `SCHED_NAME` varchar(120) NOT NULL,
                              `LOCK_NAME` varchar(40) NOT NULL,
                              PRIMARY KEY (`SCHED_NAME`,`LOCK_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_paused_trigger_grps definition

CREATE TABLE `qrtz_paused_trigger_grps` (
                                            `SCHED_NAME` varchar(120) NOT NULL,
                                            `TRIGGER_GROUP` varchar(190) NOT NULL,
                                            PRIMARY KEY (`SCHED_NAME`,`TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_scheduler_state definition

CREATE TABLE `qrtz_scheduler_state` (
                                        `SCHED_NAME` varchar(120) NOT NULL,
                                        `INSTANCE_NAME` varchar(190) NOT NULL,
                                        `LAST_CHECKIN_TIME` bigint(13) NOT NULL,
                                        `CHECKIN_INTERVAL` bigint(13) NOT NULL,
                                        PRIMARY KEY (`SCHED_NAME`,`INSTANCE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.regular_temperature_control_execute_detail definition

CREATE TABLE `regular_temperature_control_execute_detail` (
                                                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                              `regular_tci_id` bigint(50) NOT NULL COMMENT '定时温控基本信息表ID',
                                                              `execute_time` varchar(225) DEFAULT NULL COMMENT '执行时间',
                                                              `execute_data` varchar(500) DEFAULT NULL COMMENT '执行数据',
                                                              `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                                              `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                                              `week_execute_flag` tinyint(3) DEFAULT NULL COMMENT '周末是否执行标识(0:是, 1:否)',
                                                              `not_execute_date` varchar(1000) DEFAULT NULL COMMENT '不执行的日期',
                                                              `cron_expression` varchar(50) DEFAULT NULL COMMENT '周期cron表达式',
                                                              `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                              `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                              `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                              `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                              `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                                              `job_id` varchar(32) DEFAULT NULL COMMENT 'linkapp_job_entity主键ID',
                                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务执行表';


-- linkappdb.regular_temperature_control_info definition

CREATE TABLE `regular_temperature_control_info` (
                                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                    `name` varchar(50) NOT NULL COMMENT '名称',
                                                    `description` varchar(225) DEFAULT NULL COMMENT '说明',
                                                    `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                                                    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                    `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                                                    `status` tinyint(3) DEFAULT '0' COMMENT '1:启用，0:禁用',
                                                    `delete_status` tinyint(3) DEFAULT '0' COMMENT '0:正常，1:删除',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时温控基本信息表';


-- linkappdb.regular_temperature_control_ref_device definition

CREATE TABLE `regular_temperature_control_ref_device` (
                                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                          `regular_tci_id` bigint(50) NOT NULL COMMENT '定时温控基本信息表ID',
                                                          `device_code` varchar(32) DEFAULT NULL COMMENT '设备code码',
                                                          PRIMARY KEY (`id`),
                                                          KEY `index_device_code` (`device_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务关联设备表';


-- linkappdb.rule_condition definition

CREATE TABLE `rule_condition` (
                                  `id` varchar(32) NOT NULL COMMENT '主键',
                                  `type` int(2) DEFAULT NULL COMMENT '条件类型：1-时间范围，2设备状态',
                                  `sort_flag` varchar(20) DEFAULT NULL COMMENT '排序标记',
                                  `rule_engine_id` varchar(32) DEFAULT NULL COMMENT '规则引擎id',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  `device_code` varchar(32) DEFAULT NULL COMMENT '设备编号',
                                  `time_scope_cron` varchar(120) DEFAULT NULL COMMENT '时间范围cron表达式',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条件器';


-- linkappdb.rule_engine definition

CREATE TABLE `rule_engine` (
                               `id` varchar(32) NOT NULL COMMENT '主键',
                               `name` varchar(50) DEFAULT NULL COMMENT '规则名称',
                               `status` int(2) DEFAULT '0' COMMENT '状态 0-停用 1-启用',
                               `space_id` varchar(32) DEFAULT NULL COMMENT '空间id',
                               `area_id` varchar(32) DEFAULT NULL COMMENT '区域id',
                               `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
                               `auto_recoverable` tinyint(1) DEFAULT '0' COMMENT '可自动恢复的',
                               `description` varchar(255) DEFAULT NULL COMMENT '规则描述说明',
                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                               `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                               `creator` varchar(32) DEFAULT NULL COMMENT '创建人id',
                               `modifier` varchar(32) DEFAULT NULL COMMENT '修改人id',
                               PRIMARY KEY (`id`),
                               KEY `rule_area_id_index` (`space_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能规则引擎';


-- linkappdb.rule_execution definition

CREATE TABLE `rule_execution` (
                                  `id` varchar(32) NOT NULL COMMENT '主键',
                                  `type` int(2) DEFAULT NULL COMMENT '类型：1-下行服务，2-告警工单',
                                  `rule_engine_id` varchar(32) DEFAULT NULL COMMENT '规则引擎id',
                                  `device_unit_id` varchar(32) DEFAULT NULL COMMENT '设备型号id',
                                  `sort_flag` varchar(20) DEFAULT NULL COMMENT '排序标记',
                                  `alarm_template_id` varchar(32) DEFAULT NULL COMMENT '告警通知模板id(只有type为2-告警工单时，才有值)',
                                  `delay_time` float(15,2) DEFAULT '0.00' COMMENT '延迟时间',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='执行器';


-- linkappdb.rule_execution_ref_alarm_person_contact definition

CREATE TABLE `rule_execution_ref_alarm_person_contact` (
                                                           `id` varchar(32) NOT NULL COMMENT '主键',
                                                           `rule_execution_id` varchar(32) DEFAULT NULL COMMENT '执行器 id',
                                                           `alarm_person_contact_id` varchar(32) DEFAULT NULL COMMENT '告警通知人 id',
                                                           `person_type` int(11) DEFAULT NULL COMMENT '通知人类型，null、0是以前的逻辑，1是租户用户',
                                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='执行器关联告警通知人';


-- linkappdb.rule_trigger definition

CREATE TABLE `rule_trigger` (
                                `id` varchar(32) NOT NULL COMMENT '主键',
                                `type` int(2) DEFAULT NULL COMMENT '类型：3-定时触发，1设备触发属性触发，2设备触发上下线触发',
                                `rule_engine_id` varchar(32) DEFAULT NULL COMMENT '规则引擎id',
                                `sort_flag` varchar(20) DEFAULT NULL COMMENT '排序标记',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                `common_rule_item` int(1) DEFAULT NULL COMMENT '通用规则的类型列举 0-离线 1-设备上线',
                                `device_unit_id` varchar(32) DEFAULT NULL COMMENT '设备型号id',
                                `job_entity_id` varchar(32) DEFAULT NULL COMMENT '定时任务id',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则触发器';


-- linkappdb.rule_trigger_ref_device definition

CREATE TABLE `rule_trigger_ref_device` (
                                           `id` varchar(32) NOT NULL COMMENT '主键',
                                           `rule_trigger_id` varchar(32) DEFAULT NULL COMMENT '触发器 id',
                                           `device_code` varchar(32) DEFAULT NULL COMMENT '设备编号',
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `rule_trigger_id_device_code_index` (`rule_trigger_id`,`device_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能规则联动配置关联设备表';


-- linkappdb.sbdw_alarm_config definition

CREATE TABLE `sbdw_alarm_config` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `type` tinyint(1) NOT NULL COMMENT '1:进入,2:离开,3:在外停留,4:在内停留',
                                     `duration` int(11) DEFAULT NULL COMMENT '进入、离开、停留时长(分钟)',
                                     `tooltip` varchar(255) DEFAULT NULL COMMENT '提示信息',
                                     `fence_id` int(11) NOT NULL COMMENT '电子围栏ID',
                                     PRIMARY KEY (`id`),
                                     KEY `fence_id_index` (`fence_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警设备';


-- linkappdb.sbdw_alarm_device_config definition

CREATE TABLE `sbdw_alarm_device_config` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                            `device_code` varchar(225) DEFAULT NULL COMMENT '设备编码',
                                            `fence_id` int(11) NOT NULL COMMENT '电子围栏ID',
                                            PRIMARY KEY (`id`),
                                            KEY `fence_id_index` (`fence_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警设备';


-- linkappdb.sbdw_alarm_info definition

CREATE TABLE `sbdw_alarm_info` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `device_code` varchar(32) NOT NULL COMMENT '设备编码',
                                   `fence_id` int(11) NOT NULL COMMENT '电子围栏ID',
                                   `type` int(3) DEFAULT NULL COMMENT '电子围栏超时告警类型（1:进入超时告警,2:离开超时告警）',
                                   `record_id` int(11) DEFAULT NULL COMMENT '设备位置与围栏关系记录ID',
                                   `tooltip` varchar(255) DEFAULT NULL COMMENT '提示信息',
                                   `alarm_time` datetime DEFAULT NULL COMMENT '告警时间',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
                                   `sms_status` int(3) DEFAULT '0' COMMENT '短信发送状态：0:未发送，1：已发送',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警设备';


-- linkappdb.sbdw_alarm_person_contact definition

CREATE TABLE `sbdw_alarm_person_contact` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                             `fence_id` int(11) NOT NULL COMMENT '电子围栏ID',
                                             `alarm_person_contact_id` varchar(32) DEFAULT NULL COMMENT '告警通知人 id',
                                             PRIMARY KEY (`id`),
                                             KEY `fence_id_index` (`fence_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警设备';


-- linkappdb.sbdw_electronicfence definition

CREATE TABLE `sbdw_electronicfence` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `name` varchar(50) DEFAULT NULL COMMENT '围栏名称',
                                        `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
                                        `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                        `gid` varchar(50) DEFAULT NULL COMMENT '高德围栏ID',
                                        `update_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `type` tinyint(1) DEFAULT NULL COMMENT '1:圆,2:多边形',
                                        PRIMARY KEY (`id`),
                                        KEY `tenant_id_index` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子围栏';


-- linkappdb.sbdw_location_fence_record definition

CREATE TABLE `sbdw_location_fence_record` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                              `device_code` varchar(32) NOT NULL COMMENT '设备编码',
                                              `fence_id` int(11) NOT NULL COMMENT '电子围栏ID',
                                              `in_flag` int(3) DEFAULT NULL COMMENT '指定坐标是否在围栏中(0:不在，1:在)',
                                              `positioning_time` datetime DEFAULT NULL COMMENT '定位时间',
                                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `check_status` int(3) DEFAULT '0' COMMENT '0:未验证，1：已验证',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备位置与围栏关系记录表';


-- linkappdb.sm_area definition

CREATE TABLE `sm_area` (
                           `id_` bigint(20) NOT NULL,
                           `create_time` datetime DEFAULT NULL,
                           `creator` varchar(32) DEFAULT NULL,
                           `modifier` varchar(32) DEFAULT NULL,
                           `modify_time` datetime DEFAULT NULL,
                           `latitude_` double DEFAULT NULL,
                           `level_` int(11) DEFAULT NULL,
                           `longitude_` double DEFAULT NULL,
                           `name_` varchar(32) DEFAULT NULL,
                           `parent_id_` bigint(20) DEFAULT NULL,
                           `search_code_` varchar(255) DEFAULT NULL,
                           PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_customer definition

CREATE TABLE `sm_customer` (
                               `id_` bigint(20) NOT NULL,
                               `create_time` datetime DEFAULT NULL,
                               `creator` varchar(32) DEFAULT NULL,
                               `modifier` varchar(32) DEFAULT NULL,
                               `modify_time` datetime DEFAULT NULL,
                               `address_` varchar(255) DEFAULT NULL,
                               `customer_code_` varchar(255) DEFAULT NULL,
                               `display_` bit(1) DEFAULT NULL,
                               `email_` varchar(32) DEFAULT NULL,
                               `locked_` bit(1) DEFAULT NULL,
                               `logo_` varchar(32) DEFAULT NULL,
                               `name_` varchar(32) DEFAULT NULL,
                               `phone_number_` varchar(32) DEFAULT NULL,
                               `remark_` varchar(255) DEFAULT NULL,
                               PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_department definition

CREATE TABLE `sm_department` (
                                 `id_` bigint(20) NOT NULL,
                                 `create_time_` datetime DEFAULT NULL,
                                 `creator_` varchar(32) DEFAULT NULL,
                                 `modifier_` varchar(32) DEFAULT NULL,
                                 `modify_time_` datetime DEFAULT NULL,
                                 `address_` varchar(255) DEFAULT NULL,
                                 `code_` varchar(32) DEFAULT NULL,
                                 `contact_` varchar(255) DEFAULT NULL,
                                 `email_` varchar(32) DEFAULT NULL,
                                 `level_` int(11) DEFAULT NULL,
                                 `name_` varchar(32) DEFAULT NULL,
                                 `parent_id_` bigint(20) DEFAULT NULL,
                                 `remark_` varchar(128) DEFAULT NULL,
                                 `search_code_` varchar(255) DEFAULT NULL,
                                 `customer_id_` bigint(20) DEFAULT NULL,
                                 `display_` bit(1) DEFAULT NULL,
                                 `order_no_` int(11) DEFAULT NULL,
                                 PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_dict definition

CREATE TABLE `sm_dict` (
                           `id_` bigint(20) NOT NULL,
                           `create_time_` datetime DEFAULT NULL,
                           `creator_` varchar(32) DEFAULT NULL,
                           `modifier_` varchar(32) DEFAULT NULL,
                           `modify_time_` datetime DEFAULT NULL,
                           `code_` varchar(32) DEFAULT NULL,
                           `display_` bit(1) DEFAULT NULL,
                           `ext_` varchar(128) DEFAULT NULL,
                           `level_` int(11) DEFAULT NULL,
                           `name_` varchar(32) DEFAULT NULL,
                           `order_no_` int(11) DEFAULT NULL,
                           `parent_id_` bigint(20) DEFAULT NULL,
                           `remark_` varchar(128) DEFAULT NULL,
                           PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_dict_item definition

CREATE TABLE `sm_dict_item` (
                                `id_` bigint(20) NOT NULL,
                                `create_time_` datetime DEFAULT NULL,
                                `creator_` varchar(32) DEFAULT NULL,
                                `modifier_` varchar(32) DEFAULT NULL,
                                `modify_time_` datetime DEFAULT NULL,
                                `code_` varchar(32) DEFAULT NULL,
                                `dict_id_` bigint(20) DEFAULT NULL,
                                `display_` bit(1) DEFAULT NULL,
                                `ext_` varchar(128) DEFAULT NULL,
                                `level_` int(11) DEFAULT NULL,
                                `name_` varchar(32) DEFAULT NULL,
                                `order_no_` int(11) DEFAULT NULL,
                                `parent_id_` bigint(20) DEFAULT NULL,
                                `remark_` varchar(128) DEFAULT NULL,
                                PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_feedback definition

CREATE TABLE `sm_feedback` (
                               `feedback_id_` bigint(20) NOT NULL,
                               `create_time_` datetime DEFAULT NULL,
                               `creator_` varchar(32) DEFAULT NULL,
                               `modifier_` varchar(32) DEFAULT NULL,
                               `modify_time_` datetime DEFAULT NULL,
                               `customer_id_` bigint(20) DEFAULT NULL,
                               `display_` bit(1) DEFAULT NULL,
                               `feedback_information_` longtext,
                               PRIMARY KEY (`feedback_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_i18n definition

CREATE TABLE `sm_i18n` (
                           `id_` bigint(20) NOT NULL,
                           `create_time` datetime DEFAULT NULL,
                           `creator` varchar(32) DEFAULT NULL,
                           `modifier` varchar(32) DEFAULT NULL,
                           `modify_time` datetime DEFAULT NULL,
                           `key_` bigint(20) DEFAULT NULL,
                           `language_` varchar(32) DEFAULT NULL,
                           `module_` varchar(255) DEFAULT NULL,
                           `value_` varchar(64) DEFAULT NULL,
                           PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_language definition

CREATE TABLE `sm_language` (
                               `language_id_` bigint(20) NOT NULL,
                               `code_` varchar(32) DEFAULT NULL,
                               `description_` varchar(32) DEFAULT NULL,
                               PRIMARY KEY (`language_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_log definition

CREATE TABLE `sm_log` (
                          `log_id_` bigint(20) NOT NULL,
                          `create_time_` datetime DEFAULT NULL,
                          `creator_` varchar(50) DEFAULT NULL,
                          `modifier_` varchar(50) DEFAULT NULL,
                          `modify_time_` datetime DEFAULT NULL,
                          `content_` varchar(4000) DEFAULT NULL,
                          `customer_id_` bigint(20) DEFAULT NULL,
                          `display_` bit(1) DEFAULT NULL,
                          `fail_information_` longtext,
                          `ip_` varchar(255) DEFAULT NULL,
                          `module_id_` bigint(20) DEFAULT NULL,
                          `request_information_` longtext,
                          `response_information_` longtext,
                          `status_` bit(1) DEFAULT NULL,
                          PRIMARY KEY (`log_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_material definition

CREATE TABLE `sm_material` (
                               `id_` bigint(20) NOT NULL,
                               `create_time` datetime DEFAULT NULL,
                               `creator` varchar(32) DEFAULT NULL,
                               `modifier` varchar(32) DEFAULT NULL,
                               `modify_time` datetime DEFAULT NULL,
                               `department_id` bigint(20) DEFAULT NULL,
                               `audition_code` varchar(255) DEFAULT NULL,
                               `file_length` bigint(20) DEFAULT NULL,
                               `file_name_` varchar(255) DEFAULT NULL,
                               `file_size` bigint(20) DEFAULT NULL,
                               `file_suffixes` varchar(255) DEFAULT NULL,
                               `file_url_` varchar(255) DEFAULT NULL,
                               `height` int(11) DEFAULT NULL,
                               `thumbnail` varchar(255) DEFAULT NULL,
                               `type_code` varchar(255) DEFAULT NULL,
                               `width` int(11) DEFAULT NULL,
                               PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_menu definition

CREATE TABLE `sm_menu` (
                           `id_` bigint(20) NOT NULL,
                           `create_time_` datetime DEFAULT NULL,
                           `creator_` varchar(32) DEFAULT NULL,
                           `modifier_` varchar(32) DEFAULT NULL,
                           `modify_time_` datetime DEFAULT NULL,
                           `display_` bit(1) DEFAULT NULL,
                           `icon_` varchar(128) DEFAULT NULL,
                           `level_` int(11) DEFAULT NULL,
                           `name_` varchar(32) DEFAULT NULL,
                           `order_no_` int(11) DEFAULT NULL,
                           `parent_id_` bigint(20) DEFAULT NULL,
                           `path_` varchar(32) DEFAULT NULL,
                           `privilege_code_` bigint(20) DEFAULT NULL,
                           `remark_` varchar(128) DEFAULT NULL,
                           `search_code_` varchar(128) DEFAULT NULL,
                           `state_` bit(1) DEFAULT NULL,
                           `system_id_` bigint(20) DEFAULT NULL,
                           PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_privilege definition

CREATE TABLE `sm_privilege` (
                                `id_` bigint(20) NOT NULL,
                                `create_time_` datetime DEFAULT NULL,
                                `creator_` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                `privilege_code_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                `description_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                `level_` int(11) DEFAULT NULL,
                                `name_` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                `parent_id_` bigint(20) DEFAULT NULL,
                                `search_code_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                `seq_` int(11) DEFAULT NULL,
                                `type_` int(11) DEFAULT NULL,
                                `url_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                `is_log_` int(11) DEFAULT NULL,
                                PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_role definition

CREATE TABLE `sm_role` (
                           `role_id_` bigint(20) NOT NULL,
                           `create_time` datetime DEFAULT NULL,
                           `creator` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `modifier` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `modify_time` datetime DEFAULT NULL,
                           `description_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `name_` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `department_id_` bigint(20) DEFAULT NULL,
                           `display_` bit(1) DEFAULT NULL,
                           PRIMARY KEY (`role_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_system_config definition

CREATE TABLE `sm_system_config` (
                                    `sys_key_` varchar(128) NOT NULL,
                                    `description_` varchar(256) DEFAULT NULL,
                                    `type_` int(11) DEFAULT NULL,
                                    `sys_value_` varchar(256) DEFAULT NULL,
                                    PRIMARY KEY (`sys_key_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_system_info definition

CREATE TABLE `sm_system_info` (
                                  `id_` bigint(20) NOT NULL,
                                  `create_time_` datetime DEFAULT NULL,
                                  `creator_` varchar(255) DEFAULT NULL,
                                  `modifier_` varchar(255) DEFAULT NULL,
                                  `modify_time_` datetime DEFAULT NULL,
                                  `code_` varchar(32) DEFAULT NULL,
                                  `display_` bit(1) DEFAULT NULL,
                                  `name_` varchar(32) DEFAULT NULL,
                                  `order_no_` int(11) DEFAULT NULL,
                                  `remark_` varchar(128) DEFAULT NULL,
                                  `url_` varchar(64) DEFAULT NULL,
                                  PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_user definition

CREATE TABLE `sm_user` (
                           `id` bigint(20) NOT NULL,
                           `create_time` datetime DEFAULT NULL,
                           `creator` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `modifier` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `modify_time` datetime DEFAULT NULL,
                           `department_id` bigint(20) DEFAULT NULL,
                           `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `birthday` date DEFAULT NULL,
                           `code` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `icon` longtext CHARACTER SET utf8 COLLATE utf8_bin,
                           `icon_sm` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `id_number` varchar(18) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `nickname` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `password` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                           `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `sex` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `username` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                           `display` bit(1) DEFAULT NULL,
                           `locked` bit(1) DEFAULT NULL,
                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_user_history_p definition

CREATE TABLE `sm_user_history_p` (
                                     `id_` bigint(20) NOT NULL,
                                     `modify_time_` datetime DEFAULT NULL,
                                     `password_` varchar(256) DEFAULT NULL,
                                     `user_id_` bigint(20) DEFAULT NULL,
                                     PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_user_login_info definition

CREATE TABLE `sm_user_login_info` (
                                      `user_id_` bigint(20) NOT NULL,
                                      `error_count_` int(11) DEFAULT NULL,
                                      `error_time_` datetime DEFAULT NULL,
                                      `lock_time_` datetime DEFAULT NULL,
                                      `unlock_time_` datetime DEFAULT NULL,
                                      PRIMARY KEY (`user_id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.sm_user_ref_role definition

CREATE TABLE `sm_user_ref_role` (
                                    `user_id` bigint(20) NOT NULL,
                                    `role_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.smoke_warning definition

CREATE TABLE `smoke_warning` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键(自增)',
                                 `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                 `device_code` varchar(32) NOT NULL COMMENT '设备编号',
                                 `area_path` varchar(255) DEFAULT NULL COMMENT '区域层级路径',
                                 `device_name` varchar(32) NOT NULL COMMENT '设备名称',
                                 `cycle` varchar(20) DEFAULT NULL COMMENT '周期',
                                 `data_count` int(8) DEFAULT NULL COMMENT '统计的数据量',
                                 `average_value` float(10,2) NOT NULL COMMENT '平均值',
                                 `start_time` datetime NOT NULL COMMENT '开始时间',
                                 `end_time` datetime NOT NULL COMMENT '结束时间',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 UNIQUE KEY `unique_code_endtime` (`device_code`,`end_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='荆州油烟预警';


-- linkappdb.tenant_function_ref_area definition

CREATE TABLE `tenant_function_ref_area` (
                                            `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
                                            `function_identifier` varchar(50) NOT NULL COMMENT '功能标识',
                                            `area_id` varchar(32) NOT NULL COMMENT '区域id',
                                            `area_path` varchar(512) DEFAULT NULL COMMENT '区域位置'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户关联功能关联区域';


-- linkappdb.qrtz_triggers definition

CREATE TABLE `qrtz_triggers` (
                                 `SCHED_NAME` varchar(120) NOT NULL,
                                 `TRIGGER_NAME` varchar(190) NOT NULL,
                                 `TRIGGER_GROUP` varchar(190) NOT NULL,
                                 `JOB_NAME` varchar(190) NOT NULL,
                                 `JOB_GROUP` varchar(190) NOT NULL,
                                 `DESCRIPTION` varchar(250) DEFAULT NULL,
                                 `NEXT_FIRE_TIME` bigint(13) DEFAULT NULL,
                                 `PREV_FIRE_TIME` bigint(13) DEFAULT NULL,
                                 `PRIORITY` int(11) DEFAULT NULL,
                                 `TRIGGER_STATE` varchar(16) NOT NULL,
                                 `TRIGGER_TYPE` varchar(8) NOT NULL,
                                 `START_TIME` bigint(13) NOT NULL,
                                 `END_TIME` bigint(13) DEFAULT NULL,
                                 `CALENDAR_NAME` varchar(190) DEFAULT NULL,
                                 `MISFIRE_INSTR` smallint(2) DEFAULT NULL,
                                 `JOB_DATA` blob,
                                 PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
                                 KEY `IDX_QRTZ_T_J` (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`),
                                 KEY `IDX_QRTZ_T_JG` (`SCHED_NAME`,`JOB_GROUP`),
                                 KEY `IDX_QRTZ_T_C` (`SCHED_NAME`,`CALENDAR_NAME`),
                                 KEY `IDX_QRTZ_T_G` (`SCHED_NAME`,`TRIGGER_GROUP`),
                                 KEY `IDX_QRTZ_T_STATE` (`SCHED_NAME`,`TRIGGER_STATE`),
                                 KEY `IDX_QRTZ_T_N_STATE` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`,`TRIGGER_STATE`),
                                 KEY `IDX_QRTZ_T_N_G_STATE` (`SCHED_NAME`,`TRIGGER_GROUP`,`TRIGGER_STATE`),
                                 KEY `IDX_QRTZ_T_NEXT_FIRE_TIME` (`SCHED_NAME`,`NEXT_FIRE_TIME`),
                                 KEY `IDX_QRTZ_T_NFT_ST` (`SCHED_NAME`,`TRIGGER_STATE`,`NEXT_FIRE_TIME`),
                                 KEY `IDX_QRTZ_T_NFT_MISFIRE` (`SCHED_NAME`,`MISFIRE_INSTR`,`NEXT_FIRE_TIME`),
                                 KEY `IDX_QRTZ_T_NFT_ST_MISFIRE` (`SCHED_NAME`,`MISFIRE_INSTR`,`NEXT_FIRE_TIME`,`TRIGGER_STATE`),
                                 KEY `IDX_QRTZ_T_NFT_ST_MISFIRE_GRP` (`SCHED_NAME`,`MISFIRE_INSTR`,`NEXT_FIRE_TIME`,`TRIGGER_GROUP`,`TRIGGER_STATE`),
                                 CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) REFERENCES `qrtz_job_details` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_blob_triggers definition

CREATE TABLE `qrtz_blob_triggers` (
                                      `SCHED_NAME` varchar(120) NOT NULL,
                                      `TRIGGER_NAME` varchar(190) NOT NULL,
                                      `TRIGGER_GROUP` varchar(190) NOT NULL,
                                      `BLOB_DATA` blob,
                                      PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
                                      KEY `SCHED_NAME` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
                                      CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_cron_triggers definition

CREATE TABLE `qrtz_cron_triggers` (
                                      `SCHED_NAME` varchar(120) NOT NULL,
                                      `TRIGGER_NAME` varchar(190) NOT NULL,
                                      `TRIGGER_GROUP` varchar(190) NOT NULL,
                                      `CRON_EXPRESSION` varchar(120) NOT NULL,
                                      `TIME_ZONE_ID` varchar(80) DEFAULT NULL,
                                      PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
                                      CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_simple_triggers definition

CREATE TABLE `qrtz_simple_triggers` (
                                        `SCHED_NAME` varchar(120) NOT NULL,
                                        `TRIGGER_NAME` varchar(190) NOT NULL,
                                        `TRIGGER_GROUP` varchar(190) NOT NULL,
                                        `REPEAT_COUNT` bigint(7) NOT NULL,
                                        `REPEAT_INTERVAL` bigint(12) NOT NULL,
                                        `TIMES_TRIGGERED` bigint(10) NOT NULL,
                                        PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
                                        CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- linkappdb.qrtz_simprop_triggers definition

CREATE TABLE `qrtz_simprop_triggers` (
                                         `SCHED_NAME` varchar(120) NOT NULL,
                                         `TRIGGER_NAME` varchar(190) NOT NULL,
                                         `TRIGGER_GROUP` varchar(190) NOT NULL,
                                         `STR_PROP_1` varchar(512) DEFAULT NULL,
                                         `STR_PROP_2` varchar(512) DEFAULT NULL,
                                         `STR_PROP_3` varchar(512) DEFAULT NULL,
                                         `INT_PROP_1` int(11) DEFAULT NULL,
                                         `INT_PROP_2` int(11) DEFAULT NULL,
                                         `LONG_PROP_1` bigint(20) DEFAULT NULL,
                                         `LONG_PROP_2` bigint(20) DEFAULT NULL,
                                         `DEC_PROP_1` decimal(13,4) DEFAULT NULL,
                                         `DEC_PROP_2` decimal(13,4) DEFAULT NULL,
                                         `BOOL_PROP_1` varchar(1) DEFAULT NULL,
                                         `BOOL_PROP_2` varchar(1) DEFAULT NULL,
                                         PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
                                         CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;