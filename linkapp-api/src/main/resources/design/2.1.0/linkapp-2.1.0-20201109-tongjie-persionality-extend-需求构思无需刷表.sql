#  设备属性个性化继承

insert into linkapp_device_attribute_tenant_config (id, tenant_id, device_attribute_id, sort_no, is_show)
select replace(uuid(), '-', ''), '#{tenantId}', lda2.id, ldatc.sort_no, ldatc.is_show
from linkapp_device_attribute_tenant_config ldatc
         inner join linkapp_device_attribute lda on ldatc.device_attribute_id = lda.id
#     lda2 是 新插入的设备属性
         inner join linkapp_device_attribute lda2 on lda.identifier = lda2.identifier and lda.device_unit_id = lda2.device_unit_id
where lda2.id in ('需求构思无需刷表');
