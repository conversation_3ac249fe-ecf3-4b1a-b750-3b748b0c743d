package ${package.Entity};

#foreach($pkg in ${table.importPackages})
import ${pkg};
#end

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * $!{table.comment}
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("${table.name}")
public class ${entity} implements Serializable{
    #if(${entitySerialVersionUID})
    private static final long serialVersionUID=1L;
    #end

    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    private Integer id;

## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    /**
     * ${field.comment}
     */
    @TableField(value = "${field.name}")
## 逻辑删除注解
    #if("${field.name}" == "$logicDeleteFieldName")
    @TableLogic
    private Integer ${field.propertyName};
    #else
    private ${field.propertyType} ${field.propertyName};
    #end

#end
## ----------  END 字段循环遍历  ----------
}
