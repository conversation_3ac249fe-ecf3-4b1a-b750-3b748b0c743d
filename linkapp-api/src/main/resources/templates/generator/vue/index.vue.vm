#set($entity = ${entity})
#set($lowerCaseEntity = $entity.substring(0,1).toLowerCase() + $entity.substring(1))
<template>
    <div class="userManagerList" :loading="loading">
        <Card dis-hover class="search-card">
            <Form :model="searchObj" @submit.native.prevent label-position="left">
                <Row :gutter="20">
                    <Col :span="item.span" v-for="(item, key) in searchFormSchema" :key="key">
                    <FormItem :label="item.label" :label-width="40">
                        <Input v-model.trim="searchObj[item.field]" :placeholder="'请输入' + item.label" clearable></Input>
                    </FormItem>
                    </Col>
                    <Col span="6">
                    <FormItem :label-width="20">
                        <Button icon="md-refresh" @click="resetSearch">重置</Button>
                        <Button type="primary" icon="md-search" @click="search">搜索</Button>
                    </FormItem>
                    </Col>
                </Row>
            </Form>
        </Card>
        <ContentCard>
            <!-- 数据列表 -->
            <div class="btn-content">
                <Button type="primary" icon="md-add" v-auth="'${package.ModuleName}:${table.name}:add'" @click="handleSave">新建</Button>
                <Button v-show="selectionRows && selectionRows.length > 0" type="error" icon="md-trash" v-auth="'${package.ModuleName}:${table.name}:deleteBatch'" @click="batchDelete">批量删除</Button>
            </div>
            <EmpTable :columns="columns" url="/${package.ModuleName}/${table.entityPath}/listPage" ref="tb" :height="tbHeight" @on-selection-change="onSelectChange">
                <template slot-scope="{ row }" slot="action">
                    <LinkBtn
                            type="text"
                            size="small"
                            style="font-size: 13px;"
                            v-auth="'${package.ModuleName}:${table.name}:edit'" @click.native="handleSave(row)" >编辑</LinkBtn>
                    <Dropdown transfer v-auth="[ '${package.ModuleName}:${table.name}:delete' ]" >
                        <a href="javascript:void(0)">
                            更多
                            <Icon type="ios-arrow-down"></Icon>
                        </a>
                        <DropdownMenu transfer slot="list">
                            <DropdownItem v-auth="'${package.ModuleName}:${table.name}:delete'" @click.native="handleDel(row)" >删除</DropdownItem>
                        </DropdownMenu>
                    </Dropdown>
                </template>
            </EmpTable>
        </ContentCard>
        <ModalForm ref="modalForm" @success="handleSuccess" />
    </div>
</template>
#[[
<script>
    import Util from '@/libs/util';
    import EmpTable from '@/components/EmpTable.vue';
    import ModalForm from './components/modalForm'
    import { columns, searchFormSchema } from './data'
    import { api } from './api'

    export default {
        components: { EmpTable, ModalForm },
        data() {
            return {
                loading: false,
                tbHeight: Util.tbHeight,
                columns,
                searchFormSchema,
                searchObj: {},
                selectionRows: [],
            };
        },
        activated() {
            const tabs = sessionStorage.getItem('tabs') ? JSON.parse(sessionStorage.getItem('tabs')) : [];
            if (!tabs.find(tab => tab === this.$route.name)) {
                this.searchObj = {};
                this.search();
            }
        },
        deactivated() {
        },
        mounted() {
            this.search()
        },
        methods: {
            resetSearch () {
                this.searchObj = {}
                this.search()
            },
            search() {
                this.$refs.tb.search(this.searchObj)
            },
            onSelectChange () {
                this.selectionRows = this.$refs.tb.getSelection('id')
            },
            handleSave (row) {
                this.$refs.modalForm.handleSave(row)
            },
            handleDel (row) {
                this.$Modal.confirm({
                    title: '删除',
                    content: `<p>您确定要删除该数据吗？</p>`,
                    onOk: () => {
                        api.deleteOne({ params: { id: row.id }}).then(res => {
                            this.search()
                        }).catch(err => {})
                    }
                });
            },
            batchDelete (row) {
                this.$Modal.confirm({
                    title: '批量删除',
                    content: `<p>您确定要批量删除选中数据吗？</p>`,
                    onOk: () => {
                        api.batchDelete({ params: { ids: this.selectionRows.join(',') }}).then(res => {
                            this.search()
                        }).catch(err => {})
                    }
                });
            },
            handleSuccess () {
                this.search()
            }
        }
    };
</script>
]]#
<style lang="less" scoped>
    @keyframes ani-demo-spin {
        from {
            transform: rotate(0deg);
        }
        50% {
            transform: rotate(180deg);
        }
        to {
            transform: rotate(360deg);
        }
    }
</style>
