import Util from '@/libs/util'

export const saveOrUpdate = (data) => Util.requestPost('/${package.ModuleName}/${table.entityPath}/save', data)
export const deleteOne = (data) => Util.requestDelete('/${package.ModuleName}/${table.entityPath}/delete', data)
export const batchDelete = (data) => Util.requestDelete('/${package.ModuleName}/${table.entityPath}/deleteBatch', data)
export const exportData = (data) => Util.requestPost('/${package.ModuleName}/${table.entityPath}/exportXls', data)

export const api = {
    saveOrUpdate,
    deleteOne,
    batchDelete,
    exportData
}