select seq_ into @lastsqp from linkapp_privilege order by id_ desc limit 1;

INSERT INTO `linkapp_privilege` (`parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `seq_`, `type_`, `is_log_`, `flag_`, `url_`)
VALUES
    ('0', '$!{table.comment}', 'modules:${package.ModuleName}:menu', '$!{table.comment}', 1, @lastsqp + 0.01, 1, 1, 0, 'modules/${package.ModuleName}');

-- 按钮父菜单ID
set @parentId = @@identity;

INSERT INTO `linkapp_privilege` (`parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `seq_`, `type_`, `is_log_`, `flag_`)
VALUES
    (@parentId, '添加', '${package.ModuleName}:${table.name}:add', '$!{table.comment}添加', 2, @lastsqp + 0.02, 2, 1, 0);
INSERT INTO `linkapp_privilege` (`parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `seq_`, `type_`, `is_log_`, `flag_`)
VALUES
    (@parentId, '编辑', '${package.ModuleName}:${table.name}:edit', '$!{table.comment}编辑', 2, @lastsqp + 0.03, 2, 1, 0);
INSERT INTO `linkapp_privilege` (`parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `seq_`, `type_`, `is_log_`, `flag_`)
VALUES
    (@parentId, '删除', '${package.ModuleName}:${table.name}:delete', '$!{table.comment}删除', 2, @lastsqp + 0.04, 2, 1, 0);
INSERT INTO `linkapp_privilege` (`parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `seq_`, `type_`, `is_log_`, `flag_`)
VALUES
    (@parentId, '批量删除', '${package.ModuleName}:${table.name}:deleteBatch', '$!{table.comment}批量删除', 2, @lastsqp + 0.05, 2, 1, 0);