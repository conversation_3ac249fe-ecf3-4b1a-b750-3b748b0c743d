<template>
    <Modal
            v-model="showModalForm"
            :title="addTitle"
            footer-hide
            :width="width"
            :mask-closable="false"
    >
        <Form
                :model="formData"
                :label-position="labelPosition"
                ref="formData"
                :label-width="labelWidth"
                :rules="rules"
                :disabled="disabled"
                style="padding:0 40px;"
        >
            <FormItem v-for="schema in formSchema" :key="schema.field"
                      :schema="schema"
                      :label="schema.label"
                      :prop="schema.field"
                      :required="schema.required"
                      :rules="schema.rules">
                <component :is="schema.component" v-bind="bindPorp(schema)" v-model="formData[schema.field]"></component>
            </FormItem>

            <FormItem>
                <Button @click="cancel" style="margin-left: 8px">取消</Button>
                <Button type="primary" @click="submitformData('formData')" :loading="submitLoading">确定</Button>
            </FormItem>
        </Form>
    </Modal>
</template>
#[[
<script>
    import { formSchema } from '../data'
    import { api } from '../api'

    export default {
        props: {
            addTitle: { default: '编辑' },
            width: { default: 800 },
            labelPosition: { default: 'right' },
            labelWidth: { default: 100 },
            disabled: { default: false },
            rules: {
                typeof: Object,
                default () {
                    return {}
                }
            },
        },
        data() {
            return {
                showModalForm: false,
                submitLoading: false,
                formSchema,
                formData: {},
            };
        },
        computed: { },
        mounted() { },
        created() { },
        methods: {
            bindPorp (schema) {
                return Object.assign({
                    placeholder: `请输入` + schema.label
                }, schema.componentProps)
            },
            handleSave (row) {
                this.showModalForm = true
                if(row && Object.keys(row).length > 1){
                    this.formData['id'] = row['id']
                    formSchema.forEach(f => {
                        this.formData[f.field] = row[f.field]
                    })
                }else{
                    this.formData = {}
                }
            },
            cancel () {},
            submitformData (name) {
                this.logining = true;
                this.$refs[name].validate(valid => {
                    if (valid) {
                        if (this.submitLoading) {
                            return
                        }
                        this.submitLoading = true
                        api.saveOrUpdate(this.formData).then(resp => {
                            this.submitLoading = false
                            if (resp.data.success) {
                                this.showModalForm = false
                                this.$emit('success', data)
                                this.$Message.success(resp.data.message || '提交成功');
                            }else{
                                this.$Message.error(resp.data.message || '提交失败');
                            }
                        }).catch(() => {
                            this.submitLoading = false
                        }).finally(() => {
                            this.$refs.formData.resetFields()
                        });
                    }
                })
            }
        },
    };
</script>
]]#