#set($excludeColumnsField = ['id', 'creator', 'create_time', 'delete_state', 'tenant_id'])
#set($excludeSearchField = ['id', 'creator', 'create_time', 'modifier', 'modify_time', 'delete_state', 'tenant_id'])
#set($excludeFormField = ['creator', 'create_time', 'modifier', 'modify_time', 'delete_state', 'tenant_id'])
//列表数据
export const columns = [
    {
        title: 'ID',
        key: 'id',
        type: 'selection'
    },
#foreach($field in ${table.fields})
    #if(!$excludeColumnsField.contains($field.name))
    {
        title: '${field.comment}',
        key: '${field.propertyName}',
        minWidth: 120,
        resizable: true,
        #if($!{field.columnType} == 'STRING')
        ellipsis: true,
        tooltip: true,
        #end
    },
    #end
#end
    {
        title: '操作',
        width: 105,
        slot: 'action',
        fixed: 'right'
    }
]

//查询数据
export const searchFormSchema = [
#foreach($field in ${table.fields})
    #if(!$excludeSearchField.contains($field.name))
    {
        label: '${field.comment}',
        field: '${field.propertyName}',
        span: 6,
    },
    #end
#end
]

//表单数据
export const formSchema = [
#foreach($field in ${table.fields})
    #if(!$excludeFormField.contains($field.name))
    {
        label: '${field.comment}',
        field: '${field.propertyName}',
        // required: true,
        rules: [],
        component: 'Input',
        componentProps: {
            clearable: true
        }
    },
    #end
#end
]