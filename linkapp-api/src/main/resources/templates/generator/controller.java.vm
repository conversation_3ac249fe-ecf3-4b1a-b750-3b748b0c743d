package ${package.Controller};
#set($entity = ${entity})
#set($lowerCaseEntity = $entity.substring(0,1).toLowerCase() + $entity.substring(1))

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.easylinkin.linkappapi.common.model.RequestModel;
import ${package.Entity}.${entity};
import ${package.Service}.${table.serviceName};
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;
#if(${superControllerClassPackage})
import ${superControllerClassPackage};
#end

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;


/**
 * $!{table.comment} 前端控制器
 *
 * <AUTHOR>
 * @since ${date}
 */
@Api(tags = "$!{table.comment}")
@RestController
@RequestMapping("/${package.ModuleName}/${table.entityPath}")
public class ${table.controllerName} {
    @Autowired
    private ${table.serviceName} ${lowerCaseEntity}Service;

    /**
     * 分页列表查询
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value="分页列表查询", notes="分页列表查询")
    @PostMapping(value = "/listPage")
    public RestMessage queryPageList(@RequestBody RequestModel<${entity}> requestModel) {
        // Assert.notNull(requestModel,"参数不能为空");
        // Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        if(CollUtil.isNotEmpty(requestModel.getSorts())){
            requestModel.getSorts().forEach(s -> {
                requestModel.getPage().getOrders().add(new OrderItem().setColumn(StrUtil.toUnderlineCase(s.getField())).setAsc("ASC".equalsIgnoreCase(s.getSortRule())));
            });
        }else{
            requestModel.getPage().getOrders().add(new OrderItem().setColumn("id").setAsc(false));
        }
        IPage<${entity}> pageList = ${lowerCaseEntity}Service.page(requestModel.getPage(), Wrappers.lambdaQuery(requestModel.getCustomQueryParams()));
        return RestBuilders.successBuilder().data(pageList).build();
    }

    /**
     * 保存
     *
     * @param ${lowerCaseEntity}
     * @return
     */
    @ApiOperation(value="保存", notes="保存")
    @PostMapping(value = "/save")
    public RestMessage save(@RequestBody ${entity} ${lowerCaseEntity}) {
        if(${lowerCaseEntity}.getId() != null){
            ${lowerCaseEntity}Service.updateById(${lowerCaseEntity});
        } else{
            ${lowerCaseEntity}Service.save(${lowerCaseEntity});
        }
        return RestBuilders.successBuilder().message("保存成功" ).build();
    }

    /**
     *   通过id删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value="通过id删除", notes="通过id删除")
    @DeleteMapping(value = "/delete")
    public RestMessage delete(@RequestParam("id") Integer id) {
        ${lowerCaseEntity}Service.removeById(id);
        return RestBuilders.successBuilder().message("删除成功").build();
    }

    /**
     *  批量删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value="批量删除", notes="批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public RestMessage deleteBatch(@RequestParam("ids") String ids) {
        this.${lowerCaseEntity}Service.removeByIds(Arrays.asList(ids.split(",")));
        return RestBuilders.successBuilder().message("批量删除成功").build();
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value="通过id查询", notes="通过id查询")
    @GetMapping(value = "/queryById")
    public RestMessage queryById(@RequestParam("id") Integer id) {
        ${entity} ${lowerCaseEntity} = ${lowerCaseEntity}Service.getById(id);
        if(${lowerCaseEntity} == null) {
            return RestBuilders.errorBuilder().message("未找到对应数据").build();
        }
        return RestBuilders.successBuilder().data(${lowerCaseEntity}).build();
    }
}
