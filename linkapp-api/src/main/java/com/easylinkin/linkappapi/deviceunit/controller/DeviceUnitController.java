package com.easylinkin.linkappapi.deviceunit.controller;


import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.application.service.ApplicationService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit;
import com.easylinkin.linkappapi.deviceunit.service.DeviceUnitService;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;

import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@RestController
@Api(value="设备型号",tags={"设备型号"})
@RequestMapping("/deviceUnit")
public class DeviceUnitController {

    @Resource
    private DeviceUnitService deviceUnitService;
    
    @Resource
    ApplicationService applicationService;
    
    @Resource
    LinkappUserContextProducer linkappUserContextProducer;

    @ApiOperation("根据查询条件获取设备型号列表")
    @PostMapping("getAll")
    public RestMessage getAll(@RequestBody DeviceUnit deviceUnit) {
        return RestBuilders.successBuilder(deviceUnitService.getAll(deviceUnit)).build();
    }

    @ApiOperation("根据设备类型ID获取设备型号")
    @PostMapping("selectApplicationDeviceUnitByDeviceType")
    public RestMessage selectApplicationDeviceUnitByDeviceType(@RequestBody DeviceUnit deviceUnit) {
    	Long userId = linkappUserContextProducer.getCurrent().getId();
    	List<DeviceUnit> deviceUnitList = applicationService.selectApplicationDeviceUnitByUser(userId.toString());
    	if(!deviceUnitList.isEmpty()) {
    		List<String> deviceUnitIds = new ArrayList<>();
    		for(DeviceUnit du: deviceUnitList) {
    			deviceUnitIds.add(du.getId());
        	}
    		deviceUnit.setDeviceUnitIds(deviceUnitIds);
    	}
        return RestBuilders.successBuilder(deviceUnitService.selectApplicationDeviceUnitByDeviceType(deviceUnit)).build();
    }

    @ApiOperation("根据用户获取设备型号，查询传参未使用")
    @PostMapping("selectApplicationDeviceUnitByUser")
    public RestMessage selectApplicationDeviceUnitByUser(@RequestBody DeviceUnit deviceUnit) {
    	Long userId = linkappUserContextProducer.getCurrent().getId();
        return RestBuilders.successBuilder(applicationService.selectApplicationDeviceUnitByUser(userId.toString())).build();
    }

    @ApiOperation("根据条件查询设备型号数据")
    @PostMapping("selectDeviceUnit")
    public RestMessage selectDeviceUnit(@RequestBody DeviceUnit deviceUnit) {
        return RestBuilders.successBuilder(deviceUnitService.selectDeviceUnit(deviceUnit)).build();
    }

    @ApiOperation("分页获取设备最新型号数据")
    @PostMapping("/selectPage")
	public RestMessage selectPage(@RequestBody RequestModel<DeviceUnit> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DeviceUnit> record = deviceUnitService.selectDeviceUnitsPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    @ApiOperation("根据条件查询设备型号数据")
    @PostMapping("getDeviceUnitWithAttrs")
    public RestMessage getDeviceUnitWithAttrs(@RequestBody DeviceUnit deviceUnit) {
        return RestBuilders.successBuilder(deviceUnitService.getDeviceUnitWithAttrs(deviceUnit)).build();
    }

    @ApiOperation("根据型号编码获取设备型号版本号列表")
    @RequestMapping("/getDeviceUnitVersionList")
    public RestMessage getDeviceUnitVersionList(@RequestBody DeviceUnit deviceUnit) {
        return RestBuilders.successBuilder(deviceUnitService.selectDeviceUnitVersionList(deviceUnit.getCode())).build();
    }

    @ApiOperation("根据设备型号ID修改设备型号数据")
    @PostMapping("/update")
    public RestMessage update(@RequestBody DeviceUnit deviceUnit) {
        Assert.notNull(deviceUnit , "设备型号为空");
        Assert.notNull(deviceUnit.getId() , "设备型号ID为空");
        return RestBuilders.successBuilder().data(deviceUnitService.updateById(deviceUnit)).build();
    }
}
