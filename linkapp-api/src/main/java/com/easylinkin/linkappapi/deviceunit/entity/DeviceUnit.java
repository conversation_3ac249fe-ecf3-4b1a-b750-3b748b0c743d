package com.easylinkin.linkappapi.deviceunit.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute;
import com.easylinkin.linkappapi.deviceservice.entity.DeviceServices;
import com.easylinkin.linkappapi.devicetype.entity.DeviceType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("设备型号实体类")
@TableName("linkapp_device_unit")
public class DeviceUnit extends Model<DeviceUnit> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty("公司ID")
    @TableField("company_id")
    private String companyId;

    /**
     * 名称
     */
    @ApiModelProperty("类型名称")
    @TableField("name")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty("设备类型编码")
    @TableField("code")
    private String code;


    /**
     * 设备类型id
     */
    @ApiModelProperty("设备类型ID")
    @TableField("device_type_id")
    private String deviceTypeId;

    /**
     * 设备类型名称
     */
    @ApiModelProperty("设备类型名称")
    @TableField("device_type_name")
    private String deviceTypeName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;

    /**
     * 删除标识
     */
    @ApiModelProperty(value = "删除标识", required = false, example = "1")
    @TableField("delete_state")
    private Integer deleteState;

    /**
     * 图标
     */
    @ApiModelProperty("图标")
    @TableField("icon")
    private String icon;

    @ApiModelProperty("")
    @TableField("identification")
    private String identification;

    /**
     * 设备寿命（月）
     */
    @ApiModelProperty(value = "设备寿命", required = false, example = "1")
    @TableField("device_life")
    private Float deviceLife;

    /**
     * 可视化配置
     */
    @ApiModelProperty("可视化配置")
    @TableField("visualization_config")
    private String visualizationConfig;
    
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @TableField("version")
    private String version;
    
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @TableField("physics_model")
    private String physicsModel;

    /**
     * 检修周期（天）
     */
    @ApiModelProperty(value = "检修周期", required = false, example = "0")
    @TableField("repair_cycle")
    private Float repairCycle;

    /**
     * 设备离线时间（小时）
     */
    @ApiModelProperty(value = "离线时间", required = false, example = "0")
    @TableField("offline_time")
    private Float offlineTime;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty("创建人")
    @TableField("creator")
    private String creator;

    @ApiModelProperty("修改人")
    @TableField("modifier")
    private String modifier;

    @ApiModelProperty("修改时间")
    @TableField("modify_time")
    private Date modifyTime;

    @ApiModelProperty("设备型号ID")
    @TableField(exist = false)
    private List<String> deviceUnitIds;

    @ApiModelProperty("设备类型")
    @TableField(exist = false)
    private DeviceType deviceType;

    @ApiModelProperty("设备属性")
    @TableField(exist = false)
    private List<DeviceAttribute> attributeList;

    @ApiModelProperty("设备服务")
    @TableField(exist = false)
    private List<DeviceServices> deviceServiceList;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
