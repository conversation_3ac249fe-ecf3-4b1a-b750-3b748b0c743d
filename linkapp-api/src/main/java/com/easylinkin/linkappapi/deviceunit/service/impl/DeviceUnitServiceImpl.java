package com.easylinkin.linkappapi.deviceunit.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import java.util.List;

import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit;
import com.easylinkin.linkappapi.deviceunit.mapper.DeviceUnitMapper;
import com.easylinkin.linkappapi.deviceunit.service.DeviceUnitService;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
public class DeviceUnitServiceImpl extends ServiceImpl<DeviceUnitMapper, DeviceUnit> implements DeviceUnitService {

    @Override
    public List<DeviceUnit> getAll(DeviceUnit deviceUnit) {
        QueryWrapper qw = new QueryWrapper();
        if (StringUtils.isNotEmpty(deviceUnit.getId())) {
            qw.eq("id", deviceUnit.getId());
        }
        if (StringUtils.isNotEmpty(deviceUnit.getCode())) {
            qw.eq("code", deviceUnit.getCode());
        }
        if (StringUtils.isNotEmpty(deviceUnit.getVersion())) {
            qw.eq("version", deviceUnit.getVersion());
        }
        if (StringUtils.isNotEmpty(deviceUnit.getName())) {
            qw.eq("name", deviceUnit.getName());
        }
        if (StringUtils.isNotEmpty(deviceUnit.getDeviceTypeId())) {
            qw.eq("device_type_id", deviceUnit.getDeviceTypeId());
        }
        if (StringUtils.isNotEmpty(deviceUnit.getCreator())) {
            qw.eq("creator", deviceUnit.getCreator());
        }

        qw.orderByAsc("code");
        return baseMapper.selectList(qw);
    }

    @Override
	public List<DeviceUnit> selectApplicationDeviceUnitByDeviceType(DeviceUnit deviceUnit) {
		return baseMapper.selectApplicationDeviceUnitByDeviceType(deviceUnit);
	}

    @Override
    public String getIdByCode(String value) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("code", value);
        queryWrapper.select("id");
        List<DeviceUnit> deviceUnits = baseMapper.selectList(queryWrapper);
        if (deviceUnits.isEmpty()) {
            throw new BusinessException("由编号" + value + "找不到设备型号");
        }
        if (deviceUnits.size() > 1) {
            throw new BusinessException("由编号" + value + "找到多个型号");
        }
        return deviceUnits.get(0).getId();
    }

    @Override
    public List<DeviceUnit> getDeviceUnitWithAttrs(DeviceUnit deviceUnit) {
        return baseMapper.getDeviceUnitWithAttrs(deviceUnit);
    }

    @Override
    public IPage<DeviceUnit> selectDeviceUnitsPage(Page page, DeviceUnit deviceUnit) {
        return baseMapper.selectDeviceUnitsLatestVersion(page, deviceUnit);
    }

	@Override
	public DeviceUnit selectDeviceUnit(DeviceUnit deviceUnit) {
		return baseMapper.selectDeviceUnit(deviceUnit);
	}

	@Override
	public List<String> selectDeviceUnitVersionList(String code) {
		return baseMapper.selectDeviceUnitVersionList(code);
	}

    @Override
    public String selectPhysicsModelByDeviceCode(String code) {
        return baseMapper.selectPhysicsModelByDeviceCode(code);
    }

    @Override
    public List<String> selectDeviceUnitByDeviceTypeNames(List<String> deviceTypeNames) {
        if(CollectionUtil.isEmpty(deviceTypeNames)){
            return null;
        }
        List<DeviceUnit> deviceUnitList = this.selectByDeviceTypeNames(deviceTypeNames);
        if(CollectionUtil.isNotEmpty(deviceUnitList)){
            List<String> deviceUnitCodes = deviceUnitList.stream().map(DeviceUnit::getCode).distinct().collect(Collectors.toList());
            return deviceUnitCodes;
        }
        return null;
    }

    @Override
    public List<DeviceUnit> selectByDeviceTypeNames(List<String> deviceTypeNames) {
        if(CollectionUtil.isEmpty(deviceTypeNames)){
            return null;
        }
        List<DeviceUnit> deviceUnitList = baseMapper.selectDeviceUnitByTypeNames(deviceTypeNames);
        if(CollectionUtil.isNotEmpty(deviceUnitList)){
            return deviceUnitList;
        }
        return null;
    }
}
