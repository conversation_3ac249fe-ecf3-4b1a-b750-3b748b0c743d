package com.easylinkin.linkappapi.deviceunit.service;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface DeviceUnitService extends IService<DeviceUnit> {

    List<DeviceUnit> getAll(DeviceUnit deviceUnit);

    List<DeviceUnit> selectApplicationDeviceUnitByDeviceType(DeviceUnit deviceUnit);

    /**
     * 分页查询用户列表
     */
    IPage<DeviceUnit> selectDeviceUnitsPage(Page page,DeviceUnit deviceUnit);

    DeviceUnit selectDeviceUnit(DeviceUnit deviceUnit);

    /**
     * 根据编码查找型号id
     */
    String getIdByCode(String value);

    List<DeviceUnit> getDeviceUnitWithAttrs(DeviceUnit deviceUnit);

    List<String> selectDeviceUnitVersionList(String code);

    /**
     * 通过设备编号查询对应的物模型信息
     * */
    String selectPhysicsModelByDeviceCode(String code);

    /**
     * 通过设备类型名称列表查询所有设备型号
     * @param deviceTypeNames
     * @return
     */
    List<String> selectDeviceUnitByDeviceTypeNames(List<String> deviceTypeNames);

    List<DeviceUnit> selectByDeviceTypeNames(List<String> deviceTypeNames);
}
