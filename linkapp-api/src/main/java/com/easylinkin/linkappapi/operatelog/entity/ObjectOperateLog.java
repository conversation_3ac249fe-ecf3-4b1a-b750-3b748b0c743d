package com.easylinkin.linkappapi.operatelog.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-07
 */
@Data
@TableName("linkapp_obj_operate_log")
public class ObjectOperateLog extends Model<ObjectOperateLog> {

    private static final long serialVersionUID = 1L;

    @TableId
    private String logId;

    @TableField("object_id")
    private String objectId;

    @TableField("object_key")
    private String objectKey;

    /**
     * 操作类型
     */
    @TableField("operate_desc")
    private String operateDesc;

    private Date createTime;

    private String creator;

    private String url;

    private String content;

    private String failInformation;

    private String ip;

    private String moduleName;

    private String tenantId;

    private Boolean status;

    @TableField(exist = false)
    String queryTimeStart;

    @TableField(exist = false)
    String queryTimeEnd;

    @TableField("creator_name")
    private String creatorName;

    public ObjectOperateLog() {
        super();
    }

    public ObjectOperateLog(String moduleName, String operateDesc) {
        super();
        this.moduleName = moduleName;
        this.operateDesc = operateDesc;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    @Override
    protected Serializable pkVal() {
        return this.logId;
    }

}
