package com.easylinkin.linkappapi.operatelog.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("operate_log")
public class OperateLog extends Model<OperateLog> {

    private static final long serialVersionUID = 1L;

    @TableId
    private String logId;

    private Date createTime;

    private String creator;

    private String url;

    private String content;

    private String failInformation;

    private String ip;

    private String moduleName;

    private Boolean status;

    @TableField(exist = false)
    String queryTimeStart;

    @TableField(exist = false)
    String queryTimeEnd;


    @Override
    protected Serializable pkVal() {
        return this.logId;
    }

}
