package com.easylinkin.linkappapi.operatelog.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 访问菜单日志
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_visit_privilege_log")
public class VisitPrivilegeLog extends Model<VisitPrivilegeLog> {

  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  @TableId(value = "id_", type = IdType.AUTO)
  private Integer id;

  /**
   * 菜单名称
   */
  @TableField("name_")
  private String name;

  /**
   * 菜单code
   */
  @TableField("privilege_code_")
  private String privilegeCode;

  /**
   * 菜单前端地址
   */
  @TableField("url_")
  private String url;

  /**
   * 租户id
   */
  @TableField("tenant_id_")
  private String tenantId;

  /**
   * 创建时间
   */
  @TableField("create_time_")
  private Date createTime;

  /**
   * 创建人id
   */
  @TableField("creator_id_")
  private Long creatorId;


  @Override
  protected Serializable pkVal() {
    return this.id;
  }

}
