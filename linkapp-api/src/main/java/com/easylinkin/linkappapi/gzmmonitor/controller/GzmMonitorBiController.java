package com.easylinkin.linkappapi.gzmmonitor.controller;

import com.easylinkin.linkappapi.gzmmonitor.service.GzmMonitorItemConfigService;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmAlarmDayQueryVO;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmAlarmVO;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmMonitorOverviewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 系统管理/大屏配置/高支模监测大屏接口
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@RestController
@RequestMapping("/gzmmonitor/gzmMonitorConfig")
@Api(tags = "高支模监测大屏接口")
public class GzmMonitorBiController {


  @Resource
  private GzmMonitorItemConfigService gzmMonitorItemConfigService;

  /**
   * 监测概况
   *
   * @return
   */
  @PostMapping("getGzmMonitorOverview")
  @ApiOperation("监测概况")
  public RestMessage getGzmMonitorOverview() {
    GzmMonitorOverviewVO overview = gzmMonitorItemConfigService.getGzmMonitorOverview();
    return RestBuilders.successBuilder(overview).build();
  }

  /**
   * 告警类型统计
   *
   * @return
   */
  @PostMapping("getGzmAlarmTypeStatistics")
  @ApiOperation("告警类型统计")
  public RestMessage getGzmAlarmTypeStatistics() {
    List<GzmAlarmVO> record = gzmMonitorItemConfigService.getGzmAlarmTypeStatistics();
    return RestBuilders.successBuilder(record).build();
  }

  /**
   * 告警规则趋势
   *
   * @param queryVO
   * @return
   */
  @PostMapping("/alarmTrend")
  @ApiOperation("告警规则趋势")
  public RestMessage alarmTrend(@RequestBody GzmAlarmDayQueryVO queryVO) {
    List<GzmAlarmVO> alarmTrendVOList = gzmMonitorItemConfigService.alarmTrend(queryVO);
    return RestBuilders.successBuilder(alarmTrendVOList).build();
  }

  
}
