package com.easylinkin.linkappapi.gzmmonitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 高支模监测点位表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_gzm_monitor_item_config")
public class GzmMonitorItemConfig extends Model<GzmMonitorItemConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备id
     */
    @TableField("device_code")
    private String deviceCode;

    /**
     * 租户id
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * x坐标值
     */
    @TableField("x_axis")
    private Double xaxis;

    /**
     * y轴坐标值
     */
    @TableField("y_axis")
    private Double yaxis;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 是否删除，0删除，1存在
     */
    @TableField("delete_state")
    private Integer deleteState;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
