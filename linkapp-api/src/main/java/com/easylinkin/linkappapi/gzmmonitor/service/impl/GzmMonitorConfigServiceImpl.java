package com.easylinkin.linkappapi.gzmmonitor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.service.CommonService;
import com.easylinkin.linkappapi.gzmmonitor.entity.GzmMonitorConfig;
import com.easylinkin.linkappapi.gzmmonitor.mapper.GzmMonitorConfigMapper;
import com.easylinkin.linkappapi.gzmmonitor.service.GzmMonitorConfigService;
import com.easylinkin.linkappapi.jkmonior.entity.JkMonitorConfig;
import com.easylinkin.linkappapi.machinery.entity.TowerCranePositionImage;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 高支模监测配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Service
public class GzmMonitorConfigServiceImpl extends ServiceImpl<GzmMonitorConfigMapper, GzmMonitorConfig> implements
    GzmMonitorConfigService {

  @Resource
  private CommonService commonService;
  @Resource
  private LinkappUserContextProducer linkappUserContextProducer;

  @Override
  public RestMessage saveConfig(GzmMonitorConfig gzmMonitorConfig) {
    LinkappUser linkappUser = linkappUserContextProducer.getNotNullCurrent();
    String tenantId = linkappUser.getTenantId();
    gzmMonitorConfig.setTenantId(tenantId);

    //再次查询
    LambdaQueryWrapper<GzmMonitorConfig> qw = new LambdaQueryWrapper();
    qw.eq(GzmMonitorConfig::getTenantId, tenantId)
        .eq(GzmMonitorConfig::getDeleteState, 1);
    List<GzmMonitorConfig> list = baseMapper.selectList(qw);
    if (CollectionUtil.isNotEmpty(list)) {
      GzmMonitorConfig old = list.get(0);
      Integer id = old.getId();
      gzmMonitorConfig.setId(null);
      BeanUtil.copyProperties(gzmMonitorConfig, old, CopyOptions.create().setIgnoreNullValue(false));
      old.setId(id);
      old.setModifier(linkappUser.getId().toString());
      old.setModifyTime(DateUtil.date());
      baseMapper.updateById(old);
    } else {
      //为空保存
      gzmMonitorConfig.setCreator(linkappUser.getId().toString());
      gzmMonitorConfig.setCreateTime(DateUtil.date());
      gzmMonitorConfig.setModifier(linkappUser.getId().toString());
      gzmMonitorConfig.setModifyTime(DateUtil.date());
      gzmMonitorConfig.setDeleteState(1);
      baseMapper.insert(gzmMonitorConfig);
    }

    return RestBuilders.successBuilder().build();
  }

  @Override
  public GzmMonitorConfig getConfig() {
    String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
    //从数据库取
    LambdaQueryWrapper<GzmMonitorConfig> qw = new LambdaQueryWrapper();
    qw.eq(GzmMonitorConfig::getTenantId, tenantId)
        .eq(GzmMonitorConfig::getDeleteState, 1);
    List<GzmMonitorConfig> configs = baseMapper
        .selectList(qw);
    if (configs.size() > 0) {
      return configs.get(0);
    }
    return null;
  }
}
