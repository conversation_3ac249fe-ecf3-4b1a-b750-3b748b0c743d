package com.easylinkin.linkappapi.gzmmonitor.controller;


import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.gzmmonitor.entity.GzmMonitorItemConfig;
import com.easylinkin.linkappapi.gzmmonitor.service.GzmMonitorItemConfigService;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmMonitorDeviceUnitVO;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmMonitorItemConfigVO;
import com.easylinkin.linkappapi.machinery.vo.TowerCranePositionVO;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 系统管理/大屏配置/高支模监测
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@RestController
@RequestMapping("/gzmmonitor/gzmMonitorItemConfig")
public class GzmMonitorItemConfigController {

  @Resource
  private GzmMonitorItemConfigService gzmMonitorItemConfigService;


  @PostMapping
  @ApiOperation("保存高支模监测点位")
  @CommonOperateLogAnnotate(module = LogModule.DASHBOARD_SETTING, desc = "保存高支模监测点位")
  public RestMessage saveAll(@RequestBody List<GzmMonitorItemConfig> items) {
    return RestBuilders.successBuilder()
        .success((this.gzmMonitorItemConfigService.saveAll(items))).build();
  }

  /**
   * 查询所有数据
   *
   * @return 所有数据
   */
  @PostMapping("getList")
  @ApiOperation("查询所有数据")
  public RestMessage getList() {
    List<GzmMonitorItemConfigVO> record = gzmMonitorItemConfigService.selectAll();
    return RestBuilders.successBuilder(record).build();
  }

  /**
   * 查询高支模监测点列表-按照型号分组
   *
   * @return
   */
  @PostMapping("getDeviceListGroupByDeviceType")
  @ApiOperation("查询高支模监测点列表-按照型号分组")
  public RestMessage getDeviceListGroupByDeviceType() {
    List<GzmMonitorDeviceUnitVO> record = gzmMonitorItemConfigService.getDeviceListGroupByDeviceType();
    return RestBuilders.successBuilder(record).build();
  }
}
