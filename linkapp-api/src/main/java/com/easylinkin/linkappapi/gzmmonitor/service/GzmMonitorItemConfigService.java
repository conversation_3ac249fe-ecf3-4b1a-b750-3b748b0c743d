package com.easylinkin.linkappapi.gzmmonitor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.gzmmonitor.entity.GzmMonitorItemConfig;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmAlarmDayQueryVO;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmAlarmVO;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmMonitorDeviceUnitVO;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmMonitorItemConfigVO;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmMonitorOverviewVO;
import java.util.List;

/**
 * <p>
 * 高支模监测点位表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface GzmMonitorItemConfigService extends IService<GzmMonitorItemConfig> {

  List<GzmMonitorItemConfigVO> selectAll();

  boolean saveAll(List<GzmMonitorItemConfig> items);

  List<GzmMonitorDeviceUnitVO> getDeviceListGroupByDeviceType();

  GzmMonitorOverviewVO getGzmMonitorOverview();

  List<GzmAlarmVO> getGzmAlarmTypeStatistics();

  List<GzmAlarmVO> alarmTrend(GzmAlarmDayQueryVO queryVO);
}
