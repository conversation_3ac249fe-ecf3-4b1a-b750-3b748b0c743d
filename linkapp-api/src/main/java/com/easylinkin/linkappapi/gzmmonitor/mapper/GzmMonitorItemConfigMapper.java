package com.easylinkin.linkappapi.gzmmonitor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.gzmmonitor.entity.GzmMonitorItemConfig;
import com.easylinkin.linkappapi.gzmmonitor.vo.GzmMonitorItemConfigVO;
import java.util.List;

/**
 * <p>
 * 高支模监测点位表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface GzmMonitorItemConfigMapper extends BaseMapper<GzmMonitorItemConfig> {

  List<GzmMonitorItemConfigVO> selectAllByTenantId(String tenantId);
}
