package com.easylinkin.linkappapi.gzmmonitor.controller;


import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.gzmmonitor.entity.GzmMonitorConfig;
import com.easylinkin.linkappapi.gzmmonitor.service.GzmMonitorConfigService;
import com.easylinkin.linkappapi.jkmonior.entity.vo.JkMonitorConfigVo;
import com.easylinkin.linkappapi.machinery.entity.TowerCranePositionImage;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 系统管理/大屏配置/高支模监测
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@RestController
@RequestMapping("/gzmmonitor/gzmMonitorConfig")
@Api(tags = "高支模监测")
public class GzmMonitorConfigController {

  @Resource
  private GzmMonitorConfigService gzmMonitorConfigService;

  @PostMapping("/saveConfig")
  @ApiOperation("保存项目高支模监测配置")
  @CommonOperateLogAnnotate(module = LogConstant.LogModule.DASHBOARD_SETTING, desc = "保存项目高支模监测配置")
  public RestMessage saveConfig(@RequestBody GzmMonitorConfig gzmMonitorConfig) {
    return gzmMonitorConfigService.saveConfig(gzmMonitorConfig);
  }

  @PostMapping("getConfig")
  @ApiOperation("获取高支模监测配置")
  public RestMessage getConfig() {
    GzmMonitorConfig config = gzmMonitorConfigService.getConfig();
    return RestBuilders.successBuilder().data(config).build();
  }

}
