package com.easylinkin.linkappapi.trainingpenetrate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.easylinkin.linkappapi.common.translate.CodeI18n;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/** 人员培训考核记录表
 * <AUTHOR>
 * @date 2025/7/1 下午 5:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_linkapp_safety_edu_detail")
public class RailLinkappSafetyEduDetail {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 花名册人员id
     */
    @TableField(value = "person_id")
    private String personId;

    /**
     * 花名册人员姓名
     */
    @TableField(value = "person_name")
    private String personName;

    /**
     * 花名册人员手机号
     */
    @TableField(value = "person_phone")
    private String personPhone;

    /**
     * 花名册人员在岗位情况
     */
    @TableField(value = "person_status")
    private Integer personStatus;

    /**
     * 考核分数
     */
    @TableField(value = "kh_number")
    private Integer khNumber;
    /**
     * 是否合格 0合格，1不合格
     */
    @TableField(value = "is_qual")
    private Integer isQual;
    /**
     * 提交时间/更新时间
     */
    @TableField(value = "modify_time")
    private Date modifyTime;
    /**
     * 培训记录id
     */
    @TableField(value = "edu_id")
    private String eduId;
    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;
    /**
     * 岗位/工种
     */
    @TableField(value = "job_type")
    private String jobType;


}
