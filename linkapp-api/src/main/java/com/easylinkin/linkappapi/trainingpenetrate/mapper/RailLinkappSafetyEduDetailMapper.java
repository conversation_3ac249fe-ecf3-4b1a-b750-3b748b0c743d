package com.easylinkin.linkappapi.trainingpenetrate.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduDetailDTO;
import com.easylinkin.linkappapi.trainingpenetrate.entity.RailLinkappSafetyEduDetail;
import com.easylinkin.linkappapi.trainingpenetrate.vo.EduDetailStatVO;
import com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RailLinkappSafetyEduDetailMapper extends  BaseMapper<RailLinkappSafetyEduDetail>{
    IPage<RailLinkappSafetyEduDetailVO> selectPage(Page<RailLinkappSafetyEduDetail> page, @Param("entity") RailLinkappSafetyEduDetailDTO railLinkappSafetyEduPlanDTO);

    List<RailLinkappSafetyEduDetailVO> selectPage(@Param("entity") RailLinkappSafetyEduDetailDTO railLinkappSafetyEduPlanDTO);

    IPage<EduDetailStatVO> selectPersonnelStatList(Page<RailLinkappSafetyEduDetail> page, @Param("entity") RailLinkappSafetyEduDetailDTO railLinkappSafetyEduPlanDTO);

    List<EduDetailStatVO> selectPersonnelStatList(@Param("entity") RailLinkappSafetyEduDetailDTO railLinkappSafetyEduPlanDTO);





}
