package com.easylinkin.linkappapi.trainingpenetrate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.easylinkin.linkappapi.common.translate.Code2Text;
import com.easylinkin.linkappapi.common.translate.CodeI18n;
import com.easylinkin.linkappapi.common.translate.impl.DictTranslateor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**培训穿透 培训信息
 * <AUTHOR>
 * @date 2025/7/1 下午 4:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_linkapp_safety_edu_plan")
@CodeI18n
public class RailLinkappSafetyEduPlan  extends Model<RailLinkappSafetyEduPlan>{

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 培训名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * 培训日期
     */
    @TableField(value = "edu_date")
    private String eduDate;

    /**
     * 培训类型 字典 training_type
     */
    @TableField(value = "training_type")
    @Code2Text(translateor = DictTranslateor.class, value = "training_type")
    private String trainingType;
    /**
     * 培训场地
     */
    @TableField(value = "training_site")
    private String trainingSite;
    /**
     * 培训内容
     */
    @TableField(value = "content")
    private String content;
    /**
     * 培训负责人id 花名册id
     */
    @TableField(value = "curator_id")
    private String curatorId;
    /**
     * 负责人姓名 花名册姓名
     */
    @TableField(value = "curator_name")
    private String curatorName;
    /**
     * 备注
     */
    @TableField(value = "notes")
    private String notes;
    /**
     * 图片地址
     */
    @TableField(value = "imgs")
    private String imgs;
    /**
     * 附件地址
     */
    @TableField(value = "files")
    private String files;
    /**
     * 及格标准
     */
    @TableField(value = "pass_number")
    private Integer passNumber;
    /**
     * 培训网格id
     */
    @TableField(value = "grid_id")
    private String gridId;
    /**
     * 培训网格名称
     */
    @TableField(value = "grid_name")
    private String gridName;
    /**
     * 参与人数
     */
    @TableField(value = "complete_number")
    private Integer completeNumber;
    /**
     * 通过人数
     */
    @TableField(value = "pass_through_number")
    private Integer passThroughNumber;

    /**
     * 修改人/更新人
     */
    @TableField(value = "modifier")
    private String modifier;
    /**
     * 创建人/提交人
     */
    @TableField(value = "creator")
    private String creator;
    /**
     * 修改时间/更新时间
     */
    @TableField(value = "modify_time")
    private Date modifyTime;
    /**
     * 创建时间/提交时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;

}
