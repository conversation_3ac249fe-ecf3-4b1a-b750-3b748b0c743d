package com.easylinkin.linkappapi.trainingpenetrate.dto;

import com.easylinkin.linkappapi.trainingpenetrate.entity.RailLinkappSafetyEduDetail;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/1 下午 6:56
 */
@Data
public class RailLinkappSafetyEduDetailDTO extends RailLinkappSafetyEduDetail {

    private String rosterType;
    /**
     * 排序规则
     * joinRainCount负责培训次数，examCount参与培训次数
     */
    private String  orderField;
    /**
     * 排序规则
     * ASC 或 DESC，控制升序/降序。
     */
    private String  orderType;
}
