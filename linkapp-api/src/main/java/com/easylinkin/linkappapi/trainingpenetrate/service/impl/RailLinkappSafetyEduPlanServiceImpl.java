package com.easylinkin.linkappapi.trainingpenetrate.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.service.CommonService;
import com.easylinkin.linkappapi.config.entity.SysDictItem;
import com.easylinkin.linkappapi.config.service.SysDictItemService;
import com.easylinkin.linkappapi.config.service.SysDictService;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappGridManagementInfo;
import com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappGridManagementInfoMapper;
import com.easylinkin.linkappapi.roster.entity.RailLinkappRosterPersonnel;
import com.easylinkin.linkappapi.roster.mapper.LinkappRosterPersonnelMapper;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduDetailDTO;
import com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduPlanDTO;
import com.easylinkin.linkappapi.trainingpenetrate.entity.RailLinkappSafetyEduDetail;
import com.easylinkin.linkappapi.trainingpenetrate.entity.RailLinkappSafetyEduPlan;
import com.easylinkin.linkappapi.trainingpenetrate.mapper.RailLinkappSafetyEduDetailMapper;
import com.easylinkin.linkappapi.trainingpenetrate.mapper.RailLinkappSafetyEduPlanMapper;
import com.easylinkin.linkappapi.trainingpenetrate.service.IRailLinkappSafetyEduPlanService;
import com.easylinkin.linkappapi.trainingpenetrate.vo.EduDetailStatVO;
import com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduDetailVO;
import com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduPlanVO;
import com.easylinkin.sm.entity.Log;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/7/1 下午 4:55
 */
@Service
public class RailLinkappSafetyEduPlanServiceImpl extends ServiceImpl<RailLinkappSafetyEduPlanMapper, RailLinkappSafetyEduPlan> implements IRailLinkappSafetyEduPlanService {
    @Autowired
    private LinkappUserContextProducer linkappUserContextProducer;

    @Autowired
    private LinkappRosterPersonnelMapper linkappRosterPersonnelMapper;

    @Autowired
    private RailLinkappGridManagementInfoMapper gridManagementInfoMapper;
    @Resource
    private CommonService commonService;
    @Autowired
    private RailLinkappSafetyEduDetailMapper safetyEduDetailMapper;

    @Autowired
    private SysDictItemService sysDictItemService;





    @Override
    public IPage<RailLinkappSafetyEduPlanVO> selectPage(Page page, RailLinkappSafetyEduPlanDTO customQueryParams) {
        customQueryParams.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        return baseMapper.selectPage(page,customQueryParams);
    }

    @Override
    public void add(RailLinkappSafetyEduPlanDTO dto) {
        //保存
        RailLinkappSafetyEduPlan one = new RailLinkappSafetyEduPlan();
        one.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        one.setName(dto.getName());
        one.setTrainingType(dto.getTrainingType());
        one.setTrainingSite(dto.getTrainingSite());
        one.setEduDate(dto.getEduDate());
        one.setContent(dto.getContent());
        one.setCuratorId(dto.getCuratorId());
        one.setNotes(dto.getNotes());
        //查询负责人名称
        RailLinkappRosterPersonnel personnel = linkappRosterPersonnelMapper.selectById(Long.valueOf(dto.getCuratorId()));
        if (Objects.nonNull(personnel)) {
            one.setCuratorName(personnel.getRealName());
        }

        //培训班组//网格
      //  if (StringUtils.isNotEmpty(dto.getGridId())) {
         //   one.setGridId(dto.getGridId());
            //查询网格
         //   RailLinkappGridManagementInfo railLinkappGridManagementInfo = gridManagementInfoMapper.selectById(Long.valueOf(dto.getGridId()));
          //  if (Objects.nonNull(railLinkappGridManagementInfo)) {
                one.setGridName(dto.getGridName());
           // }
      //  }
        one.setImgs(dto.getImgs());
        one.setFiles(dto.getFiles());
        one.setPassNumber(dto.getPassNumber());
        //保存新增人员内容
        commonService.setCreateAndModifyInfo(one);
        baseMapper.insert(one);
        boolean isz = false;
        for(RailLinkappSafetyEduDetailDTO ddto:dto.getAddList()){
            if(ddto.getPersonId().equals(one.getCuratorId())){
                isz = true;
            }
        }
     /*   if (!isz){
            //保存培训负责人信息
            RailLinkappSafetyEduDetailDTO fzr =  new RailLinkappSafetyEduDetailDTO();
            fzr.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
            fzr.setEduId(String.valueOf(one.getId()));
            fzr.setPersonName(personnel.getRealName());
            fzr.setPersonPhone(personnel.getLinkPhone());
            fzr.setPersonId(String.valueOf(personnel.getId()));
            fzr.setPersonName(String.valueOf(personnel.getWorkStatus()));
            //fzr.setKhNumber();
            dto.getAddList().add(fzr);
        }*/
        //保存人员信息
        addDetails(one,dto.getAddList());
    }

    @Override
    public void edit(RailLinkappSafetyEduPlanDTO dto) {
        RailLinkappSafetyEduPlan railLinkappSafetyEduPlan = baseMapper.selectById(dto.getId());
        if (Objects.isNull(railLinkappSafetyEduPlan)){
            return;
        }
        railLinkappSafetyEduPlan.setName(dto.getName());
        railLinkappSafetyEduPlan.setTrainingType(dto.getTrainingType());
        railLinkappSafetyEduPlan.setTrainingSite(dto.getTrainingSite());
        railLinkappSafetyEduPlan.setEduDate(dto.getEduDate());
        if (StringUtils.isEmpty(dto.getCuratorId())){
            railLinkappSafetyEduPlan.setCuratorId("");
            railLinkappSafetyEduPlan.setCuratorName("");
        }else{
            if (!railLinkappSafetyEduPlan.getCuratorId().equals(dto.getCuratorId())){
                RailLinkappRosterPersonnel personnel = linkappRosterPersonnelMapper.selectById(Long.valueOf(dto.getCuratorId()));
                if (Objects.nonNull(personnel)) {
                    railLinkappSafetyEduPlan.setCuratorId(dto.getCuratorId());
                    railLinkappSafetyEduPlan.setCuratorName(dto.getCuratorName());
                    //对培训负责人进行了修改
                    //safetyEduDetailMapper.selectbY
                }
            }
        }
 /*       if (StringUtils.isEmpty(dto.getGridId())){
            railLinkappSafetyEduPlan.setGridId("");
            railLinkappSafetyEduPlan.setGridName("");
        }else{
            RailLinkappRosterPersonnel personnel = linkappRosterPersonnelMapper.selectById(Long.valueOf(dto.getCuratorId()));
            //查询网格
            RailLinkappGridManagementInfo railLinkappGridManagementInfo = gridManagementInfoMapper.selectById(Long.valueOf(dto.getGridId()));
            if (Objects.nonNull(railLinkappGridManagementInfo)) {
                railLinkappSafetyEduPlan.setGridId(dto.getGridId());*/
                railLinkappSafetyEduPlan.setGridName(dto.getGridName());
    /*        }
        }*/
        railLinkappSafetyEduPlan.setImgs(dto.getImgs());
        railLinkappSafetyEduPlan.setFiles(dto.getFiles());
        railLinkappSafetyEduPlan.setPassNumber(dto.getPassNumber());
        commonService.setModifyInfo(railLinkappSafetyEduPlan);
        baseMapper.updateById(railLinkappSafetyEduPlan);
        //保存人员信息
        editDetails(railLinkappSafetyEduPlan,dto.getAddList(),dto.getDelList());
    }

    @Override
    public RailLinkappSafetyEduPlanVO selectOne(String id) {
        RailLinkappSafetyEduPlanVO railLinkappSafetyEduPlan = baseMapper.findByOne(Long.valueOf(id));
        if (Objects.isNull(railLinkappSafetyEduPlan)){
            return null;
        }
        RailLinkappSafetyEduDetailDTO dto = new RailLinkappSafetyEduDetailDTO();
        dto.setEduId(id);
        dto.setTenantId(railLinkappSafetyEduPlan.getTenantId());
        railLinkappSafetyEduPlan.setView(safetyEduDetailMapper.selectPage(dto));
        return railLinkappSafetyEduPlan;
    }

    @Override
    public List<RailLinkappSafetyEduPlanVO> selectList(RailLinkappSafetyEduPlanDTO customQueryParams) {
        return baseMapper.selectPage(customQueryParams);
    }

    @Override
    public void del(String id) {
        RailLinkappSafetyEduPlan railLinkappSafetyEduPlan = baseMapper.selectById(Long.valueOf(id));
        if (Objects.isNull(railLinkappSafetyEduPlan)){
            return;
        }
        baseMapper.deleteById(railLinkappSafetyEduPlan.getId());
        QueryWrapper<RailLinkappSafetyEduDetail> wapper = new QueryWrapper<>();
        wapper.eq("edu_id",railLinkappSafetyEduPlan.getId());
        wapper.eq("tenant_id",railLinkappSafetyEduPlan.getTenantId());
        safetyEduDetailMapper.delete(wapper);
    }

    @Override
    public IPage<EduDetailStatVO> selectPersonnelStatPage(Page page, RailLinkappSafetyEduDetailDTO customQueryParams) {
        customQueryParams.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        IPage iPage = safetyEduDetailMapper.selectPersonnelStatList(page, customQueryParams);
        List<EduDetailStatVO> records = iPage.getRecords();
        List<SysDictItem> jobs = sysDictItemService.selectByDictItems("jobs");
        Map<String, String> jobs_map = new HashMap<>();
        for(SysDictItem job:jobs){
            jobs_map.put(job.getItemValue(),job.getItemText());
        }
        List<SysDictItem> work_type = sysDictItemService.selectByDictItems("work_type");
        Map<String, String> work_map = new HashMap<>();
        for (SysDictItem work:work_type){
            work_map.put(work.getItemValue(),work.getItemText());
        }
        for (EduDetailStatVO vos:records){
            //岗位
            RailLinkappRosterPersonnel personnel = linkappRosterPersonnelMapper.selectById(vos.getPersonId());
            if (Objects.nonNull(personnel)){
                String item = "";
                if (personnel.getRosterType().contains("0")){
                    //管理人员
                    item = jobs_map.get(personnel.getRosterPost());
                }else if (personnel.getRosterType().contains("1")){
                    //劳务人员
                    item = work_map.get(personnel.getWorkType());
                }
                vos.setJobType(item);
            }
        }
        return iPage;
    }
    @Override
    public List<EduDetailStatVO> selectPersonnelStatList( RailLinkappSafetyEduDetailDTO customQueryParams){

        customQueryParams.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<EduDetailStatVO> records = safetyEduDetailMapper.selectPersonnelStatList(customQueryParams);
        List<SysDictItem> jobs = sysDictItemService.selectByDictItems("jobs");
        Map<String, String> jobs_map = new HashMap<>();
        for(SysDictItem job:jobs){
            jobs_map.put(job.getItemValue(),job.getItemText());
        }
        List<SysDictItem> work_type = sysDictItemService.selectByDictItems("work_type");
        Map<String, String> work_map = new HashMap<>();
        for (SysDictItem work:work_type){
            work_map.put(work.getItemValue(),work.getItemText());
        }
        List<Long> pros = new ArrayList<>();
        for (EduDetailStatVO vos:records){
            pros.add(Long.valueOf(vos.getPersonId()));
        }
        Map<String, String> typeMap = new HashMap<>();
        Map<String, String> workMap = new HashMap<>();
        List<RailLinkappRosterPersonnel> railLinkappRosterPersonnels = linkappRosterPersonnelMapper.selectBatchIds(pros);
        for (RailLinkappRosterPersonnel per:railLinkappRosterPersonnels){
            typeMap.put(String.valueOf(per.getId()),per.getRosterType());
            workMap.put(String.valueOf(per.getId()),per.getRosterType().contains("0")?per.getRosterPost():per.getRosterType().contains("1")?per.getWorkType():"");
        }
        for (EduDetailStatVO vos:records){
            //岗位
            String s = typeMap.get(vos.getPersonId());
            if (StringUtils.isNotEmpty(s)){
                String item = "";
                if (s.contains("0")){
                    //管理人员
                    String s1 = workMap.get(vos.getPersonId());
                    item = StringUtils.isNotEmpty(s1)?jobs_map.get(s1):"";
                }else if (s.contains("1")){
                    //劳务人员
                    String s1 = workMap.get(vos.getPersonId());
                    item = StringUtils.isNotEmpty(s1)?work_map.get(s1):"";
                }
                vos.setJobType(item);
            }
        }
        return records;
    }

    @Override
    public Map<String, Object> getPersonnelStatEduList(String id) {
        Map<String, Object> result = new HashMap<>();
        RailLinkappRosterPersonnel personnel = linkappRosterPersonnelMapper.selectById(Long.valueOf(id));
        if (Objects.nonNull(personnel)){
            result.put("name",personnel.getRealName());
            //岗位
            if (Objects.nonNull(personnel)){
                SysDictItem item = null;
                if (personnel.getRosterType().contains("0")){
                    //管理人员
                    item = sysDictItemService.selectByDictItem("jobs",personnel.getRosterPost());
                }else if (personnel.getRosterType().contains("1")){
                    //劳务人员
                    item = sysDictItemService.selectByDictItem("work_type",personnel.getWorkType());
                }
                result.put("jobType",Objects.nonNull(item)?item.getItemText():"");
            }
        }
        QueryWrapper<RailLinkappSafetyEduPlan> railLinkappSafetyEduPlanQueryWrapper = new QueryWrapper<>();
        railLinkappSafetyEduPlanQueryWrapper.eq("curator_id",id);
        List<RailLinkappSafetyEduPlan> railLinkappSafetyEduPlans = baseMapper.selectList(railLinkappSafetyEduPlanQueryWrapper);
        Map<String,RailLinkappSafetyEduPlan> fzrMap = new HashMap<>();
        for (RailLinkappSafetyEduPlan eduPlan:railLinkappSafetyEduPlans){
            fzrMap.put(String.valueOf(eduPlan.getId()),eduPlan);
        }
        List<Map<String,Object>> list = new ArrayList<>();
        //查询考试记录
        RailLinkappSafetyEduDetailDTO dto = new RailLinkappSafetyEduDetailDTO();
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        dto.setPersonId(id);
        List<RailLinkappSafetyEduDetailVO> railLinkappSafetyEduDetailVOS = safetyEduDetailMapper.selectPage(dto);
        List<Long> objects = new ArrayList<>();
        for (RailLinkappSafetyEduDetailVO vos:railLinkappSafetyEduDetailVOS){
            Map<String, Object> maps = new HashMap<>();
            RailLinkappSafetyEduPlan railLinkappSafetyEduPlan = baseMapper.selectById(vos.getEduId());
            if (Objects.nonNull(railLinkappSafetyEduPlan)){
                maps.put("date",railLinkappSafetyEduPlan.getEduDate());
                maps.put("eduName",railLinkappSafetyEduPlan.getName());
                RailLinkappSafetyEduPlan fzr = fzrMap.get(String.valueOf(railLinkappSafetyEduPlan.getId()));
                boolean aNull = Objects.isNull(fzr);
                maps.put("isFzr",aNull?"否":"是");
                maps.put("number",vos.getKhNumber());
                list.add(maps);
                objects.add(railLinkappSafetyEduPlan.getId());
            }
        }
        for(Long id_l:objects){
            RailLinkappSafetyEduPlan railLinkappSafetyEduPlan = fzrMap.get(String.valueOf(id_l));
            if (railLinkappSafetyEduPlan!=null){
                fzrMap.remove(String.valueOf(id_l));
            }
        }
        for (Map.Entry<String,RailLinkappSafetyEduPlan> entry : fzrMap.entrySet()) {
            RailLinkappSafetyEduPlan value = entry.getValue();
            Map<String, Object> maps = new HashMap<>();
            maps.put("date",value.getEduDate());
            maps.put("eduName",value.getName());
            maps.put("isFzr","是");
            maps.put("number","");
            list.add(maps);
        }
        result.put("eduData",list);
        return result;
    }

    public void editDetails(RailLinkappSafetyEduPlan one, List<RailLinkappSafetyEduDetailDTO> Adddetails, List<RailLinkappSafetyEduDetailDTO> delDetails){
        int sum = 0;
        int jgSum = 0;
        Date  now  = new Date();
        //删除
        if (Objects.nonNull(delDetails)){
           for (RailLinkappSafetyEduDetailDTO del:delDetails){
              if (Objects.nonNull(del.getId())){
                  safetyEduDetailMapper.deleteById(del.getId());
              }
           }
        }
        //新增 // 编辑
        for (RailLinkappSafetyEduDetailDTO add:Adddetails){
            if (Objects.nonNull(add.getId())){
                  //编辑
                RailLinkappSafetyEduDetail railLinkappSafetyEduDetail = safetyEduDetailMapper.selectById(add.getId());
                if (Objects.nonNull(railLinkappSafetyEduDetail)){
                    railLinkappSafetyEduDetail.setKhNumber(add.getKhNumber());
                    if (Objects.nonNull(one.getPassNumber()) && Objects.nonNull(railLinkappSafetyEduDetail.getKhNumber())){
                        if (railLinkappSafetyEduDetail.getKhNumber()>=one.getPassNumber()){
                            railLinkappSafetyEduDetail.setIsQual(0);
                            /*  jgSum++;*/
                        }else{
                            railLinkappSafetyEduDetail.setIsQual(1);
                        }
                    }else{
                        railLinkappSafetyEduDetail.setIsQual(0);
                    }
                    railLinkappSafetyEduDetail.setModifyTime(now);
                    safetyEduDetailMapper.updateById(railLinkappSafetyEduDetail);
                }
            }else{
                //新增
                RailLinkappSafetyEduDetail detail = new RailLinkappSafetyEduDetail();
                detail.setTenantId(one.getTenantId());
                detail.setEduId(String.valueOf(one.getId()));
                detail.setModifyTime(now);
                if (Objects.isNull(add.getKhNumber())){
                    add.setKhNumber(0);
                }
                detail.setKhNumber(add.getKhNumber());
                detail.setPersonId(add.getPersonId());
                detail.setPersonName(add.getPersonName());
                detail.setPersonPhone(add.getPersonPhone());
                detail.setPersonStatus(add.getPersonStatus());
                if (Objects.nonNull(one.getPassNumber()) && Objects.nonNull(detail.getKhNumber())){
                    if (detail.getKhNumber()>=one.getPassNumber()){
                        detail.setIsQual(0);
                      /*  jgSum++;*/
                    }else{
                        detail.setIsQual(1);
                    }
                }else{
                    detail.setIsQual(0);
                }
                safetyEduDetailMapper.insert(detail);
            }
        }
        //重新计算 参与考核人员 和 通过人数
        QueryWrapper<RailLinkappSafetyEduDetail> ros = new QueryWrapper<>();
        ros.eq("tenant_id",one.getTenantId());
        ros.eq("edu_id",one.getId());
        List<RailLinkappSafetyEduDetail> details = safetyEduDetailMapper.selectList(ros);
        if (details.size() == 0){
            one.setCompleteNumber(0);
            one.setPassThroughNumber(0);
        }else{
            for (RailLinkappSafetyEduDetail detail:details){
               if (Integer.valueOf(0).equals(detail.getIsQual())){
                   jgSum++;
               }

            }
        }
        one.setCompleteNumber(details.size());
        one.setPassThroughNumber(jgSum);
        baseMapper.updateById(one);
    }
    public void addDetails(RailLinkappSafetyEduPlan one, List<RailLinkappSafetyEduDetailDTO> details){
        int sum = 0;
        int jgSum = 0;
        Date  now  = new Date();
       for (RailLinkappSafetyEduDetailDTO dto:details){
           if (Objects.isNull(dto.getId())){

               RailLinkappSafetyEduDetail detail = new RailLinkappSafetyEduDetail();
               detail.setTenantId(one.getTenantId());
               detail.setEduId(String.valueOf(one.getId()));
               detail.setModifyTime(now);
               if (Objects.isNull(dto.getKhNumber())){
                   dto.setKhNumber(0);
               }
               detail.setKhNumber(dto.getKhNumber());
               detail.setPersonId(dto.getPersonId());
               detail.setPersonName(dto.getPersonName());
               detail.setPersonPhone(dto.getPersonPhone());
               detail.setPersonStatus(dto.getPersonStatus());

               //获取人员类型
               if (Objects.nonNull(one.getPassNumber()) && Objects.nonNull(detail.getKhNumber())){
                   if (detail.getKhNumber()>=one.getPassNumber()){
                       detail.setIsQual(0);
                       jgSum++;
                   }else{
                       detail.setIsQual(1);
                   }
               }else{
                   detail.setIsQual(0);
                   jgSum++;
               }
               //岗位
               RailLinkappRosterPersonnel personnel = linkappRosterPersonnelMapper.selectById(dto.getPersonId());
               if (Objects.nonNull(personnel)){
                   SysDictItem item = null;
                   if (personnel.getRosterType().contains("0")){
                      //管理人员
                       item = sysDictItemService.selectByDictItem("jobs",personnel.getRosterPost());
                   }else if (personnel.getRosterType().contains("1")){
                      //劳务人员
                       item = sysDictItemService.selectByDictItem("work_type",personnel.getWorkType());
                   }
                   detail.setJobType(Objects.nonNull(item)?item.getItemText():"");
               }
               safetyEduDetailMapper.insert(detail);
               sum++;
           }
       }
        one.setCompleteNumber(sum);
        one.setPassThroughNumber(jgSum);
        baseMapper.updateById(one);
    }
    @Override
    public List<EduDetailStatVO> getStart(RailLinkappSafetyEduDetailDTO dto){
        List<SysDictItem> jobs = sysDictItemService.selectByDictItems("jobs");
        Map<String, String> jobs_map = new HashMap<>();
        for(SysDictItem job:jobs){
            jobs_map.put(job.getItemValue(),job.getItemText());
        }
        List<SysDictItem> work_type = sysDictItemService.selectByDictItems("work_type");
        Map<String, String> work_map = new HashMap<>();
        for (SysDictItem work:work_type){
            work_map.put(work.getItemValue(),work.getItemText());
        }
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<EduDetailStatVO> eduDetailStatVOS = baseMapper.selectPersonnelStatList(dto);
        QueryWrapper<RailLinkappSafetyEduDetail> objectQueryWrapper1 = new QueryWrapper<>();
        objectQueryWrapper1.eq("tenant_id",dto.getTenantId());
        //人员信息
        List<RailLinkappSafetyEduDetail> details = safetyEduDetailMapper.selectList(objectQueryWrapper1);

        QueryWrapper<RailLinkappSafetyEduPlan> objectQueryWrapper2 = new QueryWrapper<>();
        objectQueryWrapper2.eq("tenant_id",dto.getTenantId());
        //培训记录
        List<RailLinkappSafetyEduPlan> plans = baseMapper.selectList(objectQueryWrapper2);
        List<Long> pros = new ArrayList<>();
        for (EduDetailStatVO vos:eduDetailStatVOS){
            pros.add(Long.valueOf(vos.getPersonId()));
        }
        Map<String, String> typeMap = new HashMap<>();
        Map<String, String> workMap = new HashMap<>();
        List<RailLinkappRosterPersonnel> railLinkappRosterPersonnels = new ArrayList<>();
        if (pros.size()>0){
            railLinkappRosterPersonnels = linkappRosterPersonnelMapper.selectBatchIds(pros);
        }
        for (RailLinkappRosterPersonnel per:railLinkappRosterPersonnels){
            typeMap.put(String.valueOf(per.getId()),per.getRosterType());
            workMap.put(String.valueOf(per.getId()),per.getRosterType().contains("0")?per.getRosterPost():per.getRosterType().contains("1")?per.getWorkType():"");
        }
        for (EduDetailStatVO vo:eduDetailStatVOS){
            int examCount = 0;
            int joinRainCount = 0;
            int passTrainCount = 0;
            int tg = 0;
            //参与培训
           String personId =  vo.getPersonId();
           for (RailLinkappSafetyEduDetail detail:details){
               if (personId.equals(detail.getPersonId())){
                   examCount++;
                   if (detail.getIsQual().equals(0)){
                       //0合格，1不合格
                       tg++;
                   }
               }

           }
            String s2 = calculatePercentageOptimized( examCount,tg);
            vo.setPassRate(s2);
            vo.setExamCount(examCount);
            passTrainCount = examCount;
            vo.setPassTrainCount(passTrainCount);

           for(RailLinkappSafetyEduPlan plan:plans){
               if (personId.equals(plan.getCuratorId())){
                   joinRainCount++;
               }
           }
            vo.setJoinRainCount(joinRainCount);
            String s = typeMap.get(personId);
            if (StringUtils.isNotEmpty(s)){
                String item = "";
                if (s.contains("0")){
                    //管理人员
                    String s1 = workMap.get(personId);
                    item = StringUtils.isNotEmpty(s1)?jobs_map.get(s1):"";
                }else if (s.contains("1")){
                    //劳务人员
                    String s1 = workMap.get(personId);
                    item = StringUtils.isNotEmpty(s1)?work_map.get(s1):"";
                }
                vo.setJobType(item);
            }
        }
        if (StringUtils.isNotEmpty(dto.getOrderField())&&StringUtils.isNotEmpty(dto.getOrderType())){
           if (eduDetailStatVOS.size()>0){
               sort(eduDetailStatVOS,dto.getOrderField(),dto.getOrderType());
           }
        }
        return eduDetailStatVOS;
    }

    @Override
    public Map<String, Object> getStatEduNumber() {
        QueryWrapper<RailLinkappSafetyEduPlan> qw_edu = new QueryWrapper<>();
        qw_edu.eq("tenant_id",linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<RailLinkappSafetyEduPlan> edus = baseMapper.selectList(qw_edu);

        QueryWrapper<RailLinkappSafetyEduDetail> qw_edu_det = new QueryWrapper<>();
        qw_edu_det.eq("tenant_id",linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<RailLinkappSafetyEduDetail> details = safetyEduDetailMapper.selectList(qw_edu_det);
        int sum = edus.size();
        int sumDetail = details.size();
        int hegeSum = 0;
        //统计合格率
        for (RailLinkappSafetyEduDetail detail:details){
            if (Objects.nonNull(detail.getIsQual())){
               if (detail.getIsQual().equals(0)){
                   hegeSum++;
               }
            }
        }
        //获取类型字典
        List<SysDictItem> trainingSite = sysDictItemService.selectByDictItems("training_type");
        Map<String, String> dictMap = new HashMap<>();
        List<Map<String, Object>> dictListInit = new ArrayList<>();
        for (SysDictItem dict:trainingSite){
            dictMap.put(dict.getItemValue(),dict.getItemText());
            Map<String , Object> dictm = new HashMap<>();
            dictm.put("types",dict.getItemText());
            dictm.put("sums",0);
            dictm.put("key",dict.getItemValue());
            dictListInit.add(dictm);
        }
        //合格率
        String passRate = calculatePercentageOptimized(sumDetail, hegeSum, "0.0");
        Map<String, Integer> transMap = new HashMap<>();
        for (RailLinkappSafetyEduPlan plan:edus){
            String  type =  plan.getTrainingType();
            Integer s = transMap.get(dictMap.get(type));
            if (Objects.nonNull(s)){
                s++;
            }else{
                s = 1;
            }
            transMap.put(dictMap.get(type),s);
        }
        for(Map<String, Object> map:dictListInit){
            String key = Objects.toString(map.get("types"),"");
            Integer integer = transMap.get(key);
            map.put("sums",Objects.isNull(integer)?0:integer);
        }
        transMap = new HashMap<>();
        for(Map<String,Object> map:dictListInit){
            transMap.put(Objects.toString(map.get("types")),Integer.valueOf(String.valueOf(map.get("sums"))));
        }
        List<Map.Entry<String, Integer>> entryList = new ArrayList<>(transMap.entrySet());
        entryList.sort((a, b) -> b.getValue().compareTo(a.getValue()));
       // 2. 构建新map，前三名直接放入，其他合并为“其他数量”
        Map<String, Integer> resultMap = new LinkedHashMap<>();
        int otherCount = 0;
        for (int i = 0; i < entryList.size(); i++) {
            Map.Entry<String, Integer> entry = entryList.get(i);
            if (i < 3) {
                resultMap.put(entry.getKey(), entry.getValue());
            } else {
                otherCount += entry.getValue();
            }
        }
        //if (otherCount > 0) {
        resultMap.put("其他", otherCount);
         //  }
        //统计 类型
        List<Map<String, Object>> list = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : resultMap.entrySet()) {
            Map<String, Object> map = new HashMap<>();
            map.put("name",entry.getKey());
            map.put("sums", entry.getValue());
            list.add(map);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("sum",sum);//培训总数
        result.put("sumDetail",sumDetail);//参会人员数量
        result.put("passRate",passRate);//合格率
        result.put("piChar",list);//斌图
        return result;
    }

    /**
     * 优化版本，重用DecimalFormat对象
     */
    public static String calculatePercentageOptimized(int numerator, int denominator) {
        if (denominator == 0) {
            return "0.00%";
        }
        double percentage = (double) denominator / numerator  * 100;
        return new DecimalFormat("0.00").format(percentage) + "%";
    }

    /**
     * 优化版本，重用DecimalFormat对象
     */
    public static String calculatePercentageOptimized(int numerator, int denominator,String format) {
        if (denominator == 0) {
            return "0.00%";
        }
        double percentage = (double) denominator / numerator  * 100;
        return new DecimalFormat("0.00").format(percentage) + "%";
    }

    /**
     * 对 EduDetailStatVO 列表排序
     * @param list 需要排序的列表
     * @param orderField 排序字段（"examCount" 或 "joinRainCount"）
     * @param orderType 排序类型（"ASC" 或 "DESC"）
     */
    public static void sort(List<EduDetailStatVO> list, String orderField, String orderType) {
        if (list == null || list.isEmpty() || orderField == null || orderType == null) {
            return;
        }

        Comparator<EduDetailStatVO> comparator = null;

        switch (orderField) {
            case "examCount":
                comparator = Comparator.comparing(vo -> vo.getExamCount() == null ? 0 : vo.getExamCount());
                break;
            case "joinRainCount":
                comparator = Comparator.comparing(vo -> vo.getJoinRainCount() == null ? 0 : vo.getJoinRainCount());
                break;
            default:
                // 未知字段不排序
                return;
        }

        if ("DESC".equalsIgnoreCase(orderType)) {
            comparator = comparator.reversed();
        }

        list.sort(comparator);
    }

}
