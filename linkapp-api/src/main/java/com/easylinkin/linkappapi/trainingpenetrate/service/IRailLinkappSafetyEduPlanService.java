package com.easylinkin.linkappapi.trainingpenetrate.service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduDetailDTO;
import com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduPlanDTO;
import com.easylinkin.linkappapi.trainingpenetrate.entity.RailLinkappSafetyEduPlan;
import com.easylinkin.linkappapi.trainingpenetrate.vo.EduDetailStatVO;
import com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduPlanVO;

import java.util.List;
import java.util.Map;

public interface IRailLinkappSafetyEduPlanService extends IService<RailLinkappSafetyEduPlan> {
    IPage<RailLinkappSafetyEduPlanVO> selectPage(Page page, RailLinkappSafetyEduPlanDTO customQueryParams);

    void add(RailLinkappSafetyEduPlanDTO dto);

    void edit(RailLinkappSafetyEduPlanDTO dto);

    RailLinkappSafetyEduPlanVO selectOne(String id);

    List<RailLinkappSafetyEduPlanVO> selectList(RailLinkappSafetyEduPlanDTO customQueryParams);

    void del(String id);

    IPage<EduDetailStatVO> selectPersonnelStatPage(Page page, RailLinkappSafetyEduDetailDTO customQueryParams);

    List<EduDetailStatVO> selectPersonnelStatList( RailLinkappSafetyEduDetailDTO customQueryParams);

    Map<String, Object> getPersonnelStatEduList(String id);

    List<EduDetailStatVO> getStart(RailLinkappSafetyEduDetailDTO dto);

    Map<String, Object> getStatEduNumber();
}
