package com.easylinkin.linkappapi.trainingpenetrate.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
@Data
public class PageResult<T>   implements Serializable {
    private static final long serialVersionUID = 1L;
    private List<T> records;
    private long total;
    private int size;
    private int current;
    private List<?> orders = Collections.emptyList();
    private boolean searchCount = true;
    private long pages;

    // 构造方法
    public PageResult(List<T> records, long total, int size, int current) {
        this.records = records;
        this.total = total;
        this.size = size;
        this.current = current;
        this.pages = (total + size - 1) / size; // 计算总页数
    }

    // getter/setter 省略，可用lombok @Data
}