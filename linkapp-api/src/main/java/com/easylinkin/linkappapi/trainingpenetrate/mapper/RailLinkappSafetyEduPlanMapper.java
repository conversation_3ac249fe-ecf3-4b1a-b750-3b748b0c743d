package com.easylinkin.linkappapi.trainingpenetrate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappGridManagementInfo;
import com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduDetailDTO;
import com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduPlanDTO;
import com.easylinkin.linkappapi.trainingpenetrate.entity.RailLinkappSafetyEduPlan;
import com.easylinkin.linkappapi.trainingpenetrate.vo.EduDetailStatVO;
import com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduPlanVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RailLinkappSafetyEduPlanMapper extends  BaseMapper<RailLinkappSafetyEduPlan>{

    IPage<RailLinkappSafetyEduPlanVO> selectPage(Page<RailLinkappGridManagementInfo> page, @Param("entity") RailLinkappSafetyEduPlanDTO railLinkappSafetyEduPlanDTO);
    List<RailLinkappSafetyEduPlanVO> selectPage(@Param("entity") RailLinkappSafetyEduPlanDTO railLinkappSafetyEduPlanDTO);
    @Select("SELECT * FROM rail_linkapp_safety_edu_plan WHERE id = #{id}")
    RailLinkappSafetyEduPlanVO findByOne(@Param("id") Long id);

    List<EduDetailStatVO> selectPersonnelStatList(@Param("entity") RailLinkappSafetyEduDetailDTO railLinkappSafetyEduPlanDTO);

    @Select("SELECT COUNT(*) FROM rail_linkapp_safety_edu_plan WHERE tenant_id = #{tenantId} and training_type = 3 or training_type = 4 and grid_name  = #{assessGroup} and  DATE_FORMAT(edu_date,'%Y-%m') = #{assessMonth}")
    int selectEduByType34Count(@Param("tenantId") String tenantId, @Param("assessGroup") String assessGroup, @Param("assessMonth") String assessMonth);

    @Select("SELECT COUNT(*) FROM rail_linkapp_safety_edu_plan WHERE tenant_id = #{tenantId} and training_type = 2 and grid_name  = #{assessGroup} and  DATE_FORMAT(edu_date,'%Y-%m') = #{assessMonth}")
    int selectEduByType2Count(@Param("tenantId") String tenantId, @Param("assessGroup") String assessGroup, @Param("assessMonth") String assessMonth);


}
