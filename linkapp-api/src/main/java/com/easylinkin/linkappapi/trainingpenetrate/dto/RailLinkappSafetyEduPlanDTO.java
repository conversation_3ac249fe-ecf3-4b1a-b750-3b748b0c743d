package com.easylinkin.linkappapi.trainingpenetrate.dto;

import com.easylinkin.linkappapi.common.translate.CodeI18n;
import com.easylinkin.linkappapi.trainingpenetrate.entity.RailLinkappSafetyEduPlan;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/1 下午 4:45
 */
@Data
public class RailLinkappSafetyEduPlanDTO extends RailLinkappSafetyEduPlan {
    private String startDate;
    private String endDate;
    private List<String> ids;
    /**
     * 新增的数组
     */
    List<RailLinkappSafetyEduDetailDTO> addList;
    /**
     * 删除的数组
     */
    List<RailLinkappSafetyEduDetailDTO> delList;
}
