package com.easylinkin.linkappapi.trainingpenetrate.util;

import com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduDetailVO;
import com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduPlanVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/7/8 下午 6:02
 */
public class EasyExcelUtil {

    public void exportExcel(HttpServletResponse response, Map<String,List<RailLinkappSafetyEduDetailVO>> trans_map, List<RailLinkappSafetyEduPlanVO> data){

    }

    // 示例：模拟多条记录
    private List<Map<String, Object>> getRecordList() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("fileName", "三级安全教育_" + i);
            map.put("title", "2025-11-25三级安全教育_" + i);
            map.put("projectName", "邹城至济宁高速公路上跨京沪铁路项目");
            map.put("trainType", "三级安全教育");
            map.put("place", "现场会议室");
            map.put("trainDate", "2024-11-25");
            map.put("summary", "三级安全教育培训");
            map.put("manager", "管理员1");
            map.put("joinNum", "4");
            map.put("passNum", "2");
            List<List<String>> dataList = new ArrayList<>();
            dataList.add(Arrays.asList("1", "", "", "不在岗", "", "78"));
            dataList.add(Arrays.asList("2", "", "", "不在岗", "", "100"));
            dataList.add(Arrays.asList("3", "", "", "不在岗", "", "0"));
            dataList.add(Arrays.asList("4", "", "", "不在岗", "", "0"));
            map.put("dataList", dataList);
            list.add(map);
        }
        return list;
    }

    // 生成带合并单元格的Excel
    public static void createExcelWithMerge( OutputStream out,RailLinkappSafetyEduPlanVO vo,List<RailLinkappSafetyEduDetailVO> dlist,String projectName,String pxtype) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(vo.getName());


        // 设置列宽
        for (int i = 0; i <= 6; i++) {
            sheet.setColumnWidth(i, 16 * 256);
        }

        // 通用边框样式
        CellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);
        borderStyle.setAlignment(HorizontalAlignment.CENTER);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);

// 1. 大标题（C1:F1合并）
        Row row0 = sheet.createRow(0);
        for (int i = 0; i < 6; i++) row0.createCell(i).setCellStyle(borderStyle);
        row0.getCell(0).setCellValue(vo.getName());
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

// 2. 项目名称行（A2:A3合并，B2:D3合并，E2:E3合并，F2单元格，F3单元格）
        Row row1 = sheet.createRow(1);
        Row row2 = sheet.createRow(2);
        for (int i = 0; i < 6; i++) {
            row1.createCell(i).setCellStyle(borderStyle);
            row2.createCell(i).setCellStyle(borderStyle);
        }
        row1.getCell(0).setCellValue("项目名称");
        row1.getCell(1).setCellValue(projectName);
        row1.getCell(4).setCellValue("培训类型");
        row1.getCell(5).setCellValue(pxtype);
        row2.getCell(4).setCellValue("培训日期");
        row2.getCell(5).setCellValue(vo.getEduDate());
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0)); // A2:A3
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 3)); // B2:D3
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 4, 4)); // E2:E3

// 3. 培训场地行（A4，B4:D4合并，E4，F4）
        Row row3 = sheet.createRow(3);
        for (int i = 0; i < 6; i++) row3.createCell(i).setCellStyle(borderStyle);
        row3.getCell(0).setCellValue("培训场地");
        row3.getCell(1).setCellValue(vo.getTrainingSite());
        row3.getCell(4).setCellValue("负责人");
        row3.getCell(5).setCellValue(vo.getCuratorName());
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 1, 3)); // B4:D4

// 4. 内容概要行（A5，B5:D5合并，E5，F5）
        Row row4 = sheet.createRow(4);
        for (int i = 0; i < 6; i++) row4.createCell(i).setCellStyle(borderStyle);
        row4.getCell(0).setCellValue("内容概要");
        row4.getCell(1).setCellValue(vo.getNotes());
        row4.getCell(4).setCellValue("参与人数");
        row4.getCell(5).setCellValue(vo.getCompleteNumber());
        sheet.addMergedRegion(new CellRangeAddress(4, 5, 0, 0)); // A2:A3
        sheet.addMergedRegion(new CellRangeAddress(4, 5, 1, 3)); // B2:D3
       // sheet.addMergedRegion(new CellRangeAddress(4, 5, 1, 3)); // B5:D5

// 5. 通过人数行（E6，F6）
        Row row5 = sheet.createRow(5);
        for (int i = 0; i < 6; i++) row5.createCell(i).setCellStyle(borderStyle);
        row5.getCell(4).setCellValue("通过人数");
        row5.getCell(5).setCellValue(vo.getPassThroughNumber());

        // 7. 列名行
        Row row6 = sheet.createRow(6);
        String[] colNames = {"序号", "姓名", "岗位/工种", "在岗情况", "手机号", "本次成绩(分)"};
        for (int i = 0; i < colNames.length; i++) {
            row6.createCell(i).setCellValue(colNames[i]);
            row6.getCell(i).setCellStyle(borderStyle);
        }

        // 列名行
       /* Row row6 = sheet.createRow(6);
        String[] colNames = {"序号", "姓名", "岗位/工种", "在岗情况", "手机号", "本次成绩(分)"};
        for (int i = 0; i < colNames.length; i++) {
            row6.createCell(i).setCellValue(colNames[i]);
        }*/
        //List<Map<>> objects = new ArrayList<>();
        // 数据行
        List<RailLinkappSafetyEduDetailVO> dataList   = dlist;
        int index = 1;
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet.createRow(7 + i);
            RailLinkappSafetyEduDetailVO rowData = dataList.get(i);
            for (int j = 0; j < colNames.length; j++) {
                Cell coll = row.createCell(j);
                   switch (j){
                       case 0:
                           coll.setCellValue(index);
                           index++;
                           break;
                       case 1:
                           coll.setCellValue(rowData.getPersonName());
                           break;
                       case 2:
                           coll.setCellValue(rowData.getJobType());
                           break;
                       case 3:
                           coll.setCellValue(Integer.valueOf(0).equals(rowData.getPersonStatus())?"不住岗":Integer.valueOf(1).equals(rowData.getPersonStatus())?"在岗":"");
                           break;
                       case 4:
                           coll.setCellValue(rowData.getPersonPhone());
                           break;
                       case 5:
                           coll.setCellValue(rowData.getKhNumber().toString());
                           break;
                       default:
                           break;
                   }
                coll.setCellStyle(borderStyle);
            }
        }

        workbook.write(out);
        workbook.close();
    }
}
