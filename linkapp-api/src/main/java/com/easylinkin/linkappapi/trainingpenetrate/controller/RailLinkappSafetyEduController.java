package com.easylinkin.linkappapi.trainingpenetrate.controller;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.common.utils.DateUtil;
import com.easylinkin.linkappapi.common.utils.StringUtil;
import com.easylinkin.linkappapi.config.entity.SysDictItem;
import com.easylinkin.linkappapi.config.service.SysDictItemService;
import com.easylinkin.linkappapi.penetsecuremanage.utils.ExcelHelper;
import com.easylinkin.linkappapi.roster.dto.LinkappRosterPersonnelDTO;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import com.easylinkin.linkappapi.tenant.entity.LinkappTenant;
import com.easylinkin.linkappapi.tenant.mapper.LinkappTenantMapper;
import com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduDetailDTO;
import com.easylinkin.linkappapi.trainingpenetrate.dto.RailLinkappSafetyEduPlanDTO;
import com.easylinkin.linkappapi.trainingpenetrate.mapper.RailLinkappSafetyEduDetailMapper;
import com.easylinkin.linkappapi.trainingpenetrate.service.IRailLinkappSafetyEduPlanService;
import com.easylinkin.linkappapi.trainingpenetrate.util.EasyExcelUtil;
import com.easylinkin.linkappapi.trainingpenetrate.vo.EduDetailStatVO;
import com.easylinkin.linkappapi.trainingpenetrate.vo.PageResult;
import com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduDetailVO;
import com.easylinkin.linkappapi.trainingpenetrate.vo.RailLinkappSafetyEduPlanVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**培训穿透，培训信息相关接口
 * <AUTHOR>
 * @date 2025/7/1 下午 4:57
 */
@RestController
@RequestMapping("/safetyEdu")
public class RailLinkappSafetyEduController {

    @Autowired
    private IRailLinkappSafetyEduPlanService railLinkappSafetyEduPlanService;

    @Autowired
    private RailLinkappSafetyEduDetailMapper railLinkappSafetyEduDetailMapper;
    @Autowired
    private LinkappUserContextProducer linkappUserContextProducer;

    @Autowired
    private SysDictItemService sysDictItemService;

    @Autowired
    private LinkappTenantMapper tenantMapper;



    /**
     * 列表分页查询
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getPage")
    public RestMessage selectPage(@RequestBody RequestModel<RailLinkappSafetyEduPlanDTO> requestModel) {
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<RailLinkappSafetyEduPlanVO> record = railLinkappSafetyEduPlanService.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     *  培训信息保存接口
     * @param dto
     * @return
     */
    @PostMapping(value = "/save")
    public RestMessage save(@RequestBody RailLinkappSafetyEduPlanDTO dto){
        //新增校验
        if (StringUtils.isEmpty(dto.getTrainingType())){
            return RestBuilders.failureMessage().setMessage("培训类型不能为空");
        }
        if (StringUtils.isEmpty(dto.getName())){
            return RestBuilders.failureMessage().setMessage("培训名称不能为空");
        }
        if (StringUtils.isEmpty(dto.getEduDate())){
            return RestBuilders.failureMessage().setMessage("培训时间不能为空");
        }
        if (StringUtils.isEmpty(dto.getContent())){
            return RestBuilders.failureMessage().setMessage("培训内容不能为空");
        }
        if (StringUtils.isEmpty(dto.getCuratorId())){
            return RestBuilders.failureMessage().setMessage("培训负责人不能为空");
        }
        if (Objects.isNull(dto.getId())){
            //新增
            if (Objects.isNull(dto.getAddList())){
                return RestBuilders.failureMessage().setMessage("培训人员不能为空");
            }
            if (dto.getAddList().size() == 0){
                return RestBuilders.failureMessage().setMessage("请选中参与培训的人员");
            }
            railLinkappSafetyEduPlanService.add(dto);
        }else{
            //编辑
            railLinkappSafetyEduPlanService.edit(dto);
        }
        return RestBuilders.successMessage().setMessage("操作成功");
    }

    /**
     * 培训信息详细查询接口
     * @return
     */
    @GetMapping(value = "/getOne")
    public RestMessage getOne(String id){
        if (StringUtils.isEmpty(id)){
            return RestBuilders.failureMessage().setMessage("记录id不能为空");
        }
        RailLinkappSafetyEduPlanVO one  = railLinkappSafetyEduPlanService.selectOne(id);
        if (Objects.isNull(one)){
            return RestBuilders.failureMessage().setMessage("查询记录不存在");
        }
        return RestBuilders.successBuilder(one).build();
    }

    /**
     * 导出培训信息接口
     */
    @PostMapping(value = "/exportByIds")
    public void exportByIds(HttpServletRequest request, HttpServletResponse response,@RequestBody RailLinkappSafetyEduPlanDTO dto){
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<RailLinkappSafetyEduPlanVO> railLinkappSafetyEduPlanVOS = railLinkappSafetyEduPlanService.selectList(dto);
        List<Map<String,Object>> data = new ArrayList<>();
        for (RailLinkappSafetyEduPlanVO vo:railLinkappSafetyEduPlanVOS){
            Map<String, Object> perms = new HashMap<>();
            perms.put("name",vo.getName());
            SysDictItem sysDictItem = sysDictItemService.selectByDictItem("training_type", vo.getTrainingType());
            if (Objects.isNull(sysDictItem)){
                perms.put("trainingType","");
            }else {
                perms.put("trainingType",sysDictItem.getItemText());
            }
            perms.put("eduDate",vo.getEduDate());
            perms.put("content",vo.getContent());
            perms.put("curatorName",vo.getCuratorName());
            perms.put("trainingSite",vo.getTrainingSite());
            perms.put("gridName",vo.getGridName());
            perms.put("sumAnTg",""+vo.getPassThroughNumber()+"/"+vo.getCompleteNumber());
            data.add(perms);
        }
        String[] header_cn = { "安全教育培训名称","培训类型","培训时间","培训内容","负责人","培训场地","培训班组","通过人数/参与人数"};
        String[] header = { "name","trainingType","eduDate","content","curatorName","trainingSite","gridName","sumAnTg"};
        ExcelHelper.exportData(header, header_cn, data, "安全教育"+ DateUtil.getYYYYMMDDHHNumberString(new Date()) +".xlsx", request, response, 2);
    }
    @PostMapping(value = "/exportByIdsZip")
    public void exportByIdsZip(HttpServletRequest request, HttpServletResponse response,@RequestBody RailLinkappSafetyEduPlanDTO dto) throws IOException {
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<RailLinkappSafetyEduPlanVO> railLinkappSafetyEduPlanVOS = railLinkappSafetyEduPlanService.selectList(dto);
        if(railLinkappSafetyEduPlanVOS.size() == 0){

        }
        Map<String,List<RailLinkappSafetyEduDetailVO>>  trans_map  = new HashMap<>();
        for (RailLinkappSafetyEduPlanVO vo:railLinkappSafetyEduPlanVOS){
            //获取下级人员
            RailLinkappSafetyEduDetailDTO dtod = new RailLinkappSafetyEduDetailDTO();
            dtod.setEduId(String.valueOf(vo.getId()));
            dtod.setTenantId(vo.getTenantId());
            List<RailLinkappSafetyEduDetailVO> railLinkappSafetyEduDetailVOS = railLinkappSafetyEduDetailMapper.selectPage(dtod);
            if (Objects.nonNull(railLinkappSafetyEduDetailVOS)){
                trans_map.put(String.valueOf(vo.getId()),railLinkappSafetyEduDetailVOS);
            }
        }
        response.setContentType("application/zip");
        response.setCharacterEncoding("utf-8");
        String zipName = URLEncoder.encode("安全教育培训", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + zipName + ".zip");
        int index = 1;
        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            for (RailLinkappSafetyEduPlanVO vo : railLinkappSafetyEduPlanVOS) {
                // 1. 生成Excel到内存
                ByteArrayOutputStream excelOut = new ByteArrayOutputStream();
                LinkappTenant byId = tenantMapper.getById(vo.getTenantId());
                String projectName = "";
                String type = "";
                if (Objects.nonNull(byId)) {
                    projectName = byId.getPlatformProjectName();
                }
                //培训类型
                if (StringUtils.isNotEmpty(vo.getTrainingType())) {
                    SysDictItem sysDictItem = sysDictItemService.selectByDictItem("training_type", vo.getTrainingType());
                    type = (Objects.isNull(sysDictItem) ? "" : sysDictItem.getItemText());
                }
                EasyExcelUtil.createExcelWithMerge(excelOut, vo, trans_map.get(String.valueOf(vo.getId())), projectName, type);
                // 2. 写入zip
                String fileName = vo.getName()+ "_"+index+".xlsx";
                index++;
                zipOut.putNextEntry(new ZipEntry(fileName));
                zipOut.write(excelOut.toByteArray());
                zipOut.closeEntry();
                excelOut.close();
            }
        }
    }

    /**
     * 培训信息删除接口
     * @param id
     */
    @GetMapping(value = "/del")
    public RestMessage del(String id){
       if (StringUtils.isEmpty(id)){
           return RestBuilders.failureMessage().setMessage("id不能为空");
       }
        railLinkappSafetyEduPlanService.del(id);
        return RestBuilders.successMessage().setMessage("操作成功");
    }

    @PostMapping(value = "/selectPersonnelStatPage")
    public RestMessage  selectPersonnelStatPage(@RequestBody RequestModel<RailLinkappSafetyEduDetailDTO> requestModel){
        List<EduDetailStatVO> start = railLinkappSafetyEduPlanService.getStart(requestModel.getCustomQueryParams());

        PageResult<EduDetailStatVO> paginate = paginate(start, (int) requestModel.getPage().getSize(), (int) requestModel.getPage().getCurrent());

        return RestBuilders.successBuilder(paginate).data(paginate).build();
    }
    /**
     * 培训人员统计导出接口
     */
    @PostMapping(value = "/exportByPersonnelStat")
    public void exportByPersonnelStat(HttpServletRequest request, HttpServletResponse response,@RequestBody RailLinkappSafetyEduDetailDTO dto){
        List<EduDetailStatVO> eduDetailStatVOS = railLinkappSafetyEduPlanService.getStart(dto);
        List<Map<String,Object>> data = new ArrayList<>();
        for (EduDetailStatVO vos : eduDetailStatVOS){
            Map<String, Object> map = new HashMap<>();
            //人员
            map.put("name",vos.getRealName() + "   " +vos.getJobType());
            //参与培训次数
            map.put("examCount",vos.getExamCount());
            //负责培训次数
            map.put("joinRainCount",vos.getJoinRainCount());
            //考试次数，考试通过率
            map.put("passTrain",vos.getPassTrainCount() +"/"+ vos.getPassRate());
            data.add(map);
        }
        String[] header_cn = { "人员信息","参与培训次数","负责培训次数","考试次数/考试通过率"};
        String[] header = { "name","examCount","joinRainCount","passTrain"};
        ExcelHelper.exportData(header, header_cn, data, "人员考核统计"+ DateUtil.getYYYYMMDDHHNumberString(new Date()) +".xlsx", request, response, 1);
    }

    @GetMapping(value = "/getPersonnelStatEduList")
    public RestMessage  getPersonnelStatEduList(String id){
        Map<String,Object> result =  railLinkappSafetyEduPlanService.getPersonnelStatEduList(id);
        return RestBuilders.successBuilder(result).build();
    }

    /**
     * 自动分页
     * @param data    全部数据
     * @param size    每页条数
     * @param current 当前页码（从1开始）
     * @return PageResult<T>
     */
    public static <T> PageResult<T> paginate(List<T> data, int size, int current) {
        if (data == null || data.isEmpty() || size <= 0 || current <= 0) {
            return new PageResult<>(Collections.emptyList(), 0, size, current);
        }
        int total = data.size();
        int fromIndex = (current - 1) * size;
        int toIndex = Math.min(fromIndex + size, total);

        List<T> pageRecords;
        if (fromIndex >= total) {
            pageRecords = Collections.emptyList();
        } else {
            pageRecords = data.subList(fromIndex, toIndex);
        }
        return new PageResult<>(pageRecords, total, size, current);
    }

    /** 穿透管理大屏培训统计接口
     * @return
     */
    @GetMapping(value = "/getStatEduNumber")
    public RestMessage getStatEduNumber(){
       Map<String,Object>  result = railLinkappSafetyEduPlanService.getStatEduNumber();
       return RestBuilders.successBuilder(result).build();
    }

}
