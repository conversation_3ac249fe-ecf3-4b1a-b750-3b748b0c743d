package com.easylinkin.linkappapi.dashboard.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大屏管理
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("linkapp_dashboard")
public class Dashboard extends Model<Dashboard> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId("id")
    private String id;

    /**
     * 大屏名称
     */
    @TableField("name")
    private String name;


    /**
     * 大屏缩略图链接
     */
    @TableField("sketch")
    private String sketch;

    /**
     * 大屏链接地址
     */
    @TableField("dashboard_url")
    private String dashboardUrl;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 里程碑设置
     */
    @TableField("milestone_setting")
    private String milestoneSetting;

    /**
     * 任务设置
     */
    @TableField("task_setting")
    private String taskSetting;


    /**
     * 是否展示bim
     */
    @TableField("show_bim")
    private Boolean showBim;

    /**
     * bimId
     */
    @TableField("bim_id")
    private String bimId;

    /**
     * 集成id
     */
    @TableField(exist = false)
    private String integrateId;

    /**
     * 背景图
     */
    @TableField("background_url")
    private String backgroundUrl;

    /**
     * 预警设置
     */
    @TableField("warning_setting")
    private String warningSetting;

    /**
     * 是否删除字段 0:已删; 1:存在
     */
    @TableField("delete_state")
    @TableLogic(value = "1", delval = "0")
    private Integer deleteState = 1;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;



    /**
     * 创建人id
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
