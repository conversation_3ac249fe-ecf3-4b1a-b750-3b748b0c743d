package com.easylinkin.linkappapi.dashboard.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.dashboard.entity.Dashboard;
import com.easylinkin.linkappapi.dashboard.service.DashboardService;
import com.easylinkin.linkappapi.operatelog.LogHelper;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;

import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 大屏 Controller
 * <AUTHOR>
 * @since 2020-07-07
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

	@Resource
	private DashboardService dashboardService;

	@CommonOperateLogAnnotate(module = LogModule.DASHBOARD, desc = "新增大屏")
	@PostMapping("/addDashboard")
	public RestMessage addDashboard(@RequestBody Dashboard dashboard){
		//Assert.notNull(dashboard.getName(),"名称不能为空");
		Assert.notNull(dashboard.getDashboardUrl(),"大屏地址不能为空");
		return RestBuilders.successBuilder().success(dashboardService.addDashboard(dashboard)).build();
	}

//	@CommonOperateLogAnnotate(module = LogModule.DASHBOARD, desc = "更新大屏")
	@PostMapping("/updateDashboard")
	//@CommonOperateLogAnnotate(module = LogModule.DASHBOARD_SETTING, desc = "项目概览设置")
	@CommonOperateLogAnnotate(module = LogModule.DASHBOARD_SETTING, desc = "")
	public RestMessage updateDashboard(@RequestBody Dashboard dashboard){
		//Assert.notNull(dashboard.getName(),"名称不能为空");
//		Assert.notNull(dashboard.getDashboardUrl(),"大屏地址不能为空");
		String warningSetting = dashboard.getWarningSetting();
		if(StringUtils.isNotEmpty(warningSetting)){
			LogHelper.setContent(LogConstant.LogOperateType.DASHBOARD_WARNING_SETTING);
		}else{
			LogHelper.setContent(LogConstant.LogOperateType.DASHBOARD_OVERVIEW_SETTING);
		}
		return RestBuilders.successBuilder().success(dashboardService.updateDashboard(dashboard)).build();
	}

	@CommonOperateLogAnnotate(module = LogModule.DASHBOARD, desc = "批量删除大屏")
	@PostMapping("/deleteDashboards")
	public RestMessage deleteDashboard(@RequestBody List<String> list){
		return RestBuilders.successBuilder().success(dashboardService.deleteDashboard(list)).build();
	}


	@ApiOperation("大屏分页列表")
	@PostMapping("/getPage")
	public RestMessage getPage(@RequestBody RequestModel<Dashboard> requestModel) {
		Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
		Assert.notNull(requestModel.getPage(), "page 不能为空");
		IPage<Dashboard> record = dashboardService.getPage(requestModel.getPage(), requestModel.getCustomQueryParams());
		return RestBuilders.successBuilder().data(record).build();
	}

	@PostMapping("/getById")
	public RestMessage getDashboardById(@RequestBody Dashboard dashboard){
		Assert.notNull(dashboard.getId(),"大屏ID不能为空");
		return RestBuilders.successBuilder().data(dashboardService.getDashboardById(dashboard.getId())).build();
	}
	@PostMapping("/getDashboardByTenantId")
	public RestMessage getDashboardByTenantId(@RequestBody Dashboard dashboard){
		Assert.notNull(dashboard.getTenantId(),"大屏ID不能为空");
		return RestBuilders.successBuilder().data(dashboardService.getDashboardByTenantId(dashboard.getTenantId())).build();
	}

	@ApiOperation("获取大屏列表全集")
	@PostMapping("/getList")
	public RestMessage getList(){
		List<Dashboard> dashboards = dashboardService.getList();
		return RestBuilders.successBuilder().data(dashboards).build();
	}

	/**
	 * 获取bimface 的token
	 */
	@ApiOperation("获取bimface 的token")
	@GetMapping("/getBimToken")
	public RestMessage getBimToken(){
	 	String bimToken = dashboardService.getBimToken();
		return RestBuilders.successBuilder().data(bimToken).build();
	}


}
