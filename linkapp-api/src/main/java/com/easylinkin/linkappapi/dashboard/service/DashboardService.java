package com.easylinkin.linkappapi.dashboard.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.dashboard.entity.Dashboard;
import com.easylinkin.linkappapi.tenant.entity.LinkappTenant;
import java.util.List;

/**
 * <p>
 * 大屏管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
public interface DashboardService extends IService<Dashboard> {

	boolean addDashboard(Dashboard dashboard);

	boolean updateDashboard(Dashboard dashboard);

	boolean deleteDashboard(List<String> dashboardList);

	IPage<Dashboard> getPage(IPage page, Dashboard dashboard);

	Dashboard getDashboardByName(String name);

	Dashboard getDashboardById(String id);

	Dashboard getDashboardByTenantId(String tenantId);

	List<Dashboard> getList();

  void createIfNotExist(LinkappTenant tenant);

	/**
	 * 获取bimface token
	 * @return
	 */
	String getBimToken();
}
