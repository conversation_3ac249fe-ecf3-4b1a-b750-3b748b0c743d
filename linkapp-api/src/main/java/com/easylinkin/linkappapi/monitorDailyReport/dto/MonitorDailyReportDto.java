package com.easylinkin.linkappapi.monitorDailyReport.dto;

import com.easylinkin.linkappapi.monitorDailyReport.entity.MonitorDailyReport;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class MonitorDailyReportDto extends MonitorDailyReport {
    private String uploadUserName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

}
