package com.easylinkin.linkappapi.monitorDailyReport.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.monitorDailyReport.dto.MonitorDailyReportDto;
import com.easylinkin.linkappapi.monitorDailyReport.service.MonitorDailyReportService;
import com.easylinkin.linkappapi.monitorDailyReport.vo.MonitorDailyReportVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-16
 */
@RestController
@RequestMapping("/monitorDailyReport")
public class MonitorDailyReportController {

    @Autowired
    private MonitorDailyReportService service;

    @PostMapping("/getPage")
    @ApiOperation("查询分页")
    public RestMessage selectPage(@RequestBody RequestModel<MonitorDailyReportDto> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<MonitorDailyReportVo> record = service.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    @PostMapping("/list")
    @ApiOperation("查询列表")
    public RestMessage selectList(@RequestBody MonitorDailyReportDto monitorDailyReportDto) {
        List<MonitorDailyReportVo> record = service.selectList(monitorDailyReportDto);
        return RestBuilders.successBuilder(record).build();
    }

    @PostMapping("/uploadReport")
    @ApiOperation("上传监测日报")
    public RestMessage uploadReport(@RequestBody MonitorDailyReportDto monitorDailyReportDto){
        Assert.notNull(monitorDailyReportDto.getReportUrl(), "日报url不能为空");
        Assert.notNull(monitorDailyReportDto.getReportName(), "日报名称不能为空");

        return RestBuilders.successBuilder(service.uploadReport(monitorDailyReportDto)).build();
    }

    @GetMapping("/deleteReport/{ids}")
    @ApiOperation("删除监测日报")
    public RestMessage deleteReport(@PathVariable("ids") String ids){
        Assert.notNull(ids, "日报id不能为空");
        return RestBuilders.successBuilder(service.deleteReport(ids)).build();
    }

    @GetMapping("/getOne/{id}")
    @ApiOperation("查看监测日报详情")
    public RestMessage getOne(@PathVariable("id") Long id){
        Assert.notNull(id, "日报id不能为空");
        MonitorDailyReportVo vo = service.getOne(id);
        if (null == vo){
            return RestBuilders.errorBuilder().message("记录未找到").build();
        }
        return RestBuilders.successBuilder(vo).build();
    }

}
