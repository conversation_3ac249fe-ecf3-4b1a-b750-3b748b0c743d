package com.easylinkin.linkappapi.monitorDailyReport.service.iml;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.monitorDailyReport.dto.MonitorDailyReportDto;
import com.easylinkin.linkappapi.monitorDailyReport.entity.MonitorDailyReport;
import com.easylinkin.linkappapi.monitorDailyReport.mapper.MonitorDailyReportMapper;
import com.easylinkin.linkappapi.monitorDailyReport.service.MonitorDailyReportService;
import com.easylinkin.linkappapi.monitorDailyReport.vo.MonitorDailyReportVo;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2020-04-16
 */
@Service
@Slf4j
public class MonitorDailyReportServiceImpl extends ServiceImpl<MonitorDailyReportMapper, MonitorDailyReport> implements MonitorDailyReportService {


    @Resource
    private RedissonClient redisson;

    @Autowired
    private LinkappUserContextProducer linkappUserContextProducer;


    @Override
    public IPage<MonitorDailyReportVo> selectPage(Page page, MonitorDailyReportDto monitorDailyReportDto) {
        monitorDailyReportDto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        return baseMapper.selectList(page,monitorDailyReportDto);
    }

    @Override
    public List<MonitorDailyReportVo> selectList(MonitorDailyReportDto monitorDailyReportDto) {
        monitorDailyReportDto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        return baseMapper.selectList(monitorDailyReportDto);
    }

    @Override
    public int uploadReport(MonitorDailyReportDto monitorDailyReportDto) {
        monitorDailyReportDto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        monitorDailyReportDto.setUploadUid(linkappUserContextProducer.getCurrentRosterPersonnelId() != null ? linkappUserContextProducer.getCurrentRosterPersonnelId() : 0);
        monitorDailyReportDto.setUploadTime(new Date());
        monitorDailyReportDto.setIsDeleted(0);
        return baseMapper.insert(monitorDailyReportDto);
    }

    @Override
    public int deleteReport(String ids) {
        Integer flag = 1;
        String[] idArr = ids.split(",");
        for (String id:idArr){
            MonitorDailyReport report = baseMapper.selectById(Long.valueOf(id));
            report.setIsDeleted(1); //设置为已删除

           flag = baseMapper.updateById(report);
           if (1 != flag){
               return flag;
           }
        }
        return 1;
    }

    @Override
    public MonitorDailyReportVo getOne(Long id) {
        MonitorDailyReportDto dto = new MonitorDailyReportDto();
        dto.setId(id);
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<MonitorDailyReportVo> voList = baseMapper.selectList(dto);
        if (voList.size() >0){
            return voList.get(0);
        }
        return null;
    }
}
