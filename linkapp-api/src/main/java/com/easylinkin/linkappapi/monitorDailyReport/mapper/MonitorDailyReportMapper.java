package com.easylinkin.linkappapi.monitorDailyReport.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.monitorDailyReport.dto.MonitorDailyReportDto;
import com.easylinkin.linkappapi.monitorDailyReport.entity.MonitorDailyReport;
import com.easylinkin.linkappapi.monitorDailyReport.vo.MonitorDailyReportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 */
public interface MonitorDailyReportMapper extends BaseMapper<MonitorDailyReport> {

    IPage<MonitorDailyReportVo> selectList(Page page, @Param("monitorDailyReportDto") MonitorDailyReportDto monitorDailyReportDto);

    List<MonitorDailyReportVo> selectList( @Param("monitorDailyReportDto") MonitorDailyReportDto monitorDailyReportDto);



}
