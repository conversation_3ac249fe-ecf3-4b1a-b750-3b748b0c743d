package com.easylinkin.linkappapi.monitorDailyReport.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.monitorDailyReport.dto.MonitorDailyReportDto;
import com.easylinkin.linkappapi.monitorDailyReport.entity.MonitorDailyReport;
import com.easylinkin.linkappapi.monitorDailyReport.vo.MonitorDailyReportVo;

import java.util.List;

/**
 * <p>
 * 服务类 没有用到的模块
 * </p>

 */
public interface MonitorDailyReportService extends IService<MonitorDailyReport> {

    IPage<MonitorDailyReportVo> selectPage(Page page, MonitorDailyReportDto monitorDailyReportDto);

    List<MonitorDailyReportVo> selectList(MonitorDailyReportDto monitorDailyReportDto);

    int uploadReport(MonitorDailyReportDto monitorDailyReportDto);

    int deleteReport(String ids);

    MonitorDailyReportVo getOne(Long id);

}
