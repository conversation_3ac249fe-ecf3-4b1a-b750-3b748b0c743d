package com.easylinkin.linkappapi.monitorDailyReport.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_monitor_daily_report")
public class MonitorDailyReport extends Model<MonitorDailyReport> {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 上传类型：0人工上传，1自动生成
     */
    @TableField("upload_type")
    private Integer uploadType;

    /**
     * 监测日期
     */
    @TableField("monitor_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date monitorDate;

    /**
     *  项目id
     */
    @TableField("tenant_id")
    private String tenantId;


    /**
     * 日报名称
     */
    @TableField("report_name")
    private String reportName;

    /**
     * 附件url
     */
    @TableField("repoort_url")
    private String reportUrl;

    /**
     * 上传人
     */
    @TableField("upload_uid")
    private Long uploadUid;

    /**
     * 上传时间
     */
    @TableField("upload_time")
    private Date uploadTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除：0否1是
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
