package com.easylinkin.linkappapi.enterprise;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.quality.dto.QualityQuestionInfoDto;
import com.easylinkin.linkappapi.quality.dto.QuestionClassQueryDTO;
import com.easylinkin.linkappapi.quality.service.QualityInspectionAccountService;
import com.easylinkin.linkappapi.tenant.entity.LinkappTenant;
import com.easylinkin.linkappapi.tenant.sevice.LinkappTenantService;
import com.easylinkin.linkappapi.webcammanage.entity.EnterpriseVideoTreeQueryDTO;
import com.easylinkin.linkappapi.webcammanage.entity.ProjectAwardQueryDTO;
import com.easylinkin.linkappapi.webcammanage.service.DeviceAreaHandlerService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * aep调用企业大屏的通用接口
 */
@RestController
@RequestMapping("/enterpriseOpenApi")
public class EnterpriseController {

  private RestTemplate restTemplate = new RestTemplate();

  @Autowired
  private DeviceAreaHandlerService deviceAreaHandlerService;

  @Autowired
  private QualityInspectionAccountService qualityInspectionAccountService;

  @Autowired
  private LinkappTenantService linkappTenantService;

  @Value("${safetyweb.url}")
  private String host;

  @PostMapping("/organization/listProjectTree")
  private JSONObject listProjectTree(@RequestBody JSONObject jsonObject){
    HttpHeaders headers = getDefaultHeader();
    HttpEntity httpEntity = new HttpEntity<>(jsonObject, headers);
    return restTemplate.postForObject(host+"enterpriseOpenApi/organization/listProjectTree",
        httpEntity, JSONObject.class);
  }


  private HttpHeaders getDefaultHeader(){
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
    headers.set("Accept", "application/json");
    return headers;
  }
  @PostMapping("getEnterpriseVideoTree")
  public RestMessage getEnterpriseVideoTree(@RequestBody EnterpriseVideoTreeQueryDTO enterpriseVideoTreeQueryDTO) {
    List<Map> list = deviceAreaHandlerService.getEnterpriseVideoTree(enterpriseVideoTreeQueryDTO);
    return RestBuilders.successBuilder().data(list).build();
  }

  @GetMapping("dashBoard/projectNumAreaTree")
  public JSONObject projectNumAreaTree() {
    return restTemplate.getForObject(host+"enterpriseOpenApi/dashBoard/projectNumAreaTree",JSONObject.class);
  }


  @GetMapping("dashBoard/projectCount")
  public JSONObject projectCount() {
    return restTemplate.getForObject(host+"enterpriseOpenApi/dashBoard/projectCount",JSONObject.class);
  }

  @GetMapping("dashBoard/projectTypeStatistic")
  public JSONObject projectTypeStatistic() {
    return restTemplate.getForObject(host+"enterpriseOpenApi/dashBoard/projectTypeStatistic",JSONObject.class);
  }

  @GetMapping("dashBoard/projectProgress")
  public JSONObject projectProgress() {
    return restTemplate.getForObject(host+"enterpriseOpenApi/dashBoard/projectProgress",JSONObject.class);
  }

  @PostMapping("dashBoard/projectAwardList")
  public JSONObject projectAwardList(@RequestBody RequestModel<ProjectAwardQueryDTO> query) {
    ProjectAwardQueryDTO dto = new ProjectAwardQueryDTO();
    dto.setLevel(query.getCustomQueryParams().getLevel());
    dto.setPageIndex((int) query.getPage().getCurrent());
    dto.setPageSize((int) query.getPage().getSize());
    HttpHeaders headers = getDefaultHeader();
    HttpEntity httpEntity = new HttpEntity<>(dto, headers);
    return restTemplate.postForObject(host+"enterpriseOpenApi/dashBoard/projectAwardList", httpEntity, JSONObject.class);
  }

  @PostMapping("dashBoard/queClassStatis")
  @ApiOperation("质量检查分析-问题分类统计")
  public RestMessage queClassStatis(@RequestBody QuestionClassQueryDTO dto) {
    Assert.notNull(dto.getStartTime(), "开始时间不能为空");
    Assert.notNull(dto.getEndTime(), "结束时间不能为空");
    return qualityInspectionAccountService.queClassStatis(dto);
  }


  @PostMapping("dashBoard/projectQuestionStatis")
  @ApiOperation("质量检查分析-各项目问题出现分析")
  public RestMessage projectQuestionStatis(@RequestBody QuestionClassQueryDTO dto) {
    Assert.notNull(dto.getStartTime(), "开始时间不能为空");
    Assert.notNull(dto.getEndTime(), "结束时间不能为空");
    return qualityInspectionAccountService.projectQuestionStatis(dto);
  }

  @PostMapping("dashBoard/accountListQuery")
  @ApiOperation("检查台账列表查询")
  public RestMessage accountListQuery(@RequestBody RequestModel<QualityQuestionInfoDto> requestModel) {
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    QueryWrapper<LinkappTenant> queryWrapper = new QueryWrapper<>();
    queryWrapper.like(StringUtils.isNotBlank(requestModel.getCustomQueryParams().getProjectName()), "platform_project_name", requestModel.getCustomQueryParams().getProjectName());
    List<LinkappTenant> linkappTenantList = linkappTenantService.list(queryWrapper);
    List<Long> projectIds = linkappTenantList.stream().map(LinkappTenant::getProjectId).map(Long::valueOf).collect(Collectors.toList());
    requestModel.getCustomQueryParams().setProjectIds(projectIds);
    IPage<QualityQuestionInfoDto> record =  qualityInspectionAccountService.queryOpenApiListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }

  @GetMapping("dashBoard/getDetail/{id}")
  @ApiOperation("质量台账详细")
  public RestMessage findById(@PathVariable("id")Integer id, Integer type){
    Assert.notNull(id, "id不能为空");
    Assert.notNull(type, "type不能为空");
    QualityQuestionInfoDto qualityQuestionInfoDto =  qualityInspectionAccountService.findById(id,type);
    return RestBuilders.successBuilder().data(qualityQuestionInfoDto).build();
  }


}
