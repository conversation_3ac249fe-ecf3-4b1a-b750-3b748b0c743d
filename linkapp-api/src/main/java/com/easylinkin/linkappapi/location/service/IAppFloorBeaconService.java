package com.easylinkin.linkappapi.location.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.location.entity.AppFloorBeacon;
import com.easylinkin.linkappapi.location.vo.AppFloorBeaconVO;
import site.morn.rest.RestMessage;

/**
 * 楼层信标 服务类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface IAppFloorBeaconService extends IService<AppFloorBeacon> {
    IPage<AppFloorBeaconVO> getAppFloorList(RequestModel<AppFloorBeaconVO> requestModel);

    boolean updateStateBatch(AppFloorBeaconVO appFloorBeaconVO);

    RestMessage insertAndUpdate(AppFloorBeacon appFloorBeacon);

    AppFloorBeaconVO queryByBeaconId(Integer id);
}
