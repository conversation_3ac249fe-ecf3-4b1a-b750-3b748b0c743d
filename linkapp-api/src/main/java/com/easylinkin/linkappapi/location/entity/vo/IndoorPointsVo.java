package com.easylinkin.linkappapi.location.entity.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 胡明威
 * @Date: 2023/09/27/11:05
 * @Description:室内位置打点
 */
@Data
public class IndoorPointsVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 单体id
     */
    private Integer buildingId;

    /**
     * 单体名称
     */
    private String buildingName;

    /**
     * 单体经度
     */
    private String longitude;

    /**
     * 单体纬度
     */
    private String latitude;

    /**
     * 坐标类型(1高德地图)
     */
    private Integer coordinateType;

    /**
     * 区域坐标json
     */
    private String coordinate;

    /**
     * 单体内人员定位数量
     */
    private Integer num;
}
