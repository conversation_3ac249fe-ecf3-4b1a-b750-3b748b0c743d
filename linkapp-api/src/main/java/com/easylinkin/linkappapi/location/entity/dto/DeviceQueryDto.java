package com.easylinkin.linkappapi.location.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 胡明威
 * @Date: 2023/09/20/14:19
 * @Description:
 */
@ApiModel("安全帽分页列表查询条件")
@Data
public class DeviceQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @ApiModelProperty("设备编码")
    private String deviceCode;

    /**
     * 人员姓名
     */
    @ApiModelProperty("人员姓名")
    private String userName;

    /**
     * 定位开始时间
     */
    @ApiModelProperty("定位开始时间")
    private Date locationStartTime;

    /**
     * 定位结束时间
     */
    @ApiModelProperty("定位结束时间")
    private Date locationEndTime;

    /**
     * 绑定状态：0-未绑定 1-已绑定
     */
    @ApiModelProperty("绑定状态")
    private Integer bindState;

    /**
     * 佩戴状态：0-脱离 1-穿戴
     */
    @ApiModelProperty("佩戴状态")
    private Integer wearingState;

    /**
     * 在线状态：0-离线 1-在线
     */
    @ApiModelProperty("在线状态")
    private Integer onlineState;

    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty("位置类型 0-室内 1-室外")
    private Integer locationType;


}
