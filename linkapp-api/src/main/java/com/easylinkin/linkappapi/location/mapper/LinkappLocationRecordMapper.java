package com.easylinkin.linkappapi.location.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.location.entity.LinkappLocationRecord;
import com.easylinkin.linkappapi.location.entity.dto.DeviceRecordQueryDTO;
import com.easylinkin.linkappapi.location.entity.vo.LocationRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 定位记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface LinkappLocationRecordMapper extends BaseMapper<LinkappLocationRecord> {

    List<LocationRecordVo> queryLocationRecordList(@Param("query")DeviceRecordQueryDTO query, IPage<LocationRecordVo> page);
}
