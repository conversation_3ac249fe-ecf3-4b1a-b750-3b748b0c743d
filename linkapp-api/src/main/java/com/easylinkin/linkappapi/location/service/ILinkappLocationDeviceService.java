package com.easylinkin.linkappapi.location.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.lobar.dto.excel.ExcelResultDTO;
import com.easylinkin.linkappapi.location.entity.LinkappLocationDevice;
import com.easylinkin.linkappapi.location.entity.dto.DeviceQueryDto;
import com.easylinkin.linkappapi.location.entity.dto.IndoorLocationDto;
import com.easylinkin.linkappapi.location.entity.dto.PeopleQueryDto;
import com.easylinkin.linkappapi.openapi.dto.DatapushDTO;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.web.multipart.MultipartFile;
import site.morn.rest.RestMessage;

import java.util.List;
import java.util.Set;

/**
 * 定位设备表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface ILinkappLocationDeviceService extends IService<LinkappLocationDevice> {

    RestMessage queryList(RequestModel<DeviceQueryDto> requestModel);

    ExcelResultDTO importExcel(MultipartFile file, LinkappUser currentUserInfo) throws Exception;

    RestMessage untie(Set<Integer> ids);

    RestMessage synchronizeLocationDevice();

    void datapushHandler(DatapushDTO datapushDTO) throws JsonProcessingException;

    RestMessage queryPeopleInfo(PeopleQueryDto dto);

    RestMessage getLocationPoints();

    RestMessage getIndoorLocationInfo(IndoorLocationDto dto);

    RestMessage countNumberOfDevicePeople();


}
