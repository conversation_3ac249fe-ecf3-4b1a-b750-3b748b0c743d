package com.easylinkin.linkappapi.location.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class ScreenCountVo implements Serializable {

    /**
     * 定位管理总人数
     */
    @ApiModelProperty("定位管理总人数")
    private Integer peopleNumber;

    /**
     *  室内人员监测点数量
     */
    @ApiModelProperty("室内人员监测点数量")
    private Integer beaconNumber;

    /**
     * 工作区对象列表
     */
    private List<AreaObject> areaList;

    /**
     * 工种对象列表
     */
    private List<WorkTypeObject> workTypeList;

}
