package com.easylinkin.linkappapi.location.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.location.entity.LinkappLocationRecord;
import com.easylinkin.linkappapi.location.entity.dto.DeviceRecordQueryDTO;
import site.morn.rest.RestMessage;

/**
 * 定位记录表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface ILinkappLocationRecordService extends IService<LinkappLocationRecord> {

    RestMessage queryLocationRecordList(RequestModel<DeviceRecordQueryDTO> requestModel);
}
