package com.easylinkin.linkappapi.location.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 胡明威
 * @Date: 2023/09/27/15:07
 * @Description:
 */
@Data
public class IndoorLocationDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    @ApiModelProperty(hidden = true)
    private String tenantId;

    /**
     * 楼层id
     */
    private Integer floorId;
}
