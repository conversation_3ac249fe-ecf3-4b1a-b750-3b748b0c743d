package com.easylinkin.linkappapi.location.entity.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 胡明威
 * @Date: 2023/09/27/14:59
 * @Description:人员室内定位数据
 */
@Data
public class IndoorLocationVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 参建公司
     */
    private String companyName;

    /**
     * 定位x轴坐标
     */
    private String xPosition;

    /**
     * 定位y轴坐标
     */
    private String yPosition;

    /**
     * 工种类型名称
     */
    private String workTypeName;
}
