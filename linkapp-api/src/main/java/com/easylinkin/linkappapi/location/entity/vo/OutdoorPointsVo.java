package com.easylinkin.linkappapi.location.entity.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 胡明威
 * @Date: 2023/09/27/11:21
 * @Description:室外位置打点
 */
@Data
public class OutdoorPointsVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 工种类型名称
     */
    private String workTypeName;
}
