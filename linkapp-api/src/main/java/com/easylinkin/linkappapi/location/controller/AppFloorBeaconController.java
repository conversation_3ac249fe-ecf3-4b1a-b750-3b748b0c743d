package com.easylinkin.linkappapi.location.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.location.entity.AppFloorBeacon;
import com.easylinkin.linkappapi.location.service.IAppFloorBeaconService;
import com.easylinkin.linkappapi.location.vo.AppFloorBeaconVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;


/**
 * 楼层信标管理
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Api(tags = "楼层信标管理")
@RestController
@RequestMapping("/location/appFloorBeacon")
public class AppFloorBeaconController {
    @Autowired
    private IAppFloorBeaconService appFloorBeaconService;

    /**
     * 分页列表查询
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value="分页列表查询", notes="分页列表查询")
    @PostMapping(value = "/listPage")
    public RestMessage queryPageList(@RequestBody RequestModel<AppFloorBeaconVO> requestModel) {
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<AppFloorBeaconVO> pageList = appFloorBeaconService.getAppFloorList(requestModel);
        return RestBuilders.successBuilder().data(pageList).build();
    }

    /**
     * 保存
     *
     * @param appFloorBeacon
     * @return
     */
    @ApiOperation(value="保存", notes="保存")
    @PostMapping(value = "/save")
    public RestMessage save(@RequestBody AppFloorBeacon appFloorBeacon) {
        return appFloorBeaconService.insertAndUpdate(appFloorBeacon);
    }

    /**
     *   通过id删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value="通过id删除", notes="通过id删除")
    @DeleteMapping(value = "/delete")
    public RestMessage delete(@RequestParam("id") Integer id) {
        appFloorBeaconService.removeById(id);
        return RestBuilders.successBuilder().message("删除成功").build();
    }

    /**
     *  批量删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value="批量删除", notes="批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public RestMessage deleteBatch(@RequestParam("ids") String ids) {
        this.appFloorBeaconService.removeByIds(Arrays.asList(ids.split(",")));
        return RestBuilders.successBuilder().message("批量删除成功").build();
    }

    /**
     * 批量禁用
     *
     * @param appFloorBeaconVO
     * @return
     */
    @ApiOperation(value="批量禁用")
    @PostMapping(value = "/updateStateBatch")
    public RestMessage updateStateBatch(@RequestBody AppFloorBeaconVO appFloorBeaconVO) {
        return RestBuilders.successBuilder().data(appFloorBeaconService.updateStateBatch(appFloorBeaconVO)).build();
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value="通过id查询", notes="通过id查询")
    @GetMapping(value = "/queryById")
    public RestMessage queryById(@RequestParam("id") Integer id) {
        AppFloorBeaconVO appFloorBeaconVO = appFloorBeaconService.queryByBeaconId(id);
        if(appFloorBeaconVO == null) {
            return RestBuilders.errorBuilder().message("未找到对应数据").build();
        }
        return RestBuilders.successBuilder().data(appFloorBeaconVO).build();
    }
}
