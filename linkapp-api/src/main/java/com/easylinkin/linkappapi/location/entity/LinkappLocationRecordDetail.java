package com.easylinkin.linkappapi.location.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 定位记录明细表
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("linkapp_location_record_detail")
public class LinkappLocationRecordDetail implements Serializable{
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    private Integer id;

    /**
     * 关联定位记录表id
     */
    @TableField(value = "link_id_")
    private Long linkId;

    /**
     * 设备编号(信标)
     */
    @TableField(value = "device_code_")
    private String deviceCode;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id_")
    private String tenantId;

    /**
     * 安装位置
     */
    @TableField(value = "indoor_location_")
    private String indoorLocation;

    /**
     * 高度
     */
    @TableField(value = "height_")
    private Float height;

    /**
     * 信号强度
     */
    @TableField(value = "signal_strength_")
    private String signalStrength;

    /**
     * 是否删除字段 1:存在; 0:删除
     */
    @TableField(value = "delete_state_")
    private Boolean deleteState;

    /**
     * 创建人
     */
    @TableField(value = "creator_")
    private String creator;

    /**
     * 创建日期
     */
    @TableField(value = "create_time_")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "modifier_")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField(value = "remark_")
    private String remark;

}
