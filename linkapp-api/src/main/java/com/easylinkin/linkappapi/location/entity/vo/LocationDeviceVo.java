package com.easylinkin.linkappapi.location.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 胡明威
 * @Date: 2023/09/20/15:19
 * @Description:
 */
@Data
public class LocationDeviceVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("定位设备id")
    private Integer id;

    @ApiModelProperty("设备编码")
    private String deviceCode;

    @ApiModelProperty("绑定状态")
    private String bindState;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("人员姓名")
    private String userName;

    @ApiModelProperty("班组名称")
    private String groupName;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("设备类型")
    private String deviceType;

    @ApiModelProperty("电池电量")
    private String battery;

    @ApiModelProperty("在线状态 0-离线 1-在线")
    private String onlineState;

    @ApiModelProperty("穿戴状态 0-脱离 1-穿戴")
    private String wearingState;

    @ApiModelProperty("位置类型 0-室内 1-室外")
    private String locationType;

    @ApiModelProperty("室内位置")
    private String indoorLocation;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("定位时间")
    private String locationTime;

    @ApiModelProperty("工种")
    private String workTypeName;


    @ApiModelProperty("区域")
    private Integer peopleArea;


    @ApiModelProperty("楼层id")
    private String floorId;

    @ApiModelProperty("身份证号")
    private String cardNo;
}
