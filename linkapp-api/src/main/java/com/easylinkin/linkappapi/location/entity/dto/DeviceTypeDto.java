package com.easylinkin.linkappapi.location.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 胡明威
 * @Date: 2023/09/22/15:44
 * @Description:
 */
@ApiModel("通过设备类型查询设备信息")
@Data
public class DeviceTypeDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;
}
