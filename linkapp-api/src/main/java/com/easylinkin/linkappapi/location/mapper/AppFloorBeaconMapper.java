package com.easylinkin.linkappapi.location.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.location.entity.AppFloorBeacon;
import com.easylinkin.linkappapi.location.vo.AppFloorBeaconVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 楼层信标 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface AppFloorBeaconMapper extends BaseMapper<AppFloorBeacon> {
    IPage<AppFloorBeaconVO> getBeaconList(Page page, @Param("appFloorBeaconVO") AppFloorBeaconVO appFloorBeaconVO);

    List<AppFloorBeaconVO> getBeaconList(@Param("appFloorBeaconVO") AppFloorBeaconVO appFloorBeaconVO);

    /**
     * 通过信标编码获取信标列表及其所在楼栋经纬度
     * @param appFloorBeaconVO
     * @return
     */
    List<AppFloorBeaconVO> getBeaconListByCode(@Param("appFloorBeaconVO") AppFloorBeaconVO appFloorBeaconVO);
}
