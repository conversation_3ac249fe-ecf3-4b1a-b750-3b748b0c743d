package com.easylinkin.linkappapi.location.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.location.entity.LinkappLocationRecord;
import com.easylinkin.linkappapi.location.entity.LinkappLocationRecordDetail;
import com.easylinkin.linkappapi.location.entity.dto.DeviceRecordQueryDTO;
import com.easylinkin.linkappapi.location.service.ILinkappLocationRecordDetailService;
import com.easylinkin.linkappapi.location.service.ILinkappLocationRecordService;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;


/**
 * 定位记录
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Api(tags = "定位记录")
@RestController
@RequestMapping("/location/linkappLocationRecord")
public class LinkappLocationRecordController {
    @Autowired
    private ILinkappLocationRecordService linkappLocationRecordService;

    @Autowired
    private LinkappUserContextProducer linkappUserContextProducer;

    @Autowired
    private ILinkappLocationRecordDetailService linkappLocationRecordDetailService;

    /**
     * 分页列表查询
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value="分页列表查询", notes="分页列表查询")
    @PostMapping(value = "/listPage")
    public RestMessage queryPageList(@RequestBody RequestModel<DeviceRecordQueryDTO> requestModel) {
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        return linkappLocationRecordService.queryLocationRecordList(requestModel);
    }

    /**
     * 记录详情查询
     * @param dto
     * @return
     */
    @ApiOperation("记录详情查询")
    @PostMapping("/getDetail")
    public RestMessage getDetail(@RequestBody DeviceRecordQueryDTO dto) {
        Assert.notNull(dto.getId(), "记录id不能为空");
        QueryWrapper<LinkappLocationRecordDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("link_id_", dto.getId());
        List<LinkappLocationRecordDetail> list = linkappLocationRecordDetailService.list(queryWrapper);
        return RestBuilders.successBuilder(list).build();
    }

    /**
     * 查询人员历史位置
     * @param dto
     * @return
     */
    @ApiOperation("查询人员历史位置")
    @PostMapping("/getHistory")
    public RestMessage getHistory(@RequestBody RequestModel<DeviceRecordQueryDTO> dto) {
        Assert.notNull(dto.getCustomQueryParams().getId(), "id不能为空");
        Assert.notNull(dto.getPage(), "page 不能为空");
        QueryWrapper<LinkappLocationRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_info_", dto.getCustomQueryParams().getId())
                .ge(StringUtils.isNotBlank(dto.getCustomQueryParams().getBeginTime()), "create_time_", dto.getCustomQueryParams().getBeginTime())
                .le(StringUtils.isNotBlank(dto.getCustomQueryParams().getEndTime()), "create_time_", dto.getCustomQueryParams().getEndTime())
                .orderByDesc("create_time_");
        IPage page = linkappLocationRecordService.page(dto.getPage(), queryWrapper);
        return RestBuilders.successBuilder(page).build();
    }

    /**
     * 保存
     *
     * @param linkappLocationRecord
     * @return
     */
    @ApiOperation(value="保存", notes="保存")
    @PostMapping(value = "/save")
    public RestMessage save(@RequestBody LinkappLocationRecord linkappLocationRecord) {
        if(linkappLocationRecord.getId() != null){
            linkappLocationRecordService.updateById(linkappLocationRecord);
        } else{
            linkappLocationRecordService.save(linkappLocationRecord);
        }
        return RestBuilders.successBuilder().message("保存成功" ).build();
    }

    /**
     *   通过id删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value="通过id删除", notes="通过id删除")
    @DeleteMapping(value = "/delete")
    public RestMessage delete(@RequestParam("id") Integer id) {
        linkappLocationRecordService.removeById(id);
        return RestBuilders.successBuilder().message("删除成功").build();
    }

    /**
     *  批量删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value="批量删除", notes="批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public RestMessage deleteBatch(@RequestParam("ids") String ids) {
        this.linkappLocationRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return RestBuilders.successBuilder().message("批量删除成功").build();
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value="通过id查询", notes="通过id查询")
    @GetMapping(value = "/queryById")
    public RestMessage queryById(@RequestParam("id") Integer id) {
        LinkappLocationRecord linkappLocationRecord = linkappLocationRecordService.getById(id);
        if(linkappLocationRecord == null) {
            return RestBuilders.errorBuilder().message("未找到对应数据").build();
        }
        return RestBuilders.successBuilder().data(linkappLocationRecord).build();
    }
}
