package com.easylinkin.linkappapi.location.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 胡明威
 * @Date: 2023/10/18/15:01
 * @Description:
 */
@Data
public class DeviceRecordQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 定位开始时间
     */
    private String beginTime;

    /**
     * 定位结束时间
     */
    private String endTime;

    /**
     * 租户id
     */
    private String tenantId;

    private String id;
}
