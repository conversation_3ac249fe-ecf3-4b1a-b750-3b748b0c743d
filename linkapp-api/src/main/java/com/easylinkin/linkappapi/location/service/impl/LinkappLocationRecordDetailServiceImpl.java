package com.easylinkin.linkappapi.location.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.location.entity.LinkappLocationRecordDetail;
import com.easylinkin.linkappapi.location.mapper.LinkappLocationRecordDetailMapper;
import com.easylinkin.linkappapi.location.service.ILinkappLocationRecordDetailService;
import org.springframework.stereotype.Service;

/**
 * 定位记录明细表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Service
public class LinkappLocationRecordDetailServiceImpl extends ServiceImpl<LinkappLocationRecordDetailMapper, LinkappLocationRecordDetail> implements ILinkappLocationRecordDetailService {
}