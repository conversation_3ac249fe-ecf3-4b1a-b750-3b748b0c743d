package com.easylinkin.linkappapi.location.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 楼层信标
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("app_floor_beacon")
public class AppFloorBeacon implements Serializable{
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    private Integer id;

    /**
     * 设备编号
     */
    @TableField(value = "device_code_")
    private String deviceCode;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id_")
    private String tenantId;

    /**
     * 安装时间
     */
    @TableField(value = "bind_time_")
    private Date bindTime;

    /**
     * 启用状态：0-禁用，1-启用
     */
    @TableField(value = "state_")
    private Boolean state;

    /**
     * 电池电量
     */
    @TableField(value = "battery_electricity_")
    private Integer batteryElectricity;

    /**
     * 楼层id
     */
    @TableField(value = "floor_id_")
    private Integer floorId;

    /**
     * 平面图x轴坐标
     */
    @TableField(value = "beacon_x_position_")
    private Float beaconXPosition;

    /**
     * 平面图y轴坐标
     */
    @TableField(value = "beacon_y_position_")
    private Float beaconYPosition;

    /**
     * 创建人
     */
    @TableField(value = "creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time_")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "modifier_id_")
    private Long modifierId;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time_")
    private Date modifyTime;

}
