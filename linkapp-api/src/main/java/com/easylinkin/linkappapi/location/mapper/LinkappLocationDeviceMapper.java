package com.easylinkin.linkappapi.location.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.lobar.dto.UserProjectDTO;
import com.easylinkin.linkappapi.location.entity.LinkappLocationDevice;
import com.easylinkin.linkappapi.location.entity.dto.DeviceQueryDto;
import com.easylinkin.linkappapi.location.entity.dto.DeviceTypeDto;
import com.easylinkin.linkappapi.location.entity.dto.IndoorLocationDto;
import com.easylinkin.linkappapi.location.entity.dto.PeopleQueryDto;
import com.easylinkin.linkappapi.location.entity.vo.*;
import com.easylinkin.linkappapi.location.vo.AppFloorBeaconVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 定位设备表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface LinkappLocationDeviceMapper extends BaseMapper<LinkappLocationDevice> {

    List<LocationDeviceVo> queryList(@Param("query") DeviceQueryDto queryDto, IPage<LocationDeviceVo> page);
    List<LocationDeviceVo> queryList(@Param("query") DeviceQueryDto queryDto);

    List<Device> queryProjectDevice(DeviceTypeDto dto);

    PeopleInfoVo queryPeopleInfo(PeopleQueryDto dto);

    List<IndoorPointsVo> queryIndoorPoints(@Param("tenantId") String tenantId);

    List<OutdoorPointsVo> queryOutdoorPoints(@Param("tenantId") String tenantId);

    List<IndoorLocationVo> queryIndoorLocationInfo(IndoorLocationDto dto);

    AppFloorBeaconVO queryFloorInfo(AppFloorBeaconVO appFloorBeaconVO);

    UserProjectDTO queryUserProjectInfo(String tenantId, String cardNo);

    void updateDeviceOnLineStatus();
}
