package com.easylinkin.linkappapi.location.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.easylinkin.linkappapi.location.entity.AppFloorBeacon;
import lombok.Data;

import java.util.Date;

@Data
public class AppFloorBeaconVO extends AppFloorBeacon {

    /**
     * 批量id
     */
    private String ids;

    private Date startTime;

    private Date endTime;

    /**
     * 创建人昵称
     */
    private String creator;

    /**
     * 安装位置
     */
    private String location;

    /**
     * 楼层ids
     */
    private String floors;

    /**
     * 对应楼层图纸文件
     */
    private String fileUrl;

    /**
     * 楼层最小高度
     */
    private Float minHeight;

    /**
     * 楼层最大高度
     */
    private Float maxHeight;

    /**
     * 经度
     */
    private Float longitude;

    /**
     * 纬度
     */
    private Float latitude;

}
