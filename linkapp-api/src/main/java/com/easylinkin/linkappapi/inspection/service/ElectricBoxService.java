package com.easylinkin.linkappapi.inspection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricBox;
import com.easylinkin.linkappapi.messagecenter.entity.MessageCenter;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 配电箱表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface ElectricBoxService extends IService<ElectricBox> {

  IPage<ElectricBox> queryListByPage(RequestModel<ElectricBox> requestModel);

  /**
   * 增加
   * @param electricBox
   */
  void insert(ElectricBox electricBox);

  /**
   * 根据id编辑
   * @param electricBox
   */
  void updateOne(ElectricBox electricBox);

  /**
   * @Description: 删除配电箱（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  void delBatch(Set<String> ids);

  /**
   * @Description: 查询可选负责人列表
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  List<LinkappUser> queryLeads();

  /**
   * 查询未巡检的配电箱
   *
   * @return
   */
  List<ElectricBox> listUnChecked();

  /**
   * 根据条件，分页查询 未被巡检过的
   * @param requestModel
   * @return
   */
  IPage<ElectricBox> listUnCheckedDetails(RequestModel<MessageCenter> requestModel);

  List<Device> getMonitorDevice(String id);

  void checkMonitorDevice(ElectricBox electricBox);

  /**
   * 查看当天的巡检记录
   * @param type 1已巡检 0未巡检
   */
  IPage<ElectricBoxDTO> todayCheck(RequestModel<ElectricBoxDTO> requestModel);
}
