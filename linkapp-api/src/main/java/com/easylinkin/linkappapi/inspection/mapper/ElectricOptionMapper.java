package com.easylinkin.linkappapi.inspection.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.inspection.entity.ElectricOption;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 配电箱巡检项表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
public interface ElectricOptionMapper extends BaseMapper<ElectricOption> {

  IPage<ElectricOption> queryListByPage(Page page, ElectricOption electricOption);
}
