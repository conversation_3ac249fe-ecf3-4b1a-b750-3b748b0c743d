package com.easylinkin.linkappapi.inspection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxWarnGroupByDeviceDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxWarnGroupByTypeDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricBox;
import com.easylinkin.linkappapi.messagecenter.entity.MessageCenter;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 配件箱表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
public interface ElectricBoxMapper extends BaseMapper<ElectricBox> {

  IPage<ElectricBox> queryListByPage(Page page, @Param("electricBox") ElectricBox electricBox);

  IPage<ElectricBox> getDone(Page page, @Param("electricBoxDTO") ElectricBoxDTO electricBoxDTO);

  ElectricBox findById(String id);

  List<ElectricBox> listUnChecked(@Param("start") Date start, @Param("end") Date end);

  IPage<ElectricBox> listUnCheckedDetails(Page page, @Param("messageCenter") MessageCenter messageCenter, @Param("start") Date start, @Param("date10") Date date10);

  void removeMonitorDevice(@Param("electricBox")ElectricBox electricBox);

  List<ElectricBoxDTO> queryRefElectricBoxDevices(ElectricBox electricBox);

  List<ElectricBoxWarnGroupByTypeDTO> countElectricBoxWarnGroupByType(ElectricBoxDTO electricBoxDTO);

  List<ElectricBoxWarnGroupByDeviceDTO> countElectricBoxWarnGroupByDevice(ElectricBoxDTO electricBoxDTO);

  /**
   * 计算关联的设备code
   * @param tenantId
   * @return
   */
  List<String> findMonitorDeviceCodes(String tenantId);

  /**
   * 查看当天的巡检记录
   * @param requestModel 1已巡检 0未巡检
   */
    IPage<ElectricBoxDTO> todayCheck(Page page, @Param("electricBox")ElectricBoxDTO electricBoxDTO);


}
