package com.easylinkin.linkappapi.inspection.dto;

import com.easylinkin.linkappapi.inspection.entity.ElectricBoxRecord;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/08/17
 * @description
 */
@Data
public class ElectricBoxRecordDTO extends ElectricBoxRecord {
  private String startTime;
  private String endTime;
  /**
   *  0:逐时,1:逐月30天 默认逐时0
   */
  private Integer dailyOrMonthly;
  private String startTimeH;
  private String endTimeH;
}
