package com.easylinkin.linkappapi.inspection.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配电箱数据记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_electric_box_record")
public class ElectricBoxRecord extends Model<ElectricBoxRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配电箱ID 
     */
    @TableField("electric_box_id_")
    private String electricBoxId;

    /**
     * 设备id
     */
    @TableField("device_code")
    private String deviceCode;

    /**
     * 租户id
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * 通道1温度 单位℃
     */
    @TableField("temperature_1")
    private Double temperature1;

    /**
     * 通道2温度 单位℃
     */
    @TableField("temperature_2")
    private Double temperature2;

    /**
     * 通道3温度 单位℃
     */
    @TableField("temperature_3")
    private Double temperature3;

    /**
     * 通道4温度 单位℃
     */
    @TableField("temperature_4")
    private Double temperature4;

    /**
     * 剩余电流 单位mA
     */
    @TableField("residual_current")
    private Double residualCurrent;

    /**
     * 有功总功率 单位W
     */
    @TableField("active_power")
    private Double activePower;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
