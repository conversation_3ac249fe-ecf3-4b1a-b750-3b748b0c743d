package com.easylinkin.linkappapi.inspection.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.inspection.dto.RailLinkappElectricFireRecordDTO;
import com.easylinkin.linkappapi.inspection.entity.RailLinkappElectricFireRecord;
import com.easylinkin.linkappapi.inspection.vo.RailLinkappElectricFireRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RailLinkappElectricFireRecordMapper extends BaseMapper<RailLinkappElectricFireRecord>{
    List<RailLinkappElectricFireRecord> getList(RailLinkappElectricFireRecordDTO recordDTO);

    /**
     * 用电安全统计,时间聚合查询
     */
    List<RailLinkappElectricFireRecord> selectByDateOrTime(@Param("recordDto") RailLinkappElectricFireRecordDTO recordDTO);

    List<RailLinkappElectricFireRecord> selectByTwentyFourHour(@Param("recordDto") RailLinkappElectricFireRecordDTO recordDTO);

    List<RailLinkappElectricFireRecord> selectByThirtyDays(@Param("recordDto") RailLinkappElectricFireRecordDTO recordDTO);

    List<RailLinkappElectricFireRecordVO> codeByNewRecords(@Param("tenantId") String tenantId, @Param("codes") List<String> codes);
}
