package com.easylinkin.linkappapi.inspection.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.inspection.dto.RailLinkappElectricFireRecordDTO;
import com.easylinkin.linkappapi.inspection.entity.RailLinkappElectricFireRecord;
import com.easylinkin.linkappapi.inspection.vo.RailLinkappElectricFireRecordVO;
import com.easylinkin.linkappapi.openapi.dto.DatapushDTO;

import java.util.List;
import java.util.Map;

public interface IRailLinkappElectricFireRecordService extends IService<RailLinkappElectricFireRecord> {
    /**
     * 电气火灾流水处理
     * @param datapushDTO
     */
    void datapushHandler(DatapushDTO datapushDTO);

    /**
     *按24小时或者按月统计电气火灾漏电情况
     * @param railLinkappElectricFireRecordDTO
     * @return
     */
    Map<String, Object> boxRecordByhoursOrMonthly(RailLinkappElectricFireRecordDTO railLinkappElectricFireRecordDTO);

    /**
     * 按24小时或者按月统计电气火灾温度情况
     * @param railLinkappElectricFireRecordDTO
     * @return
     */
    Map<String, Object> temperatureLineHursOrMonthly(RailLinkappElectricFireRecordDTO railLinkappElectricFireRecordDTO);

    List<RailLinkappElectricFireRecordVO> codeByNewRecords(String tenantId, List<String> value);
}
