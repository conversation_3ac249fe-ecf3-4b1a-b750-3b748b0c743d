package com.easylinkin.linkappapi.inspection.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus;
import com.easylinkin.linkappapi.deviceattributestatus.service.DeviceAttributeStatusService;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxWarnGroupByDeviceDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxWarnGroupByTypeDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricCheckDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricBox;
import com.easylinkin.linkappapi.inspection.mapper.ElectricBoxMapper;
import com.easylinkin.linkappapi.inspection.service.ElectricBoxBiService;
import com.easylinkin.linkappapi.inspection.service.ElectricCheckService;
import com.easylinkin.linkappapi.manageinfo.entity.ManageInfo;
import com.easylinkin.linkappapi.ruleengine.entity.RuleEngine;
import com.easylinkin.linkappapi.ruleengine.mapper.RuleEngineMapper;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/08/16
 * @description
 */
@Service
public class ElectricBoxBiServiceImpl implements ElectricBoxBiService {

  @Resource
  private ElectricBoxMapper electricBoxMapper;

  @Autowired
  private LinkappUserContextProducer linkappUserContextProducer;

  @Autowired
  private DeviceAttributeStatusService deviceAttributeStatusService;

  @Autowired
  private ElectricCheckService electricCheckService;

  @Override
  public List<ElectricBoxDTO> queryRefElectricBoxDevices(ElectricBox electricBox) {
    String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
    electricBox.setTenantId(tenantId);
    List<ElectricBoxDTO> electricBoxList = electricBoxMapper.queryRefElectricBoxDevices(electricBox);
    if(CollectionUtil.isNotEmpty(electricBoxList)){
      electricBoxList.forEach(p->{
        // 设备状态
        Device device = new Device();
        device.setModelId(-1);
        device.setId(p.getId());
        device.setDeviceTypeName("配电箱");
        List<DeviceAttributeStatus> deviceRealtimeDataForBigScreen = deviceAttributeStatusService
            .getDeviceRealtimeDataForBigScreen(device);
        p.setAttributeStatuses(deviceRealtimeDataForBigScreen);
        // 今日巡检状态
        ElectricBoxDTO electricBoxDTO = new ElectricBoxDTO();
        electricBoxDTO.setId(p.getId());
        electricBoxDTO.setCheckTime(new Date());
        ElectricCheckDTO checkStatusByIdAndDate = electricCheckService
            .findCheckStatusByIdAndDate(electricBoxDTO);
        if(null == checkStatusByIdAndDate){
          p.setCheckStatus(0);
        }else{
          if(checkStatusByIdAndDate.getResult()){
            p.setCheckStatus(1);
          }else{
            p.setCheckStatus(2);
          }
        }

      });
    }
    return electricBoxList;
  }

  @Autowired
  private RuleEngineMapper ruleEngineMapper;

  @Override
  public List<ElectricBoxWarnGroupByTypeDTO> countElectricBoxWarnGroupByType(ElectricBoxDTO electricBoxDTO) {
    List<ElectricBoxWarnGroupByTypeDTO> result = new ArrayList<>();
    //20221010增加补全数据
    //1根据租户id、规则类型、规则名称前缀
    String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
    String prefixName = "电气火灾-";
    QueryWrapper<RuleEngine> reqw = new QueryWrapper<>();
    reqw.eq("tenant_id", tenantId);
    reqw.likeRight(StringUtils.isNotBlank(prefixName), "name", prefixName);
    List<RuleEngine> ruleEngineList = ruleEngineMapper.selectList(reqw);
    if(CollectionUtil.isEmpty(ruleEngineList)){
      return null;
    }
    ruleEngineList.forEach(p->{
      ElectricBoxWarnGroupByTypeDTO electricBoxWarnGroupByTypeDTO = new ElectricBoxWarnGroupByTypeDTO();
      electricBoxWarnGroupByTypeDTO.setRuleId(p.getId());
      electricBoxWarnGroupByTypeDTO.setRuleName(p.getName());
      electricBoxWarnGroupByTypeDTO.setCount(0);
      result.add(electricBoxWarnGroupByTypeDTO);
    });
    electricBoxDTO.setTenantId(tenantId);
    List<ElectricBoxWarnGroupByTypeDTO> list = electricBoxMapper.countElectricBoxWarnGroupByType(electricBoxDTO);
    if(CollectionUtil.isNotEmpty(list)){
      Map<String, ElectricBoxWarnGroupByTypeDTO> map = list.stream()
          .collect(Collectors.toMap(ElectricBoxWarnGroupByTypeDTO::getRuleId, c -> c));
      result.stream().forEach(p->{
        if(map.containsKey(p.getRuleId())){
          ElectricBoxWarnGroupByTypeDTO typeDTO = map.get(p.getRuleId());
          p.setCount(typeDTO.getCount());
        }
      });
    }
    return result;
  }

  @Override
  public List<ElectricBoxWarnGroupByDeviceDTO> countElectricBoxWarnGroupByDevice(ElectricBoxDTO electricBoxDTO) {
    List<ElectricBoxWarnGroupByDeviceDTO> result = new ArrayList<>();
    // 查询所有含配电箱设备
    ElectricBox electricBox = new ElectricBox();
    String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
    electricBox.setTenantId(tenantId);
    List<ElectricBoxDTO> electricBoxList = electricBoxMapper.queryRefElectricBoxDevices(electricBox);
    if(CollectionUtil.isEmpty(electricBoxList)){
      return null;
    }
    electricBoxList.forEach(p->{
      ElectricBoxWarnGroupByDeviceDTO deviceDTO = new ElectricBoxWarnGroupByDeviceDTO();
      deviceDTO.setCode(p.getCode());
      deviceDTO.setPosition(p.getPosition());
      deviceDTO.setCount(0);
      result.add(deviceDTO);
    });
    electricBoxDTO.setTenantId(tenantId);
    List<ElectricBoxWarnGroupByDeviceDTO> list = electricBoxMapper.countElectricBoxWarnGroupByDevice(electricBoxDTO);
    if(CollectionUtil.isNotEmpty(list)){
      Map<String, ElectricBoxWarnGroupByDeviceDTO> map = list.stream()
          .collect(Collectors.toMap(ElectricBoxWarnGroupByDeviceDTO::getCode, c -> c));
      result.stream().forEach(p->{
        if(map.containsKey(p.getCode())){
          ElectricBoxWarnGroupByDeviceDTO deviceDTO = map.get(p.getCode());
          p.setCount(deviceDTO.getCount());
        }
      });
    }
    return result;
  }
}
