package com.easylinkin.linkappapi.inspection.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配件箱表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_electric_box")
public class ElectricBox extends Model<ElectricBox> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId("id")
    private String id;

    /**
     * 项目ID(linkapp_tenant)
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * 设备编号
     */
    @TableField("code_")
    private String code;

    /**
     * 设备类型(0主配电箱,1一级配电箱,2二级配电箱)
     */
    @TableField("type_")
    private Integer type;

    /**
     * 设置位置
     */
    @TableField("position_")
    private String position;

    /**
     * 负责人id
     */
    @TableField("lead_id_")
    private String leadId;

    /**
     * 电路图url
     */
    @TableField("circuitry_")
    private String circuitry;

    /**
     * 电工证url
     */
    @TableField("electrician_")
    private String electrician;

    /**
     * 二维码url
     */
    @TableField("qr_code")
    private String qrCode;

    /**
     * 电气火灾监控设备code
     */
    @TableField("monitor_device_code_")
    private String monitorDeviceCode;

    /**
     * 1已删除 0未删除
     */
    @TableField("is_delete_")
    private Integer isDelete;

    /**
     * 启用状态
     * 0禁用 1启用
     */
    @TableField("use_state_")
    private Integer useState;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * id集合
     * @return
     */
    @TableField(exist = false)
    private Set<String> ids;

    /**
     * 负责人姓名
     * @return
     */
    @TableField(exist = false)
    private String leadName;

    /**
     * 联系方式
     */
    @TableField(exist = false)
    private String telephone;

    /**
     * 微信扫描二维码路径
     */
    @TableField(exist = false)
    private String qrCodeUrl;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
