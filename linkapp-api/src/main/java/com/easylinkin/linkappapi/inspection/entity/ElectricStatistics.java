package com.easylinkin.linkappapi.inspection.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配件箱巡检统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_electric_statistics")
public class ElectricStatistics extends Model<ElectricStatistics> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId("id")
    private String id;

    /**
     * 项目ID(linkapp_tenant)
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * 考勤日期
     */
    @TableField("record_time_")
    private Date recordTime;

    /**
     * 电箱总数
     */
    @TableField("sum_")
    private Integer sum;

    /**
     * 已巡检数
     */
    @TableField("done_")
    private Integer done;

    /**
     * 所有配电箱的id集合，逗号隔开
     */
    @TableField("sum_id_")
    private String sumId;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * id集合
     * @return
     */
    @TableField(exist = false)
    private Set<String> ids;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
