package com.easylinkin.linkappapi.inspection.dto;

import com.easylinkin.linkappapi.inspection.entity.RailLinkappElectricFireRecord;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/20 上午 9:20
 */
@Data
public class RailLinkappElectricFireRecordDTO extends RailLinkappElectricFireRecord {
    private String startTime;
    private String endTime;
    /**
     *  0:逐时,1:逐月30天 默认逐时0
     */
    private Integer dailyOrMonthly;
    private String startTimeH;
    private String endTimeH;
}
