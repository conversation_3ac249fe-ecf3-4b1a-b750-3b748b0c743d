package com.easylinkin.linkappapi.inspection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.inspection.entity.ElectricOption;
import java.util.List;

/**
 * <p>
 * 配电箱巡检项表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface ElectricOptionService extends IService<ElectricOption> {

  IPage<ElectricOption> queryListByPage(RequestModel<ElectricOption> requestModel);

  /**
   * 增加
   * @param electricOption
   */
  void insert(ElectricOption electricOption);

  /**
   * 根据id编辑
   * @param electricOption
   */
  void updateOne(ElectricOption electricOption);

  /**
   * @Description: 查询巡检项
   * <AUTHOR> yuan<PERSON>
   * @date 2020/11/04 11:42
   */
  List<ElectricOption> options();

  List<ElectricOption> optionsByTenantId(String tenantId);
}
