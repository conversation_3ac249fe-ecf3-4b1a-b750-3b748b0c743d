package com.easylinkin.linkappapi.inspection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.inspection.dto.ElectricStatisticsDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricBox;
import com.easylinkin.linkappapi.inspection.entity.ElectricStatistics;

/**
 * <p>
 * 配电箱巡检统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface ElectricStatisticsService extends IService<ElectricStatistics> {

  IPage<ElectricStatisticsDTO> queryListByPage(RequestModel<ElectricStatisticsDTO> requestModel);

  /**
   * 增加
   * @param electricStatistics
   */
  void insert(ElectricStatistics electricStatistics);

  /**
   * 根据id编辑
   * @param electricStatistics
   */
  void updateOne(ElectricStatistics electricStatistics);

  /**
   * 更新今天的统计
   */
  void countToday();

  /**
   * @Description: 未巡检配电箱
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  IPage<ElectricBox> undoList(RequestModel<ElectricStatisticsDTO> requestModel);

  /**
   * @Description: 已巡检配电箱
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  IPage<ElectricBox> done(RequestModel<ElectricStatisticsDTO> requestModel);

  /**
   * @Description: 统计当天巡检情况
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  void countToday(String tenantId,Integer done);
}
