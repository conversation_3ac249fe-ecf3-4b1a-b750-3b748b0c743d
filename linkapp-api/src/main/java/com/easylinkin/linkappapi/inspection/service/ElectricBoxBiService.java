package com.easylinkin.linkappapi.inspection.service;

import com.easylinkin.linkappapi.inspection.dto.ElectricBoxDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxWarnGroupByDeviceDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxWarnGroupByTypeDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricBox;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/08/16
 * @description
 */
public interface ElectricBoxBiService {

  List<ElectricBoxDTO> queryRefElectricBoxDevices(ElectricBox electricBox);

  List<ElectricBoxWarnGroupByTypeDTO> countElectricBoxWarnGroupByType(ElectricBoxDTO electricBoxDTO);

  List<ElectricBoxWarnGroupByDeviceDTO> countElectricBoxWarnGroupByDevice(ElectricBoxDTO electricBoxDTO);
}
