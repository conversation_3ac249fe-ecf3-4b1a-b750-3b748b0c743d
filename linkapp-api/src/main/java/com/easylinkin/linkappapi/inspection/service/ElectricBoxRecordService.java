package com.easylinkin.linkappapi.inspection.service;

import com.easylinkin.linkappapi.inspection.dto.ElectricBoxRecordDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricBoxRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.openapi.dto.DatapushDTO;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 配电箱数据记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface ElectricBoxRecordService extends IService<ElectricBoxRecord> {

  /**
   * 查询配电箱数据记录
   * @param recordDTO
   * @return
   */
  List<ElectricBoxRecord> getList(ElectricBoxRecordDTO recordDTO);

  /**
   * 配电箱流水处理
   * @param datapushDTO
   */
  void datapushHandler(DatapushDTO datapushDTO);

  /**
   *按24小时或者按月统计配电箱温度情况
   * @param electricBoxRecordDTO
   * @return
   */
  Map<String, Object> boxRecordByhoursOrMonthly(ElectricBoxRecordDTO electricBoxRecordDTO);


}
