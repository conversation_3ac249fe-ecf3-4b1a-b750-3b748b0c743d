package com.easylinkin.linkappapi.inspection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.inspection.dto.AppElectricBoxDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricCheckDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricCheckPhotoAlbumDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricCheckPhotoAlbumQueryDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricCheck;
import java.util.List;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

/**
 * <p>
 * 配电箱巡检表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface ElectricCheckService extends IService<ElectricCheck> {

  IPage<ElectricCheckDTO> queryListByPage(RequestModel<ElectricCheckDTO> requestModel);

  /**
   * 增加
   *
   * @param electricCheck
   * @return
   */
  Boolean insert(ElectricCheck electricCheck);

  /**
   * 根据id编辑
   *
   * @param electricCheck
   */
  void updateOne(ElectricCheck electricCheck);

  /**
   * @Description: 根据配电箱id和日期查询巡检项情况 时间格式 2022-05-01
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  AppElectricBoxDTO findByIdAndDate(ElectricBoxDTO electricBoxDTO);

  /**
   * @Description: 巡检提交
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  void check(ElectricCheckDTO electricCheckDTO);

  /**
   * @Description: 根据配电箱id和日期查询配电箱巡检状态
   * @param electricBoxDTO
   * @return
   */
  ElectricCheckDTO findCheckStatusByIdAndDate(ElectricBoxDTO electricBoxDTO);

  IPage<ElectricCheckPhotoAlbumDTO> selectPhotoAlbumByPage(RequestModel<ElectricCheckPhotoAlbumQueryDTO> requestModel);

  void refreshAllStatus();

  int photoAlbumCount(ElectricCheckPhotoAlbumQueryDTO dto);

  String exportPhotoAlbum(ElectricCheckPhotoAlbumQueryDTO dto);

  /**
   * 查询巡检月视图
   */
  List<ElectricCheckDTO> monthList(ElectricCheckDTO electricCheckDTO);

  /**
   * 导出巡检月视图
   */
  XWPFDocument monthExport(ElectricCheckDTO electricCheckDTO) throws Exception;
}

