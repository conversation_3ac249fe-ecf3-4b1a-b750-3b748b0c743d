package com.easylinkin.linkappapi.inspection.dto;

import com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus;
import com.easylinkin.linkappapi.inspection.entity.ElectricBox;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @Author: ka<PERSON> <PERSON><PERSON>
 * @Date: 2022/6/15 16:08
 * @Description:
 */
@Data
public class ElectricBoxDTO extends ElectricBox {

  /**
   *开始时间
   */
  private Date startTime;

  /**
   * 结束时间
   */
  private Date endTime;

  /**
   * 巡检时间
   */
  private Date checkTime;

  /**
   * 设备状态
   */
  private List<DeviceAttributeStatus> attributeStatuses;

  /**
   * 巡检状态 0未巡检 1正常 2异常
   */
  private Integer checkStatus;

  private Date lastPushTime;

  /**
   * 是否已巡检
   * 1 已巡检 0 未巡检
   */
  private Integer isInspected;
}
