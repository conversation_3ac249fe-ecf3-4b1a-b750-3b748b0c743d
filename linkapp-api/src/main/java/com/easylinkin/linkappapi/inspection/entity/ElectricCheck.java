package com.easylinkin.linkappapi.inspection.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配电箱巡检表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_electric_check")
public class  ElectricCheck extends Model<ElectricCheck> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId("id")
    private String id;

    /**
     * 项目ID(linkapp_tenant)，为null时为共有的默认数据
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * 配电箱ID 
     */
    @TableField("electric_box_id_")
    private String electricBoxId;

    /**
     * 巡检时间
     */
    @TableField("check_time_")
    private Date checkTime;

    /**
     * 提交人id
     */
    @TableField("check_user_id_")
    private Long checkUserId;

    /**
     * 地理位置
     */
    @TableField("position_")
    private String position;

    /**
     * 相片url,多张逗号分隔
     */
    @TableField("photo_")
    private String photo;

    /**
     * 巡检提交内容json
     */
    @TableField("content_")
    private String content;

    /**
     * 巡检状态 0正常 1异常
     */
    @TableField("status_")
    private Integer status;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * id集合
     * @return
     */
    @TableField(exist = false)
    private List<String> ids;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
