package com.easylinkin.linkappapi.inspection.controller;

import com.easylinkin.linkappapi.inspection.dto.AppElectricBoxDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricCheckDTO;
import com.easylinkin.linkappapi.inspection.service.ElectricCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * @Author: kan yuanfeng
 * @Date: 2022/6/20 09:32
 * @Description: app配电箱接口
 */
@RestController
@RequestMapping("appElectricBox")
@Api(tags = "app配电箱接口")
public class AppElectricBoxController {

  @Autowired
  private ElectricCheckService electricCheckService;

  /**
   * @Description: 根据配电箱id和日期查询巡检项情况
   *               时间格式 2022-05-01
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("detail")
  @ApiOperation("根据id查询配电箱巡检详情")
  public RestMessage findByIdAndDate(@RequestBody ElectricBoxDTO electricBoxDTO){
    AppElectricBoxDTO appElectricBoxDTO = electricCheckService.findByIdAndDate(electricBoxDTO);
    return RestBuilders.successBuilder().data(appElectricBoxDTO).build();
  }

  /**
   * @Description: 巡检提交
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("check")
  @ApiOperation("巡检提交")
  public RestMessage check(@RequestBody ElectricCheckDTO electricCheckDTO){
    electricCheckService.check(electricCheckDTO);
    return RestBuilders.successBuilder().build();
  }
}
