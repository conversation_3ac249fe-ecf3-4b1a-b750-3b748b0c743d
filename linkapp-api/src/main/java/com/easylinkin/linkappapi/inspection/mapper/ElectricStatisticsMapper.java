package com.easylinkin.linkappapi.inspection.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.inspection.dto.ElectricStatisticsDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricStatistics;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 配件箱巡检统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
public interface ElectricStatisticsMapper extends BaseMapper<ElectricStatistics> {

  IPage<ElectricStatisticsDTO> queryListByPage(Page page, @Param("electricStatisticsDTO") ElectricStatisticsDTO electricStatisticsDTO);
}
