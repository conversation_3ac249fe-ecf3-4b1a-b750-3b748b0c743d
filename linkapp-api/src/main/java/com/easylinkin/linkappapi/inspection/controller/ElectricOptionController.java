package com.easylinkin.linkappapi.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.inspection.entity.ElectricOption;
import com.easylinkin.linkappapi.inspection.service.ElectricOptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * @author: kan yuanfeng
 * @Date: 2020/11/04 11:42
 * @Description: 配电箱巡检项管理
 */
@RestController
@RequestMapping("electricOption")
@Api(tags = "配电箱巡检项管理")
public class ElectricOptionController {
  
  @Autowired
  private ElectricOptionService electricOptionService;

  /**
   * @Description: 增加配电箱巡检
   * <AUTHOR> yuan<PERSON>
   * @date 2020/11/04 11:42
   */
  @PostMapping
  @ApiOperation("增加配电箱巡检")
  public RestMessage insert(@RequestBody ElectricOption electricOption){
    //参数验证
    electricOptionService.insert(electricOption);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 删除配电箱巡检（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @DeleteMapping
  @ApiOperation("删除配电箱巡检（包含批量删除）")
  public RestMessage delBatch(@RequestBody ElectricOption electricOption){
    Assert.notEmpty(electricOption.getIds(),"id不能为空");
    electricOptionService.removeByIds(electricOption.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑配电箱巡检
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PutMapping
  @ApiOperation("编辑配电箱巡检")
  public RestMessage updateById(@RequestBody ElectricOption electricOption){
    Assert.notNull(electricOption.getId(),"id不能为空");
    electricOptionService.updateOne(electricOption);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询配电箱巡检详情
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询配电箱巡检详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    ElectricOption electricOption = electricOptionService.getById(id);
    return RestBuilders.successBuilder().data(electricOption).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryListByPage(@RequestBody RequestModel<ElectricOption> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<ElectricOption> record =  electricOptionService.queryListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * @Description: 查询巡检项
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @GetMapping("options")
  @ApiOperation("查询巡检项")
  public RestMessage options(){
    List<ElectricOption> electricOptionList =  electricOptionService.options();
    return RestBuilders.successBuilder().data(electricOptionList).build();
  }
}

