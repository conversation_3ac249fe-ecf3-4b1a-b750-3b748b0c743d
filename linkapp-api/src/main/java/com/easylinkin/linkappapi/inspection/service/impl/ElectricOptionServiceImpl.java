package com.easylinkin.linkappapi.inspection.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.inspection.entity.ElectricOption;
import com.easylinkin.linkappapi.inspection.mapper.ElectricOptionMapper;
import com.easylinkin.linkappapi.inspection.service.ElectricOptionService;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 配电箱巡检项表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Service
public class ElectricOptionServiceImpl extends
    ServiceImpl<ElectricOptionMapper, ElectricOption> implements ElectricOptionService {

  @Resource
  private LinkappUserContextProducer linkappUserContextProducer;

  /**
   * 增加
   * @param electricOption
   */
  @Override
  public void insert(ElectricOption electricOption) {
    /**
     * 验证重复
     */
    this.checkExist(electricOption);
    electricOption.setStatus(1);
    //设置基本属性
    this.setBase(electricOption);
    this.save(electricOption);
  }

  /**
   * 根据id编辑
   * @param electricOption
   */
  @Override
  public void updateOne(ElectricOption electricOption) {
    /**
     * 验证重复
     */
    this.checkExist(electricOption);
    //设置基本属性
    this.setBase(electricOption);
    this.updateById(electricOption);
  }

  /**
   * @Description: 查询巡检项
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public List<ElectricOption> options() {
    //项目id 过滤
    QueryWrapper<ElectricOption> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("tenant_id_",linkappUserContextProducer.getNotNullCurrent().getTenantId())
        .eq("status_",1);
    queryWrapper.orderByAsc("create_time_");
    List<ElectricOption> optionList = baseMapper.selectList(queryWrapper);
    return optionList;
  }

  @Override
  public List<ElectricOption> optionsByTenantId(String tenantId) {
    //项目id 过滤
    QueryWrapper<ElectricOption> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("tenant_id_",tenantId)
        .eq("status_",1);
    queryWrapper.orderByAsc("create_time_");
    List<ElectricOption> optionList = baseMapper.selectList(queryWrapper);
    return optionList;
  }

  @Override
  public IPage<ElectricOption> queryListByPage(RequestModel<ElectricOption> requestModel) {
    Page page = requestModel.getPage();
    ElectricOption electricOption = requestModel.getCustomQueryParams();
    //项目id 过滤
    QueryWrapper<ElectricOption> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("tenant_id_",linkappUserContextProducer.getNotNullCurrent().getTenantId());
    if (StringUtils.isNotBlank(electricOption.getContent())){
      queryWrapper.like("content_",electricOption.getContent());
    }
    if (null != electricOption.getStatus()){
      queryWrapper.eq("status_",electricOption.getStatus());
    }
    queryWrapper.orderByDesc("create_time_");
    IPage ipage = baseMapper.selectPage(page, queryWrapper);
    return ipage;
  }

  /**
   * 验证重复
   */
  private void checkExist(ElectricOption electricOption) {
    QueryWrapper<ElectricOption> queryWrapper = new QueryWrapper<>();
    //设置判断重复条件
    queryWrapper.eq("content_",electricOption.getContent())
        .eq("tenant_id_",linkappUserContextProducer.getNotNullCurrent().getTenantId());
    //编辑的时候存在id
    Optional.ofNullable(electricOption.getId()).ifPresent(id -> queryWrapper.ne("id",electricOption.getId()));
    Integer integer = baseMapper.selectCount(queryWrapper);
    if (integer>0){
      throw new BusinessException("该巡检项已存在");
    }
  }

  /**
   * 设置基本属性
   * @param electricOption
   */
  private void setBase(ElectricOption electricOption) {
    electricOption.setModifyTime(new Date());
    electricOption.setModifyId(linkappUserContextProducer.getCurrent().getId());
    //没有id就是新增,有就是编辑
    if (null == electricOption.getId()){
      electricOption.setCreatorId(linkappUserContextProducer.getCurrent().getId());
      electricOption.setCreateTime(new Date());
      electricOption.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
    }
  }
}
