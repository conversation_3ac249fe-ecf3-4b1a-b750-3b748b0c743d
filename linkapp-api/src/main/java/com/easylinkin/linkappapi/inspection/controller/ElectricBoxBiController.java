package com.easylinkin.linkappapi.inspection.controller;

import com.easylinkin.linkappapi.inspection.dto.*;
import com.easylinkin.linkappapi.inspection.entity.ElectricBox;
import com.easylinkin.linkappapi.inspection.entity.ElectricBoxRecord;
import com.easylinkin.linkappapi.inspection.service.ElectricBoxBiService;
import com.easylinkin.linkappapi.inspection.service.ElectricBoxRecordService;
import com.easylinkin.linkappapi.inspection.service.IRailLinkappElectricFireRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/08/16
 * @description
 */
@Slf4j
@Api(value = "配电箱管理大屏", tags = {"配电箱管理大屏接口"})
@RestController
@RequestMapping("/electricBoxBi")
public class ElectricBoxBiController {

    @Autowired
    private ElectricBoxBiService electricBoxBiService;

    @Autowired
    private ElectricBoxRecordService electricBoxRecordService;

    @Autowired
    private IRailLinkappElectricFireRecordService RailLinkappElectricFireRecordService;

    /**
     * 根据条件获取已绑定设备的配电箱信息
     *
     * @return 配电箱设备信息
     */
    @PostMapping("/queryRefElectricBoxDevices")
    @ApiOperation("根据条件获取已绑定设备的配电箱信息")
    public RestMessage queryRefElectricBoxDevices(@RequestBody ElectricBox electricBox) {
        log.info("根据条件获取已绑定设备的配电箱信息");
        List<ElectricBoxDTO> electricBoxList = electricBoxBiService
            .queryRefElectricBoxDevices(electricBox);
        return RestBuilders.successBuilder().data(electricBoxList).build();
    }

    /**
     * 获取配电箱设备告警数量统计告警类型分组
     *
     * @param electricBoxDTO 配电箱设备条件dto
     * @return 配电箱设备告警类型数量统计
     */
    @PostMapping("/countElectricBoxWarnGroupByType")
    @ApiOperation("获取配电箱设备告警数量统计告警类型分组")
    public RestMessage countElectricBoxWarnGroupByType(@RequestBody ElectricBoxDTO electricBoxDTO) {
        log.info("获取配电箱设备告警数量统计告警类型分组");
        List<ElectricBoxWarnGroupByTypeDTO> list = electricBoxBiService
            .countElectricBoxWarnGroupByType(electricBoxDTO);

        return RestBuilders.successBuilder().data(list).build();
    }

    /**
     * 获取配电箱设备告警数量统计设备分组
     *
     * @param electricBoxDTO 配电箱设备条件dto
     * @return 配电箱设备告警数量统计
     */
    @PostMapping("/countElectricBoxWarnGroupByDevice")
    @ApiOperation("获取配电箱设备告警数量统计设备分组")
    public RestMessage countElectricBoxWarnGroupByDevice(@RequestBody ElectricBoxDTO electricBoxDTO) {
        log.info("获取配电箱设备告警数量统计设备分组");
        List<ElectricBoxWarnGroupByDeviceDTO> list = electricBoxBiService
            .countElectricBoxWarnGroupByDevice(electricBoxDTO);
        return RestBuilders.successBuilder().data(list).build();
    }


    /**
     * 查询配电箱数据记录
     * @param electricBoxRecordDTO 配电箱数据记录条件dto
     * @return 配电箱数据记录列表
     */
    @PostMapping("/getElectricBoxRecordList")
    @ApiOperation("查询配电箱数据记录")
    public RestMessage getElectricBoxRecordList(@RequestBody ElectricBoxRecordDTO electricBoxRecordDTO) {
        log.info("查询配电箱数据记录");
        List<ElectricBoxRecord> list = electricBoxRecordService.getList(electricBoxRecordDTO);
        return RestBuilders.successBuilder().data(list).build();
    }

    /**
     *  按24小时或者按月统计配电箱l漏电情况
     * @param railLinkappElectricFireRecordDTO
     * {
     *   deviceCode: //设备id
     *   electricBoxId://配电箱设备id
     *   dailyOrMonthly://0:逐时,1:逐月30天 默认逐时0
     * }
     * @return
     */
    @PostMapping(value = "/boxRecordByhoursOrMonthly")
    public RestMessage boxRecordByhoursOrMonthly(@RequestBody RailLinkappElectricFireRecordDTO railLinkappElectricFireRecordDTO){
        if (Objects.isNull(railLinkappElectricFireRecordDTO.getDailyOrMonthly())){
            railLinkappElectricFireRecordDTO.setDailyOrMonthly(0);
        }
        Map<String,Object>  result = RailLinkappElectricFireRecordService.boxRecordByhoursOrMonthly(railLinkappElectricFireRecordDTO);
        return RestBuilders.successBuilder().data(result).build();
    }
    /**
     *  按24小时或者按月统计配电箱温度情况
     * @param railLinkappElectricFireRecordDTO
     * {
     *   deviceCode: //设备id
     *   electricBoxId://配电箱设备id
     *   dailyOrMonthly://0:逐时,1:逐月30天 默认逐时0
     * }
     * @return
     */
    @PostMapping(value = "/temperatureLineHursOrMonthly")
    public RestMessage temperatureLineHursOrMonthly(@RequestBody RailLinkappElectricFireRecordDTO railLinkappElectricFireRecordDTO){
        if (Objects.isNull(railLinkappElectricFireRecordDTO.getDailyOrMonthly())){
            railLinkappElectricFireRecordDTO.setDailyOrMonthly(0);
        }
        Map<String,Object>  result = RailLinkappElectricFireRecordService.temperatureLineHursOrMonthly(railLinkappElectricFireRecordDTO);
        return RestBuilders.successBuilder().data(result).build();
    }

}
