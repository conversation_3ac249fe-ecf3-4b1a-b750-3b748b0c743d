package com.easylinkin.linkappapi.inspection.mapper;

import com.easylinkin.linkappapi.inspection.dto.ElectricBoxDTO;
import com.easylinkin.linkappapi.inspection.dto.ElectricBoxRecordDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricBoxRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 配电箱数据记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface ElectricBoxRecordMapper extends BaseMapper<ElectricBoxRecord> {

  List<ElectricBoxRecord> getList(ElectricBoxRecordDTO recordDTO);

  /**
   * 用电安全统计,时间聚合查询
   */
  List<ElectricBoxRecord> selectByDateOrTime(@Param("recordDto") ElectricBoxRecordDTO recordDTO);
}
