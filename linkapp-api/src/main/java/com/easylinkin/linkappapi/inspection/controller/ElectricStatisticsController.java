package com.easylinkin.linkappapi.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.inspection.dto.ElectricStatisticsDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricBox;
import com.easylinkin.linkappapi.inspection.entity.ElectricStatistics;
import com.easylinkin.linkappapi.inspection.service.ElectricStatisticsService;
import com.easylinkin.linkappapi.lobar.util.WarningTimer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * @author: kan yuan<PERSON>
 * @Date: 2020/11/04 11:42
 * @Description: 配电箱巡检统计管理
 */
@RestController
@RequestMapping("electricStatistics")
@Api(tags = "配电箱巡检统计管理")
public class ElectricStatisticsController {
  
  @Autowired
  private ElectricStatisticsService electricStatisticsService;

  /**
   * @Description: 增加配电箱巡检统计
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping
  @ApiOperation("增加配电箱巡检统计")
  public RestMessage insert(@RequestBody ElectricStatistics electricStatistics){
    //todo 参数验证
    electricStatisticsService.insert(electricStatistics);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 删除配电箱巡检统计（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @DeleteMapping
  @ApiOperation("删除配电箱巡检统计（包含批量删除）")
  public RestMessage delBatch(@RequestBody ElectricStatistics electricStatistics){
    Assert.notEmpty(electricStatistics.getIds(),"id不能为空");
    electricStatisticsService.removeByIds(electricStatistics.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑配电箱巡检统计
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PutMapping
  @ApiOperation("编辑配电箱巡检统计")
  public RestMessage updateById(@RequestBody ElectricStatistics electricStatistics){
    Assert.notNull(electricStatistics.getId(),"id不能为空");
    electricStatisticsService.updateOne(electricStatistics);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询配电箱巡检统计详情
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询配电箱巡检统计详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    ElectricStatistics electricStatistics = electricStatisticsService.getById(id);
    return RestBuilders.successBuilder().data(electricStatistics).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryListByPage(@RequestBody RequestModel<ElectricStatisticsDTO> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<ElectricStatisticsDTO> record =  electricStatisticsService.queryListByPage(requestModel);
    record.getRecords().forEach(e ->{
      e.setDone(null == e.getDone()?0:e.getDone());
      e.setUnDo(e.getSum()-e.getDone());
    });
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * @Description: 未巡检配电箱
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("undo")
  @ApiOperation("未巡检配电箱")
  public RestMessage undoList(@RequestBody RequestModel<ElectricStatisticsDTO> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<ElectricBox> record =  electricStatisticsService.undoList(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * @Description: 已巡检配电箱
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("done")
  @ApiOperation("已巡检配电箱")
  public RestMessage done(@RequestBody RequestModel<ElectricStatisticsDTO> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<ElectricBox> record =  electricStatisticsService.done(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }
}

