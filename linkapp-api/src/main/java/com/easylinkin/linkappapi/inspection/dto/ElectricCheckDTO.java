package com.easylinkin.linkappapi.inspection.dto;

import com.easylinkin.linkappapi.inspection.entity.ElectricCheck;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @Author: kan <PERSON><PERSON>
 * @Date: 2022/6/20 09:37
 * @Description:
 */
@Data
public class ElectricCheckDTO extends ElectricCheck {
  //检查项List
  private List<ElectricOptionDTO> electricOptionList;

  /**
   * 提交人name
   */
  private String checkUserName;

  /**
   * 结果是否正常(true正常 false异常)
   */
  private Boolean result;

  private List<String> photoList;

  private String phone;

  private Integer type;

  private Date startTime;

  private Date endTime;

  private String code;

  private String electricBoxPosition;

  private String stasticId;

  /**
   * 0全部，1已巡检，2未巡检，3异常
   */
  private Integer queryType;

  /**
   * 月份
   */
  private String mouthStr;

  /**
   * 当月检查数据
   */
  private List<String> mouthList;
}
