package com.easylinkin.linkappapi.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.inspection.dto.ElectricCheckDTO;
import com.easylinkin.linkappapi.inspection.entity.ElectricBox;
import com.easylinkin.linkappapi.inspection.entity.ElectricCheck;
import com.easylinkin.linkappapi.inspection.service.ElectricBoxService;
import com.easylinkin.linkappapi.inspection.service.ElectricCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * @author: kan yuanfeng
 * @Date: 2020/11/04 11:42
 * @Description: 配电箱巡检管理
 */
@RestController
@RequestMapping("electricCheck")
@Api(tags = "配电箱巡检管理")
public class ElectricCheckController {
  
  @Autowired
  private ElectricCheckService electricCheckService;
  @Autowired
  private ElectricBoxService electricBoxService;

  /**
   * @Description: 增加配电箱巡检
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping
  @ApiOperation("增加配电箱巡检")
  public RestMessage insert(@RequestBody ElectricCheck electricCheck){
    //todo 参数验证
    electricCheckService.insert(electricCheck);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 删除配电箱巡检（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @DeleteMapping
  @ApiOperation("删除配电箱巡检（包含批量删除）")
  public RestMessage delBatch(@RequestBody ElectricCheck electricCheck){
    Assert.notEmpty(electricCheck.getIds(),"id不能为空");
    electricCheckService.removeByIds(electricCheck.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑配电箱巡检
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PutMapping
  @ApiOperation("编辑配电箱巡检")
  public RestMessage updateById(@RequestBody ElectricCheck electricCheck){
    Assert.notNull(electricCheck.getId(),"id不能为空");
    electricCheckService.updateOne(electricCheck);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询配电箱巡检详情
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询配电箱巡检详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    ElectricCheck electricCheck = electricCheckService.getById(id);
    return RestBuilders.successBuilder().data(electricCheck).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryListByPage(@RequestBody RequestModel<ElectricCheckDTO> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<ElectricCheckDTO> record =  electricCheckService.queryListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }

  @GetMapping("refreshAllStatus")
  @ApiOperation("刷新所有巡检状态")
  public RestMessage refreshAllStatus(){
    electricCheckService.refreshAllStatus();
    return RestBuilders.successBuilder().data(null).build();
  }

  /**
   * 查询巡检月视图
   */
  @PostMapping("month/list")
  @ApiOperation("查询巡检月视图")
  public RestMessage monthList(@RequestBody RequestModel<ElectricCheckDTO> requestModel){
    ElectricCheckDTO customQueryParams = requestModel.getCustomQueryParams();
    List<ElectricCheckDTO> electricCheckDTOS = electricCheckService.monthList(customQueryParams);
    IPage<ElectricCheckDTO> record = new Page<>();
    record.setRecords(electricCheckDTOS);
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * 导出巡检月视图
   */
  @PostMapping("month/export")
  @ApiOperation("查询巡检月视图")
  public void monthExport(@RequestBody ElectricCheckDTO electricCheckDTO,HttpServletResponse response) throws Exception {
    XWPFDocument doc = electricCheckService.monthExport(electricCheckDTO);
    doc.write(response.getOutputStream());
    response.flushBuffer();
  }

  /**
   * 导出巡检月视图(批量)
   */
  @PostMapping("month/exportBatch/{mouthStr}")
  @ApiOperation("查询巡检月视图")
  public void monthExportBatch(@RequestBody ElectricBox electricBox,HttpServletResponse response,@PathVariable("mouthStr") String mouthStr) throws Exception {
    //查询配电箱
    RequestModel<ElectricBox> requestModel = new RequestModel<>();
    requestModel.setCustomQueryParams(electricBox);
    requestModel.setPage(new Page(0,-1));
    IPage<ElectricBox> record =  electricBoxService.queryListByPage(requestModel);
    ElectricCheckDTO electricCheckDTO = new ElectricCheckDTO();
    List<String> set = record.getRecords().stream().map(r -> r.getId())
        .collect(Collectors.toList());
    electricCheckDTO.setMouthStr(mouthStr);
    electricCheckDTO.setIds(set);
    XWPFDocument doc = electricCheckService.monthExport(electricCheckDTO);
    doc.write(response.getOutputStream());
    response.flushBuffer();
  }

}

