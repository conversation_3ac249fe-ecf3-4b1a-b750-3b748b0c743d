package com.easylinkin.linkappapi.manageinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.manageinfo.entity.ManageInfo;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;

/**
 * ManageInfo表数据库访问层
 *
 * <AUTHOR>
 * @date 2022/08/03
 */
public interface ManageInfoMapper extends BaseMapper<ManageInfo> {


    /**
     * 查询分页
     *
     * @param page        分页参数对象
     * @param appManageInfo 过滤参数对象
     * @return 查询分页结果
     */
    IPage<ManageInfo> selectPage(Page page, @Param("appManageInfo") ManageInfo appManageInfo);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    ManageInfo getOneById(@Param("id") Serializable id);
}

