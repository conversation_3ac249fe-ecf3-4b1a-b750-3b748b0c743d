package com.easylinkin.linkappapi.manageinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_manage_info")
public class ManageInfo extends Model<ManageInfo> {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    
    private Long id;

    /**
     * 管理区域名称
     */
    
    private String mangeName;

    /**
     * 管理区域编码
     */
    
    private String mangeCode;

    /**
     * 类型，1国家、2省、3市、4区
     */
    private Integer type;

    /**
     * 等级，树形层级
     */
    private Integer level;

    /**
     * 父级id
     */
    
    private Long parentId;

    /**
     * 父级ids
     */
    
    private String parentIds;

    /**
     * 是否直辖市，0否，1是
     */
    
    private Integer direstManage;

    /**
     * 排序号
     */
    
    private Integer sortNo;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取管理区域名称
     *
     * @return mange_name - 管理区域名称
     */
    public String getMangeName() {
        return mangeName;
    }

    /**
     * 设置管理区域名称
     *
     * @param mangeName 管理区域名称
     */
    public void setMangeName(String mangeName) {
        this.mangeName = mangeName;
    }

    /**
     * 获取管理区域编码
     *
     * @return mange_code - 管理区域编码
     */
    public String getMangeCode() {
        return mangeCode;
    }

    /**
     * 设置管理区域编码
     *
     * @param mangeCode 管理区域编码
     */
    public void setMangeCode(String mangeCode) {
        this.mangeCode = mangeCode;
    }

    /**
     * 获取类型，1国家、2省、3市、4区
     *
     * @return type - 类型，1国家、2省、3市、4区
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型，1国家、2省、3市、4区
     *
     * @param type 类型，1国家、2省、3市、4区
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取等级，树形层级
     *
     * @return level - 等级，树形层级
     */
    public Integer getLevel() {
        return level;
    }

    /**
     * 设置等级，树形层级
     *
     * @param level 等级，树形层级
     */
    public void setLevel(Integer level) {
        this.level = level;
    }

    /**
     * 获取父级id
     *
     * @return parent_id - 父级id
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * 设置父级id
     *
     * @param parentId 父级id
     */
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    /**
     * 获取父级ids
     *
     * @return parent_ids - 父级ids
     */
    public String getParentIds() {
        return parentIds;
    }

    /**
     * 设置父级ids
     *
     * @param parentIds 父级ids
     */
    public void setParentIds(String parentIds) {
        this.parentIds = parentIds;
    }

    /**
     * 获取是否直辖市，0否，1是
     *
     * @return direst_manage - 是否直辖市，0否，1是
     */
    public Integer getDirestManage() {
        return direstManage;
    }

    /**
     * 设置是否直辖市，0否，1是
     *
     * @param direstManage 是否直辖市，0否，1是
     */
    public void setDirestManage(Integer direstManage) {
        this.direstManage = direstManage;
    }

    /**
     * 获取排序号
     *
     * @return sort_no - 排序号
     */
    public Integer getSortNo() {
        return sortNo;
    }

    /**
     * 设置排序号
     *
     * @param sortNo 排序号
     */
    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }
}
