package com.easylinkin.linkappapi.manageinfo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.service.CommonService;
import com.easylinkin.linkappapi.common.utils.excel.ExcelConstant;
import com.easylinkin.linkappapi.common.utils.excel.ExcelTools;
import com.easylinkin.linkappapi.common.utils.io.OutputStreamUtil;
import com.easylinkin.linkappapi.manageinfo.dao.ManageInfoMapper;
import com.easylinkin.linkappapi.manageinfo.entity.ManageInfo;
import com.easylinkin.linkappapi.manageinfo.vo.ManageInfoVo;
import com.easylinkin.linkappapi.manageinfo.service.ManageInfoService;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * ManageInfo表服务实现类
 *
 * <AUTHOR>
 * @date 2022/08/03
 */
@Slf4j
@Service("appManageInfoService")
public class ManageInfoServiceImpl extends ServiceImpl
        <ManageInfoMapper, ManageInfo> implements ManageInfoService {
    @Resource
    private CommonService commonService;
    @Resource
    private LinkappUserContextProducer linkappUserContextProducer;

    @Override
    public boolean saveOne(ManageInfo appManageInfo) {
        commonService.setCreateAndModifyInfo(appManageInfo);
       // appManageInfo.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        validParamRequired(appManageInfo);
        validRepeat(appManageInfo);
        validParamFormat(appManageInfo);
        return save(appManageInfo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(ManageInfo appManageInfo) {
        Assert.notNull(appManageInfo.getId(), "id不能为空");
        commonService.setModifyInfo(appManageInfo);
        //appManageInfo.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        validRepeat(appManageInfo);
        validParamFormat(appManageInfo);
        return updateById(appManageInfo);
    }

    @Override
    public IPage<ManageInfo> selectPage(Page page, ManageInfo appManageInfo) {
        //appManageInfo.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        return baseMapper.selectPage(page, appManageInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        return removeByIds(idList);
    }

    @Override
    public void export(ManageInfo appManageInfo, HttpServletRequest request, HttpServletResponse
            response) {

        IPage<ManageInfo> page = selectPage(new Page(0, -1), appManageInfo);
        List<ManageInfo> records = page.getRecords();
        List
                <ManageInfoVo> appManageInfoVos = new ArrayList<>();
        for (ManageInfo expert : records) {
            appManageInfoVos.add(new ManageInfoVo(expert));
        }

        String keyValue = "名称:name,租户id:tenantId";
        String title = "ManageInfo导出数据";
        String fileName = title + ".xls";
        try {
            OutputStream outputStream = OutputStreamUtil
                    .getOutputStream(request, response, fileName);
            ExcelTools.exportExcel(outputStream, keyValue, appManageInfoVos, ExcelConstant.XLS, title);
            response.flushBuffer();
            outputStream.close();
        } catch (IOException e) {
            log.error("excel导出失败", e);
            throw new RuntimeException("excel导出失败！IOException异常" + e.getMessage());
        } catch (Exception e) {
            log.error("excel导出失败", e);
            throw new RuntimeException("excel导出失败！" + e.getMessage());
        }
    }

    @Override
    public ManageInfo getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(ManageInfo appManageInfo) {
        /* QueryWrapper<ManageInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", appManageInfo.getName());
        queryWrapper.eq("tenant_id", linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<ManageInfo> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("名称有重复");
        }
        if (ObjectUtils.isEmpty(appManageInfo.getId())) {
            throw new BusinessException("名称已存在");
        }
        if (!appManageInfo.getId().equals(list.get(0).getId())) {
            throw new BusinessException("名称已存在");
        }
                    */

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(ManageInfo appManageInfo) {
        //Assert.notNull(appManageInfo, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(appManageInfo.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(ManageInfo appManageInfo) {
        //Assert.isTrue(appManageInfo.getName() == null || appManageInfo.getName().length() <= 50,
        //        "名称超长");
    }
}

