package com.easylinkin.linkappapi.manageinfo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.manageinfo.entity.ManageInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * ManageInfo表服务接口
 *
 * <AUTHOR>
 * @date 2022/08/03
 */
public interface ManageInfoService extends IService<ManageInfo> {

    /**
     * 新增
     *
     * @param appManageInfo 实体对象
     * @return 操作结果
     */
    boolean saveOne(ManageInfo appManageInfo);

    /**
     * 修改单条
     *
     * @param appManageInfo 实体对象
     * @return 修改结果
     */
    boolean updateOne(ManageInfo appManageInfo);

    /**
     * 查询分页
     *
     * @param page        分页对象
     * @param appManageInfo 分页参数对象
     * @return 查询分页结果
     */
    IPage<ManageInfo> selectPage(Page page, ManageInfo appManageInfo);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    ManageInfo getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param appManageInfo 过滤条件实体对象
     * @param request     请求
     * @param response    响应
     */
    void export(ManageInfo appManageInfo, HttpServletRequest request, HttpServletResponse response);

}

