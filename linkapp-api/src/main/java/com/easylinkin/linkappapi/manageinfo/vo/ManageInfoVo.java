package com.easylinkin.linkappapi.manageinfo.vo;

import com.easylinkin.linkappapi.manageinfo.entity.ManageInfo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ManageInfo实体类Vo
 *
 * <AUTHOR>
 * @date 2022/08/03
 */

@Data
@Accessors(chain = true)
public class ManageInfoVo extends ManageInfo {
    /**
     * 创建时间
     */
    private String createTimeStr;

    public ManageInfoVo(ManageInfo appManageInfo) {
        //this.setName(appManageInfo.getName());
    }
}
