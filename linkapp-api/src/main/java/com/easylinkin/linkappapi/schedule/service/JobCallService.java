package com.easylinkin.linkappapi.schedule.service;

import com.easylinkin.linkappapi.shigongyun.vo.MonitorInfoVo;
import site.morn.rest.RestMessage;

/**
 * <AUTHOR>
 **/
public interface JobCallService {

  /**
   * 基坑监测数据转换保存
   *
   * @param monitorInfoVo 基坑监测数据入参vo
   * @return 统一出参
   */
  RestMessage jkMonitorDataConverSave(MonitorInfoVo monitorInfoVo);

  /**
   * 读配置的基坑监测数据转存
   * @param tmpVo
   * @return
   */
  RestMessage jkMonitorDataConfigTrans(MonitorInfoVo tmpVo);
}
