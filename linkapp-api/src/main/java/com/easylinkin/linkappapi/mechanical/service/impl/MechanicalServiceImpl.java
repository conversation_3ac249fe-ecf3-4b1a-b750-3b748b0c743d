package com.easylinkin.linkappapi.mechanical.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.config.dao.SysDictItemMapper;
import com.easylinkin.linkappapi.config.entity.SysDictItem;
import com.easylinkin.linkappapi.config.service.SysDictItemService;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.device.mapper.DeviceMapper;
import com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus;
import com.easylinkin.linkappapi.deviceattributestatus.service.DeviceAttributeStatusService;
import com.easylinkin.linkappapi.mechanical.entity.Mechanical;
import com.easylinkin.linkappapi.mechanical.entity.MechanicalRefDevice;
import com.easylinkin.linkappapi.mechanical.mapper.MechanicalMapper;
import com.easylinkin.linkappapi.mechanical.mapper.MechanicalRefDeviceMapper;
import com.easylinkin.linkappapi.mechanical.service.IMechanicalService;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 铁路机械表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Service
@Slf4j
public class MechanicalServiceImpl extends ServiceImpl<MechanicalMapper, Mechanical> implements IMechanicalService {

    @Resource
    private LinkappUserContextProducer linkappUserContextProducer;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private SysDictItemService sysDictItemService;

    @Resource
    private DeviceAttributeStatusService deviceAttributeStatusService;
    @Autowired
    private MechanicalRefDeviceMapper mechanicalRefDeviceMapper;



    @Override
    public IPage<Mechanical> listPage(RequestModel<Mechanical> requestModel) {
        return baseMapper.listPage(requestModel.getPage(), requestModel.getCustomQueryParams());
    }

    @Override
    public IPage<Map<String, Object>> listPageApp(RequestModel<Mechanical> requestModel) {

        IPage<Mechanical> mechanicalIPage = baseMapper.listPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        IPage<Map<String, Object>> pageMap = new Page<>();
        pageMap.setPages(mechanicalIPage.getPages());
        pageMap.setCurrent(mechanicalIPage.getCurrent());
        pageMap.setTotal(mechanicalIPage.getTotal());
        pageMap.setSize(mechanicalIPage.getSize());

        List<Map<String, Object>> mapList = new ArrayList<>();

        List<Mechanical> mechanicalList = mechanicalIPage.getRecords();


        List<SysDictItem> itemList = sysDictItemService.selectByDictItems("machine_type");
        for (SysDictItem item:itemList){
            Map<String,Object> map = new HashMap<>();
            map.put("machineTypeValue", item.getItemValue());
            map.put("machineType",item.getItemText());
            List<Mechanical> mechanicals = new ArrayList<>();
            for (Mechanical mechanical:mechanicalList){
                if (mechanical.getType().equals(item.getItemValue())){
                    Mechanical temp = new Mechanical();
                    temp.setId(mechanical.getId());
                    temp.setName(mechanical.getName());
                    temp.setCode(mechanical.getCode());
                    temp.setEnterTime(mechanical.getEnterTime());
                    temp.setSpecification(mechanical.getSpecification());
                    temp.setFiveChartImg(mechanical.getFiveChartImg());
                    mechanicals.add(temp);
                }
            }
            map.put("mechineList", mechanicals);
            map.put("total",mechanicals.size());
            mapList.add(map);
        }

        pageMap.setRecords(mapList);
        return pageMap;
    }

    @Override
    public Mechanical queryById(String id) {
        return baseMapper.queryById(id);
    }

    @Override
    public Mechanical queryMechanicalByDeviceCode(String deviceId, String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            tenantId = linkappUserContextProducer.getTenantId();
        }
        return baseMapper.selectMechanicalByDeviceCode(deviceId, tenantId);
    }

    @Override
    public List<Device> queryDeviceByMechanicalIdAndType(String deviceName, String type) {
        List<String> deviceIds = baseMapper.selectDeviceByMechanicalIdAndType(deviceName, type);
        if (CollectionUtil.isEmpty(deviceIds)) {
            return null;
        }
        List<Device> deviceList = new ArrayList<>();
        for (String deviceId : deviceIds) {
            Device device1 = getDeviceByDeviceId(deviceId);
            if (device1 != null) {
                deviceList.add(device1);
            }
        }
        return deviceList;
    }

    private Device getDeviceByDeviceId(String deviceId) {
        Device device = new Device();
        device.setId(deviceId);
        List<Device> devices = deviceMapper.selectDevices(device);
        if (CollectionUtil.isEmpty(devices)) {
            return null;
        }
        Device device1 = devices.get(0);
        QueryWrapper<DeviceAttributeStatus> qw = new QueryWrapper<>();
        qw.select("prop_code", "prop_value", "version");
        qw.eq("device_code", device1.getCode());
//        qw.in("prop_code", device.getRequiredDeviceAttributeStatusPropCodes());
        if (device1 != null && device1.getDeviceUnit() != null
                && device1.getDeviceUnit().getVersion() != null) {
            qw.eq("version", device1.getDeviceUnit().getVersion());
        }
        device1.setDeviceAttributeStatusList(deviceAttributeStatusService.list(qw));
        return device1;
    }

    @Scheduled(cron = "0 0 0 * * ?")
    @Override
    public void doDeparture() {
        try {
            log.info("--------进入定时任务-------doDeparture-------");
            List<Mechanical> mechanicalList = baseMapper.selectMechanicalByOutTime();
            if (CollectionUtil.isNotEmpty(mechanicalList)) {
                List<String> mechanicalIdList = mechanicalList.stream().map(Mechanical::getId).collect(Collectors.toList());
                // 退场操作 删除关联的设备
                LambdaQueryWrapper<MechanicalRefDevice> lqw = new LambdaQueryWrapper<>();
                lqw.in(MechanicalRefDevice::getRailMechanicalId, mechanicalIdList);
                mechanicalRefDeviceMapper.delete(lqw);
            }
            baseMapper.doDeparture();
            log.info("--------定时任务执行完成-------doDeparture-------");
        }catch (Exception e){
            log.error("--------定时任务执行异常-------doDeparture-------",e);
        }
    }

    @Override
    public void setOutTimeNull(String id) {
        baseMapper.setOutTimeNull(id);
    }

    @Override
    public List<Mechanical> selectByList(Mechanical requestModel) {
        return  baseMapper.listPage(requestModel);
    }
}