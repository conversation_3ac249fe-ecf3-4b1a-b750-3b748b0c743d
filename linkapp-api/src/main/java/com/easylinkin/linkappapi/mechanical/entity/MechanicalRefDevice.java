package com.easylinkin.linkappapi.mechanical.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 机械关联设备表
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("rail_mechanical_ref_device")
public class MechanicalRefDevice implements Serializable{
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.AUTO, value = "id_")
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id_")
    private String tenantId;

    /**
     * 机械id
     */
    @TableField(value = "rail_mechanical_id_")
    private String railMechanicalId;

    /**
     * 关联设备类型 1 主机 2 定位设备 3 视频监控设备 4 其他
     */
    @TableField(value = "device_type_")
    private Integer deviceType;

    /**
     * 设备id
     */
    @TableField(value = "device_id_")
    private String deviceId;

    /**
     * 设备code
     */
    @TableField(value = "device_code_")
    private String deviceCode;

    /**
     * 设备名称
     */
    @TableField(value = "device_name_")
    private String deviceName;

    /**
     * 删除状态 0：未删除 1：已删除
     */
    @TableField(value = "delete_state")
    @TableLogic(value = "0", delval = "1")
    private Integer deleteState;

    /**
     * 创建人id
     */
    @TableField(value = "create_id_")
    private Long createId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField(value = "modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField(value = "remark_")
    private String remark;

    @TableField(exist = false)
    List<DeviceAttributeStatus> linkappAttributeStatusList;

}
