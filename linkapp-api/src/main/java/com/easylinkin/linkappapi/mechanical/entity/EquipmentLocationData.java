package com.easylinkin.linkappapi.mechanical.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备设施定位数据表
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("rail_equipment_location_data")
public class EquipmentLocationData implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id_" , type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id_")
    private String tenantId;

    /**
     * 设备设施id
     */
    @TableField(value = "equipment_id_")
    private String equipmentId;

    /**
     * 设备设施编码
     */
    @TableField(value = "equipment_code_")
    private String equipmentCode;

    /**
     * 定位时间
     */
    @TableField(value = "location_time_")
    private Date locationTime;

    /**
     * 经度
     */
    @TableField(value = "location_lng_")
    private String locationLng;

    /**
     * 纬度
     */
    @TableField(value = "location_lat_")
    private String locationLat;

    /**
     * 是否移动 0-未移动，1-移动
     */
    @TableField(value = "move_flag_")
    private Integer moveFlag;

    /**
     * 创建人id
     */
    @TableField(value = "create_id_")
    private Long createId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField(value = "modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField(value = "remark_")
    private String remark;

}
