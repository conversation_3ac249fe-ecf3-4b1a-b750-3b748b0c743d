package com.easylinkin.linkappapi.mechanical.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 机械工作记录表
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("rail_mechanical_work_record")
public class MechanicalWorkRecord implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id_" , type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id_")
    private String tenantId;

    /**
     * 机械id
     */
    @TableField(value = "mechanical_id_")
    private String mechanicalId;

    /**
     * 作业开始时间
     */
    @TableField(value = "start_time_")
    private Date startTime;

    /**
     * 作业结束时间
     */
    @TableField(value = "end_time_")
    private Date endTime;

    /**
     * 作业时长
     */
    @TableField(value = "work_time_")
    private Integer workTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_id_")
    private Long createId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField(value = "modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField(value = "remark_")
    private String remark;

    /**
     * 查询开始时间
     */
    @TableField(exist = false)
    private String queryStartTime;
    /**
     * 查询结束时间
     */
    @TableField(exist = false)
    private String queryEndTime;
    /**
     * 设备位置数据
     */
    @TableField(exist = false)
    private List<EquipmentLocationData> locationDataList;

}
