package com.easylinkin.linkappapi.mechanical.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.mechanical.entity.MechanicalRefDevice;
import org.apache.ibatis.annotations.Param;

/**
 * 机械关联设备表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
public interface MechanicalRefDeviceMapper extends BaseMapper<MechanicalRefDevice> {
    MechanicalRefDevice selectRecentBindRecord(@Param("deviceId") String id, @Param("tenantId") String tenantId);
}