package com.easylinkin.linkappapi.mechanical.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.circuit.dto.RailGiveAlarmTypeConfigEnum;
import com.easylinkin.linkappapi.circuit.entity.RailGiveAlarmTypeConfig;
import com.easylinkin.linkappapi.circuit.mapper.RailGiveAlarmTypeConfigMapper;
import com.easylinkin.linkappapi.circuit.mapper.RailGiveSystemAlarmMapper;
import com.easylinkin.linkappapi.crane.entity.CraneVerticalProtectionArea;
import com.easylinkin.linkappapi.crane.entity.WorkTime;
import com.easylinkin.linkappapi.crane.mapper.CraneVerticalProtectionAreaMapper;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.crane.entity.CraneOperationArea;
import com.easylinkin.linkappapi.crane.mapper.CraneOperationAreaMapper;
import com.easylinkin.linkappapi.device.constant.DeviceConstant;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.device.mapper.DeviceMapper;
import com.easylinkin.linkappapi.mechanical.entity.*;
import com.easylinkin.linkappapi.mechanical.mapper.EquipmentLocationDataMapper;
import com.easylinkin.linkappapi.mechanical.mapper.EquipmentLocationMapper;
import com.easylinkin.linkappapi.mechanical.mapper.MechanicalRefDeviceMapper;
import com.easylinkin.linkappapi.mechanical.mapper.MechanicalWorkRecordMapper;
import com.easylinkin.linkappapi.mechanical.service.CraneSafeMonitorService;
import com.easylinkin.linkappapi.mechanical.service.IEquipmentLocationService;
import com.easylinkin.linkappapi.mechanical.service.IMechanicalService;
import com.easylinkin.linkappapi.mechanical.util.CraneUtil;
import com.easylinkin.linkappapi.mechanical.vo.EquipmentLocationVo;
import com.easylinkin.linkappapi.openapi.dto.DatapushDTO;
import com.easylinkin.linkappapi.person.entity.PersonLocationData;
import com.easylinkin.linkappapi.person.entity.PersonOperationArea;
import com.easylinkin.linkappapi.person.entity.PersonRefDevice;
import com.easylinkin.linkappapi.person.entity.PersonWorkRecord;
import com.easylinkin.linkappapi.person.mapper.PersonLocationDataMapper;
import com.easylinkin.linkappapi.person.mapper.PersonOperationAreaMapper;
import com.easylinkin.linkappapi.person.mapper.PersonRefDeviceMapper;
import com.easylinkin.linkappapi.person.mapper.PersonWorkRecordMapper;
import com.easylinkin.linkappapi.roster.entity.RailLinkappRosterPersonnel;
import com.easylinkin.linkappapi.roster.mapper.RailLinkappRosterPersonnelMapper;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import com.easylinkin.linkappapi.tenant.entity.LinkappTenant;
import com.easylinkin.linkappapi.tenant.mapper.LinkappTenantMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 铁路设备设施定位表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Service
public class EquipmentLocationServiceImpl extends ServiceImpl<EquipmentLocationMapper, EquipmentLocation> implements IEquipmentLocationService {
    @Resource
    private LinkappUserContextProducer linkappUserContextProducer;

    /**
     * 人员移动距离最小阈值 2, 单位：米
     */
    @Value("${person.min.threshold.distance:2d}")
    private Double personMinThresholdDistance;

    /**
     * 人员移动距离最小阈值 10, 单位：米
     */
    @Value("${person.max.threshold.distance:10d}")
    private Double personMaxThresholdDistance;

    /**
     * 人员移动时间阈值 5, 单位：分钟
     */
    @Value("${person.threshold.time:5}")
    private Integer personThresholdTime;

    /**
     * 车辆移动距离最小阈值 1, 单位：米
     */
    @Value("${car.threshold.distance:1}")
    private Double carMinThresholdDistance;

    /**
     * 车辆移动距离最大阈值 30, 单位：米
     */
    @Value("${car.max.threshold.distance:30}")
    private Double carMaxThresholdDistance;

    /**
     * 车辆移动时间阈值 30, 单位：分钟
     */
    @Value("${car.threshold.time:30}")
    private Integer carThresholdTime;

    /**
     * 人员停留距离阈值 2, 单位：米
     */
    @Value("${person.stay.threshold.distance:2d}")
    private Double personStayThresholdDistance;

    /**
     * 人员停留时间阈值 30, 单位：分钟
     * 单
     */
    @Value("${person.stay.threshold.time:30}")
    private Integer personStayThresholdTime;


    @Autowired
    private IMechanicalService mechanicalService;

    @Autowired
    private EquipmentLocationDataMapper equipmentLocationDataMapper;

    @Autowired
    private RailGiveAlarmTypeConfigMapper giveAlarmTypeConfigMapper;

    @Autowired
    private CraneOperationAreaMapper craneOperationAreaMapper;

    @Autowired
    private CraneSafeMonitorService craneSafeMonitorService;

    @Autowired
    private LinkappTenantMapper linkappTenantMapper;

    @Autowired
    private MechanicalRefDeviceMapper mechanicalRefDeviceMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private MechanicalWorkRecordMapper mechanicalWorkRecordMapper;

    @Autowired
    private PersonLocationDataMapper personLocationDataMapper;

    @Autowired
    private PersonOperationAreaMapper personOperationAreaMapper;

    @Autowired
    private PersonRefDeviceMapper personRefDeviceMapper;

    @Autowired
    private PersonWorkRecordMapper personWorkRecordMapper;

    @Autowired
    private CraneVerticalProtectionAreaMapper craneVerticalProtectionAreaMapper;
    @Autowired
    private RailGiveSystemAlarmMapper railGiveSystemAlarmMapper;
    @Autowired
    private RailLinkappRosterPersonnelMapper railLinkappRosterPersonnelMapper;


    @Override
    public IPage<EquipmentLocation> listPage(RequestModel<EquipmentLocationVo> requestModel) {
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        requestModel.getCustomQueryParams().setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        IPage<EquipmentLocation> pageList = baseMapper.listPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        pageList.getRecords().forEach(item -> {
            DeviceConstant.RailwayDeviceType railwayDeviceTypeByTypeId = DeviceConstant.RailwayDeviceType.getRailwayDeviceTypeByTypeId(item.getEquipmentTypeName());
            if (railwayDeviceTypeByTypeId != null) {
                item.setEquipmentType(railwayDeviceTypeByTypeId.getType().toString());
            } else {
                item.setEquipmentType("-1");
                //补全设备的在线状态
                List<MechanicalRefDevice> refDevices = mechanicalRefDeviceMapper.selectList(new LambdaQueryWrapper<MechanicalRefDevice>()
                        .in(MechanicalRefDevice::getRailMechanicalId, item.getEquipmentId())
                        .eq(MechanicalRefDevice::getDeviceType, 1)
                );
                if (CollectionUtil.isNotEmpty(refDevices)){
                    String deviceCode = refDevices.get(0).getDeviceCode();
                    Device device = deviceMapper.selectOne(new LambdaQueryWrapper<Device>()
                            .eq(Device::getCode, deviceCode)
                    );
                    if (device != null){
                        item.setOnlineState(device.getOnlineState());
                        item.setIsAlarm(device.getStatus());
                    }
                }
                //补全机械 在场退场状态
                Mechanical mechanical = mechanicalService.queryById(item.getEquipmentId());
                if (null != mechanical){
                    item.setStatus(mechanical.getStatus());
                }

            }
        });
        return pageList;
    }

    @Override
    public void datapushHandler(DatapushDTO datapushDTO) {
        // 流水处理流程
        JSONObject data = datapushDTO.getData();
        // String deviceCode = datapushDTO.getDevice_id();
        //将查询逻辑放入主线程
        Device device = datapushDTO.getDevice_data_latest();
        List<String> deviceTypeNames = Arrays.asList(DeviceConstant.RailwayDeviceType.BEIDOU_LOCATION.getDescription(),// 机械-北斗
                DeviceConstant.RailwayDeviceType.HAND_TERMINAL.getDescription(),// 人员-手持终端
                DeviceConstant.RailwayDeviceType.SMART_SECURITY_HAT.getDescription()// 人员-智能安全帽
                );

        String deviceTypeName = device.getDeviceUnit().getDeviceTypeName();
        if (!deviceTypeNames.contains(deviceTypeName)) {
            return;
        }
        String tenantId = device.getTenantId();
        LinkappTenant tenant = linkappTenantMapper.getById(tenantId);
        if (tenant == null) {
            return;
        }
        String platformProjectName = tenant.getPlatformProjectName();
        // 校验查询设备的经纬度信息是否都有
        Double longitude = null;
        Double latitude = null;
        Double heading = null;
        if (deviceTypeName.equals(DeviceConstant.RailwayDeviceType.BEIDOU_LOCATION.getDescription())) {
            longitude = data.getDouble("longitude");
            latitude = data.getDouble("latitude");
            heading = data.getDouble("heading");
            if (Objects.isNull(longitude) || Objects.isNull(latitude) || Objects.isNull(heading)) {
                return;
            }
        } else if (deviceTypeName.equals(DeviceConstant.RailwayDeviceType.HAND_TERMINAL.getDescription())) {
            longitude = data.getDouble("lng");
            latitude = data.getDouble("lat");
        } else if (deviceTypeName.equals(DeviceConstant.RailwayDeviceType.SMART_SECURITY_HAT.getDescription())) {
            longitude = data.getDouble("lng");
            latitude = data.getDouble("lat");
        }

        if (Objects.isNull(longitude) || Objects.isNull(latitude)) {
            return;
        }
        // 0.更新设备本身定位信息
        LambdaUpdateWrapper<EquipmentLocation> allDeviceUw = new LambdaUpdateWrapper<>();
        allDeviceUw.eq(EquipmentLocation::getEquipmentId, device.getId());
        allDeviceUw.eq(EquipmentLocation::getTenantId, tenantId);
        allDeviceUw.eq(EquipmentLocation::getLocationType, 1);// 北斗定位
        allDeviceUw.orderByDesc(EquipmentLocation::getId);
        EquipmentLocation entity = new EquipmentLocation();
        entity.setEquipmentLat(latitude.toString());
        entity.setEquipmentLng(longitude.toString());
        entity.setModifyTime(new Date());
        baseMapper.update(entity, allDeviceUw);

        // 1.机械定位处理
        if (deviceTypeName.equals(DeviceConstant.RailwayDeviceType.BEIDOU_LOCATION.getDescription())) {
            mechanicalLocationHandler(device, tenantId, platformProjectName,longitude, latitude, heading);
        }
        // 2.人员定位处理
        if (deviceTypeName.equals(DeviceConstant.RailwayDeviceType.HAND_TERMINAL.getDescription())
            || deviceTypeName.equals(DeviceConstant.RailwayDeviceType.SMART_SECURITY_HAT.getDescription())) {
            personLocationHandler(device, tenantId, platformProjectName, longitude, latitude);
        }

    }

    private void mechanicalLocationHandler(Device device, String tenantId, String platformProjectName, Double longitude, Double latitude, Double heading) {
        // 1.查询机械
        Mechanical mechanical = mechanicalService.queryMechanicalByDeviceCode(device.getId(), tenantId);
        if (Objects.isNull(mechanical)) {
            return;
        }
        if (mechanical.getXOffset() != null && mechanical.getYOffset() != null) {
            // 偏移量（单位：米）
            double x = mechanical.getXOffset(); // x轴偏移量
            double y = mechanical.getYOffset();  // y轴偏移量
            // 计算新的经纬度点
            double[] newCoordinates = CraneUtil.calculateNewCoordinatesNew1(longitude, latitude, x, y, heading);
            longitude = newCoordinates[0];
            latitude = newCoordinates[1];
        }
        // 2.更新机械经纬度信息
        LambdaQueryWrapper<EquipmentLocation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EquipmentLocation::getEquipmentId, mechanical.getId());
        queryWrapper.eq(EquipmentLocation::getTenantId, tenantId);
        queryWrapper.eq(EquipmentLocation::getLocationType, 1);// 北斗定位
        queryWrapper.orderByDesc(EquipmentLocation::getId);
        queryWrapper.last("limit 1");
        EquipmentLocation equipmentLocation = baseMapper.selectOne(queryWrapper);
        if (!Objects.isNull(equipmentLocation)) {
            // 判断定位是否是静止点
            equipmentLocation.setEquipmentLat(latitude.toString());
            equipmentLocation.setEquipmentLng(longitude.toString());
            equipmentLocation.setModifyTime(new Date());
            baseMapper.updateById(equipmentLocation);
        }
        // 3.1更新机械关联设备的经纬度信息
        List<MechanicalRefDevice> refDeviceList = mechanical.getRefDeviceList();
        if (CollectionUtil.isNotEmpty(refDeviceList)) {
            List<String> deviceIds = refDeviceList.stream().map(MechanicalRefDevice::getDeviceId).collect(Collectors.toList());
            LambdaUpdateWrapper<EquipmentLocation> locationUw = new LambdaUpdateWrapper<>();
            locationUw.in(EquipmentLocation::getEquipmentId, deviceIds);
            locationUw.eq(EquipmentLocation::getTenantId, tenantId);
            locationUw.eq(EquipmentLocation::getLocationType, 1);// 北斗定位
            locationUw.orderByDesc(EquipmentLocation::getId);

            EquipmentLocation entity1 = new EquipmentLocation();
            entity1.setEquipmentLat(latitude.toString());
            entity1.setEquipmentLng(longitude.toString());
            entity1.setModifyTime(new Date());
            baseMapper.update(entity1, locationUw);
        }
        // 判断定位是否是静止点
        Integer moveFlag = getMechanicalMoveFlag(mechanical.getId(), tenantId, longitude, latitude);
        // 4.新增定位记录
        EquipmentLocationData equipmentLocationData = EquipmentLocationData.builder()
                .equipmentId(mechanical.getId())
                .equipmentCode(mechanical.getCode())
                .locationLat(latitude.toString())
                .locationLng(longitude.toString())
                .locationTime(new Date())
                .createTime(new Date())
                .modifyTime(new Date())
                .tenantId(tenantId)
                .build();
        equipmentLocationData.setMoveFlag(moveFlag);
        equipmentLocationDataMapper.insert(equipmentLocationData);
        // 5.处理告警
        // 5.1 查询告警规则配置
        LambdaQueryWrapper<RailGiveAlarmTypeConfig> objectQueryWrapper = new LambdaQueryWrapper<>();
        objectQueryWrapper.eq(RailGiveAlarmTypeConfig::getTenantId, tenantId);
        objectQueryWrapper.eq(RailGiveAlarmTypeConfig::getType, RailGiveAlarmTypeConfigEnum.AlarmType_23.getType());
        List<RailGiveAlarmTypeConfig> railGiveAlarmTypeConfigList = giveAlarmTypeConfigMapper.selectList(objectQueryWrapper);
        if (CollectionUtil.isEmpty(railGiveAlarmTypeConfigList)) {
            return;
        }
        Boolean alarmFlag = false;
        RailGiveAlarmTypeConfig config = railGiveAlarmTypeConfigList.get(0);
        // 5.2 定位相关告警处理
        String mechanicalId = mechanical.getId();
        List<CraneOperationArea> operationAreas = craneOperationAreaMapper.selectByMechanicalId(mechanicalId);
        if (CollectionUtil.isNotEmpty(operationAreas)) {
            String HHmmss = cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.NORM_TIME_PATTERN);
            List<CraneOperationArea> alarmAreaList = new ArrayList<>();
            List<CraneOperationArea> notWorkTimeAreaList = new ArrayList<>();
            // 5.3 遍历所有区域，判断是否在作业时间
            for (CraneOperationArea operationArea : operationAreas) {
                List<WorkTime> workTimeList = operationArea.getWorkTimeList();
                // 判断HHmmss 在WorkTime 之间
                boolean hHmmssInHHmmssRange = CraneUtil.isHHmmssInHHmmssRangeList(HHmmss, workTimeList);
                if (hHmmssInHHmmssRange) {
                    alarmAreaList.add(operationArea);
                } else {
                    notWorkTimeAreaList.add(operationArea);
                }
            }

            // 5.4 不在作业时间，直接不告警
            if (CollectionUtil.isNotEmpty(notWorkTimeAreaList)) {
                for (CraneOperationArea operationArea : notWorkTimeAreaList){
                    operationArea.setAlarmFlag(false);
                    craneOperationAreaMapper.updateById(operationArea);
                }
            }

            // 5.5 判断设置是否启用
            if (config.getEnabled() == 1) {
                // 5.5.1 未启用则取消所有该设备的人员侵限告警
                alarmFlag = false;
            } else {
                // 5.5.2 启用则判断是否在作业区间
                if (CollectionUtil.isNotEmpty(alarmAreaList)) {
                    for (CraneOperationArea operationArea : alarmAreaList) {
                        boolean isInOperationArea = CraneUtil.isInPolygon(equipmentLocationData.getLocationLng(), equipmentLocationData.getLocationLat(), operationArea.getLocation());
                        // 多个区域在其中一个就不告警
                        if (isInOperationArea) {
                            alarmFlag = false;
                            break;
                        } else {
                            alarmFlag = true;
                        }
                    }
                }
            }
            // 5.6 处理告警
            if (alarmFlag) {
                String content = "离开作业区域，请注意";
                craneSafeMonitorService.triggerAlarmWithAreaIds(device, mechanical.getId(), mechanical.getName(), RailGiveAlarmTypeConfigEnum.AlarmType_23, config, content, platformProjectName, null);
                // 根据告警状态更新区域告警状态
                if (CollectionUtil.isNotEmpty(alarmAreaList)) {
                    for (CraneOperationArea operationArea : alarmAreaList){
                        operationArea.setAlarmFlag(true);
                        craneOperationAreaMapper.updateById(operationArea);
                    }
                }
            } else {
                craneSafeMonitorService.cancelAlarm(device, mechanical.getId(), config.getType());
                // 根据告警状态更新区域告警状态
                if (CollectionUtil.isNotEmpty(alarmAreaList)) {
                    for (CraneOperationArea operationArea : alarmAreaList) {
                        // 查询区域下是否还有告警
                        boolean leftAreaAlarm = railGiveSystemAlarmMapper.isLeftAreaAlarm(operationArea.getId().toString(), Arrays.asList(RailGiveAlarmTypeConfigEnum.AlarmType_23.getType()));
                        if (!leftAreaAlarm) {
                            operationArea.setAlarmFlag(false);
                            craneOperationAreaMapper.updateById(operationArea);
                        }
                    }
                }
            }
        } else {
            craneSafeMonitorService.cancelAlarm(device, mechanical.getId(), config.getType());
        }
//        craneSafeMonitorService.updateDeviceAlarmStatus(device);
    }

    /**
     * 机械计算移动轨迹点
     *
     *         // 需确认如何判断人员或吊车的轨迹点点位，需要有一个算法
     *         // 1.当前点与前一个轨迹点的距离大于最大阈值距离max，则为轨迹点。
     *         // 2.如果小于最大阈值距离max，大于最小距离min，则判断时间是否大于持续时间，大于则为轨迹点。
     *         // 3.如果小于最小距离，则为非轨迹点。
     *         // 1.查询机械最近的未结束的轨迹记录
     *
     * @param mechanicalId
     * @param tenantId
     * @param longitude
     * @param latitude
     * @return
     */
    private Integer getMechanicalMoveFlag(String mechanicalId, String tenantId, Double longitude, Double latitude) {
        // 判断定位是否是静止点
        LambdaQueryWrapper<MechanicalWorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MechanicalWorkRecord::getTenantId, tenantId);
        queryWrapper.eq(MechanicalWorkRecord::getMechanicalId, mechanicalId);
        // 结束时间为空，表示未结束
        queryWrapper.isNull(MechanicalWorkRecord::getEndTime);
        queryWrapper.orderByDesc(MechanicalWorkRecord::getCreateTime);
        queryWrapper.last("limit 1");
        MechanicalWorkRecord mechanicalWorkRecord = mechanicalWorkRecordMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(mechanicalWorkRecord)) {
            // 2.查询上一个轨迹点
            Date startTime = mechanicalWorkRecord.getStartTime();
            LambdaQueryWrapper<EquipmentLocationData> locationDataQueryWrapper = new LambdaQueryWrapper<>();
            locationDataQueryWrapper.eq(EquipmentLocationData::getEquipmentId, mechanicalId);
            locationDataQueryWrapper.eq(EquipmentLocationData::getTenantId, tenantId);
            locationDataQueryWrapper.eq(EquipmentLocationData::getMoveFlag, 1);
            // 大于等于开始时间
            locationDataQueryWrapper.ge(EquipmentLocationData::getLocationTime, startTime);
            locationDataQueryWrapper.orderByDesc(EquipmentLocationData::getCreateTime);
            locationDataQueryWrapper.last("limit 1");
            EquipmentLocationData lastLocationData = equipmentLocationDataMapper.selectOne(locationDataQueryWrapper);
            if (ObjectUtil.isNotEmpty(lastLocationData)) {
                // 3.计算距离
                Double lat1 = Double.parseDouble(lastLocationData.getLocationLat());
                Double lng1 = Double.parseDouble(lastLocationData.getLocationLng());
                double distance = CraneUtil.getDistance(lat1, lng1, latitude, longitude);
                if (distance > carMaxThresholdDistance) {
                    return 1;
                } else if (distance > carMinThresholdDistance) {
                    // 4.计算时间
                    Date lastLocationTime = lastLocationData.getLocationTime();
                    long time = (new Date().getTime() - lastLocationTime.getTime()) / 1000;
                    if (time > carThresholdTime * 60) {
                        return 1;
                    } else {
                        return 0;
                    }
                } else {
                    return 0;
                }
            } else {
                return 1;
            }
        }
        return 0;
    }

    private void personLocationHandler(Device device, String tenantId, String platformProjectName, Double longitude, Double latitude) {
        // 1.设备查询人员
        LambdaQueryWrapper<PersonRefDevice> personDeviceQuery = new LambdaQueryWrapper<>();
        personDeviceQuery.eq(PersonRefDevice::getDeviceId, device.getId());
        personDeviceQuery.eq(PersonRefDevice::getTenantId, tenantId);
        PersonRefDevice personRefDevice = personRefDeviceMapper.selectOne(personDeviceQuery);
        if (Objects.isNull(personRefDevice)) {
            return;
        }
        Long personId = personRefDevice.getPersonId();
        RailLinkappRosterPersonnel person = railLinkappRosterPersonnelMapper.selectById(personId);
        if (ObjectUtil.isNull(person)) {
            return;
        }

        // 2.判断定位是否是静止点
        Integer moveFlag = getPersonMoveFlag(personId, tenantId, longitude, latitude);
        // 2.1 判断前一个轨迹点是否存在人员是否长时间逗留, 并更新长期停留状态
        updateStayFlag(personId, tenantId, longitude, latitude);

        // 3.新增定位记录
        PersonLocationData personLocationData = PersonLocationData.builder()
                .tenantId(tenantId)
                .personId(personId)
                .deviceId(device.getId())
                .deviceCode(device.getCode())
                .deviceName(device.getName())
                .locationLat(latitude.toString())
                .locationLng(longitude.toString())
                .locationTime(new Date())
                .moveFlag(moveFlag)
                .stayFlag(0)// 默认为0，表示未长期停留
                .createTime(new Date())
                .modifyTime(new Date())
                .build();
        personLocationDataMapper.insert(personLocationData);

        // 4.处理告警
        // 4.1 查询人员相关告警规则配置
        LambdaQueryWrapper<RailGiveAlarmTypeConfig> alarmConfigQuery = new LambdaQueryWrapper<>();
        alarmConfigQuery.eq(RailGiveAlarmTypeConfig::getTenantId, tenantId);
        alarmConfigQuery.in(RailGiveAlarmTypeConfig::getType, Arrays.asList(
                RailGiveAlarmTypeConfigEnum.AlarmType_36.getType(), // 人员侵限告警
                RailGiveAlarmTypeConfigEnum.AlarmType_37.getType()  // 离开作业区域告警
        ));
        List<RailGiveAlarmTypeConfig> alarmConfigs = giveAlarmTypeConfigMapper.selectList(alarmConfigQuery);

        if (CollectionUtil.isNotEmpty(alarmConfigs)) {
            for (RailGiveAlarmTypeConfig config : alarmConfigs) {
                if (config.getType().equals(RailGiveAlarmTypeConfigEnum.AlarmType_37.getType())) {
                    // 4.2 查询人员作业区域
                    List<PersonOperationArea> operationAreas = personOperationAreaMapper.selectByPersonId(personId.intValue());
                    if (CollectionUtil.isEmpty(operationAreas)) {
                        // 取消告警
                        craneSafeMonitorService.cancelAlarm(device, person.getId().toString(), config.getType());
                        continue;
                    }
                    String HHmmss = cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.NORM_TIME_PATTERN);
                    Boolean alarmFlag = false;
                    List<PersonOperationArea> workTimeAreas = new ArrayList<>();
                    List<PersonOperationArea> notWorkTimeAreas = new ArrayList<>();
                    // 4.3 遍历人员作业区域，判断是否在作业时间内
                    for (PersonOperationArea operationArea : operationAreas) {
                        List<WorkTime> workTimeList = operationArea.getWorkTimeList();
                        // 判断当前时间是否在作业时间范围内
                        boolean isInWorkTime = CraneUtil.isHHmmssInHHmmssRangeList(HHmmss, workTimeList);
                        if (isInWorkTime) {
                            workTimeAreas.add(operationArea);
                        } else {
                            notWorkTimeAreas.add(operationArea);
                        }
                    }

                    // 4.4 不在作业时间的区域，直接不告警，取消告警状态
                    if (CollectionUtil.isNotEmpty(notWorkTimeAreas)) {
                        for (PersonOperationArea operationArea : notWorkTimeAreas) {
                            operationArea.setAlarmFlag(false);
                            personOperationAreaMapper.updateById(operationArea);
                        }
                    }

                    // 4.5 判断设置是否启用
                    if (config.getEnabled() == 1) {
                        // 4.5.1 未启用则取消所有该设备的人员侵限告警
                        alarmFlag = false;
                    } else {
                        // 4.5.2 启用则判断是否在作业区域内
                        if (CollectionUtil.isNotEmpty(workTimeAreas)) {
                            for (PersonOperationArea operationArea : workTimeAreas) {
                                boolean isInOperationArea = CraneUtil.isInPolygon(longitude.toString(), latitude.toString(), operationArea.getLocation());
                                // 多个区域在其中一个就不告警
                                if (isInOperationArea) {
                                    alarmFlag = false;
                                    break;
                                } else {
                                    alarmFlag = true;
                                }
                            }
                        }
                    }

                    // 4.6 触发告警或取消告警
                    if (alarmFlag) {
                        String content = "离开作业区域，请注意";
                        List<String> alarmAreaIds = workTimeAreas.stream().map(PersonOperationArea::getId).map(Object::toString).collect(Collectors.toList());
                        String areaIdsStr = String.join(",", alarmAreaIds);
                        craneSafeMonitorService.triggerAlarmWithAreaIds(device, person.getId().toString(), person.getRealName(), RailGiveAlarmTypeConfigEnum.AlarmType_37, config, content, platformProjectName, areaIdsStr);
                        // 4.6.1 更新作业区域告警状态（仅针对在作业时间内的区域）
                        if (CollectionUtil.isNotEmpty(workTimeAreas)) {
                            for (PersonOperationArea operationArea : workTimeAreas) {
                                operationArea.setAlarmFlag(true);
                                personOperationAreaMapper.updateById(operationArea);
                            }
                        }
                    } else {
                        // 4.6.2 取消告警
                        craneSafeMonitorService.cancelAlarm(device, person.getId().toString(), config.getType());
                        if (CollectionUtil.isNotEmpty(workTimeAreas)) {
                            for (PersonOperationArea operationArea : workTimeAreas) {
                                // 判断区域是否还有其他告警
                                boolean leftAreaAlarm = railGiveSystemAlarmMapper.isLeftAreaAlarm(operationArea.getId().toString(), Arrays.asList(RailGiveAlarmTypeConfigEnum.AlarmType_37.getType()));
                                if (!leftAreaAlarm) {
                                    operationArea.setAlarmFlag(false);
                                    personOperationAreaMapper.updateById(operationArea);
                                }
                            }
                        }
                    }
                } else if (config.getType().equals(RailGiveAlarmTypeConfigEnum.AlarmType_36.getType())) {
                    // 4.2 查询垂直保护区
                    LambdaQueryWrapper<CraneVerticalProtectionArea> verticalAreaQuery = new LambdaQueryWrapper<>();
                    verticalAreaQuery.eq(CraneVerticalProtectionArea::getTenantId, tenantId);
                    verticalAreaQuery.eq(CraneVerticalProtectionArea::getEnabledFlag, 1);
                    List<CraneVerticalProtectionArea> verticalProtectionAreas = craneVerticalProtectionAreaMapper.selectList(verticalAreaQuery);

                    if (CollectionUtil.isEmpty(verticalProtectionAreas)) {
                        // 取消告警
                        craneSafeMonitorService.cancelAlarm(device, person.getId().toString(), config.getType());
                        continue;
                    }

                    String HHmmss = cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.NORM_TIME_PATTERN);
                    Boolean alarmFlag = false;
                    List<CraneVerticalProtectionArea> workTimeAreas = new ArrayList<>();
                    List<CraneVerticalProtectionArea> notWorkTimeAreas = new ArrayList<>();
                    List<String> alarmAreaIds = new ArrayList<>();

                    // 4.3 遍历垂直保护区，判断是否在作业时间内
                    for (CraneVerticalProtectionArea protectionArea : verticalProtectionAreas) {
                        List<WorkTime> workTimeList = protectionArea.getWorkTimeList();
                        // 判断当前时间是否在作业时间范围内
                        boolean isInWorkTime = CraneUtil.isHHmmssInHHmmssRangeList(HHmmss, workTimeList);
                        if (isInWorkTime) {
                            workTimeAreas.add(protectionArea);
                        } else {
                            notWorkTimeAreas.add(protectionArea);
                        }
                    }

                    // 4.4 不在作业时间的区域，直接不告警，取消告警状态
                    if (CollectionUtil.isNotEmpty(notWorkTimeAreas)) {
                        for (CraneVerticalProtectionArea protectionArea : notWorkTimeAreas) {
                            protectionArea.setAlarmFlag(false);
                            craneVerticalProtectionAreaMapper.updateById(protectionArea);
                        }
                    }

                    List<CraneVerticalProtectionArea> alarmAreas = new ArrayList<>();
                    // 4.5 判断设置是否启用
                    if (config.getEnabled() == 1) {
                        // 4.5.1 未启用则取消所有该设备的人员侵限告警
                        alarmFlag = false;
                    } else {
                        // 4.5.2 启用则判断是否在垂直保护区内（危险区域）
                        if (CollectionUtil.isNotEmpty(workTimeAreas)) {
                            for (CraneVerticalProtectionArea protectionArea : workTimeAreas) {
                                boolean isInDangerArea = CraneUtil.isInPolygon(longitude.toString(), latitude.toString(), protectionArea.getLocation());
                                if (isInDangerArea) {
                                    alarmFlag = true;
                                    alarmAreaIds.add(protectionArea.getId().toString());
                                    alarmAreas.add(protectionArea);
                                }
                            }
                        }
                    }

                    // 4.6 触发告警或取消告警
                    if (alarmFlag) {
                        String content = "入侵保护区，请注意";
                        String areaIdsStr = String.join(",", alarmAreaIds);
                        craneSafeMonitorService.triggerAlarmWithAreaIds(device, person.getId().toString(), person.getRealName(), RailGiveAlarmTypeConfigEnum.AlarmType_36, config, content, platformProjectName, areaIdsStr);
                        // 4.6.1 更新垂直保护区告警状态（仅针对在作业时间内的区域）
                        if (CollectionUtil.isNotEmpty(alarmAreas)) {
                            for (CraneVerticalProtectionArea protectionArea : alarmAreas) {
                                protectionArea.setAlarmFlag(true);
                                craneVerticalProtectionAreaMapper.updateById(protectionArea);
                            }
                        }
                    } else {
                        // 4.6.2 取消告警
                        craneSafeMonitorService.cancelAlarm(device, person.getId().toString(), config.getType());
                        if (CollectionUtil.isNotEmpty(workTimeAreas)) {
                            for (CraneVerticalProtectionArea protectionArea : workTimeAreas) {
                                // 判断区域是否还有其他告警
                                boolean leftAreaAlarm = railGiveSystemAlarmMapper.isLeftAreaAlarm(protectionArea.getId().toString(), Arrays.asList(
                                        RailGiveAlarmTypeConfigEnum.AlarmType_17.getType(),// 吊车安全限界告警
                                        RailGiveAlarmTypeConfigEnum.AlarmType_21.getType(),// 吊车施工防侵限告警
                                        RailGiveAlarmTypeConfigEnum.AlarmType_22.getType(),// 吊车防倾倒侵限告警
                                        RailGiveAlarmTypeConfigEnum.AlarmType_36.getType()));// 人员侵限告警
                                if (!leftAreaAlarm) {
                                    protectionArea.setAlarmFlag(false);
                                    craneVerticalProtectionAreaMapper.updateById(protectionArea);
                                }
                            }
                        }
                    }
                }
            }
        }
//        // 5.更新设备告警状态
//        craneSafeMonitorService.updateDeviceAlarmStatus(device);
    }

    /**
     * 判断前一个轨迹点是否存在人员是否长时间逗留
     * 该人员在一个地方超过 0.5h未动（需要测试设备精度，暂定2m,做到数据库配置），在此点上打出该标识
     *
     * @param personId
     * @param tenantId
     * @param longitude
     * @param latitude
     * @return
     */
    private void updateStayFlag(Long personId, String tenantId, Double longitude, Double latitude) {
        // 2.查询上一个轨迹点
        LambdaQueryWrapper<PersonLocationData> locationDataQueryWrapper = new LambdaQueryWrapper<>();
        locationDataQueryWrapper.eq(PersonLocationData::getPersonId, personId);
        locationDataQueryWrapper.eq(PersonLocationData::getTenantId, tenantId);
        locationDataQueryWrapper.eq(PersonLocationData::getMoveFlag, 1);
        locationDataQueryWrapper.orderByDesc(PersonLocationData::getCreateTime);
        locationDataQueryWrapper.last("limit 1");
        PersonLocationData lastLocationData = personLocationDataMapper.selectOne(locationDataQueryWrapper);
        if (ObjectUtil.isNotEmpty(lastLocationData)) {
            // 3.计算距离
            Double lat1 = Double.parseDouble(lastLocationData.getLocationLat());
            Double lng1 = Double.parseDouble(lastLocationData.getLocationLng());
            double distance = CraneUtil.getDistance(lat1, lng1, latitude, longitude);
            Date lastLocationTime = lastLocationData.getLocationTime();
            long time = (new Date().getTime() - lastLocationTime.getTime()) / 1000;
            if (distance < personStayThresholdDistance && time > personStayThresholdTime * 60) {
                // 4.更新标识
                lastLocationData.setStayFlag(1);
                lastLocationData.setModifyTime(new Date());
                personLocationDataMapper.updateById(lastLocationData);
            }
        }
    }

    /**
     * 人员计算移动轨迹点
     *
     * @param personId
     * @param tenantId
     * @param longitude
     * @param latitude
     * @return
     */
    private Integer getPersonMoveFlag(Long personId, String tenantId, Double longitude, Double latitude) {
        // 判断定位是否是静止点
        LambdaQueryWrapper<PersonWorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonWorkRecord::getTenantId, tenantId);
        queryWrapper.eq(PersonWorkRecord::getPersonId, personId);
        // 结束时间为空，表示未结束
        queryWrapper.isNull(PersonWorkRecord::getEndTime);
        queryWrapper.orderByDesc(PersonWorkRecord::getCreateTime);
        queryWrapper.last("limit 1");
        PersonWorkRecord personWorkRecord = personWorkRecordMapper.selectOne(queryWrapper);

        if (ObjectUtil.isNotEmpty(personWorkRecord)) {
            // 2.查询上一个轨迹点
            Date startTime = personWorkRecord.getStartTime();
            LambdaQueryWrapper<PersonLocationData> locationDataQueryWrapper = new LambdaQueryWrapper<>();
            locationDataQueryWrapper.eq(PersonLocationData::getPersonId, personId);
            locationDataQueryWrapper.eq(PersonLocationData::getTenantId, tenantId);
            locationDataQueryWrapper.eq(PersonLocationData::getMoveFlag, 1);
            // 大于等于开始时间
            locationDataQueryWrapper.ge(PersonLocationData::getLocationTime, startTime);
            locationDataQueryWrapper.orderByDesc(PersonLocationData::getCreateTime);
            locationDataQueryWrapper.last("limit 1");
            PersonLocationData lastLocationData = personLocationDataMapper.selectOne(locationDataQueryWrapper);

            if (ObjectUtil.isNotEmpty(lastLocationData)) {
                // 3.计算距离
                Double lat1 = Double.parseDouble(lastLocationData.getLocationLat());
                Double lng1 = Double.parseDouble(lastLocationData.getLocationLng());
                double distance = CraneUtil.getDistance(lat1, lng1, latitude, longitude);

                if (distance > personMaxThresholdDistance) {
                    return 1;
                } else if (distance > personMinThresholdDistance) {
                    // 4.计算时间
                    Date lastLocationTime = lastLocationData.getLocationTime();
                    long time = (new Date().getTime() - lastLocationTime.getTime()) / 1000;
                    if (time > personThresholdTime * 60) {
                        return 1;
                    } else {
                        return 0;
                    }
                } else {
                    return 0;
                }
            } else {
                return 1;
            }
        }
        return 0;
    }

}