package com.easylinkin.linkappapi.mechanical.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.mechanical.entity.EquipmentLocationData;
import com.easylinkin.linkappapi.mechanical.mapper.EquipmentLocationDataMapper;
import com.easylinkin.linkappapi.mechanical.service.IEquipmentLocationDataService;
import org.springframework.stereotype.Service;

/**
 * 设备设施定位数据表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class EquipmentLocationDataServiceImpl extends ServiceImpl<EquipmentLocationDataMapper, EquipmentLocationData> implements IEquipmentLocationDataService {
}