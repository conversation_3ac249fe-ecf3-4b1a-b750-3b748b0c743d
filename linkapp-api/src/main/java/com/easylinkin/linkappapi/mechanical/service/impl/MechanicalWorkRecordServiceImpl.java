package com.easylinkin.linkappapi.mechanical.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.mechanical.entity.MechanicalWorkRecord;
import com.easylinkin.linkappapi.mechanical.mapper.MechanicalWorkRecordMapper;
import com.easylinkin.linkappapi.mechanical.service.IMechanicalWorkRecordService;
import org.springframework.stereotype.Service;

/**
 * 机械工作记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class MechanicalWorkRecordServiceImpl extends ServiceImpl<MechanicalWorkRecordMapper, MechanicalWorkRecord> implements IMechanicalWorkRecordService {
    @Override
    public IPage<MechanicalWorkRecord> queryPageList(RequestModel<MechanicalWorkRecord> requestModel) {
        return baseMapper.queryPageList(requestModel.getPage(), requestModel.getCustomQueryParams());
    }
}