package com.easylinkin.linkappapi.mechanical.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.mechanical.entity.EquipmentLocation;
import com.easylinkin.linkappapi.mechanical.vo.EquipmentLocationVo;
import com.easylinkin.linkappapi.openapi.dto.DatapushDTO;

/**
 * 铁路设备设施定位表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface IEquipmentLocationService extends IService<EquipmentLocation> {
    IPage<EquipmentLocation> listPage(RequestModel<EquipmentLocationVo> requestModel);

    void datapushHandler(DatapushDTO datapushDTO);
}