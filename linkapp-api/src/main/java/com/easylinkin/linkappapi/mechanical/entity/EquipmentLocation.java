package com.easylinkin.linkappapi.mechanical.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.easylinkin.linkappapi.danger.entity.Danger;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 铁路设备设施定位表
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("rail_equipment_location")
public class EquipmentLocation extends Model<EquipmentLocation> implements Serializable{
    private static final long serialVersionUID=1L;

    @TableId
    private String id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id_")
    private String tenantId;

    /**
     * 设备设施类型id -1为机械设备
     */
    @TableField(value = "equipment_type_")
    private String equipmentType;

    /**
     * 设备设施类型名称
     */
    @TableField(exist = false)
    private String equipmentTypeName;

    /**
     * 设备设施id
     */
    @TableField(value = "equipment_id_")
    private String equipmentId;

    /**
     * 设备设施名称
     */
    @TableField(value = "equipment_name_")
    private String equipmentName;

    /**
     * 定位类型 1、北斗定位 2、人员定位 3、地图打点
     */
    @TableField(value = "location_type_")
    private Integer locationType;

    /**
     * 设备设施经度
     */
    @TableField(value = "equipment_lng_")
    private String equipmentLng;

    /**
     * 设备设施纬度
     */
    @TableField(value = "equipment_lat_")
    private String equipmentLat;

    /**
     * 创建人id
     */
    @TableField(value = "create_id_")
    private Long createId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField(value = "modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField(value = "remark_")
    private String remark;

    /**
     * 设备设施编码
     */
    @TableField(exist = false)
    private String equipmentCode;

    /**
     * 0:离线; 1:在线
     */
    @TableField(exist = false)
    private Integer onlineState;

    /**
     * ai设备最新上传的图片 设备上传图片
     */
    @TableField(exist = false)
    private String aiRecordImg;

    /**
     * 机械状态：0 在场 1 已退场
     */
    @TableField(exist = false)
    private Integer status;
    /**
     * 设备状态,0:正常; 1:告警；新增默认：正常
     */
    @TableField(exist = false)
    private Integer isAlarm;
}
