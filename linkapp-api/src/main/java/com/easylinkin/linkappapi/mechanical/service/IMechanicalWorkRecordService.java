package com.easylinkin.linkappapi.mechanical.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.mechanical.entity.MechanicalWorkRecord;

/**
 * 机械工作记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface IMechanicalWorkRecordService extends IService<MechanicalWorkRecord> {
    IPage<MechanicalWorkRecord> queryPageList(RequestModel<MechanicalWorkRecord> requestModel);
}