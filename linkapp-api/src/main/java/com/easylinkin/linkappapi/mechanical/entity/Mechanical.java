package com.easylinkin.linkappapi.mechanical.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.easylinkin.linkappapi.common.translate.Code2Text;
import com.easylinkin.linkappapi.common.translate.CodeI18n;
import com.easylinkin.linkappapi.common.translate.impl.DictTranslateor;
import com.easylinkin.linkappapi.common.translate.impl.RosterNameTranslateor;
import com.easylinkin.linkappapi.common.translate.impl.TenantProjectNameTranskateor;
import com.easylinkin.linkappapi.common.typehandler.StringToListHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 铁路机械表
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@CodeI18n
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("rail_mechanical")
public class Mechanical implements Serializable{
    private static final long serialVersionUID=1L;

    @TableId
    @TableField(value = "id")
    private String id;

    /**
     * 租户id
     */
    @Code2Text(translateor = TenantProjectNameTranskateor.class)
    @TableField(value = "tenant_id_")
    private String tenantId;

    /**
     * 工点id
     */
    @TableField(value = "work_point_id_")
    private String workPointId;

    /**
     * 工点名称
     */
    @TableField(exist = false)
    private String workPointName;

    /**
     * 机械类型
     */
    @Code2Text(value = "machine_type", translateor = DictTranslateor.class)
    @TableField(value = "type_")
    private String type;

    /**
     * 机械名称
     */
    @TableField(value = "name_")
    private String name;

    /**
     * 机械编号
     */
    @TableField(value = "code_")
    private String code;

    /**
     * 机械状态：0 在场 1 已退场
     */
    @TableField(value = "status_")
    private Integer status;

    /**
     * 产权类型 1：自有 2：租赁（劳务自带）
     */
    @TableField(value = "owner_ship_type_")
    private Integer ownerShipType;

    /**
     * 产权单位名称
     */
    @TableField(value = "owner_ship_name_")
    private String ownerShipName;

    /**
     * 规格型号
     */
    @TableField(value = "specification_")
    private String specification;

    /**
     * 进场时间
     */
    @TableField(value = "enter_time_")
    private Date enterTime;

    /**
     * 出场时间
     */
    @TableField(value = "out_time_")
    private Date outTime;

    /**
     * 司机ids
     */
    @Code2Text(translateor = RosterNameTranslateor.class)
    @TableField(value = "driver_ids_", typeHandler = StringToListHandler.class)
    private List<String> driverIds;

    /**
     * 施工单位管理员id
     */
    @TableField(value = "manager_id_")
    private String managerId;

    /**
     * 施工单位管理员姓名
     */
    @TableField(value = "manager_name_")
    private String managerName;

    /**
     * 是否特种机械： 0：否 1：是
     */
    @TableField(value = "is_special_equipment_")
    private Integer isSpecialEquipment;

    /**
     * 机械验收情况
     */
    @TableField(value = "machinery_acceptance_status_")
    private String machineryAcceptanceStatus;

    /**
     * 机械计划拆除时间
     */
    @TableField(value = "planned_removal_time_")
    private Date plannedRemovalTime;

    /**
     * 有无助力： 0：无 1：有
     */
    @TableField(value = "is_power_")
    private Integer isPower;

    /**
     * 生产厂家
     */
    @TableField(value = "manufacturer_")
    private String manufacturer;

    /**
     * 联系方式
     */
    @TableField(value = "manufacturer_tel_")
    private String manufacturerTel;

    /**
     * 维护人
     */
    @TableField(value = "maintainer_")
    private String maintainer;

    /**
     * 联系方式
     */
    @TableField(value = "maintainer_tel_")
    private String maintainerTel;

    /**
     * 维护日期
     */
    @TableField(value = "maintainer_date_")
    private Date maintainerDate;

    /**
     * 验收文件列表
     */
    @TableField(value = "inspection_file_list_", typeHandler = StringToListHandler.class)
    private List<String> inspectionFileList;

    /**
     * 合格证书文件列表
     */
    @TableField(value = "certificate_file_list_", typeHandler = StringToListHandler.class)
    private List<String> certificateFileList;

    /**
     * 鉴定证书文件列表
     */
    @TableField(value = "appraisal_certificate_file_list_", typeHandler = StringToListHandler.class)
    private List<String> appraisalCertificateFileList;

    /**
     * 登记使用证书文件列表
     */
    @TableField(value = "registration_certificate_file_list_", typeHandler = StringToListHandler.class)
    private List<String> registrationCertificateFileList;

    /**
     * 机械照片文件列表
     */
    @TableField(value = "equipment_photo_file_list_",  typeHandler = StringToListHandler.class)
    private List<String> equipmentPhotoFileList;

    /**
     * 机械特殊信息json
     */
    @TableField(value = "specifical_info_")
    private String specificalInfo;

    /**
     * 删除状态 0：未删除 1：已删除
     */
    @TableField(value = "delete_state")
    @TableLogic(value = "0", delval = "1")
    private Integer deleteState;

    /**
     * 创建人id
     */
    @TableField(value = "create_id_")
    private Long createId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField(value = "modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField(value = "remark_")
    private String remark;

    /**
     * 定位x偏移量
     */
    @TableField(value = "x_offset_")
    private Double xOffset;
    /**
     * 定位y偏移量
     */
    @TableField(value = "y_offset_")
    private Double yOffset;

    /**
     * 五定图时间：年月日时分
     */
    @TableField(value = "five_chart_time_")
    private Date fiveChartTime;

    /**
     * 五定图作业内容
     */
    @TableField(value = "five_chart_content_")
    private String fiveChartContent;

    /**
     * 五定图图片：五张图片；
     */
    @TableField(value = "five_chart_img_",  typeHandler = StringToListHandler.class)
    private List<String> fiveChartImg;

    /**
     * 关联设备列表
     */
    @TableField(exist = false)
    private List<MechanicalRefDevice> refDeviceList;

    /**
     * 机械类型列表
     */
    @TableField(exist = false)
    private List<String> types;
    /**
     * 进场开始时间
     */
    @TableField(exist = false)
    private Date enterStartTime;
    /**
     * 进场结束时间
     */
    @TableField(exist = false)
    private Date enterEndTime;
    /**
     * 出场开始时间
     */
    @TableField(exist = false)
    private Date outStartTime;
    /**
     * 出场结束时间
     */
    @TableField(exist = false)
    private Date outEndTime;

    /**
     * 机械编号/名称
     */
    @TableField(exist = false)
    private String codeOrName;

    /**
     * 设备在线状态：0:离线; 1:正常
     */
    @TableField(exist = false)
    private Integer onlineState;

    /**
     * 设备报警状态：0:正常; 1:报警
     */
    @TableField(exist = false)
    private Integer alarmState;

    /**
     * 是否关联主机设备
     */
    @TableField(exist = false)
    private Boolean isRefMainDevice;
}
