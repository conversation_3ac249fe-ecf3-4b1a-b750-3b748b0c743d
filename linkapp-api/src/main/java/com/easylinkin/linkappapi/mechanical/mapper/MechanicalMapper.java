package com.easylinkin.linkappapi.mechanical.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.mechanical.entity.Mechanical;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

/**
 * 铁路机械表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface MechanicalMapper extends BaseMapper<Mechanical> {
    IPage<Mechanical> listPage(Page page, @Param("customQueryParams") Mechanical customQueryParams);

    List<Mechanical> listPage(@Param("customQueryParams") Mechanical mechanical);

    Mechanical queryById(String id);

    Mechanical selectMechanicalByDeviceCode(@Param("id") String id, @Param("tenantId") String tenantId);

    List<Mechanical> selectMechanicalByOperationAreaId(@Param("id") Long id);

    List<Mechanical> selectMechanicalByWorkPointId(@Param("workPointId") String workPointId,  @Param("type") String type);

    List<String> selectDeviceByMechanicalIdAndType(@Param("mechanicalId") String mechanicalId, @Param("type") String type);

    @Update("update rail_mechanical set status_ = 1 where out_time_ <= now() and status_ = 0")
    void doDeparture();

    @Update("update rail_mechanical set out_time_ = null where id = #{id}")
    void setOutTimeNull(@Param("id") String id);

    @Select("select * from rail_mechanical where out_time_ <= now() and status_ = 0")
    List<Mechanical> selectMechanicalByOutTime();
}