package com.easylinkin.linkappapi.security.repository;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.easylinkin.linkappapi.security.entity.LinkappUserLoginInfo;

import site.morn.boot.data.jpa.JpaRepository;

/**
 * 登录信息Dao
 *
 * <AUTHOR>
 * @since 0.0.1-SNAPSHOT, 2018/12/10
 */
@Repository
public interface LinkappUserLoginInfoRepository extends JpaRepository<LinkappUserLoginInfo, Long> {

  /**
   * 重置用户登录锁定信息
   */
  @Modifying
  @Transactional
  @Query("update LinkappUserLoginInfo set errorCount = 0 ,lockTime = null where userId =:userId")
  void resetUserLoginLockInfo(@Param("userId") Long userId);
}
