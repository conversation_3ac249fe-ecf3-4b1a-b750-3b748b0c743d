package com.easylinkin.linkappapi.security.config;

import cn.hutool.http.HttpStatus;
import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.web.session.InvalidSessionStrategy;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MyInvalidSessionStrategy implements InvalidSessionStrategy {
    @Override
    public void onInvalidSessionDetected(HttpServletRequest httpServletRequest, HttpServletResponse response) throws IOException {
        /* 接口请求没有页面，给统一的友好提示，可以直接利用封装的的业务异常
        response.setStatus(HttpStatus.HTTP_UNAUTHORIZED);
        response.setContentType("application/json;charset=utf-8");
        response.getWriter().write("当前登录已失效！请重新登录");
        */
        throw new BusinessException("当前登录已失效！请重新登录");
    }
}
