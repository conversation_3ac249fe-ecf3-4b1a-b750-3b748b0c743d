package com.easylinkin.linkappapi.security.repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Repository;

import com.easylinkin.linkappapi.security.entity.LinkappPrivilege;

import site.morn.boot.data.jpa.JpaRepository;

/**
 * 权限数据访问
 *
 * <AUTHOR>
 * @since 0.1.1,  2019/08/28
 */
@Repository
public interface LinkappPrivilegeRepository extends JpaRepository<LinkappPrivilege, Long> {

  Set<LinkappPrivilege> findByIdIn(Collection ids);

  List<LinkappPrivilege> findAllByParentId(Long parentId);
}
