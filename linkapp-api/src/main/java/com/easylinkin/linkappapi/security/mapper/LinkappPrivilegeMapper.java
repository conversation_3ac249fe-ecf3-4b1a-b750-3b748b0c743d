package com.easylinkin.linkappapi.security.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.security.entity.LinkappPrivilege;
import com.easylinkin.linkappapi.security.entity.LinkappRoleRefPrivilege;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.TreeSet;

/**
 * 用户Mapper
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/20
 */
@Mapper
public interface LinkappPrivilegeMapper extends BaseMapper<LinkappPrivilege> {

	List<LinkappPrivilege> selectPrivilegeByRole(@Param("roleId") String roleId, @Param("type") Integer type);

	List<LinkappPrivilege> selectPrivilegeAll(LinkappPrivilege linkappPrivilege);

	/**
	 * 验证url是否重复
	 * @param linkappPrivilege
	 * @return
	 */
	List<LinkappPrivilege> checkPrivilegeUrlIsRepeat(LinkappPrivilege linkappPrivilege);

	/**
	 * 验证seq_ 排序号是否重复
	 * @param linkappPrivilege
	 * @return
	 */
	int checkPrivilegeSeqIsRepeat(LinkappPrivilege linkappPrivilege);


	TreeSet<LinkappPrivilege> selectPrivilegeByUser(@Param("userId") String userId, @Param("type") Integer type);

	TreeSet<LinkappPrivilege> selectPrivilegeCustomByUser(@Param("tenantId") String tenantId, @Param("userId") String userId,@Param("type")  Integer type);

	void deletePrivilege2Role(@Param("roleId") Long roleId, @Param("type") Integer type);

	void insertPrivilege2Role(LinkappRoleRefPrivilege roleRefPrivilege);

	void insertPrivilegeExt(LinkappPrivilege linkappPrivilege);

	void updatePrivilegeExt(LinkappPrivilege linkappPrivilege);

	List<LinkappPrivilege> getPrivilegeMaxId();

	LinkappPrivilege selectByCode(String code);

	@Select("SELECT COUNT(*) FROM linkapp_privilege lp LEFT JOIN linkapp_tenant_ref_privilege ltrp on lp.id_ = ltrp.privilege_id " +
			"WHERE ltrp.tenant_id = #{tenantId} and (lp.id_ = 99159 OR name_ like '%隐患排查%')")
	int selectHiddenTroubleMenu(@Param("tenantId") String tenantId);

}
