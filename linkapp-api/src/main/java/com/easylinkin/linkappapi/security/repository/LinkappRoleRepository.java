package com.easylinkin.linkappapi.security.repository;

import java.util.Collection;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.easylinkin.linkappapi.security.entity.LinkappRole;

import site.morn.boot.data.jpa.JpaRepository;

/**
 * 角色数据访问
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/19
 */
@Repository
public interface LinkappRoleRepository extends JpaRepository<LinkappRole, Long> {


  /**
   * 按用户查询
   *
   * @param userId 用户编号
   * @return 角色集合
   */
  List<LinkappRole> findByUsersId(Long userId);

}
