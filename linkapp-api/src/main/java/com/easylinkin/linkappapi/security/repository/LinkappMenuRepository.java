package com.easylinkin.linkappapi.security.repository;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.easylinkin.linkappapi.security.entity.LinkappMenu;

import site.morn.boot.data.jpa.JpaRepository;

/**
 * 菜单数据
 *
 * <AUTHOR>
 * @since 0.1.1,  2019/08/19
 */
@Repository
public interface LinkappMenuRepository extends JpaRepository<LinkappMenu, Long> {

  @Modifying
  @Transactional
  @Query("update LinkappMenu set display = false where searchCode like :searchCode")
  void patchHide(@Param("searchCode") String searchCode);


  @Modifying
  @Transactional
  @Query("update LinkappMenu set searchCode = CONCAT(:newSearchCode,SUBSTRING(searchCode,LENGTH(:oldSearchCode) + 1)) where searchCode like CONCAT(:oldSearchCode,'%')")
  void updateSearchCode(@Param("newSearchCode") String newSearchCode,
      @Param("oldSearchCode") String oldSearchCode);

}
