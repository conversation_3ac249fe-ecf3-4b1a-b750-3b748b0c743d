package com.easylinkin.linkappapi.security.entity;

import java.io.Serializable;
import java.util.Set;

import javax.persistence.AttributeOverride;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import io.swagger.annotations.ApiModel;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import site.morn.data.Displayable;
import site.morn.framework.entity.BaseRole;

/**
 * 角色实体
 *
 * <AUTHOR>
 */
@AttributeOverride(name = "id", column = @Column(name = "role_id_"))
@AttributeOverride(name = "name", column = @Column(name = "name_", length = 32))
@AttributeOverride(name = "description", column = @Column(name = "description_"))
@Data
@DynamicInsert
@DynamicUpdate
@Entity
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@TableName("linkapp_role")
@Table(name = "linkapp_role")
@ApiModel("角色实体类")
public class LinkappRole extends BaseRole implements Displayable, Serializable {

  /**
   * 序列化
   */
  private static final long serialVersionUID = 1L;

  /**
   * 组织机构Id
   */
  @Min(Long.MIN_VALUE)
  @Max(Long.MAX_VALUE)
  @Column(name = "department_id_")
  @TableField("department_id_")
  @ApiModelProperty(value = "组织机构ID", allowEmptyValue = true)
  private Long departmentId;

  @Transient
  @TableField(exist = false)
  @ApiModelProperty(value = "组织机构名称", allowEmptyValue = true)
  private String departmentName;

  @ApiModelProperty("显示状态")
  @Column(name = "display_")
  @TableField("display_")
  private Boolean display;

  /**
   * 菜单权限
   */
  @TableField(exist = false)
  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  @ManyToMany
  @JoinTable(name = "linkapp_role_ref_privilege", joinColumns = @JoinColumn(name = "role_id_"), inverseJoinColumns = @JoinColumn(name = "privilege_id_"))
  private Set<LinkappPrivilege> privileges;

  /**
   * 关联用户
   */
  @TableField(exist = false)
  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  @ManyToMany(mappedBy = "roles")
  private Set<LinkappUser> users;
  
  @TableField("tenant_id")
  @Column(name="tenant_id")
  private String tenantId;
  
  @TableField("code_")
  @Column(name="code_")
  @ApiModelProperty(value = "角色编码", allowEmptyValue = true)
  private String code;
  
  @TableField(exist = false)
  @Transient
  private String username;
}
