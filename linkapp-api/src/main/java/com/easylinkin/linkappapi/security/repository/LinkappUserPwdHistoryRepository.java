package com.easylinkin.linkappapi.security.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.easylinkin.linkappapi.security.entity.LinkappUserPwdHistory;
import com.easylinkin.sm.entity.UserPwdHistory;

import site.morn.boot.data.jpa.JpaRepository;

/**
 * 历史密码
 *
 * <p>从0.1.1摘录
 *
 * <AUTHOR>
 * @since 0.0.1-SNAPSHOT, 2019/8/8
 */
@Repository
public interface LinkappUserPwdHistoryRepository extends JpaRepository<LinkappUserPwdHistory, Long> {

  /**
   * 在密码历史表中增加记录
   */
  @Modifying
  @Transactional
  @Query(value = "insert into link_user_history_p (id_,user_id_,password_,modify_time_) values(?1,?2,?3,?4)", nativeQuery = true)
  void addUserPwdHistory(Long id, Long userId, String password, Date modifyTime);


  /**
   * 查询用户密码历史记录
   * <br>
   *
   * <AUTHOR>
   */
  @Query(value = "select u from LinkappUserPwdHistory u where u.userId = :userId")
  List<UserPwdHistory> findPwdHistory(@Param("userId") Long userId,
      Pageable pageable);
}
