package com.easylinkin.linkappapi.security.service.impl;

import com.easylinkin.linkappapi.security.entity.LinkappUser;
import com.easylinkin.linkappapi.security.repository.LinkappUserRepository;
import com.easylinkin.linkappapi.security.service.LinkappLoginService;

import site.morn.boot.data.CrudServiceSupport;

/**
 * 登录服务实现
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/16
 */
public class LinkappLoginServiceImpl extends CrudServiceSupport<LinkappUser, Long, LinkappUserRepository> implements
LinkappLoginService {

  @Override
  public boolean checkLogin(LinkappUser user) {
    return false;
  }

  @Override
  public boolean checkUserStatus() {
    return false;
  }
}
