package com.easylinkin.linkappapi.security.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.lobar.dto.excel.ExcelResultDTO;
import com.easylinkin.linkappapi.security.entity.LinkappRole;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import com.easylinkin.linkappapi.space.entity.LinkappSpace;
import com.easylinkin.sm.dto.ResetPasswordVo;
import org.springframework.web.multipart.MultipartFile;
import site.morn.boot.data.CrudService;
import site.morn.boot.data.DisplayableService;
import site.morn.boot.data.LockableService;
import site.morn.rest.RestMessage;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户服务
 *
 * <AUTHOR>
 * @since 1.0.0, 2017/9/28
 */

public interface LinkappUserService extends CrudService<LinkappUser, Long>, DisplayableService<LinkappUser, Long>,
    LockableService<LinkappUser, Long> {

  /**
   * 判断角色是否绑定用户
   *
   * @param roleId 角色ID
   * @return 角色是否绑定用户
   */
  boolean existsByRole(Long roleId);

  /**
   * 获取当前用户的管理员对象
   * @return 当前用户的管理员对象
   */
  LinkappUser getCurrentTenantAdmin();

  /**
   * 根据租户id获取管理员用户
   *
   * @param tenantId
   * @return
   */
  LinkappUser getCurrentTenantAdmin(String tenantId);

  /**
   * 根据用户名查用户
   *
   * @param username 用户名
   * @return 用户
   */
  LinkappUser findByUsername(String username);

    LinkappUser findAdminUserByProjectId(String projectId);

    List<LinkappUser> findByUsernameBatch(List<String> userNameList);

  void tenantLock(List<String> tenantIds);

  /**
   * 重置密码
   *
   * @param vo 视图对象
   */
  void resetPassword(ResetPasswordVo vo);

  /**
   * 修改密码
   *
   * @param vo 视图对象
   */
  void updatePassword(ResetPasswordVo vo);

  /**
   * 修改个人信息
   *
   * @param user 用户
   * @return 用户
   */
  LinkappUser updateProfile(LinkappUser user);
  
  Boolean updateAccount(String account,String newAccount);
  
  void user2Roles(LinkappUser user,List<LinkappRole> roles);

  /**
   * 忘记密码，重置密码
   *
   * @param username 用户名
   * @param password 新密码
   */
  void forgetPassword(String username, String password);

  /**
   * 生成验证码并发送到手机（缓存），生成验证码前先对表单校验
   *
   * @param username 用户名
   * @param phone 用户手机号
   * @param imageVode 图片验证码
   * @return 成功失败消息
   */
  String sendVerificationCode(String username, String phone, String imageVode);

  /**
   * 表单验证，图片验证码、用户名、手机号、验证码是否正确
   *
   * @param username 用户名
   * @param phone 用户手机号
   * @param imageVode 图片验证码
   * @param code 短信验证码
   * @return 成功失败消息
   */
  boolean verificationMessages(String username, String phone, String imageVode, String code);

  /**
   * 生成图片验证码（缓存）
   *
   * @return 图片验证码
   */
  String getImage();

  /**
   * 查询用户列表
   */
  List<LinkappUser> selectUsersSorted(LinkappUser user);

  /**
   * 分页查询用户列表
   */
  IPage<LinkappUser> selectUsersPage(Page page,LinkappUser user);

  /**
   * 查询用户
   */
  List<LinkappUser> selectUsers(LinkappUser user);
  
  
  Integer auditUser(String date, String dateExp, String tenantId);
  
  List<LinkappSpace> selectCurrentUserSpace(Long id);
  
  IPage<LinkappUser> selectUserByRolePage(Page page, LinkappRole role);

  /**
   * 根据角色查询用户
   *
   * @param role
   * @return
   */
  List<LinkappUser> selectUserByRole(LinkappRole role);

  /***
   * 修改用户大屏图标显示
   * @param user
   */
  void updateUserIsShowScreen(LinkappUser user);

  /**
   * 通过租户id查询用户名称
   * @param tenantId
   * @return
   */
  String findAdminTypeUserNameByTenantId(String tenantId);

  /***
   * 通过租户id查询用户id、名称、租户id
   * @param tenantIds
   * @return
   */
  List<LinkappUser> findAdminTypeUserNameByTenantIds(List<String> tenantIds);

  /**
   * 检查租户下手机号唯一
   *
   * @param user
   */
  void validRepeat(LinkappUser user);

  /**
   * 重置密码，手机号相同用户的密码都统一重置
   *
   * @return 成功失败消息
   */
  RestMessage resetPasswordByVerificationCode(String phone, String password, String verificationCode);

  List<LinkappUser> selectUserByPrivilegeCode(String privilegeCode, List<String> tenantIds);

  void updateRegistrationId(LinkappUser appUser);

  List<LinkappUser> findByPhone(String phone);

  LinkappUser findById(Long id);

  ExcelResultDTO importExcel(MultipartFile file, Integer type) throws Exception;

  Map<String, LinkappUser> mapUserByIds(Collection<String> ids);
}
