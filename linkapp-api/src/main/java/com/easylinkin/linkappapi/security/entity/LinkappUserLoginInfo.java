package com.easylinkin.linkappapi.security.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 用户登录信息实体类
 *
 * <AUTHOR>
 * @since 0.0.1-SNAPSHOT, 2017年7月10日
 * @since 0.1.0-SNAPSHOT,  2018/11/7 11:40
 */

@Data
@Entity
@Table(name = "sm_user_login_info")
@DynamicInsert
@DynamicUpdate
public class LinkappUserLoginInfo implements Serializable {

  /**
   * @serialField
   */
  private static final long serialVersionUID = 1L;

  /**
   * 用户id
   */
  @Id
  @Column(name = "user_id_")
  private Long userId;

  /**
   * 第一次登录失败的时间
   *
   * <p>如果后面连续登录失败不做更新
   */
  @Column(name = "error_time_")
  private Date errorTime;

  /**
   * 累计登录失败次数
   *
   * <p>只针对用户登录密码校验错误，登录成功后清空
   */
  @Column(name = "error_count_")
  private Integer errorCount;

  /**
   * 锁定时间
   */
  @Column(name = "lock_time_")
  private Date lockTime;

  /**
   * 解锁时间
   */
  @Column(name = "unlock_time_")
  private Date unlockTime;
}
