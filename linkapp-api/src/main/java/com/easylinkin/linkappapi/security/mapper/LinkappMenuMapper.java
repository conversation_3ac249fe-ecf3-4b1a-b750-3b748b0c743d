package com.easylinkin.linkappapi.security.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.security.entity.LinkappMenu;

/**
 * 用户Mapper
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/20
 */
@Mapper
public interface LinkappMenuMapper extends BaseMapper<LinkappMenu> {

  List<LinkappMenu> getMenuPage(@Param("queryParams") LinkappMenu queryParams, @Param("module") String module,
      @Param("language") String language, Page page);

  List<LinkappMenu> getMenuAll(@Param("queryParams") LinkappMenu queryParams, @Param("module") String module,
      @Param("language") String language);
}
