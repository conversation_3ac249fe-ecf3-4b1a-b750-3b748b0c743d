package com.easylinkin.linkappapi.security.config.admin;

import com.easylinkin.linkappapi.common.constant.CommonConstant;
import com.easylinkin.linkappapi.operatelog.entity.CommonOperateLog;
import com.easylinkin.linkappapi.operatelog.service.CommonOperateLogService;
import com.easylinkin.linkappapi.security.config.LoginCountCheck;
import com.easylinkin.sm.entity.User;
import com.easylinkin.sm.entity.UserLoginInfo;
import com.easylinkin.sm.service.LogService;
import com.easylinkin.sm.service.UserLoginInfoService;
import com.easylinkin.sm.service.UserService;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import site.morn.boot.web.Responses;
import site.morn.framework.context.AccountContext;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 认证成功处理者
 *
 * <AUTHOR>
 * @since 1.2.0, 2019/8/29
 */
@Component
public class RestAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

  @Autowired
  private UserLoginInfoService userLoginInfoService;
  @Autowired
  private UserService userService;
  @Autowired
  private LogService logService;
  @Resource
  private LoginCountCheck loginCountCheck;
  @Resource
  private CommonOperateLogService commonOperateLogService;
  

  @Override
  public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
      Authentication authentication) {

    String username = request.getParameter("username");
    if (username == null && request.getAttribute("username") != null) {
      username = request.getAttribute("username").toString();
    }
    User user = userService.findByUsername(username);
    //Department department = departmentService.get(user.getDepartmentId());

//    Log log = new Log();
//    log.setModuleId(Long.parseLong(Module.LOGIN));
//    log.setIp(request.getRemoteAddr());
//    log.setContent("用户登录");
//    log.setStatus(true);
//    log.setDisplay(true);
//    //log.setCustomerId(department.getCustomerId());
//    logService.add(log);

    CommonOperateLog commonOperateLog = new CommonOperateLog();
    commonOperateLog.setTenantId("operation_management_user");
    commonOperateLog.setModuleName("登录");
    commonOperateLog.setContent("管理端登录成功");
    commonOperateLog.setUserAccount(username);
    commonOperateLog.setCreateTime(new Date()).setResult(true);
    commonOperateLogService.addLog(commonOperateLog);

    UserLoginInfo userLoginInfo = userLoginInfoService.get(user.getId());
    if (Objects.nonNull(userLoginInfo)) {
      userLoginInfoService.clear(user.getId());
    }

    // 清空登录缓存
    AccountContext.cacheGroup().clearKey(authentication.getName());
    HttpSession session = request.getSession(false);
    if(session != null){
      session.setMaxInactiveInterval(CommonConstant.SESSION_TIME_DEFAULT);;
    }
    RestMessage message = RestBuilders.successMessage("login.success");
//    错误次数记录 达到规定次数 锁定规定时间
    loginCountCheck.afterLoginHandler(true, user.getUsername());
    
    Responses.standard(response).respond(message);
  }
}
