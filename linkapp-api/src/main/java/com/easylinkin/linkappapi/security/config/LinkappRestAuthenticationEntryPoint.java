package com.easylinkin.linkappapi.security.config;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import site.morn.boot.web.Responses;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * REST认证异常处理
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/29
 */
public class LinkappRestAuthenticationEntryPoint implements AuthenticationEntryPoint {

  @Override
  public void commence(HttpServletRequest request, HttpServletResponse response,
      AuthenticationException e) {
    RestMessage restMessage = RestBuilders.failureBuilder().message(e.getMessage()).build();
    Responses.standard(response).respond(restMessage);
  }
}
