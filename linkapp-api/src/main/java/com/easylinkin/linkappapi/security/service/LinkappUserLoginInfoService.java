package com.easylinkin.linkappapi.security.service;

import com.easylinkin.linkappapi.security.entity.LinkappUserLoginInfo;

import site.morn.boot.data.CrudService;

/**
 * 用户登录信息服务
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/16
 */
public interface LinkappUserLoginInfoService extends CrudService<LinkappUserLoginInfo, Long> {

  /**
   * 累计失败次数
   *
   * @param userId 用户ID
   * @return 用户是否锁定
   */
  boolean addErrorCount(Long userId);

  /**
   * 清空失败次数
   *
   * @param userId 用户ID
   */
  void clearErrorCount(Long userId);

  /**
   * 清空失败记录
   *
   * <p>清空失败次数、锁定时间、解锁时间
   *
   * @param userId 用户ID
   */
  void clear(Long userId);
}
