package com.easylinkin.linkappapi.security.context;

import static site.morn.framework.context.CommonConstant.Caches.USER;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.easylinkin.linkappapi.roster.entity.RailLinkappRosterPersonnel;
import com.easylinkin.linkappapi.roster.mapper.RailLinkappRosterPersonnelMapper;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import com.easylinkin.linkappapi.security.service.LinkappUserService;
import com.easylinkin.linkappapi.space.entity.LinkappSpace;
import com.easylinkin.sm.entity.User;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import site.morn.bean.annotation.Objective;
import site.morn.cache.CacheGroup;
import site.morn.framework.context.AccountContext;
import site.morn.framework.context.UserContextProducer;

/**
 * 用户适配器
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/4/16
 */
@Component
@Objective
@Slf4j
public class LinkappUserContextProducer implements UserContextProducer<LinkappUser> {

  @Resource
  private LinkappUserService userService;

  @Lazy
  @Resource
  private RailLinkappRosterPersonnelMapper rosterPersonnelMapper;

  @Override
  public LinkappUser getCurrent() {
    String username = getCurrentUsername();
    if (username == null) {
      return null;
    }
    CacheGroup cacheGroup = AccountContext.cacheGroup();
    Object obj = cacheGroup.get(USER, username, () -> userService.findByUsername(username));
// 解决后端用户登录 操作记录操作日志报错，User cast to LinkappUser
    if (obj instanceof LinkappUser) {
      return (LinkappUser) obj;
    } else if (obj instanceof User) {
      User user = (User) obj;
      LinkappUser linkappUser = new LinkappUser();
      linkappUser.setTenantId("operation_management_user");
      linkappUser.setUsername(user.getUsername());
      linkappUser.setId(user.getId());
      linkappUser.setPhone(user.getPhone());
      return linkappUser;
    } else { 
//      log.info("getCurrent()-当前未登陆");
      return null;
    }

  }

  public LinkappUser getNotNullCurrent() {
    LinkappUser user = getCurrent();
    Assert.notNull(user, "获取当前用户为空");
    return user;
  }

  @Override
  public String getCurrentUsername() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    return Optional.ofNullable(authentication).map(Authentication::getName).orElse(null);
  }
  
  // 获取登录用户的空间
  public List<LinkappSpace> getCurrentUserSpace(Long id) {
	  CacheGroup cacheGroup = AccountContext.cacheGroup();
	  return cacheGroup.get("space", id.toString(), () -> getSpaces(id));
  }
  
  private List<LinkappSpace> getSpaces(Long id){
	  return userService.selectCurrentUserSpace(id);
  }

  /**
   * 获取tenantId
   * @return
   */
  public String getTenantId() {
    LinkappUser user = getCurrent();
    if (null != user){
      return user.getTenantId();
    }
    return null;
  }

  /**
   * 获取当前用户对应的花名册人员
   * 通过当前用户的nickName去花名册中匹配人员，然后对比ID
   *
   * @return 花名册人员信息，如果未找到返回null
   * <AUTHOR>
   */
  public RailLinkappRosterPersonnel getCurrentRosterPersonnel() {
    LinkappUser currentUser = getCurrent();
    if (currentUser == null || !StringUtils.hasText(currentUser.getNickname())) {
      return null;
    }

    String tenantId = currentUser.getTenantId();
    String nickName = currentUser.getNickname();

    // 通过nickName在花名册中查找匹配的人员
    LambdaQueryWrapper<RailLinkappRosterPersonnel> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(RailLinkappRosterPersonnel::getTenantId, tenantId)
           .eq(RailLinkappRosterPersonnel::getRealName, nickName); // 在岗状态

    List<RailLinkappRosterPersonnel> rosterList = rosterPersonnelMapper.selectList(wrapper);

    if (rosterList != null && !rosterList.isEmpty()) {
      // 如果找到多个，返回第一个（通常应该只有一个）
      return rosterList.get(0);
    }

    return null;
  }

  /**
   * 获取当前用户对应的花名册人员ID
   *
   * @return 花名册人员ID，如果未找到返回null
   * <AUTHOR>
   */
  public Long getCurrentRosterPersonnelId() {
    RailLinkappRosterPersonnel rosterPersonnel = getCurrentRosterPersonnel();
    return rosterPersonnel != null ? rosterPersonnel.getId() : null;
  }

  /**
   * 获取指定用户
   *
   * <p>清空角色信息，避免缓存冗余数据
   *
   * @param username 用户名
   * @return 用户
   */
//  private LinkappUser getUser(String username) {
//    LinkappUser user = userService.findByUsername(username);
//    if (Objects.isNull(user)) {
//      return null;
//    }
//    LinkappUser copy = new LinkappUser();
//    BeanUtils.copyProperties(user, copy);
//    copy.setRoles(Collections.emptyList());
//    return copy;
//  }

}
