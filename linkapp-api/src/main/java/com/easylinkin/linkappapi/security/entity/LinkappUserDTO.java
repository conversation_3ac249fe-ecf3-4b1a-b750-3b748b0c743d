package com.easylinkin.linkappapi.security.entity;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/28
 * @description
 */
@Data
public class LinkappUserDTO extends LinkappUser{
  private String roleNames;
  private String companyName;
  public static List<LinkappUserDTO> creatExcel() {
    List<LinkappUserDTO> linkappUserDTOS = new ArrayList<>();
    LinkappUserDTO linkappUserDTO = new LinkappUserDTO();
    linkappUserDTO.setNickname("张三");
    linkappUserDTO.setPhone("18878784545");
    linkappUserDTO.setRoleNames("XXXX,XX");
    linkappUserDTO.setCompanyName("武汉才华有限公司");
    linkappUserDTO.setSex("男");
    linkappUserDTOS.add(linkappUserDTO);
    return linkappUserDTOS;
  }

}
