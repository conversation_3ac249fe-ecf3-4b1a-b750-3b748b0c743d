package com.easylinkin.linkappapi.security.service;

import java.util.Collection;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.security.entity.LinkappRole;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import com.easylinkin.linkappapi.space.entity.LinkappSpace;

import site.morn.boot.data.CrudService;
import site.morn.boot.data.DisplayableService;

/**
 * 角色服务
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/15
 */
public interface LinkappRoleService extends CrudService<LinkappRole, Long>, DisplayableService<LinkappRole, Long> {

  /**
   * 查询用户所属角色
   *
   * @param userId 用户ID
   * @return 用户所属角色
   */
  Collection<LinkappRole> getByUserId(Long userId);
  
  @Override
  void delete(Long roleId);
  
  /**
   * 当前租户下的角色列表
   *
   * @return 角色集合
   */
  List<LinkappRole> findRoles(LinkappRole role);
  
  
  IPage<LinkappRole> selectPage(Page page, LinkappRole role);
  
  void role2Spaces(LinkappRole role,List<LinkappSpace> spaces);
  
  List<LinkappSpace> selectSpaceByRole(Long id);
  
  void role2Users(LinkappRole role,List<LinkappUser> users);
  
  List<LinkappUser> selectUserByRole(Long roleId);

  List<LinkappRole> checkRole(LinkappRole role);

  LinkappRole findByRoleId(Long roldId);

  /**
   * 查询用户关联的角色
   * @param user
   * @return
   */
  List<LinkappRole> selectRolesByUser(LinkappUser user);
}
