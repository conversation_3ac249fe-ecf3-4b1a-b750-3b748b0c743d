package com.easylinkin.linkappapi.security.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.easylinkin.linkappapi.common.constant.CommonConstant;
import com.easylinkin.linkappapi.security.entity.LinkappTenantSessionConfig;
import com.easylinkin.linkappapi.security.mapper.LinkappTenantSessionConfigMapper;
import com.easylinkin.linkappapi.security.service.TenantSessionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/8 14:46
 */
@Service
@Slf4j
public class TenantSessionServiceImpl implements TenantSessionService {

    @Resource
    private LinkappTenantSessionConfigMapper linkappTenantSessionConfigMapper;

    @Override
    public Integer getTimeout(String tenantId) {
        if(StrUtil.isEmpty(tenantId)){
            return CommonConstant.SESSION_TIME_DEFAULT;
        }

        QueryWrapper qw = new QueryWrapper();
        qw.eq("tenant_id", tenantId);
        qw.eq("delete_status", 0);
        List<LinkappTenantSessionConfig> list = linkappTenantSessionConfigMapper.selectList(qw);
        if(ObjectUtils.isNotEmpty(list)){
            LinkappTenantSessionConfig linkappTenantSessionConfig = list.get(0);
            if(ObjectUtils.isEmpty(linkappTenantSessionConfig.getSessionTimeout())){
                linkappTenantSessionConfig.setSessionTimeout(CommonConstant.SESSION_TIME_DEFAULT);
            }
            return linkappTenantSessionConfig.getSessionTimeout();
        }else {
            log.info("无租户会话时长配置数据");
            return CommonConstant.SESSION_TIME_DEFAULT;
        }
    }
}
