package com.easylinkin.linkappapi.security.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/8 14:48
 */
@Data
@Accessors(chain = true)
@TableName(value = "linkapp_tenant_session_config")
public class LinkappTenantSessionConfig {

    @TableId(type= IdType.AUTO)
    private Integer id;

    @TableField(value = "tenant_id")
    private String tenantId;

    @TableField(value = "session_timeout")
    private Integer sessionTimeout;

    @TableField(value = "description")
    private String description;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "delete_status")
    private Integer deleteStatus;
}
