package com.easylinkin.linkappapi.security.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 权限个性化实体
 *
 */
@Data
@TableName("linkapp_privilege_custom")
@ApiModel("权限个性化实体")
public class LinkappPrivilegeCustom implements Serializable {
  private static final long serialVersionUID=1L;

  /**
   * 主键
   */
  @TableId(value = "id_", type = IdType.AUTO)
  protected Long id;

  /**
   * 权限id
   */
  @ApiModelProperty(value = "父节点", example = "1")
  @TableField("privilege_id_")
  protected Long privilegeId;

  /**
   * 权限名
   */
  @ApiModelProperty(value = "权限名", example = "")
  @TableField("name_")
  protected String name;

  @ApiModelProperty(value = "租户ID", example = "")
  @TableField("tenant_id_")
  protected String tenantId;

  /**
   * 排序的序号
   */
  @TableField("seq_")
  protected Double sort;

}
