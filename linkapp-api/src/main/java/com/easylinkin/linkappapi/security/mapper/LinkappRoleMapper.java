package com.easylinkin.linkappapi.security.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.security.entity.LinkappRole;
import com.easylinkin.linkappapi.security.entity.LinkappRoleRefSpace;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import com.easylinkin.linkappapi.security.entity.LinkappUserRefRole;
import com.easylinkin.linkappapi.space.entity.LinkappSpace;
/**
 * 角色Mapper
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/23
 */
public interface LinkappRoleMapper extends BaseMapper<LinkappRole> {

  
  /**
   * 按用户查询
   *
   * @param userId 用户编号
   * @return 角色集合
   */
  @Select("select a.role_id_ as id,a.* from linkapp_role a JOIN linkapp_user_ref_role b where b.role_id = a.role_id_ and b.user_id = #{userId}")
  List<LinkappRole> findByUsersId(Long userId);
  
  
  /**
   * 当前租户下的角色列表
   *
   * @return 角色集合
   */
  List<LinkappRole> findRoles(@Param("role") LinkappRole role);
  
  /**
   * 当前租户下的角色列表分页
   *
   * @return 角色集合
   */
  List<LinkappRole> findRoles(Page page,@Param("role") LinkappRole role);
  
  List<LinkappSpace> selectSpaceByRole(@Param("id") Long id);
	
  void deleteRole2Spaces(Long userId);
	
  void insertRole2Spaces(LinkappRoleRefSpace roleRefSpace);
  
  List<LinkappUser> selectUserByRole(@Param("id") Long id);
	
  void deleteRole2Users(@Param("roleId")Long roleId);
	
  void insertRole2Users(LinkappUserRefRole userRefRole);

  LinkappRole findByRoleId(Long roldId);
}
