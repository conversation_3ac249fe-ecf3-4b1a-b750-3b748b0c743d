package com.easylinkin.linkappapi.security.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;
import javax.persistence.AttributeOverride;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import site.morn.boot.data.entity.ReviserEntity;
import site.morn.tree.TreeNode;

/**
 * 菜单
 *
 * <AUTHOR>
 * @since 0.1.1,  2019/08/16
 */
@Data
@Entity
@Table(name = "linkapp_menu")
@TableName("linkapp_menu")
@FieldNameConstants
@DynamicInsert
@DynamicUpdate
@AttributeOverride(name = "modifier", column = @Column(name = "modifier_", length = 32))
@AttributeOverride(name = "modifyTime", column = @Column(name = "modify_time_"))
@AttributeOverride(name = "creator", column = @Column(name = "creator_", length = 32))
@AttributeOverride(name = "createTime", column = @Column(name = "create_time_"))
public class LinkappMenu extends ReviserEntity implements Serializable, TreeNode {

  /**
   * 序列化
   */
  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  @Min(Long.MIN_VALUE)
  @Max(Long.MAX_VALUE)
  @Id
  @Column(name = "id_")
  @GeneratedValue
  private Long id;

  /**
   * 菜单名称
   */
  @Size(max = 32)
  @Column(name = "name_", length = 32)
  private String name;

  /**
   * 菜单路径
   */
  @Size(max = 32)
  @Column(name = "path_", length = 32)
  private String path;

  /**
   * 菜单图标
   */
  @Size(max = 128)
  @Column(name = "icon_", length = 128)
  private String icon;

  /**
   * 权限码
   */
//  @Size(max = 32)
//  @Column(name = "privilege_code_", length = 32)
  @Min(Long.MIN_VALUE)
  @Max(Long.MAX_VALUE)
  @Column(name = "privilege_code_")
  private Long privilegeCode;

  /**
   * 权限名称
   */
  @Transient
  private String privilegeName;

  /**
   * 权限码集合
   */
  @Transient
  private List<Long> privilegeCodes;

  /**
   * 层级编码
   */
  @Size(max = 128)
  @Column(name = "search_code_")
  private String searchCode;

  /**
   * 菜单层级
   */
  @Column(name = "level_")
  private Integer level;

  /**
   * 父级id
   */
  @Min(Long.MIN_VALUE)
  @Max(Long.MAX_VALUE)
  @Column(name = "parent_id_")
  private Long parentId;

  @Transient
  private String parentName;

  /**
   * 所属系统
   */
  @Min(Long.MIN_VALUE)
  @Max(Long.MAX_VALUE)
  @Column(name = "system_id_")
  private Long systemId;

  @Transient
  private String systemName;

  /**
   * 排序
   */
  @Min(Integer.MIN_VALUE)
  @Max(Integer.MAX_VALUE)
  @Column(name = "order_no_")
  private Integer orderNo;

  /**
   * 显示状态
   */
  @Column(name = "state_")
  private Boolean state;

  /**
   * 软删除字段
   */
  @Column(name = "display_")
  private Boolean display;

  /**
   * 备注
   */
  @Size(max = 128)
  @Column(name = "remark_", length = 128)
  private String remark;

}
