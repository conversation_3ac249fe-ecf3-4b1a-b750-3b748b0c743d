package com.easylinkin.linkappapi.security.service;

import java.util.List;

import com.easylinkin.linkappapi.security.entity.LinkappMenu;

import site.morn.boot.data.CrudService;

/**
 * 菜单Service
 *
 * <AUTHOR>
 * @since 0.1.1,  2019/08/19
 */
public interface LinkappMenuService extends CrudService<LinkappMenu, Long> {

  void patchHide(List<String> searchCodes);

  /**
   * 根据系统权限查询菜单
   *
   * @param privilegeIds 系统权限ID
   * @return 菜单集合
   */
  List<LinkappMenu> findByPrivilegeIds(List<Long> privilegeIds);
}
