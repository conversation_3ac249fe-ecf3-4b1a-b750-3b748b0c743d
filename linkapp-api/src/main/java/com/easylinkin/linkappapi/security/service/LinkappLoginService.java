package com.easylinkin.linkappapi.security.service;

import com.easylinkin.linkappapi.security.entity.LinkappUser;

import site.morn.boot.data.CrudService;

/**
 * 登录服务
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/16
 */
public interface LinkappLoginService extends CrudService<LinkappUser, Long> {

  /**
   * 校验用户名和密码
   *
   * @param user 用户
   * @return 校验是否成功
   */
  boolean checkLogin(LinkappUser user);

  /**
   * 校验用户状态
   *
   * @return 用户能否登录
   */
  boolean checkUserStatus();
}
