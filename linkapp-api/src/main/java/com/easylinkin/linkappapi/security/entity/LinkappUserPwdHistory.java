package com.easylinkin.linkappapi.security.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * <AUTHOR>
 * @DATE 2018/11/7 11:41
 * @since 0.0.1-SNAPSHOT, 2017年7月10日
 * @since 0.1.0-SNAPSHOT,  2018/11/7
 */

@Data
@Entity
@Table(name = "sm_user_history_p")
@DynamicInsert
@DynamicUpdate
public class LinkappUserPwdHistory implements Serializable {

  @Min(Long.MIN_VALUE)
  @Max(Long.MAX_VALUE)
  @Id
  @Column(name = "id_")
  @GeneratedValue
  private Long id;

  /**
   * 用户id
   */
  @Min(Long.MIN_VALUE)
  @Max(Long.MAX_VALUE)
  @Column(name = "user_id_")
  private Long userId;

  /**
   * 密码
   */
  @Size(max = 256)
  @Column(name = "password_", length = 256)
  private String password;

  /**
   * 修改时间
   */
  @Column(name = "modify_time_")
  private Date modifyTime;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }

  public Date getModifyTime() {
    return modifyTime;
  }

  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

}
