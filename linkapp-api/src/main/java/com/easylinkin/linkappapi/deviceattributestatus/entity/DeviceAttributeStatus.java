package com.easylinkin.linkappapi.deviceattributestatus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description = "设备属性状态实体类")
@TableName("linkapp_device_attribute_status")
public class DeviceAttributeStatus extends Model<DeviceAttributeStatus> {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    @ApiModelProperty(value = "设备属性状态主键ID", name = "id", required = false, example = "005db25c939066e425d628cf4f185a7f")
    private String id;

    /**
     * 设备编码
     */
    @TableField("device_code")
    @ApiModelProperty(value = "设备编码", name = "deviceCode", required = false, example = "VIR1634267778684")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", name = "deviceName", required = false, example = "")
    @TableField("device_name")
    private String deviceName;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称", name = "propName", required = false, example = "")
    @TableField("prop_name")
    private String propName;

    /**
     * 属性编码
     */
    @ApiModelProperty(value = "属性编码", name = "propCode", required = false, example = "")
    @TableField("prop_code")
    private String propCode;

    /**
     * 时间戳
     */
    @TableField("timestamp")
    private Long timestamp;

    /**
     * 唯一标签
     */
    @TableField("unique_tag")
    private String uniqueTag;

    /**
     * 设置唯一标识
     */
    public void setUniqueTag() {
        StringBuilder sb = new StringBuilder();
        sb.append(getDeviceCode()).append(getPropCode()).append(getVersion());
        if (getParentPropCode() != null) {
            sb.append(getParentPropCode());
        }
        if (getArrayIndex() != null) {
            sb.append(getArrayIndex());
        }
        this.uniqueTag = sb.toString();
    }

    /**
     * 属性单位
     */
    @ApiModelProperty(value = "属性单位", name = "propUnit", required = false, example = "")
    @TableField("prop_unit")
    private String propUnit;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位", name = "unit", required = false, example = "")
    @TableField(exist = false)
    private String unit;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", name = "version", required = false, example = "")
    @TableField("version")
    private String version;

    @ApiModelProperty(value = "父ID", name = "parentId", required = false, example = "")
    @TableField("parent_id")
    private String parentId;

    /**
     * 数组序号
     */
    @ApiModelProperty(value = "数组序号", name = "arrayIndex", required = false, example = "0")
    @TableField("array_index")
    private Integer arrayIndex;

    /**
     * 父属性的编码
     */
    @ApiModelProperty(value = "父属性的编码", name = "parentPropCode", required = false, example = "")
    @TableField("parent_prop_code")
    private String parentPropCode;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值", name = "propValue", required = false, example = "")
    @TableField("prop_value")
    private String propValue;

    @TableField("create_time")
    private Date createTime;

    @TableField("creator")
    private String creator;

    @TableField("modifier")
    private String modifier;

    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 时间查询起始
     */
    @ApiModelProperty(value = "时间查询起始", name = "queryTimeStart", required = false, example = "")
    @TableField(exist = false)
    private String queryTimeStart;
    /**
     * 时间查询结束
     */
    @ApiModelProperty(value = "时间查询结束", name = "queryTimeEnd", required = false, example = "")
    @TableField(exist = false)
    private String queryTimeEnd;

    /**
     * 设备型号id
     */
    @ApiModelProperty(value = "设备型号ID", name = "deviceUnitId", required = false, example = "")
    @TableField(exist = false)
    private String deviceUnitId;

    @ApiModelProperty(value = "设备型号编码", name = "deviceUnitCode", required = false, example = "")
    @TableField(exist = false)
    private String deviceUnitCode;

    /**
     * 设备属性排序
     */
    @ApiModelProperty(value = "设备属性排序", name = "sortNo", required = false, example = "0")
    @TableField(exist = false)
    private Integer sortNo;

    /**
     * 设备属性是否展示
     */
    @ApiModelProperty("设备属性是否展示")
    @TableField(exist = false)
    private Boolean isShow;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @TableField(exist = false)
    @ApiModelProperty(value = "规范", name = "specs", required = false, example = "{\"0\":\"断电\",\"1\":\"通电\"}")
    private String specs;

    @TableField(exist = false)
    @ApiModelProperty(value = "可视化配置", name = "visualizationConfig", required = false, example = "")
    private String visualizationConfig;

    @ApiModelProperty(value = "租户ID", name = "tenantId", required = false, example = "")
    @TableField(exist = false)
    private String tenantId;

    /**
     * 时间查询条件
     */
    @ApiModelProperty(value = "时间查询条件", name = "queryTimeList", required = false)
    @TableField(exist = false)
    private List<Date> queryTimeList;

    @ApiModelProperty(value = "属性显示图标", name = "icoPath", required = false, example = "")
    @TableField(exist = false)
    private String icoPath;

    /**
     * 子属性列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "子属性列表", name = "childAttributeList", required = false)
    private List<DeviceAttributeStatus> childAttributeList;

    @TableField(exist = false)
    @ApiModelProperty(value = "设备物模型定义字段", name = "necessaryFieldList", required = false, example = "{\"switch_state\", \"ab_phase_voltage\"}")
    private List<String> necessaryFieldList;

    /**
     * 当查不出数据时，是否生成数据
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否生成数据", name = "genarateData", required = false, example = "")
    private Boolean genarateData;

    @TableField(exist = false)
    private List<String> checkedAttrName;

    @ApiModelProperty(value = "设备类型编码", name = "typeCode", required = false, example = "")
    @TableField(exist = false)
    private String typeCode;

    @ApiModelProperty(value = "无数据也展示", name = "noDataShow", required = false, example = "")
    @TableField(exist = false)
    private Boolean noDataShow;

    @TableField(exist = false)
    private String projectCode;

}
