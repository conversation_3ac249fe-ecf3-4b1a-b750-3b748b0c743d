package com.easylinkin.linkappapi.deviceattributestatus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
public interface DeviceAttributeStatusMapper extends BaseMapper<DeviceAttributeStatus> {

    List<DeviceAttributeStatus> getPageGlobal(@Param("deviceAttributeStatus") DeviceAttributeStatus deviceAttributeStatus);

    IPage<DeviceAttributeStatus> getPageGlobal(@Param("page") Page page, @Param("deviceAttributeStatus") DeviceAttributeStatus deviceAttributeStatus);

    /**
     * 查询设备实时数据
     * @param deviceAttributeStatus
     * @return
     */
    List<DeviceAttributeStatus> getDeviceRealtimeDataGlobal(DeviceAttributeStatus deviceAttributeStatus);

  List<DeviceAttributeStatus> getDeviceRealtimeDataByDevice(Device device);

    /**
     * 全局查询，不用拦截里加的租户信息
     * @param device
     * @return
     */
    List<DeviceAttributeStatus> getDeviceRealtimeDataByDeviceGlobal(Device device);

    String queryDeviceVersion(@Param("deviceCode") String deviceCode);
}
