package com.easylinkin.linkappapi.deviceattributestatus.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-18
 */
public interface DeviceAttributeStatusService extends IService<DeviceAttributeStatus> {

    IPage<DeviceAttributeStatus> getPageGlobal(Page page, DeviceAttributeStatus deviceAttributeStatus);

    List<DeviceAttributeStatus> getPageGlobal(DeviceAttributeStatus deviceAttributeStatus);

    List<DeviceAttributeStatus> getProps(DeviceAttributeStatus deviceAttributeStatus);

    void exportData(DeviceAttributeStatus deviceAttributeStatus, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询实时 设备属性状态
     */
    List<DeviceAttributeStatus> getDeviceRealtimeData(DeviceAttributeStatus deviceAttributeStatus);


    List<DeviceAttributeStatus> getDeviceRealtimeDataForBigScreen(Device device);
    /**
     * 查询实时 设备属性状态(ws大屏)
     */
    List<DeviceAttributeStatus> getDeviceRealtimeDataBigScreen(DeviceAttributeStatus deviceAttributeStatus);

    /**
     * 格式化 设备属性列表，按照约定结构返回
     *
     * @param deviceAttributeStatusList 输入的属性列表
     * @return 格式化后的属性列表
     */
    List<DeviceAttributeStatus> getFormatDeviceAttributeStatusList(List<DeviceAttributeStatus> deviceAttributeStatusList);

    List<DeviceAttributeStatus> getDeviceRealtimeDataByParentPropCode(DeviceAttributeStatus deviceAttributeStatus);

    /**
     * 查询历史数据
     */
    IPage<Map> getHistoricDataFromEs(RequestModel<DeviceAttributeStatus> requestModel);


    void exportHistoryExcel(DeviceAttributeStatus deviceAttributeStatus, HttpServletRequest request, HttpServletResponse response);
    /**
     * 获取最新的设备属性状态 查询条件 设备编号
     * @param code
     * @return
     */
    List<DeviceAttributeStatus> getLatestDeviceAttributeStatusList(String code,List<String> necessaryFieldList);

    /***
     * 查询设备实时数据
     * @param deviceAttributeStatus
     * @return
     */
    List<DeviceAttributeStatus> getDeviceRealtimeDataNoVersion(DeviceAttributeStatus deviceAttributeStatus);

    /**
     * 插入或修改
     * @param deviceAttributeStatus vo
     * @return 操作结果
     */
    boolean saveOrUpdate2(DeviceAttributeStatus deviceAttributeStatus);

    boolean update2(DeviceAttributeStatus deviceAttributeStatus);

    /**
     * 通过某指标，查询设备最新属性值
     * @param deviceAttributeStatus
     * @param deviceTypeName 设备类型名称
     * @return
     */
    List<DeviceAttributeStatus> getDeviceRealtimeDataByCertainTag(DeviceAttributeStatus deviceAttributeStatus, String deviceTypeName);

    /**
     * 通过项目编码获取设备状态数据
     * @param map
     * @return
     */
    List<DeviceAttributeStatus> getDeviceDataByProject(Map<String, String> map);

    /**
     * 根据条件查找最近的一包流水时间
     * @param requestModel
     * @return
     */
    Date getLatestRecordTime(RequestModel<DeviceAttributeStatus> requestModel);
}
