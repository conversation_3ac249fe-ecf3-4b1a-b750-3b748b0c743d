package com.easylinkin.linkappapi.appversion.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.appversion.entity.AppVersionInfo;
import com.easylinkin.linkappapi.appversion.mapper.AppVersionInfoMapper;
import com.easylinkin.linkappapi.appversion.service.AppVersionInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <p>
 * App版本信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
@Service
public class AppVersionInfoServiceImpl extends
    ServiceImpl<AppVersionInfoMapper, AppVersionInfo> implements AppVersionInfoService {

  @Override
  public AppVersionInfo queryAppLatestVersion(AppVersionInfo appVersionInfo) {
    Assert.isTrue(StringUtils.isNotBlank(appVersionInfo.getOsName()), "系统名称不能为空");
    QueryWrapper<AppVersionInfo> wrapper = new QueryWrapper<>();
    wrapper.eq(StringUtils.isNotBlank(appVersionInfo.getOsName()), "os_name",
        appVersionInfo.getOsName());
    wrapper.orderByDesc("create_time");
    wrapper.last("limit 1");
    AppVersionInfo latestVersion = this.baseMapper.selectOne(wrapper);
    return latestVersion;
  }

  @Override
  public AppVersionInfo addNewAppVersion(AppVersionInfo appVersionInfo) {
    Assert.isTrue(StringUtils.isNotBlank(appVersionInfo.getOsName()), "系统名称不能为空");
    Assert.isTrue(StringUtils.isNotBlank(appVersionInfo.getAppUrl()), "App下载地址不能为空");
    Assert.isTrue(StringUtils.isNotBlank(appVersionInfo.getAppVersion()), "App版本不能为空");
    Assert.isTrue(StringUtils.isNotBlank(appVersionInfo.getApplicationVersion()), "App应用版本不能为空");
    String appVersion = appVersionInfo.getAppVersion();
    Assert.isTrue(appVersion.split("[.]").length == 4, "App版本号不合法");

    //参数补充
    appVersionInfo.setDeleted(1);
    appVersionInfo.setCreateTime(DateUtil.date());
    this.baseMapper.insert(appVersionInfo);

    return appVersionInfo;
  }

  @Override
  public boolean updateAppVersion(AppVersionInfo appVersionInfo) {
    Assert.isTrue(appVersionInfo.getId() != null, "ID为空");
    Assert.isTrue(StringUtils.isNotBlank(appVersionInfo.getOsName()), "系统名称不能为空");
    Assert.isTrue(StringUtils.isNotBlank(appVersionInfo.getAppUrl()), "App下载地址不能为空");
    Assert.isTrue(StringUtils.isNotBlank(appVersionInfo.getAppVersion()), "App版本不能为空");
    Assert.isTrue(StringUtils.isNotBlank(appVersionInfo.getApplicationVersion()), "App应用版本不能为空");
    String appVersion = appVersionInfo.getAppVersion();
    Assert.isTrue(appVersion.split("[.]").length == 4, "App版本号不合法");

    //更新时间
    appVersionInfo.setUpdateTime(DateUtil.date());
    int upNum = this.baseMapper.updateById(appVersionInfo);
    if (upNum != 1) {
      return false;
    }
    return true;
  }

  @Override
  public boolean delAppVersion(Integer id) {
    Assert.isTrue(id != null, "ID为空");
    AppVersionInfo appVersionInfo = this.baseMapper.selectById(id);
    Assert.isTrue(appVersionInfo != null, "版本不存在");
    int upNum = this.baseMapper.deleteById(id);
    if (upNum != 1) {
      return false;
    }
    return true;
  }

  @Override
  public IPage<AppVersionInfo> getPage(Page page, AppVersionInfo appVersionInfo) {
    IPage<AppVersionInfo> list = this.baseMapper.getAppVerssionInfo(page, appVersionInfo);
    return list;
  }


}
