package com.easylinkin.linkappapi.appversion.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.appversion.entity.AppVersionInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * App版本信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
public interface AppVersionInfoMapper extends BaseMapper<AppVersionInfo> {


  /**
   * 获取版本列表
   *
   * @param page           页面
   * @param appVersionInfo 条件对象
   * @return 版本list
   */
  IPage<AppVersionInfo> getAppVerssionInfo(Page page,
      @Param("appVersionInfo") AppVersionInfo appVersionInfo);
}
