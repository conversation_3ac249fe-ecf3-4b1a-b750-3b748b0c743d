package com.easylinkin.linkappapi.appversion.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.appversion.entity.AppVersionInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * App版本信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
public interface AppVersionInfoService extends IService<AppVersionInfo> {

  /**
   * 获取最新app版本信息
   * @param appVersionInfo 条件对象
   * @return app信息对象
   */
  AppVersionInfo queryAppLatestVersion(AppVersionInfo appVersionInfo);

  /**
   * 新增新版本
   * @param appVersionInfo 信息
   * @return app信息对象
   */
  AppVersionInfo addNewAppVersion(AppVersionInfo appVersionInfo);
  /**
   * 修改版本
   * @param appVersionInfo 信息
   * @return app信息对象
   */
  boolean updateAppVersion(AppVersionInfo appVersionInfo);

  /**
   * 删除版本
   * @param id 主键
   * @return app信息对象
   */
  boolean delAppVersion(Integer id);

  /**
   * 版本列表
   * @param page page对象
   * @param appVersionInfo 查询条件对象
   * @return 分页数据
   */
  IPage<AppVersionInfo> getPage(Page page, AppVersionInfo appVersionInfo);
}
