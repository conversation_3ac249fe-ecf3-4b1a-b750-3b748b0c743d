package com.easylinkin.linkappapi.appversion.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.appversion.entity.AppVersionInfo;
import com.easylinkin.linkappapi.appversion.service.AppVersionInfoService;
import com.easylinkin.linkappapi.asset.entity.Asset;
import com.easylinkin.linkappapi.common.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * App版本信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
@Api(value = "app版本信息", tags = {"app版本管理"})
@RestController
@RequestMapping("/appversion/appVersionInfo")
public class AppVersionInfoController {

  @Autowired
  private AppVersionInfoService appVersionInfoService;

  @ApiOperation("获取最新版本")
  @PostMapping("/queryAppLatestVersion")
  public RestMessage queryAppLatestVersion(@RequestBody AppVersionInfo appVersionInfo) {
    return RestBuilders.successBuilder()
        .data(appVersionInfoService.queryAppLatestVersion(appVersionInfo)).build();
  }
  @ApiOperation("新增版本")
  @PostMapping("/addNewAppVersion")
  public RestMessage addNewAppVersion(@RequestBody AppVersionInfo appVersionInfo) {

    return RestBuilders.successBuilder()
        .data(appVersionInfoService.addNewAppVersion(appVersionInfo)).build();
  }

  @ApiOperation("修改版本")
  @PutMapping("/updateAppVersion")
  public RestMessage updateAppVersion(@RequestBody AppVersionInfo appVersionInfo) {

    return RestBuilders.successBuilder()
        .data(appVersionInfoService.updateAppVersion(appVersionInfo)).build();
  }
  @ApiOperation("删除版本")
  @DeleteMapping("/delAppVersion/{id}")
  public RestMessage delAppVersion(@PathVariable Integer id) {

    return RestBuilders.successBuilder()
        .data(appVersionInfoService.delAppVersion(id)).build();
  }

  @ApiOperation("版本列表")
  @PostMapping("/getPage")
  public RestMessage getPage(@RequestBody RequestModel<AppVersionInfo> requestModel) {
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<AppVersionInfo> record = appVersionInfoService.getPage(requestModel.getPage(), requestModel.getCustomQueryParams());
    return RestBuilders.successBuilder().data(record).build();
  }
}
