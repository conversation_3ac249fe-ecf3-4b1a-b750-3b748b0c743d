package com.easylinkin.linkappapi.appversion.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * App版本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_version_info")
public class AppVersionInfo extends Model<AppVersionInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备品牌，apple、huawei
     */
    @TableField("device_brand")
    private String deviceBrand;

    /**
     * 系统名称，ios，android
     */
    @TableField("os_name")
    private String osName;

    /**
     * 系统版本
     */
    @TableField("os_version")
    private String osVersion;

    /**
     * 系统语言，zh-CN
     */
    @TableField("os_language")
    private String osLanguage;

    /**
     * app版本号
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * 应用版本号
     */
    @TableField("application_version")
    private String applicationVersion;

    /**
     * app下载地址
     */
    @TableField("app_url")
    private String appUrl;

    /**
     * 更新内容
     */
    @TableField("update_content")
    private String updateContent;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，0是，1否
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
