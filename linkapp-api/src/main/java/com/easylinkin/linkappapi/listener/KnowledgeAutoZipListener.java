package com.easylinkin.linkappapi.listener;

import com.easylinkin.linkappapi.event.KnowledgeAutoZipEvent;
import com.easylinkin.linkappapi.knowledgebase.entity.KnowledgeBase;
import com.easylinkin.linkappapi.knowledgebase.service.KnowledgeBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/17
 * @description 监听知识库的修改和新增 自动打包知识zip
 */
@Slf4j
@Component("knowledgeAutoZipListener")
public class KnowledgeAutoZipListener implements ApplicationListener<KnowledgeAutoZipEvent> {

  @Autowired
  private KnowledgeBaseService knowledgeBaseService;

  @Async
  @Override
  public void onApplicationEvent(KnowledgeAutoZipEvent event) {
    log.info("自动打包知识zip start");
    log.info("自动打包知识zip-被调用-线程:" + Thread.currentThread());
    log.info("自动打包知识zip-被调用-线程状态:" + Thread.currentThread().getState());
    Long begin = System.currentTimeMillis();
    KnowledgeBase knowledgeBase = event.getSource();
    knowledgeBaseService.knowledgeZip(knowledgeBase);
    Long end = System.currentTimeMillis();
    log.info("自动打包知识zip耗时={}秒",(end-begin)/1000);
    log.info("自动打包知识zip end");
  }

}
