//package com.easylinkin.linkappapi.listener;
//
//import com.easylinkin.linkappapi.alarm.entity.Alarm;
//import java.util.concurrent.CopyOnWriteArraySet;
//import javax.websocket.OnClose;
//import javax.websocket.OnOpen;
//import javax.websocket.Session;
//import javax.websocket.server.PathParam;
//import javax.websocket.server.ServerEndpoint;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
///**
// * <p>设备地图页面监听器</p>
// *
// * <AUTHOR>
// * @since 2020/9/23 12:06
// */
//@ServerEndpoint("/deviceMapListener/{tenantId}/{userId}")
//@Component
//public class DeviceMapListener extends Listener {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceMapListener.class);
//
//    private static CopyOnWriteArraySet<Listener> webSocketSet = new CopyOnWriteArraySet<>();
//
//
//    @Override
//    @OnOpen
//    public void onOpen(Session session, @PathParam("tenantId") String tenantId, @PathParam("userId") String userId) {
//        init(session, tenantId, userId);
//        // 加入set中
//        webSocketSet.add(this);
//    }
//
//    /**
//     * 连接关闭调用的方法
//     */
//    @Override
//    @OnClose
//    public void onClose() {
//        LOGGER.info("通知关闭连接,用户id:{}", getUserId());
//        // 从set中删除
//        webSocketSet.remove(this);
//    }
//
//    public void sendNotice(Alarm alarm) {
//        for (Listener listener : webSocketSet) {
//            if (listener.getTenantId() != null && listener.getTenantId().equals(alarm.getTenantId())) {
//                listener.sendMessage("deviceMapListener 更新页面");
//            }
//        }
//    }
//}