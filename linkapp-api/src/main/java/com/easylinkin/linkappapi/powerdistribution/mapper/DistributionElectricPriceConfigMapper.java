package com.easylinkin.linkappapi.powerdistribution.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPriceConfig;

public interface DistributionElectricPriceConfigMapper extends BaseMapper<DistributionElectricPriceConfig> {
    List<DistributionElectricPriceConfig> findAllByElectricPriceId(@Param("electricPriceId")Long electricPriceId);


}