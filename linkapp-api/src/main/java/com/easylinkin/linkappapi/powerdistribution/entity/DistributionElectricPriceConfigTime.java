package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalTime;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@TableName(value = "distribution_electric_price_config_time")
public class DistributionElectricPriceConfigTime {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /***
     * 0 不分时 峰谷周期 1 尖，2 峰，3 平，4 谷
     */
    @NotNull
    @Range(min = 0,max = 4)
    @TableField(value = "type")
    private Integer type;

    /**
     * 开始时间
     */
    @NotNull
    @TableField(value = "start_time")
    private LocalTime startTime;

    /**
     * 结束时间
     */
    @NotNull
    @TableField(value = "end_time")
    private LocalTime endTime;

    /**
     * 单价
     */
    @NotNull
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 分时用电设置id
     */
    @TableField(value = "electric_price_config_id")
    private Long electricPriceConfigId;
}