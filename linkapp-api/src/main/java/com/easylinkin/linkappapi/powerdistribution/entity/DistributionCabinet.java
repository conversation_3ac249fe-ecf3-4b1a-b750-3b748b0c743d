package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配电柜
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("distribution_cabinet")
public class DistributionCabinet extends Model<DistributionCabinet> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 配电柜类型名称
     */
    @TableField("name")
    private String name;

    /**
     * 配电柜类型id
     */
    @TableField("distribution_cabinet_type_id")
    private String distributionCabinetTypeId;

    /**
     * 配电站id
     */
    @TableField("distribution_room_id")
    private String distributionRoomId;

    /**
     * 排序编号
     */
    @TableField("sort_no")
    private Integer sortNo;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @TableField("creator")
    private String creator;

    /**
     * 修改人id
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    @TableField(exist = false)
    private List<DistributionCabinetRefDevice> distributionCabinetRefDeviceList;

    /**
     * 状态
     */
    @TableField(exist = false)
    private DistributionCabinetStatus distributionCabinetStatus;


    /**
     * 配电柜类型名称
     */
    @TableField(exist = false)
    private String distributionCabinetTypeName;

    /**
     * 对应组态图片
     */
    @TableField(exist = false)
    private String statusPicture;

    /***
     * 区域
     */
    @TableField(exist = false)
    private String areaPath;

    /***
     * 站点
     */
    @TableField(exist = false)
    private String roomName;

    /***
     * 位置名称
     */
    @TableField(exist = false)
    private String siteName;

    /***
     * 企业ID
     */
    @TableField(exist = false)
    private Integer companyId;

    @TableField(exist = false)
    private Set<String> ids;

    @TableField(exist = false)
    private Set<String> roomIds;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
