package com.easylinkin.linkappapi.powerdistribution.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.device.mapper.DeviceMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetArchivesMapper;
import com.easylinkin.linkappapi.powerdistribution.service.RunAnalyzeService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmConfig;

import java.util.List;

import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetAlarmConfigMapper;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetAlarmConfigService;
import org.springframework.util.Assert;

@Service
public class DistributionCabinetAlarmConfigServiceImpl extends ServiceImpl<DistributionCabinetAlarmConfigMapper, DistributionCabinetAlarmConfig> implements DistributionCabinetAlarmConfigService {
    @Resource
    DeviceMapper deviceMapper;
    @Resource
    RunAnalyzeService runAnalyzeService;

    @Override
    public int batchInsert(List<DistributionCabinetAlarmConfig> list) {
        return baseMapper.batchInsert(list);
    }


    /***
     *  指标设置
     * @param distributionCabinetAlarmConfig
     */
    @Override
    public void saveOrUpdateEntity(DistributionCabinetAlarmConfig distributionCabinetAlarmConfig) {
        Assert.notNull(distributionCabinetAlarmConfig, "参数不能为空");
        Assert.notNull(distributionCabinetAlarmConfig.getCabinetId(), "cabinetId 不能为空");
        Assert.notNull(distributionCabinetAlarmConfig.getDeviceCode(), "deviceCode 不能为空");
        Assert.notNull(distributionCabinetAlarmConfig.getType(), "type 不能为空");
        // 1 负荷，2 不平衡度
        if (distributionCabinetAlarmConfig.getType() == 1) {
            //最小0，最大9999
            boolean count = loadindex(distributionCabinetAlarmConfig.getDeviceCode());
            if (!count) {
                throw new IllegalArgumentException("设备[" + distributionCabinetAlarmConfig.getDeviceCode() + "]无设置负荷指标属性");
            }
        } else {
            boolean count = aoib(distributionCabinetAlarmConfig.getDeviceCode());
            if (!count) {
                throw new IllegalArgumentException("设备[" + distributionCabinetAlarmConfig.getDeviceCode() + "]无设置不平衡度指标属性");
            }
        }
        baseMapper.updateByCabinetIdAndDeviceCodeAndType(distributionCabinetAlarmConfig);
    }

    @Override
    public DistributionCabinetAlarmConfig getDistributionCabinetAlarmConfigByCondition(DistributionCabinetAlarmConfig distributionCabinetAlarmConfig) {
        QueryWrapper qw = new QueryWrapper();
        qw.eq("device_code", distributionCabinetAlarmConfig.getDeviceCode());
        qw.eq("cabinet_id", distributionCabinetAlarmConfig.getCabinetId());
        qw.eq("type", distributionCabinetAlarmConfig.getType());
        List<DistributionCabinetAlarmConfig> list = baseMapper.selectList(qw);
        if(ObjectUtils.isEmpty(list)){
            return null;
        }else {
            DistributionCabinetAlarmConfig result = list.get(0);
            return result;
        }
    }

    /***
     * 指标过滤
     * @param deviceCode
     * @return
     */
    @Override
    public boolean loadindex(String deviceCode){
        List<Device> deviceList = deviceMapper.getMonitorDevices(new Device().setCodes(Arrays.asList(deviceCode)));
        if(deviceList == null || deviceList.size()<=0 || ObjectUtils.isEmpty(deviceList)){
            return false;
        }
        List<Device> deviceListResult = runAnalyzeService.deviceAttributeFiler(deviceList,1);
        if(deviceListResult == null || deviceListResult.size()<=0 ||  ObjectUtils.isEmpty(deviceListResult)){
            return false;
        }
        return true;
    }

    /***
     * 不平衡过滤
     * @param deviceCode
     * @return
     */
    @Override
    public boolean aoib(String deviceCode){
        List<Device> deviceList = deviceMapper.getMonitorDevices(new Device().setCodes(Arrays.asList(deviceCode)));
        if(deviceList == null || deviceList.size()<=0 || ObjectUtils.isEmpty(deviceList)){
            return false;
        }
        List<Device> deviceListResult = runAnalyzeService.deviceAttributeFiler(deviceList,6);
        if(deviceListResult == null || deviceListResult.size()<=0 || ObjectUtils.isEmpty(deviceListResult)){
            return false;
        }
        return true;
    }
}
