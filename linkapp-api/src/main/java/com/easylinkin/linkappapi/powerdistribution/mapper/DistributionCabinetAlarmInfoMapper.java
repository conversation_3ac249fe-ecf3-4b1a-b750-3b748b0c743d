package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.alarm.entity.Alarm;
import com.easylinkin.linkappapi.alarm.entity.AlarmVo;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmInfo;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface DistributionCabinetAlarmInfoMapper extends BaseMapper<DistributionCabinetAlarmInfo> {
    int batchInsert(@Param("list") List<DistributionCabinetAlarmInfo> list);

    IPage<DistributionCabinetAlarmInfo> findAllByCabientId(Page page, @Param("distributionCabinetAlarmInfo") DistributionCabinetAlarmInfo distributionCabinetAlarmInfo, @Param("spacesIds") List<String> spacesIds);

    /***
     * 查询告警
     * @param distributionCabinetAlarmInfo
     * @param spacesIds
     * @return
     */
    List<DistributionCabinetAlarmInfo> findAllByCabientId(@Param("distributionCabinetAlarmInfo") DistributionCabinetAlarmInfo distributionCabinetAlarmInfo, @Param("spacesIds") List<String> spacesIds);

    /***
     * 导出
     * @param distributionCabinetAlarmInfo
     * @param spacesIds
     * @return
     */
    List<AlarmVo> findAllByCabientIdVos(@Param("distributionCabinetAlarmInfo") DistributionCabinetAlarmInfo distributionCabinetAlarmInfo, @Param("spacesIds") List<String> spacesIds);

    Alarm getDetail(@Param("distributionCabinetAlarmInfo") DistributionCabinetAlarmInfo distributionCabinetAlarmInfo);
}