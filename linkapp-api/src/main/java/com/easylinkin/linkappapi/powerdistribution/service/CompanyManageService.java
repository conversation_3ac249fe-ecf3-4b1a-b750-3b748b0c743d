package com.easylinkin.linkappapi.powerdistribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.powerdistribution.entity.CompanyManageBean;

import java.util.List;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/11 11:37
 */
public interface CompanyManageService {

    /**
     *新增企业信息
     * @param companyManageBean
     * @return
     */
    String saveCompanyInfo(CompanyManageBean companyManageBean);

    /**
     *修改企业信息
     * @param companyManageBean
     * @return
     */
    String updateCompanyInfo(CompanyManageBean companyManageBean);

    /**
     *删除企业
     * @param companyIds
     * @return
     */
    void deleteCompanyInfo(String companyIds);

    /**
     *获取企业分页列表
     * @param page
     * @param companyManageBean
     * @return
     */
    IPage<CompanyManageBean> getCompanyPage(Page page, CompanyManageBean companyManageBean);

    List<CompanyManageBean> getCompanyList(CompanyManageBean companyManageBean);

}
