package com.easylinkin.linkappapi.powerdistribution.service;

import java.util.List;

import com.easylinkin.linkappapi.powerdistribution.entity.DistributionAreaParam;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetArchives;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetArchivesVo;
import com.easylinkin.linkappapi.space.entity.LinkappArea;
import com.easylinkin.linkappapi.space.vo.SpaceTreeVo;
import org.springframework.web.bind.annotation.RequestBody;

public interface DistributionCabinetArchivesService extends IService<DistributionCabinetArchives> {

    /***
     * 查询区域树
     * @param linkappArea
     * @return
     */
    List<SpaceTreeVo> selectLinkappAreaTreeList(DistributionAreaParam distributionAreaParam);

    /***
     * 保存修改档案信息
     * @param distributionCabinetArchives
     */
    void saveOrUpdateEntity(DistributionCabinetArchives distributionCabinetArchives);

    /***
     * 设备档案查询
     * @param distributionCabinetArchives
     * @return
     */
    DistributionCabinetArchivesVo getDistributionCabinetArchives(DistributionCabinetArchives distributionCabinetArchives);
}

