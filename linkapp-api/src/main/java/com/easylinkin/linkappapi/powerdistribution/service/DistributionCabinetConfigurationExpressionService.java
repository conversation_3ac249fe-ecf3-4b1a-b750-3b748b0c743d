package com.easylinkin.linkappapi.powerdistribution.service;

import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfigurationExpression;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetTypeSite;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 组态表达式 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetConfigurationExpressionService extends IService<DistributionCabinetConfigurationExpression> {

    /**
     * 新增组态表达式
     * @param distributionCabinetConfigurationExpression
     */
    void add(DistributionCabinetConfigurationExpression distributionCabinetConfigurationExpression);
    
    List<DistributionCabinetConfigurationExpression> getDistributionCabinetConfigurationExpressionByTenant(String tenantId,String deviceUnitCode);
}
