package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配电柜类型关联位置信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("distribution_cabinet_type_site")
public class DistributionCabinetTypeSite extends Model<DistributionCabinetTypeSite> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 位置名称
     */
    @TableField("site_name")
    private String siteName;


    /**
     * 序号
     */
    @TableField("sort_no")
    private Integer sortNo;

    /**
     * 是否必填
     */
    @TableField("required")
    private Boolean required;

    /**
     * 是否是电力设备
     */
    @TableField("electric_equipment")
    private Boolean electricEquipment;

    /**
     * 型号code
     */
    @TableField("device_unit_code")
    private String deviceUnitCode;

    /**
     * 型号版本号
     */
    @TableField("device_unit_version")
    private String deviceUnitVersion;

    /**
     * 配电柜类型id
     */
    @TableField("distribution_cabinet_type_id")
    private String distributionCabinetTypeId;

    /**
     * 组态
     */
    @TableField(exist = false)
    private List<DistributionCabinetConfiguration> distributionCabinetConfigurationList;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
