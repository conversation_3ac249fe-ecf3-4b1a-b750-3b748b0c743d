package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;

/**
 * <p>
 * 配电柜关联设备
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("distribution_cabinet_ref_device")
public class DistributionCabinetRefDevice extends Model<DistributionCabinetRefDevice> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 配电柜id
     */
    @TableField("distribution_cabinet_id")
    private String distributionCabinetId;

    /**
     * 设备code
     */
    @TableField("device_code")
    private String deviceCode;

    /**
     * 设备型号code
     */
    @TableField(exist = false)
    private String deviceUnitCode;

    /**
     * 设备实时状态
     */
    @TableField(exist = false)
    private List<DeviceAttributeStatus> deviceAttributeStatusList;

    /**
     * 配电柜类型位置id
     */
    @TableField("distribution_cabinet_type_site_id")
    private String distributionCabinetTypeSiteId;

    /**
     * 配电柜类型位置 序号
     */
    @TableField(exist = false)
    private Integer sortNo;

    /**
     * 是否是电力设备
     */
    @TableField(exist = false)
    private Boolean electricEquipment;

    /**
     * 位置信息
     */
    @TableField(exist = false)
    private String distributionCabinetTypeSiteName;

    /**
     * 组态列表
     */
    @TableField(exist = false)
    private List<DistributionCabinetConfiguration> distributionCabinetConfigurationList;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
