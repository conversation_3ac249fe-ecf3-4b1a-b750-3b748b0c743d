package com.easylinkin.linkappapi.powerdistribution.controller;

import com.easylinkin.linkappapi.elasticsearch.entity.EsQuerymodel;
import com.easylinkin.linkappapi.powerdistribution.entity.ElectricityAnalysisParam;
import com.easylinkin.linkappapi.powerdistribution.service.ElectricityAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.io.IOException;

/**
 * @program: linkapp-group-cloud
 * @description: 电量分析
 * @author: chenkaixuan
 * @create: 2021-09-18 10:14
 */
@Slf4j
@RestController
@RequestMapping("/electricityAnalysis")
public class ElectricityAnalysisController {
    @Autowired
    ElectricityAnalysisService electricityAnalysisService;

    @PostMapping("sharingElectric")
    public RestMessage add(@Validated @RequestBody ElectricityAnalysisParam electricityAnalysisParam) {
        try {
            return RestBuilders.successBuilder(electricityAnalysisService.energySameProcessing(electricityAnalysisParam)).build();
        } catch (IOException e) {
            log.error("电量分析-分时用电错误:",e.getMessage() );
            e.printStackTrace();
            return RestBuilders.failureMessage().setMessage("电量分析-分时用电错误:"+e.getMessage());
        }
    }
}
