package com.easylinkin.linkappapi.powerdistribution.service.impl;

import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetFileMapper;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetFile;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetFileService;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

@Service
public class DistributionCabinetFileServiceImpl extends ServiceImpl<DistributionCabinetFileMapper, DistributionCabinetFile> implements DistributionCabinetFileService{

    @Override
    public void saveBatchEntity(List<DistributionCabinetFile> list) {
        if(list == null ){
            return;
        }
        for (DistributionCabinetFile distributionCabinetFile : list) {
            Assert.notNull(distributionCabinetFile.getCabinetId(), "cabinetId 不能为空");
        }
        Set<String> collect = list.stream().map(DistributionCabinetFile::getCabinetId).collect(Collectors.toSet());
        //先删除后保存
        new LambdaUpdateChainWrapper<>(baseMapper)
                .in(DistributionCabinetFile::getCabinetId,collect )
                .remove();
        saveBatch(list);
    }
}
