package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfiguration;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 配电柜类型组态 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetConfigurationMapper extends BaseMapper<DistributionCabinetConfiguration> {

    List<DistributionCabinetConfiguration> getDistributionCabinetConfigurationWithExpressions(@Param("distributionCabinetConfiguration") DistributionCabinetConfiguration distributionCabinetConfiguration);

    List<DistributionCabinetConfiguration> getDistributionCabinetConfigurations(@Param("distributionCabinetConfiguration") DistributionCabinetConfiguration distributionCabinetConfiguration);

    IPage<DistributionCabinetConfiguration> getDistributionCabinetConfigurations(@Param("page") Page page, @Param("distributionCabinetConfiguration") DistributionCabinetConfiguration distributionCabinetConfiguration);

    DistributionCabinetConfiguration getDistributionCabinetConfiguration(@Param("id") String id);
}
