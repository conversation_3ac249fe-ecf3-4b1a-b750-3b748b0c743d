package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 配电柜类型 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetTypeMapper extends BaseMapper<DistributionCabinetType> {

    List<DistributionCabinetType> getDistributionCabinetTypes(@Param("distributionCabinetType") DistributionCabinetType distributionCabinetType);

    IPage<DistributionCabinetType> getDistributionCabinetTypes(@Param("page") Page page, @Param("distributionCabinetType") DistributionCabinetType distributionCabinetType);

    DistributionCabinetType getDistributionCabinetType(@Param("id") String id);

    /**
     * 查询配电柜 以及配电柜位置信息+组态+组态表达式
     */
    List<DistributionCabinetType> getDistributionCabinetTypeAndCabinetTypeAndCabinetConfigurations(@Param("distributionCabinetType") DistributionCabinetType distributionCabinetType);
}
