package com.easylinkin.linkappapi.powerdistribution.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.NumberFormat;

import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "distribution_cabinet_alarm_config")
public class DistributionCabinetAlarmConfig{
    /**
     * 配电柜ID
     */
    @TableId(value = "cabinet_id", type = IdType.INPUT)
    private String cabinetId;

    /**
     * 配电柜ID
     */
    @TableId(value = "device_code", type = IdType.INPUT)
    private String deviceCode;

    /**
     * 指标类型 1：负荷指标 2:不平衡度指标
     */
    @Range(min = 1, max = 2)
    @TableId(value = "type", type = IdType.INPUT)
    private Integer type;

    /**
     * 负荷上限,严重不平衡度
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_INT,pattern = "#")
    @TableField(value = "value_max")
    private BigDecimal valueMax;

    /**
     * 负荷下限,不平衡度
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_INT,pattern = "#")
    @TableField(value = "value_min")
    private BigDecimal valueMin;

    /***
     * 设备名称
     */
    @TableField(exist = false)
    private String name;

    /***
     * 查询值
     */
    @TableField(exist = false)
    private BigDecimal queryValue;
}