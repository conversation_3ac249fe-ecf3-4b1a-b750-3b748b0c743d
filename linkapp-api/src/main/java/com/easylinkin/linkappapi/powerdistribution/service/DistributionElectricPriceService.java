package com.easylinkin.linkappapi.powerdistribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPrice;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface DistributionElectricPriceService extends IService<DistributionElectricPrice> {
    IPage<DistributionElectricPrice> page(Page page, DistributionElectricPrice distributionElectricPrice);

    DistributionElectricPrice findAllById(DistributionElectricPrice distributionElectricPrice);

    void saveOrUpdateEntity(DistributionElectricPrice distributionElectricPrice);

    void removeByIds(DistributionElectricPrice distributionElectricPrice);

    List<DistributionElectricPrice> getAll(DistributionElectricPrice distributionElectricPrice);
}
