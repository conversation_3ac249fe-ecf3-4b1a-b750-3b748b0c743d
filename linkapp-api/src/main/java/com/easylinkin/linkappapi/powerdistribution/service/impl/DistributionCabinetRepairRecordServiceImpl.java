package com.easylinkin.linkappapi.powerdistribution.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.common.utils.excel.ExcelConstant;
import com.easylinkin.linkappapi.common.utils.excel.ExcelTools;
import com.easylinkin.linkappapi.common.utils.io.OutputStreamUtil;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairFile;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetRepairFileMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetRepairRecordMapper;
import java.util.stream.Collectors;

import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairRecord;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetRepairRecordService;
import org.springframework.util.Assert;

@Service
public class DistributionCabinetRepairRecordServiceImpl extends ServiceImpl<DistributionCabinetRepairRecordMapper, DistributionCabinetRepairRecord> implements DistributionCabinetRepairRecordService{

    @Resource
    DistributionCabinetRepairFileMapper distributionCabinetRepairFileMapper;
    @Override
    public int batchInsert(List<DistributionCabinetRepairRecord> list) {
        return baseMapper.batchInsert(list);
    }

	@Override
	public List<DistributionCabinetRepairRecord> findAllByCabinetId(DistributionCabinetRepairRecord distributionCabinetRepairRecord){
        Assert.notNull(distributionCabinetRepairRecord.getCabinetId(), "cabinetId 不能为空");
		 return baseMapper.findAllByCabinetId(distributionCabinetRepairRecord);
	}

	@Override
    public void saveOrUpdateEntity(DistributionCabinetRepairRecord distributionCabinetRepairRecord){
        if(distributionCabinetRepairRecord == null){
            return;
        }
        Long id = distributionCabinetRepairRecord.getId();
        paramValidate(distributionCabinetRepairRecord);
        distributionCabinetRepairRecord.setCreateTime(LocalDateTime.now());
        saveOrUpdate(distributionCabinetRepairRecord);
        //修改先删除子类，再添加
        if(id != null){
            distributionCabinetRepairFileMapper.deleteByCabinetRepairId(id);
        }
        if(distributionCabinetRepairRecord.getDistributionCabinetRepairFileList() != null && distributionCabinetRepairRecord.getDistributionCabinetRepairFileList().size() > 0
                && distributionCabinetRepairRecord.getId() != null){
            List<DistributionCabinetRepairFile> cabinetRepairFiles = distributionCabinetRepairRecord.getDistributionCabinetRepairFileList().stream().map(m -> {
                m.setCabinetRepairId(distributionCabinetRepairRecord.getId());
                return m;
            }).collect(Collectors.toList());
            distributionCabinetRepairFileMapper.batchInsert(cabinetRepairFiles);
        }
    }

    @Override
    public DistributionCabinetRepairRecord findByIdOrderByCreateTimeDesc(DistributionCabinetRepairRecord distributionCabinetRepairRecord){
        Assert.notNull(distributionCabinetRepairRecord.getId(), "id 不能为空");
        List<DistributionCabinetRepairRecord> byIdOrderByCreateTimeDesc = baseMapper.findByIdOrderByCreateTimeDesc(distributionCabinetRepairRecord.getId());
        if(byIdOrderByCreateTimeDesc == null || byIdOrderByCreateTimeDesc.size()<=0){
            return null;
        }
        return byIdOrderByCreateTimeDesc.get(0);
    }

    @Override
    public void removeByIds(DistributionCabinetRepairRecord distributionCabinetRepairRecord) {
        Assert.notEmpty(distributionCabinetRepairRecord.getIds(), "ids 不能为空");
        for (Long aLong : distributionCabinetRepairRecord.getIds()) {
            distributionCabinetRepairFileMapper.deleteByCabinetRepairId(aLong);
        }
        removeByIds(distributionCabinetRepairRecord.getIds());
    }

    @Override
    public void exportData(DistributionCabinetRepairRecord distributionCabinetRepairRecord, HttpServletRequest request, HttpServletResponse response) {
        Assert.notNull(distributionCabinetRepairRecord.getCabinetId(), "cabinetId 不能为空");

        List<DistributionCabinetRepairRecord> result = baseMapper.findAllByCabinetId(distributionCabinetRepairRecord);
        String keyValue = "姓名:name,内容:content,状态:statusName,时间:repairTimeStr";
        String title = "设备档案检修记录导出表格";
        String fileName = title + ".xls";
        try {
            OutputStream outputStream = OutputStreamUtil
                    .getOutputStream(request, response, fileName);
            ExcelTools.exportExcel(outputStream, keyValue, result, ExcelConstant.XLS, title);
            response.flushBuffer();
            outputStream.close();
        } catch (IOException e) {
            log.error("excel导出失败", e);
            throw new RuntimeException("excel导出失败！IOException异常" + e.getMessage());
        } catch (Exception e) {
            log.error("excel导出失败", e);
            throw new RuntimeException("excel导出失败！" + e.getMessage());
        }

    }

    private void paramValidate(DistributionCabinetRepairRecord distributionCabinetRepairRecord){
        Assert.notNull(distributionCabinetRepairRecord.getName(), "name 不能为空");
        Assert.notNull(distributionCabinetRepairRecord.getStatus(),"status 不能为空" );
        Assert.notNull(distributionCabinetRepairRecord.getContent(),"content 不能为空" );
        Assert.notNull(distributionCabinetRepairRecord.getRepairTime(),"repairTime 不能为空" );
    }


    @Override
    public IPage<DistributionCabinetRepairRecord> page(Page page, DistributionCabinetRepairRecord distributionCabinetRepairRecord) {
        Assert.notNull(distributionCabinetRepairRecord.getCabinetId(), "cabinetId 不能为空");
        return baseMapper.findAllByCabinetId(page, distributionCabinetRepairRecord);
    }
}
