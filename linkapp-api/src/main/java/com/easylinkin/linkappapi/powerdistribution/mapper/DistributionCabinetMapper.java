package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 配电柜 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetMapper extends BaseMapper<DistributionCabinet> {

    List<DistributionCabinet> getDistributionCabinets(@Param("distributionCabinet") DistributionCabinet distributionCabinet);

    List<DistributionCabinetRefDevice> getCabinetRefDevicesAndDeviceStatus(@Param("distributionCabinet") DistributionCabinet distributionCabinet);

    IPage<DistributionCabinet> getDistributionCabinets(@Param("page") Page page, @Param("distributionCabinet") DistributionCabinet distributionCabinet);

    DistributionCabinet getDistributionCabinet(@Param("id") String id);

    List<DistributionCabinet> getDistributionCabinetWithRefDevices(@Param("distributionCabinet") DistributionCabinet distributionCabinet);

    List<DistributionCabinet> getRealTimeDistributionCabinetWithStatus(@Param("distributionCabinet") DistributionCabinet distributionCabinet);

    DistributionCabinet getDistributionCabinetDetail(@Param("id") String id);

    List<DistributionCabinet> getDistributionCabinetAreaPathInfo(@Param("distributionCabinet") DistributionCabinet distributionCabinet, @Param("device") Device device);

    List<DistributionCabinet> getDistributionCabinetByAttr(@Param("distributionCabinet") DistributionCabinet distributionCabinet, @Param("fieldProperties") List<String> fieldProperties);
}
