package com.easylinkin.linkappapi.powerdistribution.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/14 10:46
 */
@Data
public class ElectricityDataEs {

    /**
     *总视在功率
     */
    @JSONField(name="apparent_power")
    private Double apparentPower;

    /**
     *AB线电压
     */
    @JSONField(name="ab_phase_voltage")
    private Double abPhaseVoltage;

    /**
     *BC线电压
     */
    @JSONField(name="bc_phase_voltage")
    private Double bcPhaseVoltage;

    /**
     *CA线电压
     */
    @JSONField(name="ac_phase_voltage")
    private Double acPhaseVoltage;

    /**
     *A相电压
     */
    @JSONField(name="a_phase_voltage")
    private Double aPhaseVoltage;

    /**
     *B相电压
     */
    @JSONField(name="b_phase_voltage")
    private Double bPhaseVoltage;

    /**
     *C相电压
     */
    @JSONField(name="c_phase_voltage")
    private Double cPhaseVoltage;

    /**
     *A相电流
     */
    @JSONField(name="a_phase_current")
    private Double aPhaseCurrent;

    /**
     *B相电流
     */
    @JSONField(name="b_phase_current")
    private Double bPhaseCurrent;

    /**
     *C相电流
     */
    @JSONField(name="c_phase_current")
    private Double cPhaseCurrent;


    /**
     *电压不平衡率
     */
    @JSONField(name="voltage_unbalance_rate")
    private Double voltageUnbalanceRate;

    /**
     *电流不平衡率
     */
    @JSONField(name="current_unbalance_rate")
    private Double currentUnbalanceRate;



    @JSONField(name="a_phase_voltage_harmonics")
    private Double aPhaseVoltageHarmonics;

    private Double aPhaseVoltageHarmonics1;

    public void setAPhaseVoltageHarmonics(Double aPhaseVoltageHarmonics){
        this.aPhaseVoltageHarmonics = aPhaseVoltageHarmonics;
        this.aPhaseVoltageHarmonics1 = aPhaseVoltageHarmonics;
    }

    @JSONField(name="a_phase_voltage_harmonics_2")
    private Double aPhaseVoltageHarmonics2;

    @JSONField(name="a_phase_voltage_harmonics_3")
    private Double aPhaseVoltageHarmonics3;

    @JSONField(name="a_phase_voltage_harmonics_4")
    private Double aPhaseVoltageHarmonics4;

    @JSONField(name="a_phase_voltage_harmonics_5")
    private Double aPhaseVoltageHarmonics5;

    @JSONField(name="a_phase_voltage_harmonics_6")
    private Double aPhaseVoltageHarmonics6;

    @JSONField(name="a_phase_voltage_harmonics_7")
    private Double aPhaseVoltageHarmonics7;

    @JSONField(name="a_phase_voltage_harmonics_8")
    private Double aPhaseVoltageHarmonics8;

    @JSONField(name="a_phase_voltage_harmonics_9")
    private Double aPhaseVoltageHarmonics9;

    @JSONField(name="a_phase_voltage_harmonics_10")
    private Double aPhaseVoltageHarmonics10;

    @JSONField(name="a_phase_voltage_harmonics_11")
    private Double aPhaseVoltageHarmonics11;

    @JSONField(name="a_phase_voltage_harmonics_12")
    private Double aPhaseVoltageHarmonics12;

    @JSONField(name="a_phase_voltage_harmonics_13")
    private Double aPhaseVoltageHarmonics13;

    @JSONField(name="a_phase_voltage_harmonics_14")
    private Double aPhaseVoltageHarmonics14;

    @JSONField(name="a_phase_voltage_harmonics_15")
    private Double aPhaseVoltageHarmonics15;

    @JSONField(name="a_phase_voltage_harmonics_16")
    private Double aPhaseVoltageHarmonics16;

    @JSONField(name="a_phase_voltage_harmonics_17")
    private Double aPhaseVoltageHarmonics17;

    @JSONField(name="a_phase_voltage_harmonics_18")
    private Double aPhaseVoltageHarmonics18;

    @JSONField(name="a_phase_voltage_harmonics_19")
    private Double aPhaseVoltageHarmonics19;

    @JSONField(name="a_phase_voltage_harmonics_20")
    private Double aPhaseVoltageHarmonics20;

    @JSONField(name="a_phase_voltage_harmonics_21")
    private Double aPhaseVoltageHarmonics21;

    @JSONField(name="a_phase_voltage_harmonics_22")
    private Double aPhaseVoltageHarmonics22;

    @JSONField(name="a_phase_voltage_harmonics_23")
    private Double aPhaseVoltageHarmonics23;

    @JSONField(name="a_phase_voltage_harmonics_24")
    private Double aPhaseVoltageHarmonics24;

    @JSONField(name="a_phase_voltage_harmonics_25")
    private Double aPhaseVoltageHarmonics25;

    @JSONField(name="a_phase_voltage_harmonics_26")
    private Double aPhaseVoltageHarmonics26;

    @JSONField(name="a_phase_voltage_harmonics_27")
    private Double aPhaseVoltageHarmonics27;

    @JSONField(name="a_phase_voltage_harmonics_28")
    private Double aPhaseVoltageHarmonics28;

    @JSONField(name="a_phase_voltage_harmonics_29")
    private Double aPhaseVoltageHarmonics29;

    @JSONField(name="a_phase_voltage_harmonics_30")
    private Double aPhaseVoltageHarmonics30;

    @JSONField(name="a_phase_voltage_harmonics_31")
    private Double aPhaseVoltageHarmonics31;

    @JSONField(name="b_phase_voltage_harmonics")
    private Double bPhaseVoltageHarmonics;

    private Double bPhaseVoltageHarmonics1;

    public void setBPhaseVoltageHarmonics(Double bPhaseVoltageHarmonics){
        this.bPhaseVoltageHarmonics = bPhaseVoltageHarmonics;
        this.bPhaseVoltageHarmonics1 = bPhaseVoltageHarmonics;
    }

    @JSONField(name="b_phase_voltage_harmonics_2")
    private Double bPhaseVoltageHarmonics2;

    @JSONField(name="b_phase_voltage_harmonics_3")
    private Double bPhaseVoltageHarmonics3;

    @JSONField(name="b_phase_voltage_harmonics_4")
    private Double bPhaseVoltageHarmonics4;

    @JSONField(name="b_phase_voltage_harmonics_5")
    private Double bPhaseVoltageHarmonics5;

    @JSONField(name="b_phase_voltage_harmonics_6")
    private Double bPhaseVoltageHarmonics6;

    @JSONField(name="b_phase_voltage_harmonics_7")
    private Double bPhaseVoltageHarmonics7;

    @JSONField(name="b_phase_voltage_harmonics_8")
    private Double bPhaseVoltageHarmonics8;

    @JSONField(name="b_phase_voltage_harmonics_9")
    private Double bPhaseVoltageHarmonics9;

    @JSONField(name="b_phase_voltage_harmonics_10")
    private Double bPhaseVoltageHarmonics10;

    @JSONField(name="b_phase_voltage_harmonics_11")
    private Double bPhaseVoltageHarmonics11;

    @JSONField(name="b_phase_voltage_harmonics_12")
    private Double bPhaseVoltageHarmonics12;

    @JSONField(name="b_phase_voltage_harmonics_13")
    private Double bPhaseVoltageHarmonics13;

    @JSONField(name="b_phase_voltage_harmonics_14")
    private Double bPhaseVoltageHarmonics14;

    @JSONField(name="b_phase_voltage_harmonics_15")
    private Double bPhaseVoltageHarmonics15;

    @JSONField(name="b_phase_voltage_harmonics_16")
    private Double bPhaseVoltageHarmonics16;

    @JSONField(name="b_phase_voltage_harmonics_17")
    private Double bPhaseVoltageHarmonics17;

    @JSONField(name="b_phase_voltage_harmonics_18")
    private Double bPhaseVoltageHarmonics18;

    @JSONField(name="b_phase_voltage_harmonics_19")
    private Double bPhaseVoltageHarmonics19;

    @JSONField(name="b_phase_voltage_harmonics_20")
    private Double bPhaseVoltageHarmonics20;

    @JSONField(name="b_phase_voltage_harmonics_21")
    private Double bPhaseVoltageHarmonics21;

    @JSONField(name="b_phase_voltage_harmonics_22")
    private Double bPhaseVoltageHarmonics22;

    @JSONField(name="b_phase_voltage_harmonics_23")
    private Double bPhaseVoltageHarmonics23;

    @JSONField(name="b_phase_voltage_harmonics_24")
    private Double bPhaseVoltageHarmonics24;

    @JSONField(name="b_phase_voltage_harmonics_25")
    private Double bPhaseVoltageHarmonics25;

    @JSONField(name="b_phase_voltage_harmonics_26")
    private Double bPhaseVoltageHarmonics26;

    @JSONField(name="b_phase_voltage_harmonics_27")
    private Double bPhaseVoltageHarmonics27;

    @JSONField(name="b_phase_voltage_harmonics_28")
    private Double bPhaseVoltageHarmonics28;

    @JSONField(name="b_phase_voltage_harmonics_29")
    private Double bPhaseVoltageHarmonics29;

    @JSONField(name="b_phase_voltage_harmonics_30")
    private Double bPhaseVoltageHarmonics30;

    @JSONField(name="b_phase_voltage_harmonics_31")
    private Double bPhaseVoltageHarmonics31;

    @JSONField(name="c_phase_voltage_harmonics")
    private Double cPhaseVoltageHarmonics;

    private Double cPhaseVoltageHarmonics1;

    public void setCPhaseVoltageHarmonics(Double cPhaseVoltageHarmonics){
        this.cPhaseVoltageHarmonics = cPhaseVoltageHarmonics;
        this.cPhaseVoltageHarmonics1 = cPhaseVoltageHarmonics;
    }

    @JSONField(name="c_phase_voltage_harmonics_2")
    private Double cPhaseVoltageHarmonics2;

    @JSONField(name="c_phase_voltage_harmonics_3")
    private Double cPhaseVoltageHarmonics3;

    @JSONField(name="c_phase_voltage_harmonics_4")
    private Double cPhaseVoltageHarmonics4;

    @JSONField(name="c_phase_voltage_harmonics_5")
    private Double cPhaseVoltageHarmonics5;

    @JSONField(name="c_phase_voltage_harmonics_6")
    private Double cPhaseVoltageHarmonics6;

    @JSONField(name="c_phase_voltage_harmonics_7")
    private Double cPhaseVoltageHarmonics7;

    @JSONField(name="c_phase_voltage_harmonics_8")
    private Double cPhaseVoltageHarmonics8;

    @JSONField(name="c_phase_voltage_harmonics_9")
    private Double cPhaseVoltageHarmonics9;

    @JSONField(name="c_phase_voltage_harmonics_10")
    private Double cPhaseVoltageHarmonics10;

    @JSONField(name="c_phase_voltage_harmonics_11")
    private Double cPhaseVoltageHarmonics11;

    @JSONField(name="c_phase_voltage_harmonics_12")
    private Double cPhaseVoltageHarmonics12;

    @JSONField(name="c_phase_voltage_harmonics_13")
    private Double cPhaseVoltageHarmonics13;

    @JSONField(name="c_phase_voltage_harmonics_14")
    private Double cPhaseVoltageHarmonics14;

    @JSONField(name="c_phase_voltage_harmonics_15")
    private Double cPhaseVoltageHarmonics15;

    @JSONField(name="c_phase_voltage_harmonics_16")
    private Double cPhaseVoltageHarmonics16;

    @JSONField(name="c_phase_voltage_harmonics_17")
    private Double cPhaseVoltageHarmonics17;

    @JSONField(name="c_phase_voltage_harmonics_18")
    private Double cPhaseVoltageHarmonics18;

    @JSONField(name="c_phase_voltage_harmonics_19")
    private Double cPhaseVoltageHarmonics19;

    @JSONField(name="c_phase_voltage_harmonics_20")
    private Double cPhaseVoltageHarmonics20;

    @JSONField(name="c_phase_voltage_harmonics_21")
    private Double cPhaseVoltageHarmonics21;

    @JSONField(name="c_phase_voltage_harmonics_22")
    private Double cPhaseVoltageHarmonics22;

    @JSONField(name="c_phase_voltage_harmonics_23")
    private Double cPhaseVoltageHarmonics23;

    @JSONField(name="c_phase_voltage_harmonics_24")
    private Double cPhaseVoltageHarmonics24;

    @JSONField(name="c_phase_voltage_harmonics_25")
    private Double cPhaseVoltageHarmonics25;

    @JSONField(name="c_phase_voltage_harmonics_26")
    private Double cPhaseVoltageHarmonics26;

    @JSONField(name="c_phase_voltage_harmonics_27")
    private Double cPhaseVoltageHarmonics27;

    @JSONField(name="c_phase_voltage_harmonics_28")
    private Double cPhaseVoltageHarmonics28;

    @JSONField(name="c_phase_voltage_harmonics_29")
    private Double cPhaseVoltageHarmonics29;

    @JSONField(name="c_phase_voltage_harmonics_30")
    private Double cPhaseVoltageHarmonics30;

    @JSONField(name="c_phase_voltage_harmonics_31")
    private Double cPhaseVoltageHarmonics31;

    @JSONField(name="a_phase_current_harmonics")
    private Double aPhaseCurrentHarmonics;

    private Double aPhaseCurrentHarmonics1;

    public void setAPhaseCurrentHarmonics(Double aPhaseCurrentHarmonics){
        this.aPhaseCurrentHarmonics = aPhaseCurrentHarmonics;
        this.aPhaseCurrentHarmonics1 = aPhaseCurrentHarmonics;
    }

    @JSONField(name="a_phase_current_harmonics_2")
    private Double aPhaseCurrentHarmonics2;

    @JSONField(name="a_phase_current_harmonics_3")
    private Double aPhaseCurrentHarmonics3;

    @JSONField(name="a_phase_current_harmonics_4")
    private Double aPhaseCurrentHarmonics4;

    @JSONField(name="a_phase_current_harmonics_5")
    private Double aPhaseCurrentHarmonics5;

    @JSONField(name="a_phase_current_harmonics_6")
    private Double aPhaseCurrentHarmonics6;

    @JSONField(name="a_phase_current_harmonics_7")
    private Double aPhaseCurrentHarmonics7;

    @JSONField(name="a_phase_current_harmonics_8")
    private Double aPhaseCurrentHarmonics8;

    @JSONField(name="a_phase_current_harmonics_9")
    private Double aPhaseCurrentHarmonics9;

    @JSONField(name="a_phase_current_harmonics_10")
    private Double aPhaseCurrentHarmonics10;

    @JSONField(name="a_phase_current_harmonics_11")
    private Double aPhaseCurrentHarmonics11;

    @JSONField(name="a_phase_current_harmonics_12")
    private Double aPhaseCurrentHarmonics12;

    @JSONField(name="a_phase_current_harmonics_13")
    private Double aPhaseCurrentHarmonics13;

    @JSONField(name="a_phase_current_harmonics_14")
    private Double aPhaseCurrentHarmonics14;

    @JSONField(name="a_phase_current_harmonics_15")
    private Double aPhaseCurrentHarmonics15;

    @JSONField(name="a_phase_current_harmonics_16")
    private Double aPhaseCurrentHarmonics16;

    @JSONField(name="a_phase_current_harmonics_17")
    private Double aPhaseCurrentHarmonics17;

    @JSONField(name="a_phase_current_harmonics_18")
    private Double aPhaseCurrentHarmonics18;

    @JSONField(name="a_phase_current_harmonics_19")
    private Double aPhaseCurrentHarmonics19;

    @JSONField(name="a_phase_current_harmonics_20")
    private Double aPhaseCurrentHarmonics20;

    @JSONField(name="a_phase_current_harmonics_21")
    private Double aPhaseCurrentHarmonics21;

    @JSONField(name="a_phase_current_harmonics_22")
    private Double aPhaseCurrentHarmonics22;

    @JSONField(name="a_phase_current_harmonics_23")
    private Double aPhaseCurrentHarmonics23;

    @JSONField(name="a_phase_current_harmonics_24")
    private Double aPhaseCurrentHarmonics24;

    @JSONField(name="a_phase_current_harmonics_25")
    private Double aPhaseCurrentHarmonics25;

    @JSONField(name="a_phase_current_harmonics_26")
    private Double aPhaseCurrentHarmonics26;

    @JSONField(name="a_phase_current_harmonics_27")
    private Double aPhaseCurrentHarmonics27;

    @JSONField(name="a_phase_current_harmonics_28")
    private Double aPhaseCurrentHarmonics28;

    @JSONField(name="a_phase_current_harmonics_29")
    private Double aPhaseCurrentHarmonics29;

    @JSONField(name="a_phase_current_harmonics_30")
    private Double aPhaseCurrentHarmonics30;

    @JSONField(name="a_phase_current_harmonics_31")
    private Double aPhaseCurrentHarmonics31;

    @JSONField(name="b_phase_current_harmonics")
    private Double bPhaseCurrentHarmonics;

    private Double bPhaseCurrentHarmonics1;

    public void setBPhaseCurrentHarmonics(Double bPhaseCurrentHarmonics){
        this.bPhaseCurrentHarmonics = bPhaseCurrentHarmonics;
        this.bPhaseCurrentHarmonics1 = bPhaseCurrentHarmonics;
    }

    @JSONField(name="b_phase_current_harmonics_2")
    private Double bPhaseCurrentHarmonics2;

    @JSONField(name="b_phase_current_harmonics_3")
    private Double bPhaseCurrentHarmonics3;

    @JSONField(name="b_phase_current_harmonics_4")
    private Double bPhaseCurrentHarmonics4;

    @JSONField(name="b_phase_current_harmonics_5")
    private Double bPhaseCurrentHarmonics5;

    @JSONField(name="b_phase_current_harmonics_6")
    private Double bPhaseCurrentHarmonics6;

    @JSONField(name="b_phase_current_harmonics_7")
    private Double bPhaseCurrentHarmonics7;

    @JSONField(name="b_phase_current_harmonics_8")
    private Double bPhaseCurrentHarmonics8;

    @JSONField(name="b_phase_current_harmonics_9")
    private Double bPhaseCurrentHarmonics9;

    @JSONField(name="b_phase_current_harmonics_10")
    private Double bPhaseCurrentHarmonics10;

    @JSONField(name="b_phase_current_harmonics_11")
    private Double bPhaseCurrentHarmonics11;

    @JSONField(name="b_phase_current_harmonics_12")
    private Double bPhaseCurrentHarmonics12;

    @JSONField(name="b_phase_current_harmonics_13")
    private Double bPhaseCurrentHarmonics13;

    @JSONField(name="b_phase_current_harmonics_14")
    private Double bPhaseCurrentHarmonics14;

    @JSONField(name="b_phase_current_harmonics_15")
    private Double bPhaseCurrentHarmonics15;

    @JSONField(name="b_phase_current_harmonics_16")
    private Double bPhaseCurrentHarmonics16;

    @JSONField(name="b_phase_current_harmonics_17")
    private Double bPhaseCurrentHarmonics17;

    @JSONField(name="b_phase_current_harmonics_18")
    private Double bPhaseCurrentHarmonics18;

    @JSONField(name="b_phase_current_harmonics_19")
    private Double bPhaseCurrentHarmonics19;

    @JSONField(name="b_phase_current_harmonics_20")
    private Double bPhaseCurrentHarmonics20;

    @JSONField(name="b_phase_current_harmonics_21")
    private Double bPhaseCurrentHarmonics21;

    @JSONField(name="b_phase_current_harmonics_22")
    private Double bPhaseCurrentHarmonics22;

    @JSONField(name="b_phase_current_harmonics_23")
    private Double bPhaseCurrentHarmonics23;

    @JSONField(name="b_phase_current_harmonics_24")
    private Double bPhaseCurrentHarmonics24;

    @JSONField(name="b_phase_current_harmonics_25")
    private Double bPhaseCurrentHarmonics25;

    @JSONField(name="b_phase_current_harmonics_26")
    private Double bPhaseCurrentHarmonics26;

    @JSONField(name="b_phase_current_harmonics_27")
    private Double bPhaseCurrentHarmonics27;

    @JSONField(name="b_phase_current_harmonics_28")
    private Double bPhaseCurrentHarmonics28;

    @JSONField(name="b_phase_current_harmonics_29")
    private Double bPhaseCurrentHarmonics29;

    @JSONField(name="b_phase_current_harmonics_30")
    private Double bPhaseCurrentHarmonics30;

    @JSONField(name="b_phase_current_harmonics_31")
    private Double bPhaseCurrentHarmonics31;

    @JSONField(name="c_phase_current_harmonics")
    private Double cPhaseCurrentHarmonics;

    private Double cPhaseCurrentHarmonics1;

    public void setCPhaseCurrentHarmonics(Double cPhaseCurrentHarmonics){
        this.cPhaseCurrentHarmonics = cPhaseCurrentHarmonics;
        this.cPhaseCurrentHarmonics1 = cPhaseCurrentHarmonics;
    }

    @JSONField(name="c_phase_current_harmonics_2")
    private Double cPhaseCurrentHarmonics2;

    @JSONField(name="c_phase_current_harmonics_3")
    private Double cPhaseCurrentHarmonics3;

    @JSONField(name="c_phase_current_harmonics_4")
    private Double cPhaseCurrentHarmonics4;

    @JSONField(name="c_phase_current_harmonics_5")
    private Double cPhaseCurrentHarmonics5;

    @JSONField(name="c_phase_current_harmonics_6")
    private Double cPhaseCurrentHarmonics6;

    @JSONField(name="c_phase_current_harmonics_7")
    private Double cPhaseCurrentHarmonics7;

    @JSONField(name="c_phase_current_harmonics_8")
    private Double cPhaseCurrentHarmonics8;

    @JSONField(name="c_phase_current_harmonics_9")
    private Double cPhaseCurrentHarmonics9;

    @JSONField(name="c_phase_current_harmonics_10")
    private Double cPhaseCurrentHarmonics10;

    @JSONField(name="c_phase_current_harmonics_11")
    private Double cPhaseCurrentHarmonics11;

    @JSONField(name="c_phase_current_harmonics_12")
    private Double cPhaseCurrentHarmonics12;

    @JSONField(name="c_phase_current_harmonics_13")
    private Double cPhaseCurrentHarmonics13;

    @JSONField(name="c_phase_current_harmonics_14")
    private Double cPhaseCurrentHarmonics14;

    @JSONField(name="c_phase_current_harmonics_15")
    private Double cPhaseCurrentHarmonics15;

    @JSONField(name="c_phase_current_harmonics_16")
    private Double cPhaseCurrentHarmonics16;

    @JSONField(name="c_phase_current_harmonics_17")
    private Double cPhaseCurrentHarmonics17;

    @JSONField(name="c_phase_current_harmonics_18")
    private Double cPhaseCurrentHarmonics18;

    @JSONField(name="c_phase_current_harmonics_19")
    private Double cPhaseCurrentHarmonics19;

    @JSONField(name="c_phase_current_harmonics_20")
    private Double cPhaseCurrentHarmonics20;

    @JSONField(name="c_phase_current_harmonics_21")
    private Double cPhaseCurrentHarmonics21;

    @JSONField(name="c_phase_current_harmonics_22")
    private Double cPhaseCurrentHarmonics22;

    @JSONField(name="c_phase_current_harmonics_23")
    private Double cPhaseCurrentHarmonics23;

    @JSONField(name="c_phase_current_harmonics_24")
    private Double cPhaseCurrentHarmonics24;

    @JSONField(name="c_phase_current_harmonics_25")
    private Double cPhaseCurrentHarmonics25;

    @JSONField(name="c_phase_current_harmonics_26")
    private Double cPhaseCurrentHarmonics26;

    @JSONField(name="c_phase_current_harmonics_27")
    private Double cPhaseCurrentHarmonics27;

    @JSONField(name="c_phase_current_harmonics_28")
    private Double cPhaseCurrentHarmonics28;

    @JSONField(name="c_phase_current_harmonics_29")
    private Double cPhaseCurrentHarmonics29;

    @JSONField(name="c_phase_current_harmonics_30")
    private Double cPhaseCurrentHarmonics30;

    @JSONField(name="c_phase_current_harmonics_31")
    private Double cPhaseCurrentHarmonics31;


}


