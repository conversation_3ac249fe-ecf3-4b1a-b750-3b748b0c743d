package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionRoom;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 配电房 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionRoomMapper extends BaseMapper<DistributionRoom> {

    List<DistributionRoom> getDistributionRooms(@Param("distributionRoom") DistributionRoom distributionRoom, @Param("spacesIds") List<String> spacesIds);

    DistributionRoom getDistributionRoom(@Param("id") String id);

    IPage<DistributionRoom> getDistributionRooms(@Param("page") Page page, @Param("distributionRoom") DistributionRoom distributionRoom, @Param("spacesIds") List<String> spacesIds);

    List<Device> getRefdevices(@Param("id") String id);
}
