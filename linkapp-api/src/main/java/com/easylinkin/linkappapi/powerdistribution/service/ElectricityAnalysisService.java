package com.easylinkin.linkappapi.powerdistribution.service;

import com.easylinkin.linkappapi.elasticsearch.entity.EsQuerymodel;
import com.easylinkin.linkappapi.powerdistribution.entity.ElectricityAnalysisParam;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public interface ElectricityAnalysisService {

    Map<String,Map<String,Object>> energyStatisticsByDeviceCodeEs(EsQuerymodel esQuerymodel);

    /***
     *  用电分析
     * @param electricityAnalysisParam
     * @return
     * @throws IOException
     */
    Map<String,Object>  energySameProcessing(ElectricityAnalysisParam electricityAnalysisParam) throws IOException;
}
