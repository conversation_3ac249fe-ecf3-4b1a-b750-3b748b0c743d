package com.easylinkin.linkappapi.powerdistribution.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.alarm.entity.Alarm;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface DistributionCabinetAlarmInfoService extends IService<DistributionCabinetAlarmInfo>{

    /***
     * 触发告警
     * @param device
     */
    void triggerAlarm(Device device);

    /***
     * 分页
     * @param page
     * @param distributionCabinetAlarmInfo
     * @return
     */
    IPage<DistributionCabinetAlarmInfo> getAlarmPage(Page page, DistributionCabinetAlarmInfo distributionCabinetAlarmInfo);

    /**
     * 导出
     * @param alarm
     * @param request
     * @param response
     */
    void exportData(DistributionCabinetAlarmInfo alarm, HttpServletRequest request, HttpServletResponse response);

    /***
     * 批量告警处理
     * @param distributionCabinetAlarmInfo
     */
    void  alarmProcessBatch(DistributionCabinetAlarmInfo distributionCabinetAlarmInfo);

    /***
     * 告警详情
     * @param distributionCabinetAlarmInfo
     * @return
     */
    Alarm getDetail(DistributionCabinetAlarmInfo distributionCabinetAlarmInfo);
}
