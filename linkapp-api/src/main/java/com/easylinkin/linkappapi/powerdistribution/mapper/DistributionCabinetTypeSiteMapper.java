package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetTypeSite;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 配电柜类型关联位置信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetTypeSiteMapper extends BaseMapper<DistributionCabinetTypeSite> {

    List<DistributionCabinetTypeSite> getDistributionCabinetTypeSites(@Param("distributionCabinetTypeSite") DistributionCabinetTypeSite distributionCabinetTypeSite);

    IPage<DistributionCabinetTypeSite> getDistributionCabinetTypeSites(@Param("page") Page page, @Param("distributionCabinetTypeSite") DistributionCabinetTypeSite distributionCabinetTypeSite);

    DistributionCabinetTypeSite getDistributionCabinetTypeSite(@Param("id") String id);
    
    List<DistributionCabinetTypeSite> getDistributionCabinetTypeSiteByTenant(@Param("tenantId")String tenantId,@Param("deviceUnitCode")String deviceUnitCode);
}
