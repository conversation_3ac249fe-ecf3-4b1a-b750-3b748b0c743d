package com.easylinkin.linkappapi.powerdistribution.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPrice;

public interface DistributionElectricPriceMapper extends BaseMapper<DistributionElectricPrice> {
    DistributionElectricPrice findAllById(@Param("id") Long id);

    DistributionElectricPrice findAllByCabinetId(@Param("cabinetId") String cabinetId);
}