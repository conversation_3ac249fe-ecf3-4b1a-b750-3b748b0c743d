package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.alarm.entity.AlarmPersonContact;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCompanyAlarmContactMid;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Set;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/15 9:54
 */
public interface DistributionCompanyAlarmContactMidMapper extends BaseMapper<DistributionCompanyAlarmContactMid> {

    List<AlarmPersonContact> findAllAlarmContactInCompanyId(@Param("companyIds") Set<Integer> companyIds);
}
