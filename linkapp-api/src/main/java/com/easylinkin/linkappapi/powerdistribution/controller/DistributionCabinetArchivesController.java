package com.easylinkin.linkappapi.powerdistribution.controller;

import com.easylinkin.linkappapi.powerdistribution.entity.DistributionAreaParam;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmConfig;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetArchives;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetFile;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetAlarmConfigService;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetAlarmInfoService;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetArchivesService;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetFileService;
import com.easylinkin.linkappapi.space.entity.LinkappArea;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: linkapp-group-cloud
 * @description: 设备档案信息
 * @author: chenkaixuan
 * @create: 2021-09-14 11:41
 */
@RestController
@RequestMapping("/distributionCabinetArchives/basic")
public class DistributionCabinetArchivesController {
    @Autowired
    DistributionCabinetArchivesService service;
    @Autowired
    DistributionCabinetFileService fileService;
    @Autowired
    DistributionCabinetAlarmConfigService distributionCabinetAlarmConfigService;
    @Autowired
    DistributionCabinetAlarmInfoService distributionCabinetAlarmInfoService;

    /***
     * 查询区域树列表
     * @param distributionAreaParam
     * @return
     */
    @RequestMapping("selectLinkappAreaTreeList")
    public RestMessage selectLinkappAreaTreeList(@RequestBody DistributionAreaParam distributionAreaParam){
        return RestBuilders.successBuilder().data(service.selectLinkappAreaTreeList(distributionAreaParam)).build();
    }

    /***
     * 配置指标
     * @param distributionCabinetAlarmConfig
     * @return
     */
    @RequestMapping("configNorm")
    public RestMessage configNorm(@Validated @RequestBody  DistributionCabinetAlarmConfig distributionCabinetAlarmConfig){
        distributionCabinetAlarmConfigService.saveOrUpdateEntity(distributionCabinetAlarmConfig);
        return RestBuilders.successBuilder().build();
    }

    /***
     * 保存档案信息
     * @param distributionCabinetArchives
     * @return
     */
    @RequestMapping("saveOrUpdate")
    public RestMessage saveOrUpdateEntity(@RequestBody DistributionCabinetArchives distributionCabinetArchives){
        service.saveOrUpdateEntity(distributionCabinetArchives);
        return RestBuilders.successBuilder().build();
    }

    /***
     * 查询设备档案
     * @param distributionCabinetArchives
     * @return
     */
    @RequestMapping("getDistributionCabinetArchives")
    public RestMessage getDistributionCabinetArchives(@RequestBody DistributionCabinetArchives distributionCabinetArchives){
        return RestBuilders.successBuilder().data(service.getDistributionCabinetArchives(distributionCabinetArchives)).build();
    }

    /***
     * 文档信息-文件上传
     * @param list
     * @return
     */
    @RequestMapping("fileUpload")
    public RestMessage saveOrUpdateEntity(@RequestBody List<DistributionCabinetFile> list){
       fileService.saveBatchEntity(list);
        return RestBuilders.successBuilder().build();
    }


}
