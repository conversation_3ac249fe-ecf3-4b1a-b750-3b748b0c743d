package com.easylinkin.linkappapi.powerdistribution.service;

import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmConfig;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
public interface DistributionCabinetAlarmConfigService extends IService<DistributionCabinetAlarmConfig>{


    int batchInsert(List<DistributionCabinetAlarmConfig> list);

    void saveOrUpdateEntity(DistributionCabinetAlarmConfig distributionCabinetAlarmConfig);

    /**
     *获取配电柜配置信息
     * @param distributionCabinetAlarmConfig
     * @return
     */
    DistributionCabinetAlarmConfig getDistributionCabinetAlarmConfigByCondition(DistributionCabinetAlarmConfig distributionCabinetAlarmConfig);

    boolean loadindex(String deviceCode);

    boolean aoib(String deviceCode);
}
