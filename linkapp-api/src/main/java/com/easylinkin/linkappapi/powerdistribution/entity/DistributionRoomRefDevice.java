package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配电房关联设备
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("distribution_room_ref_device")
public class DistributionRoomRefDevice extends Model<DistributionRoomRefDevice> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 配电房id
     */
    @TableField("distribution_room_id")
    private String distributionRoomId;

    /**
     * 设备编号
     */
    @TableField("device_code")
    private String deviceCode;

    /**
     * 排序编号 default 0 从0 开始
     */
    @TableField("sort_no")
    private Integer sortNo;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
