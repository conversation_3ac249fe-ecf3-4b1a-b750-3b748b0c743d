package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配电柜类型
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("distribution_cabinet_type")
public class DistributionCabinetType extends Model<DistributionCabinetType> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 规则联动配置名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 创建人id
     */
    @TableField("creator")
    private String creator;

    /**
     * 修改人id
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 配电柜类型位置信息
     */
    @TableField(exist = false)
    private List<DistributionCabinetTypeSite> distributionCabinetTypeSiteList;

    /**
     * 勾选的ids
     */
    @TableField(exist = false)
    private Set<String> ids;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
