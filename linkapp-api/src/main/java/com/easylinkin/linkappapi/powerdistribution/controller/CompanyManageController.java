package com.easylinkin.linkappapi.powerdistribution.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.powerdistribution.entity.CompanyManageBean;
import com.easylinkin.linkappapi.powerdistribution.service.CompanyManageService;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.util.List;

/**
 * class info :配电管理：企业管理
 *
 * <AUTHOR>
 * @date 2021/9/11 11:35
 */
@RestController
@RequestMapping("/companyManage")
public class CompanyManageController {

    @Resource
    private CompanyManageService companyManageService;

    @PostMapping("addCompany")
    public RestMessage addCompany(@RequestBody CompanyManageBean CompanyManageBean) {
        String result = companyManageService.saveCompanyInfo(CompanyManageBean);
        if(result != null){
            return RestBuilders.failureBuilder().message(result).build();
        }
        return RestBuilders.successBuilder().message("success").build();
    }

    @PostMapping("updateCompany")
    public RestMessage updateCompany(@RequestBody CompanyManageBean companyManageBean) {
        String result = companyManageService.updateCompanyInfo(companyManageBean);
        if(result != null){
            return RestBuilders.failureBuilder().message(result).build();
        }
        return RestBuilders.successBuilder().message("success").build();
    }

    @PostMapping("deleteCompany")
    public RestMessage deleteCompany(@RequestBody CompanyManageBean companyManageBean) {
        Assert.notNull(companyManageBean, "companyManageBean 不能为空");
        Assert.notNull(companyManageBean.getCompanyIds(), "companyIds 不能为空");
        companyManageService.deleteCompanyInfo(companyManageBean.getCompanyIds());
        return RestBuilders.successBuilder().message("success").build();
    }

    @PostMapping("getCompanyPage")
    public RestMessage getCompanyPage(@RequestBody RequestModel<CompanyManageBean> requestModel) {
        Assert.notNull(requestModel, "requestModel 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        IPage companyPage = companyManageService.getCompanyPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(companyPage).build();
    }

    @PostMapping("getCompanyList")
    public RestMessage getCompanyList(@RequestBody CompanyManageBean companyManageBean) {
        Assert.notNull(companyManageBean, "companyManageBean 不能为空");
        List<CompanyManageBean> companyList = companyManageService.getCompanyList(companyManageBean);
        return RestBuilders.successBuilder().data(companyList).build();
    }






}
