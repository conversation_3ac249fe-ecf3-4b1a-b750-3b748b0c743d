package com.easylinkin.linkappapi.powerdistribution.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairRecord;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetRepairRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @program: linkapp-group-cloud
 * @description: 设备档案-检修记录
 * @author: chenkaixuan
 * @create: 2021-09-16 16:41
 */
@RestController
@RequestMapping("/distributionCabinetArchives/repair")
public class DistributionCabinetRepairRecordController {
    @Autowired
    DistributionCabinetRepairRecordService distributionCabinetRepairRecordService;

    @PostMapping("getPage")
    public RestMessage getDistributionCabinetsPage(@RequestBody RequestModel<DistributionCabinetRepairRecord> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DistributionCabinetRepairRecord> record = distributionCabinetRepairRecordService.page(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    @PostMapping("saveOrUpdate")
    public RestMessage getDistributionCabinetsPage(@RequestBody DistributionCabinetRepairRecord distributionCabinetRepairRecord) {
        distributionCabinetRepairRecordService.saveOrUpdateEntity(distributionCabinetRepairRecord);
        return RestBuilders.successBuilder().build();
    }

    @PostMapping("exportData")
    public void exportData(@RequestBody DistributionCabinetRepairRecord distributionCabinetRepairRecord, HttpServletRequest request, HttpServletResponse response) {
        distributionCabinetRepairRecordService.exportData(distributionCabinetRepairRecord, request, response);
    }

    @PostMapping("removeByIds")
    public RestMessage removeByIds(@RequestBody DistributionCabinetRepairRecord distributionCabinetRepairRecord){
        distributionCabinetRepairRecordService.removeByIds(distributionCabinetRepairRecord);
        return RestBuilders.successBuilder().build();
    }

    @PostMapping("findByIdOrderByCreateTimeDesc")
    public RestMessage findByIdOrderByCreateTimeDesc(@RequestBody DistributionCabinetRepairRecord distributionCabinetRepairRecord){
        return RestBuilders.successBuilder().data(distributionCabinetRepairRecordService.findByIdOrderByCreateTimeDesc(distributionCabinetRepairRecord)).build();
    }
}
