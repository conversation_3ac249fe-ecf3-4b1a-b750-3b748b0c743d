package com.easylinkin.linkappapi.powerdistribution.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetType;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetTypeService;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 配电柜类型 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@RestController
@RequestMapping("/distributionCabinetType")
public class DistributionCabinetTypeController {


    @Resource
    private DistributionCabinetTypeService service;


    @PostMapping("add")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_TYPE, desc = "新增配电柜类型")
    public RestMessage add(@RequestBody @Valid DistributionCabinetType distributionCabinetType) {
        Assert.notNull(distributionCabinetType, "参数为空！");
        Assert.isTrue(ObjectUtils.isEmpty(distributionCabinetType.getId()), "请检查传参,新增时id应为空！");
        service.add(distributionCabinetType);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("get")
    public RestMessage getDistributionCabinetTypes(@RequestBody DistributionCabinetType distributionCabinetType) {
        Assert.notNull(distributionCabinetType, "distributionCabinetType 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinetTypes(distributionCabinetType)).build();
    }


    @PostMapping("getPage")
    public RestMessage getDistributionCabinetTypesPage(@RequestBody RequestModel<DistributionCabinetType> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DistributionCabinetType> record = service.getDistributionCabinetTypes(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    @PostMapping("getById")
    public RestMessage getById(@RequestParam("id") String id) {
        Assert.hasLength(id, "id 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinetType(id)).build();
    }

    @PostMapping("update")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_TYPE, desc = "修改配电柜类型")
    public RestMessage update(@RequestBody DistributionCabinetType distributionCabinetType) {
        service.updateDistributionCabinetType(distributionCabinetType);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("deleteBatch")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_TYPE, desc = "批量删除配电柜类型")
    public RestMessage deleteBatch(@RequestBody List<DistributionCabinetType> distributionCabinetTypeList) {
        Assert.notEmpty(distributionCabinetTypeList, "参数为空");
        service.deleteBatch(distributionCabinetTypeList);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("exportData")
    @ApiOperation("导出")
    public void exportData(@RequestBody DistributionCabinetType distributionCabinetType, HttpServletRequest request, HttpServletResponse response) {
        service.exportData(distributionCabinetType, request, response);
    }

    @ApiOperation("导入")
    @PostMapping("importData")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_TYPE, desc = "导入配电柜类型")
    public RestMessage importData(@RequestParam(value = "file") MultipartFile file) {
        Assert.notNull(file, "文件不能为空");
        Set errors = service.importData(file);
        if(errors.isEmpty()) {
            return RestBuilders.successBuilder().data("导入成功").build();
        }else {
            String errorMsg = "";
            for(Object obj : errors) {
                errorMsg = errorMsg +"第" + obj.toString() + "行  ";
            }
            errorMsg = errorMsg + "数据错误,请核对后再提交。";
            return RestBuilders.successBuilder().data(errorMsg).build();
        }
    }

}

