package com.easylinkin.linkappapi.powerdistribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetTypeSite;
import java.util.List;

/**
 * <p>
 * 配电柜类型关联位置信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetTypeSiteService extends IService<DistributionCabinetTypeSite> {


    void add(DistributionCabinetTypeSite distributionCabinetTypeSite);


    /**
     * 批量删除
     */
    void deleteBatch(List<DistributionCabinetTypeSite> distributionCabinetTypeSiteList);


    /**
     * 修改
     */
    void updateDistributionCabinetTypeSite(DistributionCabinetTypeSite distributionCabinetTypeSite);


    /**
     * 根据条件查询配电房所有
     */
    List<DistributionCabinetTypeSite> getDistributionCabinetTypeSites(DistributionCabinetTypeSite distributionCabinetTypeSite);


    /**
     * 根据条件查询配电房 分页
     *
     * @param page 分页参数
     * @param distributionCabinetTypeSite 查询参数vo
     * @return 查询结果
     */
    IPage<DistributionCabinetTypeSite> getDistributionCabinetTypeSites(Page page, DistributionCabinetTypeSite distributionCabinetTypeSite);


    /**
     * 单条查询
     *
     * @param id 主键key
     * @return 查询结果
     */
    DistributionCabinetTypeSite getDistributionCabinetTypeSite(String id);
    
    List<DistributionCabinetTypeSite> getDistributionCabinetTypeSiteByTenant(String tenantId,String deviceUnitCode);

}
