package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 配电柜关联设备 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetRefDeviceMapper extends BaseMapper<DistributionCabinetRefDevice> {

    List<DistributionCabinetRefDevice> getCabinetRefDevicesWithConfigurationAndExpressions(@Param("device") Device device);
}
