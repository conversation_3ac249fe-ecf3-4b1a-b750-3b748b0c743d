package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfigurationExpression;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 组态表达式 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetConfigurationExpressionMapper extends BaseMapper<DistributionCabinetConfigurationExpression> {

    List<DistributionCabinetConfigurationExpression> getDistributionCabinetConfigurationExpressions(@Param("distributionCabinetConfigurationExpression") DistributionCabinetConfigurationExpression distributionCabinetConfigurationExpression);

    List<DistributionCabinetConfigurationExpression> getDistributionCabinetConfigurationExpressionsByTenant(@Param("tenantId")String tenantId,@Param("deviceUnitCode")String deviceUnitCode);
}
