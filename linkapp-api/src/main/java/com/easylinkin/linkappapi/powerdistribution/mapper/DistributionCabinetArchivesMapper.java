package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetArchives;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetArchivesVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DistributionCabinetArchivesMapper extends BaseMapper<DistributionCabinetArchives> {

    DistributionCabinetArchivesVo getDistributionCabinetArchives(@Param("distributionCabinetArchives") DistributionCabinetArchives distributionCabinetArchives);

    List<DistributionCabinetArchives> findAllByCabinetId(@Param("cabinetId")String cabinetId);

    /***
     * 查询设备是否需要设置不平衡度指标
     * @param deviceCode
     * @return
     */
    Integer getDeviceAttrIsExistAoib(@Param("deviceCode")String deviceCode);

    /***
     * 查询设备是否需要设置负荷指标
     * @param deviceCode
     * @return
     */
    Integer getDeviceAttrIsExistLoad(@Param("deviceCode")String deviceCode);
}