package com.easylinkin.linkappapi.powerdistribution.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionRoom;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionRoomService;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 配电房 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@RestController
@RequestMapping("/distributionRoom")
public class DistributionRoomController {


    @Resource
    private DistributionRoomService service;


    @PostMapping("add")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_ROOM, desc = "新增配电房")
    public RestMessage add(@RequestBody @Valid DistributionRoom distributionRoom) {
        service.add(distributionRoom);
        return RestBuilders.successBuilder().build();
    }

    @PostMapping("setDetailConfig")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_ROOM, desc = "配置配电房")
    public RestMessage setDetailConfig(@RequestBody @Valid DistributionRoom distributionRoom) {
        service.setDetailConfig(distributionRoom);
        return RestBuilders.successBuilder().build();
    }

    /**
     * 配电房配置之保存排序
     * @param distributionRoom
     * @return
     */
    @PostMapping("setDetailConfigDistributionCabinet")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_ROOM, desc = "配置配电房")
    public RestMessage setDetailConfigDistributionCabinet(@RequestBody @Valid DistributionRoom distributionRoom) {
        service.setDetailConfigDistributionCabinet(distributionRoom);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("get")
    public RestMessage getDistributionRooms(@RequestBody DistributionRoom distributionRoom) {
        Assert.notNull(distributionRoom, "distributionRoom 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionRooms(distributionRoom)).build();
    }


    @PostMapping("getPage")
    public RestMessage getDistributionRoomsPage(@RequestBody RequestModel<DistributionRoom> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DistributionRoom> record = service.getDistributionRooms(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    @PostMapping("getById")
    public RestMessage getById(@RequestParam("id") String id) {
        Assert.hasLength(id, "id 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionRoom(id)).build();
    }


    @PostMapping("getMonitor")
    public RestMessage getMonitor(@RequestParam("id") String id) {
        Assert.hasLength(id, "id 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionRoomMonitor(id)).build();
    }

    @PostMapping("getRefdevices")
    public RestMessage getRefdevices(@RequestBody DistributionRoom distributionRoom) {
        Assert.notNull(distributionRoom, "参数为空");
        Assert.hasLength(distributionRoom.getId(), "id 不能为空");
        return RestBuilders.successBuilder().data(service.getRefdevices(distributionRoom.getId())).build();
    }

    @PostMapping("update")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_ROOM, desc = "修改配电房")
    public RestMessage update(@RequestBody DistributionRoom distributionRoom) {
        service.updateDistributionRoom(distributionRoom);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("deleteBatch")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_ROOM, desc = "批量删除配电房")
    public RestMessage deleteBatch(@RequestBody List<DistributionRoom> distributionRoomList) {
        Assert.notEmpty(distributionRoomList, "参数为空");
        service.deleteBatch(distributionRoomList);
        return RestBuilders.successBuilder().build();
    }


}
