package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 组态表达式
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("distribution_cabinet_configuration_expression")
public class DistributionCabinetConfigurationExpression extends Model<DistributionCabinetConfigurationExpression> {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    /**
     * 组态id
     */
    @TableField("distribution_cabinet_configuration_id")
    private String distributionCabinetConfigurationId;

    /**
     * 设备型号code
     */
    @TableField("device_unit_code")
    private String deviceUnitCode;

    /**
     * 设备型号版本号
     */
    @TableField("device_unit_version")
    private String deviceUnitVersion;

    /**
     * 设备属性标志符
     */
    @TableField("device_attribute_identifier")
    private String deviceAttributeIdentifier;

    @TableField(exist = false)
    private DeviceAttribute deviceAttribute;

    /**
     * 父属性属性标志符
     */
    @TableField("device_attribute_parent_identifier")
    private String deviceAttributeParentIdentifier;

    /**
     * 算术符
     */
    @TableField("calculate_sign")
    private String calculateSign;

    /**
     * 数值
     */
    @TableField("value")
    private String value;

    /**
     * 排序
     */
    @TableField("sort_no")
    private Integer sortNo;

    /**
     * 逻辑运算符（与、或）
     */
    @TableField("logic_code")
    private String logicCode;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
