package com.easylinkin.linkappapi.powerdistribution.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetService;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 配电柜 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@RestController
@RequestMapping("/distributionCabinet")
public class DistributionCabinetController {


    @Resource
    private DistributionCabinetService service;


    @PostMapping("add")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET, desc = "新增配电柜")
    public RestMessage add(@RequestBody @Valid DistributionCabinet distributionCabinet) {
        service.add(distributionCabinet);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("get")
    public RestMessage getDistributionCabinets(@RequestBody DistributionCabinet distributionCabinet) {
        Assert.notNull(distributionCabinet, "distributionCabinet 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinets(distributionCabinet)).build();
    }


    @PostMapping("getPage")
    public RestMessage getDistributionCabinetsPage(@RequestBody RequestModel<DistributionCabinet> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DistributionCabinet> record = service.getDistributionCabinets(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    @PostMapping("getById")
    public RestMessage getById(@RequestParam("id") String id) {
        Assert.hasLength(id, "id 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinet(id)).build();
    }

    @PostMapping("getDistributionCabinetDetail")
    public RestMessage getDistributionCabinetDetail(@RequestParam("id") String id) {
        Assert.hasLength(id, "id 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinetDetail(id)).build();
    }

    @PostMapping("update")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET, desc = "修改配电柜")
    public RestMessage update(@RequestBody DistributionCabinet distributionCabinet) {
        service.updateDistributionCabinet(distributionCabinet);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("deleteBatch")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET, desc = "批量删除配电柜")
    public RestMessage deleteBatch(@RequestBody List<DistributionCabinet> distributionCabinetList) {
        Assert.notEmpty(distributionCabinetList, "参数为空");
        service.deleteBatch(distributionCabinetList);
        return RestBuilders.successBuilder().build();
    }


}

