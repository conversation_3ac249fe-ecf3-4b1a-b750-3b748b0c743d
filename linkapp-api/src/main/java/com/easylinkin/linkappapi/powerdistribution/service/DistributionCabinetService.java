package com.easylinkin.linkappapi.powerdistribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinet;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 配电柜 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetService extends IService<DistributionCabinet> {


    void add(DistributionCabinet distributionCabinet);


    /**
     * 批量删除
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteBatch(List<DistributionCabinet> distributionCabinetList);


    /**
     * 修改
     */
    @Transactional(rollbackFor = Exception.class)
    void updateDistributionCabinet(DistributionCabinet distributionCabinet);


    /**
     * 根据条件查询配电房所有
     */
    List<DistributionCabinet> getDistributionCabinets(DistributionCabinet distributionCabinet);


    /**
     * 根据条件查询配电房 分页
     *
     * @param page 分页参数
     * @param distributionCabinet 查询参数vo
     * @return 查询结果
     */
    IPage<DistributionCabinet> getDistributionCabinets(Page page, DistributionCabinet distributionCabinet);


    /**
     * 单条查询
     *
     * @param id 主键key
     * @return 查询结果
     */
    DistributionCabinet getDistributionCabinet(String id);


    /**
     * 查询配电柜以及组态
     * @param distributionCabinet 配电柜 vo
     * @return
     */
    List<DistributionCabinet> getRealTimeDistributionCabinetWithStatus(DistributionCabinet distributionCabinet);

    /**
     * 配电柜详情查看
     *
     * @param id key
     * @return 配电柜详情 包括组态信息以及 设备信息
     */
    DistributionCabinet getDistributionCabinetDetail(String id);
}
