package com.easylinkin.linkappapi.powerdistribution.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfiguration;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetConfigurationService;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 配电柜类型组态 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@RestController
@RequestMapping("/distributionCabinetConfiguration")
public class DistributionCabinetConfigurationController {


    @Resource
    private DistributionCabinetConfigurationService service;


    @PostMapping("add")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_CONFIGURATION, desc = "新增配电柜类型组态")
    public RestMessage add(@RequestBody @Valid DistributionCabinetConfiguration distributionCabinetConfiguration) {
        service.add(distributionCabinetConfiguration);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("get")
    public RestMessage getDistributionCabinetConfigurations(@RequestBody DistributionCabinetConfiguration distributionCabinetConfiguration) {
        Assert.notNull(distributionCabinetConfiguration, "distributionCabinetConfiguration 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinetConfigurations(distributionCabinetConfiguration)).build();
    }

    @PostMapping("getDistributionCabinetConfigurationWithExpressions")
    public RestMessage getDistributionCabinetConfigurationWithExpressions(@RequestBody DistributionCabinetConfiguration distributionCabinetConfiguration) {
        Assert.notNull(distributionCabinetConfiguration, "distributionCabinetConfiguration 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinetConfigurationWithExpressions(distributionCabinetConfiguration)).build();
    }


    @PostMapping("getPage")
    public RestMessage getDistributionCabinetConfigurationsPage(@RequestBody RequestModel<DistributionCabinetConfiguration> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DistributionCabinetConfiguration> record = service.getDistributionCabinetConfigurations(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    @PostMapping("getById")
    public RestMessage getById(@RequestParam("id") String id) {
        Assert.hasLength(id, "id 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinetConfiguration(id)).build();
    }

    @PostMapping("update")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_CONFIGURATION, desc = "修改配电柜类型组态")
    public RestMessage update(@RequestBody DistributionCabinetConfiguration distributionCabinetConfiguration) {
        service.updateDistributionCabinetConfiguration(distributionCabinetConfiguration);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("deleteBatch")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_CONFIGURATION, desc = "批量删除配电柜类型组态")
    public RestMessage deleteBatch(@RequestBody List<DistributionCabinetConfiguration> distributionCabinetConfigurationList) {
        Assert.notEmpty(distributionCabinetConfigurationList, "参数为空");
        service.deleteBatch(distributionCabinetConfigurationList);
        return RestBuilders.successBuilder().build();
    }

//    /**
//     * 刷旧数据 组编号用 一次性接口
//     */
//    @PostMapping("flashOldGroupNumberSortDataGlobal")
//    @CommonOperateLogAnnotate(module = LogModule.DistributionCabinetConfiguration, desc = "flashOldGroupNumberSortDataGlobal")
//    public RestMessage flashOldGroupNumberSortDataGlobal() {
//        service.flashOldGroupNumberSortDataGlobal();
//        return RestBuilders.successBuilder().build();
//    }


}

