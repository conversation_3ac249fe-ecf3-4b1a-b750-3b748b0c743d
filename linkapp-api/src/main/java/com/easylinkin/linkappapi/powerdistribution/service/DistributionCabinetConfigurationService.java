package com.easylinkin.linkappapi.powerdistribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfiguration;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 配电柜类型组态 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetConfigurationService extends IService<DistributionCabinetConfiguration> {


    @Transactional(rollbackFor = Exception.class)
    void add(DistributionCabinetConfiguration distributionCabinetConfiguration);


    /**
     * 批量删除
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteBatch(List<DistributionCabinetConfiguration> distributionCabinetConfigurationList);


    /**
     * 修改
     */
    @Transactional(rollbackFor = Exception.class)
    void updateDistributionCabinetConfiguration(DistributionCabinetConfiguration distributionCabinetConfiguration);


    /**
     * 根据条件查询配电房所有
     */
    List<DistributionCabinetConfiguration> getDistributionCabinetConfigurations(DistributionCabinetConfiguration distributionCabinetConfiguration);


    List<DistributionCabinetConfiguration> getDistributionCabinetConfigurationWithExpressions(DistributionCabinetConfiguration distributionCabinetConfiguration);

    /**
     * 根据条件查询配电房 分页
     *
     * @param page 分页参数
     * @param distributionCabinetConfiguration 查询参数vo
     * @return 查询结果
     */
    IPage<DistributionCabinetConfiguration> getDistributionCabinetConfigurations(Page page, DistributionCabinetConfiguration distributionCabinetConfiguration);


    /**
     * 单条查询
     *
     * @param id 主键key
     * @return 查询结果
     */
    DistributionCabinetConfiguration getDistributionCabinetConfiguration(String id);

    /**
     * 刷数据用 重刷所有的组编号 根据创建时间排序刷顺序
     * 范围：所有租户的数据
     */
    void flashOldGroupNumberSortDataGlobal();
}
