package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@NoArgsConstructor
@TableName(value = "distribution_electric_price_config")
public class DistributionElectricPriceConfig {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 月份集合
     */
    @TableField(value = "month")
    private String month;

    /**
     * 是否分时：0 分时用电,1 不分时
     */
    @Range(min = 0,max = 1)
    @TableField(value = "type")
    private Integer type;

    /**
     * 电价方案ID
     */
    @TableField(value = "electric_price_id")
    private Long electricPriceId;

    /***
     * 月份集合
     */
    @NotEmpty
    @TableField(exist = false)
    @Size(min = 1, max=12)
    private List<@Range(min = 1, max = 12) Integer> monthList;

    /***
     * 分时用电设置时间
     */
    @Valid
    @TableField(exist = false)
    private List<DistributionElectricPriceConfigTime> distributionElectricPriceConfigTimeList;
}