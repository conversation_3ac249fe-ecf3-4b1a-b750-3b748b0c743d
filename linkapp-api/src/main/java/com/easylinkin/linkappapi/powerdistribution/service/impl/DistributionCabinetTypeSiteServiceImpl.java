package com.easylinkin.linkappapi.powerdistribution.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfiguration;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetTypeSite;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetTypeSiteMapper;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetConfigurationService;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetTypeSiteService;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <p>
 * 配电柜类型关联位置信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Service
public class DistributionCabinetTypeSiteServiceImpl extends ServiceImpl<DistributionCabinetTypeSiteMapper, DistributionCabinetTypeSite> implements DistributionCabinetTypeSiteService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DistributionCabinetTypeSiteServiceImpl.class);

    @Resource
    private DistributionCabinetConfigurationService distributionCabinetConfigurationService;


    @Override
    public void add(DistributionCabinetTypeSite distributionCabinetTypeSite) {
        validParamRequired(distributionCabinetTypeSite);
        validParamFormat(distributionCabinetTypeSite);
        validRepeat(distributionCabinetTypeSite);
        save(distributionCabinetTypeSite);
        List<DistributionCabinetConfiguration> distributionCabinetConfigurationList = distributionCabinetTypeSite.getDistributionCabinetConfigurationList();
        if (ObjectUtils.isNotEmpty(distributionCabinetConfigurationList)) {
            for (DistributionCabinetConfiguration distributionCabinetConfiguration : distributionCabinetConfigurationList) {
                distributionCabinetConfiguration.setDistributionCabinetTypeSiteId(distributionCabinetTypeSite.getId());
                distributionCabinetConfigurationService.add(distributionCabinetConfiguration);
            }
        }
    }


    /**
     * 校验重复
     */
    private void validRepeat(DistributionCabinetTypeSite distributionCabinetTypeSite) {
//        同一配电柜类型下 配电柜类型位置名称不能相同
        QueryWrapper<DistributionCabinetTypeSite> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("site_name", distributionCabinetTypeSite.getSiteName());
        queryWrapper.eq("distribution_cabinet_type_id", distributionCabinetTypeSite.getDistributionCabinetTypeId());
        List<DistributionCabinetTypeSite> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("配电柜类型位置名称有重复");
        }
        if (StringUtils.isEmpty(distributionCabinetTypeSite.getId())) {
            throw new BusinessException("配电柜类型位置名称已存在");
        }
        if (!distributionCabinetTypeSite.getId().equals(list.get(0).getId())) {
            throw new BusinessException("配电柜类型位置名称已存在");
        }
        
    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(DistributionCabinetTypeSite distributionCabinetTypeSite) {
        Assert.notNull(distributionCabinetTypeSite, "参数为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetTypeSite.getSiteName()), "名称为空");
        Assert.hasLength(distributionCabinetTypeSite.getDeviceUnitCode(), "参数 deviceUnitCode为空");
        Assert.hasLength(distributionCabinetTypeSite.getDeviceUnitVersion(), "参数deviceUnitVersion为空");
        Assert.hasLength(distributionCabinetTypeSite.getDistributionCabinetTypeId(), "参数distributionCabinetTypeId为空");

    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(DistributionCabinetTypeSite distributionCabinetTypeSite) {
        Assert.isTrue(distributionCabinetTypeSite.getSiteName() == null || distributionCabinetTypeSite.getSiteName().length() <= 32, "名称超长");
        Assert.isTrue(distributionCabinetTypeSite.getDeviceUnitCode() == null || distributionCabinetTypeSite.getDeviceUnitCode().length() <= 32, "getDeviceUnitCode超长");
        Assert.isTrue(distributionCabinetTypeSite.getDeviceUnitVersion() == null || distributionCabinetTypeSite.getDeviceUnitVersion().length() <= 32, "getDeviceUnitVersion超长");
        Assert.isTrue(distributionCabinetTypeSite.getDistributionCabinetTypeId() == null || distributionCabinetTypeSite.getDistributionCabinetTypeId().length() <= 32, "getDistributionCabinetTypeId超长");
    }

    @Override
    public List<DistributionCabinetTypeSite> getDistributionCabinetTypeSites(DistributionCabinetTypeSite distributionCabinetTypeSite) {
        return baseMapper.getDistributionCabinetTypeSites(distributionCabinetTypeSite);
    }

    @Override
    public IPage<DistributionCabinetTypeSite> getDistributionCabinetTypeSites(Page page, DistributionCabinetTypeSite distributionCabinetTypeSite) {
        return baseMapper.getDistributionCabinetTypeSites(page, distributionCabinetTypeSite);
    }

    @Override
    public DistributionCabinetTypeSite getDistributionCabinetTypeSite(String id) {
        return baseMapper.getDistributionCabinetTypeSite(id);
    }

    @Override
    public void updateDistributionCabinetTypeSite(DistributionCabinetTypeSite distributionCabinetTypeSite) {
        Assert.notNull(distributionCabinetTypeSite, "参数为空");
        DistributionCabinetTypeSite originDistributionCabinetTypeSite = getById(distributionCabinetTypeSite.getId());
        Assert.notNull(originDistributionCabinetTypeSite, "原配电柜类型位置不存在");
        Assert.hasLength(distributionCabinetTypeSite.getDistributionCabinetTypeId(), "getDistributionCabinetTypeId参数为空");
        validRepeat(distributionCabinetTypeSite);
        validParamFormat(distributionCabinetTypeSite);
        updateById(distributionCabinetTypeSite);
    }

    @Override
    public void deleteBatch(List<DistributionCabinetTypeSite> distributionCabinetTypeSiteList) {
        Assert.notEmpty(distributionCabinetTypeSiteList, "参数为空");
        List<String> ids = distributionCabinetTypeSiteList.stream().map(DistributionCabinetTypeSite::getId).collect(Collectors.toList());
        removeByIds(ids);
    }


	@Override
	public List<DistributionCabinetTypeSite> getDistributionCabinetTypeSiteByTenant(String tenantId,
			String deviceUnitCode) {
		return baseMapper.getDistributionCabinetTypeSiteByTenant(tenantId, deviceUnitCode);
	}
}

