package com.easylinkin.linkappapi.powerdistribution.service;

import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.powerdistribution.entity.*;

import java.io.IOException;
import java.util.List;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/13 10:42
 */
public interface RunAnalyzeService {

    /**
     * 根据配电柜获取关联设备列表
     * @param cabinetId
     * @param type
     * @return
     */
    List<Device> getDeviceListByCabinetId(String cabinetId, Integer type);

    /**
     * 负荷分析
     * @param runAnalyzeParams
     * @return
     * @throws IOException
     */
    LoadAnalyzeResult loadAnalyze(RunAnalyzeParams runAnalyzeParams) throws IOException;

    /**
     * 电力参数
     * @param runAnalyzeParams
     * @return
     * @throws IOException
     */
    ElectricityParamsResult electricityParams(RunAnalyzeParams runAnalyzeParams) throws IOException;

    /**
     * 三相不平衡
     * @param runAnalyzeParams
     * @return
     * @throws IOException
     */
    ThreePhaseUnbalanceResult threePhaseUnbalance(RunAnalyzeParams runAnalyzeParams) throws IOException;

    /**
     * 谐波分析
     * @param runAnalyzeParams
     * @return
     * @throws IOException
     */
    HarmonicsResult harmonics(RunAnalyzeParams runAnalyzeParams) throws IOException;

    List<Device> deviceAttributeFiler(List<Device> deviceList, Integer type);
}
