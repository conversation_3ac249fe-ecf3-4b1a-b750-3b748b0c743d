package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmConfig;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface DistributionCabinetAlarmConfigMapper extends BaseMapper<DistributionCabinetAlarmConfig> {
    int batchInsert(@Param("list") List<DistributionCabinetAlarmConfig> list);

    List<DistributionCabinetAlarmConfig> findAllByDeviceCode(@Param("distributionCabinetAlarmConfig") DistributionCabinetAlarmConfig distributionCabinetAlarmConfig);

    int updateByCabinetIdAndDeviceCodeAndType(@Param("updated") DistributionCabinetAlarmConfig updated);


}