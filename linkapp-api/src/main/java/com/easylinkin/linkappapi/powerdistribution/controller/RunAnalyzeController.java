package com.easylinkin.linkappapi.powerdistribution.controller;

import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.powerdistribution.entity.*;
import com.easylinkin.linkappapi.powerdistribution.service.RunAnalyzeService;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * class info :配电管理-电力运行分析
 *
 * <AUTHOR>
 * @date 2021/9/13 10:41
 */
@RestController
@RequestMapping("/electricityRunAnalyze")
public class RunAnalyzeController {

    @Resource
    private RunAnalyzeService runAnalyzeService;

    @GetMapping("getDeviceListByCabinetId/{cabinetId}/{type}")
    public RestMessage getDeviceListByCabinetId(@PathVariable("cabinetId") String cabinetId, @PathVariable("type") Integer type) {
        Assert.notNull(cabinetId, "cabinetId 不能为空");
        Assert.notNull(type, "type 不能为空");
        List<Device> deviceListByCabinetId = runAnalyzeService.getDeviceListByCabinetId(cabinetId, type);
        return RestBuilders.successBuilder().data(deviceListByCabinetId).build();
    }

    /**
     * 负荷分析
     * @param runAnalyzeParams
     * @return
     * @throws IOException
     */
    @PostMapping("loadAnalyze")
    public RestMessage loadAnalyze(@RequestBody RunAnalyzeParams runAnalyzeParams) throws IOException {
        requiredParamsCheck(runAnalyzeParams);
        LoadAnalyzeResult loadAnalyzeResult = runAnalyzeService.loadAnalyze(runAnalyzeParams);
        return RestBuilders.successBuilder().data(loadAnalyzeResult).build();
    }

    /**
     * 电力参数
     * @param runAnalyzeParams
     * @return
     * @throws IOException
     */
    @PostMapping("electricityParams")
    public RestMessage electricityParams(@RequestBody RunAnalyzeParams runAnalyzeParams) throws IOException {
        requiredParamsCheck(runAnalyzeParams);
        ElectricityParamsResult electricityParamsResult = runAnalyzeService.electricityParams(runAnalyzeParams);
        return RestBuilders.successBuilder().data(electricityParamsResult).build();
    }

    /**
     * 三相不平衡
     * @param runAnalyzeParams
     * @return
     * @throws IOException
     */
    @PostMapping("threePhaseUnbalance")
    public RestMessage threePhaseUnbalance(@RequestBody RunAnalyzeParams runAnalyzeParams) throws IOException {
        requiredParamsCheck(runAnalyzeParams);
        ThreePhaseUnbalanceResult threePhaseUnbalanceResult = runAnalyzeService.threePhaseUnbalance(runAnalyzeParams);
        return RestBuilders.successBuilder().data(threePhaseUnbalanceResult).build();
    }

    /**
     * 谐波分析
     * @param runAnalyzeParams
     * @return
     * @throws IOException
     */
    @PostMapping("harmonics")
    public RestMessage harmonics(@RequestBody RunAnalyzeParams runAnalyzeParams) throws IOException {
        requiredParamsCheck(runAnalyzeParams);
        HarmonicsResult harmonics = runAnalyzeService.harmonics(runAnalyzeParams);
        return RestBuilders.successBuilder().data(harmonics).build();
    }

    private void requiredParamsCheck(RunAnalyzeParams runAnalyzeParams){
        Assert.notNull(runAnalyzeParams, "runAnalyzeParams 不能为空");
        Assert.notNull(runAnalyzeParams.getDeviceCode(), "deviceCode 不能为空");
        Assert.notNull(runAnalyzeParams.getCabinetId(), "cabinetId 不能为空");
        Assert.notNull(runAnalyzeParams.getDate(), "date 不能为空");
    }

}
