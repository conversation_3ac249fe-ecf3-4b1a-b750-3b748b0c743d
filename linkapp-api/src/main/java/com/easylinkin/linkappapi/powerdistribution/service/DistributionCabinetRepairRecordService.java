package com.easylinkin.linkappapi.powerdistribution.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface DistributionCabinetRepairRecordService extends IService<DistributionCabinetRepairRecord>{


    int batchInsert(List<DistributionCabinetRepairRecord> list);

    /***
     * 根据配电柜查询
     * @param distributionCabinetRepairRecord
     * @return
     */
	List<DistributionCabinetRepairRecord> findAllByCabinetId(DistributionCabinetRepairRecord distributionCabinetRepairRecord);

    IPage<DistributionCabinetRepairRecord> page(Page page, DistributionCabinetRepairRecord distributionCabinetRepairRecord);

    void saveOrUpdateEntity(DistributionCabinetRepairRecord distributionCabinetRepairRecord);

    void exportData(DistributionCabinetRepairRecord distributionCabinetRepairRecord, HttpServletRequest request, HttpServletResponse response);

    void removeByIds(DistributionCabinetRepairRecord distributionCabinetRepairRecord);

    DistributionCabinetRepairRecord findByIdOrderByCreateTimeDesc(DistributionCabinetRepairRecord distributionCabinetRepairRecord);
}
