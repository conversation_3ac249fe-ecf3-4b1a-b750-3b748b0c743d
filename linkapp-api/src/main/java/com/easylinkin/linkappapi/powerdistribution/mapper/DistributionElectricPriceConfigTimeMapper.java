package com.easylinkin.linkappapi.powerdistribution.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPriceConfigTime;

public interface DistributionElectricPriceConfigTimeMapper extends BaseMapper<DistributionElectricPriceConfigTime> {
    List<DistributionElectricPriceConfigTime> findAllByElectricPriceConfigId(@Param("electricPriceConfigId")Long electricPriceConfigId);
    int insertList(@Param("list")List<DistributionElectricPriceConfigTime> list);
}