package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@TableName(value = "distribution_electric_price")
public class DistributionElectricPrice {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @NotBlank
    @TableField(value = "name")
    private String name;

    /**
     * 描述
     */
    @Size(max = 250)
    @TableField(value = "description")
    private String description;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time")
    private LocalDateTime modifyTime;

    /**
     * 创建人id
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 修改人id
     */
    @TableField(value = "modifier")
    private String modifier;

    /***
     * 配置
     */
    @Valid
    @TableField(exist = false)
    private List<DistributionElectricPriceConfig> distributionElectricPriceConfigList;

    @TableField(exist = false)
    private List<Long> ids;

    /***
     * 查询开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date queryStartTime;
    /**
     * 查询结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date queryEndTime;

    /***
     * 配电柜DI
     */
    @TableField(exist = false)
    private String cabinetId;
}