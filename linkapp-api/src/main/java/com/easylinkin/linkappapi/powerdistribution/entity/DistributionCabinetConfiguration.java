package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配电柜类型组态
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("distribution_cabinet_configuration")
public class DistributionCabinetConfiguration extends Model<DistributionCabinetConfiguration> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态图
     */
    @TableField("status_picture")
    private String statusPicture;

    /**
     * 配电柜类型id
     */
    @TableField(exist = false)
    private String distributionCabinetTypeId;

    /**
     * 配电柜类型的位置信息id
     */
    @TableField("distribution_cabinet_type_site_id")
    private String distributionCabinetTypeSiteId;

    /**
     * 组编号
     */
    @TableField("group_number")
    private Integer groupNumber;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 创建人id
     */
    @TableField("creator")
    private String creator;

    /**
     * 修改人id
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 组态表达式
     */
    @TableField(exist = false)
    private List<DistributionCabinetConfigurationExpression> distributionCabinetConfigurationExpressions;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
