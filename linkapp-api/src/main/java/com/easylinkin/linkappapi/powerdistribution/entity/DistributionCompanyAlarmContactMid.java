package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/15 9:54
 */
@Data
@Accessors(chain = true)
public class DistributionCompanyAlarmContactMid {

    @TableId(type= IdType.AUTO)
    private Integer id;

    @TableField(value = "company_id")
    private Integer companyId;

    @TableField(value = "alarm_contact_id")
    private String alarmContactId;
}
