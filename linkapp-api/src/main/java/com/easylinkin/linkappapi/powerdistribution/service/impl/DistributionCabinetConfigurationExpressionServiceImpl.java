package com.easylinkin.linkappapi.powerdistribution.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetConfigurationExpression;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetTypeSite;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetConfigurationExpressionMapper;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetConfigurationExpressionService;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <p>
 * 组态表达式 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Service
public class DistributionCabinetConfigurationExpressionServiceImpl extends ServiceImpl<DistributionCabinetConfigurationExpressionMapper, DistributionCabinetConfigurationExpression> implements DistributionCabinetConfigurationExpressionService {

    @Override
    public void add(DistributionCabinetConfigurationExpression distributionCabinetConfigurationExpression) {

        validParamRequired(distributionCabinetConfigurationExpression);
        validParamFormat(distributionCabinetConfigurationExpression);
        save(distributionCabinetConfigurationExpression);
    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(DistributionCabinetConfigurationExpression distributionCabinetConfigurationExpression) {
        Assert.notNull(distributionCabinetConfigurationExpression, "参数为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetConfigurationExpression.getDistributionCabinetConfigurationId()), "getDistributionCabinetConfigurationId 为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetConfigurationExpression.getValue()), "getValue 为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetConfigurationExpression.getDeviceUnitCode()), "getDeviceUnitCode 为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetConfigurationExpression.getDeviceUnitVersion()), "getDeviceUnitVersion 为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetConfigurationExpression.getCalculateSign()), "getCalculateSign 为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetConfigurationExpression.getDeviceAttributeIdentifier()), "getDeviceAttributeIdentifier 为空");
        Assert.notNull(distributionCabinetConfigurationExpression.getSortNo(), "getSortNo 为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(DistributionCabinetConfigurationExpression distributionCabinetConfigurationExpression) {
        Assert.isTrue(distributionCabinetConfigurationExpression.getDeviceUnitCode() == null || distributionCabinetConfigurationExpression.getDeviceUnitCode().length() <= 32, "getDeviceUnitCode 超长");
        Assert.isTrue(distributionCabinetConfigurationExpression.getDeviceAttributeIdentifier() == null || distributionCabinetConfigurationExpression.getDeviceAttributeIdentifier().length() <= 32, "getDeviceAttributeIdentifier 超长");
        Assert.isTrue(distributionCabinetConfigurationExpression.getDistributionCabinetConfigurationId() == null || distributionCabinetConfigurationExpression.getDistributionCabinetConfigurationId().length() <= 32, "getDistributionCabinetConfigurationId 超长");
        Assert.isTrue(distributionCabinetConfigurationExpression.getValue() == null || distributionCabinetConfigurationExpression.getValue().length() <= 32, "getValue 超长");
    }

	@Override
	public List<DistributionCabinetConfigurationExpression> getDistributionCabinetConfigurationExpressionByTenant(
			String tenantId, String deviceUnitCode) {
		return baseMapper.getDistributionCabinetConfigurationExpressionsByTenant(tenantId, deviceUnitCode);
	}

}
