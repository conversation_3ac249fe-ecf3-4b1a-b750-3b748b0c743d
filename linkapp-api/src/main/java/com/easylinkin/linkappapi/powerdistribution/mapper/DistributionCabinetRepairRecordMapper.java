package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairRecord;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface DistributionCabinetRepairRecordMapper extends BaseMapper<DistributionCabinetRepairRecord> {
    int batchInsert(@Param("list") List<DistributionCabinetRepairRecord> list);

    List<DistributionCabinetRepairRecord> findAllByCabinetId(@Param("distributionCabinetRepairRecord") DistributionCabinetRepairRecord distributionCabinetRepairRecord);

    IPage<DistributionCabinetRepairRecord> findAllByCabinetId(@Param("page") Page page, @Param("distributionCabinetRepairRecord") DistributionCabinetRepairRecord distributionCabinetRepairRecord);

    List<DistributionCabinetRepairRecord> findByIdOrderByCreateTimeDesc(@Param("id") Long id);


}