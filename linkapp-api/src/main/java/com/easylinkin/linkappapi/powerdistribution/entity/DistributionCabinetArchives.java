package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@NoArgsConstructor
@TableName(value = "distribution_cabinet_archives")
public class DistributionCabinetArchives {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 生产厂商
     */
    @Length(max = 30)
    @TableField(value = "manufacturer")
    private String manufacturer;

    /**
     * 出厂日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "dom")
    private LocalDateTime dom;

    /**
     * 额定容量
     */
    @Length(max = 30)
    @TableField(value = "rated_capacity")
    private String ratedCapacity;

    /**
     * 额定电压
     */
    @Length(max = 30)
    @TableField(value = "rated_voltage")
    private String ratedVoltage;

    /**
     * 安装位置
     */
    @Length(max = 100)
    @TableField(value = "address")
    private String address;

    /**
     * 备注
     */
    @Length(max = 100)
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 配电柜ID
     */
    @TableField(value = "cabinet_id")
    private String cabinetId;
}