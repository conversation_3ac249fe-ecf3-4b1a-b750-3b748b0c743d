package com.easylinkin.linkappapi.powerdistribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRepairFile;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DistributionCabinetRepairFileMapper extends BaseMapper<DistributionCabinetRepairFile> {
    int batchInsert(@Param("list") List<DistributionCabinetRepairFile> list);

    List<DistributionCabinetRepairFile> findAllByCabinetRepairId(@Param("cabinetRepairId")Long cabinetRepairId);

    int deleteByCabinetRepairId(@Param("cabinetRepairId")Long cabinetRepairId);


}