package com.easylinkin.linkappapi.powerdistribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionRoom;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 配电房 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionRoomService extends IService<DistributionRoom> {


    void add(DistributionRoom distributionRoom);


    /**
     * 批量删除
     */
    void deleteBatch(List<DistributionRoom> distributionRoomList);


    /**
     * 修改
     */
    void updateDistributionRoom(DistributionRoom distributionRoom);


    /**
     * 配置详情
     * @param distributionRoom
     */
    @Transactional(rollbackFor = Exception.class)
    void setDetailConfig(DistributionRoom distributionRoom);

    @Transactional(rollbackFor = Exception.class)
    void setDetailConfigDistributionCabinet(DistributionRoom distributionRoom);

    /**
     * 根据条件查询配电房所有
     */
    List<DistributionRoom> getDistributionRooms(DistributionRoom distributionRoom);


    /**
     * 根据条件查询配电房 分页
     *
     * @param page 分页参数
     * @param distributionRoom 查询参数vo
     * @return 查询结果
     */
    IPage<DistributionRoom> getDistributionRooms(Page page, DistributionRoom distributionRoom);


    /**
     * 单条查询
     *
     * @param id 主键key
     * @return 查询结果
     */
    DistributionRoom getDistributionRoom(String id);


    /**
     * 查询配电房监控
     *
     * @param id 配电房id
     */
    DistributionRoom getDistributionRoomMonitor(String id);

    /**
     * 查询与配电房相关联的设备信息
     * @param id 配电房id
     * @return
     */
    List<Device> getRefdevices(String id);
}
