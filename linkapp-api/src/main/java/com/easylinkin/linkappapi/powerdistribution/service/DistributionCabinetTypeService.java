package com.easylinkin.linkappapi.powerdistribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetType;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 配电柜类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface DistributionCabinetTypeService extends IService<DistributionCabinetType> {


    @Transactional(rollbackFor = Exception.class)
    void add(DistributionCabinetType distributionCabinetType);


    /**
     * 批量删除
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteBatch(List<DistributionCabinetType> distributionCabinetTypeList);


    /**
     * 修改
     */
    @Transactional(rollbackFor = Exception.class)
    void updateDistributionCabinetType(DistributionCabinetType distributionCabinetType);


    /**
     * 根据条件查询配电房所有
     */
    List<DistributionCabinetType> getDistributionCabinetTypes(DistributionCabinetType distributionCabinetType);


    /**
     * 根据条件查询配电房 分页
     *
     * @param page 分页参数
     * @param distributionCabinetType 查询参数vo
     * @return 查询结果
     */
    IPage<DistributionCabinetType> getDistributionCabinetTypes(Page page, DistributionCabinetType distributionCabinetType);


    /**
     * 单条查询
     *
     * @param id 主键key
     * @return 查询结果
     */
    DistributionCabinetType getDistributionCabinetType(String id);

    /**
     * 导出内容
     * @param distributionCabinetType
     * @param request
     * @param response
     */
    void exportData(DistributionCabinetType distributionCabinetType, HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Set importData(MultipartFile file);
}
