package com.easylinkin.linkappapi.powerdistribution.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/16 16:21
 */
@Data
public class ElectricityParamsResult {

    private List<ElectricityEs> esApparentPowerList;

    /**
     * 相电压数据
     */
    private List<AttributeInfo> phaseVoltageAttribute;
    private Double maxPhaseVoltage;

    /**
     * 线电压数据
     */
    private List<AttributeInfo> doublePhaseVoltageAttribute;
    private Double maxDoublePhaseVoltage;

    /**
     * 相电流数据
     */
    private List<AttributeInfo> phaseCurrentAttribute;
    private Double maxPhaseCurrent;

}
