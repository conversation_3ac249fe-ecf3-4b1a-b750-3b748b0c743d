package com.easylinkin.linkappapi.powerdistribution.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionRoomRefDevice;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionRoomRefDeviceMapper;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionRoomRefDeviceService;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <p>
 * 配电房关联设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Service
public class DistributionRoomRefDeviceServiceImpl extends ServiceImpl<DistributionRoomRefDeviceMapper, DistributionRoomRefDevice> implements DistributionRoomRefDeviceService {

    @Override
    public void add(DistributionRoomRefDevice distributionRoomRefDevice) {
        validParamRequired(distributionRoomRefDevice);
        validParamFormat(distributionRoomRefDevice);
        validRepeat(distributionRoomRefDevice);
        save(distributionRoomRefDevice);
    }

    /**
     * 校验重复
     */
    private void validRepeat(DistributionRoomRefDevice distributionRoomRefDevice) {
        QueryWrapper<DistributionRoomRefDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("distribution_room_id", distributionRoomRefDevice.getDistributionRoomId());
        queryWrapper.eq("device_code", distributionRoomRefDevice.getDeviceCode());
        List<DistributionRoomRefDevice> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("配电房关联设备名称有重复");
        }
        if (StringUtils.isEmpty(distributionRoomRefDevice.getId())) {
            throw new BusinessException("配电房关联设备名称已存在");
        }
        if (!distributionRoomRefDevice.getId().equals(list.get(0).getId())) {
            throw new BusinessException("配电房关联设备名称已存在");
        }
        
    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(DistributionRoomRefDevice distributionRoomRefDevice) {
        Assert.notNull(distributionRoomRefDevice, "参数为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionRoomRefDevice.getDeviceCode()), "getDeviceCode 为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionRoomRefDevice.getDistributionRoomId()), "getDistributionRoomId 为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(DistributionRoomRefDevice distributionRoomRefDevice) {
        Assert.isTrue(distributionRoomRefDevice.getDeviceCode() == null || distributionRoomRefDevice.getDeviceCode().length() <= 32, "getDeviceCode 超长");
        Assert.isTrue(distributionRoomRefDevice.getDistributionRoomId() == null || distributionRoomRefDevice.getDistributionRoomId().length() <= 32, "distributionRoomId 超长");
    }
}
