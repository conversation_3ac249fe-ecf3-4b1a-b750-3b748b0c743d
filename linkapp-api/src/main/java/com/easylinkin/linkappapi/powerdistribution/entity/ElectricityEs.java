package com.easylinkin.linkappapi.powerdistribution.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/14 10:45
 */
@Data
public class ElectricityEs {

    private String deviceCode;
    private String deviceName;
    private ElectricityDataEs data;
    private String storageTime;
    private String createTime;

    public Double getVoltageUnbalanceRate(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getVoltageUnbalanceRate() == null){
            return 0.0;
        }
        return this.data.getVoltageUnbalanceRate();
    }

    public Double getCurrentUnbalanceRate(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getCurrentUnbalanceRate() == null){
            return 0.0;
        }
        return this.data.getCurrentUnbalanceRate();
    }

    /**
     * 总视在功率
     * @return
     */
    public Double getApparentPower(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getApparentPower() == null){
            return 0.0;
        }
        return this.data.getApparentPower();
    }

    /**     线电压          */
    public Double getAbPhaseVoltage(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getAbPhaseVoltage() == null){
            return 0.0;
        }
        return this.data.getAbPhaseVoltage();
    }

    public Double getBcPhaseVoltage(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getBcPhaseVoltage() == null){
            return 0.0;
        }
        return this.data.getBcPhaseVoltage();
    }

    public Double getAcPhaseVoltage(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getAcPhaseVoltage() == null){
            return 0.0;
        }
        return this.data.getAcPhaseVoltage();
    }

    /**     相电压          */
    public Double getAPhaseVoltage(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getAPhaseVoltage() == null){
            return 0.0;
        }
        return this.data.getAPhaseVoltage();
    }

    public Double getBPhaseVoltage(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getBPhaseVoltage() == null){
            return 0.0;
        }
        return this.data.getBPhaseVoltage();
    }

    public Double getCPhaseVoltage(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getCPhaseVoltage() == null){
            return 0.0;
        }
        return this.data.getCPhaseVoltage();
    }


    /**     相电流          */
    public Double getAPhaseCurrent(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getAPhaseCurrent() == null){
            return 0.0;
        }
        return this.data.getAPhaseCurrent();
    }

    public Double getBPhaseCurrent(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getBPhaseCurrent() == null){
            return 0.0;
        }
        return this.data.getBPhaseCurrent();
    }

    public Double getCPhaseCurrent(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getCPhaseCurrent() == null){
            return 0.0;
        }
        return this.data.getCPhaseCurrent();
    }

    public Double getAPhaseVoltageHarmonics(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getAPhaseVoltageHarmonics() == null){
            return 0.0;
        }
        return this.data.getAPhaseVoltageHarmonics();
    }

    public Double getBPhaseVoltageHarmonics(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getBPhaseVoltageHarmonics() == null){
            return 0.0;
        }
        return this.data.getBPhaseVoltageHarmonics();
    }

    public Double getCPhaseVoltageHarmonics(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getCPhaseVoltageHarmonics() == null){
            return 0.0;
        }
        return this.data.getCPhaseVoltageHarmonics();
    }

    public Double getAPhaseCurrentHarmonics(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getAPhaseCurrentHarmonics() == null){
            return 0.0;
        }
        return this.data.getAPhaseCurrentHarmonics();
    }

    public Double getBPhaseCurrentHarmonics(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getBPhaseCurrentHarmonics() == null){
            return 0.0;
        }
        return this.data.getBPhaseCurrentHarmonics();
    }

    public Double getCPhaseCurrentHarmonics(){
        if(this.data == null){
            return 0.0;
        }
        if(this.data.getCPhaseCurrentHarmonics() == null){
            return 0.0;
        }
        return this.data.getCPhaseCurrentHarmonics();
    }

}
