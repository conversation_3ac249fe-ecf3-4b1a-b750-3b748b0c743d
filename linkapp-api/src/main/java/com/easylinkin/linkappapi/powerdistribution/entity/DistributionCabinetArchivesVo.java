package com.easylinkin.linkappapi.powerdistribution.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @program: linkapp-group-cloud
 * @description: 设备档案Vo
 * @author: chenkaixuan
 * @create: 2021-09-13 15:55
 */
@Data
public class DistributionCabinetArchivesVo {
    /**
     * 配电柜Id
     */
    private String cabinetId;

    /***
     * 配电柜名称
     */
    private String cabinetName;

    /***
     * 配电柜类型
     */
    private String cabinetTypeName;

    /***
     * 站点
     */
    private String roomName;
    /***
     * 空间
     */
    private String spaceName;
    /***
     * 区域
     */
    private String areaPath;
    /***
     * 公司
     */
    private String companyName;
    /***
     * 电价
     */
    private String priceName;

    /***
     * 档案信息
     */
    private DistributionCabinetArchives distributionCabinetArchives;

    /***
     * 配电柜配置设备
     */
    private List<DistributionCabinetAlarmConfig> distributionCabinetAlarmConfigList;

    /***
     * 文档信息
     */
    private List<DistributionCabinetFile> distributionCabinetFileList;
}
