package com.easylinkin.linkappapi.powerdistribution.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetTypeSite;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetTypeSiteService;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 配电柜类型关联位置信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@RestController
@RequestMapping("/distributionCabinetTypeSite")
public class DistributionCabinetTypeSiteController {

    @Resource
    private DistributionCabinetTypeSiteService service;


    @PostMapping("add")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_TYPE_SITE, desc = "新增配电柜类型关联位置信息")
    public RestMessage add(@RequestBody @Valid DistributionCabinetTypeSite distributionCabinetTypeSite) {
        service.add(distributionCabinetTypeSite);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("get")
    public RestMessage getDistributionCabinetTypeSites(@RequestBody DistributionCabinetTypeSite distributionCabinetTypeSite) {
        Assert.notNull(distributionCabinetTypeSite, "distributionCabinetTypeSite 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinetTypeSites(distributionCabinetTypeSite)).build();
    }


    @PostMapping("getPage")
    public RestMessage getDistributionCabinetTypeSitesPage(@RequestBody RequestModel<DistributionCabinetTypeSite> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DistributionCabinetTypeSite> record = service.getDistributionCabinetTypeSites(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    @PostMapping("getById")
    public RestMessage getById(@RequestParam("id") String id) {
        Assert.hasLength(id, "id 不能为空");
        return RestBuilders.successBuilder().data(service.getDistributionCabinetTypeSite(id)).build();
    }

    @PostMapping("update")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_TYPE_SITE, desc = "修改配电柜类型关联位置信息")
    public RestMessage update(@RequestBody DistributionCabinetTypeSite distributionCabinetTypeSite) {
        service.updateDistributionCabinetTypeSite(distributionCabinetTypeSite);
        return RestBuilders.successBuilder().build();
    }


    @PostMapping("deleteBatch")
    @CommonOperateLogAnnotate(module = LogModule.DISTRIBUTION_CABINET_TYPE_SITE, desc = "批量删除配电柜类型关联位置信息")
    public RestMessage deleteBatch(@RequestBody List<DistributionCabinetTypeSite> distributionCabinetTypeSiteList) {
        Assert.notEmpty(distributionCabinetTypeSiteList, "参数为空");
        service.deleteBatch(distributionCabinetTypeSiteList);
        return RestBuilders.successBuilder().build();
    }


}

