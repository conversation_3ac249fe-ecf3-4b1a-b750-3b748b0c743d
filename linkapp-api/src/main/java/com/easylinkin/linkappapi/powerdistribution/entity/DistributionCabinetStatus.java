package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配电柜状态(冗余部分组态字段)
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("distribution_cabinet_status")
public class DistributionCabinetStatus extends Model<DistributionCabinetStatus> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 配电柜id
     */
    @TableField("distribution_cabinet_id")
    private String distributionCabinetId;

    /**
     * 组态id
     */
    @TableField("distribution_cabinet_configuration_id")
    private String distributionCabinetConfigurationId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


    /**
     * 组态名称
     */
    @TableField(exist = false)
    private String distributionCabinetConfigurationName;

    /**
     * 组编号
     */
    @TableField(exist = false)
    private Integer groupNumber;

    /**
     * 状态图
     */
    @TableField(exist = false)
    private String statusPicture;

    /**
     * 配电柜类型的位置信息id
     */
    @TableField(exist = false)
    private String distributionCabinetTypeSiteId;



    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
