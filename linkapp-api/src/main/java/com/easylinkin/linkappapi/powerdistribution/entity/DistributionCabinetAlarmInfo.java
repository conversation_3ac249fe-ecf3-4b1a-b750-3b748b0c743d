package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.List;

import com.easylinkin.linkappapi.device.entity.Device;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName(value = "distribution_cabinet_alarm_info")
public class DistributionCabinetAlarmInfo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(exist = false)
    private String idStr;

    /**
     * 配电柜ID
     */
    @TableField(value = "cabinet_id")
    private String cabinetId;

    /**
     * 设备code
     */
    @TableField(value = "device_code")
    private String deviceCode;

    /**
     * 告警规则名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 告警规则名称
     */
    @TableField(value = "content")
    private String content;

    /**
     * 告警数据日志
     */
    @TableField(value = "source_json")
    private String sourceJson;

    /**
     * 处理状态；1未处理，2已处理
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 误报标志，0 非误报，1误报
     */
    @TableField(value = "alarm_status")
    private Integer alarmStatus;

    /**
     * 告警时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "alarm_time")
    private LocalDateTime alarmTime;

    /**
     * 处理人
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 处理时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "deal_time")
    private LocalDateTime dealTime;

    /**
     * 说明
     */
    @TableField(value = "description")
    private String description;

    /**
     * 告警等级；1高，2中，3低
     */
    @TableField("level")
    private Integer level;

    /***
     * 位置
     */
    @TableField(exist = false)
    private String areaPath;

    /***
     * 站点
     */
    @TableField(exist = false)
    private String roomName;

    /**
     * 配电柜名称
     */
    @TableField(exist = false)
    private String cabinetName;

    /***
     * 企业ID
     */
    @TableField(exist = false)
    private Integer companyId;

    @TableField(exist = false)
    private String deviceName;

    @TableField(exist = false)
    private String tenantId;

    /**
     * 设备类型id(查询条件)
     */
    @TableField(exist = false)
    private String deviceTypeId;

    @TableField(exist = false)
    private String deviceTypeName;

    /**
     * 查询时间起始
     */
    @TableField(exist = false)
    private String queryTimeStart;
    /**
     * 查询时间起始
     */
    @TableField(exist = false)
    private String queryTimeEnd;

    /***
     * 标识类型 1 告警，2 指标告警
     */
    @TableField(exist = false)
    private Integer identificationType;

    /***
     * 指标告警
     */
    @TableField(exist = false)
    private List<Long> ids;

    /***
     * 告警Ids
     */
    @TableField(exist = false)
    private List<String> alarmIds;

    @TableField(exist = false)
    private Device device;

    @TableField(exist = false)
    private String ruleEngineName;

}