package com.easylinkin.linkappapi.powerdistribution.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.device.mapper.DeviceMapper;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetAlarmConfig;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetArchives;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetAlarmConfigMapper;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetArchivesMapper;
import com.easylinkin.linkappapi.powerdistribution.mapper.DistributionCabinetRefDeviceMapper;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetAlarmConfigService;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetRefDeviceService;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import com.easylinkin.linkappapi.powerdistribution.service.RunAnalyzeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p>
 * 配电柜关联设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Service
public class DistributionCabinetRefDeviceServiceImpl extends ServiceImpl<DistributionCabinetRefDeviceMapper, DistributionCabinetRefDevice> implements DistributionCabinetRefDeviceService {
    @Resource
    DistributionCabinetAlarmConfigService distributionCabinetAlarmConfigService;

    @Override
    public void add(DistributionCabinetRefDevice distributionCabinetRefDevice) {
        validParamRequired(distributionCabinetRefDevice);
        validParamFormat(distributionCabinetRefDevice);
        if (validRepeat(distributionCabinetRefDevice)) {
            return;
        }
        //查询是否存在不平衡属性，保存默认值
        boolean count = distributionCabinetAlarmConfigService.aoib(distributionCabinetRefDevice.getDeviceCode());
        if(count){
            //不平衡度 15%，严重不平衡度 30%
            DistributionCabinetAlarmConfig distributionCabinetAlarmConfig = new DistributionCabinetAlarmConfig();
            distributionCabinetAlarmConfig.setCabinetId(distributionCabinetRefDevice.getDistributionCabinetId());
            distributionCabinetAlarmConfig.setDeviceCode(distributionCabinetRefDevice.getDeviceCode());
            distributionCabinetAlarmConfig.setType(2);
            distributionCabinetAlarmConfig.setValueMin(new BigDecimal(15));
            distributionCabinetAlarmConfig.setValueMax(new BigDecimal(30));
            distributionCabinetAlarmConfigService.save(distributionCabinetAlarmConfig);
        }
        //检查是否存在 负荷指标
         count = distributionCabinetAlarmConfigService.loadindex(distributionCabinetRefDevice.getDeviceCode());
        if(count){
            DistributionCabinetAlarmConfig distributionCabinetAlarmConfig = new DistributionCabinetAlarmConfig();
            distributionCabinetAlarmConfig.setCabinetId(distributionCabinetRefDevice.getDistributionCabinetId());
            distributionCabinetAlarmConfig.setDeviceCode(distributionCabinetRefDevice.getDeviceCode());
            distributionCabinetAlarmConfig.setType(1);
            distributionCabinetAlarmConfigService.save(distributionCabinetAlarmConfig);
        }
        save(distributionCabinetRefDevice);
    }

    @Override
    public List<DistributionCabinetRefDevice> getCabinetRefDevicesWithConfigurationAndExpressions(Device device) {
        Assert.notNull(device, "设备参数为空");
        Assert.hasLength(device.getCode(), "设备编号为空");
        return baseMapper.getCabinetRefDevicesWithConfigurationAndExpressions(device);
    }


    /**
     * 校验重复
     */
    private boolean validRepeat(DistributionCabinetRefDevice distributionCabinetRefDevice) {
        QueryWrapper<DistributionCabinetRefDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("distribution_cabinet_id", distributionCabinetRefDevice.getDistributionCabinetId());
        queryWrapper.eq("distribution_cabinet_type_site_id", distributionCabinetRefDevice.getDistributionCabinetTypeSiteId());
        queryWrapper.eq("device_code", distributionCabinetRefDevice.getDeviceCode());
        return count(queryWrapper) > 0;
    }

    /**
     * 校验参数必填
     */
    private void validParamRequired(DistributionCabinetRefDevice distributionCabinetRefDevice) {
        Assert.notNull(distributionCabinetRefDevice, "参数为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetRefDevice.getDeviceCode()),
            "getDeviceCode 为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetRefDevice.getDistributionCabinetId()),
            "getDistributionCabinetId 为空");
        Assert.isTrue(StringUtils.isNotBlank(distributionCabinetRefDevice.getDistributionCabinetTypeSiteId()),
            "getDistributionCabinetTypeSiteId 为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(DistributionCabinetRefDevice distributionCabinetRefDevice) {
        Assert.isTrue(distributionCabinetRefDevice.getDeviceCode() == null ||
            distributionCabinetRefDevice.getDeviceCode().length() <= 32, "getDeviceCode 超长");
        Assert.isTrue(distributionCabinetRefDevice.getDistributionCabinetId() == null ||
            distributionCabinetRefDevice.getDistributionCabinetId().length() <= 32, "getDistributionCabinetId 超长");
        Assert.isTrue(distributionCabinetRefDevice.getDistributionCabinetTypeSiteId() == null ||
            distributionCabinetRefDevice.getDistributionCabinetTypeSiteId().length() <= 32, "getDistributionCabinetTypeSiteId 超长");
    }
}
