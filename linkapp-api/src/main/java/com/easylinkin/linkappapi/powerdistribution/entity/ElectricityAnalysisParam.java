package com.easylinkin.linkappapi.powerdistribution.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @program: linkapp-group-cloud
 * @description: 电量分析请求参数
 * @author: chenkaixuan
 * @create: 2021-09-18 16:18
 */
@Data
public class ElectricityAnalysisParam{
    /***
     * 配电柜ID
     */
    @NotNull
    private String cabinetId;
    /***
     * 设备code
     */
    @NotNull
    private String deviceCode;
    /***
     * 查询起始时间
     */
    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date queryStartDate;
    /***
     * 查询截止时间
     */
    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date queryEndDate;
    /***
     * 类型 0 分时，1 周 2月 3自定义 4年 5日
     */
    private int type;
}
