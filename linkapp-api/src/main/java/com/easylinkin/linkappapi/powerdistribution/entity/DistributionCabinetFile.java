package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName(value = "distribution_cabinet_file")
public class DistributionCabinetFile {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配电柜ID
     */
    @TableField(value = "cabinet_id")
    private String cabinetId;

    /**
     * 文件url
     */
    @TableField(value = "file_url")
    private String fileUrl;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;
}