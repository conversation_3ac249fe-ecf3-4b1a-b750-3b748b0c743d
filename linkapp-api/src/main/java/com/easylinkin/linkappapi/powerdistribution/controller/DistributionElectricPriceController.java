package com.easylinkin.linkappapi.powerdistribution.controller;

import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.powerdistribution.entity.DistributionElectricPrice;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionElectricPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * @program: linkapp-group-cloud
 * @description: 配电-电价方案
 * @author: chenkaixuan
 * @create: 2021-09-10 10:58
 */
@RestController
@RequestMapping("/distributionElectricPrice")
public class DistributionElectricPriceController {
    @Autowired
    DistributionElectricPriceService distributionElectricPriceService;

    @PostMapping("page")
    public RestMessage page(@RequestBody RequestModel<DistributionElectricPrice> requestModel) {
        Assert.notNull(requestModel, "参数不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        return RestBuilders.successBuilder().data(distributionElectricPriceService.page(requestModel.getPage(), requestModel.getCustomQueryParams())).build();
    }

    @PostMapping("getAll")
    public RestMessage getAll(@RequestBody DistributionElectricPrice distributionElectricPrice) {
        Assert.notNull(distributionElectricPrice, "参数不能为空");
        return RestBuilders.successBuilder().data(distributionElectricPriceService.getAll(distributionElectricPrice)).build();
    }

    @PostMapping("saveOrUpdate")
    public RestMessage saveOrUpdate(@Validated @RequestBody DistributionElectricPrice distributionElectricPrice){
        distributionElectricPriceService.saveOrUpdateEntity(distributionElectricPrice);
        return RestBuilders.successBuilder().build();
    }

    @PostMapping("findAllById")
    public RestMessage findAllById(@RequestBody DistributionElectricPrice distributionElectricPrice){
        return RestBuilders.successBuilder().data(distributionElectricPriceService.findAllById(distributionElectricPrice)).build();
    }

    @PostMapping("removeByIds")
    public RestMessage removeByIds(@RequestBody DistributionElectricPrice distributionElectricPrice){
        distributionElectricPriceService.removeByIds(distributionElectricPrice);
        return RestBuilders.successBuilder().build();
    }

}
