package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.space.entity.LinkappArea;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配电房
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("distribution_room")
public class DistributionRoom extends Model<DistributionRoom> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 区域id
     */
    @TableField("area_id")
    private String areaId;

    /**
     * 区域路径(查询用)
     */
    @TableField(exist = false)
    private String areaPath;

    /**
     * 区域
     */
    @TableField(exist = false)
    private LinkappArea linkappArea;

    /**
     * 视频地址
     */
    @TableField(value = "video_path", strategy = FieldStrategy.IGNORED)
    private String videoPath;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 逻辑删除，1存在，0已删除
     */
    @TableField("delete_state")
    @TableLogic(value = "1", delval = "0")
    private Integer deleteState;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 纬度
     */
    @TableField("latitude")
    private String latitude;

    /**
     * 经度
     */
    @TableField("longitude")
    private String longitude;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 创建人id
     */
    @TableField("creator")
    private String creator;

    /**
     * 修改人id
     */
    @TableField("modifier")
    private String modifier;

    /***
     * 企业ID
     */
    @TableField("company_info_id")
    private Integer companyInfoId;

    /***
     * 电价方案ID
     */
    @TableField("electric_price_id")
    private Long electricPriceId;

    /**
     * 关联的设备
     */
    @TableField(exist = false)
    private List<Device> deviceList;

    /**
     * 关联的配电柜
     */
    @TableField(exist = false)
    private List<DistributionCabinet> distributionCabinetList;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
