package com.easylinkin.linkappapi.powerdistribution.controller;


import com.easylinkin.linkappapi.powerdistribution.entity.DistributionCabinetRefDevice;
import com.easylinkin.linkappapi.powerdistribution.service.DistributionCabinetRefDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 配电柜关联设备 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@RestController
@RequestMapping("/distributionCabinetRefDevice")
public class DistributionCabinetRefDeviceController {
}
