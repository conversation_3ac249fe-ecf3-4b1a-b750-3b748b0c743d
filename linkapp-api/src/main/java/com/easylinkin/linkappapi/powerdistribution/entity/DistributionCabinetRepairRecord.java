package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@TableName(value = "distribution_cabinet_repair_record")
public class DistributionCabinetRepairRecord {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 姓名
     */
    @NotBlank
    @Length(max = 32)
    @TableField(value = "name")
    private String name;

    /**
     * 内容
     */
    @NotBlank
    @Length(max = 100)
    @TableField(value = "content")
    private String content;

    /**
     * 检修时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "repair_time")
    private LocalDateTime repairTime;

    @TableField(exist = false)
    private String repairTimeStr;

    /**
     * 处理状态；0 异常，1正常
     */
    @Size(min = 0,max = 1)
    @TableField(value = "status")
    private Integer status;

    @TableField(exist = false)
    private String statusName;

    /**
     * 配电柜ID
     */
    @TableField(value = "cabinet_id")
    private String cabinetId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(exist = false)
    private List<DistributionCabinetRepairFile> distributionCabinetRepairFileList;

    @TableField(exist = false)
    private List<Long> ids;

    /***
     * 查询开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date queryStartTime;
    /**
     * 查询结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date queryEndTime;
}