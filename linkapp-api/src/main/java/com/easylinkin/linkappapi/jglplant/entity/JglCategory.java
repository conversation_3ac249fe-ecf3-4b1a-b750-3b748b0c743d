package com.easylinkin.linkappapi.jglplant.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * 鸡公岭植物分类(JglCategory)表实体类
 *
 * <AUTHOR>
 * @since 2022-02-24 15:16:39
 */
@SuppressWarnings("serial")
public class JglCategory extends Model<JglCategory> {

    private Integer id;
    //中文名
    private String name;
    //类别，1-纲，2-科，3-属，4-种
    private Integer type;
    //英文名
    private String englishName;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}

