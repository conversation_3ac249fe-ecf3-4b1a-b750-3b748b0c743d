package com.easylinkin.linkappapi.jglplant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.jglplant.entity.JglPlant;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * 鸡公岭-植物库(JglPlant)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-18 17:47:20
 */
public interface JglPlantService extends IService<JglPlant> {

    /**
     * 新增
     *
     * @param jglPlant 实体对象
     * @return 操作结果
     */
    boolean saveOne(JglPlant jglPlant);

    /**
     * 修改单条
     * @param jglPlant 实体对象
     * @return 修改结果
     */
    boolean updateOne(JglPlant jglPlant);

    /**
     * 查询分页
     * @param page 分页对象
     * @param customQueryParams 分页参数对象
     * @return 查询分页结果
     */
    IPage<JglPlant> selectPage(Page<JglPlant> page, JglPlant customQueryParams);

    /**
     * 获取单条
     * @param id 主键id
     * @return 查询结果
     */
    JglPlant getOneById(Serializable id);

    /**
     * 根据id批量删除
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     * @param jglPlant 过滤条件实体对象
     * @param request 请求
     * @param response 响应
     */
    void export(JglPlant jglPlant, HttpServletRequest request, HttpServletResponse response);
}

