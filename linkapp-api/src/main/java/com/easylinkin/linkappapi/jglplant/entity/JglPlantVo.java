package com.easylinkin.linkappapi.jglplant.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class JglPlantVo extends JglPlant {
    /**
     * 创建时间
     */
    private String createTimeStr;
    /**
     * 分类
     */
    private String classification;


    public JglPlantVo(JglPlant jglPlant) {
        this.setName(jglPlant.getName());
        StringBuilder sb = new StringBuilder();
        if (jglPlant.getType1() != null) {
            sb.append(jglPlant.getType1());
        }
        if (jglPlant.getType2() != null) {
            sb.append(jglPlant.getType2());
        }
        if (jglPlant.getType3() != null) {
            sb.append(jglPlant.getType3());
        }
        if (jglPlant.getType4() != null) {
            sb.append(jglPlant.getType4());
        }

        this.setClassification(sb.toString());
        this.setArea(jglPlant.getArea());
        this.setFeature(jglPlant.getFeature());

    }
}
