package com.easylinkin.linkappapi.deviceparm.controller;


import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.easylinkin.linkappapi.deviceparm.entity.DeviceParm;
import com.easylinkin.linkappapi.deviceparm.service.DeviceParmService;

import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@RestController
@Api(value="设备参数控制器",tags={"设备参数"})
@RequestMapping("/deviceParm")
public class DeviceParmController {

    @Resource
    private DeviceParmService deviceParmService;

    @ApiOperation("根据查询条件获取设备参数list")
    @PostMapping("getAll")
    public RestMessage getAll(@RequestBody DeviceParm deviceParm) {
        return RestBuilders.successBuilder(deviceParmService.getAll(deviceParm)).build();
    }

    @ApiOperation("获取顶级设备参数列表")
    @PostMapping("selectDeviceParmList")
    public RestMessage selectDeviceParmList(@RequestBody DeviceParm deviceParm) {
        return RestBuilders.successBuilder(deviceParmService.selectDeviceParmList(deviceParm)).build();
    }
}
