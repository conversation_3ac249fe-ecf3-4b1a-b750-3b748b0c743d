package com.easylinkin.linkappapi.deviceparm.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.deviceparm.mapper.DeviceParmDictionaryMapper;
import com.easylinkin.linkappapi.deviceparm.entity.DeviceParmDictionary;
import com.easylinkin.linkappapi.deviceparm.service.DeviceParmDictionaryService;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

@Service
public class DeviceParmDictionaryServiceImpl extends ServiceImpl<DeviceParmDictionaryMapper, DeviceParmDictionary> implements DeviceParmDictionaryService{
    @Resource
    private LinkappUserContextProducer linkappUserContextProducer;

    @Override
    public IPage<DeviceParmDictionary> page(Page<DeviceParmDictionary> page,DeviceParmDictionary deviceParmDictionary) {
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        deviceParmDictionary.setTenantId(tenantId);
        return new LambdaQueryChainWrapper<>(baseMapper)
                .select(DeviceParmDictionary::getId,DeviceParmDictionary::getCommandName,DeviceParmDictionary::getCommandValue)
                .eq(DeviceParmDictionary::getTenantId, deviceParmDictionary.getTenantId())
                .like(!StringUtils.isEmpty(deviceParmDictionary.getCommandName()),DeviceParmDictionary::getCommandName, deviceParmDictionary.getCommandName())
                .orderByDesc(DeviceParmDictionary::getModifyTime,DeviceParmDictionary::getCreateTime)
                .page(page);
    }

    @Override
    public List<DeviceParmDictionary> getAll(DeviceParmDictionary deviceParmDictionary) {
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        deviceParmDictionary.setTenantId(tenantId);
        return new LambdaQueryChainWrapper<>(baseMapper)
                .select(DeviceParmDictionary::getId,DeviceParmDictionary::getCommandName,DeviceParmDictionary::getCommandValue)
                .eq(DeviceParmDictionary::getTenantId, deviceParmDictionary.getTenantId())
                .like(!StringUtils.isEmpty(deviceParmDictionary.getCommandName()),DeviceParmDictionary::getCommandName, deviceParmDictionary.getCommandName())
                .orderByDesc(DeviceParmDictionary::getModifyTime,DeviceParmDictionary::getCreateTime)
                .list();
    }

    /***
     * 添加
     * @param deviceParmDictionary
     */
    @Override
    public void addDeviceParmDictionary(DeviceParmDictionary deviceParmDictionary){
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        Assert.notNull(deviceParmDictionary.getCommandName(),"commandName 不能为空");
        Assert.notNull(deviceParmDictionary.getCommandValue(),"commandValue 不能为空");
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        deviceParmDictionary.setTenantId(tenantId);
        deviceParmDictionary.setId(null);
        validateNameIsExist(deviceParmDictionary);
        LocalDateTime dateTime=LocalDateTime.now();
        deviceParmDictionary.setCreateTime(dateTime);
        deviceParmDictionary.setModifyTime(dateTime);
        save(deviceParmDictionary);
    }

    @Override
    public void saveDeviceParmDictionary(DeviceParmDictionary deviceParmDictionary){
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        if(StringUtils.isEmpty(deviceParmDictionary.getId())){
            addDeviceParmDictionary(deviceParmDictionary);
        }else{
            updateDeviceParmDictionary(deviceParmDictionary);
        }
    }

    /***
     * 删除
     * @param deviceParmDictionary
     */
    @Override
    public void removeDeviceParmDictionary(DeviceParmDictionary deviceParmDictionary){
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        Assert.notNull(deviceParmDictionary.getId(),"id 不能为空");
        removeById(deviceParmDictionary.getId());
    }

    @Override
    public void updateDeviceParmDictionary(DeviceParmDictionary deviceParmDictionary){
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        Assert.notNull(deviceParmDictionary.getId(),"id 不能为空");
        Assert.notNull(deviceParmDictionary.getCommandName(),"commandName 不能为空");
        Assert.notNull(deviceParmDictionary.getCommandValue(),"commandValue 不能为空");
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        deviceParmDictionary.setTenantId(tenantId);
        validateNameIsExist(deviceParmDictionary);
        new LambdaUpdateChainWrapper<>(baseMapper)
                .set(DeviceParmDictionary::getCommandName, deviceParmDictionary.getCommandName())
                .set(DeviceParmDictionary::getCommandValue, deviceParmDictionary.getCommandValue())
                .set(DeviceParmDictionary::getModifyTime,LocalDateTime.now())
                .eq(DeviceParmDictionary::getId,deviceParmDictionary.getId())
                .eq(DeviceParmDictionary::getTenantId,deviceParmDictionary.getTenantId())
                .update();
    }

    /***
     * 效验name是否存在
     * @param deviceParmDictionary
     * @return
     */
    private void validateNameIsExist(DeviceParmDictionary deviceParmDictionary){
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        if(StringUtils.isEmpty(deviceParmDictionary.getTenantId())){
            String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
            deviceParmDictionary.setTenantId(tenantId);
        }
        //查询是否重复
        Integer count = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(DeviceParmDictionary::getTenantId, deviceParmDictionary.getTenantId())
                .eq(DeviceParmDictionary::getCommandName, deviceParmDictionary.getCommandName())
                .ne(deviceParmDictionary.getId() != null, DeviceParmDictionary::getId, deviceParmDictionary.getId())
                .count();
        if(count != null && count>0){
            throw new IllegalArgumentException("指令名称["+deviceParmDictionary.getCommandName()+"]已存在");
        }
    }
}
