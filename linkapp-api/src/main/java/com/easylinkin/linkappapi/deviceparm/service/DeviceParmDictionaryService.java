package com.easylinkin.linkappapi.deviceparm.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.deviceparm.entity.DeviceParmDictionary;
import com.baomidou.mybatisplus.extension.service.IService;
public interface DeviceParmDictionaryService extends IService<DeviceParmDictionary>{

    IPage<DeviceParmDictionary> page(Page<DeviceParmDictionary> page, DeviceParmDictionary deviceParmDictionary);

    List<DeviceParmDictionary> getAll(DeviceParmDictionary deviceParmDictionary);

    void addDeviceParmDictionary(DeviceParmDictionary deviceParmDictionary);

    void removeDeviceParmDictionary(DeviceParmDictionary deviceParmDictionary);

    void updateDeviceParmDictionary(DeviceParmDictionary deviceParmDictionary);

    void saveDeviceParmDictionary(DeviceParmDictionary deviceParmDictionary);

}
