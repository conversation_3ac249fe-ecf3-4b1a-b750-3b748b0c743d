package com.easylinkin.linkappapi.deviceparm.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.deviceparm.entity.DeviceParm;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface DeviceParmService extends IService<DeviceParm> {

    List<DeviceParm> getAll(DeviceParm deviceParm);

    List<DeviceParm> selectDeviceParmList(DeviceParm deviceParm);

    /**
     * 提取 父子层级格式化服务参数list，条数可能会减少
     * @param parmList 原服务参数list
     * @return 格式化后的
     */
    List<DeviceParm> copyFormatDeviceParmList(List<DeviceParm> parmList);
}
