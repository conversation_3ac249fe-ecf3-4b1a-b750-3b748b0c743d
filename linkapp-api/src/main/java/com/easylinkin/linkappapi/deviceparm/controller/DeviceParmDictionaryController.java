package com.easylinkin.linkappapi.deviceparm.controller;

import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.deviceparm.entity.DeviceParmDictionary;
import com.easylinkin.linkappapi.deviceparm.service.DeviceParmDictionaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * @program: linkapp-group-mydev
 * @description: 设备服务指令
 * @author: chenkaixuan
 * @create: 2021-08-24 10:18
 */
@RestController
@Api(value="设备服务参数字典",tags={"设备服务参数字典"})
@RequestMapping("/deviceParmDictionary")
public class DeviceParmDictionaryController {
    @Autowired
    private DeviceParmDictionaryService deviceParmDictionaryService;

    @ApiOperation("分页查询设备服务参数字典")
    @PostMapping("page")
    public RestMessage getAll(@RequestBody RequestModel<DeviceParmDictionary> requestModel) {
        Assert.notNull(requestModel,"参数不能为空");
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        return RestBuilders.successBuilder().data(deviceParmDictionaryService.page(requestModel.getPage(),requestModel.getCustomQueryParams())).build();
    }

    @ApiOperation("查询全部设备服务参数字典")
    @PostMapping("getAll")
    public RestMessage getAll(@RequestBody DeviceParmDictionary deviceParmDictionary) {
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        return RestBuilders.successBuilder().data(deviceParmDictionaryService.getAll(deviceParmDictionary)).build();
    }

    @ApiOperation("保存设备服务参数字典数据")
    @PostMapping("save")
    public RestMessage save(@RequestBody DeviceParmDictionary deviceParmDictionary) {
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        deviceParmDictionaryService.saveDeviceParmDictionary(deviceParmDictionary);
        return RestBuilders.successBuilder().build();
    }

    @ApiOperation("修改设备服务参数字典数据")
    @PostMapping("update")
    public RestMessage update(@RequestBody DeviceParmDictionary deviceParmDictionary) {
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        deviceParmDictionaryService.updateDeviceParmDictionary(deviceParmDictionary);
        return RestBuilders.successBuilder().build();
    }

    @ApiOperation("移除设备服务参数字典数据")
    @PostMapping("remove")
    public RestMessage remove(@RequestBody DeviceParmDictionary deviceParmDictionary) {
        Assert.notNull(deviceParmDictionary,"参数不能为空");
        deviceParmDictionaryService.removeDeviceParmDictionary(deviceParmDictionary);
        return RestBuilders.successBuilder().build();
    }
}
