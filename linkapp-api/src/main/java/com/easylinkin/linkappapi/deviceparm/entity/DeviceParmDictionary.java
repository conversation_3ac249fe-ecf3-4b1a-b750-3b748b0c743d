package com.easylinkin.linkappapi.deviceparm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@NoArgsConstructor
@ApiModel("设备参数字典实体类")
@TableName(value = "linkapp_device_parm_dictionary")
public class DeviceParmDictionary {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Integer id;

    /**
     * 命令名称
     */
    @TableField(value = "command_name")
    @ApiModelProperty("命令名称")
    private String commandName;

    /**
     * 命令值
     */
    @TableField(value = "command_value")
    @ApiModelProperty("命令值")
    private String commandValue;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    @ApiModelProperty("租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time")
    @ApiModelProperty("修改时间")
    private LocalDateTime modifyTime;
}