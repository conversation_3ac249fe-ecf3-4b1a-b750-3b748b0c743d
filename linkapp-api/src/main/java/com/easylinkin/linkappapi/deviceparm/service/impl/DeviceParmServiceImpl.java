package com.easylinkin.linkappapi.deviceparm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.deviceparm.entity.DeviceParm;
import com.easylinkin.linkappapi.deviceparm.mapper.DeviceParmMapper;
import com.easylinkin.linkappapi.deviceparm.service.DeviceParmService;

import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
public class DeviceParmServiceImpl extends ServiceImpl<DeviceParmMapper, DeviceParm> implements DeviceParmService {

    @Autowired
    DeviceParmMapper deviceParmMapper;

    @Override
    public List<DeviceParm> getAll(DeviceParm deviceParm) {
        QueryWrapper qw = new QueryWrapper();
        if (StringUtils.isNotEmpty(deviceParm.getId())) {
            qw.eq("id", deviceParm.getId());
        }
        if (StringUtils.isNotEmpty(deviceParm.getCreator())) {
            qw.eq("creator", deviceParm.getCreator());
        }
        if (StringUtils.isNotEmpty(deviceParm.getName())) {
            qw.eq("name", deviceParm.getName());
        }
        return baseMapper.selectList(qw);
    }

    @Override
    public List<DeviceParm> selectDeviceParmList(DeviceParm deviceParm) {

        List<DeviceParm> parmList = deviceParmMapper.selectDeviceParmList(deviceParm);
        //            查找出顶级的设备参数
        List<DeviceParm> topParms = copyFormatDeviceParmList(parmList);
        return topParms;
    }

    @Override
    public List<DeviceParm> copyFormatDeviceParmList(List<DeviceParm> parmList) {
        if (ObjectUtils.isEmpty(parmList)) {
            return parmList;
        }
        List<DeviceParm> topParms = parmList.stream().filter(e -> ObjectUtils.isEmpty(e.getParentId())).collect(Collectors.toList());
        for (DeviceParm topParm : topParms) {
            List<DeviceParm> childParamList = parmList.stream().filter(e -> topParm.getId().equals(e.getParentId())).collect(Collectors.toList());
            topParm.setChildParamList(childParamList);
        }
        return topParms;
    }
}
