package com.easylinkin.linkappapi.blockqueue;

import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/08/14
 * @description
 */
public class MemoryBlockingQueue<T> implements BlockingQueue<T> {

  private LinkedBlockingQueue<T> queue = new LinkedBlockingQueue<>();

  @Override
  public void add(T t) {
    queue.offer(t);
  }

  @Override
  public T take() throws InterruptedException{
    return queue.take();
  }

  @Override
  public int size() {
    return queue.size();
  }
}
