package com.easylinkin.linkappapi.blockqueue;

import com.easylinkin.linkappapi.blockqueue.vo.BlockingQueueUseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 阻塞队列管理
 * <AUTHOR>
 * @version 1.0
 * @date 2023/08/14
 * @description 阻塞队列管理
 */
@RestController
@RequestMapping("blockingQueue")
@Api(tags = "阻塞队列管理")
public class BlockingQueueController {

  @Autowired
  private QueueManager queueManager;

  @GetMapping("/pendingTasksPerQueue")
  @ApiOperation(value = "获取队列中的任务数")
  public List<BlockingQueueUseVO> getPendingTasksPerQueue() {
    return queueManager.getPendingTasksPerQueue();
  }

  // 获取熔断url列表
  @GetMapping("/getCircuitBreakerUrlList")
  @ApiOperation(value = "获取熔断url列表")
  public Map<String, List<Object>> getCircuitBreakerUrlList() {
    return queueManager.getCircuitBreakerUrlList();
  }


}
