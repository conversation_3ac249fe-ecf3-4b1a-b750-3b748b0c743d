package com.easylinkin.linkappapi.blockqueue.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/08/14
 * @description 队列key常量
 */
public interface QueueKeyConstant {
	// 空调分户计量-耗能统计
//	String AIR_CONDITIONING_ENERGY_CONSUMPTION_STATISTICS = "air_conditioning_energy_consumption_statistics";

  // 电子围栏告警功能
	String ELECTRONIC_FENCE_ALARM_FUNCTION = "electronic_fence_alarm_function";

  // 闸机流水处理
	String GATE_FLOW_PROCESSING = "gate_flow_processing";

  // 喷淋流水处理
	String SPRAY_FLOW_PROCESSING = "spray_flow_processing";

  // 用水流水处理
	String WATER_FLOW_PROCESSING = "water_flow_processing";

  // 用电流水处理
	String ELECTRICITY_FLOW_PROCESSING = "electricity_flow_processing";

  // 塔机流水处理
	String TOWER_CRANE_FLOW_PROCESSING = "tower_crane_flow_processing";


  // 配电箱流水处理
	String ELECTRRIC_BOX_FLOW_PROCESSING = "electric_box_flow_processing";


  // ai相机预警流水统计
	String AI_CAMERA_WARNING_FLOW_STATISTICS = "ai_camera_warning_flow_statistics";


  // 升降机流水处理
	String LIFT_FLOW_PROCESSING = "lift_flow_processing";


	// 定位器流水处理
	String LOCATION_PROCESSING = "location_processing";


  // 扬尘、塔机、升降机设备数据上传到省厅系统
	String DATA_UPLOAD_TO_PROVINCIAL_HALL_SYSTEM = "data_upload_to_provincial_hall_system";

	//断路器设备流水
	String RAIL_CIRCUIT_GIVEALARM_FAULT_PROCESSING = "rail_circuit_givealarm_fault_processing";

	//吊车安全监测
	String RAIL_CRANE_SAFETY_MONITORING = "rail_crane_safety_monitoring1";

	//电器火灾流水处理
	String RAIL_CIRCUIT_DQHZ_PROCESSING = "rail_circuit_dqhz_processing";

	//北斗定位
	String BEIDOU_LOCATION = "beidou_location1";

	//广场喇叭
	String RAIL_VOICE_BROAD_PROCESSING = "rail_voice_broad_processing";

	//高支模监测
	String RAIL_HIGH_FORMWORK_PROCESSING = "rail_high_formwork_processing";

	//新塔机流水处理
	String RAIL_NEW_TOWER_CRANE_FLOW_PROCESSING = "rail_new_tower_crane_flow_processing";

	//AI综合安防行为记录
	//String RAIL_AI_HOST_RECORD_PROCESSING = "rail_ai_host_record_processing";

	// QueueKey 枚举enum 包括两个字段 queueKey 和 threadName
	enum QueueKeyEnum {
//		AIR_CONDITIONING_ENERGY_CONSUMPTION_STATISTICS(QueueKeyConstant.AIR_CONDITIONING_ENERGY_CONSUMPTION_STATISTICS,
//				"air_conditioning_thread"),
		ELECTRONIC_FENCE_ALARM_FUNCTION(QueueKeyConstant.ELECTRONIC_FENCE_ALARM_FUNCTION, "electronic_fence_thread"),
		GATE_FLOW_PROCESSING(QueueKeyConstant.GATE_FLOW_PROCESSING, "gate_flow_processing_thread"),
		SPRAY_FLOW_PROCESSING(QueueKeyConstant.SPRAY_FLOW_PROCESSING, "spray_flow_processing_thread"),
		WATER_FLOW_PROCESSING(QueueKeyConstant.WATER_FLOW_PROCESSING, "water_flow_processing_thread"),
		ELECTRICITY_FLOW_PROCESSING(QueueKeyConstant.ELECTRICITY_FLOW_PROCESSING, "electricity_flow_processing_thread"),
//		TOWER_CRANE_FLOW_PROCESSING(QueueKeyConstant.TOWER_CRANE_FLOW_PROCESSING, "tower_crane_flow_processing_thread"),
		ELECTRRIC_BOX_FLOW_PROCESSING(QueueKeyConstant.ELECTRRIC_BOX_FLOW_PROCESSING, "electric_box_flow_processing_thread"),
		AI_CAMERA_WARNING_FLOW_STATISTICS(QueueKeyConstant.AI_CAMERA_WARNING_FLOW_STATISTICS, "ai_camera_warning_flow_statistics_thread"),
		LIFT_FLOW_PROCESSING(QueueKeyConstant.LIFT_FLOW_PROCESSING, "lift_flow_processing_thread"),
		LOCATION_PROCESSING(QueueKeyConstant.LOCATION_PROCESSING, "location_processing_thread"),
		DATA_UPLOAD_TO_PROVINCIAL_HALL_SYSTEM(QueueKeyConstant.DATA_UPLOAD_TO_PROVINCIAL_HALL_SYSTEM,
				"data_upload_to_provincial_hall_system_thread"),
		RAIL_CIRCUIT_GIVEALARM_FAULT_PROCESSING(QueueKeyConstant.RAIL_CIRCUIT_GIVEALARM_FAULT_PROCESSING,"circuit_givealarm_fault_thread"),
		RAIL_CRANE_SAFETY_MONITORING(QueueKeyConstant.RAIL_CRANE_SAFETY_MONITORING,"rail_crane_safety_monitoring_thread"),
		BEIDOU_LOCATION(QueueKeyConstant.BEIDOU_LOCATION,"beidou_location"),
		RAIL_CIRCUIT_DQHZ_PROCESSING(QueueKeyConstant.RAIL_CIRCUIT_DQHZ_PROCESSING,"rail_circuit_dqhz_processing_thread"),
		RAIL_VOICE_BROAD_PROCESSING(QueueKeyConstant.RAIL_VOICE_BROAD_PROCESSING,"rail_voice_broad_processing_thread"),
		RAIL_HIGH_FORMWORK_PROCESSING(QueueKeyConstant.RAIL_HIGH_FORMWORK_PROCESSING,"rail_high_formwork_processing_thread"),
		RAIL_NEW_TOWER_CRANE_FLOW_PROCESSING(QueueKeyConstant.RAIL_NEW_TOWER_CRANE_FLOW_PROCESSING,"rail_new_tower_crane_flow_processing_thread");

		private String queueKey;
		private String threadName;

		QueueKeyEnum(String queueKey, String threadName) {
			this.queueKey = queueKey;
			this.threadName = threadName;
		}

		public String getQueueKey() {
			return queueKey;
		}

		public String getThreadName() {
			return threadName;
		}

		public static String getThreadNameByQueueKey(String queueKey) {
			for (QueueKeyEnum queueKeyEnum : QueueKeyEnum.values()) {
				if (queueKeyEnum.getQueueKey().equals(queueKey)) {
					return queueKeyEnum.getThreadName();
				}
			}
			return null;
		}

		//获取所有的队列key的ArrayList
		public static List<String> getAllQueueKey() {
			List<String> queueKeyList = new ArrayList<>();
			for (QueueKeyEnum queueKeyEnum : QueueKeyEnum.values()) {
				queueKeyList.add(queueKeyEnum.getQueueKey());
			}
			return queueKeyList;
		}
	}

}
