package com.easylinkin.linkappapi.annotation;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

/**
 * class info :自定义注解获取方法执行时间拦截
 *
 * remark : exposeProxy的默认值是false, (exposeProxy = true)会让自定义注解扫描到内部方法
 *
 * <AUTHOR>
 * @date 2021/8/27 12:12
 */
@Aspect
@Component
@Slf4j
@EnableAspectJAutoProxy
public class CustomMethodTimerInterpreter {

    @Pointcut("@annotation(com.easylinkin.linkappapi.annotation.CustomMethodTimer)")
    public void annotationLog(){}

    @Around("annotationLog()")
    public Object handlingTimeAround(ProceedingJoinPoint joinPoint){
        try {
            long startTime = System.currentTimeMillis();
            String methodName = joinPoint.getSignature().getName();
            Object target = joinPoint.getTarget();
            String className = target.getClass().getName();
            Object proceed = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            log.info("{}:{}执行, 耗时{}毫秒", className, methodName, endTime-startTime);
            return proceed;
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

}
