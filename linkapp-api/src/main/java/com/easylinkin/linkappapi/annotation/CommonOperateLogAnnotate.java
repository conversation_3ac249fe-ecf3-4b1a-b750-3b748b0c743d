package com.easylinkin.linkappapi.annotation;

import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface CommonOperateLogAnnotate {

    /**
     * 是否记录参数 默认 true
     * @return
     */
    boolean recordParam() default true;

    LogModule module();

    String desc();
    
}
