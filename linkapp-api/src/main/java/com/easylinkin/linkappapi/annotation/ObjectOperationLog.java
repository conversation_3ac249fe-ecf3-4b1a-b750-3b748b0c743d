package com.easylinkin.linkappapi.annotation;

import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface ObjectOperationLog {

    String value() default "";

    LogModule module();

    String desc();
}
