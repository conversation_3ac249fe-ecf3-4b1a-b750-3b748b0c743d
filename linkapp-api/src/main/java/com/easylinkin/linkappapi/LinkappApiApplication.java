package com.easylinkin.linkappapi;

import static site.morn.framework.context.CommonConstant.Config.FRAMEWORK_BASE_PACKAGES;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import java.nio.charset.StandardCharsets;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import site.morn.boot.data.jpa.JpaRepositoryFactoryProducer;

@MapperScan(basePackages = {"com.easylinkin.**.mapper","com.easylinkin.**.dao"})
@ServletComponentScan
@EnableAsync
@EnableCaching

@EnableTransactionManagement

@EntityScan({FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@EnableJpaRepositories(
    repositoryFactoryBeanClass = JpaRepositoryFactoryProducer.class,
    basePackages = {FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@SpringBootApplication(scanBasePackages = {FRAMEWORK_BASE_PACKAGES, "com.easylinkin"}, exclude = DruidDataSourceAutoConfigure.class)

//新加配置两条 来之 umbp 可能不需要
@EnableJpaAuditing
//启用属性加密
//@EnableEncryptableProperties
@EnableScheduling
public class LinkappApiApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(LinkappApiApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(LinkappApiApplication.class);
        app.setBannerMode(Banner.Mode.OFF);
        app.run(args);
        System.out.println("--服务启动成功--");
    }

    @Bean
    @Primary
    public RestTemplate restTemplate() {

        return new RestTemplate();
    }

}
