package com.easylinkin.linkappapi.devicetype.mapper;

import com.easylinkin.linkappapi.devicetype.entity.DeviceType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.shigongyun.vo.DeviceCountVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface DeviceTypeMapper extends BaseMapper<DeviceType> {

    /**
     * 根据设备类型统计设备数量
     *
     * @param deviceCountVo 条件vo
     * @return 设备类型统计设备数量List
     */
    List<Map<String, Object>> countGroupByDeviceTypeGlobal(DeviceCountVo deviceCountVo);


}
