package com.easylinkin.linkappapi.devicetype.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.devicetype.entity.DeviceType;
import com.easylinkin.linkappapi.devicetype.mapper.DeviceTypeMapper;
import com.easylinkin.linkappapi.devicetype.service.DeviceTypeService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.easylinkin.linkappapi.function.mapper.LinkappFunctionMapper;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
public class DeviceTypeServiceImpl extends ServiceImpl<DeviceTypeMapper, DeviceType> implements DeviceTypeService {
    @Resource
    LinkappUserContextProducer linkappUserContextProducer;
    @Resource
    private LinkappFunctionMapper linkappFunctionMapper;

    @Override
    public List<DeviceType> getAll(DeviceType deviceType) {
        QueryWrapper<DeviceType> qw = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(deviceType.getId())) {
            qw.eq("id", deviceType.getId());

        }
        if (StringUtils.isNotEmpty(deviceType.getName())) {
            qw.eq("name", deviceType.getName());

        }
        if (StringUtils.isNotEmpty(deviceType.getCompanyId())) {
            qw.eq("company_id", deviceType.getCompanyId());

        }
        if (StringUtils.isNotEmpty(deviceType.getCreator())) {
            qw.eq("creator", deviceType.getCreator());

        }
        if (null != deviceType.getLevel()) {
            qw.eq("level", deviceType.getLevel());

        }

        if (StringUtils.isNotEmpty(deviceType.getParentId())) {
            qw.eq("parent_id", deviceType.getParentId());

        }
        if (StringUtils.isNotEmpty(deviceType.getSearchCode())) {
            qw.eq("search_code", deviceType.getSearchCode());
        }

        return baseMapper.selectList(qw);
    }

    @Override
    public IPage getPage(Page page, DeviceType deviceType) {
        QueryWrapper qw = new QueryWrapper();
        if (StringUtils.isNotEmpty(deviceType.getName())) {
            qw.like("name", deviceType.getName());
        }
        if (StringUtils.isNotEmpty(deviceType.getId())) {
            qw.like("id", deviceType.getId());
        }
        if (StringUtils.isNotEmpty(deviceType.getParentId())) {
            qw.like("parent_id", deviceType.getParentId());
        }
        if (StringUtils.isNotEmpty(deviceType.getSearchCode())) {
            qw.like("search_code", deviceType.getSearchCode());
        }

        return baseMapper.selectPage(page, qw);
    }

    /***
     * 获取设备类型，按function功能模块过滤
     * @return
     */
    @Override
    public List<DeviceType> getDeviceTypeFilterFunction(String functionIdentifier){
        Assert.notNull(functionIdentifier,"functionIdentifier 不能为空");
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        //绑定设备做过滤
        List<Map<String, String>> funacionDeviceTypeByIdentifier = linkappFunctionMapper.getFunacionDeviceTypeByIdentifier(functionIdentifier,tenantId );
        if(funacionDeviceTypeByIdentifier == null || funacionDeviceTypeByIdentifier.size()<=0){
            return null;
        }
        List<String> deviceTypeIds=new ArrayList<>();
        funacionDeviceTypeByIdentifier.forEach(m->{
            if(m == null){
                return;
            }
            String deviceType = m.get("deviceType");
            if(StringUtils.isEmpty(deviceType)){
                return;
            }
            List<String> strings = JSONArray.parseArray(deviceType, String.class);
            if(strings == null || strings.size()<=0){
                return;
            }
            deviceTypeIds.addAll(strings);
        });
        if(deviceTypeIds == null || deviceTypeIds.size()<=0){
            return null;
        }
        return new LambdaQueryChainWrapper<>(baseMapper)
                    .select(DeviceType::getId,DeviceType::getParentId,DeviceType::getName,DeviceType::getDescription,DeviceType::getIcoPath)
                    .eq(DeviceType::getDeleteState,1)
                    .in(DeviceType::getId,deviceTypeIds)
                    .orderByDesc(DeviceType::getModifyTime,DeviceType::getCreateTime)
                    .list();
    }
}
