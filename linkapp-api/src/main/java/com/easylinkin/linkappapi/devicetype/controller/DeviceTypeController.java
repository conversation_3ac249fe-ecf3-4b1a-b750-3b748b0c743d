package com.easylinkin.linkappapi.devicetype.controller;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.easylinkin.linkappapi.application.service.ApplicationService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.devicetype.entity.DeviceType;
import com.easylinkin.linkappapi.devicetype.service.DeviceTypeService;
import com.easylinkin.linkappapi.deviceunit.entity.DeviceUnit;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@RestController
@Api(value="设备类型",tags={"设备类型"})
@RequestMapping("/deviceType")
public class DeviceTypeController {

    @Autowired
    private DeviceTypeService deviceTypeService;
    
    @Autowired
    ApplicationService applicationService;
    
    @Resource
    LinkappUserContextProducer linkappUserContextProducer;

    @ApiOperation("获取全部设备类型数据")
    @PostMapping("/getAll")
    public RestMessage getAll(@RequestBody DeviceType deviceType) {
        Assert.notNull(deviceType, "参数为空");
        List<DeviceType> record = deviceTypeService.getAll(deviceType);
        return RestBuilders.successBuilder().data(record).build();
    }

    @ApiOperation("分页获取设备类型数据")
    @PostMapping("/getPage")
    public RestMessage getPage(@RequestBody RequestModel<DeviceType> requestModel) {
        Assert.notNull(requestModel, "参数为空");
        Assert.notNull(requestModel.getPage(), "page参数为空");
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 参数为空");
        return RestBuilders.successBuilder().data(deviceTypeService.getPage(requestModel.getPage(), requestModel.getCustomQueryParams())).build();
    }

    @ApiOperation("根据用户ID获取相关联的设备型号/类型数据")
    @PostMapping("/selectApplicationDeviceTypeByUser")
    public RestMessage selectApplicationDeviceUnitByUser(@RequestBody DeviceType deviceType) {
    	Long userId = linkappUserContextProducer.getCurrent().getId();
    	List<DeviceUnit> deviceUnitList = applicationService.selectApplicationDeviceUnitByUser(userId.toString());
    	Set<DeviceType> deviceTypeList = new HashSet<>();
    	for(DeviceUnit du : deviceUnitList) {
    		DeviceType dt =new DeviceType();
    		dt.setId(du.getDeviceTypeId());
    		dt.setName(du.getDeviceTypeName());
    		deviceTypeList.add(dt);
    	}
        return RestBuilders.successBuilder(deviceTypeList).build();
    }

    @ApiOperation("根据设备类型ID修改设备类型显示图标")
    @PostMapping("/updateDeviceTypeIcoPath")
    public RestMessage updateDeviceTypeIcoPath(@RequestBody DeviceType deviceType) {
        Assert.notNull(deviceType, "参数为空");
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("id", deviceType.getId());
        updateWrapper.set("ico_path", deviceType.getIcoPath());
        return RestBuilders.successBuilder().data(deviceTypeService.update(updateWrapper)).build();
    }

    @ApiOperation("获取设备类型，按functionIdentifier功能模块过滤")
    @PostMapping("/getDeviceTypeFilterFunction")
    public RestMessage getDeviceTypeFilterFunction(@RequestParam("functionIdentifier") String functionIdentifier) {
        return RestBuilders.successBuilder().data(deviceTypeService.getDeviceTypeFilterFunction(functionIdentifier)).build();
    }
    
    @ApiOperation("根据用户ID和设备类型获取相关联的设备型号/类型数据")
    @PostMapping("/selectApplicationDeviceUnit")
    public RestMessage selectApplicationDeviceUnit(@RequestBody DeviceType deviceType) {
    	Long userId = linkappUserContextProducer.getCurrent().getId();
    	List<DeviceUnit> deviceUnitList = applicationService.selectApplicationDeviceUnit(userId.toString(),deviceType.getName());
    	Set<DeviceType> deviceTypeList = new HashSet<>();
    	for(DeviceUnit du : deviceUnitList) {
    		DeviceType dt =new DeviceType();
    		dt.setId(du.getDeviceTypeId());
    		dt.setName(du.getDeviceTypeName());
    		deviceTypeList.add(dt);
    	}
        return RestBuilders.successBuilder(deviceTypeList).build();
    }
}
