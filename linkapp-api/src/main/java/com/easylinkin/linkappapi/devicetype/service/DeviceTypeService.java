package com.easylinkin.linkappapi.devicetype.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.devicetype.entity.DeviceType;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface DeviceTypeService extends IService<DeviceType> {

    /**
     * 查询所有设备类型
     * @param deviceType 对象-设备类型
     * @return 查得结果
     */
    List<DeviceType> getAll(DeviceType deviceType);

    /**
     * 查询设备类型分页
     * @param page 分页参数
     * @param deviceType 查询参数
     * @return 分页查询结果
     */
    IPage getPage(Page page, DeviceType deviceType);

    /***
     * 获取设备类型，按function功能模块过滤
     * @param functionIdentifier
     * @return
     */
    List<DeviceType> getDeviceTypeFilterFunction(String functionIdentifier);
}
