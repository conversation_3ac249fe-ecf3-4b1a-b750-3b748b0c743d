package com.easylinkin.linkappapi.devicetype.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备类型
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("设备类型实体类")
@TableName("linkapp_device_type")
public class DeviceType extends Model<DeviceType> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键ID")
    @TableId
    private String id;

    /**
     * 父类型id
     */
    @ApiModelProperty("父ID")
    @TableField("parent_id")
    private String parentId;

    /**
     * 型号名称
     */
    @ApiModelProperty("类型名称")
    private String name;

    @ApiModelProperty("描述")
    private String description;

    @TableField("ico_path")
    @ApiModelProperty("显示图标")
    private String icoPath;

    @ApiModelProperty(value = "层级", required = false, example = "1")
    private BigDecimal level;

    @ApiModelProperty("查询码")
    @TableField("search_code")
    private String searchCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("公司ID")
    @TableField("company_id")
    private String companyId;

    /**
     * 删除标识
     */
    @ApiModelProperty(value = "删除标识", required = false, example = "1")
    @TableField("delete_state")
    private Integer deleteState;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改人")
    private String modifier;

    @ApiModelProperty("修改时间")
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 设备数
     */
    @TableField(exist = false)
    private Integer count;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
