package com.easylinkin.linkappapi.afterloan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.afterloan.entity.AfterloanEnterpriseinfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskType;

import java.util.List;

public interface AfterloanEnterpriseinfoService extends IService<AfterloanEnterpriseinfo> {
    /***
     * 获取所有企业故障
     * @param afterloanEnterpriseinfo
     * @return
     */
    List<AfterloanEnterpriseinfo> getAll(AfterloanEnterpriseinfo afterloanEnterpriseinfo);

    /***
     * 根据企业名称获取故障信息
     * @param name
     * @return
     */
    AfterloanEnterpriseinfo getAfterloanEnterpriseinfo(String name);

    /***
     * 修改或保存
     * @param afterloanEnterpriseinfo
     */
    void updateOrSave(AfterloanEnterpriseinfo afterloanEnterpriseinfo);

    /***
     * 根据类型获取
     * @param afterloanEnterpriseinfo
     * @return
     */
    List<AfterloanEnterpriseinfo> getTypeAll(AfterloanEnterpriseinfo afterloanEnterpriseinfo);

    /***
     * 根据类型获取(分页)
     * @param page
     * @param afterloanEnterpriseinfo
     * @return
     */
    Page getTypeAll(Page page,AfterloanEnterpriseinfo afterloanEnterpriseinfo);

    /***
     * 删除
     * @param afterloanEnterpriseinfo
     */
    void deleteAll(AfterloanEnterpriseinfo afterloanEnterpriseinfo);

    /***
     * 获取能耗
     * @param page
     * @param afterloanEnterpriseinfo
     * @return
     */
    IPage getMonitor(Page page, AfterloanEnterpriseinfo afterloanEnterpriseinfo);

    /***
     * 企业关联风险扫描
     * @param afterloanEnterpriseinfo
     * @return
     */
    String getEnterpriseRelationRisk(AfterloanEnterpriseinfo afterloanEnterpriseinfo);

    /***
     * 获取企业关联风险
     * @param page
     * @param afterloanRiskType
     * @return
     */
    Page getESAfterloanPageAll(Page page, AfterloanRiskType afterloanRiskType);

    /**
     * 获取企业关联风险详情
     * @param afterloanRiskType
     * @return
     */
    List<String> getEsDateByTypeAndEnterpriseIdDetails(AfterloanRiskType afterloanRiskType);
    /***
     * 查询企业信息
     * @param name
     * @return
     */
    AfterloanEnterpriseinfo getEnterpriseInfo(String name);

    /***
     * 批量删除
     * @param afterloanEnterpriseinfo
     */
    void deleteEsDateByTypeAndEnterpriseIdOrDetails(AfterloanEnterpriseinfo afterloanEnterpriseinfo);
}


