package com.easylinkin.linkappapi.afterloan.controller;

import com.alibaba.fastjson.JSONObject;
import com.easylinkin.linkappapi.afterloan.entity.AfterloanEnterpriseinfo;
import com.easylinkin.linkappapi.afterloan.service.AfterloanEnterpriseinfoService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;

/**
 * @program: linkapp-group
 * @description: 企业信息查询
 * @author: chenkai<PERSON>uan
 * @create: 2021-05-25 18:46 111
 */
@RestController
@RequestMapping("/afterloan/enterpriseinfo")
public class AfterloanEnterpriseinfoController {
    @Autowired
    AfterloanEnterpriseinfoService service;

    /***
     * 获取企业信息
     * @return
     */
    @PostMapping("get")
    public RestMessage getByName(@RequestBody AfterloanEnterpriseinfo afterloanEnterpriseinfo){
        Assert.notNull(afterloanEnterpriseinfo,"参数不能为空");
        Assert.notNull(afterloanEnterpriseinfo.getEnterpriseName(),"enterpriseName 不能为空");
        return RestBuilders.successBuilder().data(service.getEnterpriseInfo(afterloanEnterpriseinfo.getEnterpriseName())).build();
    }

    @PostMapping("updateOrSave")
    public RestMessage update(HttpServletRequest request, HttpServletResponse response,@RequestBody AfterloanEnterpriseinfo afterloanEnterpriseinfo)throws IOException {
        Assert.notNull(afterloanEnterpriseinfo,"参数不能为空");
        Long id=afterloanEnterpriseinfo.getId();
        service.updateOrSave(afterloanEnterpriseinfo);
        if(id == null){
            //直接返回
            String responseString = JSONObject.toJSONString(RestBuilders.successBuilder().build());
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(responseString.getBytes());
            outputStream.flush();
            outputStream.close();
            //同步信息
            service.getEnterpriseRelationRisk(afterloanEnterpriseinfo);
        }
        return RestBuilders.successBuilder().build();
    }

    /***
     * 获取列表数量
     * @param afterloanEnterpriseinfo
     * @return
     */
    @PostMapping("getAllTypeCount")
    public RestMessage getTypeAll(@RequestBody AfterloanEnterpriseinfo afterloanEnterpriseinfo){
        return RestBuilders.successBuilder().data(service.getTypeAll(afterloanEnterpriseinfo)).build();
    }

    @PostMapping("getAllTypeCountPage")
    public RestMessage getAllTypeCountPage(@RequestBody RequestModel<AfterloanEnterpriseinfo> requestModel){
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        return RestBuilders.successBuilder().data(service.getTypeAll(requestModel.getPage(),requestModel.getCustomQueryParams())).build();
    }

    @PostMapping("getAll")
    public RestMessage getAll(@RequestBody AfterloanEnterpriseinfo afterloanEnterpriseinfo){
        return RestBuilders.successBuilder().data(service.getAll(afterloanEnterpriseinfo)).build();
    }

    @PostMapping("deleteAll")
    public RestMessage deleteAll(@RequestBody AfterloanEnterpriseinfo afterloanEnterpriseinfo){
        service.deleteAll(afterloanEnterpriseinfo);
        return RestBuilders.successBuilder().build();
    }

    @PostMapping("getInfo")
    public RestMessage getInfo(@RequestBody AfterloanEnterpriseinfo afterloanEnterpriseinfo){
        Assert.notNull(afterloanEnterpriseinfo,"参数不能为空");
        Assert.notNull(afterloanEnterpriseinfo.getEnterpriseName(),"enterpriseName 不能为空");
        return RestBuilders.successBuilder().data(service.getEnterpriseInfo(afterloanEnterpriseinfo.getEnterpriseName())).build();
    }

    /***
     * 物联网监控风险
     * @param requestModel
     * @return
     */
    @PostMapping("getMonitor")
    public RestMessage getMonitor(@RequestBody RequestModel<AfterloanEnterpriseinfo> requestModel){
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        return RestBuilders.successBuilder().data(service.getMonitor(requestModel.getPage(),requestModel.getCustomQueryParams())).build();
    }

    @PostMapping("syn")
    public RestMessage syn(@RequestBody AfterloanEnterpriseinfo afterloanEnterpriseinfo){
        return RestBuilders.successBuilder().data(service.getEnterpriseRelationRisk(afterloanEnterpriseinfo)).build();
    }
}
