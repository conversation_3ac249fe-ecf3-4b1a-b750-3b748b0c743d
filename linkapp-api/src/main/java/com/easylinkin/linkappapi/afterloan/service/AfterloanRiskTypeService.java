package com.easylinkin.linkappapi.afterloan.service;

import com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskType;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface AfterloanRiskTypeService extends IService<AfterloanRiskType>{

    /***
     * 获取故障数
     * @param afterloanRiskType
     * @return
     */
    List<AfterloanRiskType>  getTypeAndCount(AfterloanRiskType afterloanRiskType);

}
