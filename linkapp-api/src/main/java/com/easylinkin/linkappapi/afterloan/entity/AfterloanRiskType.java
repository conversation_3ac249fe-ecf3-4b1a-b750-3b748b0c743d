package com.easylinkin.linkappapi.afterloan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "afterloan_risk_type")
public class AfterloanRiskType  extends Model<AfterloanRiskType> {
    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型 0：物联网风险，1：自身风险，2：关联风险
     */
    @TableField(value = "risk_type")
    private Integer riskType;

    /**
     * 名称
     */
    @TableField(value = "risk_name")
    private String riskName;

    /**
     * code
     */
    @TableField(value = "risk_code")
    private Integer riskCode;

    /**
     * API地址
     */
    @TableField(value = "api_url")
    private String apiUrl;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time")
    private Date modifyTime;

    /**
     * 创建人id
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 修改人id
     */
    @TableField(value = "modifier")
    private String modifier;


    @TableField(exist = false)
    private Integer number;

    /**
     * 企业编号
     */
    @TableField(exist = false)
    private Long enterpriseId;


    @TableField(exist = false)
    private List<Integer> queryRiskCodeList;

    @TableField(exist = false)
    private String detailsId;

}