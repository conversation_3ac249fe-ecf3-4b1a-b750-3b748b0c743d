package com.easylinkin.linkappapi.afterloan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "afterloan_risk_number")
public class AfterloanRiskNumber  extends Model<AfterloanRiskNumber> {

    /**
     * 企业编号
     */
    @TableId(value = "enterprise_id", type = IdType.INPUT)
    private Long enterpriseId;

    /**
     * 风险类型编号
     */
    @TableId(value = "risk_type_id", type = IdType.INPUT)
    private Long riskTypeId;

    /**
     * 公司名称
     */
    @TableField(exist = false)
    private String enterpriseName;


    /**
     * 名称
     */
    @TableField(exist = false)
    private String riskName;

    /**
     * 租户id
     */
    @TableId(value = "tenant_id",type = IdType.INPUT)
    private String tenantId;

    @TableField(value = "number")
    private Integer number;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time")
    private Date modifyTime;

    /**
     * 创建人id
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 修改人id
     */
    @TableField(value = "modifier")
    private String modifier;
}