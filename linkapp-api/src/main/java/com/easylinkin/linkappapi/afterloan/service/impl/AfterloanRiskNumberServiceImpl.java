package com.easylinkin.linkappapi.afterloan.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskNumber;
import com.easylinkin.linkappapi.afterloan.mapper.AfterloanRiskNumberMapper;
import com.easylinkin.linkappapi.afterloan.service.AfterloanRiskNumberService;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AfterloanRiskNumberServiceImpl extends ServiceImpl<AfterloanRiskNumberMapper, AfterloanRiskNumber> implements AfterloanRiskNumberService{

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertSaveOrUpdateList( List<AfterloanRiskNumber> list){
        baseMapper.insertSaveOrUpdateList(list);
    }
}
