package com.easylinkin.linkappapi.afterloan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.afterloan.entity.AfterloanRiskType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterloanRiskTypeMapper extends BaseMapper<AfterloanRiskType> {

   /***
    * 获取故障数
    * @param afterloanRiskType
    * @return
    */
   List<AfterloanRiskType> getTypeAndCount(@Param("risk") AfterloanRiskType afterloanRiskType);
}