package com.easylinkin.linkappapi.afterloan.utils;

import lombok.Data;

public enum QccRiskEnum {
        ONEONE(1,1,"严重违法","SeriousIllegalCheck"),
        ONETWO(1,2,"失信被执行人","ShixinCheck"),
        ONETHREE(1,3,"行政处罚","AdminPenaltyCheck"),
        ONEFOUR(1,4,"知识产权出质",""),
        ONEFIVE(1,5,"裁判文书","JudgmentDocCheck"),
        ONESIX(1,6,"开庭公告","CourtAnnoCheck"),
        ONESEVEN(1,7,"司法拍卖","JudicialSaleCheck"),
        ONEEIGHT(1,8,"被执行人","ZhixingCheck"),
        ONENINE(1,9,"法院公告","CourtNoticeCheck"),
        ONETEN(1,10,"立案信息","CaseFilingCheck"),
        ONEELEVEN(1,11,"限制高消费","SumptuaryCheck"),
        ONETWELVE(1,12,"终本案件","EndExecuteCaseCheck"),
        ONETHIRTEEN(1,13,"环保处罚","EnvPunishmentCheck"),
        ONEFOURTEEN(1,14,"股权出质","EquityPledgedCheck"),
        ONEFIFTEEN(1,15,"动产质押","ChattelMortgageCheck"),
        ONESIXTEEN(1,16,"股权冻结","EquityFreezeCheck"),
        ONESEVENTEEN(1,17,"经营异常","ExceptionCheck"),
        TWOONE(2,1,"严重违法","CompanyRelaSIllegalCheck"),
        TWOTWO(2,2,"失信被执行人","CompanyRelaSXCheck"),
        TWOTHREE(2,3,"行政处罚",""),
        TWOFOUR(2,4,"知识产权出质",""),
        TWOFIVE(2,5,"裁判文书","CompanyRelaJudgeDocCheck"),
        TWOSIX(2,6,"开庭公告","CompanyRelaCSACheck"),
        TWOSEVEN(2,7,"司法拍卖","CompanyRelaJSaleCheck"),
        TWOEIGHT(2,8,"被执行人","CompanyRelaZXCheck"),
        TWONINE(2,9,"法院公告","CompanyRelaCNoticeCheck"),
        TWOTEN(2,10,"立案信息","CompanyRelaCaseFilingCheck"),
        TWOELEVEN(2,11,"限制高消费","CompanyRelaSumptuaryCheck"),
        TWOTWELVE(2,12,"终本案件","CompanyRelaEndExCaseCheck"),
        TWOTHIRTEEN(2,13,"环保处罚","CompanyRelaEnvPunishCheck"),
        TWOFOURTEEN(2,14,"股权出质","CompanyRelaEPledgedCheck"),
        TWOFIFTEEN(2,15,"动产质押","CompanyRelaCMortgageCheck"),
        TWOSIXTEEN(2,16,"股权冻结","CompanyRelaEFreezeCheck"),
        TWOSEVENTEEN(2,17,"经营异常","CompanyRelaExceptionCheck");

        private Integer type;
        private Integer code;
        private String name;
        private String address;

        QccRiskEnum(Integer type,Integer code,String name,String address){
            this.code=code;
            this.type=type;
            this.name=name;
            this.address=address;
        }
        public static QccRisk getQccRisk(Integer type,Integer code) {
            for (QccRiskEnum qccRiskEnum :QccRiskEnum.values()) {
                if (qccRiskEnum.code.equals(code) && qccRiskEnum.type.equals(type)) {
                    return new QccRisk(qccRiskEnum.type,qccRiskEnum.code,qccRiskEnum.name,qccRiskEnum.address);
                }
            }
            return null;
        }

        @Data
        static class QccRisk{
                private Integer type;
                private Integer code;
                private String name;
                private String address;
                QccRisk(Integer type,Integer code,String name,String address){
                        this.code=code;
                        this.type=type;
                        this.name=name;
                        this.address=address;
                }
        }
}
