package com.easylinkin.linkappapi.machinery.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/09
 * @description
 */
@Data
public class ProjectStatisticsVO {

  // 查询参数
  private String startTime;
  private String endTime;

  // 相应参数
  /**
   * 项目id
   */
  private String projecId;
  /**
   * 项目名称
   */
  private String projectName;

  /**
   * 机械总数
   */
  private Integer total;

  /**
   * 监测设备数（绑定主机的机械数）
   */
  private Integer monitoringNum;

  // 塔吊分析汇总
  /**
   * 塔吊平均工效
   * （该项目所有塔吊的每次最大载重之和/所有塔吊的每次工作循环时间之和）/该项目的塔吊数
   */
  private Double towerCarneAvgEfficiency;

  /**
   * 当日塔吊平均工效-塔吊平均工效 的差值 0相等 1大于 -1小于
   */
  private Double towerCarneAvgEfficiencyDifferent;
  /**
   * 当日塔吊平均吊装次数
   * （当日该项目所有塔吊塔吊的吊装次数之和/塔吊监测数量）
   */
  private Double towerCarneTodayAvgWorkCount;

  /**
   * 当日塔吊平均吊装次数-塔吊平均吊装次数 的差值 0相等 1大于 -1小于
   */
  private Double towerCarneAvgWorkCountDifferent;

  // 升降机分析汇总
  /**
   * 升降机当日平均工作次数
   * （当日当前该项目的所有升降机运行次数之和/升降机监测数量）
   */
  private Double elevatorTodayAvgWorkCount;

  /**
   * 升降机当日平均工作次数-升降机平均吊装次数 的差值 0相等 1大于 -1小于
   */
  private Double elevatorAvgWorkCountDifferent;

}
