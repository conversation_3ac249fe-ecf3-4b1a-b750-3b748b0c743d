package com.easylinkin.linkappapi.machinery.controller;


import com.easylinkin.linkappapi.annotation.CommonOperateLogAnnotate;
import com.easylinkin.linkappapi.machinery.entity.TowerCranePosition;
import com.easylinkin.linkappapi.machinery.service.TowerCranePositionService;
import com.easylinkin.linkappapi.machinery.vo.TowerCranePositionVO;
import com.easylinkin.linkappapi.operatelog.constant.LogConstant.LogModule;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 塔吊在图纸上的位置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@RestController
@RequestMapping("/machinery/towerCranePosition")
@Api(tags = "塔吊在图纸上的位置")
public class TowerCranePositionController {

  @Autowired
  private TowerCranePositionService towerCranePositionService;

  /**
   * 查询所有数据
   *
   * @return 所有数据
   */
  @PostMapping("getList")
  @ApiOperation("查询所有数据")
  public RestMessage getList() {
    List<TowerCranePositionVO> record = towerCranePositionService.selectAll();
    return RestBuilders.successBuilder(record).build();
  }

  /**
   * 保存打点数据
   *
   * @param devicePositions 实体对象
   */
  @PostMapping
  @ApiOperation("保存打点数据")
  @CommonOperateLogAnnotate(module = LogModule.DASHBOARD_SETTING, desc = "群塔防碰撞设置")
  public RestMessage saveAll(@RequestBody List<TowerCranePosition> positions) {
    return RestBuilders.successBuilder()
        .success((this.towerCranePositionService.saveAll(positions))).build();
  }

  /**
   * 查询所有数据（包含告警信息）
   *
   * @return 所有数据
   */
  @PostMapping("getListIncludeCollisionAlarmRecord")
  @ApiOperation("查询所有数据（包含告警信息）")
  public RestMessage getListIncludeCollisionAlarmRecord() {
    List<TowerCranePositionVO> record = towerCranePositionService
        .getListIncludeCollisionAlarmRecord();
    return RestBuilders.successBuilder(record).build();
  }
}
