package com.easylinkin.linkappapi.machinery.controller.enterprise;

import com.easylinkin.linkappapi.common.utils.DateUtil;
import com.easylinkin.linkappapi.machinery.service.EnterpriseMachineryService;
import com.easylinkin.linkappapi.machinery.vo.ElevatorStatisticsVO;
import com.easylinkin.linkappapi.machinery.vo.ElevatorTopVO;
import com.easylinkin.linkappapi.machinery.vo.MachineryDeviceVo;
import com.easylinkin.linkappapi.machinery.vo.MachineryStatisticsVO;
import com.easylinkin.linkappapi.machinery.vo.MachineryWarnGroupTypeVO;
import com.easylinkin.linkappapi.machinery.vo.ProjectStatisticsVO;
import com.easylinkin.linkappapi.machinery.vo.ProjectWarnStatsticsVO;
import com.easylinkin.linkappapi.machinery.vo.TowerCraneStatisticsVO;
import com.easylinkin.linkappapi.machinery.vo.TowerCraneTopVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/07
 * @description 【企业级大屏】机械管理
 */
@RestController
@RequestMapping("enterpriseMachinery")
@Api(tags = "企业级大屏机械管理")
public class EnterpriseMachineryController {

  @Autowired
  private EnterpriseMachineryService enterpriseMachineryService;

  /**
   * 机械统计
   * @param statisticsVO
   * @return
   */
  @PostMapping("getMachineryStatistics")
  @ApiOperation("机械统计")
  public RestMessage getMachineryStatistics(){
    MachineryStatisticsVO statistics = enterpriseMachineryService.getMachineryStatistics();
    return RestBuilders.successBuilder().data(statistics).build();
  }

  /**
   * 塔吊统计排行榜
   * @param statisticsVO
   * @return
   */
  @PostMapping("getTowerCraneStatisticsTop")
  @ApiOperation("塔吊统计排行榜")
  public RestMessage getTowerCraneStatisticsTop(@RequestBody TowerCraneStatisticsVO statisticsVO){
    List<TowerCraneTopVO> topVOList = enterpriseMachineryService.getTowerCraneStatisticsTop(statisticsVO);
    return RestBuilders.successBuilder().data(topVOList).build();
  }

  /**
   * 升降机统计排行榜
   * @param statisticsVO
   * @return
   */
  @PostMapping("getElevatorStatisticsTop")
  @ApiOperation("升降机统计排行榜")
  public RestMessage getElevatorStatisticsTop(@RequestBody ElevatorStatisticsVO statisticsVO){
    List<ElevatorTopVO> topVOList = enterpriseMachineryService.getElevatorStatisticsTop(statisticsVO);
    return RestBuilders.successBuilder().data(topVOList).build();
  }

  /**
   * 项目列表
   *
   * @return
   */
  @PostMapping("/selectProjectStatisticList")
  @ApiOperation("项目列表")
  public RestMessage selectProjectStatisticList(@RequestBody ProjectStatisticsVO projectStatisticsVO) {
    projectStatisticsVO.setStartTime(DateUtil.getYYYYMMDDHHMMSSDate(DateUtil.getCurrentDayMixDate()));
    projectStatisticsVO.setEndTime(DateUtil.getYYYYMMDDHHMMSSDate(DateUtil.getCurrentDayMaxDate()));
    List<ProjectStatisticsVO> list = enterpriseMachineryService.selectProjectStatisticList(projectStatisticsVO);
    return RestBuilders.successBuilder().data(list).build();
  }

  @PostMapping("/countMachineryWarnGroupType")
  @ApiOperation("机械告警根据告警名称分组")
  public RestMessage countMachineryWarnGroupType(@RequestBody MachineryDeviceVo machineryDeviceVo){
    //参数验证
    Assert.notNull(machineryDeviceVo.getTypeCode(),"typeCode不能为空");
    List<MachineryWarnGroupTypeVO> list = enterpriseMachineryService.countMachineryWarnGroupType(machineryDeviceVo);
    return RestBuilders.successBuilder().data(list).build();
  }

  /**
   * 项目告警排行
   *
   * @return
   */
  @PostMapping("/getProjectWarnStatisticTop")
  @ApiOperation("项目告警排行")
  public RestMessage getProjectWarnStatisticTop(@RequestBody MachineryDeviceVo machineryDeviceVo) {
    List<ProjectWarnStatsticsVO> list = enterpriseMachineryService.getProjectWarnStatisticTop(machineryDeviceVo);
    return RestBuilders.successBuilder().data(list).build();
  }


}
