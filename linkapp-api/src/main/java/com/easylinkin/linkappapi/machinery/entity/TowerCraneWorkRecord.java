package com.easylinkin.linkappapi.machinery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 塔吊工作循环记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_tower_crane_work_record")
public class TowerCraneWorkRecord extends Model<TowerCraneWorkRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 电子档案id
     */
    @TableField("machinery_id")
    private Integer machineryId;

    /**
     * 设备id
     */
    @TableField("device_code")
    private String deviceCode;

    /**
     * 租户id
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 最大吊重 单位t
     */
    @TableField("max_weight")
    private Double maxWeight;

    /**
     * 载重百分比 单位%
     */
    @TableField("weight_percentage")
    private Double weightPercentage;

    /**
     * 最大力矩 单位t·m
     */
    @TableField("max_torque")
    private Double maxTorque;

    /**
     * 力矩百分比 单位%
     */
    @TableField("torque_percentage")
    private Double torquePercentage;

    /**
     * 最大高度 单位m
     */
    @TableField("max_height")
    private Double maxHeight;

    /**
     * 最小高度 单位m
     */
    @TableField("min_height")
    private Double minHeight;

    /**
     * 最大幅度 单位m
     */
    @TableField("max_range")
    private Double maxRange;

    /**
     * 最小幅度 单位m
     */
    @TableField("min_range")
    private Double minRange;

    /**
     * 起吊点角度 单位°
     */
    @TableField("start_rotation")
    private Double startRotation;

    /**
     * 起吊点幅度 单位m
     */
    @TableField("start_range")
    private Double startRange;

    /**
     * 起吊点高度 单位m
     */
    @TableField("start_height")
    private Double startHeight;

    /**
     * 卸吊点角度 单位°
     */
    @TableField("end_rotation")
    private Double endRotation;

    /**
     * 卸吊点幅度 单位m
     */
    @TableField("end_range")
    private Double endRange;

    /**
     * 卸吊点高度 单位m
     */
    @TableField("end_height")
    private Double endHeight;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

  /**
   * 司机姓名
   */
  @TableField("driver_name1")
  private String driverName1;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
