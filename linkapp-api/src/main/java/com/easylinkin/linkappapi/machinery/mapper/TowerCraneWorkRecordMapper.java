package com.easylinkin.linkappapi.machinery.mapper;

import com.easylinkin.linkappapi.machinery.entity.TowerCraneWorkRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.machinery.vo.TowerCraneWorkStatisticsVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 塔吊工作循环记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-14
 */
public interface TowerCraneWorkRecordMapper extends BaseMapper<TowerCraneWorkRecord> {

    List<TowerCraneWorkRecord> getList(TowerCraneWorkStatisticsVO statisticsVO);

    List<TowerCraneWorkStatisticsVO> getStatisticsOrderByMachineryGlobal(TowerCraneWorkStatisticsVO statisticsVO);

    TowerCraneWorkStatisticsVO getStatisticsSum(TowerCraneWorkStatisticsVO statisticsVO);

    List<TowerCraneWorkStatisticsVO> getStatisticsOrderByMachineryTopGlobal(TowerCraneWorkStatisticsVO statisticsVO);

    List<TowerCraneWorkStatisticsVO> getStatisticsDetailByMachineryGlobal(TowerCraneWorkStatisticsVO statisticsVO);

    /**
     * 按月份统计
     * @param tenantId
     * @param time
     * @return
     */
    List<TowerCraneWorkRecord> find4Report(@Param("tenantId") String tenantId, @Param("time") Date time);
}
