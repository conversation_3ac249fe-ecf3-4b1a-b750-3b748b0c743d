package com.easylinkin.linkappapi.machinery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 塔吊位置打点配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_tower_crane_position_image")
public class TowerCranePositionImage extends Model<TowerCranePositionImage> {

  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /**
   * 租户id
   */
  @TableField("tenant_id_")
  private String tenantId;

  /**
   * 图纸url
   */
  @TableField("url_")
  private String url;

  /**
   * 图纸配置
   */
  @TableField("config_")
  private String config;

  /**
   * 创建日期
   */
  @TableField("create_time_")
  private Date createTime;

  /**
   * 修改时间
   */
  @TableField("modify_time_")
  private Date modifyTime;

  /**
   * 备注
   */
  @TableField("remark_")
  private String remark;


  @Override
  protected Serializable pkVal() {
    return this.id;
  }

}
