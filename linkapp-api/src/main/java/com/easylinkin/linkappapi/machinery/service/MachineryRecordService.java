package com.easylinkin.linkappapi.machinery.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.machinery.dto.MachineryRecordDTO;
import com.easylinkin.linkappapi.machinery.dto.MachineryUserLinkDTO;
import com.easylinkin.linkappapi.machinery.entity.MachineryParameter;
import com.easylinkin.linkappapi.machinery.entity.MachineryRecord;
import com.easylinkin.linkappapi.machinery.vo.MachineryDeviceVo;
import site.morn.rest.RestMessage;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 闸机表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface MachineryRecordService extends IService<MachineryRecord> {

  IPage<MachineryRecord> queryListByPage(RequestModel<MachineryRecord> requestModel, Boolean enterpriseFlag);

  /**
   * 增加
   * @param machineryRecordDTO
   */
  void insert(MachineryRecordDTO machineryRecordDTO);

  /**
   * 根据id编辑
   * @param machineryRecordDTO
   */
  void updateOne(MachineryRecordDTO machineryRecordDTO);

  /**
   * @Description: 查询设备特性参数
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  List<MachineryParameter> getParameter(String code);

  /**
   * @Description: 查询管理人员
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  List<MachineryUserLinkDTO> getUser(String code, String tenantId);

  /**
   * 获取电子档案IOT设备关联数据
   * <AUTHOR>
   * @date 2022/08/05 11:42
   */
  RestMessage iotDeviceRefData(MachineryDeviceVo machineryDeviceVo);

  /**
   * @Description: 关联绑定IOT设备
   * <AUTHOR>
   * @date 2022/08/05 11:42
   */
  RestMessage bindIotDevices(MachineryDeviceVo machineryDeviceVo);

  /**
   * @Description: 根据条件，查询
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  Map<String, List<MachineryRecord>> queryAppList(MachineryRecord machineryRecord);

  /**
   * 接触绑定IOT设备关系
   * @param ids 电子档案ids
   */
  void unbindIotDevice(Collection<Integer> ids);

  /**
   * 删除项目同时
   * @param map
   */
  void deleteByMap(Map<String, Object> map);
}
