package com.easylinkin.linkappapi.machinery.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/21
 * @description
 */
@Data
public class TowerCraneWorkStatisticsDTO {

  /**
   * 塔吊监测数：当前监测数,单位：台
   */
  private Integer towerCraneCount;
  /**
   * 在线数：当前监测设备在线数，单位：台
   */
  private Integer towerCraneOnlineCount;
  /**
   * 平均吊次：每台塔吊当前吊重循环次数之和累加/当前监测数,单位：次
   */
  private Double avgWorkCount;
  /**
   * 平均单次吊重：每台塔吊当前总吊重重量之和累加/今日总吊次之和
   */
  private Double avgCountWeight;
  /**
   * 平均吊重：每台塔吊当前总吊重重量之和累加/当前监测数，单位：t
   *         吊重重量：循环数据中的最大载重
   */
  private Double avgWorkWeight;
  /**
   * 平均工效:每台塔吊当前总吊重重量之和累加/总工作循环时间之和，单位：t/min
   *        吊重重量：循环数据中的最大载重
   *        循环时间之和：当前（每次循环数据中的结束时间-开始时间）之和
   *        时间以分钟核算
   */
  private Double avgWorkEfficiency;

}
