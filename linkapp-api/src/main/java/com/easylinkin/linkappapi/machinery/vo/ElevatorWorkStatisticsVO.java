package com.easylinkin.linkappapi.machinery.vo;

import com.easylinkin.linkappapi.machinery.dto.MachineryIotDeviceDto;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/08/15
 * @description
 */
@Data
public class ElevatorWorkStatisticsVO {

  private Integer machineryId;
  private String code;
  private String startTime;
  private String endTime;
  private Integer limitNum;
  private Set<String> tenantIds;
  private List<String> codeList;
  private List<MachineryIotDeviceDto> machineryIotList;
  /**
   * workCount 运行次数 weight 运行重量
   */
  private String orderColumn;
  private String tenantId;

  private String day;
  private String refDate;//yyyy-MM-dd格式的日期
  private String hour;//按小时维度统计的时候进行使用
  private String machineryName;
  private String deviceName;
  private Integer workCount;
  private Double weight;
  private Double period;//运行时长
  private String totalPeriod;//总运行时长，用最后一次循环作业结束时间减去第一次循环作业的开始时间
  private List<ElevatorWorkStatisticsVO> hourList;//小时汇总详情
  private Date firstStartTime;//第一次循环作业的开始时间
  private Date lastEndTime;//最后一次循环作业的结束时间
}
