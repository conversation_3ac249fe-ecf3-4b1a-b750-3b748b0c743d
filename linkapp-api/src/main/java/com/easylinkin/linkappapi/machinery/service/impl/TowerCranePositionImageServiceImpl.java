package com.easylinkin.linkappapi.machinery.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.machinery.entity.TowerCranePositionImage;
import com.easylinkin.linkappapi.machinery.mapper.TowerCranePositionImageMapper;
import com.easylinkin.linkappapi.machinery.service.TowerCranePositionImageService;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 塔吊位置打点配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Service
public class TowerCranePositionImageServiceImpl extends
    ServiceImpl<TowerCranePositionImageMapper, TowerCranePositionImage> implements
    TowerCranePositionImageService {

  @Resource
  private LinkappUserContextProducer linkappUserContextProducer;

  @Override
  public void saveImage(TowerCranePositionImage image) {
    String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
    //保存到数据库
    QueryWrapper<TowerCranePositionImage> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("tenant_id_", tenantId);
    List<TowerCranePositionImage> imageList = baseMapper
        .selectList(queryWrapper);
    if (imageList.size() > 0) {
      TowerCranePositionImage oldImage = imageList.get(0);
      oldImage.setConfig(image.getConfig());
      oldImage.setUrl(image.getUrl());
      oldImage.setModifyTime(new Date());
      baseMapper.updateById(oldImage);
    } else {
      image.setTenantId(tenantId);
      image.setCreateTime(new Date());
      image.setModifyTime(new Date());
      baseMapper.insert(image);
    }
  }

  @Override
  public TowerCranePositionImage getImage() {
    String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
    //从数据库取
    QueryWrapper<TowerCranePositionImage> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("tenant_id_", tenantId);
    List<TowerCranePositionImage> imageList = baseMapper
        .selectList(queryWrapper);
    if (imageList.size() > 0) {
      return imageList.get(0);
    } else {
      return null;
    }
  }
}
