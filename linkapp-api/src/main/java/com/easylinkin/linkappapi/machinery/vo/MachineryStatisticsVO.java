package com.easylinkin.linkappapi.machinery.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/08
 * @description
 */
@Data
public class MachineryStatisticsVO {
  // 机械数量统计
  /**
   * 机械总数
   */
  private Integer total;
  /**
   * 塔吊总数
   */
  private Integer towerCarneNum;
  /**
   * 升降机总数
   */
  private Integer elevatorNum;
  /**
   * 其他机械总数
   */
  private Integer other;

  // 塔吊分析汇总
  /**
   * 塔吊平均工效
   */
  private Double towerCarneAvgEfficiency;
  /**
   * 今日塔吊工效
   */
  private Double towerCarneTodayWorkEfficiency;
  /**
   * 塔吊日平均吊装次数
   */
  private Double towerCarneAvgDayWorkCount;
  /**
   * 今日吊装次数
   */
  private Integer towerCarneTodayWorkCount;

  // 升降机分析汇总
  /**
   * 升降机日平均工作次数
   */
  private Double elevatorAvgDayWorkCount;
  /**
   * 今日升降机工作次数
   */
  private Integer elevatorTodayWorkCount;
}
