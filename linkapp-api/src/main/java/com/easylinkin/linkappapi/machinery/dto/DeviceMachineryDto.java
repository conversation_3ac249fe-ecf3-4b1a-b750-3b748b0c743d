package com.easylinkin.linkappapi.machinery.dto;

import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.devicetype.entity.DeviceType;
import com.easylinkin.linkappapi.machinery.entity.MachineryDeviceRef;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DeviceMachineryDto extends Device {
    /**
     * 关联的IOT设备类型信息
     */
    private DeviceType deviceTypeInfo;

    /**
     * 关联信息
     */
    private MachineryDeviceRef machineryDeviceRef;
    /**
     * 设备类型名
     */
    private String unitTypeName;

    /**
     * iot设备类型（业务类型）
     */
    private String iotType;

}
