package com.easylinkin.linkappapi.machinery.controller;


import com.easylinkin.linkappapi.common.utils.DateUtil;
import com.easylinkin.linkappapi.machinery.service.TowerCraneCollisionAlarmRecordService;
import com.easylinkin.linkappapi.machinery.vo.CollisionAlarmStatisticsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 塔吊防碰撞记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-27
 */
@RestController
@RequestMapping("/machinery/towerCraneCollisionAlarmRecord")
@Api(tags = "塔吊防碰撞记录")
public class TowerCraneCollisionAlarmRecordController {

  @Autowired
  private TowerCraneCollisionAlarmRecordService service;

  /**
   * 今日塔吊碰撞报警统计
   */
  @PostMapping("todayStatistics")
  @ApiOperation("今日塔吊碰撞报警统计")
  public RestMessage todayStatistics(@RequestBody CollisionAlarmStatisticsVO statisticsVO) {
    statisticsVO.setStartTime(DateUtil.getYYYYMMDDHHMMSSDate(DateUtil.getCurrentDayMixDate()));
    statisticsVO.setEndTime(DateUtil.getYYYYMMDDHHMMSSDate(DateUtil.getCurrentDayMaxDate()));
    return RestBuilders.successBuilder().data(service.getList(statisticsVO)).build();
  }

  /**
   * 司机排行榜
   */
  @PostMapping("getCollisionAlarmStatisticsByDriveNameTop")
  @ApiOperation("司机排行榜")
  public RestMessage getCollisionAlarmStatisticsByDriveNameTop(
      @RequestBody CollisionAlarmStatisticsVO statisticsVO) {
    return RestBuilders.successBuilder()
        .data(service.getCollisionAlarmStatisticsByDriveNameTop(statisticsVO)).build();
  }
}
