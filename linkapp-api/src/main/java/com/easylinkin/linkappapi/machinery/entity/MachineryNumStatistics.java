package com.easylinkin.linkappapi.machinery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 机械监测设备数量统计 按天
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_machinery_num_statistics")
public class MachineryNumStatistics extends Model<MachineryNumStatistics> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型code
     */
    @TableField("type_code_")
    private String typeCode;

    /**
     * 机械数量
     */
    @TableField("machinery_num_")
    private Integer machineryNum;

    /**
     * 某天的统计 形式yyyyMMdd
     */
    @TableField("record_day")
    private String recordDay;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
