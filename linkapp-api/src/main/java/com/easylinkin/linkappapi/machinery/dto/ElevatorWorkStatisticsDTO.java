package com.easylinkin.linkappapi.machinery.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/21
 * @description
 */
@Data
public class ElevatorWorkStatisticsDTO {

  /**
   * 升降机监测数：当前监测数,单位：台
   */
  private Integer elevatorCount;
  /**
   * 在线数：当前监测设备在线数，单位：台
   */
  private Integer elevatorOnlineCount;
  /**
   * 平均循环次数：每台升降机当前循环次数之和累加/当前监测数,单位：次
   */
  private Double avgWorkCount;
  /**
   * 平均运行重量：每台升降机当前总运行重量之和累加/当前监测数，单位：kg
   * 运行重量：循环数据中的最大载重
   */
  private Double avgWorkWeight;

}
