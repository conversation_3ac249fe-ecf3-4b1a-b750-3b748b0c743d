package com.easylinkin.linkappapi.machinery.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.machinery.entity.TowerCraneCollisionAlarmRecord;
import com.easylinkin.linkappapi.machinery.vo.CollisionAlarmRecordVO;
import com.easylinkin.linkappapi.machinery.vo.CollisionAlarmStatisticsVO;
import java.util.List;

/**
 * <p>
 * 塔吊防碰撞记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-27
 */
public interface TowerCraneCollisionAlarmRecordService extends
    IService<TowerCraneCollisionAlarmRecord> {

  List<CollisionAlarmStatisticsVO> getCollisionAlarmStatisticsByDriveNameTop(
      CollisionAlarmStatisticsVO statisticsVO);

  List<CollisionAlarmRecordVO> getList(CollisionAlarmStatisticsVO statisticsVO);

}
