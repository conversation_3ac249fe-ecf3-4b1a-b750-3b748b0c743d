package com.easylinkin.linkappapi.machinery.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.machinery.dto.MachineryUserLinkDTO;
import com.easylinkin.linkappapi.machinery.entity.MachineryUserLink;

/**
 * <p>
 * 闸机表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface MachineryUserLinkService extends IService<MachineryUserLink> {

  IPage<MachineryUserLinkDTO> queryListByPage(RequestModel<MachineryUserLinkDTO> requestModel);

  /**
   * 增加
   * @param machineryUserLinkDTO
   */
  void insert(MachineryUserLinkDTO machineryUserLinkDTO);

  /**
   * 根据id编辑
   * @param machineryUserLinkDTO
   */
  void updateOne(MachineryUserLinkDTO machineryUserLinkDTO);
}
