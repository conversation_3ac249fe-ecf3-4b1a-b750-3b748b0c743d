package com.easylinkin.linkappapi.machinery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 升降机楼层驻留记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_elevator_floor_record")
public class ElevatorFloorRecord extends Model<ElevatorFloorRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 电子档案id
     */
    @TableField("machinery_id")
    private Integer machineryId;

    /**
     * 设备code
     */
    @TableField("device_code")
    private String deviceCode;

    /**
     * 租户id
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * 开门时间
     */
    @TableField("open_time")
    private Date openTime;

    /**
     * 关门时间
     */
    @TableField("close_time")
    private Date closeTime;

    /**
     * 开关状态 0-关，1-开
     */
    @TableField("door_status")
    private Integer doorStatus;

    /**
     * 高度 单位m
     */
    @TableField("height_")
    private Double height;

    /**
     * 单体id
     */
    @TableField("building_id")
    private Integer buildingId;

    /**
     * 单体名称
     */
    @TableField("building_name")
    private String buildingName;

    /**
     * 楼层id
     */
    @TableField("floor_id")
    private Integer floorId;

    /**
     * 楼层名称
     */
    @TableField("floor_name")
    private String floorName;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
