package com.easylinkin.linkappapi.machinery.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备电子档案
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_machinery_record")
public class MachineryRecord extends Model<MachineryRecord> {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String platformProjectName;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 租户id
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * 类型code
     */
    @TableField("type_code_")
    private String typeCode;

    /**
     * 设备编号
     */
    @TableField("code_")
    private String code;

    /**
     * 名称
     */
    @TableField("name_")
    private String name;

    /**
     * 是否自有设备(1是0否)
     */
    @TableField("is_had_")
    private Integer isHad;

    /**
     * 供应商
     */
    @TableField("supplier_")
    private String supplier;

    /**
     * 进场时间
     */
    @TableField("join_time_")
    private Date joinTime;

    /**
     * 规格型号
     */
    @TableField("model_")
    private String model;

    /**
     * 生产厂家
     */
    @TableField("manufactor_")
    private String manufactor;

    /**
     * 出厂编号
     */
    @TableField("manufactor_code_")
    private String manufactorCode;

    /**
     * 登记号
     */
    @TableField("init_code_")
    private String initCode;

    /**
     * 出厂日期
     */
    @TableField("manufactor_time_")
    private Date manufactorTime;

    /**
     * 是否特种设备(1是0否)
     */
    @TableField("is_special_")
    private Integer isSpecial;

    /**
     * 设备特性参数json
     */
    @TableField("parameter_")
    private String parameter;

    /**
     * 机械管理关联单体
     */
    @TableField("building_id")
    private Integer buildingId;

    /**
     * 安装偏差值
     */
    @TableField("install_offset")
    private Double installOffset;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * id集合
     * @return
     */
    @TableField(exist = false)
    private Set<Integer> ids;

    /**
     * 型号名称
     */
    @TableField(exist = false)
    private String typeCodeName;

    @TableField(exist = false)
    private String joinStartTime;

    @TableField(exist = false)
    private String joinEndTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
