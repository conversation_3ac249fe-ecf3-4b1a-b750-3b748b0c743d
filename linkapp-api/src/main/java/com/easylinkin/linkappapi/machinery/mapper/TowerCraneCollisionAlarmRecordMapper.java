package com.easylinkin.linkappapi.machinery.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.machinery.entity.TowerCraneCollisionAlarmRecord;
import com.easylinkin.linkappapi.machinery.vo.CollisionAlarmRecordVO;
import com.easylinkin.linkappapi.machinery.vo.CollisionAlarmStatisticsVO;
import java.util.List;

/**
 * <p>
 * 塔吊防碰撞记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-27
 */
public interface TowerCraneCollisionAlarmRecordMapper extends
    BaseMapper<TowerCraneCollisionAlarmRecord> {

  List<CollisionAlarmStatisticsVO> getCollisionAlarmStatisticsByDriveNameTop(
      CollisionAlarmStatisticsVO statisticsVO);

  List<TowerCraneCollisionAlarmRecord> getList(CollisionAlarmStatisticsVO statisticsVO);

  TowerCraneCollisionAlarmRecord getOneByRefRecordId(Long refRecordId);

  List<TowerCraneCollisionAlarmRecord> selectByMachinery(
      CollisionAlarmRecordVO collisionAlarmRecordVO);
}
