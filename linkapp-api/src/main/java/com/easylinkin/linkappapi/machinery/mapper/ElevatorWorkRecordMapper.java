package com.easylinkin.linkappapi.machinery.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.machinery.entity.ElevatorWorkRecord;
import com.easylinkin.linkappapi.machinery.vo.ElevatorWorkStatisticsVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 升降机工作循环记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
public interface ElevatorWorkRecordMapper extends BaseMapper<ElevatorWorkRecord> {

  List<ElevatorWorkStatisticsVO> getStatisticsOrderByMachineryGlobal(ElevatorWorkStatisticsVO statisticsVO);

  List<ElevatorWorkStatisticsVO> getStatisticsDetailByMachineryGlobal(ElevatorWorkStatisticsVO statisticsVO);
  ElevatorWorkStatisticsVO getStatisticsSum(ElevatorWorkStatisticsVO statisticsVO);

  List<ElevatorWorkStatisticsVO> getStatisticsOrderByMachineryTopGlobal(ElevatorWorkStatisticsVO statisticsVO);

  /**
   * 按月份统计
   * @param tenantId
   * @param time
   * @return
   */
  List<ElevatorWorkRecord> find4Report(@Param("tenantId") String tenantId, @Param("time") Date time);
}
