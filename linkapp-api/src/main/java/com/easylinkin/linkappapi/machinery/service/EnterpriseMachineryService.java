package com.easylinkin.linkappapi.machinery.service;

import com.easylinkin.linkappapi.machinery.vo.ElevatorStatisticsVO;
import com.easylinkin.linkappapi.machinery.vo.ElevatorTopVO;
import com.easylinkin.linkappapi.machinery.vo.MachineryDeviceVo;
import com.easylinkin.linkappapi.machinery.vo.MachineryStatisticsVO;
import com.easylinkin.linkappapi.machinery.vo.MachineryWarnGroupTypeVO;
import com.easylinkin.linkappapi.machinery.vo.ProjectStatisticsVO;
import com.easylinkin.linkappapi.machinery.vo.ProjectWarnStatsticsVO;
import com.easylinkin.linkappapi.machinery.vo.TowerCraneTopVO;
import com.easylinkin.linkappapi.machinery.vo.TowerCraneStatisticsVO;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/07
 * @description
 */
public interface EnterpriseMachineryService {

  List<TowerCraneTopVO> getTowerCraneStatisticsTop(TowerCraneStatisticsVO statisticsVO);

  List<ElevatorTopVO> getElevatorStatisticsTop(ElevatorStatisticsVO statisticsVO);

  MachineryStatisticsVO getMachineryStatistics();

  List<ProjectStatisticsVO> selectProjectStatisticList(ProjectStatisticsVO projectStatisticsVO);

  List<MachineryWarnGroupTypeVO> countMachineryWarnGroupType(MachineryDeviceVo machineryDeviceVo);

  List<ProjectWarnStatsticsVO> getProjectWarnStatisticTop(MachineryDeviceVo machineryDeviceVo);
}
