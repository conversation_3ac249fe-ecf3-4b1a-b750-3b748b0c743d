package com.easylinkin.linkappapi.machinery.dto;

import com.easylinkin.linkappapi.machinery.entity.MachineryType;
import com.easylinkin.linkappapi.machinery.entity.MachineryUserType;
import java.util.List;
import lombok.Data;

/**
 * @Author: kan <PERSON><PERSON>
 * @Date: 2022/8/4 09:53
 * @Description:
 */
@Data
public class MachineryUserTypeDTO extends MachineryUserType {
  /**
   * 设备类别
   */
  private List<MachineryType> machineryTypes;

  /**
   * 关联人员数量
   */
  private int count;
}
