package com.easylinkin.linkappapi.machinery.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.machinery.entity.ElevatorFloorRecord;
import com.easylinkin.linkappapi.machinery.vo.ElevatorFloorStatisticsVO;
import java.util.List;

/**
 * <p>
 * 升降机楼层驻留记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
public interface ElevatorFloorRecordService extends IService<ElevatorFloorRecord> {

  List<ElevatorFloorStatisticsVO> getStatisticsOrderByFloorTop(ElevatorFloorStatisticsVO statisticsVO);
}
