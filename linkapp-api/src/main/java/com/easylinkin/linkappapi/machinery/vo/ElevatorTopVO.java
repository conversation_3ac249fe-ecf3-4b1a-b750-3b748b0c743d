package com.easylinkin.linkappapi.machinery.vo;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/08
 * @description
 */
@Data
public class ElevatorTopVO {

  public ElevatorTopVO(String projectName, Double projectAmount, Date createTime,
      Double workCount, Double weight) {
    this.projectName = projectName;
    this.projectAmount = projectAmount;
    this.createTime = createTime;
    this.workCount = workCount;
    this.weight = weight;
  }

  /**
   * 项目名称
   */
  private String projectName;

  /**
   * 项目金额
   */
  private Double projectAmount;

  /**
   * 项目创建时间
   */
  private Date createTime;
  /**
   * 工作次数
   */
  private Double workCount;
  /**
   * 运行重量
   */
  private Double weight;
}
