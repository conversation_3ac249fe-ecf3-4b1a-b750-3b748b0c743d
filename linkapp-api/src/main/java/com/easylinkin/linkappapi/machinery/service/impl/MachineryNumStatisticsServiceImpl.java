package com.easylinkin.linkappapi.machinery.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.machinery.dto.MachineryIotDeviceDto;
import com.easylinkin.linkappapi.machinery.entity.MachineryNumStatistics;
import com.easylinkin.linkappapi.machinery.mapper.MachineryNumStatisticsMapper;
import com.easylinkin.linkappapi.machinery.service.MachineryNumStatisticsService;
import com.easylinkin.linkappapi.machinery.vo.MachineryDeviceVo;
import com.easylinkin.linkappapi.shigongyun.service.MachineryMonitorBiService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 机械监测设备数量统计 按天 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-16
 */
@Service
public class MachineryNumStatisticsServiceImpl extends ServiceImpl<MachineryNumStatisticsMapper, MachineryNumStatistics> implements MachineryNumStatisticsService {

  @Resource
  private MachineryMonitorBiService machineryMonitorBiService;

  @Override
  public Integer findMachineryNumByDay(String day, String typeCode) {
    QueryWrapper<MachineryNumStatistics> qw = new QueryWrapper<>();
    qw.eq("type_code_",typeCode).eq("record_day",day);
    MachineryNumStatistics machineryNumStatistics = baseMapper.selectOne(qw);
    if(null != machineryNumStatistics){
      return machineryNumStatistics.getMachineryNum();
    }
    return 0;
  }

//  @Scheduled(cron = "0 0/1 * * * ?")// TODO 上线需要恢复
  @Scheduled(cron = "0 13 0 * * ?")//每天0点13分执行
  public void countMachinery(){
    try {
      Date yesterday = DateUtil.yesterday();
      String day = DateUtil.format(yesterday,"yyyyMMdd");
      // 1.查询所有的机械
      MachineryDeviceVo machineryDeviceVo = new MachineryDeviceVo();
      machineryDeviceVo.setTypeCode("QUIP_TSQZJ_01");
      List<MachineryIotDeviceDto> towerCraneMachineryIotDeviceDtos = machineryMonitorBiService
              .selectMachineryAndDeviceByCondition(machineryDeviceVo);
      if (CollectionUtil.isNotEmpty(towerCraneMachineryIotDeviceDtos)) {
        int size = towerCraneMachineryIotDeviceDtos.size();
        QueryWrapper<MachineryNumStatistics> qw = new QueryWrapper<>();
        qw.eq("type_code_","QUIP_TSQZJ_01").eq("record_day",day);
        List<MachineryNumStatistics> machineryNumStatisticsList = baseMapper.selectList(qw);
        if(CollectionUtil.isEmpty(machineryNumStatisticsList)){
          MachineryNumStatistics towerCraneStatistics = new MachineryNumStatistics();
          towerCraneStatistics.setRecordDay(day);
          towerCraneStatistics.setMachineryNum(size);
          towerCraneStatistics.setTypeCode("QUIP_TSQZJ_01");
          towerCraneStatistics.setCreateTime(new Date());
          baseMapper.insert(towerCraneStatistics);
        }
      }

      machineryDeviceVo.setTypeCode("QUIP_SJJ_02");
      List<MachineryIotDeviceDto> elevatorMachineryIotDeviceDtos = machineryMonitorBiService
              .selectMachineryAndDeviceByCondition(machineryDeviceVo);
      if (CollectionUtil.isNotEmpty(elevatorMachineryIotDeviceDtos)) {
        int size = elevatorMachineryIotDeviceDtos.size();
        QueryWrapper<MachineryNumStatistics> qw = new QueryWrapper<>();
        qw.eq("type_code_","QUIP_SJJ_02").eq("record_day",day);
        List<MachineryNumStatistics> machineryNumStatisticsList = baseMapper.selectList(qw);
        if(CollectionUtil.isEmpty(machineryNumStatisticsList)){
          MachineryNumStatistics elevatorStatistics = new MachineryNumStatistics();
          elevatorStatistics.setRecordDay(day);
          elevatorStatistics.setMachineryNum(size);
          elevatorStatistics.setTypeCode("QUIP_SJJ_02");
          elevatorStatistics.setCreateTime(new Date());
          baseMapper.insert(elevatorStatistics);
        }
      }
    }catch (Exception e){
      e.printStackTrace();
    }
  }
}
