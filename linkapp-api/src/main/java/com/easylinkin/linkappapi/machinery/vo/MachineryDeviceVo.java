package com.easylinkin.linkappapi.machinery.vo;

import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.machinery.entity.MachineryRecord;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 电子档案iot设备vo
 * <AUTHOR>
 */
@Data
public class MachineryDeviceVo extends MachineryRecord {
    /**
     * 绑定，0未绑定，1已绑定
     */
    private Integer binded;
    /**
     * 物模型编码list
     */
    private List<String> unitCodeList;

    /**
     * 取消绑定设备list
     */
    private List<Device> cancleBindDeviceList;
    /**
     * 要绑定的设备list
     */
    private List<Device> bindDeviceList;

    private Date startTime;

    private Date endTime;
    /**
     * 设备信息
     */
    private Device device;
    /**
     * 0:离线; 1:在线 新增默认离线
     */
    private Integer onlineState;

    private List<String> deviceTypeNameList;

    private String projectCode;

}
