package com.easylinkin.linkappapi.machinery.controller;

import com.easylinkin.linkappapi.machinery.dto.MachineryUserTypeDTO;
import com.easylinkin.linkappapi.machinery.entity.MachineryType;
import com.easylinkin.linkappapi.machinery.entity.MachineryUserType;
import com.easylinkin.linkappapi.machinery.service.MachineryUserTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * @author: kan yuan<PERSON>
 * @Date: 2020/11/04 11:42
 * @Description: 人员类别管理
 */
@RestController
@RequestMapping("machineryUserType")
@Api(tags = "人员类别管理")
public class MachineryUserTypeController {
  
  @Autowired
  private MachineryUserTypeService machineryUserTypeService;

  /**
   * @Description: 增加人员类别
   * <AUTHOR> yuan<PERSON>
   * @date 2020/11/04 11:42
   */
  @PostMapping
  @ApiOperation("增加人员类别")
  public RestMessage insert(@RequestBody MachineryUserType machineryUserType){
    //参数验证
    machineryUserTypeService.insert(machineryUserType);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 增加设备类别
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("deveces")
  @ApiOperation("增加设备类别")
  public RestMessage insertDevices(@RequestBody MachineryUserTypeDTO machineryUserTypeDTO){
    //参数验证
    Assert.notNull(machineryUserTypeDTO.getId(),"id不能为空");
    Assert.notEmpty(machineryUserTypeDTO.getMachineryTypes(),"设备类别不能为空");
    machineryUserTypeService.insertDevices(machineryUserTypeDTO);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 删除设备类别
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @DeleteMapping("devices")
  @ApiOperation("删除设备类别")
  public RestMessage deleteDevices(@RequestBody MachineryUserType machineryUserType){
    //参数验证
    Assert.notNull(machineryUserType.getId(),"id不能为空");
    Assert.notNull(machineryUserType.getDevices(),"设备类别不能为空");
    machineryUserTypeService.deleteDevices(machineryUserType);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 删除人员类别（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @DeleteMapping
  @ApiOperation("删除人员类别（包含批量删除）")
  public RestMessage delBatch(@RequestBody MachineryUserType machineryUserType){
    Assert.notEmpty(machineryUserType.getIds(),"id不能为空");
    machineryUserTypeService.delBatch(machineryUserType.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑人员类别
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PutMapping
  @ApiOperation("编辑人员类别")
  public RestMessage updateById(@RequestBody MachineryUserType machineryUserType){
    Assert.notNull(machineryUserType.getId(),"id不能为空");
    machineryUserTypeService.updateOne(machineryUserType);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询人员类别详情
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询人员类别详情")
  public RestMessage findById(@PathVariable("id")Integer id) {
    Assert.notNull(id,"id不能为空");
    MachineryUserTypeDTO machineryUserTypeDTO = machineryUserTypeService.findById(id);
    return RestBuilders.successBuilder().data(machineryUserTypeDTO).build();
  }

  /**
   * @Description: 根据条件，查询
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryList(@RequestBody MachineryUserType machineryUserType){
    List<MachineryUserTypeDTO> record =  machineryUserTypeService.queryList(machineryUserType);
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * 查询可添加的设备类型
   */
  @PostMapping("typeList")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryTypeList(@RequestBody MachineryType machineryType){
    List<MachineryType> record =  machineryUserTypeService.queryTypeList(machineryType);
    return RestBuilders.successBuilder().data(record).build();
  }
}

