package com.easylinkin.linkappapi.machinery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

/**
 * 电子档案与设备关联信息
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_machinery_device_ref")
public class MachineryDeviceRef extends Model<MachineryDeviceRef> {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 电子档案id
     */
    @Column(name = "machinery_id")
    private Integer machineryId;

    /**
     * 电子档案设备编码（冗余）
     */
    @Column(name = "machinery_code")
    private String machineryCode;

    /**
     * 设备id
     */
    @Column(name = "device_id")
    private String deviceId;

    /**
     * 设备编码（冗余）
     */
    @Column(name = "device_code")
    private String deviceCode;

    /**
     * 是否删除，0删除，1存在
     */
    @TableLogic(value = "1", delval = "0")
    @Column(name = "delete_state")
    private Integer deleteState;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;

}