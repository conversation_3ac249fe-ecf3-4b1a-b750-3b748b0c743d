package com.easylinkin.linkappapi.machinery.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.machinery.entity.ElevatorFloorRecord;
import com.easylinkin.linkappapi.machinery.mapper.ElevatorFloorRecordMapper;
import com.easylinkin.linkappapi.machinery.service.ElevatorFloorRecordService;
import com.easylinkin.linkappapi.machinery.vo.ElevatorFloorStatisticsVO;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 升降机楼层驻留记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Service
public class ElevatorFloorRecordServiceImpl extends ServiceImpl<ElevatorFloorRecordMapper, ElevatorFloorRecord> implements ElevatorFloorRecordService {

  @Resource
  private LinkappUserContextProducer linkappUserContextProducer;

  @Override
  public List<ElevatorFloorStatisticsVO> getStatisticsOrderByFloorTop(
      ElevatorFloorStatisticsVO statisticsVO) {
    String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
    statisticsVO.setTenantId(tenantId);
    return baseMapper.getStatisticsOrderByFloorTop(statisticsVO);
  }
}
