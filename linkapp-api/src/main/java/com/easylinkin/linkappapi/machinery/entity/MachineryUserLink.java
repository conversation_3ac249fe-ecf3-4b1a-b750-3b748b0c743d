package com.easylinkin.linkappapi.machinery.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 人员类别关联人员
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_machinery_user_link")
public class MachineryUserLink extends Model<MachineryUserLink> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 租户id
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * 人员类别id
     */
    @TableField("type_id_")
    private Integer typeId;

    /**
     * 用户id
     */
    @TableField("user_id_")
    private String userId;

    /**
     * 证书id(多个逗号分隔)
     */
    @TableField("certificate_id_")
    private String certificateId;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * id集合
     * @return
     */
    @TableField(exist = false)
    private Set<Integer> ids;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
