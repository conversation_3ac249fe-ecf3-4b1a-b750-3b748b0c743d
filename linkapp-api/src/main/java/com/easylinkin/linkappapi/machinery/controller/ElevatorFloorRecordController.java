package com.easylinkin.linkappapi.machinery.controller;


import com.easylinkin.linkappapi.machinery.service.ElevatorFloorRecordService;
import com.easylinkin.linkappapi.machinery.vo.ElevatorFloorStatisticsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * <p>
 * 升降机楼层驻留记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@RestController
@RequestMapping("/machinery/elevatorFloorRecord")
@Api(tags = "升降机楼层驻留记录管理")
public class ElevatorFloorRecordController {

  @Autowired
  private ElevatorFloorRecordService elevatorFloorRecordService;

  /**
   * 驻留楼层排行榜
   * @param statisticsVO
   * @return
   */
  @PostMapping("getStatisticsOrderByFloorTop")
  @ApiOperation("驻留楼层排行榜")
  public RestMessage getStatisticsOrderByMachineryTop(@RequestBody ElevatorFloorStatisticsVO statisticsVO){
    //参数验证
    Assert.notNull(statisticsVO.getMachineryId(),"id不能为空");
    Assert.notNull(statisticsVO.getCode(),"设备类别不能为空");
    return RestBuilders.successBuilder().data(elevatorFloorRecordService.getStatisticsOrderByFloorTop(statisticsVO)).build();
  }
}
