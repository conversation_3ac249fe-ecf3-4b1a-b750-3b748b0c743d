package com.easylinkin.linkappapi.machinery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 塔吊在图纸上的位置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_tower_crane_position")
public class TowerCranePosition extends Model<TowerCranePosition> {

  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /**
   * 电子档案id
   */
  @TableField("machinery_id")
  private Integer machineryId;

  /**
   * 设备code
   */
  @TableField("device_code")
  private String deviceCode;

  /**
   * 租户id
   */
  @TableField("tenant_id_")
  private String tenantId;

  /**
   * x
   */
  @TableField("position_x")
  private Float positionX;

  /**
   * y
   */
  @TableField("position_y")
  private Float positionY;

  /**
   * 圆半径
   */
  @TableField("circle_radius_")
  private Float circleRadius;

  /**
   * 创建时间
   */
  @TableField("create_time")
  private Date createTime;

  /**
   * 创建人
   */
  @TableField("creator")
  private String creator;

  /**
   * 修改人
   */
  @TableField("modifier")
  private String modifier;

  /**
   * 修改时间
   */
  @TableField("modify_time")
  private Date modifyTime;


  @Override
  protected Serializable pkVal() {
    return this.id;
  }

}
