package com.easylinkin.linkappapi.devicedata.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.alarm.entity.Alarm;
import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.device.service.DeviceService;
import com.easylinkin.linkappapi.deviceattribute.entity.DeviceAttribute;
import com.easylinkin.linkappapi.deviceattribute.service.DeviceAttributeService;
import com.easylinkin.linkappapi.deviceattributestatus.entity.DeviceAttributeStatus;
import com.easylinkin.linkappapi.deviceattributestatus.service.DeviceAttributeStatusService;
import com.easylinkin.linkappapi.devicedata.service.DeviceDataService;
import com.easylinkin.linkappapi.elasticsearch.entity.AiCameraVo;
import com.easylinkin.linkappapi.elasticsearch.service.IEService;
import com.easylinkin.linkappapi.machinery.vo.MachineryDeviceVo;
import com.easylinkin.linkappapi.shigongyun.service.MachineryMonitorBiService;
import com.easylinkin.linkappapi.tenant.entity.LinkappTenant;
import com.easylinkin.linkappapi.tenant.mapper.LinkappTenantMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@Api("设备数据获取")
@RequestMapping("/deviceData")
@Slf4j
public class DeviceDataController {

    @Resource
    private DeviceService deviceService;

    @Resource
    private DeviceAttributeStatusService deviceAttributeStatusService;

    @Resource
    private DeviceDataService deviceDataService;

    @Resource
    private LinkappTenantMapper linkappTenantMapper;

    @Resource
    private MachineryMonitorBiService machineryMonitorBiService;

    @Resource
    private IEService ieService;

    @Resource
    private DeviceAttributeService deviceAttributeService;

    @ApiOperation("设备管理列表查询")
    @PostMapping("/getPage")
    public RestMessage getPage(@RequestBody RequestModel<Device> requestModel) {
        Device customQueryParams = requestModel.getCustomQueryParams();
        Assert.notNull(customQueryParams, "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        // 通过城建项目编号查询施工云项目租户ID
        QueryWrapper<LinkappTenant> qw = new QueryWrapper<>();
        qw.eq("project_code_", requestModel.getCustomQueryParams().getProjectCode());
        String tenantId = linkappTenantMapper.selectOne(qw).getId();
        requestModel.getCustomQueryParams().setTenantId(tenantId);
        IPage<Device> record = deviceService.selectDevicesPage(requestModel.getPage(), customQueryParams);
        return RestBuilders.successBuilder().data(record).build();
    }

    @ApiOperation("获取项目设备状态")
    @PostMapping("/getDeviceDataByProject")
    public RestMessage getDeviceDataByProject(@RequestBody Map<String, String> map) {
        if (StringUtils.isBlank(map.get("projectCode"))) {
            throw new BusinessException("项目编号不能为空");
        }
        if (StringUtils.isBlank(map.get("deviceCode"))) {
            throw new BusinessException("设备编号不能为空");
        }
        List<DeviceAttributeStatus> deviceDataByProject = deviceAttributeStatusService.getDeviceDataByProject(map);
        return RestBuilders.successBuilder().data(deviceDataByProject).build();
    }

    @ApiOperation("获取项目设备详情")
    @PostMapping("/getDeviceDetail")
    public RestMessage getDeviceDataByProject2(@RequestBody Map<String, String> map) {
        if (StringUtils.isBlank(map.get("id"))) {
            throw new BusinessException("设备id不能为空");
        }
        if (StringUtils.isBlank(map.get("projectCode"))) {
            throw new BusinessException("项目编号不能为空");
        }
        if (StringUtils.isBlank(map.get("deviceCode"))) {
            throw new BusinessException("设备编号不能为空");
        }

        return deviceDataService.getDeviceDetail(map);
    }

    @ApiOperation("设备属性历史数据流水查询")
    @PostMapping("/getHistoricDataPageFromEs")
    public RestMessage getHistoricDataPageFromEs(@RequestBody RequestModel<DeviceAttributeStatus> requestModel) {
        Assert.notNull(requestModel, "参数为空");
        Assert.notNull(requestModel.getPage(), "分页参数为空");
        Assert.notNull(requestModel.getCustomQueryParams(), "参数不完整");

        // 通过城建项目编号查询施工云项目租户ID
        QueryWrapper<LinkappTenant> qw = new QueryWrapper<>();
        qw.eq("project_code_", requestModel.getCustomQueryParams().getProjectCode());
        String tenantId = linkappTenantMapper.selectOne(qw).getId();
        requestModel.getCustomQueryParams().setTenantId(tenantId);
        return RestBuilders.successBuilder().data(deviceAttributeStatusService.getHistoricDataFromEs(requestModel)).build();
    }

    @PostMapping("/getAlarmPage")
    @ApiOperation("查询告警分页列表")
    public RestMessage getAlarmPage(@RequestBody RequestModel<Alarm> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getCustomQueryParams().getDevice(), "参数设备不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<Alarm> record = deviceDataService.getAlarmInfo(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * 获取机械Iot设备今日运行次数统计
     *
     * @param machineryDeviceVo 机械设备条件vo
     * @return 机械Iot设备今日运行次数统计
     */
    @PostMapping("/countMachineryOperationToday")
    @ApiOperation("获取机械Iot设备今日运行次数统计")
    public RestMessage countMachineryOperationToday(@RequestBody MachineryDeviceVo machineryDeviceVo) {
        log.info("获取机械Iot设备今日运行次数统计");
        // 通过城建项目编号查询施工云项目租户ID
        QueryWrapper<LinkappTenant> qw = new QueryWrapper<>();
        qw.eq("project_code_", machineryDeviceVo.getProjectCode());
        String tenantId = linkappTenantMapper.selectOne(qw).getId();
        machineryDeviceVo.setTenantId(tenantId);
        return machineryMonitorBiService.countMachineryOperation(machineryDeviceVo);
    }

    /**
     * 获取机械Iot设备告警数量统计时间分组
     *
     * @param machineryDeviceVo 机械设备条件vo
     * @return 机械Iot设备类型告警数量统计
     */
    @PostMapping("/countMachineryWarnByDay")
    @ApiOperation("获取机械Iot设备告警数量统计时间分组")
    public RestMessage countMachineryWarnByDay(@RequestBody MachineryDeviceVo machineryDeviceVo) {
        log.info("获取机械Iot设备告警数量统计时间分组");
        // 通过城建项目编号查询施工云项目租户ID
        QueryWrapper<LinkappTenant> qw = new QueryWrapper<>();
        qw.eq("project_code_", machineryDeviceVo.getProjectCode());
        String tenantId = linkappTenantMapper.selectOne(qw).getId();
        machineryDeviceVo.setTenantId(tenantId);
        return machineryMonitorBiService.countMachineryWarnByDay(machineryDeviceVo);
    }

    @PostMapping("/queryAiCamera")
    @ApiOperation("获取AI预警信息")
    public RestMessage queryAiCamera(@RequestBody RequestModel<AiCameraVo> requestModel) {
        Assert.notNull(requestModel, "customQueryParams 不能为空");
        IPage<AiCameraVo> aiCameraVoIPage = ieService.queryAiAlarm(requestModel);
        return RestBuilders.successBuilder().data(aiCameraVoIPage).build();
    }

    @ApiOperation("根据查询条件获取全部设备属性列表")
    @PostMapping("/getAllAttributes")
    public RestMessage getAll(@RequestBody DeviceAttribute deviceAttribute) {
        QueryWrapper<LinkappTenant> qw = new QueryWrapper<>();
        qw.eq("project_code_", deviceAttribute.getProjectCode());
        String tenantId = linkappTenantMapper.selectOne(qw).getId();
        deviceAttribute.setTenantId(tenantId);
        return RestBuilders.successBuilder(deviceAttributeService.getAll(deviceAttribute)).build();
    }

}
