package com.easylinkin.linkappapi.jkmonior.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.jkmonior.entity.JkMonitorItemConfig;
import com.easylinkin.linkappapi.jkmonior.entity.vo.JkConfigVo;
import com.easylinkin.linkappapi.jkmonior.entity.vo.JkMonitorItemConfigVo;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * JkMonitorItemConfig表服务接口
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
public interface JkMonitorItemConfigService extends IService<JkMonitorItemConfig> {

    /**
     * 新增
     *
     * @param appJkMonitorItemConfig 实体对象
     * @return 操作结果
     */
    boolean saveOne(JkMonitorItemConfig appJkMonitorItemConfig);

    /**
     * 修改单条
     *
     * @param appJkMonitorItemConfig 实体对象
     * @return 修改结果
     */
    boolean updateOne(JkMonitorItemConfig appJkMonitorItemConfig);

    /**
     * 查询分页
     *
     * @param page                   分页对象
     * @param appJkMonitorItemConfig 分页参数对象
     * @return 查询分页结果
     */
    IPage<JkMonitorItemConfig> selectPage(Page page, JkMonitorItemConfig appJkMonitorItemConfig);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    JkMonitorItemConfig getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param appJkMonitorItemConfig 过滤条件实体对象
     * @param request                请求
     * @param response               响应
     */
    void export(JkMonitorItemConfig appJkMonitorItemConfig, HttpServletRequest request, HttpServletResponse response);

    /**
     * 获取监测项点信息
     *
     * @param jkMonitorItemConfigVo 实体对象
     * @return 统一出参
     */
    RestMessage getMonitorItemConfig(JkMonitorItemConfigVo jkMonitorItemConfigVo);

    /**
     * 同步获取最新测项、点信息
     *
     * @param jkMonitorItemConfigVo 实体对象
     * @return 统一出参
     */
    RestMessage syncMonitorItem(JkMonitorItemConfigVo jkMonitorItemConfigVo);
    /**
     * 测项点信息配置保存
     *
     * @param jkConfigVo 实体对象
     * @return 统一出参
     */
    RestMessage saveMonitorItemConfig(JkConfigVo jkConfigVo);
}

