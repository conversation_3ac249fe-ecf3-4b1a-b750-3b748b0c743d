package com.easylinkin.linkappapi.jkmonior.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_jk_monitor_config")
public class JkMonitorConfig extends Model<JkMonitorConfig> {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)

    private Integer id;

    /**
     * 租户id
     */

    private String tenantId;

    /**
     * 对接方项目id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectId;

    /**
     * 对接方名称
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String sourceProviderName;

    /**
     * 监测项接口
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String itemMonitorUrl;

    /**
     * 监测数据接口
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dataMonitorUrl;

    /**
     * 基坑概况
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;


    /**
     * 工艺技法介绍视频url
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String craftVideoUrl;
    /**
     * 工艺技法介绍视频名称
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String craftVideoName;

    /**
     * 现场视频设备id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String siteVideoDeviceId;

    /**
     * 基坑平面布置图url
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String jkImg;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 修改时间
     */

    private Date modifyTime;

    /**
     * 是否删除，0删除，1存在
     */

    private Integer deleteState;

}
