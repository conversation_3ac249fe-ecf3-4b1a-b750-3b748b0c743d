package com.easylinkin.linkappapi.jkmonior.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.jkmonior.entity.JkMonitorConfig;
import com.easylinkin.linkappapi.jkmonior.entity.vo.JkMonitorConfigVo;
import com.easylinkin.linkappapi.webcammanage.entity.DeviceAttributeStatus;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * JkMonitorConfig表服务接口
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
public interface JkMonitorConfigService extends IService<JkMonitorConfig> {

    /**
     * 新增
     *
     * @param appJkMonitorConfig 实体对象
     * @return 操作结果
     */
    boolean saveOne(JkMonitorConfig appJkMonitorConfig);

    /**
     * 修改单条
     *
     * @param appJkMonitorConfig 实体对象
     * @return 修改结果
     */
    boolean updateOne(JkMonitorConfig appJkMonitorConfig);

    /**
     * 查询分页
     *
     * @param page               分页对象
     * @param appJkMonitorConfig 分页参数对象
     * @return 查询分页结果
     */
    IPage<JkMonitorConfig> selectPage(Page page, JkMonitorConfig appJkMonitorConfig);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    JkMonitorConfig getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param appJkMonitorConfig 过滤条件实体对象
     * @param request            请求
     * @param response           响应
     */
    void export(JkMonitorConfig appJkMonitorConfig, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询租户基坑监测基础配置信息
     *
     * @return 统一出参
     */
    RestMessage getJkMonitorBaseConfig();

    /**
     * 获取摄像头设备列表
     *
     * @param requestModel 请求入参对象
     * @return 统一出参
     */
    IPage<DeviceAttributeStatus> getVideoList(Page page, Device requestModel);

    /**
     * 保存项目基坑监测基础配置
     *
     * @param jkMonitorConfigVo 实体对象
     * @return 新增结果
     */
    RestMessage saveBaseConfig(JkMonitorConfigVo jkMonitorConfigVo);
}

