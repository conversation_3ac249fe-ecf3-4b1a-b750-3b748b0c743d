package com.easylinkin.linkappapi.jkmonior.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.jkmonior.entity.JkMonitorConfig;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;

/**
 * JkMonitorConfig表数据库访问层
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
public interface JkMonitorConfigMapper extends BaseMapper<JkMonitorConfig> {


    /**
     * 查询分页
     *
     * @param page        分页参数对象
     * @param appJkMonitorConfig 过滤参数对象
     * @return 查询分页结果
     */
    IPage<JkMonitorConfig> selectPage(Page page, @Param("appJkMonitorConfig") JkMonitorConfig appJkMonitorConfig);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    JkMonitorConfig getOneById(@Param("id") Serializable id);
}

