package com.easylinkin.linkappapi.jkmonior.entity.vo;

import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.jkmonior.entity.JkMonitorConfig;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * JkMonitorConfig实体类Vo
 *
 * <AUTHOR>
 * @date 2022/10/28
 */

@Data
@Accessors(chain = true)
public class JkMonitorConfigVo extends JkMonitorConfig {
    /**
     * 创建时间
     */
    private String createTimeStr;

    /**
     * 现场视频设备信息
     */
    private Device siteVideoDevice;

    public JkMonitorConfigVo() {
    }

    public JkMonitorConfigVo(JkMonitorConfig appJkMonitorConfig) {
        //this.setName(appJkMonitorConfig.getName());
    }
}
