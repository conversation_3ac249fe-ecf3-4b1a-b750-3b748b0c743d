package com.easylinkin.linkappapi.jkmonior.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_jk_monitor_item_config")
public class JkMonitorItemConfig extends Model<JkMonitorItemConfig> {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    
    private Integer id;

    /**
     * 租户id
     */
    
    private String tenantId;

    /**
     * 类型，1测项，2测点
     */
    
    private Integer itemType;

    /**
     * 测项点编码
     */
    
    private String itemCode;

    /**
     * 测项点名称
     */
    
    private String itemName;

    /**
     * 父级id
     */
    
    private Integer parentId;

    /**
     * 父级ids
     */
    
    private String parentIds;

    /**
     * x坐标值
     */
    
    private Double xAxis;

    /**
     * y轴坐标值
     */
    
    private Double yAxis;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建时间
     */
    
    private Date createTime;

    /**
     * 修改时间
     */
    
    private Date modifyTime;

    /**
     * 是否删除，0删除，1存在
     */
    
    private Integer deleteState;

}
