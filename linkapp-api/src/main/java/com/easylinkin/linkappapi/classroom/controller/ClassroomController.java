package com.easylinkin.linkappapi.classroom.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.classroom.entity.ClassroomDevice;
import com.easylinkin.linkappapi.classroom.service.ClassroomService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.deviceservice.entity.DeviceServices;
import com.easylinkin.linkappapi.openapi.dto.ApiDownResultDTO;
import com.easylinkin.linkappapi.space.entity.LinkappArea;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/2 15:25
 */
@RestController
@RequestMapping("/classroom")
public class ClassroomController {

    @Resource
    private ClassroomService service;

    @PostMapping("getAreaDevicePage")
    public RestMessage getMonitorPageByDeviceUnit(@RequestBody RequestModel<LinkappArea> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<ClassroomDevice> record = service.getAreaDevicePage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

    @PostMapping("batchSend")
    public RestMessage batchSend(@RequestBody DeviceServices deviceServices) {
        Assert.notNull(deviceServices, "参数为空");
        Assert.notNull(deviceServices.getDownParameter(), "downParameter参数为空");
        ApiDownResultDTO send = service.batchSend(deviceServices);
        if(send != null && "0".equals(send.getCode())){
            return RestBuilders.successBuilder().data(send).build();
        }
        String resultMsg = "未获取到调用结果";
        if(send != null){
            resultMsg = StrUtil.isEmpty(send.getMessage()) ? send.getMsg() : send.getMessage();
        }
        return RestBuilders.failureBuilder().message(resultMsg).data(send).build();
    }

    @PostMapping("send")
    public RestMessage send(@RequestBody DeviceServices deviceServices) {
        Assert.notNull(deviceServices, "参数为空");
        Assert.notNull(deviceServices.getDownParameter(), "downParameter参数为空");
        ApiDownResultDTO send = service.send(deviceServices);
        if(send != null && "0".equals(send.getCode())){
            return RestBuilders.successBuilder().data(send).build();
        }
        String resultMsg = "未获取到调用结果";
        if(send != null){
            resultMsg = StrUtil.isEmpty(send.getMessage()) ? send.getMsg() : send.getMessage();
        }
        return RestBuilders.failureBuilder().message(resultMsg).data(send).build();
    }


}
