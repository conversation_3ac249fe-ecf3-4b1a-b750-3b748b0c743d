package com.easylinkin.linkappapi.classroom.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.classroom.entity.GfdRoleRefArea;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.space.entity.LinkappArea;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * 国防大角色关联区域 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-24
 */
public interface GfdRoleRefAreaMapper extends BaseMapper<GfdRoleRefArea> {

    List<LinkappArea> getAreasByRoles(@Param("roleIds") List<Long> roleIds);

    /**
     * 获取国科大权限分配分页数据
     * @param page
     * @param roleRefArea
     * @return
     */
    IPage<GfdRoleRefArea> getRoleRefAreaPage(@Param("page") Page page, @Param("item")GfdRoleRefArea roleRefArea);
}
