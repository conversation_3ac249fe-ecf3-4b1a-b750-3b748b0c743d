package com.easylinkin.linkappapi.classroom.entity;

import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.space.vo.SpaceTreeVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/2 16:02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClassroomDevice extends SpaceTreeVo {

    private List<Device> deviceList;

}
