package com.easylinkin.linkappapi.classroom.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.linkappapi.classroom.entity.GfdRoleRefArea;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import com.easylinkin.linkappapi.space.entity.LinkappArea;
import com.easylinkin.linkappapi.space.vo.SpaceTreeVo;

import java.util.List;

/**
 * <p>
 * 国防大角色关联区域 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-24
 */
public interface GfdRoleRefAreaService extends IService<GfdRoleRefArea> {

    List<LinkappArea> getAreasByUser(LinkappUser linkappUser);

    List<SpaceTreeVo> selectLinkappAreaTreeList(LinkappArea linkappArea);

    String saveRoleRefArea(GfdRoleRefArea roleRefArea);

    String updateRoleRefArea(GfdRoleRefArea roleRefArea);

    void deleteRoleRefArea(String deleteIds);

    IPage<GfdRoleRefArea> getRoleRefAreaPage(Page page, GfdRoleRefArea roleRefArea);

    List<LinkappArea> getAreaListByRole(LinkappUser user);



}
