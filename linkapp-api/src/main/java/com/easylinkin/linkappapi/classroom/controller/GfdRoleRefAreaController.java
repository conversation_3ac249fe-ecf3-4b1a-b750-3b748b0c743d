package com.easylinkin.linkappapi.classroom.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.classroom.entity.GfdRoleRefArea;
import com.easylinkin.linkappapi.classroom.service.GfdRoleRefAreaService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.meterbilling.entity.ResidentInfo;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import com.easylinkin.linkappapi.space.entity.LinkappArea;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;

/**
 * <p>
 * 国防大角色关联区域 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-24
 */
@RestController
@RequestMapping("/classroom/gfdRoleRefArea")
public class GfdRoleRefAreaController {

    @Resource
    private GfdRoleRefAreaService service;

    @PostMapping("getAreasByUser")
    public RestMessage getAreasByUser(@RequestBody LinkappUser linkappUser) {
        Assert.notNull(linkappUser, "linkappUser参数 不能为空");
        return RestBuilders.successBuilder().data(service.getAreasByUser(linkappUser)).build();
    }

    @PostMapping("/selectLinkappAreaTreeList")
    public RestMessage selectLinkappAreaTreeList(@RequestBody LinkappArea linkappArea) {
        return RestBuilders.successBuilder().data(service.selectLinkappAreaTreeList(linkappArea)).build();
    }

    @PostMapping("save")
    public RestMessage saveRoleRefArea(@RequestBody GfdRoleRefArea roleRefArea) {
        Assert.notNull(roleRefArea, "roleRefArea参数 不能为空");
        String result = service.saveRoleRefArea(roleRefArea);
        if(result != null){
            return RestBuilders.failureBuilder().message(result).build();
        }
        return RestBuilders.successBuilder().message("success").build();
    }

    @PostMapping("update")
    public RestMessage updateRoleRefArea(@RequestBody GfdRoleRefArea roleRefArea) {
        Assert.notNull(roleRefArea, "roleRefArea不能为空");
        String result = service.updateRoleRefArea(roleRefArea);
        if(result != null){
            return RestBuilders.failureBuilder().message(result).build();
        }
        return RestBuilders.successBuilder().message("success").build();
    }

    @PostMapping("delete")
    public RestMessage deleteRoleRefArea(@RequestBody GfdRoleRefArea roleRefArea) {
        Assert.notNull(roleRefArea, "roleRefArea不能为空");
        service.deleteRoleRefArea(roleRefArea.getDeleteIds());
        return RestBuilders.successBuilder().message("success").build();
    }

    @PostMapping("getPage")
    public RestMessage getResidentInfoPage(@RequestBody RequestModel<GfdRoleRefArea> requestModel) {
        Assert.notNull(requestModel, "requestModel 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        Assert.notNull(requestModel.getCustomQueryParams(), "查询参数 不能为空");
        IPage<GfdRoleRefArea> roleRefAreaPage = service.getRoleRefAreaPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(roleRefAreaPage).build();
    }

}
