package com.easylinkin.linkappapi.classroom.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 国防大角色关联区域
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("gfd_role_ref_area")
public class GfdRoleRefArea extends Model<GfdRoleRefArea> {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    private Integer id;

    /**
     * 角色id
     */
    @TableField("role_id")
    private Long roleId;

    @TableField(exist = false)
    private String roleName;

    /**
     * 区域路径
     */
    @TableField("area_id")
    private String areaId;

    @TableField(exist = false)
    private String areaPath;

    @TableField("create_time")
    private Date createTime;

    @TableField("modify_time")
    private Date modifyTime;

    @TableField("tenant_id")
    private String tenantId;

    @TableField(exist = false)
    private String deleteIds;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
