package com.easylinkin.linkappapi.roster.controller;

import com.easylinkin.linkappapi.roster.service.RosterSignUrlService;
import com.easylinkin.linkappapi.roster.vo.RosterSignUrlSaveVo;
import com.easylinkin.linkappapi.roster.vo.RosterSignUrlVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.validation.Valid;

/**
 * 花名册签名控制器
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("rosterSignUrl")
@Api(tags = "花名册签名管理")
public class RosterSignUrlController {

    @Autowired
    private RosterSignUrlService rosterSignUrlService;

    @PutMapping("/save")
    @ApiOperation("保存签名")
    public RestMessage<Boolean> saveSignUrl(@Valid @RequestBody RosterSignUrlSaveVo rosterSignUrlSaveVo) {
        boolean result = rosterSignUrlService.saveSignUrl(rosterSignUrlSaveVo);
        if (result) {
            return RestBuilders.success(true, "签名保存成功");
        } else {
            return RestBuilders.failure("签名保存失败");
        }
    }

    @GetMapping("/get")
    @ApiOperation("获取当前用户签名")
    public RestMessage<RosterSignUrlVo> getSignUrl() {
        RosterSignUrlVo rosterSignUrlVo = rosterSignUrlService.getSignUrl();
        return RestBuilders.success(rosterSignUrlVo, "获取签名成功");
    }

    @GetMapping("/get/{rosterPersonnelId}")
    @ApiOperation("根据花名册人员ID获取签名")
    public RestMessage<RosterSignUrlVo> getSignUrlByRosterPersonnelId(
            @ApiParam(value = "花名册人员ID", required = true) 
            @PathVariable Long rosterPersonnelId) {
        RosterSignUrlVo rosterSignUrlVo = rosterSignUrlService.getSignUrlByRosterPersonnelId(rosterPersonnelId);
        return RestBuilders.success(rosterSignUrlVo, "获取签名成功");
    }
}
