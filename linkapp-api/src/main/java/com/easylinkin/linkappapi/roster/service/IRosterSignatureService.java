package com.easylinkin.linkappapi.roster.service;

import com.easylinkin.linkappapi.roster.dto.SaveSignatureDTO;
import com.easylinkin.linkappapi.roster.vo.SignatureVO;

/**
 * 花名册签名服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IRosterSignatureService {

    /**
     * 保存签名
     * 根据当前用户对应的花名册人员保存签名图片
     * 
     * @param saveSignatureDTO 保存签名DTO
     * @return 是否保存成功
     */
    boolean saveSignature(SaveSignatureDTO saveSignatureDTO);

    /**
     * 获取签名
     * 根据当前用户获取对应花名册的签名图片
     * 
     * @return 签名信息
     */
    SignatureVO getSignature();

    /**
     * 根据指定花名册人员ID获取签名
     * 主要用于跟班记录代写场景，获取待办人员的签名
     * 
     * @param rosterPersonnelId 花名册人员ID
     * @return 签名信息
     */
    SignatureVO getSignatureByRosterPersonnelId(Long rosterPersonnelId);
}
