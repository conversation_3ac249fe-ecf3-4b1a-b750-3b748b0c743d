package com.easylinkin.linkappapi.roster.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 保存签名DTO
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@ApiModel("保存签名DTO")
public class SaveSignatureDTO {

    @ApiModelProperty(value = "业务类型", required = true, example = "evening_meeting")
    @NotBlank(message = "业务类型不能为空")
    private String businessType;

    @ApiModelProperty(value = "签字图片URL", required = true)
    @NotBlank(message = "签字图片不能为空")
    private String signUrl;
}
