package com.easylinkin.linkappapi.roster.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.easylinkin.linkappapi.common.translate.Code2Text;
import com.easylinkin.linkappapi.common.translate.CodeI18n;
import com.easylinkin.linkappapi.common.translate.impl.DictTranslateor;
import com.easylinkin.linkappapi.common.utils.DateUtil;
import com.easylinkin.linkappapi.roster.dto.AuthBookJson;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 下午 4:23
 * 人员花名册-人员管理
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_linkapp_roster_personnel")
@CodeI18n
public class RailLinkappRosterPersonnel extends  Model<RailLinkappRosterPersonnel> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 人员类型
     * 0:管理人员,1:劳务人员,2:防护员,3:特种作业人员
     */
    @TableField("roster_type")
    private String rosterType;
    /**
     * 人员姓名
     */
    @TableField("real_name")
    private String realName;
    /**
     * 人员所属单位，多个单位id用逗号隔开
     */
    @TableField("belonging_units")
    private String belongingUnits;
    /**
     * 身份证号码
     */
    @TableField("ids_no")
    private String idsNo;
    /**
     * 出生日期
     */
    @TableField("birthday_date")
    private String birthdayDate;
    /**
     * 性别
     */
    @TableField("sex")
    private String sex;
    /**
     * 学历,教育背景
     */
    @TableField(value = "education_background")
    @Code2Text(translateor = DictTranslateor.class, value = "degree")
    private String educationBackground;
    /**
     * 人员头像图片
     */
    @TableField("profile_pict")
    private String profilePict;
    /**
     * 闸机权限，数据源来自闸机管理，多个闸机id逗号拼接
     */
    @TableField("gate_permis")
    private String gatePermis;
    /**
     *安全教育结果
     *0合格,1不合格
     */
    @TableField("safety_edu_res")
    private Integer safetyEduRes;
    /**
     * 岗位
     * 数据源来自字典
     */
    @TableField("roster_post")
    @Code2Text(translateor = DictTranslateor.class, value = "jobs")
    private String rosterPost;
    /**
     * 入场时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @TableField(value = "start_time",strategy = FieldStrategy.IGNORED)
    private Date startTime;
    /**
     * 退场时间，如果如果用户填写了入场时间，退场时间应晚与入场时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @TableField(value="end_time",strategy = FieldStrategy.IGNORED)
    private Date endTime;
    /**
     * 授权书图片
     */
    @TableField("auth_book")
    private String authBook;
    /**
     *证书类型，字典维护
     */
    @TableField("auth_book_type")
    private String authBookType;
    /**
     *证书内容页，图片
     */
    @TableField("auth_book_content")
    private String authBookContent;
    /**
     *证书有效开始日期
     */
    @TableField("auth_book_start_date")
    private String authBookStartDate;
    /**
     * 证书有效结束日期
     */
    @TableField("auth_book_end_date")
    private String authBookEndDate;
    /**
     * 人员信息创建时间
      */
    @TableField("create_time")
    private Date createTime;
    /**
     * 创建人id
     */
    @TableField("creator")
    private String creator;

    /**
     * 人员信息更新时间
     */
    @TableField("modify_time")
    private Date modifyTime;
    /**
     *手机号码
     */
    @TableField("link_phone")
    private String linkPhone;
    /**
     * 劳务队伍id,来源劳务队伍管理
     */
    @TableField("labor_team")
    private String laborTeam;
    /**
     * 工类,一级联动选项,来源字典
     */
    @TableField("work_category")
    private String workCategory;
    /**
     * 工种,二级联动选项,来源字典
     */
    @Code2Text(translateor = DictTranslateor.class, value = "work_type")
    @TableField("work_type")
    private String workType;

    /**
     * 防护人员类型
     */
    @TableField("protect_type")
    private String protectType;
    /**
     *防护人员证书签发机构
     */
    @TableField("protect_lssuing_auth")
    private String protectLssuingAuth;
    /**
     *防护人员证书有效时间区间
     */
    @TableField("protect_auth_book_date")
    private String protectAuthBookDate;
    /**
     * 防护人员证书内容,最多5张图片
     */
    @TableField("protect_auth_book_content")
    private String protectAuthBookContent;
    /**
     *防护人员健康状态
     */
    @TableField("protect_health_status")
    private String protectHealthStatus;
    /**
     *防护人员健康证书图片
     */
    @TableField("protect_health_auth_content")
    private String protectHealthAuthContent;
    /**
     *特种作业证书类型，字典维护
     */
    @TableField("special_auth_book_type")
    private String specialAuthBookType;
    /**
     * 特种作业证书有效时间区间
     */
    @TableField("special_auth_book_date")
    private String specialAuthBookDate;
    /**
     * 特种作业证书证书内容页
     */
    @TableField("special_auth_book_content")
    private String specialAuthBookContent;
    /**
     *任命书
     */
    @TableField("appoint_book_content")
    private String appointBookContent;
    /**
     *社保证明
     */
    @TableField("social_book_content")
    private String socialBookContent;
    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
    /**
     * 更新人id
     */
    @TableField("modifier")
    private String modifier;
    /**
     * 是否在岗位
     * 0:退场，1在岗位
     */
    @TableField("work_status")
    private Integer workStatus;
    /**
     * 管理人员新增证书类型数组
     */
    @TableField(value="auth_book_json",typeHandler = AuthBookJson.class)
    private List<AuthBookJson> authBookJsonArrs;
    /**
     *管理人员新增证书类型json串
     */
    @TableField("auth_book_json")
    private String  authBookJson;
    /**
     * 特种人员新增证书类型json串
     */
    @TableField("special_auth_book_json")
    private String  specialAuthBookJson;
    /**
     * 签字图片
     */
    @TableField("sign_url")
    private String signUrl;

}
