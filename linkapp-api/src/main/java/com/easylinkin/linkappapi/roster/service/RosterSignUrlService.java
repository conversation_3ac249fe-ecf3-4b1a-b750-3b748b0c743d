package com.easylinkin.linkappapi.roster.service;

import com.easylinkin.linkappapi.roster.vo.RosterSignUrlSaveVo;
import com.easylinkin.linkappapi.roster.vo.RosterSignUrlVo;

/**
 * 花名册签名服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface RosterSignUrlService {

    /**
     * 保存签名
     * 根据当前用户对应的花名册人员保存签名图片
     * 
     * @param rosterSignUrlSaveVo 保存签名VO
     * @return 是否保存成功
     */
    boolean saveSignUrl(RosterSignUrlSaveVo rosterSignUrlSaveVo);

    /**
     * 获取签名
     * 根据当前用户获取对应花名册的签名图片
     * 
     * @return 签名信息
     */
    RosterSignUrlVo getSignUrl();

    /**
     * 根据指定花名册人员ID获取签名
     * 主要用于跟班记录代写场景，获取待办人员的签名
     * 
     * @param rosterPersonnelId 花名册人员ID
     * @return 签名信息
     */
    RosterSignUrlVo getSignUrlByRosterPersonnelId(Long rosterPersonnelId);
}
