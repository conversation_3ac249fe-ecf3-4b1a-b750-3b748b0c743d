package com.easylinkin.linkappapi.roster.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 花名册签名VO
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@ApiModel("花名册签名VO")
public class RosterSignUrlVo {

    @ApiModelProperty("花名册人员ID")
    private Long rosterPersonnelId;

    @ApiModelProperty("花名册人员姓名")
    private String realName;

    @ApiModelProperty("签字图片URL")
    private String signUrl;

    @ApiModelProperty("是否有签名")
    private Boolean hasSignature;
}
