package com.easylinkin.linkappapi.roster.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import com.easylinkin.linkappapi.roster.dto.SaveSignatureDTO;
import com.easylinkin.linkappapi.roster.dto.SignatureBusinessTypeEnum;
import com.easylinkin.linkappapi.roster.entity.RailLinkappRosterPersonnel;
import com.easylinkin.linkappapi.roster.mapper.RailLinkappRosterPersonnelMapper;
import com.easylinkin.linkappapi.roster.service.IRosterSignatureService;
import com.easylinkin.linkappapi.roster.vo.SignatureVO;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 花名册签名服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Service
public class RosterSignatureServiceImpl implements IRosterSignatureService {

    @Resource
    private RailLinkappRosterPersonnelMapper rosterPersonnelMapper;

    @Resource
    private LinkappUserContextProducer userContextProducer;

    @Override
    public boolean saveSignature(SaveSignatureDTO saveSignatureDTO) {
        // 验证业务类型
        if (!SignatureBusinessTypeEnum.isValidCode(saveSignatureDTO.getBusinessType())) {
            throw new BusinessException("无效的业务类型: " + saveSignatureDTO.getBusinessType());
        }

        // 获取当前用户对应的花名册人员
        RailLinkappRosterPersonnel rosterPersonnel = userContextProducer.getCurrentRosterPersonnel();
        if (rosterPersonnel == null) {
            throw new BusinessException("当前用户未找到对应的花名册人员信息");
        }

        // 更新签名图片
        LambdaUpdateWrapper<RailLinkappRosterPersonnel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RailLinkappRosterPersonnel::getId, rosterPersonnel.getId())
                    .set(RailLinkappRosterPersonnel::getSignUrl, saveSignatureDTO.getSignUrl());

        int updateCount = rosterPersonnelMapper.update(null, updateWrapper);
        
        if (updateCount > 0) {
            log.info("保存签名成功，花名册人员ID: {}, 业务类型: {}", 
                    rosterPersonnel.getId(), saveSignatureDTO.getBusinessType());
            return true;
        } else {
            log.error("保存签名失败，花名册人员ID: {}, 业务类型: {}", 
                     rosterPersonnel.getId(), saveSignatureDTO.getBusinessType());
            return false;
        }
    }

    @Override
    public SignatureVO getSignature() {
        // 获取当前用户对应的花名册人员
        RailLinkappRosterPersonnel rosterPersonnel = userContextProducer.getCurrentRosterPersonnel();
        if (rosterPersonnel == null) {
            throw new BusinessException("当前用户未找到对应的花名册人员信息");
        }

        return buildSignatureVO(rosterPersonnel);
    }

    @Override
    public SignatureVO getSignatureByRosterPersonnelId(Long rosterPersonnelId) {
        if (rosterPersonnelId == null) {
            throw new BusinessException("花名册人员ID不能为空");
        }

        RailLinkappRosterPersonnel rosterPersonnel = rosterPersonnelMapper.selectById(rosterPersonnelId);
        if (rosterPersonnel == null) {
            throw new BusinessException("未找到指定的花名册人员信息");
        }

        return buildSignatureVO(rosterPersonnel);
    }

    /**
     * 构建签名VO对象
     * 
     * @param rosterPersonnel 花名册人员
     * @return 签名VO
     */
    private SignatureVO buildSignatureVO(RailLinkappRosterPersonnel rosterPersonnel) {
        SignatureVO signatureVO = new SignatureVO();
        signatureVO.setRosterPersonnelId(rosterPersonnel.getId());
        signatureVO.setRealName(rosterPersonnel.getRealName());
        signatureVO.setSignUrl(rosterPersonnel.getSignUrl());
        signatureVO.setHasSignature(StringUtils.hasText(rosterPersonnel.getSignUrl()));
        
        return signatureVO;
    }
}
