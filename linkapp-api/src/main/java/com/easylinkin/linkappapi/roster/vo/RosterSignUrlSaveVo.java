package com.easylinkin.linkappapi.roster.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 保存花名册签名VO
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@ApiModel("保存花名册签名VO")
public class RosterSignUrlSaveVo {

    @ApiModelProperty(value = "业务类型", required = true, example = "1")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @ApiModelProperty(value = "签字图片URL", required = true)
    @NotBlank(message = "签字图片不能为空")
    private String signUrl;
}
