package com.easylinkin.linkappapi.roster.dto;

/**
 * 签字业务类型枚举
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public enum SignatureBusinessTypeEnum {
    
    EVENING_MEETING("evening_meeting", "晚交班"),
    SAFETY_CONFIRMATION("safety_confirmation", "安全确认表"),
    FOLLOW_RECORD("follow_record", "跟班记录"),
    MONTHLY_ASSESSMENT("monthly_assessment", "月度考核");

    private final String code;
    private final String name;

    SignatureBusinessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code查找枚举
     * 
     * @param code 业务类型代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static SignatureBusinessTypeEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SignatureBusinessTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 验证业务类型代码是否有效
     * 
     * @param code 业务类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return findByCode(code) != null;
    }
}
