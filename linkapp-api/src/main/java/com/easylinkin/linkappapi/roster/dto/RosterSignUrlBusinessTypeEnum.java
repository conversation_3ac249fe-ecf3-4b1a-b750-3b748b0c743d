package com.easylinkin.linkappapi.roster.dto;

/**
 * 花名册签字业务类型枚举
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public enum RosterSignUrlBusinessTypeEnum {
    
    /**
     * 晚交班
     */
    EVENING_MEETING(1, "晚交班"),
    
    /**
     * 安全确认表
     */
    SAFETY_CONFIRMATION(2, "安全确认表"),
    
    /**
     * 跟班记录
     */
    FOLLOW_RECORD(3, "跟班记录"),
    
    /**
     * 月度考核
     */
    MONTHLY_ASSESSMENT(4, "月度考核");

    private final Integer type;
    private final String name;

    RosterSignUrlBusinessTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据type查找枚举
     * 
     * @param type 业务类型
     * @return 对应的枚举，如果未找到返回null
     */
    public static RosterSignUrlBusinessTypeEnum findByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (RosterSignUrlBusinessTypeEnum businessType : values()) {
            if (businessType.getType().equals(type)) {
                return businessType;
            }
        }
        return null;
    }

    /**
     * 验证业务类型是否有效
     * 
     * @param type 业务类型
     * @return 是否有效
     */
    public static boolean isValidType(Integer type) {
        return findByType(type) != null;
    }
}
