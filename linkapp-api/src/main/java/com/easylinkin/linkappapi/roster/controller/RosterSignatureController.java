package com.easylinkin.linkappapi.roster.controller;

import com.easylinkin.linkappapi.roster.dto.SaveSignatureDTO;
import com.easylinkin.linkappapi.roster.service.IRosterSignatureService;
import com.easylinkin.linkappapi.roster.vo.SignatureVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 花名册签名控制器
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/roster/signature")
@Api(tags = "花名册签名管理")
public class RosterSignatureController {

    @Resource
    private IRosterSignatureService rosterSignatureService;

    @PutMapping("/save")
    @ApiOperation("保存签名")
    public RestMessage<Boolean> saveSignature(@Valid @RequestBody SaveSignatureDTO saveSignatureDTO) {
        try {
            boolean result = rosterSignatureService.saveSignature(saveSignatureDTO);
            if (result) {
                return RestBuilders.success(true, "签名保存成功");
            } else {
                return RestBuilders.failure("签名保存失败");
            }
        } catch (Exception e) {
            log.error("保存签名异常", e);
            return RestBuilders.failure("保存签名异常: " + e.getMessage());
        }
    }

    @GetMapping("/get")
    @ApiOperation("获取当前用户签名")
    public RestMessage<SignatureVO> getSignature() {
        try {
            SignatureVO signatureVO = rosterSignatureService.getSignature();
            return RestBuilders.success(signatureVO, "获取签名成功");
        } catch (Exception e) {
            log.error("获取签名异常", e);
            return RestBuilders.failure("获取签名异常: " + e.getMessage());
        }
    }

    @GetMapping("/get/{rosterPersonnelId}")
    @ApiOperation("根据花名册人员ID获取签名")
    public RestMessage<SignatureVO> getSignatureByRosterPersonnelId(
            @ApiParam(value = "花名册人员ID", required = true) 
            @PathVariable Long rosterPersonnelId) {
        try {
            SignatureVO signatureVO = rosterSignatureService.getSignatureByRosterPersonnelId(rosterPersonnelId);
            return RestBuilders.success(signatureVO, "获取签名成功");
        } catch (Exception e) {
            log.error("根据花名册人员ID获取签名异常", e);
            return RestBuilders.failure("获取签名异常: " + e.getMessage());
        }
    }
}
