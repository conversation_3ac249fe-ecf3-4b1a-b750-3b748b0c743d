package test.com.easylinkin.linkappapi.iaqi.service.impl; 

import org.junit.Test; 
import org.junit.Before; 
import org.junit.After; 

/** 
* IaqiStatisticServiceImpl Tester. 
* 
* <AUTHOR> name> 
* @since <pre>6月 30, 2022</pre> 
* @version 1.0 
*/ 
public class IaqiStatisticServiceImplTest { 

@Before
public void before() throws Exception { 
} 

@After
public void after() throws Exception { 
} 

/** 
* 
* Method: saveOne(IaqiStatistic appIaqiStatistic) 
* 
*/ 
@Test
public void testSaveOne() throws Exception { 
}

/** 
* 
* Method: countDaysGroupByLevel(CountDaysVo countDaysVo) 
* 
*/ 
@Test
public void testCountDaysGroupByLevel() throws Exception { 
}


/** 
* 
* Method: validRepeat(IaqiStatistic appIaqiStatistic) 
* 
*/ 
@Test
public void testValidRepeat() throws Exception { 
/*
try { 
   Method method = IaqiStatisticServiceImpl.getClass().getMethod("validRepeat", IaqiStatistic.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/ 
} 

/** 
* 
* Method: validParamRequired(IaqiStatistic appIaqiStatistic) 
* 
*/ 
@Test
public void testValidParamRequired() throws Exception { 
/*
try { 
   Method method = IaqiStatisticServiceImpl.getClass().getMethod("validParamRequired", IaqiStatistic.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/ 
} 

/** 
* 
* Method: validParamFormat(IaqiStatistic appIaqiStatistic) 
* 
*/ 
@Test
public void testValidParamFormat() throws Exception { 
/*
try { 
   Method method = IaqiStatisticServiceImpl.getClass().getMethod("validParamFormat", IaqiStatistic.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/ 
} 

} 
