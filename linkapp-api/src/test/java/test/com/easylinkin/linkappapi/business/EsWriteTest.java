package test.com.easylinkin.linkappapi.business;


import cn.hutool.core.date.StopWatch;
import cn.hutool.core.thread.ThreadUtil;
import com.easylinkin.linkappapi.LinkappApiApplication;
import com.easylinkin.linkappapi.elasticsearch.entity.ESconfig;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionService;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;

import static junit.framework.TestCase.assertEquals;
import static org.junit.Assert.assertFalse;
import static site.morn.framework.context.CommonConstant.Config.FRAMEWORK_BASE_PACKAGES;

//@Slf4j(topic = "EsWriteTest")
@RunWith(SpringRunner.class)
/**
 * 测试类基础注解
 * 增加webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
 * 作用：解决集成了websocket后测试类启动报javax.websocket.server.ServerContainer not available
 */
@SpringBootTest(classes = LinkappApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
/** 与Application的启动类一致开始 **/
@ComponentScan(basePackages = {FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@EntityScan({FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@ServletComponentScan
/** 与Application的启动类一致结束 **/
public class EsWriteTest {

    private Logger log = LoggerFactory.getLogger(EsWriteTest.class);

    private ESconfig eSconfig;

    @Autowired
    public void setESconfig(ESconfig eSconfig) {
        this.eSconfig = eSconfig;
    }

    @Test
    public void testWriteData() throws IOException {
        log.info("-- ES写入数据耗时测试开始 --");
        //存耗时统计数据，最后打印
        List<Map<String, Double>> timeUseList = new ArrayList<>();

        int testNum = 10;
        StopWatch sw = new StopWatch();
        sw.start();
        //索引信息
        String index = "zwtest";
        String type = "zwtest";
        //获取client,原配置获取ES连接会出现：
        //1、Connection lease request time out
        //2、error while performing request
        //3、Exception in thread "pool-10-thread-7"
        //修改配置类，提供新的获取ES连接方法，也可以补充配置文件不使用默认配置
        //RestHighLevelClient hclient = eSconfig.getFactory().getRhlClient();
        RestHighLevelClient hclient = eSconfig.restHighLevelClient();
        sw.stop();
        log.info("获取ES的RestHighLevelClient耗时：{}ms", sw.getLastTaskTimeMillis());
        int num = 10000;
        int dataNum = 500;
        int threadNum = 200;
        for (int n = 0; n < testNum; n++) {
            Map<String, Double> timeUseMp = new HashMap<>();
            log.info("ES写入数据耗时测试第{}次", n + 1);
            String id = String.valueOf(System.currentTimeMillis());

            //单个插入数据
            singleWriteData(index, type, id, hclient, sw, timeUseMp);

            //批量插入数据

            batchWriteData(index, type, id, hclient, sw, num, timeUseMp);

            //方式一：开线程模拟测试写入

            threadWriteData(index, type, id, hclient, sw, dataNum, threadNum, timeUseMp);

            //方式二：开启多线程执行写，判断所有线程执行
            id = String.valueOf(System.currentTimeMillis());
            threadWriteData2(index, type, id, hclient, sw, dataNum, threadNum, timeUseMp);
            timeUseList.add(timeUseMp);
        }
        log.info("-- ES写入数据耗时测试结束 --");
        log.info("-- ES写入数据耗时测试结论 --");
        log.info("-- 次数  |单数据写入(耗时ms)| {}条批量写入(耗时ms) | 多线程方式一{}个{}条写入(耗时ms) | 多线程方式一{}个{}条写入(耗时ms) --", num, threadNum, dataNum, threadNum, dataNum);
        for (int i = 0; i < timeUseList.size(); i++) {
            Map<String, Double> useMp = timeUseList.get(i);
            String no = StringUtils.leftPad(String.valueOf(i + 1), 5, " ");
            String singleUse = StringUtils.leftPad(String.valueOf(useMp.get("singleUse")), 11, " ");
            String batchUse = StringUtils.leftPad(String.valueOf(useMp.get("batchUse")), 20, " ");
            String threadUse1 = StringUtils.leftPad(String.valueOf(useMp.get("threadUse1")), 25, " ");
            String threadUse2 = StringUtils.leftPad(String.valueOf(useMp.get("threadUse2")), 25, " ");
            log.info("--{} {} {} {} {} --", no, singleUse, batchUse, threadUse1, threadUse2);
        }
    }

    /**
     * 多线程保存数据到ES耗时测试
     *
     * @param index     索引
     * @param type      类型
     * @param idStr     id字符串
     * @param hclient   ESclient
     * @param sw        计时对象
     * @param dataNum   单线程数据量
     * @param threadNum 线程数量
     * @param timeUseMp
     */
    private void threadWriteData2(String index, String type, String idStr, RestHighLevelClient hclient, StopWatch sw, int dataNum, int threadNum, Map<String, Double> timeUseMp) {
        sw.start();
        log.info("多线程方式2-开线程{}个模拟ES保存单线程{}条数据", threadNum, dataNum);

        List<BulkRequest> bulkRequestList = new ArrayList<>();
        for (int t = 0; t < threadNum; t++) {
            BulkRequest bulkRequest = initSingleThread(index, type, idStr, dataNum);
            bulkRequestList.add(bulkRequest);
        }
        sw.stop();
        log.info("组织准备线程插入ES的数据耗时：{}ms", sw.getLastTaskTimeMillis());

        sw.start();
       /* CompletionService<Boolean> completionService = new ExecutorCompletionService( Executors
                .newFixedThreadPool(20));*/
        //获取线程池
        //获取线程池
        if (threadNum > 100) {
            threadNum = 100;
        }
        ExecutorService executorService = ThreadUtil.newExecutor(threadNum);
        CompletionService<Boolean> completionService = new ExecutorCompletionService(executorService);
        sw.stop();
        log.info("多线程方式2-获取CompletionService多线程服务耗时{}ms", sw.getLastTaskTimeMillis());

        sw.start();
        bulkRequestList.stream().forEach(bulkRequest -> {
            completionService.submit(() -> {
                BulkResponse bulkResponse = null;
                try {
                    bulkResponse = hclient.bulk(bulkRequest, RequestOptions.DEFAULT);
                } catch (IOException e) {
                    log.error("ES保存数据bulk异常，原因：{}", e.getMessage());
                }
                //assertFalse(bulkResponse.hasFailures());
                return bulkResponse.hasFailures();
            });
        });

        for (int i = 0; i < bulkRequestList.size(); i++) {
            try {
                completionService.take().get();
            } catch (Exception e) {
                log.error("多线程方式2-获取多线程执行结果异常，原因：{}", e.getMessage());
            }
        }
        sw.stop();
        double timeUse = sw.getLastTaskTimeMillis();
        timeUseMp.put("threadUse2", timeUse);
        log.info("多线程方式2-线程{}个保存单数据{}条到ES耗时：{}ms", threadNum, dataNum, timeUse);
    }

    /**
     * 多线程保存数据到ES耗时测试
     *
     * @param index     索引
     * @param type      类型
     * @param idStr     id字符串
     * @param hclient   ESclient
     * @param sw        计时对象
     * @param dataNum   单线程数据量
     * @param threadNum 线程数量
     * @param timeUseMp
     */
    private void threadWriteData(String index, String type, String idStr, RestHighLevelClient hclient, StopWatch sw, int dataNum, int threadNum, Map<String, Double> timeUseMp) {
        sw.start();
        log.info("多线程方式1-开线程{}个模拟ES保存单线程{}条数据", threadNum, dataNum);

        List<BulkRequest> bulkRequestList = new ArrayList<>();
        for (int t = 0; t < threadNum; t++) {
            BulkRequest bulkRequest = initSingleThread(index, type, idStr, dataNum);
            bulkRequestList.add(bulkRequest);
        }
        sw.stop();
        log.info("组织准备线程插入ES的数据耗时：{}ms", sw.getLastTaskTimeMillis());

        try {
            sw.start();
            //获取线程池
            if (threadNum > 100) {
                threadNum = 100;
            }
            ExecutorService service = ThreadUtil.newExecutor(threadNum);
            sw.stop();
            log.info("获取多线程服务耗时：{}ms", sw.getLastTaskTimeMillis());

            sw.start();
            //监控子线程执行完毕,再执行主线程,要不然会导致主线程关闭,子线程也会随着关闭
            CountDownLatch countDownLatch = new CountDownLatch(bulkRequestList.size());

            for (int i = 0; i < bulkRequestList.size(); i++) {
                BulkRequest bulkRequest = bulkRequestList.get(i);
                service.execute(() -> {
                    BulkResponse bulkResponse = null;
                    try {
                        bulkResponse = hclient.bulk(bulkRequest, RequestOptions.DEFAULT);
                    } catch (IOException e) {
                        log.error("ES保存数据bulk异常，原因：{}", e.getMessage());
                    }
                    assertFalse(bulkResponse.hasFailures());
                    countDownLatch.countDown();
                });
            }
            //当子线程执行完毕时,主线程再往下执行
            countDownLatch.await();
            sw.stop();
            double timeUse = sw.getLastTaskTimeMillis();
            timeUseMp.put("threadUse1", timeUse);
            log.info("线程{}个保存单数据{}条到ES耗时：{}ms", threadNum, dataNum, timeUse);
        } catch (InterruptedException e) {
            log.error("多线程方式1-多线程执行保存ES报错，原因：{}", e.getMessage());
        }

    }

    /**
     * 初始化单线程的ES保存BulkRequest对象
     *
     * @param index   索引
     * @param type    类型
     * @param id      id字符串
     * @param dataNum 单线程数据量
     * @return BulkRequest对象
     */
    private BulkRequest initSingleThread(String index, String type, String id, int dataNum) {
        BulkRequest bulkRequest = new BulkRequest();
        for (int i = 0; i < dataNum; i++) {
            String idStr = id + "-" + i;
            //创建索引
            IndexRequest request = new IndexRequest(index, type, idStr);

            Map map = initWriteData(idStr);

            request.source(map);

            bulkRequest.add(request);
        }
        return bulkRequest;
    }

    /**
     * 组织批量插入ES请求的BulkRequest对象
     *
     * @param index     索引
     * @param type      类型
     * @param id        id
     * @param hclient   ESclient
     * @param sw        计时对象
     * @param num       数量
     * @param timeUseMp
     */
    private void batchWriteData(String index, String type, String id, RestHighLevelClient hclient, StopWatch sw, int num, Map<String, Double> timeUseMp) throws IOException {
        log.info("ES写入数据测试-批量");
        sw.start();
        BulkRequest bulkRequest = new BulkRequest();
        for (int i = 0; i < num; i++) {
            String idStr = id + "-" + i;
            //创建索引
            IndexRequest request = new IndexRequest(index, type, idStr);

            Map map = initWriteData(idStr);

            request.source(map);

            bulkRequest.add(request);
        }
        sw.stop();
        log.info("---组织ES数据请求对象BulkRequest耗时:{}ms", sw.getLastTaskTimeMillis());
        sw.start();
        BulkResponse bulkResponse = hclient.bulk(bulkRequest, RequestOptions.DEFAULT);
        assertFalse(bulkResponse.hasFailures());
        sw.stop();
        double timeUse = sw.getLastTaskTimeMillis();
        timeUseMp.put("batchUse", timeUse);
        log.info("---批量{}条数据-ES数据保存耗时:{}ms", num, timeUse);
    }

    /**
     * 单个插入数据
     *
     * @param index     索引
     * @param type      类型
     * @param id        id
     * @param hclient   hclient
     * @param sw        计时对象
     * @param timeUseMp
     */
    private void singleWriteData(String index, String type, String id, RestHighLevelClient hclient, StopWatch sw, Map<String, Double> timeUseMp) throws IOException {
        log.info("单条数据-ES保存开始");
        sw.start();

        //创建索引
        IndexRequest request = new IndexRequest(index, type, id);

        Map map = initWriteData(id);

        request.source(map);

        //插入数据
        IndexResponse indexResponse = hclient.index(request, RequestOptions.DEFAULT);
        long version = indexResponse.getVersion();
        //log.info("--version:{}", version);

        //比对结果
        assertEquals(DocWriteResponse.Result.CREATED, indexResponse.getResult());
        assertEquals(1, version);
        sw.stop();
        double timeUse = sw.getLastTaskTimeMillis();
        timeUseMp.put("singleUse", timeUse);
        log.info("---单条数据-ES保存耗时:{}ms", timeUse);
    }

    /**
     * 初始化一条数据
     *
     * @param noStr
     * @return
     */
    private Map initWriteData(String noStr) {
        Map map = new HashMap<>();
        map.put("id", noStr);  //这个应该是业务数据的主键id，这里图简便就这样
        map.put("name", "周瑜-" + noStr);
        map.put("country", "吴");
        map.put("birthday", "公元175年");
        map.put("longevity", "60");
        return map;
    }


}
