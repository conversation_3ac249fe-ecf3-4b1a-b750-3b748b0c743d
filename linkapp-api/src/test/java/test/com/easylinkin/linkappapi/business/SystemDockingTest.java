package test.com.easylinkin.linkappapi.business;


import com.easylinkin.linkappapi.LinkappApiApplication;
import com.easylinkin.linkappapi.alarm.service.AlarmService;
import com.easylinkin.linkappapi.device.entity.Device;
import com.easylinkin.linkappapi.device.service.DeviceService;
import com.easylinkin.linkappapi.openapi.dto.SystemDockDevice;
import com.easylinkin.linkappapi.openapi.dto.SystemDocking;
import com.easylinkin.linkappapi.openapi.service.SystemDockingService;
import com.easylinkin.linkappapi.ruleengine.entity.RuleExecution;
import com.easylinkin.linkappapi.ruleengine.service.RuleExecutionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

import static site.morn.framework.context.CommonConstant.Config.FRAMEWORK_BASE_PACKAGES;

/**
 * <AUTHOR>
 */

@Slf4j
@RunWith(SpringRunner.class)
/**
 * 测试类基础注解
 * 增加webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
 * 作用：解决集成了websocket后测试类启动报javax.websocket.server.ServerContainer not available
 */
@SpringBootTest(classes = LinkappApiApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
/** 与Application的启动类一致开始 **/
@ComponentScan(basePackages = {FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@EntityScan({FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@ActiveProfiles("jiangong-test")
@ServletComponentScan
/** 与Application的启动类一致结束 **/
public class SystemDockingTest {

    @Autowired
    private SystemDockingService systemDockingService;

    @Test
    public void testImportMeasureTypeData() {
        log.info("--重新注册平台设备--");
        SystemDocking oneById = systemDockingService.getOneById(173);
        systemDockingService.saveOne(oneById);
        log.info("-----------------导入结束---------------");
    }
}
