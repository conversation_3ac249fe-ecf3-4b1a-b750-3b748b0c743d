package test.com.easylinkin.linkappapi.business;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.easylinkin.linkappapi.common.utils.DateUtil;
import com.easylinkin.linkappapi.openapi.util.HttpClientUtil;
import lombok.SneakyThrows;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;

import java.nio.charset.Charset;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */


public class DataInitTest {
    @SneakyThrows
    public static void main(String[] args) {
        List<String> codeList = new ArrayList<>();
        codeList.add("61C6A2FC13D8193EC014B15C");

        codeList.add("01235D-937266-4B21EE");
        codeList.add("012301-6E8F08-A56AEE");
        codeList.add("A83325-E8B7BB-0000FF");
        codeList.add("9E9343-40CBBB-0000FF");
        codeList.add("012379-0B2AFB-3167EE");
        codeList.add("012326-FBE8F8-432DEE");
        codeList.add("012370-D1C760-FC94EE");
        codeList.add("770D4D-DDA2BB-0000FF");
        codeList.add("D4CB42-DCA0BB-0000FF");

        codeList.add("3059413");
        codeList.add("3059414");
        codeList.add("3059415");
        codeList.add("3059417");

        codeList.add("20001228");

        codeList.add("860610055014935");
        codeList.add("861248057510557");
        codeList.add("861986061339543");

        codeList.add("864162046317346");
        codeList.add("864162046363118");
        codeList.add("864162046386788");
        codeList.add("866071058727839");
        codeList.add("866071058727870");
        codeList.add("866071058731526");
        codeList.add("866071058737663");

        codeList.add("C91042373-12");

        codeList.add("C91042373-18");
        codeList.add("C91042373-19");
        codeList.add("C91042373-20");
        codeList.add("C91042373-22");

        codeList.add("J220281282964");

        for (String code : codeList){
            doBusiness(code);
        }


//        //插入数据
//        HttpPost httpPost1 = new HttpPost("http://120.26.56.240:35053/deviceflow-202403/_search");
//        //添加请求头
//        httpPost.addHeader("Content-Type", "application/json");
//        //设置接收形式
//        httpPost.setHeader("Accept", "*/*");
//        String str = HttpClientUtil.doPost(httpPost);
    }

    private static void doBusiness(String code) throws ParseException {
        //查询历史数据
        HttpPost httpPost = new HttpPost("http://10.15.7.22:15053/deviceflow-202403/_search");
        //添加请求头
        httpPost.addHeader("Content-Type", "application/json");
        //设置接收形式
        httpPost.setHeader("Accept", "*/*");
//        设置授权
//        httpPost.setHeader("Blade-Auth", "bearer " + getAccessTokenTest());
//        httpPost.setHeader("Authorization", "Basic c2FiZXI6c2FiZXJfc2VjcmV0");
        //创建登录实体
        String body = "{\n" +
                "    \"size\": 10000,\n" +
                "    \"query\": {\n" +
                "        \"bool\": {\n" +
                "            \"must\": [\n" +
                "                {\n" +
                "                    \"term\": {\n" +
                "                        \"deviceCode\": \"" + code + "\"\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"range\": {\n" +
                "                        \"createTime\": {\n" +
                "                            \"gte\": \"2024-03-01 00:00:00\",\n" +
                "                            \"lt\": \"2024-03-02 00:00:00\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    },\n" +
                "    \"from\": 0,\n" +
                "    \"sort\": [\n" +
                "        {\n" +
                "            \"createTime\": {\n" +
                "                \"order\": \"desc\"\n" +
                "            }\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        //按照日期循环查询每天
        JSONObject param = JSON.parseObject(body);
        JSONObject query = param.getJSONObject("query");
        //获取时间,从2024-03-01开始，每天往前推
        String date = "2024-03-01 00:00:00";
        //格式转换
        SimpleDateFormat dataSimpleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = dataSimpleFormat.parse(date);
        JSONArray jsonArray = query.getJSONObject("bool").getJSONArray("must");
        //获取到range
        for (int i = 0; i <= 30; i++) {
            Date endTime = DateUtil.addDay(startTime, 1);
            //替换参数
            jsonArray.getJSONObject(1).getJSONObject("range").getJSONObject("createTime").put("gte", dataSimpleFormat.format(startTime));
            jsonArray.getJSONObject(1).getJSONObject("range").getJSONObject("createTime").put("lt", dataSimpleFormat.format(endTime));
            httpPost.setEntity(new StringEntity(JSONObject.toJSONString(param), Charset.forName("UTF-8")));
            String str = HttpClientUtil.doPost(httpPost);
            JSONObject result = JSONObject.parseObject(str);
            System.out.println(param);
            startTime = endTime;
            JSONArray hits = result.getJSONObject("hits").getJSONArray("hits");
            for (int j = 0; j < hits.size(); j++) {
                JSONObject hit = hits.getJSONObject(j);
                JSONObject source = hit.getJSONObject("_source");
                //将createTime与colectTime转换为时间
//                String createTimeStr = source.getString("createTime");
//                String collectTimeStr = source.getString("collectTime");
//                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                Date createTime = simpleDateFormat.parse(createTimeStr);
//                Date collectTime = simpleDateFormat.parse(collectTimeStr);
//                source.put("createTime", createTime);
//                source.put("collectTime", collectTime);
                System.out.println(source);
                //插入数据
                HttpPost httpPost1 = new HttpPost("http://120.26.56.240:35053/deviceflow-202403/_doc");
                //添加请求头
                httpPost1.addHeader("Content-Type", "application/json");
                //设置接收形式
                httpPost1.setHeader("Accept", "*/*");
                httpPost1.setEntity(new StringEntity(JSON.toJSONString(source, SerializerFeature.WriteDateUseDateFormat), Charset.forName("UTF-8")));
                String a = HttpClientUtil.doPost(httpPost1);
                System.out.println(a);
            }
        }
    }
}
