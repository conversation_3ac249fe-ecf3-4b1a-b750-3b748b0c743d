package test.com.easylinkin.linkappapi.business;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.easylinkin.linkappapi.LinkappApiApplication;
import com.easylinkin.linkappapi.device.dao.WaterRecordsMapper;
import com.easylinkin.linkappapi.device.entity.DeviceModel;
import com.easylinkin.linkappapi.device.entity.ElectricyRecords;
import com.easylinkin.linkappapi.device.entity.WaterRecords;
import com.easylinkin.linkappapi.device.mapper.DeviceModelMapper;
import com.easylinkin.linkappapi.device.mapper.ElectricyRecordsMapper;
import com.easylinkin.linkappapi.device.service.ElectricyRecordsService;
import com.easylinkin.linkappapi.device.service.WaterRecordsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static site.morn.framework.context.CommonConstant.Config.FRAMEWORK_BASE_PACKAGES;

@Slf4j
@RunWith(SpringRunner.class)
/**
 * 测试类基础注解
 * 增加webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
 * 作用：解决集成了websocket后测试类启动报javax.websocket.server.ServerContainer not available
 */
@SpringBootTest(classes = LinkappApiApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
/** 与Application的启动类一致开始 **/
@ComponentScan(basePackages = {FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@EntityScan({FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@ServletComponentScan
@ActiveProfiles("jiangong-test")
/** 与Application的启动类一致结束 **/
public class EnvironmentAreaIdInit {

    @Autowired
    ElectricyRecordsService electricyRecordsService;

    @Autowired
    WaterRecordsService waterRecordsService;

    @Autowired
    DeviceModelMapper deviceModelMapper;

    @Test
    public void testImportMeasureTypeData() {
        log.info("--开始导入数据--");
        Map<String,DeviceModel> deviceModelMap = new HashMap<>();
        //查询水表或电表的数据
        List<ElectricyRecords> electricyRecordsList = electricyRecordsService.list();
        List<WaterRecords> waterRecordsList = waterRecordsService.list();
        for (ElectricyRecords electricyRecords : electricyRecordsList) {
            //根据设备id查询区域id
            DeviceModel deviceModel = getDeviceModel(deviceModelMap, electricyRecords.getDeviceCode());
            if (deviceModel == null) continue;
            if (deviceModel.getEnvironmentalAreaId()!=null){
                electricyRecords.setAreaId(deviceModel.getEnvironmentalAreaId());
                electricyRecordsService.updateById(electricyRecords);
            }
        }

        //更新用水记录的区域id
        for (WaterRecords waterRecords : waterRecordsList) {
            //根据设备id查询区域id
            DeviceModel deviceModel = getDeviceModel(deviceModelMap, waterRecords.getDeviceCode());
            if (deviceModel == null) continue;
            if (deviceModel.getEnvironmentalAreaId()!=null){
                waterRecords.setAreaId(deviceModel.getEnvironmentalAreaId());
                waterRecordsService.updateById(waterRecords);
            }
        }

        log.info("-----------------导入结束---------------");
    }

    private DeviceModel getDeviceModel(Map<String, DeviceModel> deviceModelMap, String waterRecords) {
        DeviceModel deviceModel = null;
        if (deviceModelMap.get(waterRecords) == null) {
            LambdaQueryWrapper<DeviceModel> deviceModelLambdaQueryWrapper = new LambdaQueryWrapper<>();
            deviceModelLambdaQueryWrapper.eq(DeviceModel::getCode, waterRecords).eq(DeviceModel::getRemoveStatus, 0);
            List deviceModelList = deviceModelMapper.selectList(deviceModelLambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(deviceModelList)) {
                deviceModel = (DeviceModel) deviceModelList.get(0);
                deviceModelMap.put(waterRecords, deviceModel);
            } else {
                deviceModel = new DeviceModel();
                deviceModelMap.put(waterRecords, deviceModel);
                return null;
            }
        } else {
            deviceModel = deviceModelMap.get(waterRecords);
        }
        return deviceModel;
    }
}
