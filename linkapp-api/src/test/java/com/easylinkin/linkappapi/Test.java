package com.easylinkin.linkappapi;

import com.alibaba.druid.filter.config.ConfigTools;
import com.easylinkin.linkappapi.common.utils.DateUtil;
import com.easylinkin.linkappapi.elasticsearch.entity.SortField;
import com.easylinkin.linkappapi.webcammanage.util.YYSHDateUtil;
import org.elasticsearch.search.sort.SortOrder;
import org.joda.time.MutableDateTime;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/6/28 11:55
 */
public class Test {

  /*  public static void main(String[] args) {
        SortField sortField = new SortField();
        sortField.setFieldName("create_time").setSortOrder(SortOrder.DESC);
        System.out.println("args = " + sortField.toString());

        Long dateInteval = YYSHDateUtil.getDateInteval("2021-06-01 00:00:00", "2021-06-28 00:00:00", DateUtil.DATE_TIME_FORMAT_DEFAULT);
        System.out.println("dateInteval = " + dateInteval/24);

    }*/

    public static void main(String[] args) throws Exception {
        String[] arr = ConfigTools.genKeyPair(512);
        System.out.println("privateKey:" + arr[0]);
        System.out.println("publicKey:" + arr[1]);
        LocalDateTime now = LocalDateTime.now();
        System.out.println(now.toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
        MutableDateTime mutableDateTime = new MutableDateTime();
        System.out.println(mutableDateTime.getHourOfDay());
        System.out.println(mutableDateTime.getMinuteOfHour());
        System.out.println(mutableDateTime.getSecondOfMinute());
        System.out.println(mutableDateTime.getMillis());
    }
}
