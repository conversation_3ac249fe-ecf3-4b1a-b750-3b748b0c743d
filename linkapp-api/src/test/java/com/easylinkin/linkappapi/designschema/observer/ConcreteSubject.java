package com.easylinkin.linkappapi.designschema.observer;

import java.util.ArrayList;
import java.util.List;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/8/17 22:18
 */
public class ConcreteSubject implements Subject{

    private List<Observer> observers = new ArrayList();

    @Override
    public void registerObserver(Observer observer) {
        observers.add(observer);
    }

    @Override
    public void removeObserver(Observer observer) {
        observers.remove(observer);
    }

    @Override
    public void notifyObservers(String message) {
        for (Observer observer : observers) {
            observer.update(message);
        }
    }
}
