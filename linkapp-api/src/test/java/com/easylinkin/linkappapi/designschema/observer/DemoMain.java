package com.easylinkin.linkappapi.designschema.observer;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/8/17 22:21
 */
public class DemoMain {

    public static void main(String[] args) {
        ConcreteSubject subject = new ConcreteSubject();
        subject.registerObserver(new ConcreteObserverOne());
        subject.registerObserver(new ConcreteObserverTwo());
        subject.notifyObservers("datapush推送json");
    }
}
