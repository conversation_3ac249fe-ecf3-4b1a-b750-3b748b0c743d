package com.easylinkin.linkappapi;

import com.easylinkin.linkappapi.common.utils.DateUtil;

import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/7/22 14:45
 */
public class MeterTest {

    public static void main(String[] args) {
        String header1 = "{ \"index\":{\"_id\": ";
        String header2 = "}}";

        String str1 = "{\"storageTime\":\"";
        String str2 = "\",\"createTime\":\"";
        String str3 = "\",\"data\":{\"magnetic_interference\":\"0\",\"device_id\":\"882020040007\",\"valve_state\":0";
        String str4 = ",\"water_flux\":";
        String str5 = "},\"deviceTypeName\":\"水表\",\"deviceCode\":\"VIR1589277462736\",\"deviceName\":\"测试\",\"deviceUnitName\":\"LoRa智能水表-CN470\"}\n";

        long index0 = 1604924917617L;
        String initDate = "2021-08-09 16:39:03";
        double index2 = 25.57;
        double step_num = 1.25;
        java.text.DecimalFormat df = new java.text.DecimalFormat("#.00");
        int second = 520;

        for (int i = 0; i<= 3; i++){
            index0 = index0 + 1;
            String headerResult = header1 + index0 + header2;

            Date date = DateUtil.addSecond(DateUtil.getDateFromFormatStr(initDate, DateUtil.DATE_TIME_FORMAT_DEFAULT), second);
            second = second + 520;
            String initDateResult = DateUtil.format(date, DateUtil.DATE_TIME_FORMAT_DEFAULT);

            index2 = index2 + step_num;
            BigDecimal b   =   new   BigDecimal(index2);
            index2 = b.setScale(2, BigDecimal.ROUND_DOWN).doubleValue();

            String result = str1 + initDateResult + str2 + initDateResult + str3  + str4 + index2 + str5;
//            System.out.println(headerResult + "\n" + result + "\n");
            saveAsFileWriter(headerResult + "\n" + result + "\n");
        }
    }

    private static String filePath = "C:\\Users\\<USER>\\Desktop\\water_meter_data.txt";

    private static void saveAsFileWriter(String content) {
        FileWriter fwriter = null;
        try {
            // true表示不覆盖原来的内容，而是加到文件的后面。若要覆盖原来的内容，直接省略这个参数就好
            fwriter = new FileWriter(filePath, true);
            fwriter.write(content);
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            try {
                fwriter.flush();
                fwriter.close();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }


}
