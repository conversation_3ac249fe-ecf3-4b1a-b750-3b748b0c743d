package com.easylinkin.linkappapi;

import java.util.HashMap;
import java.util.Map;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22
 * @description
 */
public class WeatherTest {

  public static void main(String[] args) {
//    queryWeather("武汉");
//    queryAreaId("武汉市");
    queryWeatherFromLocation("114.303412", "30.582566");
  }

  public static void queryWeatherFromLocation(String lat, String lng) {
    String host = "https://qryweather.market.alicloudapi.com";
    String path = "/lundroid/queryweatherfromlocation";
    String method = "GET";
    String appcode = "437e47c104d84e7ab333cf323c9c1ad6";
    Map<String, String> headers = new HashMap<String, String>();
    //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
    headers.put("Authorization", "APPCODE " + appcode);
    Map<String, String> querys = new HashMap<String, String>();
    querys.put("lat", lat);
    querys.put("lng", lng);

    try {
      /**
       * 重要提示如下:
       * HttpUtils请从
       * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
       * 下载
       *
       * 相应的依赖请参照
       * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
       */
      HttpResponse response = HttpUtils.doGet(host, path, method, headers, querys);
      System.out.println(response.toString());
      //获取response的body
      System.out.println(EntityUtils.toString(response.getEntity()));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  public static void queryAreaId(String area) {
    String host = "https://qryweather.market.alicloudapi.com";
    String path = "/lundroid/queryareaid";
    String method = "GET";
    String appcode = "437e47c104d84e7ab333cf323c9c1ad6";
    Map<String, String> headers = new HashMap<String, String>();
    //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
    headers.put("Authorization", "APPCODE " + appcode);
    Map<String, String> querys = new HashMap<String, String>();
    querys.put("area", area);

    try {
      /**
       * 重要提示如下:
       * HttpUtils请从
       * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
       * 下载
       *
       * 相应的依赖请参照
       * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
       */
      HttpResponse response = HttpUtils.doGet(host, path, method, headers, querys);
      System.out.println(response.toString());
      //获取response的body
      System.out.println(EntityUtils.toString(response.getEntity()));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  public static void queryWeather(String area) {
    String host = "https://qryweather.market.alicloudapi.com";
    String path = "/lundroid/queryweather";
    String method = "GET";
    String appcode = "437e47c104d84e7ab333cf323c9c1ad6";
    Map<String, String> headers = new HashMap<>();
    //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
    headers.put("Authorization", "APPCODE " + appcode);
    Map<String, String> querys = new HashMap<>();
    querys.put("area", area);
//    querys.put("areaid", "101200101");

    try {
      /**
       * 重要提示如下:
       * HttpUtils请从
       * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
       * 下载
       *
       * 相应的依赖请参照
       * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
       */
      HttpResponse response = HttpUtils.doGet(host, path, method, headers, querys);
      System.out.println(response.toString());
      //获取response的body
      System.out.println(EntityUtils.toString(response.getEntity()));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }
}

