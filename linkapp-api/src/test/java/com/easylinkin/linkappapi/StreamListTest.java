package com.easylinkin.linkappapi;

import com.alibaba.fastjson.JSON;
import com.easylinkin.linkappapi.powerdistribution.entity.ElectricityEs;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.*;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/28 19:05
 */
public class StreamListTest {

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class LqhTest {
        private Integer age;
        private String name;
        private String createTime;
    }

    public static void main(String[] args) {
       /* List<LqhTest> list = new ArrayList<>();
        LqhTest lt1 = new LqhTest();
        lt1.setAge(18).setName("刘启航1").setCreateTime("2021-09-28 19:19:28");

        LqhTest lt2 = new LqhTest();
        lt2.setAge(14).setName("刘启航2").setCreateTime("2021-09-27 19:16:28");

        LqhTest lt3 = new LqhTest();
        lt3.setAge(18).setName("刘启航3").setCreateTime("2021-09-28 19:17:28");

        LqhTest lt4 = new LqhTest();
        lt4.setAge(17).setName("刘启航4").setCreateTime("2021-09-28 19:15:28");
        list.add(lt1);
        list.add(lt2);
        list.add(lt3);
        list.add(lt4);


        Optional<LqhTest> testOptional = list.stream().max(Comparator.comparingInt(LqhTest::getAge));
        LqhTest lqhTest = testOptional.get();
        System.out.println("lqhTest = " + JSON.toJSONString(lqhTest));*/


        Map<Double, String> map = new LinkedHashMap<>();
        map.put(0.1, "1");
        map.put(1.8, "1");
        map.put(1.2, "1");
        map.put(5.1, "1");
        map.put(2.1, "1");
        Object[] key = map.keySet().toArray();
        Arrays.sort(key, Collections.reverseOrder());
        System.out.println("lqhTest = " + JSON.toJSONString(key));
    }

}
