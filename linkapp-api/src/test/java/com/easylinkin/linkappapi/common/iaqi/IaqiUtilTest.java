package com.easylinkin.linkappapi.common.iaqi;


import org.junit.Test;

import static com.easylinkin.linkappapi.common.iaqi.IaqiUtil.*;

public class IaqiUtilTest {

    @Test
    public void test() {
        System.out.println(getIaqiValueByPm(200, 170));

        System.out.println(getIaqiValueByPmType(170, 25));
        System.out.println(getIaqiValueByPmType(200, 10));
        System.out.println(getIaqiValueByPmType(900, 10));
        System.out.println(getIaqiValueByPmType(900, 25));
        System.out.println(getIaqiValueByPmType(360, 25));
        System.out.println(getIaqiValueByPmType(510, 10));

        System.out.println(getIaqiValueByPmType(500, 25));
        System.out.println(getIaqiValueByPmType(600, 10));

        System.out.println(getIaqiValueByPmType(490, 25));
        System.out.println(getIaqiValueByPmType(590, 10));
        System.out.println(getIaqiValueByPmType(0, 25));
        System.out.println(getIaqiValueByPmType(0, 10));
        System.out.println(getIaqiValueByPmType(-20, 25));
        System.out.println(getIaqiValueByPmType(-10, 10));
        System.out.println(getIaqiValueByPmType(20, 25));
        System.out.println(getIaqiValueByPmType(20, 10));
    }

}