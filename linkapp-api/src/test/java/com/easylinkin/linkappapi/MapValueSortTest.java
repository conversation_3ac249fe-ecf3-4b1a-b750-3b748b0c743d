package com.easylinkin.linkappapi;

import com.alibaba.fastjson.JSON;

import java.util.*;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/26 11:18
 */
public class MapValueSortTest {

    public static void main(String[] args) {
        Map<String, Double> oldMap = new HashMap<>();
        oldMap.put("A站", 1.62);
        oldMap.put("光谷软件园站", 8.49);
        oldMap.put("江汉路站", 0.19);
        oldMap.put("D站", 20.01);
        Map<String, Double> stringDoubleMap = sortMap(oldMap);
        System.out.println("args = " + JSON.toJSONString(stringDoubleMap));

    }

    public static Map<String, Double> sortMap(Map<String, Double> oldMap) {
        ArrayList<Map.Entry<String, Double>> list = new ArrayList<Map.Entry<String, Double>>(oldMap.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Double>>() {

            @Override
            public int compare(Map.Entry<String, Double> arg0,
                               Map.Entry<String, Double> arg1) {
                return (int) (arg1.getValue() - arg0.getValue());
            }
        });
        Map<String, Double> newMap = new LinkedHashMap<String, Double>();
        for (int i = 0; i < list.size(); i++) {
            newMap.put(list.get(i).getKey(), list.get(i).getValue());
        }
        return newMap;
    }
}
