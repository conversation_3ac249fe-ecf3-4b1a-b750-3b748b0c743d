package com.easylinkin.linkappapi;

import com.alibaba.fastjson.JSON;
import com.easylinkin.linkappapi.common.utils.message.AppSendMessageUtil;
import com.easylinkin.linkappapi.common.utils.message.entity.AppSendMessage;
import com.easylinkin.linkappapi.common.utils.message.entity.AppSendMessage.model;
import com.easylinkin.linkappapi.roster.entity.RailLinkappRosterPersonnel;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import static site.morn.framework.context.CommonConstant.Config.FRAMEWORK_BASE_PACKAGES;


@Slf4j
@RunWith(SpringRunner.class)
/**
 * 测试类基础注解
 * 增加webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
 * 作用：解决集成了websocket后测试类启动报javax.websocket.server.ServerContainer not available
 */
@SpringBootTest(classes = LinkappApiApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
/** 与Application的启动类一致开始 **/
@ComponentScan(basePackages = {FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@EntityScan({FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@ServletComponentScan
@ActiveProfiles("nnw-test")
class LinkappApiApplicationTests {


    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void contextLoads() throws InterruptedException, JsonProcessingException {
        RailLinkappRosterPersonnel rosterPersonnel = new RailLinkappRosterPersonnel();
        rosterPersonnel.setId(1234567890l);
        rosterPersonnel.setEducationBackground("1");
        String s = objectMapper.writeValueAsString(rosterPersonnel);
        System.out.println(s);
        Thread.sleep(Integer.MAX_VALUE);
    }

}
