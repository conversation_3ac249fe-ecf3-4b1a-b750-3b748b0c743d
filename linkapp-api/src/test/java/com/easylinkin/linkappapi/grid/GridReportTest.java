package com.easylinkin.linkappapi.grid;

import com.alibaba.fastjson.JSONObject;
import com.easylinkin.core.models.Operator;
import com.easylinkin.linkappapi.LinkappApiApplication;
import com.easylinkin.linkappapi.grid.models.dto.GridReportDayDetailSaveDTO;
import com.easylinkin.linkappapi.grid.models.dto.GridReportWeekDetailSaveDTO;
import com.easylinkin.linkappapi.grid.service.IGridReportDayDetailService;
import com.easylinkin.linkappapi.grid.service.IGridReportWeekDetailService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static site.morn.framework.context.CommonConstant.Config.FRAMEWORK_BASE_PACKAGES;

/**
 * <AUTHOR>
 * @date 2024/5/22 15:23
 */
@Slf4j
@Setter(onMethod_ = @Resource)
@RunWith(SpringRunner.class)
/**
 * 测试类基础注解
 * 增加webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
 * 作用：解决集成了websocket后测试类启动报javax.websocket.server.ServerContainer not available
 */
@SpringBootTest(classes = LinkappApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
/** 与Application的启动类一致开始 **/
@ComponentScan(basePackages = {FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@EntityScan({FRAMEWORK_BASE_PACKAGES, "com.easylinkin"})
@ServletComponentScan
/** 与Application的启动类一致结束 **/
public class GridReportTest {

    private IGridReportDayDetailService gridReportDayDetailService;
    private IGridReportWeekDetailService gridReportWeekDetailService;

    @Test
    public void m1() {
        log.info("test");
    }

    @Test
    public void dayDetailSave() {
        String json = "{\n" +
                "    \"reportId\": 1,\n" +
                "    \"gridHierarchy\": \"一级网格\",\n" +
                "    \"gridNum\": 50,\n" +
                "    \"buildGridNum\": 30,\n" +
                "    \"workerNum\": 200,\n" +
                "    \"checkGeneralHazard\": 5,\n" +
                "    \"changeGeneralHazard\": 3,\n" +
                "    \"checkGreatHierarchy\": 1,\n" +
                "    \"changeGreatHierarchy\": 1,\n" +
                "    \"changeInvestmentAmount\": 150.50,\n" +
                "    \"rewardIndividualsAmount\": 2000.00,\n" +
                "    \"emergencySituation\": \"无\"\n" +
                "}";

        GridReportDayDetailSaveDTO dto = JSONObject.parseObject(json, GridReportDayDetailSaveDTO.class);

        Operator operator = new Operator();
        operator.setUserId("1695");
        operator.setNickname("test");
        operator.setTenantId("5a920f536428050edddcce5212f36a97");

        dto.setOperator(operator);

        gridReportDayDetailService.saveData(dto);
    }

    @Test
    public void weekDetailSave() {
        String json = "{\n" +
                "  \"reportId\": 1,\n" +
                "  \"specialContentStatus\": \"本周专项整治内容及开展情况\",\n" +
                "  \"nextWeekSpecialPlan\": \"下周拟开展专项整治内容及开展计划\",\n" +
                "  \"otherIssuesReport\": \"其他需要上报事项\"\n" +
                "}";

        GridReportWeekDetailSaveDTO dto = JSONObject.parseObject(json, GridReportWeekDetailSaveDTO.class);

        Operator operator = new Operator();
        operator.setUserId("1695");
        operator.setNickname("test");
        operator.setTenantId("5a920f536428050edddcce5212f36a97");

        dto.setOperator(operator);

        gridReportWeekDetailService.saveData(dto);
    }
}
