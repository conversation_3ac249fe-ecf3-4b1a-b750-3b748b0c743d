package com.easylinkin.linkappapi.progress.service;

import com.easylinkin.linkappapi.progress.entity.vo.MechanicalTypeStatisticsVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 大屏形象进度服务测试类
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ScreenProgressServiceTest {

    @Autowired
    private ScreenProgressService screenProgressService;

    @Test
    public void testGetMechanicalTypeStatistics() {
        MechanicalTypeStatisticsVo result = screenProgressService.getMechanicalTypeStatistics();
        
        System.out.println("总数量: " + result.getTotalCount());
        
        if (result.getTypeStatistics() != null) {
            for (MechanicalTypeStatisticsVo.TypeStatistics typeStats : result.getTypeStatistics()) {
                System.out.println(String.format("类型: %s, 数量: %d, 占比: %s%%", 
                    typeStats.getTypeName(), 
                    typeStats.getCount(), 
                    typeStats.getPercentage()));
            }
        }
    }
}
