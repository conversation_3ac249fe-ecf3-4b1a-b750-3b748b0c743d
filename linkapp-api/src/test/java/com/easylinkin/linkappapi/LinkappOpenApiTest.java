package com.easylinkin.linkappapi;

import java.io.IOException;
import java.net.InetAddress;

public class LinkappOpenApiTest {
	public static void main(String[] args) throws IOException {


		InetAddress addr = null;
		String address = "";
		try {
			addr = InetAddress.getLocalHost();//新建一个InetAddress类
			address = addr.getHostName().toString();// 获得本机名称
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println(address);
	}
}
