//var day = ((dataCurrDay[j][attAggName].value) - dataPreDay[i][attAggName].value);
//var total = dataCurrDay[j][attAggName].value;

var total =undefined;
var day =undefined;
try {
    total = dataCurrDay[j][attAggName].hits.hits[0]._source.data[attIdArr[attIndex]];
    day = total - dataPreDay[i][attAggName].hits.hits[0]._source.data[attIdArr[attIndex]];
}catch (e){
    console.log(e)
}

var item0 = {"deviceCode": devKey, "day": day, "total": total};
if (total &lt; 0) {
    item0['actualTotal'] = total;
    item0['total'] = 0.0;
}
if (day &lt; 0) {
    item0['actualDay'] = day;
    item0['day'] = 0.0;
}
energyDay += item0['day'];
energyTotal += item0['total'];
items.push(item0);

