for (var i = 0; i < 10; i++) {
    var total = undefined;
    var day = undefined;
    try {
        // total = dataCurrDay[j][attAggName].hits.hits[0]._source.data[attIdArr[attIndex]];
        // day = total - dataPreDay[i][attAggName].hits.hits[0]._source.data[attIdArr[attIndex]];
    }catch(err){
        console.log("123")
        continue;
    }
    if(day === undefined || isNaN(day)){
        console.log("1234")

        continue;
    }
    console.log("1231236666")

}

